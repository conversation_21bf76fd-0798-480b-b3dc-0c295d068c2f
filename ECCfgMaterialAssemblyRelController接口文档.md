# ECCfgMaterialAssemblyRelController 接口文档

## 概述

ECCfgMaterialAssemblyRelController 是配置物料装配关系管理的REST API控制器，提供查询服务器SN对应的物料装配关系信息的功能。

- **模块**: 配置物料装配关系管理
- **作者**: 0668000585
- **日期**: 2025-08-05
- **服务地址**: `https://icosg.test.zte.com.cn/zte-mes-manufactureshare-datawbsys`
- **基础路径**: `/ecCfgMaterialAssemblyRel`

## 接口列表

### 1. 查询配置物料装配关系

#### 基本信息
- **接口名称**: 查询配置物料装配关系
- **请求方式**: `POST`
- **完整URL**: `https://icosg.test.zte.com.cn/zte-mes-manufactureshare-datawbsys/ecCfgMaterialAssemblyRel/getAssemblyRelationList`
- **接口描述**: 根据服务器SN列表查询对应的物料装配关系信息

#### 请求参数

**请求头**:
```
Content-Type: application/json
```

**请求体**: 
```json
[
  "server_sn_1",
  "server_sn_2",
  "server_sn_3"
]
```

| 参数名 | 类型 | 必填 | 描述 | 限制 |
|--------|------|------|------|------|
| serverSnList | List<String> | 是 | 服务器SN列表 | 数量不能超过5个 |

#### 请求示例

**RESTful接口调用**:
```bash
curl -X POST "https://icosg.test.zte.com.cn/zte-mes-manufactureshare-datawbsys/ecCfgMaterialAssemblyRel/getAssemblyRelationList" \
  -H "Content-Type: application/json" \
  -d '[
    "SN202508050001",
    "SN202508050002",
    "SN202508050003"
  ]'
```

**入参示例**:
```json
[
  "SN202508050001",
  "SN202508050002",
  "SN202508050003"
]
```

#### 响应参数

**响应格式**: `ServiceData<List<ECMaterialAssemblyDTO>>`

**返回结果示例**:

**成功响应**:
```json
{
  "code": "200",
  "message": "操作成功",
  "success": true,
  "data": [
    {
      "contractNumber": "ZTE-2025-001",
      "entityName": "TASK-202508050001",
      "orgId": "8000220",
      "serverSn": "SN202508050001",
      "assembleList": [
        {
          "itemBarcode": "210000001234567890",
          "itemType": "COMPONENT",
          "itemCode": "ZTE-CPU-001",
          "itemName": "CPU处理器模块",
          "qty": 1,
          "parentItemBarcode": "210000001234567899",
          "wpEntityName": "TASK-202508050001",
          "assembleby": "0668000585",
          "assembleDate": "2025-08-05 10:30:15"
        },
        {
          "itemBarcode": "210000001234567891",
          "itemType": "MEMORY",
          "itemCode": "ZTE-MEM-002",
          "itemName": "内存条DDR4-32G",
          "qty": 4,
          "parentItemBarcode": "210000001234567899",
          "wpEntityName": "TASK-202508050001",
          "assembleby": "0668000585",
          "assembleDate": "2025-08-05 10:35:20"
        }
      ]
    },
    {
      "contractNumber": "ZTE-2025-002",
      "entityName": "TASK-202508050002",
      "orgId": "8000220",
      "serverSn": "SN202508050002",
      "assembleList": [
        {
          "itemBarcode": "210000001234567892",
          "itemType": "BOARD",
          "itemCode": "ZTE-BOARD-003",
          "itemName": "主板模块",
          "qty": 1,
          "parentItemBarcode": "210000001234567900",
          "wpEntityName": "TASK-202508050002",
          "assembleby": "0668000586",
          "assembleDate": "2025-08-05 11:15:30"
        }
      ]
    }
  ]
}
```

**响应字段说明**:

| 字段 | 类型 | 描述 |
|------|------|------|
| code | String | 响应状态码 |
| message | String | 响应消息 |
| data | List<ECMaterialAssemblyDTO> | 物料装配关系列表 |

**ECMaterialAssemblyDTO 字段说明**:

| 字段 | 类型 | 描述 |
|------|------|------|
| contractNumber | String | 合同号 |
| entityName | String | 任务号 |
| orgId | String | 组织ID |
| serverSn | String | 服务器SN |
| assembleList | List<ECMaterialAssemblyItemDTO> | 明细列表 |

**ECMaterialAssemblyItemDTO 字段说明**:

| 字段 | 类型 | 描述 |
|------|------|------|
| itemBarcode | String | 子条码 |
| itemType | String | 条码类型 |
| itemCode | String | 物料代码 |
| itemName | String | 物料名称 |
| qty | Integer | 数量 |
| parentItemBarcode | String | 父条码 |
| wpEntityName | String | 目标任务号 |
| assembleby | String | 绑定人 |
| assembleDate | Date | 绑定时间(格式: yyyy-MM-dd HH:mm:ss) |

#### 异常响应

**1. 服务器SN数量超出限制异常**:
```json
{
  "code": "BUSINESSERROR_CODE",
  "message": "服务器SN数量不能超过5个",
  "success": false,
  "data": null
}
```

**2. 服务器SN不在前两层异常**:
```json
{
  "code": "BUSINESSERROR_CODE",
  "message": "服务器SN不在前两层",
  "success": false,
  "data": null
}
```

**3. 系统异常**:
```json
{
  "code": "500",
  "message": "系统异常，请联系管理员",
  "success": false,
  "data": null
}
```

#### 异常情况说明

| 异常类型 | 触发条件 | 错误代码 | 错误消息ID | 错误描述 |
|----------|----------|----------|-----------|----------|
| 业务异常 | 服务器SN列表数量超过5个 | BUSINESSERROR_CODE | SERVER_SN_MAX_INPUT_LIMIT | 服务器SN数量不能超过5个 |
| 业务异常 | 服务器SN不在前两层结构中 | BUSINESSERROR_CODE | SERVER_SN_NOT_IN_FIRST_TWO_LAYERS | 服务器SN不在前两层 |
| 系统异常 | 未捕获的系统异常 | 500 | - | 系统异常，请联系管理员 |

#### 状态码说明

| HTTP状态码 | 业务状态码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 200 | BUSINESSERROR_CODE | 业务逻辑异常 |
| 500 | 500 | 系统异常 |

#### 注意事项

1. **输入限制**: 
   - 服务器SN列表数量不能超过5个
   - 服务器SN不能为空或null值
   - 服务器SN格式需符合系统规范

2. **返回数据**:
   - 返回结果按服务器SN顺序组织
   - 每个服务器SN对应一个ECMaterialAssemblyDTO对象
   - assembleList可能为空列表（表示该服务器无装配关系）

3. **异常处理**: 
   - 所有异常都会被记录到日志中，包含请求参数
   - 业务异常返回具体错误信息，便于问题定位
   - 系统异常统一返回通用错误消息

4. **性能考虑**:
   - 限制单次查询数量以保证响应性能
   - 建议分批查询大量数据
   - 查询结果会按需加载明细信息

5. **数据一致性**:
   - 查询结果为实时数据
   - 装配关系数据来源于生产系统
   - 绑定时间精确到秒

#### 相关业务说明

- 该接口主要用于根据服务器SN查询对应的物料装配关系
- 装配关系包含合同号、任务号、组织信息等基础数据
- 明细信息包含物料条码、类型、代码、名称、数量等详细信息
- 支持批量查询，但有数量限制以保证系统性能

#### 依赖服务

- **ECCfgMaterialAssemblyRelService**: 核心业务逻辑服务
- **ServiceDataBuilderUtil**: 统一响应数据构建工具

#### 版本信息

- **API版本**: v1.0
- **创建日期**: 2025-08-05
- **最后修改**: 2025-08-05

---

**文档生成时间**: 2025-01-27  
**文档版本**: v1.0  
**维护人**: 系统管理员
