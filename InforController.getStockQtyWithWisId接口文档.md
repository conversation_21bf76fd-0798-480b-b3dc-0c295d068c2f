# InforController.getStockQtyWithWisId 接口文档

## 接口概述
根据仓库、物料代码、状态查询库存数量并返回仓库id

## 接口基本信息

| 属性 | 值 |
|------|-----|
| **接口名称** | getStockQtyWithWisId |
| **接口描述** | 根据仓库、物料代码、状态查询库存数量并返回仓库id |
| **请求方式** | POST |
| **接口地址** | `/inforInquery/getStockQtyWithWisId` |
| **完整URL** | `http://ip:8080/zte-mes-manufactureshare-datawbsys/inforInquery/getStockQtyWithWisId` |
| **Content-Type** | application/json |
| **控制器类** | com.zte.interfaces.InforController |

## 请求参数

### 请求头（Header）
| 参数名 | 必填 | 类型 | 说明 |
|--------|------|------|------|
| Content-Type | 是 | String | application/json |
| factoryId | 是 | String | 工厂ID（通过请求头验证） |
| empno | 是 | String | 员工工号（通过请求头验证） |

### 请求体（Body）
请求体为JSON格式，对应 `LotxlocxidDTO` 对象：

| 参数名 | 必填 | 类型 | 说明 | 限制 |
|--------|------|------|------|------|
| wisIdList | 是 | List&lt;String&gt; | 仓库ID列表 | 不能为空，最多50个，只能包含数字、字母、下划线 |
| skuList | 是 | List&lt;String&gt; | 物料代码列表 | 不能为空，最多100个 |
| status | 否 | String | 状态筛选条件 | OK:可用库存，HOLD:隔离库存 |
| brandList | 否 | List&lt;String&gt; | 品牌列表 | 本接口不使用此字段 |
| uuidList | 否 | List&lt;String&gt; | UUID列表 | 本接口不使用此字段 |

### 参数校验规则
1. **wisIdList校验**：
   - 不能为空且不能超过50个
   - wisId只能包含数字、字母、下划线（防止SQL注入）

2. **skuList校验**：
   - 不能为空且不能超过100个

3. **请求头校验**：
   - factoryId和empno必填（通过RequestHeadValidationUtil.validaFactoryIdAndEmpno验证）

## 响应结果

### 响应格式
响应数据封装在 `ServiceData` 对象中：

```json
{
  "code": {
    "code": "字符串类型的响应码",
    "messageId": "消息ID"
  },
  "bo": [
    {
      "wisId": "仓库ID",
      "sku": "物料代码", 
      "qty": 数量整数值,
      "status": "状态",
      "itemUuid": "物料UUID"
    }
  ]
}
```

### 响应参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Object | 响应状态码对象 |
| code.code | String | 响应码 |
| code.messageId | String | 消息ID |
| bo | List&lt;Lotxlocxid&gt; | 库存数据列表 |
| bo[].wisId | String | 仓库ID |
| bo[].sku | String | 物料代码 |
| bo[].qty | Integer | 库存数量 |
| bo[].status | String | 库存状态 |
| bo[].itemUuid | String | 物料UUID |

### 成功响应示例
```json
{
  "code": {
    "code": "SUCCESS_CODE",
    "messageId": "SUCCESS_MSGID"
  },
  "bo": [
    {
      "wisId": "WH001",
      "sku": "ITEM001",
      "qty": 100,
      "status": "OK",
      "itemUuid": null
    },
    {
      "wisId": "WH002", 
      "sku": "ITEM001",
      "qty": 50,
      "status": "OK",
      "itemUuid": null
    }
  ]
}
```

### 错误响应示例
```json
{
  "code": {
    "code": "VALIDATIONERROR_CODE",
    "messageId": "WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX"
  },
  "bo": null
}
```

## 请求示例

### cURL示例
```bash
curl -X POST \
  http://localhost:8080/zte-mes-manufactureshare-datawbsys/inforInquery/getStockQtyWithWisId \
  -H 'Content-Type: application/json' \
  -H 'factoryId: F001' \
  -H 'empno: EMP001' \
  -d '{
    "wisIdList": ["WH001", "WH002"],
    "skuList": ["ITEM001", "ITEM002"],
    "status": "OK"
  }'
```

### Java示例（使用RestTemplate）
```java
// 请求头
HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.APPLICATION_JSON);
headers.set("factoryId", "F001");
headers.set("empno", "EMP001");

// 请求体
LotxlocxidDTO requestDto = new LotxlocxidDTO();
requestDto.setWisIdList(Arrays.asList("WH001", "WH002"));
requestDto.setSkuList(Arrays.asList("ITEM001", "ITEM002"));
requestDto.setStatus("OK");

HttpEntity<LotxlocxidDTO> entity = new HttpEntity<>(requestDto, headers);

// 发送请求
String url = "http://localhost:8080/zte-mes-manufactureshare-datawbsys/inforInquery/getStockQtyWithWisId";
ResponseEntity<ServiceData> response = restTemplate.postForEntity(url, entity, ServiceData.class);
```

### JavaScript示例（使用fetch）
```javascript
const requestData = {
  wisIdList: ["WH001", "WH002"],
  skuList: ["ITEM001", "ITEM002"],
  status: "OK"
};

fetch('http://localhost:8080/zte-mes-manufactureshare-datawbsys/inforInquery/getStockQtyWithWisId', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'factoryId': 'F001',
    'empno': 'EMP001'
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## 业务逻辑说明

1. **数据查询逻辑**：
   - 根据提供的仓库ID列表（wisIdList）和物料代码列表（skuList）查询库存
   - 支持按状态（status）筛选：OK表示可用库存，HOLD表示隔离库存
   - 只返回数量大于0的库存记录
   - 按物料代码（sku）分组汇总数量

2. **SQL查询特点**：
   - 使用UNION ALL合并多个仓库的查询结果
   - 每个仓库的查询结果都会包含仓库ID（wis_id）
   - 对每个仓库按物料代码分组求和

3. **返回结果特点**：
   - 相同物料在不同仓库中的库存会分别返回
   - 每条记录都包含具体的仓库ID，便于识别库存位置

## 异常情况

| 异常类型 | 错误码 | 错误描述 |
|----------|--------|----------|
| 参数校验失败 | VALIDATIONERROR_CODE | wisIdList为空或超过50个 |
| 参数校验失败 | VALIDATIONERROR_CODE | skuList为空或超过100个 |
| 参数校验失败 | VALIDATIONERROR_CODE | wisId包含非法字符（只允许数字、字母、下划线） |
| 请求头校验失败 | VALIDATIONERROR_CODE | factoryId或empno缺失 |

## 注意事项

1. **安全性**：wisId参数会进行SQL注入防护，只允许包含数字、字母、下划线
2. **性能**：建议合理控制wisIdList和skuList的大小，避免查询超时
3. **数据源**：接口使用INFOR数据源（通过@DataSource(DatabaseType.INFOR)配置）
4. **请求头**：factoryId和empno是必填的请求头参数，用于权限验证

## 相关接口

- `getStockQty`：根据仓库、物料代码、状态查询库存数量（不返回仓库ID）
- `getStockQtyWithBrand`：根据仓库、品牌、状态查询库存数量并返回仓库ID

## 版本信息

- **服务名称**：zte-mes-manufactureshare-datawbsys
- **默认端口**：8080
- **接口版本**：v1.0
- **最后更新**：2024年
