package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.ApsOpProdplanServiceImpl;
import com.zte.domain.model.datawb.ApsOpProdplanRepository;
import com.zte.interfaces.dto.ProdPlanDetailDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
public class ApsOpProdplanServiceImplTest {
    @InjectMocks
    private ApsOpProdplanServiceImpl apsOpProdplanServiceImp;

    @Mock
    private ApsOpProdplanRepository apsOpProdplanRepository;

    @Test
    public void selectProdPlanByProdPlan() {
        Map<String, Object> map=new HashMap<>();
        List<String> planIdList=new ArrayList<>();
        planIdList.add("123");
        map.put("planIdList",planIdList);
        List<ProdPlanDetailDTO> detailList=new ArrayList<>();
        PowerMockito.when(apsOpProdplanRepository.selectProdPlanByProdPlan(Mockito.anyMap()))
                .thenReturn(detailList);
        apsOpProdplanServiceImp.selectProdPlanByProdPlan(map);
        //detailList不为空
        ProdPlanDetailDTO detailDTO=new ProdPlanDetailDTO();
        detailList.add(detailDTO);
        List<ProdPlanDetailDTO> wipJobPartList=new ArrayList<>();
        PowerMockito.when(apsOpProdplanRepository.selectWipJobByProdplanNo(Mockito.anyMap()))
                .thenReturn(wipJobPartList);
        apsOpProdplanServiceImp.selectProdPlanByProdPlan(map);
        //wipJobPartList 不为空
        ProdPlanDetailDTO prodPlanDetailDTO=new ProdPlanDetailDTO();
        wipJobPartList.add(prodPlanDetailDTO);
        prodPlanDetailDTO.setProdplanNo("123");
        apsOpProdplanServiceImp.selectProdPlanByProdPlan(map);
        //prodplanNo不为空
        detailDTO.setProdplanNo("123");
        apsOpProdplanServiceImp.selectProdPlanByProdPlan(map);
        //不能匹配
        prodPlanDetailDTO.setProdplanNo("456");
        Assert.assertNotNull(apsOpProdplanServiceImp.selectProdPlanByProdPlan(map));
    }



}
