package com.zte.autoTest.unitTest;

import com.zte.application.datawb.EntryBoxAppliedOthersService;
import com.zte.application.datawb.impl.NPDMaterialImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.NPDMaterialRepository;
import com.zte.interfaces.dto.NpdMaterialDTO;
import com.zte.interfaces.dto.NpdMaterialParamterDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;


@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class})
public class NPDMaterialImplTest extends PowerBaseTestCase {
    @InjectMocks
    private NPDMaterialImpl nPDMaterialImpl;

    @Mock
    private NPDMaterialRepository nPDMaterialRepository;

    @Mock
    EntryBoxAppliedOthersService entryBoxAppliedOthersService;

    @Before
    public void init() {

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void getParamter() throws Exception {

        NpdMaterialParamterDTO npdMaterialParamterDTO = new NpdMaterialParamterDTO();
        //设置开始时间
        Date date = new Date();
        GregorianCalendar gc = new GregorianCalendar();
        gc.set(Calendar.YEAR, 2013);//设置年
        gc.set(Calendar.MONTH, 8);//这里0是1月..以此向后推
        gc.set(Calendar.DAY_OF_MONTH, 29);//设置天
        gc.set(Calendar.HOUR_OF_DAY, 5);//设置小时
        gc.set(Calendar.MINUTE, 7);//设置分
        gc.set(Calendar.SECOND, 6);//设置秒
        gc.set(Calendar.MILLISECOND, 200);//设置毫秒
        date = gc.getTime();
        //设置结束时间
        Date date1 = new Date();
        GregorianCalendar gc1 = new GregorianCalendar();
        gc1.set(Calendar.YEAR, 2014);//设置年
        gc1.set(Calendar.MONTH, 8);//这里0是1月..以此向后推
        gc1.set(Calendar.DAY_OF_MONTH, 29);//设置天
        gc1.set(Calendar.HOUR_OF_DAY, 5);//设置小时
        gc1.set(Calendar.MINUTE, 7);//设置分
        gc1.set(Calendar.SECOND, 6);//设置秒
        gc1.set(Calendar.MILLISECOND, 200);//设置毫秒
        date1 = gc1.getTime();
        npdMaterialParamterDTO.setPageSize((long) 100);
        npdMaterialParamterDTO.setCurrentPage((long) 1);
        npdMaterialParamterDTO.setDateStart(date);
        npdMaterialParamterDTO.setDateEnd(date1);
        nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        npdMaterialParamterDTO.setPageSize(null);
        try {
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        npdMaterialParamterDTO.setPageSize(new Long(500));
        try {
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());

        }
        npdMaterialParamterDTO.setPageSize(new Long(-1));
        try {
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        npdMaterialParamterDTO.setPageSize(new Long(100));
        npdMaterialParamterDTO.setCurrentPage(new Long(-1));
        try {
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());

        }
        Date dt = new Date();
        npdMaterialParamterDTO.setCurrentPage(new Long(1));
        npdMaterialParamterDTO.setDateStart(dt);
        try {
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        npdMaterialParamterDTO.setDateStart(date);
        List<Integer> orgs = new ArrayList<>();
        orgs.add(new Integer(1));
        PowerMockito.when(entryBoxAppliedOthersService.checkOrgRepeat()).thenReturn(orgs);
        try {
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        List<String> softwareTaskNos = new ArrayList<>();
        for (int i = 0; i < 102; i++) {
            softwareTaskNos.add("-M20101100008");
        }
        npdMaterialParamterDTO.setSoftwareTaskNos(softwareTaskNos);
        try {
            npdMaterialParamterDTO.setSoftwareTaskNos(softwareTaskNos);
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        softwareTaskNos = new ArrayList<>();
        softwareTaskNos.add("-M20101100008");
        try {
            npdMaterialParamterDTO.setSoftwareTaskNos(softwareTaskNos);
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
    }

    @Test
    public void getParamter1() throws Exception {
        try {
            NpdMaterialParamterDTO npdMaterialParamterDTO = new NpdMaterialParamterDTO();
            Date date = new Date();
            Date date1 = new Date();
            nPDMaterialImpl.getParamter(npdMaterialParamterDTO);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());

        }
    }

    @Test
    public void getCollection() {
        NpdMaterialDTO npdMaterialDTO = new NpdMaterialDTO();
        npdMaterialDTO.setEntityName("1233");
        npdMaterialDTO.setItemNo("1233");
        List<NpdMaterialDTO> dtoOne = new ArrayList<>();
        dtoOne.add(npdMaterialDTO);
        NpdMaterialParamterDTO dtoPageSize = new NpdMaterialParamterDTO();

        Assert.assertNotNull(nPDMaterialImpl.getCollection(dtoOne, dtoPageSize));
    }

    @Test
    public void getOneTwoData() {
        NpdMaterialDTO npdMaterialDTO = new NpdMaterialDTO();

        npdMaterialDTO.setEntityName("1233");
        npdMaterialDTO.setSoftwareTaskNo("1233");
        npdMaterialDTO.setItemNo("1233");
        List<NpdMaterialDTO> dtoOne = new ArrayList<>();
        dtoOne.add(npdMaterialDTO);
        List<NpdMaterialDTO> dtoTwo = new ArrayList<>();
        dtoTwo.add(npdMaterialDTO);
        nPDMaterialImpl.getOneTwoData(dtoOne, dtoTwo);
        npdMaterialDTO.setSoftwareTaskNo("12334");
        Assert.assertNotNull(nPDMaterialImpl.getOneTwoData(dtoOne, dtoTwo));
    }

    @Test
    public void comBineEntityItemcode() {
        NpdMaterialParamterDTO npdMaterialParamterDTO = new NpdMaterialParamterDTO();
        npdMaterialParamterDTO.setPageSize((long) 100);
        npdMaterialParamterDTO.setCurrentPage((long) 1);
        NpdMaterialDTO npdMaterialDTO = new NpdMaterialDTO();
        npdMaterialDTO.setEntityName("1233");
        npdMaterialDTO.setItemNo("1233");
        List<NpdMaterialDTO> dtoOne = new ArrayList<>();
        dtoOne.add(npdMaterialDTO);
        List<NpdMaterialDTO> dtoTwo = new ArrayList<>();
        dtoTwo.add(npdMaterialDTO);
        //NpdMaterialParamterDTO npdMaterialParamterDTO =new NpdMaterialParamterDTO();
        PowerMockito.when(nPDMaterialRepository.callItemNpdMaterialCount(npdMaterialParamterDTO)).thenReturn((long) 1);
        PowerMockito.when(nPDMaterialRepository.callAnotherNpdMaterial(npdMaterialParamterDTO)).thenReturn(dtoTwo);
        PowerMockito.when(nPDMaterialRepository.callItemNpdMaterial(npdMaterialParamterDTO)).thenReturn(dtoOne);
        nPDMaterialImpl.comBineEntityItemcode(npdMaterialParamterDTO);
        PowerMockito.when(nPDMaterialRepository.callItemNpdMaterial(npdMaterialParamterDTO)).thenReturn(null);
        Assert.assertNull(nPDMaterialImpl.comBineEntityItemcode(npdMaterialParamterDTO));
    }


}
