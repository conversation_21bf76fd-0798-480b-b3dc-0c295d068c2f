package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.ApsItemOrgCivServiceImpl;
import com.zte.domain.model.stepdt.ApsItemOrgCivRepository;
import com.zte.interfaces.stepdt.dto.ApsItemOrgCivDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/22 23
 * @description:
 */
public class ApsItemOrgCivServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ApsItemOrgCivServiceImpl apsItemOrgCivServiceImpl;
    @Mock
    private ApsItemOrgCivRepository apsItemOrgCivRepository;

    @Test
    public void queryItemCivList() {
        List<ApsItemOrgCivDTO> list = new LinkedList<>();
        ApsItemOrgCivDTO a1 = new ApsItemOrgCivDTO();
        list.add(a1);
        PowerMockito.when(apsItemOrgCivRepository.queryItemCivList(list)).thenReturn(list);
        Assert.assertNotNull(apsItemOrgCivServiceImpl.queryItemCivList(list));
    }
}
