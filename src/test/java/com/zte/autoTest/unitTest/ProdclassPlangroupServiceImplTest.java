package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.ProdclassPlangroupServiceImpl;
import com.zte.domain.model.stepdt.ProdclassPlangroupRepository;
import com.zte.interfaces.stepdt.dto.ProdclassPlangroupDTO;
import com.zte.interfaces.stepdt.dto.StUtilityDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2022-09-09 10:36
 */
public class ProdclassPlangroupServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ProdclassPlangroupServiceImpl prodclassPlangroupServiceImpl;
    @Mock
    private ProdclassPlangroupRepository prodclassPlangroupRepository;

    @Before
    public void init() {

    }

    @Test
    public void queryProjectNameByEntpNo() {
        ProdclassPlangroupDTO query = new ProdclassPlangroupDTO();
        PowerMockito.when(prodclassPlangroupRepository.queryProjectNameByEntpNo(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(prodclassPlangroupServiceImpl.queryProjectNameByEntpNo(query));
    }

    @Test
    public void getStUtility() {
        PowerMockito.when(prodclassPlangroupRepository.getStUtility()).thenReturn(new ArrayList<>());
        Assert.assertNotNull(prodclassPlangroupServiceImpl.getStUtility());
    }

    @Test
    public void getStUtilityPage() {
        Page<StUtilityDTO> page = new Page<>();
        PowerMockito.when(prodclassPlangroupRepository.getStUtilityPage(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(prodclassPlangroupServiceImpl.getStUtilityPage(page));
    }

    /*Started by AICoder, pid:d58f2adfab275d214f4b0b3670b3444daa407320*/
    @Test(timeout = 8000)
    public void testPageStUtilityLazyData_NullPage() {
        try {
            prodclassPlangroupServiceImpl.pageStUtilityLazyData(null);
            fail("Should have thrown an exception");
        } catch (Exception e) {
            assertNull(e.getMessage());
        }
        Page<StUtilityDTO> page = new Page<>();
        prodclassPlangroupServiceImpl.pageStUtilityLazyData(page);
    }
    /*Ended by AICoder, pid:d58f2adfab275d214f4b0b3670b3444daa407320*/
}
