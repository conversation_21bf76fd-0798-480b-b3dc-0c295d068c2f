package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.ZmsMainboardInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.domain.model.datawb.ZmsMainboardInfoRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @since 2023年5月16日20:06
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, DataServiceClientV1.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsMainboardInfoTest extends PowerBaseTestCase {

    @InjectMocks
    private ZmsMainboardInfoServiceImpl zmsMainboardInfoServiceImpl;

    @Mock
    private ZmsMainboardInfoRepository zmsMainboardInfoRepository;

    @Mock
    private DataServiceClientV1 dataServiceClientV1;


    @Test
    public void selectCompleteMachineHead() throws Exception {

        PowerMockito.mockStatic(DataServiceClientV1.class);
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);
        String sn1 = "111";
        String sn2 = "222";
        List<String> snListEmpty = new ArrayList<>();
        List<String> snList = new ArrayList<>();
        snList.add(sn1);
        snList.add(sn2);
        PowerMockito.when(zmsMainboardInfoRepository.getCompleteMachineHead(anyObject())).thenReturn(null);
        try {
            zmsMainboardInfoServiceImpl.snValid(snListEmpty);
            zmsMainboardInfoServiceImpl.snValid(snList);
            zmsMainboardInfoServiceImpl.selectCompleteMachineHead(snList);
            zmsMainboardInfoRepository.getCompleteMachineHead(snList);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
}
