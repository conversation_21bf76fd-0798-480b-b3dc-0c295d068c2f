package com.zte.autoTest.unitTest;

/* Started by AICoder, pid:id49ay948e8fd0c1417b0a97b1256a1f5c589c54 */
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.*;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.*;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsForwardTencentServiceImplTest {

    @InjectMocks
    private ZmsForwardTencentServiceImpl zmsForwardTencentService;

    @Mock
    private ZmsForwardTencentRepository zmsForwardTencentRepository;
    @Mock
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;
    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;
    @Mock
    private ZmsOverallUnitServiceImpl zmsOverallUnitService;
    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;
    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;
    @Mock
    CfgCodeRuleItemServiceImpl cfgCodeRuleItemService;

    @Mock
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class);
    }

    @Test
    public void uploadForwardData() throws Exception {

        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<String> recordIdList = new ArrayList<>();
        recordIdList.add("11");
        dto.setRecordIdList(recordIdList);
        String empNo = "0668000680";
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineDataByManual", recordIdList, empNo);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadForwardData", dto, empNo);

        dto.setRecordIdList(null);
        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("11111");
        dto.setEntityNameList(entityNameList);
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(Mockito.any(), Mockito.any())).thenReturn("111");
        List<ZmsForwardTencentEntityDTO> zmsForwardTencentEntityDTOS = new ArrayList<>();
        PowerMockito.when(zmsForwardTencentRepository.getTencentEntity(Mockito.any())).thenReturn(zmsForwardTencentEntityDTOS);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadForwardData", dto, empNo);

        ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO = new ZmsForwardTencentEntityDTO();
        zmsForwardTencentEntityDTO.setEntityId("111");
        zmsForwardTencentEntityDTO.setEntityName("1111");
        zmsForwardTencentEntityDTO.setContractNumber("11111");
        zmsForwardTencentEntityDTOS.add(zmsForwardTencentEntityDTO);
        PowerMockito.when(zmsForwardTencentRepository.getTencentEntity(Mockito.any())).thenReturn(zmsForwardTencentEntityDTOS);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadForwardData", dto, empNo);

        dto.setEntityNameList(null);
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupCode(new BigDecimal("203010200001"));
        sysLookupValues1.setDescription("4917");
        sysLookupValuesList.add(sysLookupValues1);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        PowerMockito.when(zmsForwardTencentRepository.getTencentEntity(Mockito.any())).thenReturn(zmsForwardTencentEntityDTOS);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadForwardData", dto, empNo);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getLookupMeanList() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupCode(new BigDecimal("203010200001"));
        sysLookupValues1.setDescription("4917");
        sysLookupValuesList.add(sysLookupValues1);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupCode(new BigDecimal("203010200002"));
        sysLookupValues2.setDescription("腾讯");
        sysLookupValuesList.add(sysLookupValues2);
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupCode(new BigDecimal("203010200003"));
        sysLookupValues3.setDescription("-M");
        sysLookupValuesList.add(sysLookupValues3);
        SysLookupValues sysLookupValues4 = new SysLookupValues();
        sysLookupValues4.setLookupCode(new BigDecimal("203010200004"));
        sysLookupValues4.setDescription("装配,调试,包装,检验");
        sysLookupValuesList.add(sysLookupValues4);
        SysLookupValues sysLookupValues5 = new SysLookupValues();
        sysLookupValues5.setLookupCode(new BigDecimal("203010200005"));
        sysLookupValues5.setDescription("服务器DZH");
        sysLookupValuesList.add(sysLookupValues5);
        SysLookupValues sysLookupValues6 = new SysLookupValues();
        sysLookupValues6.setLookupCode(new BigDecimal("203010200006"));
        sysLookupValues6.setDescription("test");
        sysLookupValuesList.add(sysLookupValues6);
        SysLookupValues sysLookupValues7 = new SysLookupValues();
        sysLookupValues7.setLookupCode(new BigDecimal("203010200007"));
        sysLookupValues7.setDescription("test");
        sysLookupValuesList.add(sysLookupValues7);
        SysLookupValues sysLookupValues8 = new SysLookupValues();
        sysLookupValues8.setLookupCode(new BigDecimal("203010200008"));
        sysLookupValues8.setDescription("test");
        sysLookupValuesList.add(sysLookupValues8);
        SysLookupValues sysLookupValues9 = new SysLookupValues();
        sysLookupValues9.setLookupCode(new BigDecimal("222333"));
        sysLookupValues9.setDescription("test");
        sysLookupValuesList.add(sysLookupValues9);
        SysLookupValues sysLookupValues10 = new SysLookupValues();
        sysLookupValues10.setLookupCode(new BigDecimal("203010200011"));
        sysLookupValues10.setDescription("test");
        sysLookupValuesList.add(sysLookupValues10);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getLookupMeanList", dto);
        try{
            PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(null);
            Whitebox.invokeMethod(zmsForwardTencentService, "getLookupMeanList", dto);
        }catch (Exception e){
            e.printStackTrace();
            Assert.assertNotNull(e.toString());
        }
        Assert.assertNotNull(dto);
    }

    @Test
    public void uploadCompleteMachineData() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        String empNo = "0668000680";
        List<ZmsForwardTencentEntityDTO> zmsForwardTencentEntityDTOS = new ArrayList<>();
        ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO = new ZmsForwardTencentEntityDTO();
        zmsForwardTencentEntityDTO.setEntityId("111");
        zmsForwardTencentEntityDTO.setEntityName("1111");
        zmsForwardTencentEntityDTO.setContractNumber("11111");
        zmsForwardTencentEntityDTOS.add(zmsForwardTencentEntityDTO);
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(configDescList);
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = new ArrayList<>();
        PowerMockito.when(zmsForwardTencentRepository.getBatchRuleDTOList()).thenReturn(batchRuleDTOList);
        ZmsForwardTencentSaveDTO zmsForwardTencentSaveDTO = new ZmsForwardTencentSaveDTO();
        List<ZmsForwardTencentBoardSnDTO> boardSnDTOList = new ArrayList<>();
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setSvrSN("111");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
        zmsForwardTencentSaveDTO.setZmsForwardTencentDTOList(zmsForwardTencentDTOList);
        zmsForwardTencentSaveDTO.setBoardSnDTOList(boardSnDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "batchInsert", zmsForwardTencentSaveDTO, empNo);
        Whitebox.invokeMethod(zmsForwardTencentService, "insertInternetMain", zmsForwardTencentEntityDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void insertInternetMain() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO = new ZmsForwardTencentEntityDTO();
        zmsForwardTencentEntityDTO.setEmpNo("111");
        zmsForwardTencentEntityDTO.setEntityId("222");
        zmsForwardTencentEntityDTO.setEntityName("1111");
        String dataType = "11";
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn(dataType);
        PowerMockito.when(zmsForwardTencentRepository.insertInternetMain(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsForwardTencentService, "insertInternetMain", zmsForwardTencentEntityDTO);
        Assert.assertNotNull(dto);
    }

    @Test
    public void batchInsert() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentSaveDTO zmsForwardTencentSaveDTO = new ZmsForwardTencentSaveDTO();
        Whitebox.invokeMethod(zmsForwardTencentService, "batchInsert", zmsForwardTencentSaveDTO, null);

        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setSvrSN("111");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
        zmsForwardTencentSaveDTO.setZmsForwardTencentDTOList(zmsForwardTencentDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "pushDataToB2B", zmsForwardTencentDTOList, "11111");
        Whitebox.invokeMethod(zmsForwardTencentService, "batchInsert", zmsForwardTencentSaveDTO, "11111");

        List<ZmsForwardTencentBoardSnDTO> boardSnDTOList = new ArrayList<>();
        ZmsForwardTencentBoardSnDTO zmsForwardTencentBoardSnDTO = new ZmsForwardTencentBoardSnDTO();
        zmsForwardTencentBoardSnDTO.setBoardSn("1111");
        zmsForwardTencentBoardSnDTO.setEntityId("1111");
        zmsForwardTencentBoardSnDTO.setEntityName("12111");
        boardSnDTOList.add(zmsForwardTencentBoardSnDTO);
        zmsForwardTencentSaveDTO.setBoardSnDTOList(boardSnDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "pushDataToB2B", zmsForwardTencentDTOList, "11111");
        PowerMockito.when(zmsForwardTencentRepository.insertForwardTencentBoardSn(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsForwardTencentService, "batchInsert", zmsForwardTencentSaveDTO, "11111");
        Assert.assertNotNull(dto);
    }

    @Test
    public void pushDataToB2B() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        String empNo = "111";
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setSvrSN("1111");
        zmsForwardTencentDTO.setEntityName("111");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getNullPropertyNames", zmsForwardTencentDTO);
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        PowerMockito.when(zmsForwardTencentRepository.insertForwardTencent(Mockito.any())).thenReturn(1);
        PowerMockito.when(zmsForwardTencentRepository.deleteForwardTencent(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsForwardTencentService, "pushDataToB2B", zmsForwardTencentDTOList, empNo);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getNullPropertyNames() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        Whitebox.invokeMethod(zmsForwardTencentService, "getNullPropertyNames", zmsForwardTencentDTO);
        Assert.assertNotNull(dto);
    }

    @Test
    public void setServerMotherboardSn() throws Exception {

        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO = new ZmsForwardTencentEntityDTO();
        zmsForwardTencentEntityDTO.setEntityId("1111");
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("1111");
        zmsCbomInfoDTO.setCbomNameCn("2222");
        zmsCbomInfoDTO.setCbomVerison("3333");
        configDescList.add(zmsCbomInfoDTO);
        zmsForwardTencentEntityDTO.setConfigDescList(configDescList);
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = new ArrayList<>();
        ZmsForwardTencentBatchRuleDTO zmsForwardTencentBatchRuleDTO = new ZmsForwardTencentBatchRuleDTO();
        zmsForwardTencentBatchRuleDTO.setPartType("内存");
        zmsForwardTencentBatchRuleDTO.setPartBrand("innodisk");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setSnStart(8);
        zmsForwardTencentBatchRuleDTO.setSnEnd(10);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        zmsForwardTencentEntityDTO.setBatchRuleDTOList(batchRuleDTOList);
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(Mockito.any())).thenReturn(configDetailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("1111");
        cpmConfigItemAssembleDTO.setItemBarcode("1111");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(Mockito.any())).thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("4");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        customerItemsDTOList.clear();
        customerItemsDTO.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("2222");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("1111");
        customerItemsDTO.setCustomerComponentType("整机");
        customerItemsDTOList.add(customerItemsDTO);
        customerItemsDTO2.setProjectType("8");
        customerItemsDTO2.setZteCode("1111");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        List<CustomerItemsDTO> motherboardList = new ArrayList<>();
        List<String> boardSnList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getMainBoardSn", configDetailDTOList, motherboardList);
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(Mockito.any())).thenReturn(wsmAssembleLinesList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("1111");
        wsmAssembleLinesEntityDTO.setItemQty(22);
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(Mockito.any())).thenReturn(wsmAssembleLinesList);
        Whitebox.invokeMethod(zmsOverallUnitService, "getAllWsmAssembleLines", wsmAssembleLinesList);
        List<CustomerItemsDTO> serviceSnCustomerItemsDTOList = new ArrayList<>();
        List<String> serviceSnBoardSnList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getServiceSnBoardSn", serviceSnCustomerItemsDTOList, wsmAssembleLinesList);
        List<String> shelfModItemBarcodeList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getThreeWsmAssembleLines",shelfModItemBarcodeList, wsmAssembleLinesList);
        ZmsSspTaskInfoDTO zmsSspTaskInfoDTO = new ZmsSspTaskInfoDTO();
        String ff = "{\n" +
                "\t\"sspList\": [{\n" +
                "\t\t\"contract_id\": \"DTHL2024121721ITPTH515P1\",\n" +
                "\t\t\"NICList\": [{\n" +
                "\t\t\t\"firmware_ver\": \"\",\n" +
                "\t\t\t\"num\": \"1\",\n" +
                "\t\t\t\"item_name\": \"双光口100G DPU网卡(BF3)\",\n" +
                "\t\t\t\"brand_name\": \"MELLANOX\",\n" +
                "\t\t\t\"port1_mac\": \"C4:70:BD:0B:48:13\",\n" +
                "\t\t\t\"bmc_mac\": \"C4:70:BD:0B:48:13\",\n" +
                "\t\t\t\"slot\": \"SLOT10\",\n" +
                "\t\t\t\"media_num\": \"1\",\n" +
                "\t\t\t\"media_type\": \"智能网卡\",\n" +
                "\t\t\t\"itemcode\": \"053101700045\",\n" +
                "\t\t\t\"part_number\": \"900-9D3B6-00SC-EA0\",\n" +
                "\t\t\t\"port2_mac\": \"\",\n" +
                "\t\t\t\"sn\": \"MT2430600M5G\"\n" +
                "\t\t}],\n" +
                "\t\t\"memoryList\": [{\n" +
                "\t\t\t\"itemcode\": \"0060401C0037\",\n" +
                "\t\t\t\"num\": \"32\",\n" +
                "\t\t\t\"part_number\": \"M321R8GA0PB0-CWMKJ\",\n" +
                "\t\t\t\"item_name\": \"64G DDR5 5600\",\n" +
                "\t\t\t\"brand_name\": \"Samsung\",\n" +
                "\t\t\t\"sn\": \"80CE042420504AF78C\",\n" +
                "\t\t\t\"slot\": \"CPU2_B0\"\n" +
                "\t\t}],\n" +
                "\t\t\"sub_package_name\": \"HA2-XI7-400D\",\n" +
                "\t\t\"diskList\": [{\n" +
                "\t\t\t\"media_num\": \"4\",\n" +
                "\t\t\t\"media_type\": \"NVMeSSD\",\n" +
                "\t\t\t\"itemcode\": \"006120800201\",\n" +
                "\t\t\t\"num\": \"5\",\n" +
                "\t\t\t\"part_number\": \"SOLIDIGM SSDPF2KX038T1\",\n" +
                "\t\t\t\"item_name\": \"SSDPF2KX038T1\",\n" +
                "\t\t\t\"brand_name\": \"INTEL\",\n" +
                "\t\t\t\"sn\": \"BTAX432202LZ3P8CGN\",\n" +
                "\t\t\t\"slot\": \"109\"\n" +
                "\t\t}],\n" +
                "\t\t\"manufacture_name\": \"ZTE\",\n" +
                "\t\t\"package_name\": \"HA2-XI7\",\n" +
                "\t\t\"cpuList\": [{\n" +
                "\t\t\t\"itemcode\": \"008020101053\",\n" +
                "\t\t\t\"num\": \"2\",\n" +
                "\t\t\t\"part_number\": \"INTEL(R) XEON(R) PLATINUM 8563C\",\n" +
                "\t\t\t\"item_name\": \"52核 2.6GHz Intel Xeon Platinum 处理器 8563C\",\n" +
                "\t\t\t\"brand_name\": \"INTEL\",\n" +
                "\t\t\t\"sn\": \"E115DE61214F98ED\",\n" +
                "\t\t\t\"slot\": \"CPU2\"\n" +
                "\t\t}],\n" +
                "\t\t\"GPUList\": [],\n" +
                "\t\t\"sn\": \"219592931675\",\n" +
                "\t\t\"raidList\": [],\n" +
                "\t\t\"mainboard\": {\n" +
                "\t\t\t\"bios_ver\": \"04.25.01.00\",\n" +
                "\t\t\t\"num\": \"1\",\n" +
                "\t\t\t\"bmc_firmware_ver\": \"04.24.04.30\",\n" +
                "\t\t\t\"item_name\": \"主板\",\n" +
                "\t\t\t\"brand_name\": \"ZTE\",\n" +
                "\t\t\t\"bmc_mac\": \"90C710001560\",\n" +
                "\t\t\t\"slot\": \"1\",\n" +
                "\t\t\t\"mac2\": \"90C710001562\",\n" +
                "\t\t\t\"mac1\": \"90C710001561\",\n" +
                "\t\t\t\"itemcode\": \"130000215048\",\n" +
                "\t\t\t\"name\": \"主板\",\n" +
                "\t\t\t\"part_number\": \"\",\n" +
                "\t\t\t\"bios_ver_date\": \"\",\n" +
                "\t\t\t\"sn\": \"767810800071\"\n" +
                "\t\t},\n" +
                "\t\t\"asset_num\": \"\",\n" +
                "\t\t\"node_asset_num\": \"\"\n" +
                "\t}],\n" +
                "\t\"task_id\": \"RSVRR6900G5-M20250100735\"\n" +
                "}";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ff);
        when(JacksonJsonConverUtil.jsonToBean(Mockito.any(),Mockito.any())).thenReturn(zmsSspTaskInfoDTO);
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentDTO", zmsForwardTencentEntityDTO, customerItemsDTOList, zmsForwardTencentDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentBoardSnDTO", zmsForwardTencentEntityDTO, boardSnList, serviceSnBoardSnList);
        Whitebox.invokeMethod(zmsForwardTencentService, "setServerMotherboardSn", zmsForwardTencentEntityDTO);

        String empNo = "0668000680";
        List<ZmsForwardTencentEntityDTO> zmsForwardTencentEntityDTOS = new ArrayList<>();
        zmsForwardTencentEntityDTOS.add(zmsForwardTencentEntityDTO);
        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("1111");
        customerItemsDTO.setProjectType("5");
        customerItemsDTO.setCustomerComponentType("整机");
        customerItemsDTOList.add(customerItemsDTO);
        customerItemsDTO2.setProjectType("3");
        customerItemsDTO2.setZteCode("1111");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        List<QualityCodeOutputDTO> forWardDTOList = new ArrayList<>();
        QualityCodeOutputDTO qualityCodeOutputDTO = new QualityCodeOutputDTO();
        qualityCodeOutputDTO.setQualityCode(STR_NUMBER_ZERO);
        forWardDTOList.add(qualityCodeOutputDTO);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(any())).thenReturn(forWardDTOList);
        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("1111");
        customerItemsDTO.setProjectType("5");
        customerItemsDTO.setCustomerComponentType("整机");
        customerItemsDTOList.add(customerItemsDTO);
        customerItemsDTO2.setProjectType("3");
        customerItemsDTO2.setZteCode("1111");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        forWardDTOList.clear();
        qualityCodeOutputDTO.setQualityCode(STR_NUMBER_THREE);
        forWardDTOList.add(qualityCodeOutputDTO);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(any())).thenReturn(forWardDTOList);
        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("1111");
        customerItemsDTO.setProjectType("5");
        customerItemsDTO.setCustomerComponentType("整机");
        customerItemsDTOList.add(customerItemsDTO);
        customerItemsDTO2.setProjectType("3");
        customerItemsDTO2.setZteCode("1111");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        forWardDTOList.clear();
        qualityCodeOutputDTO.setQualityCode(STR_NUMBER_THREE);
        qualityCodeOutputDTO.setErrCause("113344");
        forWardDTOList.add(qualityCodeOutputDTO);
        PowerMockito.when(centerfactoryRemoteService.getRealTimeInteractiveB2B(any())).thenReturn(forWardDTOList);
        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("1111");
        customerItemsDTO.setProjectType("5");
        customerItemsDTO.setCustomerComponentType("整机");
        customerItemsDTOList.add(customerItemsDTO);
        customerItemsDTO2.setProjectType("3");
        customerItemsDTO2.setZteCode("1111");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineData", zmsForwardTencentEntityDTOS, empNo, dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getThreeWsmAssembleLines() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<String> itemBarcodeList = new ArrayList<>();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);

        itemBarcodeList.add("1111");
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList1 = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(Mockito.any())).thenReturn(wsmAssembleLinesList1);
        Whitebox.invokeMethod(zmsForwardTencentService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("02111");
        wsmAssembleLinesList1.add(wsmAssembleLinesEntityDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(Mockito.any())).thenReturn(wsmAssembleLinesList1);
        Whitebox.invokeMethod(zmsForwardTencentService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);

        wsmAssembleLinesList1.clear();
        wsmAssembleLinesEntityDTO.setItemCode("12111");
        wsmAssembleLinesEntityDTO.setItemBarcode("111");
        wsmAssembleLinesList1.add(wsmAssembleLinesEntityDTO);
        itemBarcodeList.clear();
        itemBarcodeList.add("23");
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(Mockito.any())).thenReturn(wsmAssembleLinesList1);
        Whitebox.invokeMethod(zmsForwardTencentService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getZmsForwardTencentDTOList() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentDTOList", configDetailDTOList, wsmAssembleLinesList, customerItemsDTOList);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("111");
        customerItemsDTO.setProjectType("2");
        customerItemsDTOList.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("222");
        customerItemsDTO2.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO2);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("111");
        cpmConfigItemAssembleDTO.setBarcodeQty("1");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO2 = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO2.setItemCode("222");
        cpmConfigItemAssembleDTO2.setBarcodeQty("1");
        configDetailDTOList.add(cpmConfigItemAssembleDTO2);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO3 = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO3.setItemCode("333");
        cpmConfigItemAssembleDTO3.setBarcodeQty("1");
        configDetailDTOList.add(cpmConfigItemAssembleDTO3);
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("111");
        wsmAssembleLinesEntityDTO.setItemQty(1);
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO2 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO2.setItemCode("222");
        wsmAssembleLinesEntityDTO2.setItemQty(1);
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO2);
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO3 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO3.setItemCode("333");
        wsmAssembleLinesEntityDTO3.setItemQty(1);
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO3);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentDTOList", configDetailDTOList, wsmAssembleLinesList, customerItemsDTOList);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getZmsForwardTencentDTO() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO = new ZmsForwardTencentEntityDTO();
        zmsForwardTencentEntityDTO.setEntityName("1111");
        zmsForwardTencentEntityDTO.setEntityId("1111");
        zmsForwardTencentEntityDTO.setEmpNo("111");
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        List<CustomerItemsDTO> serviceSnCustomerItemsDTOList = new ArrayList<>();
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentDTO", zmsForwardTencentEntityDTO, customerItemsDTOList, zmsForwardTencentDTOList);

        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setItemCode("111");
        zmsForwardTencentDTO.setEntityName("2222");
        zmsForwardTencentDTO.setPartType("GPU");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
        ZmsForwardTencentDTO zmsForwardTencentDTO2 = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO2.setItemCode("111");
        zmsForwardTencentDTO2.setEntityName("1111");
        zmsForwardTencentDTO.setPartType("GPU");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO2);
        ZmsForwardTencentDTO zmsForwardTencentDTO3 = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO3.setItemCode("222");
        zmsForwardTencentDTO3.setEntityName("1111");
        zmsForwardTencentDTO3.setPartType("GPU");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO3);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("111");
        customerItemsDTO.setCustomerComponentType("111");
        serviceSnCustomerItemsDTOList.add(customerItemsDTO);
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = new ArrayList<>();
        ZmsForwardTencentBatchRuleDTO zmsForwardTencentBatchRuleDTO = new ZmsForwardTencentBatchRuleDTO();
        zmsForwardTencentBatchRuleDTO.setPartType("内存");
        zmsForwardTencentBatchRuleDTO.setPartBrand("innodisk");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setSnStart(8);
        zmsForwardTencentBatchRuleDTO.setSnEnd(10);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        zmsForwardTencentEntityDTO.setBatchRuleDTOList(batchRuleDTOList);
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("1111");
        zmsCbomInfoDTO.setCbomNameCn("2222");
        zmsCbomInfoDTO.setCbomVerison("3333");
        configDescList.add(zmsCbomInfoDTO);
        zmsForwardTencentEntityDTO.setConfigDescList(configDescList);
        ZmsSspTaskInfoDTO zmsSspTaskInfoDTO = new ZmsSspTaskInfoDTO();
        String ff = "{\n" +
                "\t\"sspList\": [{\n" +
                "\t\t\"contract_id\": \"DTHL2024121721ITPTH515P1\",\n" +
                "\t\t\"NICList\": [{\n" +
                "\t\t\t\"firmware_ver\": \"\",\n" +
                "\t\t\t\"num\": \"1\",\n" +
                "\t\t\t\"item_name\": \"双光口100G DPU网卡(BF3)\",\n" +
                "\t\t\t\"brand_name\": \"MELLANOX\",\n" +
                "\t\t\t\"port1_mac\": \"C4:70:BD:0B:48:13\",\n" +
                "\t\t\t\"bmc_mac\": \"C4:70:BD:0B:48:13\",\n" +
                "\t\t\t\"slot\": \"SLOT10\",\n" +
                "\t\t\t\"media_num\": \"1\",\n" +
                "\t\t\t\"media_type\": \"智能网卡\",\n" +
                "\t\t\t\"itemcode\": \"053101700045\",\n" +
                "\t\t\t\"part_number\": \"900-9D3B6-00SC-EA0\",\n" +
                "\t\t\t\"port2_mac\": \"\",\n" +
                "\t\t\t\"sn\": \"MT2430600M5G\"\n" +
                "\t\t}],\n" +
                "\t\t\"memoryList\": [{\n" +
                "\t\t\t\"itemcode\": \"0060401C0037\",\n" +
                "\t\t\t\"num\": \"32\",\n" +
                "\t\t\t\"part_number\": \"M321R8GA0PB0-CWMKJ\",\n" +
                "\t\t\t\"item_name\": \"64G DDR5 5600\",\n" +
                "\t\t\t\"brand_name\": \"Samsung\",\n" +
                "\t\t\t\"sn\": \"80CE042420504AF78C\",\n" +
                "\t\t\t\"slot\": \"CPU2_B0\"\n" +
                "\t\t}],\n" +
                "\t\t\"sub_package_name\": \"HA2-XI7-400D\",\n" +
                "\t\t\"diskList\": [{\n" +
                "\t\t\t\"media_num\": \"4\",\n" +
                "\t\t\t\"media_type\": \"NVMeSSD\",\n" +
                "\t\t\t\"itemcode\": \"006120800201\",\n" +
                "\t\t\t\"num\": \"5\",\n" +
                "\t\t\t\"part_number\": \"SOLIDIGM SSDPF2KX038T1\",\n" +
                "\t\t\t\"item_name\": \"SSDPF2KX038T1\",\n" +
                "\t\t\t\"brand_name\": \"INTEL\",\n" +
                "\t\t\t\"sn\": \"BTAX432202LZ3P8CGN\",\n" +
                "\t\t\t\"slot\": \"109\"\n" +
                "\t\t}],\n" +
                "\t\t\"manufacture_name\": \"ZTE\",\n" +
                "\t\t\"package_name\": \"HA2-XI7\",\n" +
                "\t\t\"cpuList\": [{\n" +
                "\t\t\t\"itemcode\": \"008020101053\",\n" +
                "\t\t\t\"num\": \"2\",\n" +
                "\t\t\t\"part_number\": \"INTEL(R) XEON(R) PLATINUM 8563C\",\n" +
                "\t\t\t\"item_name\": \"52核 2.6GHz Intel Xeon Platinum 处理器 8563C\",\n" +
                "\t\t\t\"brand_name\": \"INTEL\",\n" +
                "\t\t\t\"sn\": \"E115DE61214F98ED\",\n" +
                "\t\t\t\"slot\": \"CPU2\"\n" +
                "\t\t}],\n" +
                "\t\t\"GPUList\": [],\n" +
                "\t\t\"sn\": \"219592931675\",\n" +
                "\t\t\"raidList\": [],\n" +
                "\t\t\"mainboard\": {\n" +
                "\t\t\t\"bios_ver\": \"04.25.01.00\",\n" +
                "\t\t\t\"num\": \"1\",\n" +
                "\t\t\t\"bmc_firmware_ver\": \"04.24.04.30\",\n" +
                "\t\t\t\"item_name\": \"主板\",\n" +
                "\t\t\t\"brand_name\": \"ZTE\",\n" +
                "\t\t\t\"bmc_mac\": \"90C710001560\",\n" +
                "\t\t\t\"slot\": \"1\",\n" +
                "\t\t\t\"mac2\": \"90C710001562\",\n" +
                "\t\t\t\"mac1\": \"90C710001561\",\n" +
                "\t\t\t\"itemcode\": \"130000215048\",\n" +
                "\t\t\t\"name\": \"主板\",\n" +
                "\t\t\t\"part_number\": \"\",\n" +
                "\t\t\t\"bios_ver_date\": \"\",\n" +
                "\t\t\t\"sn\": \"767810800071\"\n" +
                "\t\t},\n" +
                "\t\t\"asset_num\": \"\",\n" +
                "\t\t\"node_asset_num\": \"\"\n" +
                "\t}],\n" +
                "\t\"task_id\": \"RSVRR6900G5-M20250100735\"\n" +
                "}";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ff);
        when(JacksonJsonConverUtil.jsonToBean(Mockito.any(),Mockito.any())).thenReturn(zmsSspTaskInfoDTO);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchImesCustomerInfo", zmsForwardTencentDTO, customerItemsDTOList);
        List<ZmsGPUTempDTO> zmsGPUTempDTOS = new ArrayList<>();
        ZmsGPUTempDTO zmsGPUTempDTO = new ZmsGPUTempDTO();
        zmsGPUTempDTO.setGpuSn("123456789");
        zmsGPUTempDTOS.add(zmsGPUTempDTO);
        when(zmsForwardTencentRepository.getGpnTemp(Mockito.any())).thenReturn(zmsGPUTempDTOS);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentDTO", zmsForwardTencentEntityDTO, customerItemsDTOList, zmsForwardTencentDTOList);
        when(zmsForwardTencentRepository.getGpnTemp(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentDTO", zmsForwardTencentEntityDTO, customerItemsDTOList, zmsForwardTencentDTOList);
        Assert.assertNotNull(dto);
    }

    @Test
    public void bbOrZkOtherCustomerInfo() throws Exception {
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setPartType("GPU");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        zmsForwardTencentDTO.setPartType("背板");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        zmsForwardTencentDTO.setPartType("子卡");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("123");
        customerItemsDTO.setCustomerModel("123");
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        zmsForwardTencentDTO.setItemCode("234");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        zmsForwardTencentDTO.setDeviceClassName("234");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        zmsForwardTencentDTO.setDeviceClassName("123");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        zmsForwardTencentDTO.setItemCode("123");
        Whitebox.invokeMethod(zmsForwardTencentService, "bbOrZkOtherCustomerInfo", customerItemsDTOList, zmsForwardTencentDTO);
        Assert.assertNotNull(zmsForwardTencentDTO);
    }

    @Test
    public void selectItem() throws Exception {
        List<ZmsSspTaskItemDTO> items =new ArrayList<>();
        List<CustomerItemsDTO> customerItems = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("9");
        customerItemsDTO.setZteCode("123");
        customerItems.add(customerItemsDTO);
        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("8");
        customerItemsDTO.setZteCode("123");
        customerItems.add(customerItemsDTO);
        ZmsForwardTencentDTO dto = new ZmsForwardTencentDTO();
        dto.setItemCode("123");
        List<ZmsForwardTencentBatchRuleDTO> batchRules = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "selectItem", items, customerItems,dto,batchRules);
        ZmsSspTaskItemDTO item1 = new ZmsSspTaskItemDTO();
        item1.setType("GPU");
        item1.setUbbType(1);
        items.add(item1);
        ZmsSspTaskItemDTO item2= new ZmsSspTaskItemDTO();
        item2.setType("子卡");
        items.add(item2);
        Whitebox.invokeMethod(zmsForwardTencentService, "selectItem", items, customerItems,dto,batchRules);
        customerItems = new ArrayList<>();
        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("9");
        customerItemsDTO.setZteCode("234");
        customerItems.add(customerItemsDTO);
        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("8");
        customerItemsDTO.setZteCode("234");
        customerItems.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "selectItem", items, customerItems,dto,batchRules);
        items=new ArrayList<>();
        item1 = new ZmsSspTaskItemDTO();
        item1.setType("GPU");
        item1.setUbbType(2);
        items.add(item1);
        Whitebox.invokeMethod(zmsForwardTencentService, "selectItem", items, customerItems,dto,batchRules);
        Assert.assertNotNull(dto);
    }

    @Test
    public void matchImesCustomerInfo() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setItemCode("111");
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "matchImesCustomerInfo", zmsForwardTencentDTO, customerItemsDTOList);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("222");
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchImesCustomerInfo", zmsForwardTencentDTO, customerItemsDTOList);

        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("111");
        customerItemsDTO.setCustomerComponentType(ZH_ZHU_BAN);
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchImesCustomerInfo", zmsForwardTencentDTO, customerItemsDTOList);

        customerItemsDTOList.clear();
        CustomerItemsDetailDTO customerItemsDetailDTO = new CustomerItemsDetailDTO();
        customerItemsDetailDTO.setPcbVersion("111");
        customerItemsDTO.setParamsDetail(customerItemsDetailDTO);
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchImesCustomerInfo", zmsForwardTencentDTO, customerItemsDTOList);

        customerItemsDTOList.clear();
        customerItemsDTO.setCustomerComponentType(ZH_ZHENG_JI);
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchImesCustomerInfo", zmsForwardTencentDTO, customerItemsDTOList);
        Assert.assertNotNull(dto);
    }

    @Test
    public void matchSspTaskInfo() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setItemCode("111");
        ZmsSspTaskInfoDTO zmsSspTaskInfoDTO = new ZmsSspTaskInfoDTO();
        zmsSspTaskInfoDTO.setSspList(null);
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = new ArrayList<>();
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        List<ZmsSspTaskDtailDTO> zmsSspTaskDtailDTOList = new ArrayList<>();
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        ZmsSspTaskDtailDTO zmsSspTaskDtailDTO = new ZmsSspTaskDtailDTO();
        zmsSspTaskDtailDTO.setAssetNum("111");
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        List<ZmsSspTaskItemDTO> zmsSspTaskItemDTOList = new ArrayList<>();
        ZmsSspTaskItemDTO zmsSspTaskItemDTO = new ZmsSspTaskItemDTO();
        zmsSspTaskItemDTO.setItemCode("222");
        zmsSspTaskItemDTOList.add(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTO.setCpuList(zmsSspTaskItemDTOList);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        zmsSspTaskItemDTOList.clear();
        zmsSspTaskItemDTO.setItemCode("111");
        zmsSspTaskItemDTO.setPort1Mac(null);
        zmsSspTaskItemDTO.setPort2Mac("111");
        zmsSspTaskItemDTOList.add(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTO.setCpuList(null);
        zmsSspTaskDtailDTO.setMemoryList(zmsSspTaskItemDTOList);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 1,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        zmsSspTaskItemDTOList.clear();
        zmsSspTaskItemDTO.setPort1Mac("111");
        zmsSspTaskItemDTOList.add(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTO.setMemoryList(null);
        zmsSspTaskDtailDTO.setNicList(zmsSspTaskItemDTOList);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        zmsSspTaskDtailDTO.setNicList(null);
        zmsSspTaskDtailDTO.setDiskList(zmsSspTaskItemDTOList);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        List<String> snList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, "内存", "innodisk", "123456789123456789");
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        zmsSspTaskDtailDTO.setDiskList(null);
        zmsSspTaskDtailDTO.setGpuList(zmsSspTaskItemDTOList);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        snList.add("111111");
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, "内存", "innodisk", "123456789123456789");
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        zmsSspTaskDtailDTO.setGpuList(null);
        zmsSspTaskDtailDTO.setRaidList(zmsSspTaskItemDTOList);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        for(int i=0;i<200;i++){
            snList.add("1111111111"+i);
        }
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, "内存", "innodisk", "123456789123456789");
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        zmsSspTaskDtailDTOList.clear();
        zmsSspTaskDtailDTO.setRaidList(null);
        zmsSspTaskItemDTO.setSn("1111");
        zmsSspTaskDtailDTO.setMainBoard(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTOList.add(zmsSspTaskDtailDTO);
        zmsSspTaskInfoDTO.setSspList(zmsSspTaskDtailDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, "内存", "innodisk", "123456789123456789");
        Whitebox.invokeMethod(zmsForwardTencentService, "matchSspTaskInfo", zmsForwardTencentDTO, zmsSspTaskInfoDTO, batchRuleDTOList, 0,customerItemsDTOS);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getZmsSspTaskItemDTOList() throws Exception{
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");

        ZmsSspTaskDtailDTO zmsSspTaskDtailDTO = new ZmsSspTaskDtailDTO();
        zmsSspTaskDtailDTO.setAssetNum("111");
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        List<ZmsSspTaskItemDTO> zmsSspTaskItemDTOList = new ArrayList<>();
        ZmsSspTaskItemDTO zmsSspTaskItemDTO = new ZmsSspTaskItemDTO();
        zmsSspTaskItemDTO.setItemCode("222");
        zmsSspTaskItemDTOList.add(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTO.setCpuList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskItemDTOList.clear();
        zmsSspTaskItemDTO.setItemCode("111");
        zmsSspTaskItemDTO.setPort2Mac("111");
        zmsSspTaskItemDTOList.add(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTO.setCpuList(null);
        zmsSspTaskDtailDTO.setMemoryList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskItemDTOList.clear();
        zmsSspTaskItemDTO.setPort1Mac("111");
        zmsSspTaskItemDTOList.add(zmsSspTaskItemDTO);
        zmsSspTaskDtailDTO.setMemoryList(null);
        zmsSspTaskDtailDTO.setNicList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setNicList(null);
        zmsSspTaskDtailDTO.setDiskList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setDiskList(null);
        zmsSspTaskDtailDTO.setGpuList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setGpuList(null);
        zmsSspTaskDtailDTO.setRaidList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setRaidList(null);
        zmsSspTaskDtailDTO.setOpticalList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setOpticalList(null);
        zmsSspTaskDtailDTO.setPsuList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setPsuList(null);
        zmsSspTaskDtailDTO.setBpList(zmsSspTaskItemDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        zmsSspTaskDtailDTO.setBpList(null);
        zmsSspTaskItemDTO.setSn("1111");
        zmsSspTaskDtailDTO.setMainBoard(zmsSspTaskItemDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsSspTaskItemDTOList", zmsSspTaskDtailDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getProductionBatch() throws Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<ZmsForwardTencentBatchRuleDTO> batchRuleDTOList = new ArrayList<>();
        String partType = "SSD";
        String partBrand = "Tencent";
        String serverSn = "123456789123456789";
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        ZmsForwardTencentBatchRuleDTO zmsForwardTencentBatchRuleDTO = new ZmsForwardTencentBatchRuleDTO();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setSnStart(8);
        zmsForwardTencentBatchRuleDTO.setSnEnd(10);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD1");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("N");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(19);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent1");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);

        batchRuleDTOList.clear();
        zmsForwardTencentBatchRuleDTO.setPartType("SSD");
        zmsForwardTencentBatchRuleDTO.setPartBrand("Tencent");
        zmsForwardTencentBatchRuleDTO.setSnLen(18);
        zmsForwardTencentBatchRuleDTO.setEnabledFlag("Y");
        batchRuleDTOList.add(zmsForwardTencentBatchRuleDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getProductionBatch", batchRuleDTOList, partType, partBrand, serverSn);
        Assert.assertNotNull(dto);
    }

    @Test
    public  void getSspTaskInfo() throws  Exception {
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        String entityName = "1111";
        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("3434");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(anyString(),anyString())).thenReturn("3434");
        String ff = "{}";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(any(), any(), anyString())).thenReturn(ff);
        Whitebox.invokeMethod(zmsForwardTencentService, "getSspTaskInfo", entityName);

        ff = "{\n" +
                "\t\"sspList\": [{\n" +
                "\t\t\"contract_id\": \"DTHL2024121721ITPTH515P1\",\n" +
                "\t\t\"NICList\": [{\n" +
                "\t\t\t\"firmware_ver\": \"\",\n" +
                "\t\t\t\"num\": \"1\",\n" +
                "\t\t\t\"item_name\": \"双光口100G DPU网卡(BF3)\",\n" +
                "\t\t\t\"brand_name\": \"MELLANOX\",\n" +
                "\t\t\t\"port1_mac\": \"C4:70:BD:0B:48:13\",\n" +
                "\t\t\t\"bmc_mac\": \"C4:70:BD:0B:48:13\",\n" +
                "\t\t\t\"slot\": \"SLOT10\",\n" +
                "\t\t\t\"media_num\": \"1\",\n" +
                "\t\t\t\"media_type\": \"智能网卡\",\n" +
                "\t\t\t\"itemcode\": \"053101700045\",\n" +
                "\t\t\t\"part_number\": \"900-9D3B6-00SC-EA0\",\n" +
                "\t\t\t\"port2_mac\": \"\",\n" +
                "\t\t\t\"sn\": \"MT2430600M5G\"\n" +
                "\t\t}],\n" +
                "\t\t\"memoryList\": [{\n" +
                "\t\t\t\"itemcode\": \"0060401C0037\",\n" +
                "\t\t\t\"num\": \"32\",\n" +
                "\t\t\t\"part_number\": \"M321R8GA0PB0-CWMKJ\",\n" +
                "\t\t\t\"item_name\": \"64G DDR5 5600\",\n" +
                "\t\t\t\"brand_name\": \"Samsung\",\n" +
                "\t\t\t\"sn\": \"80CE042420504AF78C\",\n" +
                "\t\t\t\"slot\": \"CPU2_B0\"\n" +
                "\t\t}],\n" +
                "\t\t\"sub_package_name\": \"HA2-XI7-400D\",\n" +
                "\t\t\"diskList\": [{\n" +
                "\t\t\t\"media_num\": \"4\",\n" +
                "\t\t\t\"media_type\": \"NVMeSSD\",\n" +
                "\t\t\t\"itemcode\": \"006120800201\",\n" +
                "\t\t\t\"num\": \"5\",\n" +
                "\t\t\t\"part_number\": \"SOLIDIGM SSDPF2KX038T1\",\n" +
                "\t\t\t\"item_name\": \"SSDPF2KX038T1\",\n" +
                "\t\t\t\"brand_name\": \"INTEL\",\n" +
                "\t\t\t\"sn\": \"BTAX432202LZ3P8CGN\",\n" +
                "\t\t\t\"slot\": \"109\"\n" +
                "\t\t}],\n" +
                "\t\t\"manufacture_name\": \"ZTE\",\n" +
                "\t\t\"package_name\": \"HA2-XI7\",\n" +
                "\t\t\"cpuList\": [{\n" +
                "\t\t\t\"itemcode\": \"008020101053\",\n" +
                "\t\t\t\"num\": \"2\",\n" +
                "\t\t\t\"part_number\": \"INTEL(R) XEON(R) PLATINUM 8563C\",\n" +
                "\t\t\t\"item_name\": \"52核 2.6GHz Intel Xeon Platinum 处理器 8563C\",\n" +
                "\t\t\t\"brand_name\": \"INTEL\",\n" +
                "\t\t\t\"sn\": \"E115DE61214F98ED\",\n" +
                "\t\t\t\"slot\": \"CPU2\"\n" +
                "\t\t}],\n" +
                "\t\t\"GPUList\": [],\n" +
                "\t\t\"sn\": \"219592931675\",\n" +
                "\t\t\"raidList\": [],\n" +
                "\t\t\"mainboard\": {\n" +
                "\t\t\t\"bios_ver\": \"04.25.01.00\",\n" +
                "\t\t\t\"num\": \"1\",\n" +
                "\t\t\t\"bmc_firmware_ver\": \"04.24.04.30\",\n" +
                "\t\t\t\"item_name\": \"主板\",\n" +
                "\t\t\t\"brand_name\": \"ZTE\",\n" +
                "\t\t\t\"bmc_mac\": \"90C710001560\",\n" +
                "\t\t\t\"slot\": \"1\",\n" +
                "\t\t\t\"mac2\": \"90C710001562\",\n" +
                "\t\t\t\"mac1\": \"90C710001561\",\n" +
                "\t\t\t\"itemcode\": \"130000215048\",\n" +
                "\t\t\t\"name\": \"主板\",\n" +
                "\t\t\t\"part_number\": \"\",\n" +
                "\t\t\t\"bios_ver_date\": \"\",\n" +
                "\t\t\t\"sn\": \"767810800071\"\n" +
                "\t\t},\n" +
                "\t\t\"asset_num\": \"\",\n" +
                "\t\t\"node_asset_num\": \"\"\n" +
                "\t}],\n" +
                "\t\"task_id\": \"RSVRR6900G5-M20250100735\"\n" +
                "}";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(any(), any(), anyString())).thenReturn(ff);
        ZmsSspTaskInfoDTO zmsSspTaskInfoDTO = new ZmsSspTaskInfoDTO();
        when(JacksonJsonConverUtil.jsonToBean(Mockito.any(),Mockito.any())).thenReturn(zmsSspTaskInfoDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getSspTaskInfo", entityName);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getZmsForwardTencentBoardSnDTO() throws Exception{
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        ZmsForwardTencentEntityDTO zmsForwardTencentEntityDTO = new ZmsForwardTencentEntityDTO();
        List<String> boardSnList = new ArrayList<>();
        List<String> serviceSnBoardSnList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentBoardSnDTO", zmsForwardTencentEntityDTO, boardSnList, serviceSnBoardSnList);

        serviceSnBoardSnList.add("1111");
        Whitebox.invokeMethod(zmsForwardTencentService, "getZmsForwardTencentBoardSnDTO", zmsForwardTencentEntityDTO, boardSnList, serviceSnBoardSnList);
        Assert.assertNotNull(dto);
    }

    @Test
    public  void getMainBoardSn() throws Exception{
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        List<CustomerItemsDTO> motherboardList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getMainBoardSn", configDetailDTOList, motherboardList);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("222");
        cpmConfigItemAssembleDTO.setItemBarcode("222222");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("111");
        motherboardList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getMainBoardSn", configDetailDTOList, motherboardList);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO2 = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO2.setItemCode("111");
        cpmConfigItemAssembleDTO2.setItemBarcode("222222");
        configDetailDTOList.add(cpmConfigItemAssembleDTO2);
        Whitebox.invokeMethod(zmsForwardTencentService, "getMainBoardSn", configDetailDTOList, motherboardList);
        Assert.assertNotNull(dto);
    }

    @Test
    public  void getServiceSnBoardSn() throws Exception{
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        Whitebox.invokeMethod(zmsForwardTencentService, "getServiceSnBoardSn", customerItemsDTOList, wsmAssembleLinesList);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("4");
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getServiceSnBoardSn", customerItemsDTOList, wsmAssembleLinesList);

        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setProjectType("5");
        customerItemsDTO2.setZteCode("222");
        customerItemsDTOList.add(customerItemsDTO2);
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("111");
        wsmAssembleLinesEntityDTO.setItemBarcode("222");
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        Whitebox.invokeMethod(zmsForwardTencentService, "getServiceSnBoardSn", customerItemsDTOList, wsmAssembleLinesList);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO2 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO2.setItemCode("222");
        wsmAssembleLinesEntityDTO2.setItemBarcode("222");
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO2);
        Whitebox.invokeMethod(zmsForwardTencentService, "getServiceSnBoardSn", customerItemsDTOList, wsmAssembleLinesList);
        Assert.assertNotNull(dto);
    }

    @Test
    public  void uploadCompleteMachineDataByManual() throws Exception{
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        List<String> recordIdList = new ArrayList<>();
        String empNo = "111";
        List<ZmsForwardTencentDTO> zmsForwardTencentDTOList = new ArrayList<>();
        PowerMockito.when(zmsForwardTencentRepository.getForwardTencentByRecordId(Mockito.any())).thenReturn(zmsForwardTencentDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineDataByManual", recordIdList, empNo);

        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        zmsForwardTencentDTO.setRecordId(new BigDecimal(1));
        zmsForwardTencentDTO.setMessageId("111");
        zmsForwardTencentDTOList.add(zmsForwardTencentDTO);
        PowerMockito.when(zmsForwardTencentRepository.getForwardTencentByRecordId(Mockito.any())).thenReturn(zmsForwardTencentDTOList);
        Whitebox.invokeMethod(zmsForwardTencentService, "getNullPropertyNames", zmsForwardTencentDTO);
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        PowerMockito.when(zmsForwardTencentRepository.updateForwardTencentByRecordId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsForwardTencentService, "uploadCompleteMachineDataByManual", recordIdList, empNo);
        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:id49ay948e8fd0c1417b0a97b1256a1f5c589c54 */

    @Test
    public  void updateMesInfoUploadFailedLog() throws Exception{
        ZmsForwardTencentInDTO dto = new ZmsForwardTencentInDTO();
        dto.setUserAddress("腾讯");
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Whitebox.invokeMethod(zmsForwardTencentService, "updateMesInfoUploadFailedLog", customerDataLogDTO);

        customerDataLogDTO.setMessageType(ZTE_IMES_TENCENT_FORWARD_UPLOAD);
        String dataType = "11";
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn(dataType);
        PowerMockito.when(zmsForwardTencentRepository.updateInternetMain(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsForwardTencentService, "updateMesInfoUploadFailedLog", customerDataLogDTO);

        customerDataLogDTO.setMessageType(ZTE_IMES_ALIBABA_AIS_META_MCT);
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn(dataType);
        PowerMockito.when(zmsForwardTencentRepository.updateInternetMain(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsForwardTencentService, "updateMesInfoUploadFailedLog", customerDataLogDTO);

        Assert.assertNotNull(dto);
    }


    /* Started by AICoder, pid:n11fav72bai6a06140610988909de738f196e999 */
    @Test
    public void testGetThreeWsmAssembleLines_SecondLayerEmpty() {
        List<String> itemBarcodeList = Lists.newArrayList("barcode1", "barcode2");
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("1");
        wsmAssembleLinesEntityDTO.setItemBarcode("barcode3");
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO2 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO2.setItemCode("2");
        wsmAssembleLinesEntityDTO2.setItemBarcode("barcode4");
        List<WsmAssembleLinesEntityDTO> firstLayerList = Lists.newArrayList(wsmAssembleLinesEntityDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(itemBarcodeList)).thenReturn(firstLayerList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(Lists.newArrayList("barcode4"))).thenReturn(Collections.emptyList());
        zmsForwardTencentService.getThreeWsmAssembleLines(itemBarcodeList, wsmAssembleLinesList);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO3 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("1");
        wsmAssembleLinesEntityDTO.setItemBarcode("barcode6");
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(Lists.newArrayList("barcode4"))).thenReturn(Lists.newArrayList(wsmAssembleLinesEntityDTO3));
        zmsForwardTencentService.getThreeWsmAssembleLines(itemBarcodeList, wsmAssembleLinesList);
        wsmAssembleLinesEntityDTO.setItemCode("2");
        zmsForwardTencentService.getThreeWsmAssembleLines(itemBarcodeList, wsmAssembleLinesList);
        Assert.assertEquals(3, wsmAssembleLinesList.size());
    }
    /* Ended by AICoder, pid:n11fav72bai6a06140610988909de738f196e999 */

    /* Started by AICoder, pid:dbbd7h67afs03c514c7b0a89006f4e210b26f1fa */
    @Test
    public void test_getBarcodeExpandMap_WithProdBatchNo() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("getBarcodeExpandMap", List.class);
        method.setAccessible(true);

        List<String> barcodeList = Lists.newArrayList("barcode1", "barcode2");

        BarcodeExpandDTO dto1 = new BarcodeExpandDTO();
        dto1.setBarcode("");
        dto1.setProdBatchNo("batch1");

        BarcodeExpandDTO dto2 = new BarcodeExpandDTO();
        dto2.setBarcode("barcode2");
        dto2.setProdBatchNo("batch2");

        List<BarcodeExpandDTO> barcodeExpandList = Lists.newArrayList(dto1, dto2);

        Mockito.when(barcodeCenterRemoteService.expandQuery(Mockito.any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandList);

        Map<String, String> result = (Map<String, String>) method.invoke(zmsForwardTencentService, barcodeList);

        Assert.assertEquals("batch2", result.get("barcode2"));
    }

    /* Ended by AICoder, pid:dbbd7h67afs03c514c7b0a89006f4e210b26f1fa */



    /* Started by AICoder, pid:rac43yd5cas33e3140700b4f904371514003d363 */
    @Test
    public void test_ncOtherCustomerInfo_PartTypeNotMatch() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("ncOtherCustomerInfo", List.class, ZmsForwardTencentDTO.class);
        method.setAccessible(true);
        ZmsForwardTencentDTO  zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
         zmsForwardTencentDTO.setPartType("NOT_ZH_NC");

        method.invoke(zmsForwardTencentService, customerItemsDTOList, zmsForwardTencentDTO);

        Assert.assertNull(zmsForwardTencentDTO.getMaterialsCode());
        Assert.assertNull(zmsForwardTencentDTO.getOriPartPN());
        Assert.assertNull(zmsForwardTencentDTO.getScanPartPN());
    }

    @Test
    public void test_ncOtherCustomerInfo_NoMatchingCustomerItem() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("ncOtherCustomerInfo", List.class, ZmsForwardTencentDTO.class);
        method.setAccessible(true);
        ZmsForwardTencentDTO  zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        zmsForwardTencentDTO.setPartType(ZH_NC);
        zmsForwardTencentDTO.setItemCode("ITEM_CODE");
        zmsForwardTencentDTO.setPartNumber("PART_NUMBER");

        method.invoke(zmsForwardTencentService, customerItemsDTOList, zmsForwardTencentDTO);

        Assert.assertNull(zmsForwardTencentDTO.getMaterialsCode());
        Assert.assertNull(zmsForwardTencentDTO.getOriPartPN());
        Assert.assertNull(zmsForwardTencentDTO.getScanPartPN());
    }

    @Test
    public void test_ncOtherCustomerInfo_MatchingCustomerItem() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("ncOtherCustomerInfo", List.class, ZmsForwardTencentDTO.class);
        method.setAccessible(true);
        ZmsForwardTencentDTO  zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        zmsForwardTencentDTO.setPartType(ZH_NC);
        zmsForwardTencentDTO.setItemCode("ITEM_CODE");
        zmsForwardTencentDTO.setPartNumber("PART_NUMBER");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("ITEM_CODE");
        customerItemsDTO.setZteBrandStyle("PART_NUMBER");
        customerItemsDTO.setCustomerCode("CUSTOMER_CODE");
        customerItemsDTO.setPnCode("PN_CODE");
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("ITEM_CODE1");
        CustomerItemsDTO customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("ITEM_CODE");
        customerItemsDTO3.setZteBrandStyle("PART_NUMBER1");
        customerItemsDTOList.add(customerItemsDTO2);
        customerItemsDTOList.add(customerItemsDTO3);
        customerItemsDTOList.add(customerItemsDTO);
        method.invoke(zmsForwardTencentService, customerItemsDTOList, zmsForwardTencentDTO);

        Assert.assertEquals("CUSTOMER_CODE", zmsForwardTencentDTO.getMaterialsCode());
        Assert.assertEquals("PN_CODE", zmsForwardTencentDTO.getOriPartPN());
        Assert.assertEquals("PN_CODE", zmsForwardTencentDTO.getScanPartPN());
    }
    /* Ended by AICoder, pid:rac43yd5cas33e3140700b4f904371514003d363 */
    /* Started by AICoder, pid:t2832j3ef2b403b1460609359049925e8174862d */
    @Test
    public void test_setProductionBatch_CableOrFan() throws Exception {

        ZmsForwardTencentEntityDTO dto = new ZmsForwardTencentEntityDTO();
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        Map<String, String> barcodeExpandMap = new HashMap<>();

        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("setProductionBatch", ZmsForwardTencentEntityDTO.class, ZmsForwardTencentDTO.class, Map.class, String.class);
        method.setAccessible(true);

        zmsForwardTencentDTO.setPartType(ZH_CABLE);
        zmsForwardTencentDTO.setEntityName("RSVRG228AD-M20240200326");

        method.invoke(zmsForwardTencentService, dto, zmsForwardTencentDTO, barcodeExpandMap, "scanPartSN");

        Assert.assertEquals("2402", zmsForwardTencentDTO.getProductionBatch());

        zmsForwardTencentDTO.setPartType(ZH_FS);
        method.invoke(zmsForwardTencentService, dto, zmsForwardTencentDTO, barcodeExpandMap, "scanPartSN");
        Assert.assertEquals("2402", zmsForwardTencentDTO.getProductionBatch());

        zmsForwardTencentDTO.setEntityName("RSVRG228AD-s20240200326");
        method.invoke(zmsForwardTencentService, dto, zmsForwardTencentDTO, barcodeExpandMap, "scanPartSN");

        zmsForwardTencentDTO.setPartType("qt");
        method.invoke(zmsForwardTencentService, dto, zmsForwardTencentDTO, barcodeExpandMap, "scanPartSN");
        Assert.assertEquals(null, zmsForwardTencentDTO.getProductionBatch());



    }

    @Test
    public void test_setProductionBatch_ZK() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("setProductionBatch", ZmsForwardTencentEntityDTO.class, ZmsForwardTencentDTO.class, Map.class, String.class);
        method.setAccessible(true);
        ZmsForwardTencentEntityDTO dto = new ZmsForwardTencentEntityDTO();
        dto.setBatchRuleDTOList(Lists.newArrayList());
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        Map<String, String> barcodeExpandMap = new HashMap<>();
        zmsForwardTencentDTO.setPartType(ZH_ZK);
        zmsForwardTencentDTO.setPartVendor("VENDOR");
        zmsForwardTencentDTO.setOriPartSN("ORI_PART_SN");

        method.invoke(zmsForwardTencentService, dto, zmsForwardTencentDTO, barcodeExpandMap, "scanPartSN");

        Assert.assertNotNull(method);
    }

    @Test
    public void test_setProductionBatch_CPU() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("setProductionBatch", ZmsForwardTencentEntityDTO.class, ZmsForwardTencentDTO.class, Map.class, String.class);
        method.setAccessible(true);
        ZmsForwardTencentEntityDTO dto = new ZmsForwardTencentEntityDTO();
        ZmsForwardTencentDTO zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        Map<String, String> barcodeExpandMap = new HashMap<>();
        zmsForwardTencentDTO.setPartType(ZH_CPU);
        barcodeExpandMap.put("scanPartSN", "CPU_BATCH");
        method.invoke(zmsForwardTencentService, dto, zmsForwardTencentDTO, barcodeExpandMap, "scanPartSN");
        Assert.assertEquals("CPU_BATCH", zmsForwardTencentDTO.getProductionBatch());
    }
    /* Ended by AICoder, pid:t2832j3ef2b403b1460609359049925e8174862d */

    /* Started by AICoder, pid:u2cc49dce2k6dd314cd90b0d70850f11ecb149b0 */
    @Test
    public void test_setPCBAVersion_EmptyScanPartSN() throws Exception {
        Method method = ZmsForwardTencentServiceImpl.class.getDeclaredMethod("setPCBAVersion", ZmsForwardTencentDTO.class, CustomerItemsDTO.class);
        method.setAccessible(true);
        ZmsForwardTencentDTO  zmsForwardTencentDTO = new ZmsForwardTencentDTO();
        CustomerItemsDTO  customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerComponentType(ZH_ZHU_BAN);
        zmsForwardTencentDTO.setScanPartSN("");
        method.invoke(zmsForwardTencentService, zmsForwardTencentDTO, customerItemsDTO);
        Assert.assertNull(zmsForwardTencentDTO.getPCBAVersion());
        zmsForwardTencentDTO.setScanPartSN("SN123");
        method.invoke(zmsForwardTencentService, zmsForwardTencentDTO, customerItemsDTO);

        SpecifiedPsTaskDTO specifiedPsTaskDTO = new SpecifiedPsTaskDTO();
        specifiedPsTaskDTO.setPcbVersion("V1.0");

        List<SpecifiedPsTaskDTO> specifiedPsTaskList = new ArrayList<>();
        specifiedPsTaskList.add(specifiedPsTaskDTO);
        Mockito.when(centerfactoryRemoteService.getSpecifiedPsTaskList(Mockito.any(SpecifiedPsTaskReq.class)))
                .thenReturn(specifiedPsTaskList);
        method.invoke(zmsForwardTencentService, zmsForwardTencentDTO, customerItemsDTO);
        Assert.assertEquals("V1.0", zmsForwardTencentDTO.getPCBAVersion());
    }
    /* Ended by AICoder, pid:u2cc49dce2k6dd314cd90b0d70850f11ecb149b0 */
}
