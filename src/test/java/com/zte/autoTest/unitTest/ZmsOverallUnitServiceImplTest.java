package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsOverallUnitServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsForwardTencentRepository;
import com.zte.domain.model.datawb.ZmsOverallUnitRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_824004200010;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @since 2024年2月4日10:11:17
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ZmsStationLogUploadServiceImpl.class, CenterfactoryRemoteService.class,
        WsmAssembleLinesService.class, JacksonJsonConverUtil.class,
        JSON.class, CommonUtils.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsOverallUnitServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ZmsOverallUnitServiceImpl zmsOverallUnitService;
    @Mock
    private ZmsOverallUnitRepository zmsOverallUnitRepository;

    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;

    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Mock
    private CfgCodeRuleItemService cfgCodeRuleItemService;
    @Mock
    private ZmsForwardTencentRepository zmsForwardTencentRepository;
    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    private OverallUnitMeiTuanInDTO overallUnitMeiTuanInDTO = new OverallUnitMeiTuanInDTO();
    private String empNo;
    private List<OverallUnitMeiTuanDTO> overallUnitMeiTuanDTOList = new ArrayList<>();
    private List<OverallUnitMeiTuanLogDTO> correctDataList = new ArrayList<>();
    private List<OverallUnitMeiTuanLogDTO> tempInsertList = new ArrayList<>();
    private OverallUnitMeiTuanDTO overallUnitMeiTuanDTO = new OverallUnitMeiTuanDTO();
    private CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
    private OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO = new OverallUnitMeiTuanLogDTO();
    private List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList = new ArrayList<>();
    private List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
    private final List<WsmAssembleLinesEntityDTO> wsmAssembleLinesEntityDTOList = new ArrayList<>();
    private List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
    private final List<ZmsStationItemDTO> stations = new ArrayList<>();
    private List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(zmsOverallUnitService, "b2bSize", 100);

        List<String> entityNameList = Arrays.asList("111", "222", "333");
        List<String> organizationIdList = Arrays.asList("4917", "4437");
        List<String> statusNameList = Arrays.asList("装配", "调试", "包装");
        overallUnitMeiTuanInDTO = new OverallUnitMeiTuanInDTO();
        overallUnitMeiTuanInDTO.setUserAddress("userAddress");
        overallUnitMeiTuanInDTO.setStatusNameList(statusNameList);
        overallUnitMeiTuanInDTO.setEntityNameLike("-M");
        overallUnitMeiTuanInDTO.setOrganizationIdList(organizationIdList);
        overallUnitMeiTuanInDTO.setEntityNameList(entityNameList);
        empNo = "empNo";
        overallUnitMeiTuanDTO = new OverallUnitMeiTuanDTO();
        overallUnitMeiTuanDTO.setDirectiveNumber("11");
        overallUnitMeiTuanDTO.setContractNumber("22");
        overallUnitMeiTuanDTO.setEntityId("333");
        overallUnitMeiTuanDTO.setMfgSiteId("44");
        overallUnitMeiTuanDTO.setEntityName("55");
        overallUnitMeiTuanDTO.setWorkOrderId("66");
        overallUnitMeiTuanDTO.setUserAddress("777");
        overallUnitMeiTuanDTOList = new ArrayList<>();
        overallUnitMeiTuanDTOList.add(overallUnitMeiTuanDTO);
        correctDataList = Arrays.asList(new OverallUnitMeiTuanLogDTO(), new OverallUnitMeiTuanLogDTO());

        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerName("11");
        customerItemsDTO.setProjectPhase("2");
        customerItemsDTO.setProjectName("3");
        customerItemsDTO.setCooperationMode("4");
        customerItemsDTO.setZteCode("5");
        customerItemsDTO.setProjectType(NumConstant.NUM_THREE + "");

        overallUnitMeiTuanLogDTO = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO.setChassisSn("11");
        overallUnitMeiTuanLogDTO.setNodeSn("serviceSn");
        overallUnitMeiTuanLogDTO.setManufacturerModel("22");
        overallUnitMeiTuanLogDTO.setType(Constant.STR_NUMBER_ONE);
        overallUnitMeiTuanLogDTO.setDirectiveNumber(overallUnitMeiTuanDTO.getDirectiveNumber());
        overallUnitMeiTuanLogDTO.setWorkorderId(overallUnitMeiTuanDTO.getWorkOrderId());
        overallUnitMeiTuanLogDTO.setBrand(Constant.ZTE_EN);
        overallUnitMeiTuanLogDTO.setManufacturerName(Constant.ZTE_CN);
        overallUnitMeiTuanLogDTO.setModel("model");
        overallUnitMeiTuanLogDTO.setBoardSn("210000000000");

        tempInsertList = new ArrayList<>();
        tempInsertList.add(overallUnitMeiTuanLogDTO);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemBarcode("11");
        cpmConfigItemAssembleDTO.setBarcodeType("22");
        cpmConfigItemAssembleDTO.setItemCode("11");
        cpmConfigItemAssembleDTO.setEntityId("222");
        cpmConfigItemAssembleDTO.setItemId("123");
        cpmConfigItemAssembleDTOList = new ArrayList<>();
        cpmConfigItemAssembleDTOList.add(cpmConfigItemAssembleDTO);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("11");
        wsmAssembleLinesEntityDTO.setItemBarcode("222");
        wsmAssembleLinesEntityDTOList.add(wsmAssembleLinesEntityDTO);

        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_ORGID)));
        sysLookupValues1.setDescription("1,2,3");
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_USRADD)));
        sysLookupValues2.setDescription("User Address");
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_ENAME)));
        sysLookupValues3.setDescription("Entity Name");
        SysLookupValues sysLookupValues4 = new SysLookupValues();
        sysLookupValues4.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_STAT)));
        sysLookupValues4.setDescription("Status1,Status2");
        SysLookupValues sysLookupValues5 = new SysLookupValues();
        sysLookupValues5.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_BOQ)));
        sysLookupValues5.setDescription("Boq Lookup Type Value");
        SysLookupValues sysLookupValues6 = new SysLookupValues();
        sysLookupValues6.setLookupCode(BigDecimal.valueOf(Long.parseLong(Constant.LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_MDS)));
        sysLookupValues6.setDescription("MDS");
        sysLookupValuesList = Arrays.asList(
                sysLookupValues1,
                sysLookupValues2,
                sysLookupValues3,
                sysLookupValues4,
                sysLookupValues5,
                sysLookupValues6
        );

        List<ZmsStationProblemDTO> zmsStationProblemDTOS = new ArrayList<>();
        ZmsStationProblemDTO zmsStationProblemDTO = new ZmsStationProblemDTO();
        zmsStationProblemDTO.setProblemId("1");
        zmsStationProblemDTO.setProblemType("2");
        zmsStationProblemDTOS.add(zmsStationProblemDTO);
        ZmsStationItemDTO zmsStationItemDTO1 = new ZmsStationItemDTO();
        zmsStationItemDTO1.setState("1");
        zmsStationItemDTO1.setName("2");
        zmsStationItemDTO1.setStationId("12");
        zmsStationItemDTO1.setStartTime(223);
        zmsStationItemDTO1.setEndTime(333);
        stations.add(zmsStationItemDTO1);
        ZmsStationItemDTO zmsStationItemDTO2 = new ZmsStationItemDTO();
        zmsStationItemDTO2.setState("3");
        zmsStationItemDTO2.setName("4");
        zmsStationItemDTO2.setStationId("12");
        zmsStationItemDTO2.setStartTime(223);
        zmsStationItemDTO2.setEndTime(333);
        zmsStationItemDTO2.setProblems(zmsStationProblemDTOS);
        stations.add(zmsStationItemDTO2);

        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(sysLookupValuesList);
        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("11");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(anyString(), anyString())).thenReturn("http://1.2.3,4");
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("[{" +
                "\"serverSn\": \"210000000000\"," +
                "\"stations\":[{" +
                "\"stationId\":\"诊断Pro工序123\"," +
                "\"name\":\"诊断Pro工序\"," +
                "\"attribute\":\"测试\"," +
                "\"state\":\"PASS\",\"startTime\":1684131904," +
                "\"endTime\":" +
                "1684131914," +
                "\"problems\":[" +
                "{" +
                "\"problemId\":\"\"," +
                "\"phenomenonType\":\"\"," +
                "\"phenomenonDetail\":\"\"" +
                "}]" +
                "}," +
                "{" +
                "\"stationId\":\"装配Pro工序546\"," +
                "\"name\":\"装配Pro工序\"," +
                "\"attribute\":\"测试\"," +
                "\"state\":\"FAIL\"," +
                "\"startTime\":1684131904," +
                "\"endTime\":" +
                "1684131914," +
                "\"problems\":[" +
                "{" +
                "\"problemId\":\"装配Pro工序546\"," +
                "\"phenomenonType\":\"装配测试Pro工序\"," +
                "\"phenomenonDetail\":\"IBM_HOST上下电检测\"" +
                "}" +
                "]" +
                "}]" +
                "}]");
    }

    @Test
    public void testUploadOverallUnitData() throws Exception {
        String boqLookupTypeValue = "23252";
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(anyString(), anyString())).thenReturn(boqLookupTypeValue);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(sysLookupValuesList);
        PowerMockito.when(zmsOverallUnitRepository.getMeiTuanEntity(any())).thenReturn(null);
        zmsOverallUnitService.uploadOverallUnitData(overallUnitMeiTuanInDTO, empNo);
        PowerMockito.when(zmsOverallUnitRepository.getMeiTuanEntity(any())).thenReturn(overallUnitMeiTuanDTOList);
        try {
            overallUnitMeiTuanInDTO.setUserAddress(null);
            zmsOverallUnitService.uploadOverallUnitData(overallUnitMeiTuanInDTO, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTITY_NAME_NEED_USER_ADDRESS, e.getMessage());
        }
        try {
            zmsOverallUnitService.uploadOverallUnitData(new OverallUnitMeiTuanInDTO(), empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Assert.assertNotNull(overallUnitMeiTuanInDTO);
    }

    @Test
    public void testGetLookupMeanListEmptySysLookupValuesList() {
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(Collections.emptyList());
        try {
            zmsOverallUnitService.getLookupMeanList(new OverallUnitMeiTuanInDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Assert.assertNotNull(sysLookupValuesList);
    }

    @Test
    public void testGetLookupMeanList_NormalFlow() {
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(null);
        OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
        try {
            zmsOverallUnitService.getLookupMeanList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(sysLookupValuesList);
        try {
            zmsOverallUnitService.getLookupMeanList(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Assert.assertNotNull(sysLookupValuesList);
    }

    @Test
    public void testUploadCompleteMachineData() throws Exception {
        zmsOverallUnitService.uploadCompleteMachineData(new ArrayList<>(), empNo);
        zmsOverallUnitService.uploadCompleteMachineData(overallUnitMeiTuanDTOList, empNo);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt())).thenReturn(cpmConfigItemAssembleDTOList);
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        CommonUtils.copyProperties(customerItemsDTO1, customerItemsDTO);
        CommonUtils.copyProperties(customerItemsDTO2, customerItemsDTO);
        customerItemsDTO1.setZteCode("11");
        customerItemsDTO2.setZteCode("11");
        customerItemsDTO2.setProjectType(NumConstant.NUM_FIVE + "");
        customerItemsDTOS.clear();
        customerItemsDTOS.add(customerItemsDTO1);
        customerItemsDTOS.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList())).thenReturn(customerItemsDTOS);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesEntityDTOList);
        zmsOverallUnitService.uploadCompleteMachineData(overallUnitMeiTuanDTOList, empNo);
        Assert.assertNotNull(sysLookupValuesList);
    }

    @Test
    public void testSinceBatchInsert() throws Exception {
        zmsOverallUnitService.batchInsert(correctDataList, null);
        zmsOverallUnitService.batchInsert(null, null);
        zmsOverallUnitService.batchInsert(correctDataList, empNo);

        zmsOverallUnitService.pushDataToB2B(empNo, tempInsertList);
        overallUnitMeiTuanLogDTO.setCustomerItemsDTO(customerItemsDTO);
        tempInsertList.add(overallUnitMeiTuanLogDTO);
        zmsOverallUnitService.pushDataToB2B(empNo, tempInsertList);
        Mockito.doThrow(new NullPointerException()).when(centerfactoryRemoteService).pushDataToB2B(Mockito.anyList());
        try {
            zmsOverallUnitService.pushDataToB2B(empNo, tempInsertList);
        } catch (Exception e) {
            e.printStackTrace();
            assertNotNull(e.toString());
        }

        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt())).thenReturn(null);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(anyInt())).thenReturn(cpmConfigItemAssembleDTOList);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList())).thenReturn(null);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        customerItemsDTOS = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList())).thenReturn(customerItemsDTOS);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        customerItemsDTOS.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        CommonUtils.copyProperties(customerItemsDTO1, customerItemsDTO);
        customerItemsDTOS.add(customerItemsDTO1);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList())).thenReturn(customerItemsDTOS);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        customerItemsDTOS.clear();
        customerItemsDTOS.add(customerItemsDTO);
        customerItemsDTOS.forEach(item -> {
            item.setProjectType("2");
        });
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList())).thenReturn(customerItemsDTOS);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        customerItemsDTO1.setZteCode("11");
        customerItemsDTOS.clear();
        customerItemsDTOS.add(customerItemsDTO1);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(), anyList())).thenReturn(customerItemsDTOS);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(null);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        wsmAssembleLinesEntityDTOList.clear();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("045647474");
        wsmAssembleLinesEntityDTO.setItemBarcode("2100003526234");
        wsmAssembleLinesEntityDTOList.add(wsmAssembleLinesEntityDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesEntityDTOList);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        wsmAssembleLinesEntityDTO.setItemCode("15465474");
        wsmAssembleLinesEntityDTOList.add(wsmAssembleLinesEntityDTO);
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesEntityDTOList2 = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesEntityDTOList);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(anyList())).thenReturn(wsmAssembleLinesEntityDTOList2);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesEntityDTOList);
        wsmAssembleLinesEntityDTO.setItemCode("054654744567");
        wsmAssembleLinesEntityDTOList2.add(wsmAssembleLinesEntityDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(anyList())).thenReturn(wsmAssembleLinesEntityDTOList2);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterials(anyString())).thenReturn(wsmAssembleLinesEntityDTOList);
        wsmAssembleLinesEntityDTOList2.clear();
        wsmAssembleLinesEntityDTO.setItemCode("13464747");
        wsmAssembleLinesEntityDTOList2.add(wsmAssembleLinesEntityDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialList(anyList())).thenReturn(wsmAssembleLinesEntityDTOList2);
        zmsOverallUnitService.setServerMotherboardSn(overallUnitMeiTuanDTO);

        zmsOverallUnitService.setMainBoardSn(overallUnitMeiTuanLogDTO, cpmConfigItemAssembleDTOList, customerItemsDTOList);
        customerItemsDTOList.add(customerItemsDTO);
        zmsOverallUnitService.setMainBoardSn(overallUnitMeiTuanLogDTO, cpmConfigItemAssembleDTOList, customerItemsDTOList);
        customerItemsDTO1.setProjectType(NumConstant.NUM_FIVE + "");
        customerItemsDTOList.add(customerItemsDTO1);
        zmsOverallUnitService.setMainBoardSn(overallUnitMeiTuanLogDTO, cpmConfigItemAssembleDTOList, customerItemsDTOList);

        customerItemsDTOList.clear();
        overallUnitMeiTuanLogDTO.setBoardSn(null);
        zmsOverallUnitService.getCompleteMachineDataLogEntityDTO(overallUnitMeiTuanLogDTO, customerItemsDTOList,
                wsmAssembleLinesEntityDTOList);
        overallUnitMeiTuanLogDTO.setBoardSn("6457474");
        zmsOverallUnitService.getCompleteMachineDataLogEntityDTO(overallUnitMeiTuanLogDTO, customerItemsDTOList,
                wsmAssembleLinesEntityDTOList);
        customerItemsDTO1.setZteCode("1234");
        customerItemsDTOList.clear();
        customerItemsDTOList.add(customerItemsDTO1);
        overallUnitMeiTuanLogDTO.setBoardSn(null);
        zmsOverallUnitService.getCompleteMachineDataLogEntityDTO(overallUnitMeiTuanLogDTO, customerItemsDTOList,
                wsmAssembleLinesEntityDTOList);
        customerItemsDTO1.setProjectType("1234");
        customerItemsDTOList.clear();
        customerItemsDTOList.add(customerItemsDTO1);
        overallUnitMeiTuanLogDTO.setBoardSn(null);
        zmsOverallUnitService.getCompleteMachineDataLogEntityDTO(overallUnitMeiTuanLogDTO, customerItemsDTOList,
                wsmAssembleLinesEntityDTOList);
        overallUnitMeiTuanLogDTO.setBoardSn("11");
        zmsOverallUnitService.getCompleteMachineDataLogEntityDTO(overallUnitMeiTuanLogDTO, customerItemsDTOList,
                wsmAssembleLinesEntityDTOList);
        zmsOverallUnitService.getCompleteMachineDataLogEntityDTO(overallUnitMeiTuanLogDTO, null,
                wsmAssembleLinesEntityDTOList);

        List<OverallUnitMeiTuanLogDTO> correctDataList1 = new ArrayList<>();
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO1 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO1.setContractNumber("11");
        overallUnitMeiTuanLogDTO1.setChassisSn("11");
        overallUnitMeiTuanLogDTO1.setEntityName("11");
        overallUnitMeiTuanLogDTO1.setEntityId("11");
        overallUnitMeiTuanLogDTO1.setStationName("API");
        correctDataList1.add(overallUnitMeiTuanLogDTO1);
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO2 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO2.setContractNumber("11");
        overallUnitMeiTuanLogDTO2.setChassisSn("11");
        overallUnitMeiTuanLogDTO2.setEntityName("11");
        overallUnitMeiTuanLogDTO2.setEntityId("11");
        overallUnitMeiTuanLogDTO2.setStationName("PO");
        correctDataList1.add(overallUnitMeiTuanLogDTO2);
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO3 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO3.setContractNumber("22");
        overallUnitMeiTuanLogDTO3.setChassisSn("22");
        overallUnitMeiTuanLogDTO3.setEntityName("22");
        overallUnitMeiTuanLogDTO3.setEntityId("22");
        overallUnitMeiTuanLogDTO3.setNodeSn("210000000000");
        overallUnitMeiTuanLogDTO3.setStationName("MTHC");
        correctDataList1.add(overallUnitMeiTuanLogDTO3);

        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(anyString())).thenReturn(sysLookupValuesList);
        zmsOverallUnitService.getParamFromMdsApi(correctDataList1);
        zmsOverallUnitService.getParamFromMdsApi(tempInsertList);
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("");
        zmsOverallUnitService.getParamFromMdsApi(correctDataList1);

        zmsOverallUnitService.mdsProcessesCheck(stations, null);
        zmsOverallUnitService.mdsProcessesCheck(stations, sysLookupValuesList);

        String stationName = "12";
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPE_OVERALL_UNIT_MEITUAN, LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_00007)).thenReturn(stationName);
        List<OverallUnitMeiTuanLogDTO> overallUnitMeiTuanLogDTOList = new ArrayList<>();
        PowerMockito.when(zmsOverallUnitRepository.queryPushOverAllUnitSuccessList(any(), any())).thenReturn(overallUnitMeiTuanLogDTOList);
        List<StationLogDTO> stationLogDTOList = new ArrayList<>();
        overallUnitMeiTuanLogDTO.setModel(null);
        PowerMockito.when(zmsStationLogUploadRepository.getStationLogName(any(), any())).thenReturn(stationLogDTOList);
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("[{" +
                "\"station_id\":\"210000000000\"," +
                "\"serverSn\":\"22\"," +
                "\"fileType\":2," +
                "\"log_name\":\"\"" +
                "}]");
        zmsOverallUnitService.joinResWithMdsProcesses(stations, overallUnitMeiTuanLogDTO);
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("[{" +
                "\"station_id\":\"210000000000\"," +
                "\"serverSn\":\"22\"," +
                "\"fileType\":2," +
                "\"log_name\":\"12345\"" +
                "}]");
        zmsOverallUnitService.joinResWithMdsProcesses(stations, overallUnitMeiTuanLogDTO);
        stationName = "12,2";
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPE_OVERALL_UNIT_MEITUAN, LOOKUP_TYPE_OVERALL_UNIT_MEITUAN_00007)).thenReturn(stationName);
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO4 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO4.setRequestId("ZTE12");
        overallUnitMeiTuanLogDTO4.setChassisSn("11");
        overallUnitMeiTuanLogDTOList.add(overallUnitMeiTuanLogDTO4);
        PowerMockito.when(zmsOverallUnitRepository.queryPushOverAllUnitSuccessList(any(), any())).thenReturn(overallUnitMeiTuanLogDTOList);
        StationLogDTO stationLogDTO = new StationLogDTO();
        stationLogDTO.setLogName("342352");
        stationLogDTO.setStationId("2");
        stationLogDTO.setServerSn("serviceSn");
        stationLogDTOList.add(stationLogDTO);
        overallUnitMeiTuanLogDTO.setModel("3453463");
        PowerMockito.when(zmsStationLogUploadRepository.getStationLogName(any(), any())).thenReturn(stationLogDTOList);
        zmsOverallUnitService.joinResWithMdsProcesses(stations, overallUnitMeiTuanLogDTO);

        overallUnitMeiTuanLogDTOList.clear();
        overallUnitMeiTuanLogDTO4.setRequestId("23423");
        overallUnitMeiTuanLogDTOList.add(overallUnitMeiTuanLogDTO4);
        PowerMockito.when(zmsOverallUnitRepository.queryPushOverAllUnitSuccessList(any(), any())).thenReturn(overallUnitMeiTuanLogDTOList);
        stationLogDTOList.clear();
        stationLogDTO.setStationId("4");
        stationLogDTOList.add(stationLogDTO);
        PowerMockito.when(zmsStationLogUploadRepository.getStationLogName(any(), any())).thenReturn(stationLogDTOList);
        zmsOverallUnitService.joinResWithMdsProcesses(stations, overallUnitMeiTuanLogDTO);
        Assert.assertNotNull(correctDataList1);
    }

    @Test
    public void updateOverAllUnitMeiTuanFromMES() {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setSn("32423542");
        dto.setContractNo("235423532");
        PowerMockito.when(zmsOverallUnitRepository.updateMeiTuanOverAllUnit(any())).thenReturn(1);
        zmsOverallUnitService.updateOverAllUnitMeiTuanFromMES(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void isPushMeiTuan() throws Exception {

        zmsOverallUnitService.stationNameList = new ArrayList<>();
        zmsOverallUnitService.stationNameList.add("MTHC");
        StationLogDTO result = zmsOverallUnitService.isPushMeiTuan("2", "2", "222");
        Assert.assertEquals(result.getFileType().toString(), "1");

        result = zmsOverallUnitService.isPushMeiTuan("2", "MTHC", "788");
        Assert.assertEquals(result.getFileType().toString(), "1");

        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("3434");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(anyString(), anyString())).thenReturn("3434");
        String ff = " []";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(any(), any(), anyString())).thenReturn(ff);
        result = zmsOverallUnitService.isPushMeiTuan("2", "MTHC", "");
        Assert.assertEquals(result.getFileType().toString(), "0");

        ff = " [{\n" +
                "\t\"stationLog\": \"https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/CLOUDMDS/508db880-9053-4ff5-8351-329b6e1cc62e\",\n" +
                "\t\"log_name\": \"219520324615_2024-05-05-05-12-00.zip\",\n" +
                "\t\"station_id\": \"MTHC33\",\n" +
                "\t\"serverSn\": \"219520324615\"\n" +
                "}]";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(any(), any(), anyString())).thenReturn(ff);
        result = zmsOverallUnitService.isPushMeiTuan("2", "MTHC", "");
        Assert.assertEquals(result.getFileType().toString(), "0");


        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("3434");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(anyString(), anyString())).thenReturn("3434");
        ff = " [{\n" +
                "\t\"stationLog\": \"https://idrive.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/CLOUDMDS/508db880-9053-4ff5-8351-329b6e1cc62e\",\n" +
                "\t\"log_name\": \"219520324615_2024-05-05-05-12-00.zip\",\n" +
                "\t\"station_id\": \"MTHC\",\n" +
                "\t\"serverSn\": \"219520324615\"\n" +
                "}]";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(any(), any(), anyString())).thenReturn(ff);
        result = zmsOverallUnitService.isPushMeiTuan("2", "MTHC", "");
        Assert.assertEquals(result.getFileType().toString(), "1");
    }

    /*Started by AICoder, pid:c2f9f24667fc5eb1435c09bf5008de53d3f43b14*/
    @Test
    public void testIsPushMeiTuan_EmptyListRes() throws Exception {
        zmsOverallUnitService.stationNameList = new ArrayList<>();
        zmsOverallUnitService.stationNameList.add("999");
        List<OverallUnitMeiTuanLogDTO> listRes = new ArrayList<>();
        OverallUnitMeiTuanLogDTO list1 = new OverallUnitMeiTuanLogDTO();
        list1.setStationName("111");
        list1.setNodeSn("111");
        list1.setChassisSn("111");
        list1.setRequestId("ZTE333");
        OverallUnitMeiTuanLogDTO list2 = new OverallUnitMeiTuanLogDTO();
        list2.setStationName("333");
        list2.setNodeSn("111");
        list2.setOssFileKey("111");
        list2.setChassisSn("111");
        list2.setRequestId("ZTE222");
        OverallUnitMeiTuanLogDTO list3 = new OverallUnitMeiTuanLogDTO();
        list3.setStationName("444");
        list3.setNodeSn("111");
        list3.setOssFileKey("");
        list3.setChassisSn("111");
        list3.setRequestId("ZTE222");
        listRes.add(list1);
        listRes.add(list2);
        listRes.add(list3);
        List<ZmsStationItemDTO> stationsRes = new ArrayList<>();
        ZmsStationItemDTO sr1 = new ZmsStationItemDTO();
        sr1.setStationId("11");
        sr1.setStartTime(0);
        sr1.setEndTime(0);
        sr1.setState("111");
        sr1.setName("111");
        stationsRes.add(sr1);
        sr1.setStationId("111");
        stationsRes.add(sr1);
        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("11111");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200010)).thenReturn("11111");
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("");
        OverallUnitMeiTuanLogDTO dto = new OverallUnitMeiTuanLogDTO();
        dto.setNodeSn("111");
        dto.setChassisSn("111");
        PowerMockito.when(zmsOverallUnitRepository.queryPushOverAllUnitSuccessList(anyString(), anyList())).thenReturn(listRes);
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("");
        List<OverallUnitMeiTuanLogDTO> result = zmsOverallUnitService.joinResWithMdsProcesses(stationsRes, dto);
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("[{" +
                "\"station_id\":\"555\"," +
                "\"log_name\":\"诊断Pro工序\"" +
                "\"serverSn\":\"111\"" +
                "}," +
                "{" +
                "\"station_id\":\"666\"," +
                "\"log_name\":\"装配Pro工序\"" +
                "\"serverSn\":\"111\"" +
                "}]");
        result = zmsOverallUnitService.joinResWithMdsProcesses(stationsRes, dto);
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("[{" +
                "\"station_id\":\"333\"," +
                "\"log_name\":\"\"" +
                "}," +
                "{" +
                "\"station_id\":\"111\"," +
                "\"log_name\":\"\"" +
                "}]");
        result = zmsOverallUnitService.joinResWithMdsProcesses(stationsRes, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
        zmsOverallUnitService.stationNameList = new ArrayList<>();
        zmsOverallUnitService.stationNameList.add("111");
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("[{" +
                "\"station_id\":\"333\"," +
                "\"log_name\":\"诊断Pro工序\"" +
                "}," +
                "{" +
                "\"station_id\":\"111\"," +
                "\"log_name\":\"装配Pro工序\"" +
                "}]");
        result = zmsOverallUnitService.joinResWithMdsProcesses(stationsRes, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));

        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenThrow(new NullPointerException());
        try {
            result = zmsOverallUnitService.isPushMeiTuan(dto.getNodeSn(), stationsRes, listRes);
            Assert.assertTrue(CollectionUtils.isNotEmpty(result));
        } catch (Exception e) {
            e.printStackTrace();
            assertNotNull(e.toString());
        }
        Mockito.doThrow(new NullPointerException()).when(zmsStationLogUploadService).getToken();
        try {
            result = zmsOverallUnitService.isPushMeiTuan(dto.getNodeSn(), stationsRes, listRes);
            Assert.assertTrue(CollectionUtils.isNotEmpty(result));
        } catch (Exception e) {
            e.printStackTrace();
            assertNotNull(e.toString());
        }
    }

    /*Ended by AICoder, pid:c2f9f24667fc5eb1435c09bf5008de53d3f43b14*/

    @Test
    public void getOverallUnitMeiTuanLogDTO() {
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO1 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO1.setResult("FAIL");
        OverallUnitMeiTuanLogDTO result = zmsOverallUnitService.getOverallUnitMeiTuanLogDTO(overallUnitMeiTuanLogDTO1, "FAIL");
        Assert.assertEquals(result.getOssFileKey(), "");

        overallUnitMeiTuanLogDTO1.setResult("FAIL222");
        overallUnitMeiTuanLogDTO1.setOssFileKey("323");
        result = zmsOverallUnitService.getOverallUnitMeiTuanLogDTO(overallUnitMeiTuanLogDTO1, "SUCCESS");
        Assert.assertEquals(result.getOssFileKey(), "323");
    }

    /*Started by AICoder, pid:m6c59x045dq1e52142ce083a20969e7cd8353616*/
    @Test
    public void testGetOssFileKey_EmptyStationLogDTOList() {
        List<StationLogDTO> stationLogDTOList = new ArrayList<>();
        String result = zmsOverallUnitService.getOssFileKey(stationLogDTOList, "111", "222");
        assertNull(result);
    }

    @Test
    public void testGetOssFileKey_FilteredStationLogDTOListIsEmpty() {
        List<StationLogDTO> stationLogDTOList = new ArrayList<>();
        StationLogDTO stationLogDTO = new StationLogDTO();
        stationLogDTO.setStationId("111");
        stationLogDTO.setServerSn("222");
        stationLogDTOList.add(stationLogDTO);
        String result = zmsOverallUnitService.getOssFileKey(stationLogDTOList, "333", "444");
        assertNull(result);
    }

    @Test
    public void testGetOssFileKey_Success() {
        List<StationLogDTO> stationLogDTOList = new ArrayList<>();
        StationLogDTO stationLogDTO = new StationLogDTO();
        stationLogDTO.setStationId("111");
        stationLogDTO.setServerSn("222");
        stationLogDTO.setLogName("333");
        stationLogDTOList.add(stationLogDTO);
        String result = zmsOverallUnitService.getOssFileKey(stationLogDTOList, "111", "222");
        assertEquals("333", result);
    }
    /*Ended by AICoder, pid:m6c59x045dq1e52142ce083a20969e7cd8353616*/

    @Test
    public void testFilterMainLog() {
        List<OverallUnitMeiTuanLogDTO> correctDataList1 = new ArrayList<>();
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO1 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO1.setContractNumber("11");
        overallUnitMeiTuanLogDTO1.setChassisSn("11");
        overallUnitMeiTuanLogDTO1.setEntityName("11");
        overallUnitMeiTuanLogDTO1.setEntityId("11");
        overallUnitMeiTuanLogDTO1.setStationName("API");
        correctDataList1.add(overallUnitMeiTuanLogDTO1);
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO2 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO2.setContractNumber("11");
        overallUnitMeiTuanLogDTO2.setChassisSn("11");
        overallUnitMeiTuanLogDTO2.setEntityName("11");
        overallUnitMeiTuanLogDTO2.setEntityId("11");
        overallUnitMeiTuanLogDTO2.setStationName("PO");
        correctDataList1.add(overallUnitMeiTuanLogDTO2);
        OverallUnitMeiTuanLogDTO overallUnitMeiTuanLogDTO3 = new OverallUnitMeiTuanLogDTO();
        overallUnitMeiTuanLogDTO3.setContractNumber("22");
        overallUnitMeiTuanLogDTO3.setChassisSn("22");
        overallUnitMeiTuanLogDTO3.setEntityName("22");
        overallUnitMeiTuanLogDTO3.setEntityId("22");
        overallUnitMeiTuanLogDTO3.setStationName("MTHC");
        correctDataList1.add(overallUnitMeiTuanLogDTO3);
        PowerMockito.when(zmsForwardTencentRepository.insertInternetMain(Mockito.any())).thenReturn(1);
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn(null);
        zmsOverallUnitService.filterMainLog(correctDataList1, "");
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("MTHC");
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.anyString())).thenReturn("1");
        zmsOverallUnitService.filterMainLog(correctDataList1, "");
        zmsOverallUnitService.filterMainLog(correctDataList1, "123");
        assertNotNull(correctDataList1);
    }

    @Test
    public void testWriteMainLog() {
        PowerMockito.when(zmsForwardTencentRepository.insertInternetMain(Mockito.any())).thenReturn(1);
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.anyString())).thenReturn("1");
        List<ZmsInternetMainDTO> zmsInternetMainDTOList = new ArrayList<>();
        ZmsInternetMainDTO zmsInternetMainDTO1 = new ZmsInternetMainDTO();
        zmsInternetMainDTO1.setDataUpStatus("1");
        zmsInternetMainDTOList.add(zmsInternetMainDTO1);
        ZmsInternetMainDTO zmsInternetMainDTO2 = new ZmsInternetMainDTO();
        zmsInternetMainDTO2.setFailReason("12");
        zmsInternetMainDTOList.add(zmsInternetMainDTO2);
        StringBuilder failReason = new StringBuilder();
        for (int i = 0; i <= 2000; i++) {
            failReason.append("A");
        }
        ZmsInternetMainDTO zmsInternetMainDTO3 = new ZmsInternetMainDTO();
        zmsInternetMainDTO3.setFailReason(failReason.toString());
        zmsInternetMainDTOList.add(zmsInternetMainDTO3);
        zmsOverallUnitService.writeMainLog(zmsInternetMainDTOList, "123","美团");
        assertNotNull(zmsInternetMainDTOList);
    }

    @Test
    public void testFilterMainLogException() throws Exception {
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(anyMap(), anyString(), anyString())).thenThrow(new NullPointerException());
        try {
            zmsOverallUnitService.getParamFromMdsApi(correctDataList);
        } catch (Exception e) {
            e.printStackTrace();
            assertNotNull(e.toString());
        }
        Mockito.doThrow(new NullPointerException()).when(zmsStationLogUploadService).getToken();
        try {
            zmsOverallUnitService.getParamFromMdsApi(correctDataList);
        } catch (Exception e) {
            e.printStackTrace();
            assertNotNull(e.toString());
        }
    }
}
