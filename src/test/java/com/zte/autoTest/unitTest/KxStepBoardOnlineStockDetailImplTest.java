package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BoardOnlineStockDetailImpl;
import com.zte.domain.model.datawb.BoardOnlineStockDetail;
import com.zte.domain.model.datawb.BoardOnlineStockDetailRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-22 11:04
 */
public class KxStepBoardOnlineStockDetailImplTest extends PowerBaseTestCase {
    @InjectMocks
    private BoardOnlineStockDetailImpl boardOnlineStockDetailImpl;
    @Mock
    private BoardOnlineStockDetailRepository boardOnlineStockDetailRepository;

    @Test
    public void deleteBatch() {
        List<BoardOnlineStockDetail> list = new LinkedList<>();
        BoardOnlineStockDetail a1 = new BoardOnlineStockDetail();
        list.add(a1);
        Assert.assertNotNull(boardOnlineStockDetailImpl.deleteBatch(list));
    }

    @Test
    public void insertBatch() {
        List<BoardOnlineStockDetail> list = new LinkedList<>();
        BoardOnlineStockDetail a1 = new BoardOnlineStockDetail();
        list.add(a1);
        Assert.assertNotNull(boardOnlineStockDetailImpl.insertBatch(list));
    }
}
