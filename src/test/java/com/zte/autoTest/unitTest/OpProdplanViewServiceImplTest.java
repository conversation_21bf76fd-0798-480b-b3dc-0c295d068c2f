package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.OpProdplanViewServiceImpl;
import com.zte.domain.model.stepdt.VOpProdplanRepository;
import com.zte.interfaces.dto.VOpProdplanDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @date 2021-06-21 22:49
 */
public class OpProdplanViewServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private OpProdplanViewServiceImpl opProdplanViewServiceImpl;
    @Mock
    private VOpProdplanRepository vOpProdplanRepository;

    @Test
    public void queryOpProdplanByTaskNo() throws Exception {
        Assert.assertNotNull(opProdplanViewServiceImpl.queryOpProdplanByTaskNo(new VOpProdplanDTO() {{
            setTaskNoList(new LinkedList<String>() {{
                add("123");
            }});
        }}));
    }
}
