package com.zte.autoTest.unitTest;

import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.EntryBoxAppliedScanService;
import com.zte.application.datawb.MesMaterialConfigBindServiceReplace;
import com.zte.application.datawb.impl.EntryBoxAppliedScanServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.EntryBoxAppliedRepository;
import com.zte.domain.model.datawb.EntryBoxAppliedScanRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.anyObject;

/**
 * 装箱入库申请
 *
 * <AUTHOR> 彭国
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class EntryBoxAppliedScanTest extends PowerBaseTestCase {

    @InjectMocks
    private EntryBoxAppliedScanServiceImpl entryBoxAppliedScanServiceImpl;

    @Mock
    private EntryBoxAppliedScanService entryBoxAppliedScanService;
    @Mock
    private CfgCodeRuleItemService cfgCodeRuleItemService;
    @Mock
    private EntryBoxAppliedRepository entryBoxAppliedRepository;
    @Mock
    private MesMaterialConfigBindServiceReplace mesMaterialConfigBindServiceReplace;
    @Mock
    private EntryBoxAppliedScanRepository entryBoxAppliedScanRepository;


    @Test
    public void validateEntryBoxApplied() throws Exception {

        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(null);

        List<EntryBoxAppliedDTO> dtoEntryList = new ArrayList<>();
        EntryBoxAppliedDTO dtx = new EntryBoxAppliedDTO();
        dtx.setEntityName("IPTN6150-I20171000089");
        dtx.setOrganizationID(new Long(635));
        dtx.setPalletNo("TP1711M023160");
        dtoEntryList.add(dtx);
        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(dtoEntryList);
        PowerMockito.when(entryBoxAppliedRepository.validateSubInvCode(anyObject())).thenReturn("SSB");
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);

        List<Map<String, String>> dotListNumber = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("TP1711M023160", "C171009430690");
        dotListNumber.add(map);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dotListNumber);

        List<Map<String, String>> mapList = new ArrayList<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("BILLNUMBER", "C1233444");
        mapList.add(map1);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(mapList);
        List<EntryBoxAppliedScanDTO> dtoListScanEntry = new ArrayList<>();
        EntryBoxAppliedScanDTO dtxxxxx = new EntryBoxAppliedScanDTO();
        dtxxxxx.setScanFlag("正常结束");
        dtoListScanEntry.add(dtxxxxx);
        PowerMockito.when(entryBoxAppliedScanService.boxScanedEnd(anyObject())).thenReturn(dtoListScanEntry);
        PowerMockito.when(cfgCodeRuleItemService.getSequenceBox()).thenReturn("123333");
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn("12333");
        PowerMockito.when(entryBoxAppliedRepository.getEntrySequence()).thenReturn("12333");
        PowerMockito.when(entryBoxAppliedRepository.getHaveEntryBox(anyObject())).thenReturn(new Long(1));
        List<EntryBoxAppliedBoxDTO> dtoEntryBoxApplied = new ArrayList<>();
        EntryBoxAppliedBoxDTO xdt = new EntryBoxAppliedBoxDTO();
        dtoEntryBoxApplied.add(xdt);
        PowerMockito.when(entryBoxAppliedRepository.getEntryLineSequence()).thenReturn("1233");
        PowerMockito.when(entryBoxAppliedRepository.getBoxInfoToBuild(anyObject())).thenReturn(dtoEntryBoxApplied);
        List<Map<String, String>> dtMapDto = new ArrayList<>();
        map = new HashMap<>();
        map.put(null, null);
        dtMapDto.add(map);
        dtoEntryList = new ArrayList<>();
        EntryBoxAppliedDTO dd = new EntryBoxAppliedDTO();
        dd.setEntityId(new BigDecimal("22"));
        dd.setOrganizationID(new Long(222));
        dd.setSubInvCode("333");
        dtoEntryList.add(dd);
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn(null);
        PowerMockito.when(entryBoxAppliedRepository.getHaveEntryBox(anyObject())).thenReturn(Long.valueOf(0));
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dtMapDto);
        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(dtoEntryList);
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn("333");
        Assert.assertNotNull(dtoEntryList);
    }

    @Test
    public void setEntryLine() {
        List<EntryBoxAppliedBoxDTO> dtoEntryBoxApplied = new ArrayList<>();
        EntryBoxAppliedBoxDTO xxxDV = new EntryBoxAppliedBoxDTO();
        xxxDV.setCubage("789");
        xxxDV.setGrossWeight("789");
        xxxDV.setBoxNumber("CXXXX");
        dtoEntryBoxApplied.add(xxxDV);
        PowerMockito.when(entryBoxAppliedRepository.getBoxInfoToBuild(anyObject())).thenReturn(dtoEntryBoxApplied);
        Assert.assertNotNull(dtoEntryBoxApplied);
    }

    @Test
    public void boxScanedEnd() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> dtx = new ArrayList<>();
        dtx.add("C123333");
        paramDto.setBoxNumberList(dtx);
        List<EntryBoxAppliedScanDTO> xc = new ArrayList<>();
        EntryBoxAppliedScanDTO cvt = new EntryBoxAppliedScanDTO();
        xc.add(cvt);
        PowerMockito.when(entryBoxAppliedScanRepository.boxScanedEnd(anyObject())).thenReturn(xc);
        Assert.assertNotNull(entryBoxAppliedScanServiceImpl.boxScanedEnd(paramDto));
    }

    @Test
    public void haveManyPallet() {
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);

        List<Map<String, String>> dtMapDto = new ArrayList<>();
        Map<String, String> dtMap = new HashMap<>();
        dtMap.put("1233", "456");
        dtMap.put("456", "789");
        dtMapDto.add(dtMap);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dtMapDto);
        Assert.assertNotNull(dtMap);
    }

    @Test
    public void dealPalletNo() {
        try {
            List<String> palletList = new ArrayList<>();
            palletList.add("12333");
            PowerMockito.when(entryBoxAppliedRepository.getPalletNoScaned(anyObject())).thenReturn(palletList);
            PowerMockito.when(entryBoxAppliedRepository.getPalletNoScaned(anyObject())).thenReturn(null);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }
    }

    @Test
    public void insertHeadLine() {
        try {
            Mockito.doNothing().when(entryBoxAppliedRepository).insertEntryHead(Mockito.any());
            Mockito.doNothing().when(entryBoxAppliedRepository).insertEntryLine(Mockito.any());
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }
    }

    @Test
    public void auxiliaryBox() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        paramDto.setOrganizationID(new Long(635));
        MesEntryReadyLineDTO dtoo = new MesEntryReadyLineDTO();
        dtoo.setSunBillNumber("C12334");
        List<MesEntryReadyLineDTO> entryReadyLineMain = new ArrayList<>();
        entryReadyLineMain.add(dtoo);
        try {
            PowerMockito.when(entryBoxAppliedRepository.getEntryLineMain(anyObject())).thenReturn(entryReadyLineMain);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }
    }

    @Test
    public void boxScanOver() {
        List<EntryBoxAppliedScanDTO> dtoListScanEntry = new ArrayList<>();
        EntryBoxAppliedScanDTO dto = new EntryBoxAppliedScanDTO();
        dto.setScanFlag("ccc");
        dto.setBillNumber("C20134567");
        dtoListScanEntry.add(dto);
        PowerMockito.when(entryBoxAppliedScanService.boxScanedEnd(anyObject())).thenReturn(dtoListScanEntry);
        PowerMockito.when(entryBoxAppliedScanService.boxScanedEnd(anyObject())).thenReturn(null);
        Assert.assertNotNull(dtoListScanEntry);
    }

    @Test
    public void getConcurrently() {
        List<MesEntryReadyLineDTO> entryReadyLine = new ArrayList<>();
        MesEntryReadyLineDTO dtkl = new MesEntryReadyLineDTO();
        dtkl.setCubage("0");
        dtkl.setGrossWeight("0");
        dtkl.setBoxAtrribute("正常箱");
        entryReadyLine.add(dtkl);
        PowerMockito.when(entryBoxAppliedRepository.getEntryReadyLine(anyObject())).thenReturn(entryReadyLine);
        Assert.assertNotNull(entryReadyLine);
    }

    @Test
    public void validatePalletNo() throws MesBusinessException {
        List<Map<String, String>> dotListNumber = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("TP1711M023160", "C171009430690");
        dotListNumber.add(map);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dotListNumber);

        try {
            dotListNumber = new ArrayList<>();
            map = new HashMap<>();
            map.put("TP1711M023160", "C171009430690");
            map.put("TP1711M023160", "C171009430691");
            map.put("TP1711M023160", "C171009430692");
            map.put("TP1711M023160", "C171009430693");
            map.put("TP1711M023160", "C171009430694");
            dotListNumber.add(map);
            PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dotListNumber);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }
    }

    @Test
    public void whiteBox() throws Exception {
        List<Map<String, String>> dtMapDto = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put(Constant.PALLET_NO, "34");
        dtMapDto.add(map);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(Mockito.any())).thenReturn(dtMapDto);
        Assert.assertNotNull(dtMapDto);
    }
}
