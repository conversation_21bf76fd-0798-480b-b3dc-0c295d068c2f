package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MesBackBillsServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.MesBackBillsRepository;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;


/**
 * <AUTHOR>
 * @since 2023年8月31日14:48
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class MesBackBillsTest extends PowerBaseTestCase {

    @InjectMocks
    private MesBackBillsServiceImpl mesBackBillsService;

    @Mock
    private MesBackBillsRepository mesBackBillsRepository;


    @Test
    public void validateGetBackLineScan() throws Exception {
        List<String> scanNoEmptyList = new ArrayList<>();
        List<String> scanNoFullList = new ArrayList<>();
        List<String> scanNoList = new ArrayList<>();
        List<String> stockNoList = new ArrayList<>();
        List<String> stockNoFullList = new ArrayList<>();
        scanNoList.add("111");
        scanNoList.add("222");
        stockNoList.add("333");
        stockNoList.add("444");
        for (int i = 0; i < 30; i++) {
            scanNoFullList.add(String.valueOf(i));
            stockNoFullList.add(String.valueOf(i));
        }
        MesBackLineDTO mesBackLineDTO = MesBackLineDTO.builder().scanNoList(scanNoEmptyList).stockNoList(stockNoList).build();
        try {
            this.mesBackBillsService.getBackLineScan(mesBackLineDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_LIST_IS_NULL,e.getMessage());

        }
        mesBackLineDTO = MesBackLineDTO.builder().scanNoList(scanNoFullList).stockNoList(stockNoList).build();
        try {
            this.mesBackBillsService.getBackLineScan(mesBackLineDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SIZE_OUT_BOUND_PARAMS_LIST_OF_CHECK_BINDING,e.getMessage());

        }
        mesBackLineDTO = MesBackLineDTO.builder().scanNoList(scanNoList).stockNoList(stockNoFullList).build();
        try {
            this.mesBackBillsService.getBackLineScan(mesBackLineDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SIZE_OUT_BOUND_PARAMS_LIST_OF_CHECK_BINDING,e.getMessage());

        }
        mesBackLineDTO = MesBackLineDTO.builder().scanNoList(scanNoList).stockNoList(stockNoList).build();
        this.mesBackBillsService.getBackLineScan(mesBackLineDTO);
    }

    @Test
    public void validateGetBackBillScanCount() throws Exception {
        List<String> billNoList = new ArrayList<>();
        List<String> billNoEmptyList = new ArrayList<>();
        List<String> billNoFullList = new ArrayList<>();
        billNoList.add("111");
        billNoList.add("222");
        for (int i = 0; i < 30; i++) {
            billNoFullList.add(String.valueOf(i));
        }
        try {
            this.mesBackBillsService.getBackBillScanCount(billNoEmptyList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_LIST_IS_NULL,e.getMessage());

        }
        try {
            this.mesBackBillsService.getBackBillScanCount(billNoFullList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SIZE_OUT_BOUND_PARAMS_LIST_OF_CHECK_BINDING,e.getMessage());

        }
        this.mesBackBillsService.getBackBillScanCount(billNoList);
    }

    @Test
    public void validateUpdateBackLineScan() throws Exception {
        List<MesBackBillsLineScanDTO> mesBackBillsLineScanEmptyList = new ArrayList<>();
        List<MesBackBillsLineScanDTO> mesBackBillsLineScanDTOList = new ArrayList<>();
        MesBackBillsLineScanDTO mesBackBillsLineScanDTO = MesBackBillsLineScanDTO.builder()
                .detailId("123").scanneder("李雷").scannedTime("2023-8-31 15:11:20").build();
        mesBackBillsLineScanDTOList.add(mesBackBillsLineScanDTO);
        try {
            this.mesBackBillsService.updateBackLineScan(mesBackBillsLineScanEmptyList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_LIST_IS_NULL,e.getMessage());

        }
        this.mesBackBillsService.updateBackLineScan(mesBackBillsLineScanDTOList);
    }

    @Test
    public void validateClearBackLineScan() throws Exception {
        List<String> detailIds = new ArrayList<>();
        List<String> detailIdsEmpty = new ArrayList<>();
        List<String> detailIdsFull = new ArrayList<>();
        detailIds.add("111");
        detailIds.add("222");
        for (int i = 0; i < 60; i++) {
            detailIdsFull.add(String.valueOf(i));
        }
        try {
            this.mesBackBillsService.clearBackLineScan(detailIdsEmpty);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_LIST_IS_NULL,e.getMessage());

        }
        try {
            this.mesBackBillsService.clearBackLineScan(detailIdsFull);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SIZE_OUT_BOUND_PARAMS_LIST_OF_CHECK_BINDING,e.getMessage());

        }
        this.mesBackBillsService.clearBackLineScan(detailIds);
    }

    @Test
    public void validateUpdateBackBill() throws Exception {
        MesBackBillsDTO mesBackBillsDTO = MesBackBillsDTO.builder()
                .billNo("111").status("RETURNED").returnBy("222").returnDate("2023-8-31 15:22:22").build();
        this.mesBackBillsService.updateBackBill(mesBackBillsDTO);
        Assert.assertNotNull(mesBackBillsDTO);
    }
}
