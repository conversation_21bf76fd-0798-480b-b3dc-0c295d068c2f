package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.OtherSysProdWoListServiceImpl;
import com.zte.domain.model.datawb.OtherSysProdWoListRepository;
import com.zte.util.BaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class OtherSysProdWoListServiceImplTest extends BaseTestCase {

    @InjectMocks
    private OtherSysProdWoListServiceImpl otherSysProdWoListServiceImpl;

    @Mock
    private OtherSysProdWoListRepository otherSysProdWoListRepository;

    @Test
    public void add() throws Exception {
        otherSysProdWoListServiceImpl.add(Mockito.anyObject());
        Mockito.verify(otherSysProdWoListRepository, Mockito.times(1))
                .insertOtherSysProdWoList(Mockito.anyObject());
    }

    @Test
    public void statisticsByCondition() throws Exception {
        otherSysProdWoListServiceImpl.statisticsByCondition(Mockito.anyList());
        Mockito.verify(otherSysProdWoListRepository, Mockito.times(1))
                .statisticsByCondition(Mockito.anyList());
    }

}