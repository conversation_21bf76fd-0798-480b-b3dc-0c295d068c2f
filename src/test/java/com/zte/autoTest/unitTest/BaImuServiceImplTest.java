package com.zte.autoTest.unitTest;

import com.zte.application.impl.BaImuServiceImpl;
import com.zte.domain.model.BaImuRepository;
import com.zte.interfaces.dto.BaImuDTO;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
public class BaImuServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BaImuServiceImpl baImuService;

    @Mock
    private BaImuRepository baImuRepository;

    @Test
    @PrepareForTest({})
    public void save() throws Exception {
        List<BoardInstructionCycleDataCreateDTO> tempList = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO dto = new BoardInstructionCycleDataCreateDTO();
        tempList.add(dto);
        List<BaImuDTO> existBaImuDTOList = new ArrayList<>();
        BaImuDTO baImuDTO = new BaImuDTO();
        baImuDTO.setImuId(1);
        existBaImuDTOList.add(baImuDTO);
        PowerMockito.when(baImuRepository.getListByImuIdList(Mockito.any())).thenReturn(existBaImuDTOList);
        List<BaImuDTO> baImuDTOList = new ArrayList<>();
        BaImuDTO baImuDTO2 = new BaImuDTO();
        baImuDTO2.setImuId(2);
        baImuDTOList.add(baImuDTO);
        baImuDTOList.add(baImuDTO2);
        baImuService.save(baImuDTOList);
        Assert.assertNotNull(existBaImuDTOList);
    }
}
