package com.zte.autoTest.unitTest.wmes;

import com.zte.application.datawb.impl.WmesWerpConEntityTraceServiceImpl;
import com.zte.domain.model.datawb.WmesWerpConEntityTrace;
import com.zte.domain.model.datawb.WmesWerpConEntityTraceRepository;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-18 16:54
 */
public class WmesWerpConEntityTraceServiceImplTest extends BaseTestCase {
    @InjectMocks
    private WmesWerpConEntityTraceServiceImpl wmesWerpConEntityTraceService;
    @Mock
    private WmesWerpConEntityTraceRepository wmesWerpConEntityTraceRepository;

    @Before
    public void init() {

    }

    @Test
    public void batchQueryStatusName() {
        List<WmesWerpConEntityTrace> traceList = wmesWerpConEntityTraceService.batchQueryStatusName(null);
        Assert.assertTrue(CollectionUtils.isEmpty(traceList));

        List<String> entityIdList = new LinkedList<>();
        entityIdList.add("123");
        wmesWerpConEntityTraceService.batchQueryStatusName(entityIdList);

        List<WmesWerpConEntityTrace> traceList2 = new LinkedList<>();
        WmesWerpConEntityTrace a1 = new WmesWerpConEntityTrace();
        traceList2.add(a1);
        PowerMockito.when(wmesWerpConEntityTraceRepository.batchQueryStatusName(Mockito.anyList()))
                .thenReturn(traceList2);
        wmesWerpConEntityTraceService.batchQueryStatusName(entityIdList);
    }
}
