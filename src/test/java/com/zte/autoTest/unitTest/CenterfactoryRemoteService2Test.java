package com.zte.autoTest.unitTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.SpecifiedPsTaskDTO;
import com.zte.interfaces.dto.SpecifiedPsTaskReq;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-11-16 16:52
 */
@RunWith(PowerMockRunner.class)
public class CenterfactoryRemoteService2Test {
    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;


    @Test
    @PrepareForTest({HttpRemoteUtil.class})
    public void getCustomerItemsInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);

        List<String> itemCodeList = new ArrayList<>();
        centerfactoryRemoteService.getCustomerItemsInfo("123",itemCodeList);
        itemCodeList.add("2");
        centerfactoryRemoteService.getCustomerItemsInfo("",itemCodeList);
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"total\":1,\"size\":10,\"current\":1,\"searchCount\":true,\"pages\":1,\"records\":[{\"srcBillNo\":null,\"itemNo\":\"046050200180\",\"reelid\":\"ZTE2021032200003\",\"reserveQty\":5,\"enableFlag\":\"Y\",\"warehouseNo\":\"CS1265\",\"stockNo\":\"CS1265-0002\",\"locationNo\":\"CS1265-0002-0001\",\"canUseFlag\":null,\"attribute1\":\"7775003-SMT-A5202\",\"attribute2\":\"\",\"createBy\":\"10275508\",\"createDate\":\"2021-03-23 19:07:07\",\"lastUpdateBy\":\"10275508\",\"lastUpdateDate\":\"2021-03-23 19:07:07\"}],\"hasNextPage\":false},\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.StockLockController@getPageList\",\"code\":\"0000\",\"costTime\":\"479ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Mar 23 20:40:18 CST 2021\",\"tag\":null,\"serviceName\":\"zte-mes-manufactureshare-productiondeliverysys\",\"userId\":\"10266925\"}}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn("");
        try {
            centerfactoryRemoteService.getCustomerItemsInfo("2", itemCodeList);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION, e.getMessage());
        }
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);
        PowerMockito.when(constantInterface.getUrl(Mockito.any()))
                .thenReturn("22");
        centerfactoryRemoteService.getCustomerItemsInfo("2",itemCodeList);

    }
    @Test
    @PrepareForTest({HttpRemoteService.class})
    public void getLookupValue() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID));
        CenterfactoryRemoteService.getLookupValue("1","");
        List<SysLookupValues> lookupValuesDTOList = new ArrayList<>();
        SysLookupValues dto = new SysLookupValues();
        dto.setLookupMeaning("123");
        dto.setLookupType(new BigDecimal("123"));
        lookupValuesDTOList.add(dto);
        serviceData.setBo(lookupValuesDTOList);
        Assert.assertNull(centerfactoryRemoteService.getLookupValue("123",""));
    }
    /* Started by AICoder, pid:7abffg3d691dae114a2009099074880651d568a1 */
    @Test
    @PrepareForTest({HttpRemoteUtil.class,ServiceDataBuilderUtil.class})
    public void test_getSpecifiedPsTaskList_NullRequest() {

        List<SpecifiedPsTaskDTO> result = centerfactoryRemoteService.getSpecifiedPsTaskList(null);
        Assert.assertEquals(0, result.size());
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
        SpecifiedPsTaskReq specifiedPsTaskReq = new SpecifiedPsTaskReq();
        specifiedPsTaskReq.setSn("SN123");
        Assert.assertThrows(MesBusinessException.class, () -> centerfactoryRemoteService.getSpecifiedPsTaskList(specifiedPsTaskReq));

        String responseJson = "{\"data\": {}}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(responseJson);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(responseJson);
        centerfactoryRemoteService.getSpecifiedPsTaskList(specifiedPsTaskReq);
        responseJson = "{\"rows\": [{\"id\": 1, \"taskNo\": \"Task1\"}]}";
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(responseJson);
        List<SpecifiedPsTaskDTO> specifiedPsTaskList = centerfactoryRemoteService.getSpecifiedPsTaskList(specifiedPsTaskReq);
        Assert.assertNotNull(specifiedPsTaskList);
    }
    /* Ended by AICoder, pid:7abffg3d691dae114a2009099074880651d568a1 */

}
