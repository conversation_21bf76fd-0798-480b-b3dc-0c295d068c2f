package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BasBarcodeInforDTServiceImpl;
import com.zte.application.datawb.impl.BasBarcodeInforSsynServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.SpringUtil;
import com.zte.domain.model.datawb.BasBarcodeInforDTRepository;
import com.zte.domain.model.datawb.BasBarcodeInforSsynRepository;
import com.zte.interfaces.dto.BarcodeCommon;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

@PrepareForTest({SpringUtil.class})
public class BasBarcodeInforSsynImplTest extends PowerBaseTestCase {
    @InjectMocks
    private BasBarcodeInforSsynServiceImpl basBarcodeInforSsynServiceImpl;
    @InjectMocks
    private BasBarcodeInforDTServiceImpl basBarcodeInforDTServiceImplDT;

    @Mock
    private BasBarcodeInforDTServiceImpl basBarcodeInforDTServiceImpl;
    @Mock
    private BasBarcodeInforDTRepository basBarcodeInforDTRepository;
    @Mock
    private BasBarcodeInforSsynRepository basBarcodeInforSsynRepository;

    @Test
    public void getHttpList() {
        Map<String, String> headerParamsMap = new HashMap<>();
        headerParamsMap.put("X-Lang-Id", Constant.BARCODE_ZH_CN);
        headerParamsMap.put("X-Auth-Value", "B3D18A23CBB9DD5B56E58F011E8D483EE9D6870553161776692FA5A94B73F439");
        headerParamsMap.put("X-Auth-AccessKey", Constant.IMES_300_DH);
        headerParamsMap.put("X-Emp-No", Constant.EMPLOYEE_10312837);
        headerParamsMap.put("X-Tenant-Id", Constant.EMPLOYEE_10001);
        headerParamsMap.put("Content-Type", "application/json");
        String goodtx = "{\"lastUpdatedBy\":\"\",\"endDate\":\"2022-06-30 23:55:00\",\"sourceSystem\":\"IBARCODE\",\"isNullItemCode\":0,\"itemCode\":\"\",\"overZeroQuantity\":1,\"categoryCode\":[\"ZTESSP_SNID\",\"MR_SOURCE_LINE_SNID\"],\"tableName\":\"serial_number_barcode\",\"beginDate\":\"2022-05-31 23:55:00\",\"createBy\":\"\",\"dateType\":1,\"maxBarcodeLength\":41,\"relatedContainerBarcode\":\"\",\"isNullSourceBatchNo\":0,\"sourceBatchNo\":\"\",\"enableFlag\":\"\"}\n" +
                "\n";
        Timestamp beginDt = new Timestamp(System.currentTimeMillis());
        Timestamp endDt = new Timestamp(System.currentTimeMillis());
        String postUrl = "https://test.ibarcode.zte.com.cn/zte-iss-barcodecenter-barcode/barcode/queryByConditions";
        try {
            basBarcodeInforSsynServiceImpl.getHttpList(postUrl, headerParamsMap, beginDt, endDt);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void insertNoExistBarcode() {
        List<BarcodeCommon> insertBarcode = new ArrayList<>();
        BarcodeCommon dtx = new BarcodeCommon();
        insertBarcode.add(dtx);
        PowerMockito.when(basBarcodeInforSsynRepository.insertNoExistBarcode(any())).thenReturn(1);
        basBarcodeInforSsynServiceImpl.insertNoExistBarcode(insertBarcode);
        Assert.assertNotNull(insertBarcode);
    }

    @Test
    public void updateExistBarcode() {
        List<BarcodeCommon> insertBarcode = new ArrayList<>();
        BarcodeCommon dtx = new BarcodeCommon();
        insertBarcode.add(dtx);
        basBarcodeInforSsynServiceImpl.updateExistBarcode(insertBarcode);
        Assert.assertNotNull(insertBarcode);
    }

    @Test

    public void updateSubBarcodeSsyn() {
        PowerMockito.mockStatic(SpringUtil.class);
        List<BarcodeCommon> barcodeCollection = new ArrayList<>();
        Timestamp beginDate = new Timestamp(System.currentTimeMillis());
        BarcodeCommon item = new BarcodeCommon();
        item.setCreateDate(new Timestamp(System.currentTimeMillis()));
        item.setLastUpdatedDate(new Timestamp(System.currentTimeMillis()));
        item.setCategoryCode("12333");
        item.setBarcode("123333");
        item.setUnit("12333");
        item.setItemCode("789");
        item.setItemName("910");
        item.setItemVersion("good");
        item.setRelatedSnBarcode("gootd");
        item.setSourceBatchNo("890");
        item.setId("635");
        item.setSbomNo("789");
        item.setEnableFlag("dt");
        item.setQuantity(new BigDecimal(1));
        barcodeCollection.add(item);
        PowerMockito.mockStatic(SpringUtil.class);
        BasBarcodeInforSsynServiceImpl dtxxx = new BasBarcodeInforSsynServiceImpl();
        PowerMockito.when(SpringUtil.getBean("12333")).thenReturn(dtxxx);
        try {
            basBarcodeInforSsynServiceImpl.updateSubBarcodeSsyn(barcodeCollection, beginDate);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void updateSfcBarcode() {
        List<BarcodeCommon> barcodeCollection = new ArrayList<>();
        Timestamp beginDate = new Timestamp(System.currentTimeMillis());
        BarcodeCommon item = new BarcodeCommon();
        item.setCreateDate(new Timestamp(System.currentTimeMillis()));
        item.setLastUpdatedDate(new Timestamp(System.currentTimeMillis()));
        item.setCategoryCode("12333");
        item.setBarcode("123333");
        item.setUnit("12333");
        item.setItemCode("789");
        item.setItemName("910");
        item.setItemVersion("good");
        item.setRelatedSnBarcode("gootd");
        item.setSourceBatchNo("890");
        item.setId("635");
        item.setSbomNo("789");
        item.setEnableFlag("dt");
        item.setQuantity(new BigDecimal(1));
        barcodeCollection.add(item);
        List<List<BarcodeCommon>> dtBarcodeList = new ArrayList<>();
        dtBarcodeList.add(barcodeCollection);
        PowerMockito.when(basBarcodeInforDTServiceImpl.selectBarcodeExistMES(any())).thenReturn(barcodeCollection);
        PowerMockito.doNothing().when(basBarcodeInforDTServiceImpl).updateExistBarcodeMES(any());
        PowerMockito.doNothing().when(basBarcodeInforDTServiceImpl).insertNoExistBarcodeMES(any());
        beginDate = new Timestamp(System.currentTimeMillis());
        basBarcodeInforSsynServiceImpl.updateSfcBarcode(barcodeCollection, dtBarcodeList, beginDate);
        Assert.assertNotNull(barcodeCollection);
    }

    @Test
    public void getJsonString() {
        try {
            String goodtx = "{\"lastUpdatedBy\":\"\",\"endDate\":\"2022-06-30 23:55:00\",\"sourceSystem\":\"IBARCODE\",\"isNullItemCode\":0,\"itemCode\":\"\",\"overZeroQuantity\":1,\"categoryCode\":[\"ZTESSP_SNID\",\"MR_SOURCE_LINE_SNID\"],\"tableName\":\"serial_number_barcode\",\"beginDate\":\"2022-05-31 23:55:00\",\"createBy\":\"\",\"dateType\":1,\"maxBarcodeLength\":41,\"relatedContainerBarcode\":\"\",\"isNullSourceBatchNo\":0,\"sourceBatchNo\":\"\",\"enableFlag\":\"\"}\n" +
                    "\n";
            basBarcodeInforSsynServiceImpl.getJsonString(goodtx);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }
    }

    @Test
    public void wholeEnterSyn() {
        try {
            PowerMockito.when(basBarcodeInforDTServiceImpl.selectBarcodeExistMES(any())).thenReturn(any());
            basBarcodeInforSsynServiceImpl.wholeEnterSyn();
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }
    }

    @Test
    public void selectBarcodeExistMES() {
        List<BarcodeCommon> barcodeCollection = new ArrayList<>();
        PowerMockito.when(basBarcodeInforDTRepository.selectBarcodeExistMES(any())).thenReturn(barcodeCollection);
        Assert.assertNotNull(basBarcodeInforDTServiceImplDT.selectBarcodeExistMES(barcodeCollection));
    }

    @Test
    public void updateExistBarcodeMES() {
        List<BarcodeCommon> dtBarcodeExist = new ArrayList<>();
        BarcodeCommon dtCommon = new BarcodeCommon();
        dtBarcodeExist.add(dtCommon);
        PowerMockito.when(basBarcodeInforDTRepository.updateBarcodeExistMES(any())).thenReturn(1);
        basBarcodeInforDTServiceImplDT.updateExistBarcodeMES(dtBarcodeExist);
        Assert.assertNotNull(dtBarcodeExist);
    }

    @Test
    public void insertNoExistBarcodeMES() {
        List<BarcodeCommon> dtBarcodeExist = new ArrayList<>();
        BarcodeCommon dtCommon = new BarcodeCommon();
        dtBarcodeExist.add(dtCommon);
        PowerMockito.when(basBarcodeInforDTRepository.insertNoExistBarcodeMES(any())).thenReturn(1);
        basBarcodeInforDTServiceImplDT.insertNoExistBarcodeMES(dtBarcodeExist);
        Assert.assertNotNull(dtBarcodeExist);
    }
}
