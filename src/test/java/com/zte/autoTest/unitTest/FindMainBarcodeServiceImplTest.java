package com.zte.autoTest.unitTest;
import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.BaBomHeadServiceImpl;
import com.zte.application.datawb.impl.FindMainBarcodeServiceImpl;
import com.zte.application.stepdt.impl.AvlServiceImpl;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.BaBomHeadRepository;
import com.zte.domain.model.datawb.FindMainBarcodeRepository;
import com.zte.domain.model.stepdt.AvlRepository;
import com.zte.interfaces.dto.SubBarcodeInDTO;
import com.zte.interfaces.stepdt.dto.AvlDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.doReturn;
public class FindMainBarcodeServiceImplTest extends PowerBaseTestCase{
    @InjectMocks
    private FindMainBarcodeServiceImpl findMainBarcodeServiceImpl;
    @Mock
    private FindMainBarcodeRepository findMainBarcodeRepository;

    @Test
    public void findMainBarcode() throws Exception {
        //BaBomHead BaBomHead=new BaBomHead();
        //List<String> itemIdList=new ArrayList<>();
        //itemIdList.add("1");
        //BaBomHead.setItemIdList(itemIdList);
        //baBomHeadServiceImpl.getParamByItemId(BaBomHead);
        SubBarcodeInDTO params1=new SubBarcodeInDTO();
        params1.setItemBarcode("123,123");
        params1.setQueryType("C");
        findMainBarcodeServiceImpl.findMainBarcode(params1);

        SubBarcodeInDTO params2=new SubBarcodeInDTO();
        params2.setItemBarcode("");
        params2.setQueryType("C");
        findMainBarcodeServiceImpl.findMainBarcode(params2);

        SubBarcodeInDTO params3=new SubBarcodeInDTO();
        params3.setItemBarcode("12,12");
        params3.setQueryType("CC");
        findMainBarcodeServiceImpl.findMainBarcode(params3);

        SubBarcodeInDTO params4=new SubBarcodeInDTO();
        params4.setItemBarcode("12,12");
        params4.setQueryType("");
        findMainBarcodeServiceImpl.findMainBarcode(params4);

        SubBarcodeInDTO params5=new SubBarcodeInDTO();
        params5.setItemBarcode(",12,");
        params5.setQueryType("C");
        findMainBarcodeServiceImpl.findMainBarcode(params5);

        SubBarcodeInDTO params6=new SubBarcodeInDTO();
        params6.setItemBarcode(",,");
        params6.setQueryType("C");
        findMainBarcodeServiceImpl.findMainBarcode(params6);

        SubBarcodeInDTO params7=new SubBarcodeInDTO();
        params7.setItemBarcode(",");
        params7.setQueryType("C");
        findMainBarcodeServiceImpl.findMainBarcode(params7);

        SubBarcodeInDTO params8=new SubBarcodeInDTO();
        params8.setItemBarcode("，");
        params8.setQueryType("C");
        Assert.assertNotNull(findMainBarcodeServiceImpl.findMainBarcode(params8));
    }



}
