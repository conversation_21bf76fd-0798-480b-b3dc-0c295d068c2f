package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MeiTuanServiceImpl;
import com.zte.application.datawb.impl.ZmsOverallUnitServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.BarcodePriceRepository;
import com.zte.domain.model.datawb.MeiTuanRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.*;

import static org.mockito.Mockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class})
public class MeiTuanServiceImplTest extends PowerBaseTestCase {
    /* Started by AICoder, pid:we3ce0ec8226b2514f070860b04a07639c07d541 */
    @InjectMocks
    MeiTuanServiceImpl meiTuanServiceImpl;

    @Mock
    BarcodePriceRepository barcodePriceRepository;

    @Mock
    MeiTuanRepository meiTuanRepository;

    @Mock
    ZmsOverallUnitServiceImpl zmsOverallUnitService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;

    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;

    @Before
    public void init() {
        //PowerMockito.mockStatic(ServiceData.class,RetCode.class);
    }

    @Test
    public void Test() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        when(localeMessageSourceBean.getMessage(Mockito.any())).thenReturn("OK");
        try {
            OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
            dto.setUserAddress("北京三快");
            dto.setEntityNameLike("-M");
            doNothing().when(zmsOverallUnitService).getLookupMeanList(dto);
            List<String> oidList = new ArrayList<>();
            when(meiTuanRepository.getProductOID(Mockito.any())).thenReturn(oidList);
            when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(null);
            List<MTProductDataUploadLogDTO> list = null;
            when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("10277404", null);
            ServiceData ret = new ServiceData();
            Assert.assertNotEquals(ret, ret1);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        try {
            OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
            dto.setUserAddress("北京三快");
            dto.setEntityNameLike("-M");
            doNothing().when(zmsOverallUnitService).getLookupMeanList(dto);

            List<String> oidList = new ArrayList<>();
            oidList.add("OID20240329");
            when(meiTuanRepository.getProductOID(Mockito.any())).thenReturn(oidList);
            List<AutoSynJobsDTO> result = null;
            when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);
            when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(null);
            List<MTProductDataUploadLogDTO> list = null;
            MTProductDataUploadRequestDTO gg = new MTProductDataUploadRequestDTO();
            when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret = new ServiceData();
            Assert.assertNotEquals(ret, ret1);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        try {
            List<AutoSynJobsDTO> result = new ArrayList<>();
            AutoSynJobsDTO model = new AutoSynJobsDTO();
            model.setLastDate(null);
            result.add(model);
            List<EntityWeightDTO> ddvv = new ArrayList<>();
            EntityWeightDTO vv = new EntityWeightDTO();
            vv.setDescription("34");
            ddvv.add(vv);
            when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);
            when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(ddvv);
            List<MTProductDataUploadLogDTO> list = new ArrayList<>();
            MTProductDataUploadLogDTO model1 = new MTProductDataUploadLogDTO();
            model1.setItemCode("33");
            model1.setLastUpdateDate(new Date());
            list.add(model1);
            MTProductDataUploadRequestDTO gg = new MTProductDataUploadRequestDTO();
            List<String> dd = new ArrayList<>();
            for (int i = 0; i < 30; i++) {
                dd.add("33");
            }
            gg.setOid(dd);
            when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
            ServiceData ret2 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret = new ServiceData();
            Assert.assertNotEquals(ret, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        try {
            List<AutoSynJobsDTO> result = new ArrayList<>();
            AutoSynJobsDTO model = new AutoSynJobsDTO();
            model.setLastDate(null);
            result.add(model);
            when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);
            List<MTProductDataUploadLogDTO> list = new ArrayList<>();
            MTProductDataUploadLogDTO model1 = new MTProductDataUploadLogDTO();
            model1.setItemCode("OID20240329");
            model1.setLastUpdateDate(new Date());
            list.add(model1);
            List<EntityWeightDTO> ddvv = new ArrayList<>();
            EntityWeightDTO vv = new EntityWeightDTO();
            vv.setDescription("34");
            ddvv.add(vv);
            when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
            when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(ddvv);
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", null);
            ServiceData ret = new ServiceData();
            Assert.assertNotEquals(ret, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        List<AutoSynJobsDTO> result = new ArrayList<>();
        AutoSynJobsDTO model = new AutoSynJobsDTO();
        model.setLastDate(null);
        result.add(model);
        when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);

        OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
        dto.setUserAddress("北京三快");
        dto.setEntityNameLike("-M");
        doNothing().when(zmsOverallUnitService).getLookupMeanList(dto);

        List<String> oidList = new ArrayList<>();
        oidList.add("OID2525670");
        oidList.add("OID2525671");
        when(meiTuanRepository.getProductOID(Mockito.any())).thenReturn(oidList);

        List<MTProductDataUploadLogDTO> list = new ArrayList<>();
        MTProductDataUploadLogDTO model1 = new MTProductDataUploadLogDTO();
        model1.setItemCode("OID2525670");
        model1.setItemCode("333");
        model1.setProduceCount(0);
        model1.setLastUpdateDate(new Date());
        list.add(model1);
        MTProductDataUploadLogDTO model2 = new MTProductDataUploadLogDTO();
        model2.setItemCode("OID2525671");
        model2.setProduceWoCode("111");
        model2.setProduceCount(10);
        model2.setLastUpdateDate(new Date());
        list.add(model2);
        List<MTProductDataUploadLogDTO> his = new ArrayList<>();
        MTProductDataUploadLogDTO model3 = new MTProductDataUploadLogDTO();
        model3.setItemCode("OID2525671");
        model3.setProduceWoCode("111");
        model3.setProduceCount(10);
        his.add(model3);
        MTProductDataUploadLogDTO model4 = new MTProductDataUploadLogDTO();
        model4.setItemCode("OID2525671");
        model4.setProduceWoCode("222");
        model4.setProduceCount(0);
        his.add(model4);
        MTProductDataUploadLogDTO model5 = new MTProductDataUploadLogDTO();
        model5.setItemCode("OID2525670");
        model5.setProduceWoCode("111");
        model5.setProduceCount(10);
        his.add(model5);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);

        List<EntityWeightDTO> ddvv = new ArrayList<>();
        EntityWeightDTO vv = new EntityWeightDTO();
        vv.setDescription("34");
        ddvv.add(vv);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(ddvv);

        MTProductDataUploadRequestDTO gg = new MTProductDataUploadRequestDTO();
        gg.setOid(oidList);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        list.clear();
        model3.setProduceCount(10);
        model3.setLastUpdateDate(new Date());
        list.add(model3);
        model4.setProduceCount(10);
        model4.setLastUpdateDate(new Date());
        list.add(model4);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        gg.setOid(oidList);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        list.clear();
        model3.setProduceCount(0);
        list.add(model3);
        model4.setProduceCount(0);
        list.add(model4);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        gg.setOid(oidList);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        list.clear();
        model3.setItemCode("ssssss");
        list.add(model3);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        gg.setOid(oidList);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(null);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", null);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        his.clear();
        MTProductDataUploadLogDTO model6 = new MTProductDataUploadLogDTO();
        model6.setItemCode("OIDXXX");
        his.add(model6);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", null);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        his.clear();
        MTProductDataUploadLogDTO model7 = new MTProductDataUploadLogDTO();
        model7.setItemCode("OID2525670");
        model7.setProduceWoCode("333");
        his.add(model7);
        MTProductDataUploadLogDTO model8 = new MTProductDataUploadLogDTO();
        model8.setItemCode("OID2525671");
        model8.setProduceWoCode("111");
        his.add(model8);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        his.clear();
        model7 = new MTProductDataUploadLogDTO();
        model7.setItemCode("OID2525670");
        model7.setProduceWoCode("333");
        his.add(model7);
        model8 = new MTProductDataUploadLogDTO();
        model8.setItemCode("OID2525671");
        model8.setProduceWoCode("111");
        his.add(model8);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        //data为null 全部失效场景
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(new ArrayList<>());
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        his.clear();
        model7 = new MTProductDataUploadLogDTO();
        model7.setItemCode("OID25256701");
        model7.setProduceWoCode("333");
        his.add(model7);
        model8 = new MTProductDataUploadLogDTO();
        model8.setItemCode("OID25256712");
        model8.setProduceWoCode("111");
        his.add(model8);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        list.clear();
        MTProductDataUploadLogDTO model9 = new MTProductDataUploadLogDTO();
        model9.setItemCode("OIDXXX");
        list.add(model9);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", null);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        list.clear();
        MTProductDataUploadLogDTO model10 = new MTProductDataUploadLogDTO();
        model10.setItemCode("OID2525670");
        list.add(model10);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(null);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        his.clear();
        model7 = new MTProductDataUploadLogDTO();
        model7.setItemCode("OID25256744");
        model7.setProduceWoCode("333");
        his.add(model7);
        model8 = new MTProductDataUploadLogDTO();
        model8.setItemCode("OID25256755");
        model8.setProduceWoCode("111");
        his.add(model8);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", gg);
            ServiceData ret4 = new ServiceData();
            Assert.assertNotEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }
    @Test
    public void TestMoreThan20(){
        try {
            List<AutoSynJobsDTO> result = new ArrayList<>();
            AutoSynJobsDTO model = new AutoSynJobsDTO();
            model.setLastDate(new Date());
            result.add(model);
            when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);

            OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
            doNothing().when(zmsOverallUnitService).getLookupMeanList(dto);

            List<String> oidList = new ArrayList<>();
            for (int i=0;i<25;i++){
                oidList.add("OID"+i);
            }
            MTProductDataUploadRequestDTO req = new MTProductDataUploadRequestDTO();
            req.setOid(oidList);
            ServiceData ret3 = meiTuanServiceImpl.handleProductDataUpload("10277404", req);
            ServiceData ret4 = new ServiceData();
            Assert.assertEquals(ret4, ret3);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }

    @Test
    public void TestBranch(){
        List<AutoSynJobsDTO> result = new ArrayList<>();
        AutoSynJobsDTO model = new AutoSynJobsDTO();
        model.setLastDate(new Date());
        result.add(model);
        when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);

        OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
        doNothing().when(zmsOverallUnitService).getLookupMeanList(dto);

        List<String> oidList = new ArrayList<>();
        for (int i=0;i<5;i++){
            oidList.add("OID"+i);
        }
        MTProductDataUploadRequestDTO req = new MTProductDataUploadRequestDTO();
        req.setOid(oidList);
        when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(null);
        List<MTProductDataUploadLogDTO> list = new ArrayList<>();
        MTProductDataUploadLogDTO m1 = new MTProductDataUploadLogDTO();
        m1.setItemCode("OID6");
        m1.setProduceWoCode("666");
        m1.setLastUpdateDate(new Date());
        list.add(m1);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(null);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        MTProductDataUploadLogDTO m3 = new MTProductDataUploadLogDTO();
        m3.setItemCode("OID2");
        m3.setProduceWoCode("666");
        m3.setProduceCount(1);
        m3.setLastUpdateDate(new Date());
        list.clear();
        list.add(m3);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        MTProductDataUploadLogDTO m2 = new MTProductDataUploadLogDTO();
        m2.setItemCode("OID1");
        m2.setProduceWoCode("111");
        m2.setLastUpdateDate(new Date());
        list.add(m2);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(list);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        MTProductDataUploadLogDTO m4 = new MTProductDataUploadLogDTO();
        m4.setItemCode("OID2222");
        m4.setProduceWoCode("666");
        m4.setLastUpdateDate(new Date());
        list.add(m4);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(list);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(null);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(list);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        List<MTProductDataUploadLogDTO> his = new ArrayList<>();
        MTProductDataUploadLogDTO his1 = new MTProductDataUploadLogDTO();
        his1.setItemCode("OID0");
        his1.setProduceWoCode("OID0");
        his1.setProduceCount(1);
        his1.setLastUpdateDate(new Date());
        his.add(his1);
        MTProductDataUploadLogDTO his2 = new MTProductDataUploadLogDTO();
        his2.setItemCode("OID1");
        his2.setProduceWoCode("OID1");
        his2.setProduceCount(0);
        his2.setLastUpdateDate(new Date());
        his.add(his2);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }

    @Test
    public void testDataNull(){
        List<AutoSynJobsDTO> result = new ArrayList<>();
        AutoSynJobsDTO model = new AutoSynJobsDTO();
        model.setLastDate(new Date());
        result.add(model);
        when(barcodePriceRepository.getJobsInfo(Constant.MEITUAN_PRODUCTDATAUPLOAD_JOB_NAME)).thenReturn(result);

        OverallUnitMeiTuanInDTO dto = new OverallUnitMeiTuanInDTO();
        doNothing().when(zmsOverallUnitService).getLookupMeanList(dto);

        List<String> oidList = new ArrayList<>();
        for (int i=0;i<5;i++){
            oidList.add("OID"+i);
        }
        MTProductDataUploadRequestDTO req = new MTProductDataUploadRequestDTO();
        req.setOid(oidList);
        when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(null);
        List<MTProductDataUploadLogDTO> list = new ArrayList<>();
        List<MTProductDataUploadLogDTO> his = new ArrayList<>();
        MTProductDataUploadLogDTO m1 = new MTProductDataUploadLogDTO();
        m1.setItemCode("OID0");
        m1.setProduceWoCode("111");
        m1.setLastUpdateDate(new Date());
        list.add(m1);
        MTProductDataUploadLogDTO m2 = new MTProductDataUploadLogDTO();
        m2.setItemCode("OID0");
        m2.setProduceWoCode("222");
        m2.setProduceCount(1);
        m2.setLastUpdateDate(new Date());
        his.add(m2);
        when(meiTuanRepository.getProductDataUpload(Mockito.any())).thenReturn(list);
        List<MtProductDataStatusDTO> ds = new ArrayList<>();
        MtProductDataStatusDTO ds1 = new MtProductDataStatusDTO();
        ds.add(ds1);
        when(meiTuanRepository.getProductDataStatus(Mockito.any())).thenReturn(ds);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        try {
            ServiceData ret1 = meiTuanServiceImpl.handleProductDataUpload("11", req);
            ServiceData ret2 = new ServiceData();
            Assert.assertEquals(ret1, ret2);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }
    @Test
    public void testPushData(){
        List<MTProductDataUploadLogDTO> his = new ArrayList<>();
        List<MTProductDataUploadLogDTO> edit = new ArrayList<>();
        List<CustomerDataLogDTO> data = new ArrayList<>();
        List<MtProductDataStatusDTO> dataStatus =new ArrayList<>();
        MTProductDataUploadLogDTO dto1=new MTProductDataUploadLogDTO();
        dto1.setProduceCount(1);
        dto1.setProduceWoCode("1");
        dto1.setItemCode("1");
        MtProductDataStatusDTO dto2=new MtProductDataStatusDTO();
        dto2.setIsAdd(0);
        his.add(dto1);
        edit.add(dto1);
        dataStatus.add(dto2);
        CustomerDataLogDTO cd1 = new CustomerDataLogDTO();
        cd1.setTaskNo("1");
        data.add(cd1);
        try {
            meiTuanServiceImpl.pushData(his,edit,data,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        his.clear();
        MTProductDataUploadLogDTO dto3=new MTProductDataUploadLogDTO();
        dto3.setProduceCount(1);
        dto3.setProduceWoCode("2");
        dto3.setItemCode("2");
        his.add(dto3);
        try {
            meiTuanServiceImpl.pushData(his,edit,data,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }

    @Test
    public void testProductStatus(){
        List<MTProductDataUploadLogDTO> data = new ArrayList<>();
        MTProductDataUploadLogDTO dt1 = new MTProductDataUploadLogDTO();
        dt1.setItemCode("OID1");
        dt1.setItemTotal(10);
        dt1.setProduceCount(1);
        dt1.setProduceWoCode("pwc1");
        dt1.setWarehousingComplateTime("2025-03-10");
        MTProductDataUploadLogDTO dt2 = new MTProductDataUploadLogDTO();
        dt2.setItemCode("OID1");
        dt2.setProduceCount(1);
        dt2.setProduceWoCode("pwc2");
        dt2.setItemTotal(2);
        MTProductDataUploadLogDTO dt3 = new MTProductDataUploadLogDTO();
        dt3.setItemCode("OID2");
        dt3.setProduceCount(1);
        dt3.setProduceWoCode("pwc2");
        dt3.setItemTotal(2);
        MTProductDataUploadLogDTO dt4 = new MTProductDataUploadLogDTO();
        dt4.setItemCode("OID3");
        dt4.setProduceCount(1);
        dt4.setProduceWoCode("pwc3");
        dt4.setItemTotal(5);
        MTProductDataUploadLogDTO dt5 = new MTProductDataUploadLogDTO();
        dt5.setItemCode("OID3");
        dt5.setProduceCount(1);
        dt5.setProduceWoCode("pwc4");
        dt5.setItemTotal(1);
        MTProductDataUploadLogDTO dt6 = new MTProductDataUploadLogDTO();
        dt6.setItemCode("OID3");
        dt6.setProduceCount(1);
        dt6.setProduceWoCode("pwc5");
        dt6.setItemTotal(3);
        MTProductDataUploadLogDTO dt7 = new MTProductDataUploadLogDTO();
        dt7.setItemCode("OID3");
        dt7.setProduceCount(1);
        dt7.setProduceWoCode("pwc7");
        dt7.setItemTotal(8);
        MTProductDataUploadLogDTO dt8 = new MTProductDataUploadLogDTO();
        dt8.setItemCode("OID4");
        dt8.setProduceCount(1);
        dt8.setItemTotal(6);
        dt8.setProduceWoCode("OID4pwc7");
        MTProductDataUploadLogDTO dt9 = new MTProductDataUploadLogDTO();
        dt9.setItemCode("OID5");
        dt9.setProduceCount(1);
        dt9.setProduceWoCode("OID5pwc7");
        dt9.setItemTotal(6);
        dt9.setWarehousingComplateTime("2025-03-10");
        MTProductDataUploadLogDTO dt10 = new MTProductDataUploadLogDTO();
        dt10.setItemCode("OID6");
        dt10.setProduceCount(1);
        dt10.setProduceWoCode("OID6pwc7");
        dt10.setItemTotal(6);
        dt10.setWarehousingComplateTime("2025-03-10");
        MTProductDataUploadLogDTO dt11 = new MTProductDataUploadLogDTO();
        dt11.setItemCode("OID7");
        dt11.setProduceCount(1);
        dt11.setProduceWoCode("OID7pwc1");
        dt11.setItemTotal(6);
        dt11.setWarehousingComplateTime("2025-03-10");
        MTProductDataUploadLogDTO dt12 = new MTProductDataUploadLogDTO();
        dt12.setItemCode("OID7");
        dt12.setProduceCount(1);
        dt12.setItemTotal(6);
        dt12.setProduceWoCode("OID7pwc2");
        dt12.setWarehousingComplateTime("2025-03-10");
        MTProductDataUploadLogDTO dt13 = new MTProductDataUploadLogDTO();
        dt13.setItemCode("OID8");
        dt13.setProduceCount(1);
        dt13.setProduceWoCode("OID8pwc2");
        dt13.setWarehousingComplateTime("2025-03-10");
        dt13.setItemTotal(6);
        MTProductDataUploadLogDTO dt14 = new MTProductDataUploadLogDTO();
        dt14.setItemCode("OID9");
        dt14.setProduceCount(1);
        dt14.setProduceWoCode("OID9pwc2");
        dt14.setItemTotal(6);
        MTProductDataUploadLogDTO dt15 = new MTProductDataUploadLogDTO();
        dt15.setItemCode("OID10");
        dt15.setProduceCount(1);
        dt15.setItemTotal(6);
        dt15.setProduceWoCode("OID10pwc2");
        data.add(dt1);
        data.add(dt2);
        data.add(dt3);
        data.add(dt4);
        data.add(dt5);
        data.add(dt6);
        data.add(dt7);
        data.add(dt8);
        data.add(dt9);
        data.add(dt10);
        data.add(dt11);
        data.add(dt12);
        data.add(dt13);
        data.add(dt14);
        data.add(dt15);
        List<String> oidList = new ArrayList<>();
        oidList.add("OID1");//null
        oidList.add("OID2");//失效
        oidList.add("OID3");//退库
        oidList.add("OID4");//
        oidList.add("OID5");
        oidList.add("OID6");
        oidList.add("OID7");
        oidList.add("OID8");
        oidList.add("OID9");
        oidList.add("OID10");
        List<MtProductDataStatusDTO> dataStatus = new ArrayList<>();
        MtProductDataStatusDTO dss1 = new MtProductDataStatusDTO();
        dss1.setMemo("OID2");
        dataStatus.add(dss1);
        MtProductDataStatusDTO dss2 = new MtProductDataStatusDTO();
        dss2.setMemo("OID3");
        dataStatus.add(dss2);
        MtProductDataStatusDTO dss3 = new MtProductDataStatusDTO();
        dss3.setMemo("OID4");
        dss3.setStatus(Constant.PROCESS_SUCESS_STRING);
        dataStatus.add(dss3);
        MtProductDataStatusDTO dss4 = new MtProductDataStatusDTO();
        dss4.setMemo("OID5");
        dss4.setStatus(Constant.PROCESS_STATUS_STRING);
        dataStatus.add(dss4);
        MtProductDataStatusDTO dss5 = new MtProductDataStatusDTO();
        dss5.setMemo("OID6");
        dss5.setStatus(Constant.CONST_STOCKRETURN);
        dataStatus.add(dss5);
        MtProductDataStatusDTO dss6 = new MtProductDataStatusDTO();
        dss6.setMemo("OID8");
        dss6.setStatus(Constant.CONST_STOCKRETURN);
        dataStatus.add(dss6);
        MtProductDataStatusDTO dss7 = new MtProductDataStatusDTO();
        dss7.setMemo("OID9");
        dss7.setStatus(Constant.PROCESS_STATUS_STRING);
        dataStatus.add(dss7);
        MtProductDataStatusDTO dss8 = new MtProductDataStatusDTO();
        dss8.setMemo("OID10");
        dss8.setStatus(Constant.CONST_STOCKRETURN);
        dataStatus.add(dss8);
        List<MTProductDataUploadLogDTO> his = new ArrayList<>();
        MTProductDataUploadLogDTO his1= new MTProductDataUploadLogDTO();
        his1.setItemCode("OID2");
        his1.setProduceWoCode("pwc1");
        his1.setProduceCount(1);
        his1.setItemTotal(10);
        MTProductDataUploadLogDTO his2= new MTProductDataUploadLogDTO();
        his2.setItemCode("OID2");
        his2.setProduceWoCode("pwc2");
        his2.setProduceCount(1);
        his2.setItemTotal(1);
        his.add(his1);
        his.add(his2);

        Calendar calendar = Calendar.getInstance();
        Date now = calendar.getTime(); // 获取当前日期和时间
        // 减去一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date oneDayAgo = calendar.getTime();

        List<MTEntitiesStatusDTO> esData = new ArrayList<>();
        MTEntitiesStatusDTO es1 =new MTEntitiesStatusDTO();
        es1.setItemCode("OID3");
        es1.setProduceWoCode("pwc3");
        esData.add(es1);
        MTEntitiesStatusDTO es2 =new MTEntitiesStatusDTO();
        es2.setItemCode("OID3");
        es2.setProduceWoCode("pwc6");
        esData.add(es2);
        MTEntitiesStatusDTO es3 =new MTEntitiesStatusDTO();
        es3.setItemCode("OID3");
        es3.setProduceWoCode("pwc4");
        es3.setRkDate(now);
        es3.setTkDate(oneDayAgo);
        esData.add(es3);
        MTEntitiesStatusDTO es4 =new MTEntitiesStatusDTO();
        es4.setItemCode("OID3");
        es4.setProduceWoCode("pwc7");
        es4.setRkDate(oneDayAgo);
        es4.setTkDate(now);
        esData.add(es4);
        when(meiTuanRepository.getHistoryProductDataUpload(Mockito.any())).thenReturn(his);
        when(meiTuanRepository.getEntitiesStatus(Mockito.any())).thenReturn(esData);
        try {
            meiTuanServiceImpl.pushToImesB2B(data,"oid",oidList,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        MtProductDataStatusDTO dss9 = new MtProductDataStatusDTO();
        dss9.setMemo("OID1");
        dss9.setStatus(Constant.CONST_STOCKRETURN);
        dataStatus.add(dss9);
        try {
            meiTuanServiceImpl.pushToImesB2B(data,"oid",oidList,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        MtProductDataStatusDTO dss10 = new MtProductDataStatusDTO();
        dss10.setMemo("OID1");
        dss10.setStatus(Constant.PROCESS_SUCESS_STRING);
        dataStatus.add(dss10);
        try {
            meiTuanServiceImpl.pushToImesB2B(data,"oid",oidList,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        //非 单号_总数 场景
        dt1.setItemTotal(-1);
        try {
            meiTuanServiceImpl.pushToImesB2B(data,"oid",oidList,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        //小于场景
        dt1.setItemTotal(2);
        try {
            meiTuanServiceImpl.pushToImesB2B(data,"oid",oidList,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }

        // 总数大于Data.size
        dt1.setItemTotal(70);
        try {
            meiTuanServiceImpl.pushToImesB2B(data,"oid",oidList,dataStatus);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
    }
    /* Ended by AICoder, pid:we3ce0ec8226b2514f070860b04a07639c07d541 */
}
