package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.erpdt.impl.WipEntitiesServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.erpdt.WipEntitiesRepository;
import com.zte.interfaces.dto.PDMProductMaterialResultDTO;
import com.zte.interfaces.dto.WipEntitiesDTO;
import com.zte.interfaces.dto.WipEntitiesReportGetDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;


/**
 *
 *
 * @Author:10260525
 * @Date: 2022/5/11 9:42
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class WipEntitiesServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private WipEntitiesServiceImpl service;

	@Mock
	private WipEntitiesRepository wipEntitiesRepository;

	@Before
	public void init() {
		PowerMockito.mockStatic(CommonUtils.class);
		MockitoAnnotations.initMocks(this);
	}

	@Test
	public void getTaskNoStatusByErp()throws Exception{
		List<WipEntitiesDTO> list=new ArrayList<>();
		WipEntitiesDTO dto=new WipEntitiesDTO();
		dto.setTaskNo("123");
		list.add(dto);
		Assert.assertNotNull(service.getTaskNoStatusByErp(list));
	}

	@Test
	public void getSmReportCount() {
		PowerMockito.mockStatic(CommonUtils.class);
		WipEntitiesReportGetDTO dto = new WipEntitiesReportGetDTO();
		dto.setStatus(0);
		dto.setStart(1);
		dto.setEnd(1);
		dto.setTasks(Lists.newArrayList("1"));
		Assert.assertNotNull(service.getSmReportCount(dto));
	}

	@Test
	public void getSmReport() {
		PowerMockito.mockStatic(CommonUtils.class);
		WipEntitiesReportGetDTO dto = new WipEntitiesReportGetDTO();
		dto.setStatus(0);
		dto.setStart(1);
		dto.setEnd(1);
		dto.setTasks(Lists.newArrayList("1"));
		Assert.assertNotNull(service.getSmReport(dto));
	}

	@Test
	public void getErpStatus() {
		PowerMockito.mockStatic(CommonUtils.class);
		Assert.assertNotNull(service.getErpStatus());
	}

	@Test
	public void getBomVerByTaskNo()throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		PDMProductMaterialResultDTO pdmProductMaterialResultDTO=new PDMProductMaterialResultDTO();
		List<String> list=new ArrayList<>();
		list.add("111");
		pdmProductMaterialResultDTO.setEntityNameList(list);
		List<List<String>> splitList=new ArrayList<>();
		splitList.add(list);
		Assert.assertNotNull(service.getBomVerByTaskNo(pdmProductMaterialResultDTO));
	}

}
