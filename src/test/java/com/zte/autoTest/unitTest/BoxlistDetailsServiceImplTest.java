package com.zte.autoTest.unitTest;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import com.zte.application.datawb.impl.BoxlistDetailsServiceImpl;
import com.zte.domain.model.BoxlistDetailsRepository;
import com.zte.interfaces.dto.BoxListDetailInputCondDTO;
import com.zte.interfaces.dto.BoxListDetailInputParamDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;

@PrepareForTest({SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class})
public class BoxlistDetailsServiceImplTest extends PowerBaseTestCase{
	    @InjectMocks
	    private BoxlistDetailsServiceImpl service;

	    @Mock
	    private BoxlistDetailsRepository repository;

	    @Before
	    public void init() {

	        PowerMockito.mockStatic(SpringContextUtil.class);
	        PowerMockito.mockStatic(MicroServiceRestUtil.class);
	        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
	    }

	    @Test
	    public void batchSelectAfterSalesPacking() throws Exception{
	    	BoxListDetailInputParamDTO dto=new BoxListDetailInputParamDTO();
	    	dto.setReciver("60");
	    	List<BoxListDetailInputCondDTO> list = new ArrayList<BoxListDetailInputCondDTO>();
	    	BoxListDetailInputCondDTO model=new BoxListDetailInputCondDTO();
	    	model.setOrglogsys("635");
	    	model.setzCartonNo("C190809829855");
	    	list.add(model);
	    	
	    	model=new BoxListDetailInputCondDTO();
	    	model.setOrglogsys("635");
	    	model.setzCartonNo("C190809829856");
	    	list.add(model);
	    	dto.setBoxListDetailInput(list);
	    	service.batchSelectBoxlistDetailsByCond(dto);
	    	Mockito.verify(repository,Mockito.times(1)).batchSelectBoxlistDetailsByCond(Mockito.anyObject());
	    }
}
