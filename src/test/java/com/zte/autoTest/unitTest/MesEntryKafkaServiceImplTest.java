package com.zte.autoTest.unitTest;

import com.zte.application.datawb.MesEntryKafkaSfcService;
import com.zte.application.datawb.impl.MesEntryKafkaServiceImpl;
import com.zte.application.datawb.kafka.producer.SysLookupValuesProducer;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.MesEntryKafkaRepository;
import com.zte.interfaces.dto.EntryKafkaDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2021年7月14日17:47:50
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class MesEntryKafkaServiceImplTest extends BaseTestCase {

    @InjectMocks
    MesEntryKafkaServiceImpl mesEntryKafkaService;

    @Mock
    MesEntryKafkaSfcService mesEntryKafkaSfcService;

    @Mock
    MesEntryKafkaRepository mesEntryKafkaRepository;

    @Mock
    SysLookupValuesProducer sysLookupValuesProducer;

    @Test
    public void sendKafkaByEntryBills() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

        Assert.assertNotNull(mesEntryKafkaService.sendKafkaByEntryBills(Constant.FOUR_NUMBER_NAME));

        List<EntryKafkaDTO> entryKafkaDTOList = new ArrayList<>();
        EntryKafkaDTO entryKafkaDTO = new EntryKafkaDTO();
        entryKafkaDTO.setEntryNumber("1");
        entryKafkaDTO.setBillNumber("2");
        entryKafkaDTO.setEntityID(3);
        entryKafkaDTO.setKafkaRepeat(0);
        entryKafkaDTO.setKafkaStatus(1);
        entryKafkaDTO.setEnabledFlag("Y");
        entryKafkaDTO.setCreatedName("x1");
        entryKafkaDTO.setInCalculation(0);
        entryKafkaDTOList.add(entryKafkaDTO);

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put(Constant.ENTRY_KEY_ID, "1");
        map.put(Constant.ENTRY_KEY_TIME, "2");
        list.add(map);

        PowerMockito.when(mesEntryKafkaRepository.selectBoxupBillKeys(Mockito.any())).thenReturn(list);
        PowerMockito.when(mesEntryKafkaSfcService.getBoxScanList(Mockito.any())).thenReturn(list);
        PowerMockito.when(sysLookupValuesProducer.sendInsertMsg(Mockito.any(), Mockito.any())).thenReturn(true);

        mesEntryKafkaService.getTableIDAndLastUpdateTime(list);

        mesEntryKafkaRepository.selectEntryBills(20);
        mesEntryKafkaRepository.updateEntryBills(entryKafkaDTO);
        mesEntryKafkaRepository.selectBoxupBillKeys("123");

    }
}
