package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.StItemMessageServiceImpl;
import com.zte.domain.model.stepdt.StItemMessage;
import com.zte.domain.model.stepdt.StItemMessageRepository;
import com.zte.interfaces.dto.ZtebarcodeDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2021/01/13
 */
@RunWith(PowerMockRunner.class)
public class StItemMessageServiceImplTest extends BaseTestCase {
    @InjectMocks
    private StItemMessageServiceImpl stItemMessageService;
    @Mock
    private StItemMessageRepository stItemMessageRepository;

    @Test
    public void getItemNoAndName22() throws Exception {
        List<String> supplierCodeList = new ArrayList<String>();
        supplierCodeList.add("23");
        List<ZtebarcodeDTO> tempList = new ArrayList<ZtebarcodeDTO>();
        ZtebarcodeDTO q = new ZtebarcodeDTO();
        q.setAsn("12");
        tempList.add(q);

        List<String> l = new ArrayList<>();
        l.add("12");
        PowerMockito.when(stItemMessageRepository.getSupplierInfoBySupplerCode(l)).thenReturn(tempList);
        Assert.assertNotNull(stItemMessageService.getSupplierInfoBySupplerCode(supplierCodeList));

    }

    @Test
    public void getItemNoAndName() throws Exception {
        StItemMessage itemMessage = new StItemMessage();
        PowerMockito.when(stItemMessageRepository.getItemNoAndName("130000180345AOB")).thenReturn(itemMessage);
        Assert.assertNotNull(stItemMessageService.getItemNoAndName("130000180345AOB"));
    }

    @Test
    public void queryItemNoAndNameBatch() {
        List<StItemMessage> list = new LinkedList<>();
        StItemMessage itemMessage = new StItemMessage();
        list.add(itemMessage);
        PowerMockito.when(stItemMessageRepository.queryItemNoAndNameBatch(Mockito.anyList())).thenReturn(list);
        Assert.assertNotNull(stItemMessageService.queryItemNoAndNameBatch(Arrays.asList("23")));
    }
    @Test
    public void getMaterialMessageBatch() {
        List<StItemMessage> list = new LinkedList<>();
        StItemMessage itemMessage = new StItemMessage();
        list.add(itemMessage);
        PowerMockito.when(stItemMessageRepository.getMaterialMessageBatch(Mockito.anyList())).thenReturn(list);
        Assert.assertNotNull(stItemMessageService.getMaterialMessageBatch(Arrays.asList("23")));
    }

    @Test
    public void getMaterialMessage() throws Exception {
        StItemMessage itemMessage = new StItemMessage();
        PowerMockito.when(stItemMessageRepository.getMaterialMessage("RPA3KC319541")).thenReturn(itemMessage);
        Assert.assertNotNull(stItemMessageService.getMaterialMessage("RPA3KC319541"));
    }

    @Test
    public void getBomHeadItemCode() throws Exception {
        StItemMessage itemMessage = new StItemMessage();
        PowerMockito.when(stItemMessageRepository.getBomHeadItemCode("27048")).thenReturn(itemMessage);
        Assert.assertNotNull(stItemMessageService.getBomHeadItemCode("27048"));
    }

    @Test
    public void getSupplierInfoBySupplerCode() {
        List<String> supplierCodeList = new LinkedList<>();
        supplierCodeList.add("7459618");

        List<ZtebarcodeDTO> tempList = new LinkedList<>();
        ZtebarcodeDTO a1 = new ZtebarcodeDTO();
        tempList.add(a1);
        PowerMockito.when(stItemMessageRepository.getSupplierInfoBySupplerCode(Mockito.anyList()))
                .thenReturn(tempList);
        Assert.assertNotNull(stItemMessageService.getSupplierInfoBySupplerCode(supplierCodeList));
    }
}
