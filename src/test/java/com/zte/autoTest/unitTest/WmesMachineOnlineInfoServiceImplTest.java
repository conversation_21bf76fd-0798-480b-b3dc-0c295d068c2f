package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.WmesMachineOnlineInfoServiceImpl;
import com.zte.domain.model.datawb.WmesMachineOnlineInfoRepository;
import com.zte.interfaces.dto.WmesMachineOnlineInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class WmesMachineOnlineInfoServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WmesMachineOnlineInfoServiceImpl service;

    @Mock
    private WmesMachineOnlineInfoRepository wmesMachineOnlineInfoRepository;

    @Test
    public void insertWmesMachineOnlineInfo() throws Exception {
        List<WmesMachineOnlineInfoDTO> wmesMachineOnlineInfoDTOS = new ArrayList<>();
        WmesMachineOnlineInfoDTO wmesMachineOnlineInfoDTO = new WmesMachineOnlineInfoDTO();
        wmesMachineOnlineInfoDTOS.add(wmesMachineOnlineInfoDTO);
        wmesMachineOnlineInfoDTO.setItemCode("111");
        String factoryId = "55";
        service.insertWmesMachineOnlineInfo(wmesMachineOnlineInfoDTOS, factoryId, true);
        Assert.assertNotNull(service.insertWmesMachineOnlineInfo(wmesMachineOnlineInfoDTOS, factoryId, false));
    }
}
