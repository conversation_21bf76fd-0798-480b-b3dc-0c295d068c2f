package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.ConfigMaterialBaindDqasNewGetBarcodeServiceImpl;
import com.zte.application.datawb.impl.ConfigMaterialBaindDqasNewGetConfigItemServiceImpl;
import com.zte.application.datawb.impl.ConfigMaterialBaindDqasNewServiceGetProductImpl;
import com.zte.application.datawb.impl.ConfigMaterialBaindDqasNewServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewGetBarcodeRepository;
import com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewGetConfigItemRepository;
import com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewGetProductRepository;
import com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.BoardAssemblyRelationshipDTO;
import com.zte.interfaces.dto.CheckInDTO;
import com.zte.interfaces.dto.ConfigMaterialDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class})
@PowerMockIgnore("javax.net.ssl.*")
public class ConfigMaterialBaindDqasNewServiceImplTest extends BaseTestCase {

    @InjectMocks
    ConfigMaterialBaindDqasNewServiceImpl configMaterialBaindDqasNewService;
    @Mock
    ConfigMaterialBaindDqasNewServiceGetProductImpl configMaterialBaindDqasNewGetProductService ;
    @Mock
    ConfigMaterialBaindDqasNewGetConfigItemServiceImpl configMaterialBaindDqasNewGetConfigItemService;
    @Mock
    ConfigMaterialBaindDqasNewGetBarcodeServiceImpl configMaterialBaindDqasNewGetBarcodeService;


    @Mock
    ConfigMaterialBaindDqasNewRepository configMaterialBaindDqasNewRepository;
    @Mock
    ConfigMaterialBaindDqasNewGetBarcodeRepository configMaterialBaindDqasNewGetBarcodeRepository;
    @Mock
    ConfigMaterialBaindDqasNewGetProductRepository configMaterialBaindDqasNewGetProductRepository;
    @Mock
    ConfigMaterialBaindDqasNewGetConfigItemRepository configMaterialBaindDqasNewGetConfigItemRepository ;
    @Mock
    CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init(){
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void getSubBarCodeAssemblNew()throws Exception{
        CheckInDTO params=new CheckInDTO();
        Map<String, Object> resultMap5=new HashMap<>();
        String strDeptNo="123";
        String urL="123";
        configMaterialBaindDqasNewService.getSubBarCodeAssemblNew(params,resultMap5,strDeptNo,urL);
        List<BoardAssemblyRelationshipDTO> snList=new ArrayList<>();
        BoardAssemblyRelationshipDTO snDto=new BoardAssemblyRelationshipDTO();
        snDto.setParentSn("123main");
        snList.add(snDto);
        PowerMockito.when(centerfactoryRemoteService.getBoardAssemblyRelationship(Mockito.anyString()))
        .thenReturn(snList);
        configMaterialBaindDqasNewService.getSubBarCodeAssemblNew(params,resultMap5,strDeptNo,urL);
        snDto.setSn("123sub");
        snDto.setItemCode("123itemCode");
        try {
            configMaterialBaindDqasNewService.getSubBarCodeAssemblNew(params,resultMap5,strDeptNo,urL);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }


    @Test
    public void configMaterialBaindDqasNew() throws Exception {
        try {

            /**
             *  <AUTHOR>
             *  0 入参校验
             *  @params dto 入参实体类
             */
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            CheckInDTO params =new CheckInDTO();

            params.setContractNo("123");
            params.setControlFunc("配置物料装配绑定");
            params.setItemBarcode("33078998003Z");
            params.setItemCode("123351550386");
            params.setProcedure("全部");
            params.setRecordId(Long.valueOf("8417601").longValue());
            params.setSiteId(Long.valueOf("136592639").longValue());
            params.setTaskNo("SDHSMB20080100026");
            params.setItemBarcode("");
            configMaterialBaindDqasNewService.paramInCheck( params);//以下都是判空测试的
            params.setItemBarcode("33078998003Z");
            params.setItemCode("");
            configMaterialBaindDqasNewService.paramInCheck( params);
            params.setItemCode("123351550386");
            params.setTaskNo("");
            configMaterialBaindDqasNewService.paramInCheck( params);

            params.setContractNo("123");
            params.setControlFunc("配置物料装配绑定");
            params.setItemBarcode("33078998003Z");
            params.setItemCode("123351550386");
            params.setProcedure("全部");
            params.setRecordId(Long.valueOf("8417601").longValue());
            params.setSiteId(Long.valueOf("136592639").longValue());
            params.setTaskNo("SDHSMB20080100026");
            configMaterialBaindDqasNewService.paramInCheck( params);//ok 的




            /**
             *  <AUTHOR>
             *  2查询DQAS字典配置URL
             *  @params dto 入参实体类
             */
            List<ConfigMaterialDTO> dicList2=new ArrayList<>();
            CheckInDTO params2 =new CheckInDTO();
            configMaterialBaindDqasNewService.getDQASConfigAsamAddr(dicList2,params2);//dicList2 为空

            ConfigMaterialDTO  configMaterialDTO2=new ConfigMaterialDTO();
            configMaterialDTO2.setAttribute("123");
            configMaterialDTO2.setMeaning("123");
            dicList2.add(configMaterialDTO2);//dicList2 size为1
            configMaterialBaindDqasNewService.getDQASConfigAsamAddr(dicList2,params2);

            ConfigMaterialDTO  configMaterialDTO22=new ConfigMaterialDTO();
            configMaterialDTO22.setAttribute("123");
            dicList2.add(configMaterialDTO22);//dicList2 size=2,sql为null
            List<ConfigMaterialDTO> productDtList222 =new ArrayList<>();//sql为null


            PowerMockito.when(configMaterialBaindDqasNewGetProductRepository.getProductPlaceNameByEntityId(anyString())).thenReturn(productDtList222);

            PowerMockito.when(configMaterialBaindDqasNewGetProductService.getProductPlaceNameByEntityId(anyString())).thenReturn(productDtList222);
            params2.setTaskNo("SDHSMB20080100026");
            configMaterialBaindDqasNewService.getDQASConfigAsamAddr(dicList2,params2);

            List<ConfigMaterialDTO> productDtList2222 =new ArrayList<>();;//dicList2 size=2,sql不为null
            ConfigMaterialDTO  configMaterialDTO222=new ConfigMaterialDTO();
            configMaterialDTO222.setProductPlaceName("123");
            productDtList2222.add(configMaterialDTO222);//sql不为null
            PowerMockito.when(configMaterialBaindDqasNewGetProductService.getProductPlaceNameByEntityId(anyString())).thenReturn(productDtList2222);
            configMaterialDTO2.setSectionName("1");
            configMaterialDTO2.setMeaning("12");
            configMaterialDTO22.setSectionName("1");
            configMaterialDTO22.setMeaning("12");
            configMaterialBaindDqasNewService.getDQASConfigAsamAddr(dicList2,params2);//dtProductList 为空

            configMaterialDTO2.setSectionName("123");
            configMaterialDTO2.setMeaning("12");
            configMaterialDTO22.setSectionName("123");
            configMaterialDTO22.setMeaning("12");

            configMaterialBaindDqasNewService.getDQASConfigAsamAddr(dicList2,params2);//dtProductList 不为空

            params2.setItemBarcode("33078998003Z");
            params2.setItemCode("123351550386");
            List<ConfigMaterialDTO> configMaterialDTOList22222222=new ArrayList<>();//SQL 为空
            PowerMockito.when(configMaterialBaindDqasNewRepository.getSubItemByProcedule(anyMap())).thenReturn(configMaterialDTOList22222222);
            configMaterialBaindDqasNewService.checkDqas(params2);
            ConfigMaterialDTO configMaterialDTO22222222222=new ConfigMaterialDTO();
            configMaterialDTO22222222222.setAttribute("123");
            configMaterialDTOList22222222.add(configMaterialDTO22222222222);
            PowerMockito.when(configMaterialBaindDqasNewRepository.getSubItemByProcedule(anyMap())).thenReturn(configMaterialDTOList22222222);
            configMaterialBaindDqasNewService.checkDqas(params2);  //SQL 不为空


            List<ConfigMaterialDTO> dicList222222=new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO2222222=new ConfigMaterialDTO();
            configMaterialDTO2222222.setSectionName("");
            configMaterialDTO2222222.setMeaning("123");
            dicList222222.add(configMaterialDTO2222222);
            configMaterialBaindDqasNewService.getMeaning(dicList222222);


            /**
             *  <AUTHOR>
             *  3 当前条码进行管控+insert 入表
             *  @params dto 入参实体类
             */
            CheckInDTO params3 =new CheckInDTO();
            configMaterialBaindDqasNewService.getWLBOQControlResult(params3,"123","123");


            /**
             *  <AUTHOR>
             *  4 查看有没有需要进行下一级DQAS的
             *  @params dto 入参实体类
             */
            List<ConfigMaterialDTO> configMaterialDTOList4=new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO4=new ConfigMaterialDTO();
            configMaterialDTO4.setNextFlag("Y");
            configMaterialDTOList4.add(configMaterialDTO4);
            configMaterialBaindDqasNewService.getNextFlag(configMaterialDTOList4);


            Map<String, Object> inMap5=new HashMap<>();
            List<ConfigMaterialDTO> list5 =new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO5=new ConfigMaterialDTO();
            configMaterialDTO5.setQty(Long.valueOf("0").longValue());
            list5.add(configMaterialDTO5);
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list5);
            configMaterialBaindDqasNewService.subItemDQAS(inMap5);

            configMaterialDTO5.setQty(Long.valueOf("30").longValue());
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list5);
            configMaterialBaindDqasNewService.subItemDQAS(inMap5);


            /**
             *  <AUTHOR>
             *  5.1 校验当前条码配置物料装配绑定下挂条码是否DQAS管控+insert 入表
             *  @params dto 入参实体类
             */
            Map<String, Object> resultMap = new HashMap<>();
            List<ConfigMaterialDTO> dtSubBarcodeList =new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO51=new ConfigMaterialDTO();
            configMaterialDTO5.setItemBarcode("123");
            configMaterialDTO5.setItemCode("123");
            dtSubBarcodeList.add(configMaterialDTO51);

//            configMaterialBaindDqasNewService.getSubConfigItemAssemble1(dtSubBarcodeList,params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");
//            configMaterialBaindDqasNewService.getSubConfigItemAssemble(dtSubBarcodeList,params,"123","http://10.7.68.85/website/MESService.asmx");

            /**
             *  <AUTHOR>
             *  6. 查询Boq物料条码绑定 下挂的条码+insert 入表
             *  @params dto 入参实体类
             */
            PowerMockito.when(configMaterialBaindDqasNewGetConfigItemService.getSubBarCodeByMainBar(anyMap())).thenReturn(dtSubBarcodeList);
//            configMaterialBaindDqasNewService.getSubBarCodeByMainBar(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");


            /**
             *  <AUTHOR>
             * 7 查询部件组装 下挂的条码+insert 入表
             *  @params dto 入参实体类
             */
            //webservice 記得要mock，待補充。改返回 1，则总的类可以往下走6,7,8,，9了
            //webservice 記得要mock，要補充,改返回 1，则总的类可以往下走6,7,8,，9了
            //webservice不改返回 1，就可以增加覆盖率
            PowerMockito.when(configMaterialBaindDqasNewRepository.getSubBarByMainBujianZuzhuang(anyMap())).thenReturn(dtSubBarcodeList);
//            configMaterialBaindDqasNewService.getSubBarByMainBujianZuzhuang(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");

            /**
             *  <AUTHOR>
             * 9-2 查询imes下挂的条码+insert 入表
             *  @params dto 入参实体类
             */
            PowerMockito.when(configMaterialBaindDqasNewRepository.getSubBarByImesMainBarcode(anyMap())).thenReturn(dtSubBarcodeList);
//            configMaterialBaindDqasNewService.getSubBarByImesMainBarcode(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");


            /**
             *  <AUTHOR>
             * 8.1 23到28开头、70到79开头的12位条码是单板条码
             *  @params dto 入参实体类
             */

            params.setItemBarcode("261234565656");
//            configMaterialBaindDqasNewService.getSubBarCodeBoard1(dtSubBarcodeList,params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");


            /**
             *  <AUTHOR>
             * 8 23到28开头、70到79开头的12位条码是单板条码
             * 9  若为单板条码则到SPM中取下挂的条码进行DQAS校验
             *  @params dto 入参实体类
             */
            PowerMockito.when(configMaterialBaindDqasNewGetBarcodeService.getSubBarCodeBoard(anyMap())).thenReturn(dtSubBarcodeList);
//            configMaterialBaindDqasNewService.getSubBarCodeBoard(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");

            params.setItemBarcode("");
            PowerMockito.when(configMaterialBaindDqasNewGetBarcodeService.getSubBarCodeBoard(anyMap())).thenReturn(dtSubBarcodeList);
//            configMaterialBaindDqasNewService.getSubBarCodeBoard(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");


            params.setItemBarcode("261234565656");
            /**
             *  <AUTHOR>
             * 9.1  若为单板条码则到SPM中取下挂的条码进行DQAS校验
             *  @params dto 入参实体类
             */

            ConfigMaterialDTO configMaterialDTO9=new ConfigMaterialDTO();
            configMaterialDTO9.setConfigBarcode1("261234565656");
            configMaterialDTO9.setConfigBarcode2("261234565656");
            configMaterialDTO9.setItemCode2("123");
            configMaterialDTO9.setConfigBarcode3("123");
            configMaterialDTO9.setItemCode3("123");
            List<ConfigMaterialDTO> outList2 = new ArrayList<>();
            outList2.add(configMaterialDTO9);

            PowerMockito.when(configMaterialBaindDqasNewGetBarcodeService.getSubBarCodeAssembly(Mockito.anyMap())).thenReturn(outList2);
//            configMaterialBaindDqasNewService.getSubBarCodeAssembly(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");


            configMaterialDTO9.setConfigBarcode1("123");
            PowerMockito.when(configMaterialBaindDqasNewGetBarcodeService.getSubBarCodeAssembly(Mockito.anyMap())).thenReturn(outList2);
//            configMaterialBaindDqasNewService.getSubBarCodeAssembly(params,resultMap,"123","http://10.7.68.85/website/MESService.asmx");






            params.setContractNo("123");
            params.setControlFunc("配置物料装配绑定");
            params.setItemBarcode("33078998003Z");
            params.setItemCode("123351550386");
            params.setProcedure("全部");
            params.setRecordId(Long.valueOf("8417601").longValue());
            params.setSiteId(Long.valueOf("136592639").longValue());
            params.setTaskNo("SDHSMB20080100026");
            params.setItemBarcode("");
            configMaterialBaindDqasNewService.paramInCheckOld( params);//以下都是判空测试的
            params.setItemBarcode("33078998003Z");
            params.setItemCode("");
            configMaterialBaindDqasNewService.paramInCheckOld( params);
            params.setItemCode("123351550386");
            params.setTaskNo("");
            configMaterialBaindDqasNewService.paramInCheckOld( params);
            params.setContractNo("");
            configMaterialBaindDqasNewService.paramInCheckOld( params);

            params.setContractNo("123");
            params.setControlFunc("配置物料装配绑定");
            params.setItemBarcode("33078998003Z");
            params.setItemCode("123351550386");
            params.setProcedure("全部");
            params.setRecordId(Long.valueOf("8417601").longValue());
            params.setSiteId(Long.valueOf("136592639").longValue());
            params.setTaskNo("SDHSMB20080100026");
            configMaterialBaindDqasNewService.paramInCheckOld( params);//ok 的




            configMaterialBaindDqasNewService.getWLControlResult(dtSubBarcodeList,params,"123");//URL webservice改一下，改一下







            //为空
            PowerMockito.when(configMaterialBaindDqasNewRepository.getDeptByEntityNo(Mockito.anyString())).thenReturn(productDtList222);
            configMaterialBaindDqasNewService.checkDqasOld(params);
            //不为空
            ConfigMaterialDTO  configMaterialDTO67=new ConfigMaterialDTO();
            configMaterialDTO67.setAttribute("");
            productDtList222.add(configMaterialDTO67);
            PowerMockito.when(configMaterialBaindDqasNewRepository.getDeptByEntityNo(Mockito.anyString())).thenReturn(productDtList222);

            //subItemDQAS 为true
            configMaterialDTO5.setQty(Long.valueOf("0").longValue());
            list5.add(configMaterialDTO5);
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list5);
            configMaterialBaindDqasNewService.subItemDQAS(inMap5);
            //subItemDQAS 为false
            configMaterialDTO5.setQty(Long.valueOf("30").longValue());
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list5);
            configMaterialBaindDqasNewService.subItemDQAS(inMap5);


            configMaterialBaindDqasNewService.checkDqasOld(params);



            configMaterialDTO67.setAttribute(Constant.DEPT_NO);
            //subItemDQAS 为false
            configMaterialDTO5.setQty(Long.valueOf("30").longValue());
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list5);
            configMaterialBaindDqasNewService.subItemDQAS(inMap5);
            configMaterialBaindDqasNewService.subItemDQAS(inMap5);

            configMaterialBaindDqasNewService.checkDqasOld(params);




            CheckInDTO ccc=new CheckInDTO();
            ccc.setProcedure("全部");
//            configMaterialBaindDqasNewService.dqasWebservice(ccc,"http://10.7.68.85/website/MESService.asmx");
        }
        catch (Exception e)
        {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void dqasWebservice() {
        try {
            CheckInDTO ccc=new CheckInDTO();
            ccc.setProcedure("全部");
//            configMaterialBaindDqasNewService.dqasWebservice(ccc,"http://10.7.68.85/website/MESService.asmx");
        }
        catch (Exception e)
        {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
}
