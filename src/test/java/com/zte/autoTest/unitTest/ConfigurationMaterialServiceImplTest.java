package com.zte.autoTest.unitTest;

import com.zte.application.datawb.ConfigMaterialBindService;
import com.zte.application.datawb.impl.ConfigurationMaterialServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.ConfigurationMaterialRepository;
import com.zte.interfaces.dto.ConfigDetailDTO;
import com.zte.interfaces.dto.MfgSiteDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

public class ConfigurationMaterialServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ConfigurationMaterialServiceImpl service;

    @Mock
    private ConfigurationMaterialRepository configurationMaterialRepository;

    @Mock
    private ConfigMaterialBindService configMaterialBindService;

    @Test
    public void getConfigurationMaterialInfoByEntityNamesTest() throws Exception {
        MfgSiteDTO dto = new MfgSiteDTO();
        try {
            service.getConfigurationMaterialInfoByEntityNames(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(RetCode.VALIDATIONERROR_MSGID));
        }
        try {
            service.getConfigurationMaterialInfoByEntityNames(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.ENTITY_NAME_NOT_NULL));
        }
        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("test");
        entityNameList.add("test1");
        dto.setEntityNameList(entityNameList);
        try {
            service.getConfigurationMaterialInfoByEntityNames(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.CURRENT_PAGE_NOT_NULL));
        }
        dto.setCurrentPage(1);
        dto.setPageSize(202);
        try {
            service.getConfigurationMaterialInfoByEntityNames(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PAGE_SIZE_NOT_GREATER_THAN_200));
        }
        dto.setPageSize(200);
        List<MfgSiteDTO> mfgSiteDTOList = new ArrayList<>();
        PowerMockito.when(configurationMaterialRepository.getConfigurationBasicInfoByEntityNames(Mockito.anyObject())).thenReturn(mfgSiteDTOList);
        service.getConfigurationMaterialInfoByEntityNames(dto);

        MfgSiteDTO mfgSiteDTO = new MfgSiteDTO();

        mfgSiteDTO.setMfgSiteID("test1");
        mfgSiteDTOList.add(mfgSiteDTO);
        List<ConfigDetailDTO> subDetailDTOList = new ArrayList<>();
        PowerMockito.when(configMaterialBindService.getDetailByMfgSiteIDList(Mockito.anyList())).thenReturn(subDetailDTOList);
        service.getConfigurationMaterialInfoByEntityNames(dto);

        ConfigDetailDTO configDetailDTO = new ConfigDetailDTO();
        configDetailDTO.setMfgSiteID("test1");
        subDetailDTOList.add(configDetailDTO);
        service.getConfigurationMaterialInfoByEntityNames(dto);
    }
}
