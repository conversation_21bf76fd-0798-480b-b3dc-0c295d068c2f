package com.zte.autoTest.unitTest;

import java.util.ArrayList;
import java.util.List;

import com.zte.application.datawb.impl.OpProdPlanInfoServiceImpl;
import com.zte.domain.model.OpProductinfoRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import com.zte.application.datawb.impl.OpProdplanServiceImpl;
import com.zte.domain.model.OpProdplanRepository;
import com.zte.interfaces.dto.BoardInstructionCycleInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
public class OpProdplanServiceImplTest {
	
	@InjectMocks
	private OpProdplanServiceImpl opProdplanServiceImpl;

	@InjectMocks
	private OpProdPlanInfoServiceImpl opProdPlanInfoService;

	@Mock
	OpProdplanRepository opProdplanRepository;

	@Mock
	private OpProductinfoRepository opProductinfoRepository;

	@Before
    public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
    }
	
	@Test
	@PrepareForTest({})
    public void selectGetDateByProdplanNo() throws Exception {
		
		List<BoardInstructionCycleInfoDTO> tempList = new ArrayList<>();
		BoardInstructionCycleInfoDTO dto = new BoardInstructionCycleInfoDTO();
		tempList.add(dto);
		PowerMockito.when(opProdplanRepository.getBoardWorkorderInfoByProdplanNo(Mockito.any())).thenReturn(tempList);
		
		List<String> prodplanNoList = new ArrayList<>();
		prodplanNoList.add("123");
		Assert.assertNotNull(opProdplanServiceImpl.getBoardWorkorderInfoByProdplanNo(prodplanNoList));
    }


    @Test
	public void validateSubmitStatus()
	{
		Assert.assertFalse(opProdPlanInfoService.validateSubmitStatus("123456"));
	}
}
