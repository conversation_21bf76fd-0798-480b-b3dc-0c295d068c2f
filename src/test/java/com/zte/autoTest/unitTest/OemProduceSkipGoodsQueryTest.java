package com.zte.autoTest.unitTest;


import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.application.datawb.JobsSupplierProduceDataService;
import com.zte.application.datawb.impl.OemProduceSkipGoodsQueryServiceImpl;

import com.zte.application.impl.IMESLogServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.utils.Constant;

import com.zte.common.utils.ExcelBigDataExportManage;
import com.zte.common.utils.ExcelCommonUtils;
import com.zte.common.utils.ExcelName;
import com.zte.domain.model.MessageId;
import com.zte.domain.model.datawb.CpeBoxupBillRepository;
import com.zte.domain.model.datawb.OemProduceSkipGoodsQuery;
import com.zte.domain.model.datawb.OemProduceSkipGoodsQueryRepository;

import com.zte.interfaces.dto.ConfigureMonitorDTO;
import com.zte.interfaces.dto.OemProduceSkipGoodsQueryDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.formula.EvaluationWorkbook;
import org.apache.poi.ss.formula.udf.UDFFinder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.doThrow;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,MultipartFile.class,ExcelUtil.class,SpringContextUtil.class})
public class OemProduceSkipGoodsQueryTest extends BaseTestCase {

    @InjectMocks
    OemProduceSkipGoodsQueryServiceImpl oemProduceSkipGoodsQueryServiceImpl;

    @InjectMocks
    ExcelCommonUtils excelCommonUtils;

    @Mock
    OemProduceSkipGoodsQueryRepository oemProduceSkipGoodsQuery;
    @Mock
    CpeBoxupBillRepository cpeBoxupBillRepository;
    @Mock
    CpeBoxupBillService cpeBoxupBillService;
    @Mock
    JobsSupplierProduceDataService jobsSupplierProduceDataService;
    @Mock
    private ExcelBigDataExportManage excelBigDataExportManage;
    @Mock
    MultipartFile multipartFile;
    @Mock
    LocaleMessageSourceBean localeMessageSourceBean;
    @Mock
    Workbook workbook;
    @Mock
    Sheet sheet;
    @Mock
    Row row;
    @Mock
    Cell cell;
    @Mock
    EmailUtils emailUtils;
    @Mock
    IMESLogServiceImpl imesLogService;
    @Mock
    File file;
    @Mock
    Logger logger;

    @Before
    public void init(){

        PowerMockito.mockStatic(CommonUtils.class,MultipartFile.class,ExcelUtil.class,SpringContextUtil.class);
    }

    @Test
    public void romoveServerFile() throws Exception {
        doNothing().when(excelBigDataExportManage).romoveServerFile();
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "romoveServerFile");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getCount() throws Exception {

        OemProduceSkipGoodsQueryDTO record = new OemProduceSkipGoodsQueryDTO();
        PowerMockito.when(oemProduceSkipGoodsQuery.getCount(any())).thenReturn(1L);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getCount", record);
        Assert.assertNotNull(record);
    }

    @Test
    public void settingParam() throws Exception {

        OemProduceSkipGoodsQueryDTO record = new OemProduceSkipGoodsQueryDTO();
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "settingParam", record);

        record.setPage(1L);
        record.setEmail("123");
        record.setDoId("12");
        record.setBillNumber("23");
        record.setContractNumber("23");
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "settingParam", record);
        Assert.assertNotNull(record);
    }

    @Test
    public void queryJobsSupplierProduceData() throws Exception {
        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys = new ArrayList<>();
        OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery1 = new OemProduceSkipGoodsQuery();
        oemProduceSkipGoodsQuery1.setBarCode("345346");
        oemProduceSkipGoodsQuerys.add(oemProduceSkipGoodsQuery1);

        List<OemProduceSkipGoodsQuery> jobsSupTemp = new ArrayList<>();
        PowerMockito.when(jobsSupplierProduceDataService.getOemQueryList(any())).thenReturn(jobsSupTemp);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "copyProperties", jobsSupTemp, oemProduceSkipGoodsQuerys);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "queryJobsSupplierProduceData", oemProduceSkipGoodsQuerys);
        Assert.assertNotNull(oemProduceSkipGoodsQuerys);
        Assert.assertNotNull(jobsSupTemp);
    }

    @Test
    public void copyProperties() throws Exception {

        List<OemProduceSkipGoodsQuery> sources = new ArrayList<>();
        OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery1 = new OemProduceSkipGoodsQuery();
        oemProduceSkipGoodsQuery1.setBarCode("111111");
        sources.add(oemProduceSkipGoodsQuery1);
        List<OemProduceSkipGoodsQuery> desc = new ArrayList<>();
        OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery2 = new OemProduceSkipGoodsQuery();
        oemProduceSkipGoodsQuery2.setBarCode("111111");
        desc.add(oemProduceSkipGoodsQuery2);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "copyProperties", sources, desc);
        Assert.assertNotNull(sources);
    }

    @Test
    public void getPage() throws Exception {
        OemProduceSkipGoodsQueryDTO record = new OemProduceSkipGoodsQueryDTO();
        //doNothing().when(oemProduceSkipGoodsQueryServiceImpl).settingParam(record);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "settingParam", record);

        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys = new ArrayList<>();
        PowerMockito.when(cpeBoxupBillService.getPageByContract(any())).thenReturn(oemProduceSkipGoodsQuerys);
        PowerMockito.when(cpeBoxupBillService.getCountByContract(any())).thenReturn(0L);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getPage", record);

        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys2 = new ArrayList<>();
        OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery1 = new OemProduceSkipGoodsQuery();
        oemProduceSkipGoodsQuery1.setBarCode("234235");
        oemProduceSkipGoodsQuerys2.add(oemProduceSkipGoodsQuery1);
        PowerMockito.when(cpeBoxupBillService.getPageByContract(any())).thenReturn(oemProduceSkipGoodsQuerys2);
        PowerMockito.when(cpeBoxupBillService.getCountByContract(any())).thenReturn(1L);
        //doNothing().when(oemProduceSkipGoodsQueryServiceImpl).queryJobsSupplierProduceData(any());
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "queryJobsSupplierProduceData", oemProduceSkipGoodsQuerys);
        Assert.assertNotNull(Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getPage", record));

    }

    @Test
    public void getBtbOemPage() throws Exception {
        OemProduceSkipGoodsQueryDTO record = new OemProduceSkipGoodsQueryDTO();
        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQueryList = new ArrayList<>();
        PowerMockito.when(cpeBoxupBillService.callOemPageByContract(any())).thenReturn(oemProduceSkipGoodsQueryList);
        PowerMockito.when(cpeBoxupBillService.callOemCountByContract(any())).thenReturn(1L);
        Assert.assertNotNull(Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getBtbOemPage", record));

    }

    @Test
    public void uploadExcel() throws Exception {
        PowerMockito.mockStatic(ExcelUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(SpringContextUtil.class);

        OemProduceSkipGoodsQueryDTO dto = new OemProduceSkipGoodsQueryDTO();
        dto.setFile(multipartFile);
        PowerMockito.when(multipartFile.getOriginalFilename()).thenReturn("21321.xxxx");
        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        when(localeMessageSourceBean.getMessage(Mockito.any())).thenReturn("OK");
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
        //oemProduceSkipGoodsQueryServiceImpl.uploadExcel(dto);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "uploadExcel", dto);


        Workbook wb = null;
        PowerMockito.when(multipartFile.getOriginalFilename()).thenReturn("21321.xls");
        when(ExcelUtil.initWorkbook(any(), anyString())).thenReturn(wb);
        //oemProduceSkipGoodsQueryServiceImpl.uploadExcel(dto);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "uploadExcel", dto);

        PowerMockito.when(multipartFile.getOriginalFilename()).thenReturn("21321.xlsx");
        when(ExcelUtil.initWorkbook(any(), anyString())).thenReturn(workbook);
        PowerMockito.when(workbook.getSheetAt(0)).thenReturn(sheet);
        PowerMockito.when(sheet.getLastRowNum()).thenReturn(0);
        PowerMockito.when(sheet.getRow(0)).thenReturn(row);
        PowerMockito.when(row.getCell(0)).thenReturn(cell);
        //oemProduceSkipGoodsQueryServiceImpl.uploadExcel(dto);
        Assert.assertNotNull(Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "uploadExcel", dto));

    }

    @Test
    public void createExcel() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        OemProduceSkipGoodsQueryDTO record = new OemProduceSkipGoodsQueryDTO();
        record.setPage(1L);
        record.setEmail("123");
        record.setDoId("12");
        record.setBillNumber("23");
        record.setContractNumber("23");
        List<String> list = new ArrayList<>();
        record.setDoIds(list);
        record.setBillNumbers(list);
        record.setContractNumbers(list);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "settingParam", record);
        Long maxRows = 10L;
        PowerMockito.when(cpeBoxupBillService.getCountByContract(any())).thenReturn(maxRows);
        String path = "/usr/local/tomcat/logs/";
        Long sheetMaxRows = 1000000L;
        Long page = 1L;
        Long nowMaxRows = maxRows / sheetMaxRows >= Constant.INT_1 ? sheetMaxRows : maxRows % sheetMaxRows;
        List<String> fileUrls = new ArrayList<>();
        fileUrls.add("325262");
        //Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getExcel", 1L,1L,path,fileUrls,record);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
        String email = record.getEmail();
        StringBuilder sb = new StringBuilder();
        //Whitebox.invokeMethod(emailUtils, "sendMail", email,any(),"",sb.toString(),"");

        try {
            doNothing().when(imesLogService).log(any(),any());
            Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "createExcel", record);
        } catch (Exception e){
            Assert.assertEquals(MessageId.CALL_CLOUD_DISK_ERROR, e.getMessage());
        }

        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "createExcel", record);

        try {
            doThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_CLOUD_DISK_ERROR)).when(cpeBoxupBillService).getCountByContract(any());
            Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "createExcel", record);
        } catch (Exception e){
            try {
                PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_CLOUD_DISK_ERROR));
                doNothing().when(imesLogService).log(any(),any());
            } catch (Exception e1){
                Assert.assertEquals(MessageId.CALL_CLOUD_DISK_ERROR, e1.getMessage());
            }
            //doNothing().when(emailUtils).sendMail(any(),any(),any(),any(),any());
            Assert.assertEquals(MessageId.CALL_CLOUD_DISK_ERROR, e.getMessage());
        }

    }

    @Test
    public void getExcel() throws Exception {
        OemProduceSkipGoodsQueryDTO dto = new OemProduceSkipGoodsQueryDTO();
        dto.setPage(1L);
        dto.setEmail("123");
        dto.setDoId("12");
        dto.setBillNumber("23");
        dto.setContractNumber("23");
        String path = "/usr/local/tomcat/logs/";
        Long nowMaxRows = 1L;
        String fileName = "ExcelBigDataExport_13214215.xlsx";
        Long page = 1L;
        List<String> fileUrls = new ArrayList<>();
        List<OemProduceSkipGoodsQuery> oemProduceSkipGoods =new ArrayList<>();
        String[] files = new String[]{"1","2"};
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "writeExcelFile", path,fileName, Math.toIntExact(nowMaxRows), oemProduceSkipGoods);
        PowerMockito.when(excelBigDataExportManage.upload(any(), any(), any())).thenReturn(files);
        Whitebox.invokeMethod(excelBigDataExportManage, "serverFileManager",any());

        PowerMockito.when(file.delete()).thenReturn(false);
        logger.error(" 删除本地文件失败: {}" , path + fileName);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getExcel", nowMaxRows,page,path,fileUrls,dto);
        PowerMockito.when(file.delete()).thenReturn(true);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "getExcel", nowMaxRows,page,path,fileUrls,dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void writeExcel() throws Exception {
        OemProduceSkipGoodsQueryDTO dto = new OemProduceSkipGoodsQueryDTO();
        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys = new ArrayList<>();
        OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery2 = new OemProduceSkipGoodsQuery();
        oemProduceSkipGoodsQuery2.setBarCode("2352352");
        oemProduceSkipGoodsQuerys.add(oemProduceSkipGoodsQuery2);

        PowerMockito.when(cpeBoxupBillService.getPageByContract(any())).thenReturn(oemProduceSkipGoodsQuerys);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "queryJobsSupplierProduceData", oemProduceSkipGoodsQuerys);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "writeExcel", dto);

        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys2 = new ArrayList<>();
        for (int i=0;i<2000;i++) {
            OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery1 = new OemProduceSkipGoodsQuery();
            oemProduceSkipGoodsQuery1.setBarCode("2352352");
            oemProduceSkipGoodsQuery1.setContractNumber(String.valueOf(i));
            oemProduceSkipGoodsQuerys2.add(oemProduceSkipGoodsQuery1);
        }
        PowerMockito.when(cpeBoxupBillService.getPageByContract(any())).thenReturn(oemProduceSkipGoodsQuerys2);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "queryJobsSupplierProduceDataByPage", 1,1000,2000,oemProduceSkipGoodsQuerys2);
        Assert.assertNotNull(Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "writeExcel", dto));

    }

    @Test
    public void writeExcelFile() throws Exception {

            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            OemProduceSkipGoodsQueryDTO record=new OemProduceSkipGoodsQueryDTO();
            record.setPage(1L);
            List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys=new ArrayList<>();
            OemProduceSkipGoodsQuery o = new OemProduceSkipGoodsQuery();
            o.setPreserved1("121");
            o.setContractNumber("123");
            o.setBarCode("12");o.setBarCode("13");
            oemProduceSkipGoodsQuerys.add(o);

            Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "writeExcelFile", "123","123",123, oemProduceSkipGoodsQuerys);
        try {
            PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenThrow(new BusiException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_CLOUD_DISK_ERROR));
            Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "writeExcelFile", "123","123",123, oemProduceSkipGoodsQuerys);
        } catch (Exception e){
            logger.error(" 将文件暂存本地失败: "+ e.getMessage());
            Assert.assertEquals(MessageId.CALL_CLOUD_DISK_ERROR, e.getMessage());
        }
    }

    @Test
    public void queryJobsSupplierProduceDataByPage() throws Exception {

        List<OemProduceSkipGoodsQuery> temp = new ArrayList<>();
        List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys = new ArrayList<>();
        OemProduceSkipGoodsQuery oemProduceSkipGoodsQuery2 = new OemProduceSkipGoodsQuery();
        oemProduceSkipGoodsQuery2.setBarCode("2352352");
        oemProduceSkipGoodsQuerys.add(oemProduceSkipGoodsQuery2);
        List<OemProduceSkipGoodsQuery> jobsSupTemp = new ArrayList<>();

        PowerMockito.when(jobsSupplierProduceDataService.getOemQueryList(any())).thenReturn(jobsSupTemp);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "copyProperties", jobsSupTemp, oemProduceSkipGoodsQuerys);
        Whitebox.invokeMethod(oemProduceSkipGoodsQueryServiceImpl, "queryJobsSupplierProduceDataByPage", 1,1,1,oemProduceSkipGoodsQuerys);
        Assert.assertNotNull(temp);
        Assert.assertNotNull(oemProduceSkipGoodsQuerys);
    }

//    @Test
//    public void oemProduceSkipGoodsQuery() throws Exception {
//        try {
//            OemProduceSkipGoodsQueryDTO record=new OemProduceSkipGoodsQueryDTO();
//            record.setPage(1L);
//           // emProduceSkipGoodsQueryService.getCount(record);
//
//            record.setBillNumber("12");
//            record.setContractNumber("123");
//            record.setDoId("123");
//           // emProduceSkipGoodsQueryService.settingParam(record);
//
//            List<OemProduceSkipGoodsQuery> sources=new ArrayList<>();
//            List<OemProduceSkipGoodsQuery> desc=new ArrayList<>();
//            OemProduceSkipGoodsQuery obj=new OemProduceSkipGoodsQuery();
//            obj.setDsnNumber(Constant.STR_TAB+12);
//            obj.setWirelessName1("12");
//            obj.setWirelessName2("12");
//            obj.setWirelessPassword1("12");
//            obj.setWirelessPassword2("12");
//            obj.setAccessAddress("12");
//            obj.setUserName("12");
//            obj.setTerminalPassword("12");
//            obj.setEquipmentName(Constant.STR_TAB+12);
//            obj.setMacAddress1("12");
//            obj.setMacAddress2("12");
//            obj.setMacAddress3("12");
//            obj.setItemRevision("12");
//            obj.setRemark("12");
//            Date d=new Date();
//
//            obj.setProduceDate(d);
//            obj.setImportDate(d);
//            obj.setSoftVersion("12");
//            obj.setHardVersion("12");
//            obj.setRatedCurrent("12");
//            obj.setRatedVoltage("12");
//            obj.setBuyBatchNo("12");
//            obj.setCraftsNo("12");
//            obj.setPreserved1(Constant.STR_TAB+12);
//            obj.setPreserved2(Constant.STR_TAB+12);
//            obj.setPreserved3(Constant.STR_TAB+12);
//            obj.setPreserved4(Constant.STR_TAB+12);
//            obj.setPreserved5(Constant.STR_TAB+12);
//
//            String itemNo=obj.getItemNo();
//            obj.setItemNo(Constant.STR_TAB+itemNo);
//            sources.add(obj);
//            desc.add(obj);
//           // emProduceSkipGoodsQueryService.copyProperties(sources,desc);
//
//            List< OemProduceSkipGoodsQuery > oemProduceSkipGoodsQuerys=new ArrayList<>();
//            OemProduceSkipGoodsQuery oo =new OemProduceSkipGoodsQuery();
//            oo.setBarCode("123");
//            oemProduceSkipGoodsQuerys.add(oo);
//            //emProduceSkipGoodsQueryService.queryJobsSupplierProduceData(oemProduceSkipGoodsQuerys);
//
//
//        }
//        catch (Exception e){}
//
//    }
//
//
//    @Test
//    public void oemProduceSkipGoodsQuery3() throws Exception {
//        try {
//            PowerMockito.mockStatic(CommonUtils.class);
//            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
//
//
//
//            OemProduceSkipGoodsQueryDTO record=new OemProduceSkipGoodsQueryDTO();
//            record.setPage(1L);
//
//            List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys=new ArrayList<>();
//            OemProduceSkipGoodsQuery o=new OemProduceSkipGoodsQuery();
//            o.setPreserved1("121");
//            o.setContractNumber("123");
//            o.setBarCode("12");o.setBarCode("13");
//            oemProduceSkipGoodsQuerys.add(o);
//
//
//
//            record.setEmail("123");
//
//            record.setDoId("12");
//            record.setBillNumber("23");
//            record.setContractNumber("23");
//            //emProduceSkipGoodsQueryService.getList(record);
//            List<String> fileUrls=new ArrayList<>();
//            String[] files =new String[]{"w23242","35434637"};
//            PowerMockito.when(excelBigDataExportManage.upload("21", "temp.xlsx", record.getEmpNo())).thenReturn(files);
//            List<OemProduceSkipGoodsQuery> s2 = new ArrayList<>();
//            PowerMockito.when(cpeBoxupBillService.getPageByContract(any())).thenReturn(s2);
//            Whitebox.invokeMethod(excelBigDataExportManage, "serverFileManager", "21");
//
//            oemProduceSkipGoodsQueryServiceImpl.getExcel(0L,1L,"12",fileUrls,record) ;
//        }
//        catch (Exception e){
//            Assert.assertEquals(MessageId.CALL_CLOUD_DISK_ERROR, e.getMessage());
//        }
//
//    }
//
//
//
//    @Test
//    public void oemProduceSkipGoodsQuery45() throws Exception {
//        try {
//            PowerMockito.mockStatic(CommonUtils.class);
//            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
//
//
//
//            OemProduceSkipGoodsQueryDTO record=new OemProduceSkipGoodsQueryDTO();
//            record.setPage(1L);
//
//            List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys=new ArrayList<>();
//            OemProduceSkipGoodsQuery o=new OemProduceSkipGoodsQuery();
//            o.setPreserved1("121");
//            o.setContractNumber("123");
//            o.setBarCode("12");o.setBarCode("13");
//            oemProduceSkipGoodsQuerys.add(o);
//            record.setEmail("123");
//            record.setDoId("12");
//            record.setBillNumber("23");
//            record.setContractNumber("23");
//            List<String> fileUrls=new ArrayList<>();
//
//            List<OemProduceSkipGoodsQuery> s2 = new ArrayList<>();
//
//
//            PowerMockito.when(cpeBoxupBillService.getPageByContract(any())).thenReturn(oemProduceSkipGoodsQuerys);
//            oemProduceSkipGoodsQueryServiceImpl.writeExcel(record )  ;
//
//
//
//        }
//        catch (Exception e){}
//
//    }
//
//
//
//
//
//
//
//    @Test
//    public void oemProduceSkipGoodsQuery35() throws Exception {
//        try {
//            PowerMockito.mockStatic(CommonUtils.class);
//            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
//
//
//            List rows=new ArrayList();
//            String[] valueFields=new String[2];
//            valueFields[0]="1";
//            valueFields[2]="2";
//            rows.add("1");
//            rows.add("2");
//            excelCommonUtils.getExcelValues( rows, valueFields);
//
//
//        }
//        catch (Exception e){}
//
//    }
//
//
//
//    @Test
//    public void oemProduceSkipGoodsQuery36() throws Exception {
//        try {
//            PowerMockito.mockStatic(CommonUtils.class);
//            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
//
//            List rows=new ArrayList();
//            String[] valueFields=new String[2];
//            valueFields[0]="1";
//            valueFields[1]="2";
//            rows.add("1");
//            rows.add("2");
//            //excelCommonUtils.getExcelValues( rows, valueFields);
//
//            List<OemProduceSkipGoodsQuery> oemProduceSkipGoodsQuerys=new ArrayList<>();
//            OemProduceSkipGoodsQuery o=new OemProduceSkipGoodsQuery();
//            o.setPreserved1("121");
//            o.setContractNumber("123");
//            o.setBarCode("12");o.setBarCode("13");
//            oemProduceSkipGoodsQuerys.add(o);
//            String[] TITLE ={ "合同号","发货指令号（DO单）",
//                    "出库完成时间","装箱单号","子部件名称",
//                    "子部件代码","子部件条码","D-SN",
//                    "无线网络名称1","无线网络密钥1","无线网络名称2","无线网络密钥2",
//                    "接入地址","用户名","终端配置密码",
//                    "设备标识","MAC1","MAC2","MAC3",
//                    "GPON-SN","备注",
//                    "生产日期","导入日期","软件版本",
//                    "硬件版本","额定电压","额定电流",
//                    "外箱箱号","制造工艺单号","预留字段1","预留字段2","预留字段3","预留字段4","预留字段5"};
//            SXSSFWorkbook swb = new SXSSFWorkbook(ExcelCommonUtils.getXSSFWorkbook( "123", TITLE), 2);
//            String[] PROPS = { "contractNumber","frInsNO",
//                    "issueDate","billNumber","itemName",
//                    "itemNo","barCode","dsnNumber",
//                    "wirelessName1","wirelessPassword1","wirelessName2","wirelessPassword2",
//                    "accessAddress","userName","terminalPassword",
//                    "equipmentName","macAddress1","macAddress2","macAddress3",
//                    "itemRevision","remark",
//                    "produceDate","importDate","softVersion",
//                    "hardVersion","ratedVoltage","ratedCurrent",
//                    "buyBatchNo","craftsNo","preserved1","preserved2",
//                    "preserved3","preserved4","preserved5"};
//            SXSSFSheet sheet = swb.getSheetAt(Constant.INT_0);
//            ExcelCommonUtils.appendSXSSFWorkbook(sheet, oemProduceSkipGoodsQuerys, PROPS, Constant.INT_0);
//
//        }
//        catch (Exception e){}
//
//    }







}
