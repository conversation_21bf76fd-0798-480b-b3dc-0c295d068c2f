package com.zte.autoTest.unitTest;

import com.zte.application.impl.MtlRelatedItemsServiceImpl;
import com.zte.application.stepdt.impl.ApsItemOrgCivServiceImpl;
import com.zte.domain.model.MtlRelatedItemsRepository;
import com.zte.domain.model.stepdt.ApsItemOrgCivRepository;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.interfaces.stepdt.dto.ApsItemOrgCivDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/1
 */
public class MtlRelatedItemsServiceImplTest extends BaseTestCase {
    @InjectMocks
    private MtlRelatedItemsServiceImpl service;
    @Mock
    private MtlRelatedItemsRepository relatedItemsRepository;

    @Test
    public void getList()throws  Exception {
        MtlRelatedItemsEntityDTO dto=new MtlRelatedItemsEntityDTO();
        PowerMockito.when(relatedItemsRepository.getList(dto)).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getList(dto));
    }

    @Test
    public void getItemInfoList()throws  Exception {
        MtlRelatedItemsEntityDTO dto=new MtlRelatedItemsEntityDTO();
        PowerMockito.when(relatedItemsRepository.getItemInfoList(dto)).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getItemInfoList(dto));
    }
}
