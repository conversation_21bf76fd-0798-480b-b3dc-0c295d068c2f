package com.zte.autoTest.unitTest;


import com.zte.application.datawb.TaskToBeQueriedService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.impl.CompleteFeedbackDataLogServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.TaskToBeQueriedRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.zte.common.utils.NumConstant;
import com.zte.springbootframe.common.model.Page;
/**
 * <AUTHOR>
 * @date 2024/7/8
 */
public class CompleteFeedbackDataLogServiceImplTest  extends PowerBaseTestCase {
    @Mock
    private TaskToBeQueriedService taskToBeQueriedService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    @Mock
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadServiceImpl;

    @InjectMocks
    private CompleteFeedbackDataLogServiceImpl completeFeedbackDataLogService;

    @Mock
    private TaskToBeQueriedRepository taskToBeQueriedRepository;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;




    /* Started by AICoder, pid:ybef5h45f5ge25c1432508197005f45a6bf8a531 */
    @Test
    @PrepareForTest({HttpClientUtil.class,CenterfactoryRemoteService.class})
    public void uploadCompleteFeedbackData() throws Exception {

        String taskNo = "123456";
        UploadCompleteMachineDataDTO dto = new UploadCompleteMachineDataDTO();

        PowerMockito.mockStatic(HttpClientUtil.class, CenterfactoryRemoteService.class);
         List<SysLookupValues> sysLookupValuesList = new ArrayList();
        UploadCompleteMachineDataDTO uploadCompleteMachineDataDTO = new UploadCompleteMachineDataDTO();
        when(CenterfactoryRemoteService.getLookupValue(any())).thenReturn(sysLookupValuesList);
        List<EntityWeightDTO> list = new ArrayList<>();
        when(mesGetDictInforRepository.getDict(any())).thenReturn(list);

        List<TaskCompleteEntitiesDTO> taskCompleteEntitiesDTOList=new ArrayList<>();
        when(taskToBeQueriedRepository.queryTaskCompleteInformation(any())).thenReturn(taskCompleteEntitiesDTOList);
        List<SysLookupValues> lookupValuesList = new ArrayList<>();
        SysLookupValues dtSys = new SysLookupValues();
        dtSys.setLookupMeaning("789");
        lookupValuesList.add(dtSys);
        when(zmsDeviceInventoryUploadServiceImpl.getDataTransferBatchNo()).thenReturn(null);
        when(wsmAssembleLinesService.getSysLookupValues(any())).thenReturn(null);
       // when(completeFeedbackDataLogService.queryTaskCompleteInformationPage(any())).thenReturn(null);

        try {
            completeFeedbackDataLogService.uploadCompleteFeedbackData(uploadCompleteMachineDataDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        when(wsmAssembleLinesService.getSysLookupValues(any())).thenReturn(lookupValuesList);

        TaskCompleteEntitiesDTO taskCompleteEntitiesDTO=new TaskCompleteEntitiesDTO();
        taskCompleteEntitiesDTO.setUuid("123");
        taskCompleteEntitiesDTOList.add(taskCompleteEntitiesDTO);
        when(taskToBeQueriedRepository.queryTaskCompleteInformation(any())).thenReturn(taskCompleteEntitiesDTOList);
        EntityWeightDTO ewDTO = new EntityWeightDTO();
        ewDTO.setDescription("1111");
        list.add(ewDTO);
        when(mesGetDictInforRepository.getDict(any())).thenReturn(list);
        completeFeedbackDataLogService.uploadCompleteFeedbackData(uploadCompleteMachineDataDTO);

        for (int i = 0; i < 502; i++) {
            taskCompleteEntitiesDTO=new TaskCompleteEntitiesDTO();
            taskCompleteEntitiesDTO.setUuid("123");
            taskCompleteEntitiesDTOList.add(taskCompleteEntitiesDTO);
        }
        when(taskToBeQueriedRepository.queryTaskCompleteInformation(any())).thenReturn(taskCompleteEntitiesDTOList);
        completeFeedbackDataLogService.uploadCompleteFeedbackData(uploadCompleteMachineDataDTO);


        Assert.assertNotNull(uploadCompleteMachineDataDTO);
    }
    /* Ended by AICoder, pid:ybef5h45f5ge25c1432508197005f45a6bf8a531 */

    /* Started by AICoder, pid:933b56e04d7444298c5757b4585cf974 */
    @Test
    @PrepareForTest({HttpClientUtil.class,CenterfactoryRemoteService.class})
    public void pushDataToB2B() throws Exception {
        String taskNo = "123456";
        UploadCompleteMachineDataDTO dto =new UploadCompleteMachineDataDTO();
        dto.setEmpNo("123333");

        List<TaskCompleteEntitiesDTO> tempInsertList = Arrays.asList(new TaskCompleteEntitiesDTO());
        List<TaskCompleteEntitiesDTO> tInsertList =new ArrayList<>();
        completeFeedbackDataLogService.pushDataToB2B(tInsertList,dto,"22");

        List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList = new ArrayList<>();
        TaskCompleteEntitiesDTO completeMachineDataLogEntityDTO = new TaskCompleteEntitiesDTO();
        completeMachineDataLogEntityDTO.setUuid("123");
        tInsertList.add(completeMachineDataLogEntityDTO);


        completeFeedbackDataLogService.pushDataToB2B(tInsertList,dto,"22");
        completeFeedbackDataLogService.pushDataToB2B(tInsertList,dto,"");
        Assert.assertNotNull(completeMachineDataLogEntityDTOList);
    }
    /* Ended by AICoder, pid:933b56e04d7444298c5757b4585cf974 */
}
