package com.zte.autoTest.unitTest.kxbariii;

import com.zte.application.kxbariii.impl.BoardStoveMaintenanceServiceImpl;
import com.zte.domain.model.kxbariii.BoardStoveMaintenanceRepository;
import com.zte.interfaces.dto.kxbariii.BoardStoveMaintenanceDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @date 2023-02-07 10:23
 */
public class BoardStoveMaintenanceServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BoardStoveMaintenanceServiceImpl boardStoveMaintenanceServiceImpl;
    @Mock
    private BoardStoveMaintenanceRepository boardStoveMaintenanceRepository;

    @Before
    public void init(){

    }

    @Test
    public void getList() {
        Page<BoardStoveMaintenanceDTO> page = new Page<>();
        Page<BoardStoveMaintenanceDTO> pageInfo = boardStoveMaintenanceServiceImpl.getList(page);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
}
