package com.zte.autoTest.unitTest.kxbariii;

import com.zte.application.kxbariii.impl.ProdSmtWriteServiceImpl;
import com.zte.domain.model.kxbariii.ProdSmtWriteRepository;
import com.zte.interfaces.dto.kxbariii.ProdSmtWriteDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @date 2023-02-15 11:14
 */
@RunWith(PowerMockRunner.class)
public class ProdSmtWriteServiceImplTest {
    @InjectMocks
    private ProdSmtWriteServiceImpl prodSmtWriteServiceImpl;
    @Mock
    private ProdSmtWriteRepository prodSmtWriteRepository;
    @Before
    public void init(){

    }

    @Test
    public void pullPreBomSPMByPage() {
        Page<ProdSmtWriteDTO> page = new Page<>();
        Page<ProdSmtWriteDTO> pageInfo = prodSmtWriteServiceImpl.pullPreBomSPMByPage(page);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
}
