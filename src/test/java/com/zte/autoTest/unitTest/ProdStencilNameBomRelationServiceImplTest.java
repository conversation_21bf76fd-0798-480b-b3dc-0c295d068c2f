package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.ProdStencilnameBomRelationServiceImpl;
import com.zte.domain.model.datawb.ProdStencilnameBomRelationRepository;
import com.zte.interfaces.dto.SynchronizeStencilOfSpmDataDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName: ProdStencilNameBomRelationServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/3/1 上午11:18
 */
public class ProdStencilNameBomRelationServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ProdStencilnameBomRelationServiceImpl service;

    @Mock
    ProdStencilnameBomRelationRepository prodStencilnameBomRelationRepository;
    @Test
    public void pageSelectStencilOfSPMDataTest() throws Exception {
        SynchronizeStencilOfSpmDataDTO stencilOfSpmDataDTO = new SynchronizeStencilOfSpmDataDTO();
        try {
            service.pageSelectStencilOfSPMData(stencilOfSpmDataDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(RetCode.VALIDATIONERROR_MSGID));
        }
        stencilOfSpmDataDTO.setPage(1);
        stencilOfSpmDataDTO.setRow(100);
        service.pageSelectStencilOfSPMData(stencilOfSpmDataDTO);
    }

    @Test
    public void selectEffectiveStencilOfSPMDataTest() throws Exception {
        List<String> stencilNameList = new ArrayList<>();
        service.selectEffectiveStencilOfSPMData(stencilNameList);

        stencilNameList.add("test");
        Assert.assertNotNull(service.selectEffectiveStencilOfSPMData(stencilNameList));
    }
}
