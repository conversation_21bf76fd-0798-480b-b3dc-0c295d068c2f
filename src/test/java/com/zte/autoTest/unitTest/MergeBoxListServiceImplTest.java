package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MergeBoxListServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.MergeBoxListRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ParamterMergeDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class})
public class MergeBoxListServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    MergeBoxListServiceImpl mergeBoxListServiceImpl;
    @Mock
    MergeBoxListRepository mergeBoxListRepository;


    @Test
    public void selectMescaCpmBoxupBills() {
        try {
            PowerMockito.when(mergeBoxListRepository.selectMescaCpmBoxupBills(any())).thenReturn(any());
            ParamterMergeDTO params = new ParamterMergeDTO();
            List<String> boxList = new ArrayList<>();
            for (int i = 0; i < 300; i++) {
                boxList.add("1233333");
            }
            params.setBillsList(boxList);
            mergeBoxListServiceImpl.selectMescaCpmBoxupBills(params);
            PowerMockito.when(mergeBoxListRepository.selectMescaCpmBoxupBillsItems(any())).thenReturn(any());
            mergeBoxListServiceImpl.selectMescaCpmBoxupBillsItems(params);
            PowerMockito.when(mergeBoxListRepository.selectMescaCpmBoxupScanInfo(any())).thenReturn(any());
            mergeBoxListServiceImpl.selectMescaCpmBoxupScanInfo(params);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length()>0);
        }

    }

    @Test
    public void validateParam() {
        try {
            List<SysLookupValues> lookupValuesList = new ArrayList<>();
            SysLookupValues dtSys = new SysLookupValues();
            dtSys.setLookupMeaning("789");
            lookupValuesList.add(dtSys);
            ParamterMergeDTO params = new ParamterMergeDTO();
            List<String> boxList = new ArrayList<>();
            for (int i = 0; i < 300; i++) {
                boxList.add("1233333");
            }
            params.setBillsList(boxList);
            params.setPageSize((long) (-1));
            params.setPageSize((long) (-1));
            PowerMockito.mockStatic(CenterfactoryRemoteService.class);
            when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(lookupValuesList);
            mergeBoxListServiceImpl.validateParam(params, "789", "789");
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.EMPNO_VALIDATE, ex.getMessage());
        }
    }

    @Test
    public void validateQuality() {
        try {
            List<SysLookupValues> lookupValuesList = new ArrayList<>();
            SysLookupValues dtSys = new SysLookupValues();
            dtSys.setLookupMeaning("789");
            lookupValuesList.add(dtSys);
            ParamterMergeDTO params = new ParamterMergeDTO();
            List<String> boxList = new ArrayList<>();
            for (int i = 0; i < 300; i++) {
                boxList.add("1233333");
            }
            params.setBillsList(boxList);
            params.setPageSize((long) (-1));
            params.setPageSize((long) (-1));
            PowerMockito.mockStatic(CenterfactoryRemoteService.class);
            when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(lookupValuesList);
            mergeBoxListServiceImpl.validateQuality(params);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PAGE_NO_EMPTY_NO, ex.getMessage());

        }
    }


}