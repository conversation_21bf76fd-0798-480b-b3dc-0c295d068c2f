package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.PallectNoEveryBoxNoServiceImpl;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.domain.model.datawb.EdiSoSRepository;
import com.zte.domain.model.datawb.PallectNoEveryBoxNoServiceRepository;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.EdiSoSNewDTO;
import com.zte.interfaces.dto.PalletScanInCollectionDTO;
import com.zte.interfaces.dto.TranspotDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@PrepareForTest({MESHttpHelper.class, HttpClientUtil.class, JacksonJsonConverUtil.class, HttpRemoteService.class})
@RunWith(PowerMockRunner.class)
public class pallectNoEveryBoxNoImplTest extends BaseTestCase {

    @InjectMocks
    private PallectNoEveryBoxNoServiceImpl pallectNoEveryBoxNoServiceImpl;
    @Mock
    PallectNoEveryBoxNoServiceRepository pallectNoEveryBoxNoServiceRepository;

    @Test
    public void PallectNoEveryBoxNoServiceImpl() throws Exception {
        List<PalletScanInCollectionDTO> dtoList = new ArrayList<>();
        for (int i = 0; i < 100; i++
        ) {
            PalletScanInCollectionDTO dt1 = new PalletScanInCollectionDTO();
            dt1.setPallet("1233");
            dt1.setBoxNo("123");
            dtoList.add(dt1);
        }
        PalletScanInCollectionDTO palletScanInCollectionDTO = new PalletScanInCollectionDTO();
        PowerMockito.when(pallectNoEveryBoxNoServiceRepository.pallectGetCollection(Mockito.any())).thenReturn(dtoList);
        pallectNoEveryBoxNoServiceImpl.pallectGetCollection(palletScanInCollectionDTO);
        PowerMockito.when(pallectNoEveryBoxNoServiceRepository.pallectGetCollection(Mockito.any())).thenReturn(null);
        Assert.assertNull(pallectNoEveryBoxNoServiceImpl.pallectGetCollection(palletScanInCollectionDTO));
    }

    @Test
    public void validateParamater() throws Exception {
        try {
            PalletScanInCollectionDTO params = new PalletScanInCollectionDTO();
            List<String> dtCollection = new ArrayList<>();
            for (int i = 0; i < 90; i++) {
                dtCollection.add("12333");
            }
            params.setOutboundBoxNos(dtCollection);
            pallectNoEveryBoxNoServiceImpl.validateParamater(params);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test
    public void getEntityMax(){
        List<TranspotDTO> dtolist =new ArrayList<>();
        PowerMockito.when(pallectNoEveryBoxNoServiceRepository.getEntityMax(Mockito.any())).thenReturn(dtolist);
        Assert.assertNotNull(pallectNoEveryBoxNoServiceImpl.getEntityMax(new ArrayList<>()));
    }

}
