package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BoardPredictOutServiceImpl;
import com.zte.application.datawb.impl.BoqBomBoxPrintInfoServiceImpl;
import com.zte.domain.model.datawb.BoardPredictOutRepository;
import com.zte.domain.model.datawb.BoardPredictoutInfo;
import com.zte.domain.model.datawb.BoqBomBoxPrintInfoRepository;
import com.zte.util.BaseTestCase;
import org.apache.ibatis.annotations.Many;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2023/2/10
 */
@RunWith(PowerMockRunner.class)
public class BoardPredictOutServiceImplTest extends BaseTestCase {

    @InjectMocks
    BoardPredictOutServiceImpl boardPredictOutService;

    @Mock
    BoardPredictOutRepository boardPredictOutRepository;

    @Test
    public void selectProdPlanImesInfoByProdPlanIdList() throws Exception {
        List<String> prodplanIdList = new ArrayList<>();
        prodplanIdList.add("2");
        boardPredictOutService.selectProdPlanImesInfoByProdPlanIdList(new ArrayList<>());
        List<BoardPredictoutInfo> boardPredictoutInfoList = new ArrayList<>();
        boardPredictoutInfoList.add(new BoardPredictoutInfo());
        PowerMockito.when(boardPredictOutRepository.selectProdPlanImesByProdPlanIdList(any())).thenReturn(boardPredictoutInfoList);
        Assert.assertNotNull(boardPredictOutService.selectProdPlanImesInfoByProdPlanIdList(prodplanIdList));
    }

}
