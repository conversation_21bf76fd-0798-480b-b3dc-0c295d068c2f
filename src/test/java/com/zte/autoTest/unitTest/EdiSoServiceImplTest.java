package com.zte.autoTest.unitTest;

import com.zte.application.infordt.impl.EdiSoServiceImpl;
import com.zte.domain.model.datawb.EdiSoSRepository;
import com.zte.domain.model.infordt.EdiSoDTO;
import com.zte.domain.model.infordt.EdiSoRepository;
import com.zte.interfaces.dto.EdiSoSNewDTO;
import com.zte.interfaces.infordt.dto.EdiSosStorerDTO;
import com.zte.interfaces.infordt.dto.ReelIdAndGoodDieInfoDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/31
 **/
public class EdiSoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private EdiSoServiceImpl ediSoService;
    @Mock
    private EdiSoRepository ediSoRepository;

    @Mock
    private EdiSoSRepository ediSoSRepository;
    /* Started by AICoder, pid:ace5ci7edbl67ba14fb20a9ba09d8a827d57b856 */
    @Test
    public void queryListByCondition() {
        EdiSoDTO dto = new EdiSoDTO();
        dto.setExternorderkey("123");
        dto.setExternalorderkey2("123");
        dto.setHref51("123");
        dto.setExternalorderkey2List(new LinkedList<String>(){{add("123");}});
        dto.setHref51List(new LinkedList<String>(){{add("124");}});
        List<EdiSoDTO> list = new ArrayList<>();
        list.add(dto);
        PowerMockito.when(ediSoRepository.queryListByCondition(Mockito.any())).thenReturn(list);
        ediSoService.queryListByCondition(dto);

        dto.setExternalorderkey2List(null);
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));

        dto.setExternorderkey("");
        PowerMockito.when(ediSoRepository.queryListByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));

        dto.setExternorderkey("123");
        dto.setExternalorderkey2("");
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));
        dto.setExternalorderkey2("123");
        dto.setHref51("");
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));
        dto.setHref51("123");
        dto.setExternalorderkey2List(new ArrayList<>());
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));
        dto.setExternalorderkey2List(new LinkedList<String>(){{add("123");}});
        dto.setHref51List(new LinkedList<String>(){{add("124");}});
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));

        dto.setExternorderkey("");
        dto.setExternalorderkey2("");
        dto.setHref51("");
        dto.setExternalorderkey2List(new LinkedList<String>(){{add("123");}});
        dto.setHref51List(new LinkedList<String>(){{add("124");}});
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));
        dto.setHref51("111");
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));
        dto.setHref51List(null);
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));

        dto.setExternalorderkey2List(new LinkedList<String>(){});
        dto.setHref51List(new LinkedList<String>(){{add("124");}});
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));

        dto.setExternorderkey("");
        dto.setExternalorderkey2("");
        dto.setHref51("");
        dto.setExternalorderkey2List(new ArrayList<>());
        dto.setHref51List(new ArrayList<>());
        Assert.assertNotNull(ediSoService.queryListByCondition(dto));
    }

    @Test
    public void queryExtendkey2ByTaskNoTest(){
        EdiSoDTO dto = new EdiSoDTO();
        dto.setTaskNo("11");
        Assert.assertNotNull(ediSoService.queryExtendkey2ByTaskNo(dto));
        dto.setTaskNo("");
        Assert.assertNotNull(ediSoService.queryExtendkey2ByTaskNo(dto));
    }
    /* Ended by AICoder, pid:ace5ci7edbl67ba14fb20a9ba09d8a827d57b856 */

    @Test
    public void queryListPageByCondition() {
        EdiSoDTO dto = new EdiSoDTO();
        dto.setExternorderkey("123");
        Assert.assertNotNull(ediSoService.queryListPageByCondition(dto));
    }

    @Test
    public void queryExtendkey2ByTaskNo() {
        EdiSoDTO ediSoDTO  = new EdiSoDTO();
        ediSoDTO.setTaskNo("123");
        Assert.assertNotNull(ediSoService.queryExtendkey2ByTaskNo(ediSoDTO));
    }


    @Test
    public void queryEdiSoSInformation() {
        Assert.assertNotNull(ediSoService.queryEdiSoSInformation(new EdiSoSNewDTO()));
    }

    /**
     * 查询发料数据 EdiPcbSerialOut
     *
     * @return
     */
    @Test
    public void queryEdiPcbSerialOut() {
        Assert.assertNotNull(ediSoService.queryEdiPcbSerialOut(new EdiSoSNewDTO()));
    }
    @Test
    public void queryEdiSosStorerStatus() {
        PowerMockito.when(ediSoSRepository.queryEdiSosStorerStatus(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO()));

        PowerMockito.when(ediSoSRepository.queryEdiSosStorerStatus(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO(){{setExternorderkey("test");}}));
        Assert.assertNotNull(ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO(){{setExternorderkey("test");setCompany("装焊配送");}}));
        Assert.assertNotNull(ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO(){{setExternorderkey("test");setCompany("成型");}}));
        List<EdiSosStorerDTO> ediSosStorerDTOList = new ArrayList<>();
        EdiSosStorerDTO ediSosStorerDTO = new EdiSosStorerDTO();

        PowerMockito.when(ediSoSRepository.queryEdiSosStorerStatus(Mockito.any())).thenReturn(ediSosStorerDTOList);
        PowerMockito.when(ediSoSRepository.queryTolocForAI(Mockito.any())).thenReturn(ediSosStorerDTOList);
        ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO(){{setExternorderkey("test");setCompany("成型");}});
        ediSosStorerDTOList.add(ediSosStorerDTO);
        ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO(){{setExternorderkey("test");setCompany("成型");}});
        ediSosStorerDTO.setToloc("sz");
        ediSoService.queryEdiSosStorerStatus(new EdiSosStorerDTO(){{setExternorderkey("test");setCompany("成型");}});
    }

    @Test
    public void getReelIdAndGoodDieQtyInfoTest() {
        List<ReelIdAndGoodDieInfoDTO> a = new ArrayList<>();
        a.add(new ReelIdAndGoodDieInfoDTO());
        PowerMockito.when(ediSoSRepository.getReelIdAndGoodDieQtyInfo(Mockito.anyString())).thenReturn(a);
        List<ReelIdAndGoodDieInfoDTO> test = ediSoService.getReelIdAndGoodDieQtyInfo("test");
        Assert.assertTrue(test.size() == 1);
    }
}
