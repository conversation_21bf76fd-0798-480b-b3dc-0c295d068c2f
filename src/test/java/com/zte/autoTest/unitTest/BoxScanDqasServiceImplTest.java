package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.BoxScanDqasServiceImpl;
import com.zte.application.datawb.impl.ConfigMaterialBaindDqasNewServiceGetProductImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewRepository;
import com.zte.interfaces.dto.BoxScanCheckInDTO;
import com.zte.interfaces.dto.ConfigMaterialDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class BoxScanDqasServiceImplTest{
    @InjectMocks
    BoxScanDqasServiceImpl boxScanDqasService;
    @Mock
    ConfigMaterialBaindDqasNewServiceGetProductImpl configMaterialBaindDqasNewGetProductService ;
    @Mock
    ConfigMaterialBaindDqasNewRepository configMaterialBaindDqasNewRepository;


    @Test
    public void boxScanDqas() throws Exception {
        try {

            /**
             *  <AUTHOR>
             *  0 入参校验
             *  @params dto 入参实体类
             */
            PowerMockito.mockStatic(CommonUtils.class);
            PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            BoxScanCheckInDTO params =new BoxScanCheckInDTO();
            boxScanDqasService.checkDqas(null);
            boxScanDqasService.paramInCheck( params);//以下都是判空测试的
            params.setItemBarcode("33078998003Z");
            params.setItemNo("124");
            params.setBillNo("124");
            params.setEntityNo("123");
            params.setContractNo("123");
            boxScanDqasService.paramInCheck( params);
            params.setUpdateBy("");
            boxScanDqasService.paramInCheck( params);
            params.setUpdateBy("123");
            params.setItemBarcode("");
            boxScanDqasService.paramInCheck( params);
            params.setItemBarcode("123");
            params.setItemNo("");
            boxScanDqasService.paramInCheck( params);
            params.setItemNo("123");
            params.setBillNo("");
            boxScanDqasService.paramInCheck( params);
            params.setBillNo("123");
            params.setEntityNo("");
            boxScanDqasService.paramInCheck( params);
            params.setEntityNo("1213");
            params.setContractNo("");
            boxScanDqasService.paramInCheck( params);
            params.setContractNo("");
            params.setItemNo("124,12,123");
            boxScanDqasService.paramInCheck( params);
            params.setItemNo("12");
            boxScanDqasService.paramInCheck( params);//ok 的
            boxScanDqasService.setOutDto(false,"123","String msg","q3er");




            Map<String, Object> inMap=new HashMap<>();
            List<ConfigMaterialDTO> list =new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO=new ConfigMaterialDTO();
            configMaterialDTO.setQty((long) 0);
            list.add(configMaterialDTO);
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list);
            boxScanDqasService.subItemDQAS(inMap);

            List<ConfigMaterialDTO> list2 =new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO2=new ConfigMaterialDTO();
            configMaterialDTO2.setQty((long) 1);
            list2.add(configMaterialDTO2);
            PowerMockito.when(configMaterialBaindDqasNewRepository.checkPrefix(anyMap())).thenReturn(list2);
            boxScanDqasService.subItemDQAS(inMap);



            List<ConfigMaterialDTO> dicList=new ArrayList<>();
            ConfigMaterialDTO configMaterialDTO3=new ConfigMaterialDTO();
            configMaterialDTO3.setSectionName("");
            configMaterialDTO3.setMeaning("123");
            dicList.add(configMaterialDTO3);
            boxScanDqasService.getMeaningToUrl(dicList);

            configMaterialDTO3.setSectionName("123");
            boxScanDqasService.getProductPlaceName("123",dicList);

            boxScanDqasService.getDQASConfigAsamAddr(null,null);
            boxScanDqasService.getDQASConfigAsamAddr(dicList,null);
            boxScanDqasService.getDQASConfigAsamAddr(dicList,params);


            ConfigMaterialDTO configMaterialDTO33=new ConfigMaterialDTO();
            dicList.add(configMaterialDTO33);

            List<ConfigMaterialDTO> productDtList =new ArrayList<>();
            configMaterialDTO33.setSectionName("123");
            configMaterialDTO33.setMeaning("123");

            PowerMockito.when(configMaterialBaindDqasNewGetProductService.getProductPlaceNameByEntityId(anyString())).thenReturn(productDtList);
            boxScanDqasService.getDQASConfigAsamAddr(dicList,params);

            configMaterialDTO33.setProductPlaceName("123456");
            productDtList.add(configMaterialDTO33);
            PowerMockito.when(configMaterialBaindDqasNewGetProductService.getProductPlaceNameByEntityId(anyString())).thenReturn(productDtList);
            boxScanDqasService.getDQASConfigAsamAddr(dicList,params);

            configMaterialDTO33.setProductPlaceName("123");
            productDtList.add(configMaterialDTO33);
            PowerMockito.when(configMaterialBaindDqasNewGetProductService.getProductPlaceNameByEntityId(anyString())).thenReturn(productDtList);
            boxScanDqasService.getDQASConfigAsamAddr(dicList,params);



            boxScanDqasService.getWLControlResult(params,"123");
            boxScanDqasService.dqasWebservice (null,"123") ;
            boxScanDqasService.getWLControlResult(params,"http://10.7.68.85/website/MESService.asmx");
            boxScanDqasService.getIntResult("", "123", "1","2") ;




            params.setItemBarcode("33078998003Z");
            params.setItemNo("124");
            params.setBillNo("124");
            params.setEntityNo("123");
            params.setContractNo("123");
            params.setUpdateBy("123");
            List<ConfigMaterialDTO> configMaterialDTOList=new ArrayList<>();
            PowerMockito.when(configMaterialBaindDqasNewRepository.getDeptByEntityNo(anyString())).thenReturn(configMaterialDTOList);
            boxScanDqasService.checkDqas(params);

            ConfigMaterialDTO c1=new ConfigMaterialDTO();
            c1.setAttribute("123");
            configMaterialDTOList.add(c1);
            PowerMockito.when(configMaterialBaindDqasNewRepository.getDeptByEntityNo(anyString())).thenReturn(configMaterialDTOList);
            boxScanDqasService.checkDqas(params);


            Map<String, Object> mapDic = new HashMap<>();
            mapDic.put("biCode","3030071");
            mapDic.put("cCode","12");

            List<ConfigMaterialDTO> dicList2=new ArrayList<>();
            ConfigMaterialDTO c3=new ConfigMaterialDTO();
            c3.setMeaning("123");
            dicList2.add(c3);
            PowerMockito.when(configMaterialBaindDqasNewRepository.getDQASConfigAsamAddr(mapDic)).thenReturn(dicList2);
            boxScanDqasService.checkDqas(params);

            boxScanDqasService.setDqasParam(params);

        }
        catch (Exception e)
        {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }



}
