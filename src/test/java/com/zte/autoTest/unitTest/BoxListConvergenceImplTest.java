package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BoxListConvergenceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BoxListConvergenceRepository;
import com.zte.interfaces.dto.TaskBoxListArrayDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/10 23
 * @description:
 */
@RunWith(PowerMockRunner.class)
public class BoxListConvergenceImplTest {
    @InjectMocks
    private BoxListConvergenceImpl boxListConvergenceImpl;
    @Mock
    private BoxListConvergenceRepository boxListConvergenceRepository;

    @Test
    public void getBoxBoxNo() {
        List<TaskBoxListArrayDTO> dtc=new ArrayList<>();
        TaskBoxListArrayDTO dtx=new TaskBoxListArrayDTO();
        dtx.setBillNumber("B201136396806");
        dtc.add(dtx);
        PowerMockito.when(boxListConvergenceRepository.getBoxListArray("1233")).thenReturn(dtc);
        Assert.assertFalse(boxListConvergenceImpl.getBoxBoxNo("B201136396806"));

    }
    @Test
    public void inserKaKaInformation()
    {
        try {
            TaskBoxListArrayDTO taskBoxListArrayDTONew = new TaskBoxListArrayDTO();
            taskBoxListArrayDTONew.setKaFkaStatus(Constant.KAFKA_STATUS);
            taskBoxListArrayDTONew.setBillNumber("1233");
            taskBoxListArrayDTONew.setMd5(Constant.MD5_STATUS);
            BigDecimal mathDigtal = new BigDecimal(Constant.MATH_DIGITAL);
            taskBoxListArrayDTONew.setCreatedBy(mathDigtal);
            taskBoxListArrayDTONew.setCreatedName(Constant.CREATE_NAME);
            BigDecimal kafkaDigtal = new BigDecimal(Constant.KAFKA_DIGTAL);
            taskBoxListArrayDTONew.setKfkCompensateFlag(kafkaDigtal);
            boxListConvergenceImpl.inserKaKaInformation("1233","456");
        }
        catch (Exception e)
        {
            Assert.assertTrue(e.getMessage().length()>0);
        }


    }
}
