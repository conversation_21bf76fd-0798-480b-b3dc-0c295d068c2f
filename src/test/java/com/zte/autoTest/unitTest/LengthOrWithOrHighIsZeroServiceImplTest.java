package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.LengthOrWithOrHighIsZeroServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.EntryBoxAppliedRepository;
import com.zte.interfaces.dto.EntityWeightDTO;
import com.zte.interfaces.dto.LengthOrWithOrHighIsZeroDTO;
import com.zte.springbootframe.util.EmailUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
@PowerMockIgnore("javax.net.ssl.*")
public class LengthOrWithOrHighIsZeroServiceImplTest {

    @InjectMocks
    private LengthOrWithOrHighIsZeroServiceImpl lengthOrWithOrHighIsZeroService;

    @Mock
    EntryBoxAppliedRepository entryBoxAppliedRepository;

    @Mock
    MesGetDictInforRepository mesGetDictInforRepository;

    @Mock
    private EmailUtils emailUtils;

    @Test
    public void handleBusinessTest(){
        //PowerMockito.mockStatic(CommonUtils.class);
        List<LengthOrWithOrHighIsZeroDTO> list = new ArrayList<>();
        StringBuilder mailTo = new StringBuilder();
        try{
            lengthOrWithOrHighIsZeroService.handleBusiness();
        }catch(Exception ex){
            Assert.assertTrue(ex.getMessage().length()>0);
        }
        try{
            PowerMockito.when(entryBoxAppliedRepository.getLengthOrWithOrHighIsZero()).thenReturn(list);
            lengthOrWithOrHighIsZeroService.handleBusiness();
        }catch(Exception ex){
            Assert.assertTrue(ex.getMessage().length()>0);
        }
        try{
            LengthOrWithOrHighIsZeroDTO model= new  LengthOrWithOrHighIsZeroDTO();
            model.setEntityName("renwu12333");
            model.setBillNumber("C123456");
            list.add(model);
            PowerMockito.when(entryBoxAppliedRepository.getLengthOrWithOrHighIsZero()).thenReturn(list);
            PowerMockito.when(mesGetDictInforRepository.getDict(Constant.LOOKUP_VALUE_8006038)).thenReturn(null);
            lengthOrWithOrHighIsZeroService.handleBusiness();
        }catch (Exception ex){
            Assert.assertTrue(ex.getMessage().length()>0);
        }
        try{
            LengthOrWithOrHighIsZeroDTO model= new  LengthOrWithOrHighIsZeroDTO();
            model.setEntityName("renwu12333");
            model.setBillNumber("C123456");
            list.add(model);
            PowerMockito.when(entryBoxAppliedRepository.getLengthOrWithOrHighIsZero()).thenReturn(list);

            List<EntityWeightDTO> list1 =  new ArrayList<>();
            EntityWeightDTO item =new EntityWeightDTO();
            item.setDescription("<EMAIL>");
            list1.add(item);
            PowerMockito.when(mesGetDictInforRepository.getDict(Constant.LOOKUP_VALUE_8006038)).thenReturn(list1);
            lengthOrWithOrHighIsZeroService.handleBusiness();
        }catch (Exception ex){
            Assert.assertTrue(ex.getMessage().length()>0);
        }
        try{
            LengthOrWithOrHighIsZeroDTO model= new  LengthOrWithOrHighIsZeroDTO();
            model.setEntityName("renwu12333");
            model.setBillNumber("C123456");
            model.setOrg("635");
            model.setHigh(null);
            model.setCubage(null);
            model.setLength(null);
            model.setWidth(null);
            model.setGrossWeight(null);
            model.setNetWeight(null);
            list.add(model);

            model= new  LengthOrWithOrHighIsZeroDTO();
            model.setEntityName("renwu12333");
            model.setBillNumber("C123456");
            model.setOrg("635");
            model.setHigh("111");
            model.setCubage("222");
            model.setLength("333");
            model.setWidth("444");
            model.setGrossWeight("555");
            model.setNetWeight("666");
            list.add(model);
            lengthOrWithOrHighIsZeroService.getEmailContent(list);
        }catch (Exception ex){
            Assert.assertTrue(ex.getMessage().length()>0);
        }

    }
}
