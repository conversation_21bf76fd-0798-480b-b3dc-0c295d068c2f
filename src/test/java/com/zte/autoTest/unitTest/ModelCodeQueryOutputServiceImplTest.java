package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.impl.ModelCodeQueryOutputServiceImpl;
import com.zte.interfaces.dto.ModelCodeQueryInputDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import static org.mockito.Matchers.any;

public class ModelCodeQueryOutputServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ModelCodeQueryOutputServiceImpl service;

    @Mock
    private CfgCodeRuleItemService cfgCodeRuleItemService;

    @Test
    public void checkInputParam() {
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(any())).thenReturn("1");
        Assert.assertNotNull(service.checkInputParam(new ModelCodeQueryInputDTO(){{
            setBillNumber(Lists.newArrayList("1"));
        }}));
    }
}