package com.zte.autoTest.unitTest.api;

import com.zte.application.datawb.WmesWerpConEntityTraceService;
import com.zte.application.impl.api.ExternalInterfacesServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.api.ExternalInterfacesRepository;
import com.zte.domain.model.datawb.WmesWerpConEntityTrace;
import com.zte.interfaces.dto.api.ContractAndTaskDataDTO;
import com.zte.interfaces.dto.api.PackingDataDTO;
import com.zte.interfaces.dto.api.PackingDetailsDTO;
import com.zte.interfaces.dto.api.PalletDataDTO;
import com.zte.interfaces.dto.api.PickListQueryDTO;
import com.zte.interfaces.dto.api.PickListResultDTO;
import com.zte.interfaces.dto.api.ProdPickListMainDTO;
import com.zte.interfaces.dto.api.QueryParamBO;
import com.zte.interfaces.dto.api.SiteInfoDTO;
import com.zte.interfaces.dto.wmes.ProcPicklistDetail;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;

/**
 * <AUTHOR>
 * @date 2024-03-12 9:28
 */
public class ExternalInterfacesServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ExternalInterfacesServiceImpl externalInterfacesService;
    @Mock
    private ExternalInterfacesRepository externalInterfacesRepository;
    @Mock
    private WmesWerpConEntityTraceService wmesWerpConEntityTraceService;

    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;

    @Before
    public void init() {

    }

    /* Started by AICoder, pid:13b3e78ca73b419ba7515d4dbb189b8c */
    @Test
    public void queryPalletData() {

        String pageSize ="500";
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn(pageSize);

        QueryParamBO queryParamBO = new QueryParamBO();
        queryParamBO.setCurrentPage(-1);
        queryParamBO.setPageSize(-1);
        try {
            externalInterfacesService.queryPalletData(queryParamBO);
        }catch (Exception e){
            assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(1000);
        try {
            externalInterfacesService.queryPalletData(queryParamBO);
        }catch (Exception e){
            assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(10);
        queryParamBO.setPalletNoList(null);
        queryParamBO.setBillNumberList(null);
        PageRows<PalletDataDTO> ff = externalInterfacesService.queryPalletData(queryParamBO);
        assertEquals(ff.getRows(),null);

        List<String> palletNo = new ArrayList<>();
        palletNo.add("33");
        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(10);
        queryParamBO.setPalletNoList(palletNo);
        queryParamBO.setBillNumberList(null);
        List<PalletDataDTO> palletDataList = new LinkedList<>();
        PalletDataDTO a2 = new PalletDataDTO();
        a2.setPalletNo("123");
        palletDataList.add(a2);
        PalletDataDTO a1 = new PalletDataDTO();
        a1.setCubage("3");
        a1.setStrBills("222");
        palletDataList.add(a1);
        PowerMockito.when(externalInterfacesRepository.queryPalletData(Mockito.any())).thenReturn(palletDataList);
        PowerMockito.when(externalInterfacesRepository.queryPalletDataTotal(Mockito.any())).thenReturn(0l);
        List<PalletDataDTO> palletDataList1 = new LinkedList<>();
        a2 = new PalletDataDTO();
        a2.setPalletNo("123");
        palletDataList1.add(a2);
        PowerMockito.when(externalInterfacesRepository.batchQueryStockName(anyList()))
               .thenReturn(palletDataList1);

        ff = externalInterfacesService.queryPalletData(queryParamBO);
        assertEquals(ff.getTotal(),0);
    }/* Ended by AICoder, pid:13b3e78ca73b419ba7515d4dbb189b8c */

    /* Started by AICoder, pid:9b5b09f9746648caaaaf9b028e75c604 */
    @Test
    public void queryPackingListDetails() {
        String pageSize ="500";
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn(pageSize);

        QueryParamBO queryParamBO = new QueryParamBO();
        queryParamBO.setCurrentPage(-1);
        queryParamBO.setPageSize(-1);
        try {
            externalInterfacesService.queryPackingListDetails(queryParamBO);
        }catch (Exception e){
            assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(1000);
        try {
            externalInterfacesService.queryPackingListDetails(queryParamBO);
        }catch (Exception e){
            assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(10);
        queryParamBO.setBillNumberList(null);
        PageRows<PackingDetailsDTO> ff = externalInterfacesService.queryPackingListDetails(queryParamBO);
        assertEquals(ff.getRows(),null);

        List<String> palletNo = new ArrayList<>();
        palletNo.add("33");
        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(10);
        queryParamBO.setBillNumberList(palletNo);

        PowerMockito.when(externalInterfacesRepository.queryPackingListDetails(Mockito.any())).thenReturn(null);
        PowerMockito.when(externalInterfacesRepository.queryPackingListDetailsTotal(Mockito.any())).thenReturn(0l);
        ff = externalInterfacesService.queryPackingListDetails(queryParamBO);
        assertEquals(ff.getTotal(),0);

    }/* Ended by AICoder, pid:9b5b09f9746648caaaaf9b028e75c604 */

    /* Started by AICoder, pid:6e39d219a27645c398b5ebbbb01298ad */
    @Test
    public void queryPackingData() {

        String pageSize ="500";
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn(pageSize);

        QueryParamBO queryParamBO = new QueryParamBO();
        queryParamBO.setCurrentPage(-1);
        queryParamBO.setPageSize(-1);
        try {
            externalInterfacesService.queryPackingData(queryParamBO);
        }catch (Exception e){
            assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(1000);
        try {
            externalInterfacesService.queryPackingData(queryParamBO);
        }catch (Exception e){
            assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        queryParamBO = new QueryParamBO();
        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(100);
        PageRows<PackingDataDTO> list = externalInterfacesService.queryPackingData(queryParamBO);
        assertTrue(CollectionUtils.isEmpty(list.getRows()));

        List<SiteInfoDTO> entitySiteList = new LinkedList<>();
        SiteInfoDTO aa1 = new SiteInfoDTO();
        entitySiteList.add(aa1);
        queryParamBO.setEntitySiteList(entitySiteList);
        externalInterfacesService.queryPackingData(queryParamBO);

        queryParamBO.setBillNumberList(Arrays.asList("123"));
        externalInterfacesService.queryPackingData(queryParamBO);

        queryParamBO.setPalletNoList(Arrays.asList("22"));
        externalInterfacesService.queryPackingData(queryParamBO);

        List<String> entityNameList = new LinkedList<>();
        entityNameList.add("123");
        queryParamBO.setEntityNameList(entityNameList);
        externalInterfacesService.queryPackingData(queryParamBO);

        List<PackingDataDTO> packingList = new LinkedList<>();
        PackingDataDTO b1 = new PackingDataDTO();
        b1.setCceEntityId("123");
        b1.setBillId("123");
        b1.setProductLabelName("123");
        packingList.add(b1);
        PackingDataDTO b2 = new PackingDataDTO();
        b2.setBillId("234");
        b2.setProductLabelName("234");
        packingList.add(b2);
        PackingDataDTO b3 = new PackingDataDTO();
        b3.setBillId("234");
        b3.setProductLabelName("234");
        b3.setMainBillNumberL("11,22,33");
        packingList.add(b3);
        PackingDataDTO b4 = new PackingDataDTO();
        b4.setBillId("234");
        b4.setProductLabelName("234");
        b4.setMergeBillNumberL("11,22,33");
        packingList.add(b4);
        PowerMockito.when(externalInterfacesRepository.queryPackingData(Mockito.any())).thenReturn(packingList);
        PowerMockito.when(externalInterfacesRepository.queryPackingDataTotal(Mockito.any())).thenReturn(0l);
        PowerMockito.when(wmesWerpConEntityTraceService.batchQueryStatusName(Mockito.any()))
                .thenReturn(null);
        PageRows<PackingDataDTO> ff = externalInterfacesService.queryPackingData(queryParamBO);
        assertEquals(ff.getTotal(),0);

        List<WmesWerpConEntityTrace> traceList = new LinkedList<>();
        WmesWerpConEntityTrace c1 = new WmesWerpConEntityTrace();
        c1.setEntityId(new BigDecimal("123"));
        c1.setStatusName(">>>>");
        traceList.add(c1);
        //PowerMockito.when(wmesWerpConEntityTraceService.batchQueryStatusName(Mockito.anyList()))
        //        .thenReturn(traceList);
        ff = externalInterfacesService.queryPackingData(queryParamBO);
        assertEquals(ff.getTotal(),0);

        //PowerMockito.when(externalInterfacesRepository.batchQueryProductLabelName(Mockito.any()))
        //        .thenReturn(packingList);
        b4.setProductLabelName("234");
        ff = externalInterfacesService.queryPackingData(queryParamBO);
        assertEquals(ff.getTotal(),0);

        b4.setPalletNo("123");
        List<PalletDataDTO> palletDataList = new LinkedList<>();
        PalletDataDTO a1 = new PalletDataDTO();
        PalletDataDTO a2 = new PalletDataDTO();
        a2.setPalletNo("123");
        palletDataList.add(a1);
        palletDataList.add(a2);
        a1.setPalletNo("234");
        //PowerMockito.when(externalInterfacesRepository.batchQueryStockName(Mockito.anyList()))
        //        .thenReturn(palletDataList);
        ff = externalInterfacesService.queryPackingData(queryParamBO);
        assertEquals(ff.getTotal(),0);

        b1.setMergeFlag("Y");
        b4.setVirtualFlag("Y");
        ff = externalInterfacesService.queryPackingData(queryParamBO);
        assertEquals(ff.getTotal(),0);

        //PowerMockito.when(externalInterfacesRepository.queryMergeBillNumberListByIds(Mockito.any()))
        //        .thenReturn(packingList);
        //PowerMockito.when(externalInterfacesRepository.queryMainBillNumberListByIds(Mockito.any()))
        //        .thenReturn(packingList);
        ff = externalInterfacesService.queryPackingData(queryParamBO);
        assertEquals(ff.getTotal(),0);
    }/* Ended by AICoder, pid:6e39d219a27645c398b5ebbbb01298ad */

    @Test
    public void queryContractAndTaskData() throws Exception {
        /* Started by AICoder, pid:cfc3ed1ded9443cf8b5c54a28264e9d9 */
        QueryParamBO queryParamBO = new QueryParamBO();

        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn("500");
        try {
            Whitebox.invokeMethod(externalInterfacesService, "queryContractAndTaskData", queryParamBO);
        } catch (Exception e) {
            assertEquals(MessageId.CONTRACT_AND_TASK_IS_NULL, e.getMessage());
        }


        List<String> contracts = new ArrayList<>();
        contracts.add("S3NJ2014110101LTD");
        queryParamBO.setContracts(contracts);
        queryParamBO.setCurrentPage(0);
        queryParamBO.setPageSize(0);
        List<ContractAndTaskDataDTO> contractAndTaskDataDTOList = new ArrayList<>();
        PowerMockito.when(externalInterfacesRepository.queryContractAndTaskData(Mockito.any())).thenReturn(contractAndTaskDataDTOList);
        try {
            Whitebox.invokeMethod(externalInterfacesService, "queryContractAndTaskData", queryParamBO);
        }catch (Exception e){
            assertEquals("Pagesize.between.0.and.500", e.getMessage());
        }

        queryParamBO.setPageSize(500000);
        ContractAndTaskDataDTO contractAndTaskDataDTO = new ContractAndTaskDataDTO();
        contractAndTaskDataDTO.setContractNo("34235423");
        contractAndTaskDataDTO.setEntityId("35423523");
        contractAndTaskDataDTOList.add(contractAndTaskDataDTO);


        queryParamBO.setCurrentPage(0);
        for (int i = 0; i < 501; i++) {
            contractAndTaskDataDTO = new ContractAndTaskDataDTO();
            contractAndTaskDataDTO.setContractNo("34235423");
            contractAndTaskDataDTO.setEntityId("35423523"+ String.valueOf(i));
            contractAndTaskDataDTOList.add(contractAndTaskDataDTO);
        }

        PowerMockito.when(externalInterfacesRepository.queryContractAndTaskData(Mockito.any())).thenReturn(contractAndTaskDataDTOList);
        List<SiteInfoDTO> siteInfoDTOList = new ArrayList<>();
        PowerMockito.when(externalInterfacesRepository.querySiteInfoByEntityId(Mockito.any())).thenReturn(siteInfoDTOList);
        try {
            Whitebox.invokeMethod(externalInterfacesService, "queryContractAndTaskData", queryParamBO);
        }catch (Exception e){
            assertEquals("Pagesize.between.0.and.500", e.getMessage());
        }
        queryParamBO.setCurrentPage(1);
        queryParamBO.setPageSize(100);
        SiteInfoDTO siteInfoDTO = new SiteInfoDTO();
        siteInfoDTO.setEntityId("35423523");
        siteInfoDTOList.add(siteInfoDTO);

        PowerMockito.when(externalInterfacesRepository.queryContractAndTaskData(Mockito.any())).thenReturn(contractAndTaskDataDTOList);
        PowerMockito.when(externalInterfacesRepository.querySiteInfoByEntityId(Mockito.any())).thenReturn(siteInfoDTOList);
        Whitebox.invokeMethod(externalInterfacesService, "queryContractAndTaskData", queryParamBO);

        assertTrue(Objects.nonNull(siteInfoDTO));
        /* Ended by AICoder, pid:cfc3ed1ded9443cf8b5c54a28264e9d9 */
    }


    /* Started by AICoder, pid:0543bx0b68n25b714d230adb3090121e1b359567 */


    @Test
    public void queryMaterialOrderNoByTaskNo_ShouldReturnEmptyListWhenNoData() {
        // 模拟空结果
        PowerMockito.when(externalInterfacesRepository.queryMaterialOrderNoByTaskNo(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        List<PickListResultDTO> results = externalInterfacesService.queryMaterialOrderNoByTaskNo(Arrays.asList("TASK999"));

        // 验证结果
        assertTrue(results.isEmpty());
    }

    @Test
    public  void queryMaterialOrderNoByTaskNo_ShouldReturnEmptyListWhenInputIsEmpty() {
        List<PickListResultDTO> results = externalInterfacesService.queryMaterialOrderNoByTaskNo(Collections.emptyList());
        assertTrue(results.isEmpty());
    }

    @Test
    public void queryPickListByTaskNos() {
        PickListQueryDTO queryDTO= new PickListQueryDTO();
        queryDTO.setTaskNos(Collections.emptyList());
        queryDTO.setStartDate(null);
        queryDTO.setEndDate(null);
        List<ProdPickListMainDTO> result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertTrue(result.isEmpty());

        queryDTO.setStartDate(null);
        queryDTO.setEndDate("2025-01-01");
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertTrue(result.isEmpty());

        queryDTO.setStartDate("2025-01-01");
        queryDTO.setEndDate(null);
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertTrue(result.isEmpty());

        List<ProdPickListMainDTO> prodPickListMainDTOList = Arrays.asList(new ProdPickListMainDTO());
        PowerMockito.when(externalInterfacesRepository.queryPickListByTaskNos(Mockito.any())).thenReturn(prodPickListMainDTOList);
        queryDTO.setStartDate("2025-01-01");
        queryDTO.setEndDate("2025-01-02");
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertFalse(result.isEmpty());

        queryDTO.setTaskNos(Arrays.asList("taskNo"));
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertFalse(result.isEmpty());

        queryDTO.setStartDate(null);
        queryDTO.setEndDate(null);
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertFalse(result.isEmpty());

        queryDTO.setStartDate(null);
        queryDTO.setEndDate("2025-01-01");
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertFalse(result.isEmpty());

        queryDTO.setStartDate("2025-01-01");
        queryDTO.setEndDate(null);
        result = externalInterfacesService.queryPickListByTaskNos(queryDTO);
        assertFalse(result.isEmpty());
    }

    @Test
    public void queryPickListCondition(){
        PickListQueryDTO dto = new PickListQueryDTO();
        List<ProdPickListMainDTO> list = externalInterfacesService.queryPickListCondition(dto);
        Assert.assertTrue(CollectionUtils.isEmpty(list));

        dto.setBillNumber("123");
        externalInterfacesService.queryPickListCondition(dto);

        dto.setBillNumber(null);
        dto.setStartDate("2024");
        externalInterfacesService.queryPickListCondition(dto);

        dto.setEndDate("122");
        externalInterfacesService.queryPickListCondition(dto);
    }

    @Test
    public void queryProcPickDetailBatch() {
        List<String> billNumberList = new LinkedList<>();
        billNumberList.add("123");
        List<ProcPicklistDetail> list = externalInterfacesService.queryProcPickDetailBatch(billNumberList);
        Assert.assertTrue(CollectionUtils.isEmpty(list));

        List<ProcPicklistDetail> procPicklistDetails = new LinkedList<>();
        ProcPicklistDetail a1 = new ProcPicklistDetail();
        procPicklistDetails.add(a1);
        PowerMockito.when(externalInterfacesRepository.queryProcPickDetailBatch(Mockito.anyList()))
                .thenReturn(procPicklistDetails);
        externalInterfacesService.queryProcPickDetailBatch(billNumberList);
    }

/* Ended by AICoder, pid:0543bx0b68n25b714d230adb3090121e1b359567 */
}
