package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.SpmComDailyreportServiceImpl;
import com.zte.domain.model.datawb.SpmComDailyreportRepository;
import com.zte.interfaces.dto.SpmComDailyreportDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;


@RunWith(PowerMockRunner.class)
public class SpmComDailyreportServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SpmComDailyreportServiceImpl service;

    @Mock
    private SpmComDailyreportRepository SpmComDailyreportRepository;

    @Test
    public void insertSpmComDailyreport() throws Exception {
        List<SpmComDailyreportDTO> dtos = new ArrayList<>();
        SpmComDailyreportDTO dto = new SpmComDailyreportDTO();
        dtos.add(dto);
        dto.setFactoryId("111");
        PowerMockito.when(SpmComDailyreportRepository.deleteByFactoryIdDate(Mockito.any()))
                .thenReturn(1);
        PowerMockito.when(SpmComDailyreportRepository.insertBatch(Mockito.any()))
                .thenReturn(1);
        service.insertBatch(dtos, true);
        Assert.assertNotNull(service.insertBatch(dtos, false));
    }
}