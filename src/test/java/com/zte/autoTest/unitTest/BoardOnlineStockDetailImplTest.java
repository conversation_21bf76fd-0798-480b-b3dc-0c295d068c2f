package com.zte.autoTest.unitTest;

import com.zte.application.BaImuService;
import com.zte.application.datawb.impl.BoardFirstwarehouseServiceImpl;
import com.zte.application.datawb.impl.BoardOnlineStockDetailImpl;
import com.zte.domain.model.BoardFirstwarehouseRepository;
import com.zte.domain.model.datawb.BoardOnlineStockDetailRepository;
import com.zte.interfaces.dto.BaImuDTO;
import com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
@RunWith(PowerMockRunner.class)
public class BoardOnlineStockDetailImplTest {
    @InjectMocks
    private BoardOnlineStockDetailImpl boardOnlineStockDetail;

    @Mock
    private BoardOnlineStockDetailRepository boardOnlineStockDetailRepository;
    @Mock
    private BaImuService baImuService;

    @Test
    @PrepareForTest({})
    public void synchronizeIMUData() throws Exception {

        List<BoardInstructionCycleDataCreateDTO> tempList = new ArrayList<>();
        BoardInstructionCycleDataCreateDTO dto = new BoardInstructionCycleDataCreateDTO();
        tempList.add(dto);
        List<BaImuDTO> baImuDTOList = new ArrayList<>();
        PowerMockito.when(boardOnlineStockDetailRepository.getBaImuPageList(Mockito.any())).thenReturn(baImuDTOList);

        Assert.assertNotNull(boardOnlineStockDetail.synchronizeIMUData());
    }
}
