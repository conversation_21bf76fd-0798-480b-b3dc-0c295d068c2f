package com.zte.autoTest.unitTest;

import com.zte.application.erpdt.impl.ZteWipMoveMtlTxnServiceImpl;
import com.zte.domain.model.MtlSystemItemsBRepository;
import com.zte.domain.model.erpdt.ZteMrpWipIssueRepository;
import com.zte.domain.model.erpdt.ZteWipMoveMtlTxnRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import com.zte.domain.model.WarehouseEntryInfo;

import java.math.BigDecimal;


@RunWith(PowerMockRunner.class)
public class ZteWipMoveMtlTxnServiceImplNewTest {

    @InjectMocks
    private ZteWipMoveMtlTxnServiceImpl zteWipMoveMtlTxnServiceImpl;

    @Mock
    private ZteWipMoveMtlTxnRepository zteWipMoveMtlTxnRepository;

    @Mock
    private ZteMrpWipIssueRepository zteMrpWipIssueRepository;

    @Mock
    private MtlSystemItemsBRepository mtlSystemItemsBRepository;

    @Test
    public void writeErpIdempotent()throws Exception{
        WarehouseEntryInfo warehouseEntryInfo=new WarehouseEntryInfo();
        warehouseEntryInfo.setErpId("123");
        warehouseEntryInfo.setItemNo("12312131313131313");
        warehouseEntryInfo.setCreateBy("10261");
        warehouseEntryInfo.setBillNo("123");
        BigDecimal itemId=new BigDecimal(12);
        PowerMockito.when(mtlSystemItemsBRepository.getItemIdBySegment1(Mockito.any()))
                .thenReturn(itemId);

        long countAll=0;
        PowerMockito.when(zteWipMoveMtlTxnRepository.getZteWipMoveTxnAndBakCount(Mockito.any()))
                .thenReturn(countAll);
        PowerMockito.when(zteMrpWipIssueRepository.getZteMrpWipIssueAndBakCount(Mockito.any()))
                .thenReturn(countAll);
        zteWipMoveMtlTxnServiceImpl.writeErpIdempotent(warehouseEntryInfo);
        Assert.assertNotNull(warehouseEntryInfo);
    }
}
