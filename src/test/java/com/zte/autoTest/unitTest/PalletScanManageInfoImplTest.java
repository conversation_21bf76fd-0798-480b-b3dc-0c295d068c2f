package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.PalletScanManageInfoImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.PalletScanManageInfoRepository;
import com.zte.interfaces.dto.PalletScanManageInfoParamterDTO;
import com.zte.interfaces.dto.PalletScanManageInfoResultDTO;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

public class PalletScanManageInfoImplTest extends PowerBaseTestCase {

    /**
     * mock方法写法 10292636 彭国
     */
    @InjectMocks
    private PalletScanManageInfoImpl palletScanManageInfoImpl;

    @Mock
    private PalletScanManageInfoRepository palletScanManageInfoRepository;


    @Mock
    private   LocaleMessageSourceBean  localeMessageSourceBean;

    @Test
    public void mainValidateMethod() throws Exception {
        try {
            //覆盖1
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoImpl.isListEmpty(palletScanManageInfoParamterDTO);
            palletScanManageInfoParamterDTO.setHeight(10D);
            palletScanManageInfoParamterDTO.setLastUpdateBy("10000838");
            palletScanManageInfoParamterDTO.setWeight(23D);
            palletScanManageInfoParamterDTO.setPalletNo("TP1506M000001");


            palletScanManageInfoImpl.mainValidateMethod(palletScanManageInfoParamterDTO);
        }catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void setMessage() throws Exception {
      try
      {
          PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
          long c=3;
          Long KL=new Long(3);
          palletScanManageInfoParamterDTO.setLastUpdateBy("10000838");
          PowerMockito.when(palletScanManageInfoRepository.selectUserId("12323")).thenReturn((long)3);

          palletScanManageInfoImpl.setMessage(palletScanManageInfoParamterDTO);
      }
      catch (Exception e)
      {
          Assert.assertEquals(e.getMessage(), e.getMessage());
      }
    }
    @Test
    public void valiateUsrId ()
    {
        try {
            Long KL = new Long(3);
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();

            palletScanManageInfoImpl.valiateUsrId(KL, 1, palletScanManageInfoParamterDTO);
        }
        catch (Exception ex)
        {
              try
              {
                  Long KL = new Long(3);
                  PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();

                  palletScanManageInfoImpl.valiateUsrId(KL, 2, palletScanManageInfoParamterDTO);
              }
              catch (Exception exc)
              {
                  Assert.assertEquals(MessageId.PALLET_IS_NOT_EMPTY, exc.getMessage());

              }
            Assert.assertEquals(ex.getMessage(), ex.getMessage());

        }

    }
    @Test
    public void getMessageHeight ()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setHeight(0D);
            palletScanManageInfoImpl.getMessageWeight(palletScanManageInfoParamterDTO);
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test
    public void getMessageHeight1()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setHeight(3000D);
            palletScanManageInfoImpl.getMessageHeight(palletScanManageInfoParamterDTO);
            palletScanManageInfoParamterDTO.setHeight(30001D);
            palletScanManageInfoImpl.getMessageHeight(palletScanManageInfoParamterDTO);
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test
    public void getMessageHeight2()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setHeight(300.45678);
            palletScanManageInfoImpl.getMessageHeight(palletScanManageInfoParamterDTO);

        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test
    public void getMessageWeight()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setWeight(0D);
            palletScanManageInfoImpl.getMessageWeight(palletScanManageInfoParamterDTO);
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }
    @Test
    public void getMessageWeight1()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setWeight(10000D);
            palletScanManageInfoImpl.getMessageWeight(palletScanManageInfoParamterDTO);
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void getMessageWeight2()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setWeight(99.789);
            palletScanManageInfoImpl.getMessageWeight(palletScanManageInfoParamterDTO);
        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test
    public void getMessageWeight3()
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setWeight(300D);
            palletScanManageInfoImpl.getMessageWeight(palletScanManageInfoParamterDTO);
        }
        catch (Exception e)
        {
            Assert.assertEquals(MessageId.ONLY_ONE_DECIMAL_PLACES_ARE_ALLOWED_FOR, e.getMessage());
        }
    }
    @Test
    public void getMessageGrossWeight() {
        try
        {
        PalletScanManageInfoResultDTO palletScanManageInfoResultDTO = new PalletScanManageInfoResultDTO();
        PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
        palletScanManageInfoParamterDTO.setWeight(23D);
        Long ty = new Long(23);
        palletScanManageInfoResultDTO.setBoxWeight(ty);
        List<PalletScanManageInfoResultDTO> dotlist = new ArrayList<>();
        dotlist.add(palletScanManageInfoResultDTO);
        palletScanManageInfoImpl.getMessageGrossWeight(dotlist, palletScanManageInfoParamterDTO);
            palletScanManageInfoParamterDTO.setWeight(1000D);
            palletScanManageInfoImpl.getMessageGrossWeight(dotlist, palletScanManageInfoParamterDTO);
    }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }


    }
    @Test
    public void  getMessagePallet ( )
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoImpl.getMessagePallet(palletScanManageInfoParamterDTO);

        }
        catch (Exception e)
        {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }
    @Test
    public void  splitPalletMessage ( )
    {
        try {
            PalletScanManageInfoResultDTO palletScanManageInfoResultDTO = new PalletScanManageInfoResultDTO();
            PalletScanManageInfoParamterDTO palletScanManageInfoParamterDTO = new PalletScanManageInfoParamterDTO();
            palletScanManageInfoParamterDTO.setWeight(23D);
            Long ty = new Long(23);
            palletScanManageInfoResultDTO.setBoxWeight(ty);
            List<PalletScanManageInfoResultDTO> dotlist = new ArrayList<>();
            dotlist.add(palletScanManageInfoResultDTO);
            palletScanManageInfoImpl.splitPalletMessage(palletScanManageInfoParamterDTO,dotlist);
        }
        catch (Exception e)
        {
            Assert.assertEquals(null,e.getMessage());
        }
    }
    @Test
    public void  insertSuccess ( )
    {
        try
        {
            PalletScanManageInfoParamterDTO palletScanManageInfoParamter =new PalletScanManageInfoParamterDTO();
            Long ty = new Long(23);
            PowerMockito.when(palletScanManageInfoRepository.updatePalletScan(Mockito.any())).thenReturn(ty)
            ;
            palletScanManageInfoImpl.insertSuccess(palletScanManageInfoParamter);
        }
        catch (Exception e)
        {
            try {
                PalletScanManageInfoParamterDTO palletScanManageInfoParamter = new PalletScanManageInfoParamterDTO();
                Long ty = new Long(-1);
                PowerMockito.when(palletScanManageInfoRepository.updatePalletScan(Mockito.any())).thenReturn(ty)
                ;
                palletScanManageInfoImpl.insertSuccess(palletScanManageInfoParamter);
            }
            catch (Exception ex)
            {
                Assert.assertEquals(e.getMessage(), e.getMessage());
            }
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

}
