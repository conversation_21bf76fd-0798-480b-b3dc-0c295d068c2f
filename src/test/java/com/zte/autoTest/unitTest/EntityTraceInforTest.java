package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.datawb.impl.EntityTraceInforServiceImpl;
import com.zte.application.datawb.impl.EntityWeightEstimateServiceImpl;
import com.zte.application.impl.MesGetDictInforServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.EntityTraceInforRepository;
import com.zte.domain.model.datawb.EntityWeightEstimateRepository;
import com.zte.interfaces.dto.*;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})

public class EntityTraceInforTest extends BaseTestCase {
    @InjectMocks
    MesGetDictInforServiceImpl mesGetDictInforService;
    @InjectMocks
    EntityTraceInforServiceImpl entityTraceInforService;

    @Mock
    MesGetDictInforServiceImpl mesGetDictInforService2;
    @Mock
    EntityTraceInforServiceImpl entityTraceInforService2;
    @Mock
    EntityTraceInforRepository entityTraceInforRepository;


    @Test
    public void coverityEntityTraceInfor767() throws Exception {
        try{
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            EntityTraceInforOutDTO entityTraceInforOutDTO = new EntityTraceInforOutDTO();
            ErrDTO ee = new ErrDTO();
            ee.setProcessStatus("S");
            entityTraceInforOutDTO.setErrDTO(ee);
            params.setStatusName("1,2,3,4,5");
            PowerMockito.when(entityTraceInforService.checkParams(params, false)).thenReturn(null);

            PowerMockito.when(entityTraceInforRepository.getCount(params)).thenReturn(1);
            entityTraceInforService.getList(params);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void coverityEntityTraceInfor76() throws Exception {

        try{
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            EntityTraceInforOutDTO entityTraceInforOutDTO = new EntityTraceInforOutDTO();
            ErrDTO ee = new ErrDTO();
            ee.setProcessStatus("S");
            entityTraceInforOutDTO.setErrDTO(ee);
            params.setStatusName("1,2,3,4,5");


            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            params.setEntityName("123");
            params.setCurrentPage(1);
            params.setPageSize(2);
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            PowerMockito.when(entityTraceInforService.checkParams(params, false)).thenReturn(null);

            PowerMockito.when(entityTraceInforRepository.getList(params)).thenReturn(null);
            entityTraceInforService.getList(params);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }


    }


    @Test
    public void coverityEntityTraceInfor67() throws Exception {
        try{
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            EntityTraceInforOutDTO entityTraceInforOutDTO = new EntityTraceInforOutDTO();
            ErrDTO ee = new ErrDTO();
            ee.setProcessStatus("S");
            entityTraceInforOutDTO.setErrDTO(ee);
            params.setStatusName("1,2,3,4,5");
            PowerMockito.when(entityTraceInforService.checkParams(params, false)).thenReturn(null);

            PowerMockito.when(entityTraceInforRepository.getCount(params)).thenReturn(1);
            entityTraceInforService.getCount(params);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }


    }

    @Test
    public void coverityEntityTraceInfor6() throws Exception {
        try{
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            EntityTraceInforOutDTO entityTraceInforOutDTO = new EntityTraceInforOutDTO();
            ErrDTO ee = new ErrDTO();
            ee.setProcessStatus("S");
            entityTraceInforOutDTO.setErrDTO(ee);
            params.setStatusName("1,2,3,4,5");


            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            params.setEntityName("123");
            params.setCurrentPage(1);
            params.setPageSize(-2);
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            PowerMockito.when(entityTraceInforService.checkParams(params, false)).thenReturn(null);

            PowerMockito.when(entityTraceInforRepository.getCount(params)).thenReturn(1);
            entityTraceInforService.getCount(params);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }


    }

    @Test
    public void coverityEntityTraceInfor5() throws Exception {
        try{
            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            params.setEntityName("123");
            params.setCurrentPage(1);
            params.setPageSize(300);
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            entityTraceInforService.checkParams(params, true);
        }
        catch (Exception e){
            Assert.assertNull(e.getMessage());
        }


    }

    @Test
    public void coverityEntityTraceInfor4() throws Exception {
        try{
            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            params.setEntityName("123");
            params.setCurrentPage(1);
            params.setPageSize(-2);
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            entityTraceInforService.checkParams(params, true);

        }
        catch (Exception e){
            Assert.assertNull(e.getMessage());
        }




    }

    @Test
    public void coverityEntityTraceInfor3() throws Exception {
        try{
            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            params.setEntityName("123");
            params.setCurrentPage(1);
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            entityTraceInforService.checkParams(params, true);
        }
        catch (Exception e){
            Assert.assertNull(e.getMessage());
        }





    }

    @Test
    public void coverityEntityTraceInfor2() throws Exception {
        try {


            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }
            params.setEntityName("123");
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            entityTraceInforService.checkParams(params, true);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }


    @Test
    public void coverityEntityTraceInfor() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");


            Date d1 = new Date();
            Date d2 = new Date();
            entityTraceInforService.differentDaysByMillisecond(d1, d2);


            entityTraceInforService.setOutDto(0, null, "123", "S");
            entityTraceInforService.setOutDto(0, null, "123", "E");


            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("800033000001");
            e1.setLookupCode("1");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("800033000002");
            e2.setLookupCode("80");
            list.add(e2);
            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }

            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            PowerMockito.when(mesGetDictInforService2.getDict("12")).thenReturn(outMap);
            entityTraceInforService.getDic();


            EntityTraceInforInDTO params = new EntityTraceInforInDTO();
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(null);
            entityTraceInforService.checkParams(params, true);


            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            entityTraceInforService.checkParams(params, true);

            params.setStatusName("124");
            params.setPlanningGroupDesc("123");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String time = "2016-03-12 10:40:16";
            Date date = simpleDateFormat.parse(time);

            String time2 = "2016-05-12 10:40:16";
            Date date2 = simpleDateFormat.parse(time2);
            params.setS20DateEnd(date);
            params.setS20DateStart(date2);
            PowerMockito.when(entityTraceInforService.getDic()).thenReturn(outMap);
            entityTraceInforService.checkParams(params, true);

        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

    }


}
