package com.zte.autoTest.unitTest;


import com.zte.application.datawb.EntityWorkHourService;
import com.zte.application.datawb.impl.EntityWorkHourQueryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.EntityWorkHourQueryRepository;
import com.zte.domain.model.datawb.EntityWorkHourRepository;
import com.zte.interfaces.dto.EntityWorkHourInDTO;
import com.zte.interfaces.dto.EntityWorkHourOutDTO;
import com.zte.interfaces.dto.ProcessDTO;
import com.zte.interfaces.dto.WorkHourDTO;
import com.zte.interfaces.dto.WorkHourQueryDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class EntityWorkHourQueryServiceImplTest extends BaseTestCase {
    @InjectMocks
    EntityWorkHourQueryServiceImpl entityWorkHourQueryService;
    @Mock
    EntityWorkHourService entityWorkHourService;
    @Mock
    EntityWorkHourQueryRepository entityWorkHourQueryRepository;
    @Mock
    EntityWorkHourRepository entityWorkHourRepository;
    @InjectMocks
    EntityWorkHourQueryServiceImpl entityWorkHourQueryServiceImpl;

    @Test
    public void entityWorkHourQuery() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            EntityWorkHourInDTO params = new EntityWorkHourInDTO();
            entityWorkHourQueryService.entityWorkHourQuery(null);
            params.setEntityName("");
            params.setCreator("1298");
            params.setOrganizationId(635);
            entityWorkHourQueryService.paramInCheck(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            params.setEntityName(null);
            entityWorkHourQueryService.paramInCheck(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            params.setEntityName("123");
            params.setCreator("");
            entityWorkHourQueryService.paramInCheck(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            params.setCreator(null);
            entityWorkHourQueryService.paramInCheck(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            params.setCreator("1298");
            entityWorkHourQueryService.paramInCheck(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            Long entityId = 123L;
            PowerMockito.when(entityWorkHourQueryRepository.getEntityId(anyObject())).thenReturn(entityId);
            entityWorkHourQueryService.validationEntity(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            Long entityId2 = null;
            PowerMockito.when(entityWorkHourQueryRepository.getEntityId(anyObject())).thenReturn(entityId2);
            PowerMockito.when(entityWorkHourQueryRepository.getEntityId2(anyObject())).thenReturn(entityId);
            entityWorkHourQueryService.validationEntity(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);
            PowerMockito.when(entityWorkHourQueryRepository.getEntityId(anyObject())).thenReturn(entityId2);
            PowerMockito.when(entityWorkHourQueryRepository.getEntityId2(anyObject())).thenReturn(entityId2);
            entityWorkHourQueryService.validationEntity(params);
            entityWorkHourQueryService.entityWorkHourQuery(params);

            ArrayList<WorkHourDTO> inList = new ArrayList<>();
            WorkHourDTO w = new WorkHourDTO();
            w.setItemCode("11");
            WorkHourDTO w2 = new WorkHourDTO();
            w2.setItemCode("22");
            inList.add(w);
            inList.add(w2);
            ArrayList<WorkHourDTO> workHourDTOArrayList2 = new ArrayList<>();
            workHourDTOArrayList2.add(w);
            WorkHourDTO w3 = new WorkHourDTO();
            w3.setItemCode("112");
            workHourDTOArrayList2.add(w3);
            PowerMockito.when(entityWorkHourQueryRepository.getMatchMaterial(anyObject())).thenReturn(workHourDTOArrayList2);
            entityWorkHourQueryService.removeMaterial(inList);

            WorkHourDTO workHourDTO = new WorkHourDTO();
            workHourDTO.setProductSType("11");
            workHourDTO.setProductSType("22");
            Map<String, Object> maps = new HashMap<>();
            entityWorkHourQueryService.setProductSType(null, maps);
            entityWorkHourQueryService.setProductSType(workHourDTO, maps);

            entityWorkHourQueryService.setProductType(null, maps);
            entityWorkHourQueryService.setProductType(workHourDTO, maps);

            PowerMockito.when(entityWorkHourService.standardWorkHour2(anyObject())).thenReturn(null);
            PowerMockito.when(entityWorkHourQueryRepository.getNewMaterialInfo(anyObject())).thenReturn(null);
            ArrayList<String> workHourDTOArrayList4 = new ArrayList<>();
            workHourDTOArrayList4.add("123");

            ArrayList<WorkHourQueryDTO> standardWorkHourList5 = new ArrayList<>();
            EntityWorkHourOutDTO result = new EntityWorkHourOutDTO();
            entityWorkHourQueryService.allNoneMatch2(0, params, workHourDTOArrayList4, standardWorkHourList5, result);
            //上面封装好的函数，就可以正常调用进子嵌套函数了。
            entityWorkHourQueryService.allNoneMatch(params, null, workHourDTOArrayList4, standardWorkHourList5, result);

            ArrayList<WorkHourDTO> standardWorkHourList = new ArrayList<>();
            WorkHourDTO w4 = new WorkHourDTO();
            w4.setItemCode("123");
            w4.setProcess("123");
            BigDecimal dtx = new BigDecimal(1);
            w4.setWorkHour(dtx);
            standardWorkHourList.add(w4);

            ArrayList<WorkHourDTO> workHourDTOArrayList3 = new ArrayList<>();
            WorkHourDTO w5 = new WorkHourDTO();
            w5.setItemCode("123");
            w5.setNumber(1);
            workHourDTOArrayList3.add(w5);

            ArrayList<WorkHourQueryDTO> standardWorkHourList3 = new ArrayList<>();
            ArrayList<ProcessDTO> standardWorkHourList4 = new ArrayList<>();
//            entityWorkHourQueryService.allMatch(standardWorkHourList, workHourDTOArrayList3, workHourDTOArrayList4, standardWorkHourList3, standardWorkHourList4, result);

            workHourDTOArrayList4.add("12");
            workHourDTOArrayList4.add("13");
//            entityWorkHourQueryService.halfMath(params, standardWorkHourList, workHourDTOArrayList3, workHourDTOArrayList4, standardWorkHourList3, standardWorkHourList4, standardWorkHourList5, result);


        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(MessageId.THE_RESULT_IS_EMPTY, e.getMessage());
        }
    }

    @Test
    public void getDistinctListData() {
        try {
            Map<String, Object> inMap = new HashMap<>();
            inMap.put("entityId", 12345);
            inMap.put("sqlWhere", "");
            inMap.put("orderBy", "");
            inMap.put("mark", 1);//到了这里，Mark为1 or 2
            PowerMockito.when(entityWorkHourQueryRepository.getMaterialInfo(inMap));
            entityWorkHourQueryServiceImpl.getDistinctListData(inMap);

            inMap.put("entityId", 12345);
            inMap.put("sqlWhere", "");
            inMap.put("orderBy", "");
            inMap.put("mark", 2);//到了这里，Mark为1 or 2
            List<WorkHourDTO> standDtoList = new ArrayList<>();
            WorkHourDTO dt1 = new WorkHourDTO();
            dt1.setEntityType("godd");
            entityWorkHourQueryServiceImpl.getDistinctListData(inMap);
            PowerMockito.when(entityWorkHourQueryRepository.getStandartHours(anyObject())).thenReturn(null);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);

        }
    }
    @Test
    public void getMatch(){
        EntityWorkHourInDTO parmams =new EntityWorkHourInDTO() ;
        parmams.setCreator("1233");
        parmams.setEntityName("45678");
        parmams.setOrganizationId(1234);
        WorkHourDTO t1=new WorkHourDTO();
        t1.setItemCode("123");
        t1.setWorkHour(new BigDecimal(7));
        t1.setProductType("整机装配");
        ArrayList<WorkHourDTO> standardWorkHourList =new ArrayList<>();


        WorkHourDTO t2=new WorkHourDTO();
        t2.setItemCode("1234");
        t2.setNumber(7);
        WorkHourDTO t3=new WorkHourDTO();
        t3.setItemCode("123");
        t3.setNumber(7);
        ArrayList<WorkHourDTO> workHourDTOArrayList3=new ArrayList<>();
        workHourDTOArrayList3.add(t2);
        workHourDTOArrayList3.add(t3);
        standardWorkHourList.add(t1);
        ArrayList<WorkHourQueryDTO> standardWorkHourList3 =new ArrayList<>();
        ArrayList<ProcessDTO> standardWorkHourList4=new ArrayList<>();
        EntityWorkHourOutDTO result=new EntityWorkHourOutDTO();
        try {
            entityWorkHourQueryServiceImpl.getMatch(parmams, standardWorkHourList, workHourDTOArrayList3, result);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);

        }

    }

}
