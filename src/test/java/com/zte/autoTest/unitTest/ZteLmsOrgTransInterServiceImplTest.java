package com.zte.autoTest.unitTest;

import com.zte.application.erpdt.impl.ZteLmsOrgTransInterServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.erpdt.ZteLmsOrgTransInter;
import com.zte.domain.model.erpdt.ZteLmsOrgTransInterRepository;
import com.zte.interfaces.assembler.ZteLmsOrgTransInterAssembler;
import com.zte.interfaces.dto.ZteLmsOrgTransInterDTO;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

import static org.mockito.Mockito.*;

/**
 * 指令基础信息测试类
 * <AUTHOR> 袁海洋
 */
public class ZteLmsOrgTransInterServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ZteLmsOrgTransInterServiceImpl zteLmsOrgTransInterServiceImpl;
    @Mock
    private ZteLmsOrgTransInterRepository zteLmsOrgTransInterRepository;

    private RetCode retCode;

    private ServiceData serviceData;

    @Test
    public void setZteLmsOrgTransInterRepository() throws Exception {
        zteLmsOrgTransInterServiceImpl.setZteLmsOrgTransInterRepository(zteLmsOrgTransInterRepository);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void insertZteLmsOrgTransInterBatch() throws Exception {
        List<ZteLmsOrgTransInterDTO> list = new ArrayList<>();
        int insertNum = 0;
        Assert.assertNotNull(zteLmsOrgTransInterServiceImpl.insertZteLmsOrgTransInterBatch(list));
        doReturn(insertNum).when(zteLmsOrgTransInterRepository).insertZteLmsOrgTransInterBatch(list);
    }

    /**
     * erp子库存转移单元测试
     * <AUTHOR> 陈昭君
     */
    @Test
    @PrepareForTest({ServiceData.class, RetCode.class, ZteLmsOrgTransInterAssembler.class, StringHelper.class, ZteLmsOrgTransInterServiceImpl.class, Constant.class})
    public void getSubInventoryTransferList() throws Exception {
        ZteLmsOrgTransInterServiceImpl serivce = PowerMockito.spy(new ZteLmsOrgTransInterServiceImpl());
        serivce.setZteLmsOrgTransInterRepository(zteLmsOrgTransInterRepository);
        serviceData=PowerMockito.mock(ServiceData.class);
        retCode=PowerMockito.mock(RetCode.class);
        PowerMockito.mockStatic(StringHelper.class);
        PowerMockito.mockStatic(Constant.class);
        ZteLmsOrgTransInterDTO conditions = new ZteLmsOrgTransInterDTO();
        conditions.setInventoryItem("6464613464feerghgeew");
        PowerMockito.when(StringHelper.isEmpty(anyObject())).thenReturn(false);
        PowerMockito.whenNew(ServiceData.class).withAnyArguments().thenReturn(serviceData);
        PowerMockito.whenNew(RetCode.class).withArguments(anyString(),anyString()).thenReturn(retCode);
        PowerMockito.when(StringHelper.isEmpty(anyObject())).thenReturn(false);
        doReturn("0000").when(retCode).getCode();
        List<ZteLmsOrgTransInter> wipmove = new ArrayList<ZteLmsOrgTransInter>();
        PowerMockito.when(zteLmsOrgTransInterRepository.getSubInventoryTransferList(anyObject())).thenReturn(wipmove);
        List<ZteLmsOrgTransInterDTO> wip = new ArrayList<ZteLmsOrgTransInterDTO>();
        PowerMockito.mockStatic(ZteLmsOrgTransInterAssembler.class);
        PowerMockito.when(ZteLmsOrgTransInterAssembler.toZteLmsOrgTransInterDTOList(wipmove)).thenReturn(wip);

        conditions.setPage(1);
        conditions.setRows(10);
        Assert.assertNotNull(serivce.getSubInventoryTransferList(conditions));
    }
}
