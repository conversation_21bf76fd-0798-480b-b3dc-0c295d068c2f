package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BoqBomPrintInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.BoqBomPrintInfoRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BoqBomPrintInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    BoqBomPrintInfoServiceImpl boqBomPrintInfoService;

    @Mock
    BoqBomPrintInfoRepository boqBomPrintInfoRepository;

    @Test
    public void boqBomBoxPrint() throws Exception {
        try {
            boqBomPrintInfoService.checkScanFlag("1");
            PowerMockito.when(boqBomPrintInfoRepository.checkScanFlag(anyObject())).thenReturn(null);

        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


}
