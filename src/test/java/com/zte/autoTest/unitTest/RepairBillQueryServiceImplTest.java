package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.BoqBomBoxPrintInfoServiceImpl;
import com.zte.application.datawb.impl.RepairBillQueryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BoqBomBoxPrintInfoRepository;
import com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewRepository;
import com.zte.domain.model.datawb.RepairBillQueryRepository;
import com.zte.interfaces.dto.*;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class RepairBillQueryServiceImplTest extends BaseTestCase{
    @InjectMocks
    RepairBillQueryServiceImpl repairBillQueryService;

    @Mock
    RepairBillQueryRepository repairBillQueryRepository;

    @Test
    public void repairBillQuery() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");


            RepairBillInDTO params=new RepairBillInDTO();
            params.setItemCode(null);
            params.setPage(1);
            params.setRows(1);
            params.setStartRow(1);
            params.setEndRow(2);
            repairBillQueryService.paramInCheck( params);

            List<String> stringList = Arrays.asList("a ", " b", "  c  ");
            params.setItemCode(stringList);
            repairBillQueryService.paramInCheck( params);

            params.setPage(null);
            repairBillQueryService.paramInCheck( params);

            params.setPage(1);
            params.setRows(null);
            repairBillQueryService.paramInCheck( params);

            params.setRows(1);
            params.setPage(-1);
            repairBillQueryService.paramInCheck( params);
            params.setPage(0);
            repairBillQueryService.paramInCheck( params);

            params.setPage(1);
            params.setRows(-1);
            repairBillQueryService.paramInCheck( params);
            params.setRows(0);
            repairBillQueryService.paramInCheck( params);



            params.setRows(300);
            repairBillQueryService.paramInCheck( params);

            List<String> stringList2 = Arrays.asList("a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ","a ", " b", "  c  ");
            params.setItemCode(stringList2);
            repairBillQueryService.paramInCheck( params);
            repairBillQueryService.isNumeric("12");
            repairBillQueryService.isNumeric("String s");
            params.setPage(1);
            params.setRows(1);
            params.setStartRow(1);
            params.setEndRow(2);
            params.setItemCode(stringList);
            PowerMockito.when(repairBillQueryRepository.getRepairInfo(anyObject())).thenReturn(null);
            repairBillQueryService.getRepairInfo(params);

            repairBillQueryService.repairBillQuery(null);

            params.setRepairDate(null);
            repairBillQueryService.repairBillQuery(params);


        }
        catch (Exception e
        )
        {
            e.printStackTrace();
            Assert.assertEquals(MessageId.OPERATION_SUCCESS, e.getMessage());
        }
    }//end function


}
