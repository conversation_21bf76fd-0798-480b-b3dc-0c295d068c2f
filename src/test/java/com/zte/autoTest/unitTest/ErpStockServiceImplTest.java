package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.ErpStockServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.ErpStockInfo;
import com.zte.domain.model.ErpStockRepository;
import com.zte.domain.model.TransferItemInfoDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.junit.Assert.*;

/**
 * ����������
 *
 * @Author:
 * @Date: 2020/9/3 9:09
 */
public class ErpStockServiceImplTest extends PowerBaseTestCase {

	@InjectMocks
	private ErpStockServiceImpl erpStockService;

	@Mock
	private ErpStockRepository erpStockRepository;

	@Before
	public void init() {
		// ��������עΪ@InjectMocks
		// �������������עΪ @Mock
		// ʹ��MockitoAnnotations.initMocks(this)�Զ�����������ע�������
		MockitoAnnotations.initMocks(this);
	}

	@Test
	@PrepareForTest({CommonUtils.class})
	public void getErpStock() {
		PowerMockito.mockStatic(CommonUtils.class);
		TransferItemInfoDTO dto = new TransferItemInfoDTO();
		dto.setPageNo(1);
		dto.setPageSize(1);
		dto.setItemCode("126569611000");
		dto.setWarehouseCode("41SCZZ");
		dto.setLocationId("202");
		Assert.assertNotNull(erpStockService.getErpStock(dto));
	}

	@Test
	@PrepareForTest({CommonUtils.class})
	public void getErpTransferApplyStock() {
		PowerMockito.mockStatic(CommonUtils.class);
		TransferItemInfoDTO dto = new TransferItemInfoDTO();
		dto.setPageNo(1);
		dto.setPageSize(1);
		dto.setItemCode("126569611000");
		dto.setWarehouseCode("41SCZZ");
		dto.setLocationId("202");
		Assert.assertNotNull(erpStockService.getErpStock(dto));
	}
}
