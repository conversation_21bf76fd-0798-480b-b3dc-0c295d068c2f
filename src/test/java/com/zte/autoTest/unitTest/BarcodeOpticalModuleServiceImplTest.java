package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BarcodeOpticalModuleServiceImpl;
import com.zte.domain.model.datawb.BarcodeOpticalModuleRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2022年11月7日14:45:32
 */
@RunWith(PowerMockRunner.class)
public class BarcodeOpticalModuleServiceImplTest extends BaseTestCase {

    @Mock
    BarcodeOpticalModuleRepository barcodeOpticalModuleRepository;
    @InjectMocks
    private BarcodeOpticalModuleServiceImpl barcodeOpticalModuleServiceImpl;

    @Test
    public void seletcOptical() {
        List<String> dtArray = new ArrayList<>();
        Map<String, Object> htMap = new HashMap<>();
        PowerMockito.when(barcodeOpticalModuleRepository.seletcOptical(htMap))
                .thenReturn(any());
        Assert.assertNotNull(barcodeOpticalModuleServiceImpl.seletcOptical("1233"));
    }


}
