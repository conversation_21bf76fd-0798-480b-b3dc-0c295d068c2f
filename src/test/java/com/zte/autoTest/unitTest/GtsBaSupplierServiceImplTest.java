package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.GtsBaSupplierServiceImpl;
import com.zte.domain.model.datawb.GtsBaSupplier;
import com.zte.domain.model.datawb.GtsBaSupplierRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @Date 2020/11/9 00
 * @description:
 */
public class GtsBaSupplierServiceImplTest extends BaseTestCase {

    @InjectMocks
    GtsBaSupplierServiceImpl gtsBaSupplierService;
    @Mock
    GtsBaSupplierRepository gtsBaSupplierRepository;

    @Test
    public void selGtsBaSupplierList(){
        GtsBaSupplier dto = new GtsBaSupplier();
        dto.setPartnerIdExtList("'123','134'");
        Assert.assertNotNull(gtsBaSupplierService.selGtsBaSupplierList(dto));
    }
}
