package com.zte.autoTest.unitTest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.EntryBoxAppliedOthersService;
import com.zte.application.datawb.EntryBoxAppliedScanService;
import com.zte.application.datawb.MesMaterialConfigBindServiceReplace;
import com.zte.application.datawb.impl.EntryBoxAppliedServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.EntryBoxAppliedRepository;
import com.zte.domain.model.datawb.EntryBoxAppliedScanRepository;
import com.zte.domain.model.datawb.RecalculationConvergenceRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.anyObject;

/**
 * 装箱入库申请单元测试
 *
 * <AUTHOR>
 * @since 2023年4月13日08:47
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,HttpClientUtil.class})
@PowerMockIgnore("javax.net.ssl.*")
public class EntryBoxAppliedTest extends PowerBaseTestCase {

    @InjectMocks
    private EntryBoxAppliedServiceImpl entryBoxAppliedServiceImpl;

    @Mock
    private EntryBoxAppliedScanService entryBoxAppliedScanService;
    @Mock
    private CfgCodeRuleItemService cfgCodeRuleItemService;
    @Mock
    private EntryBoxAppliedRepository entryBoxAppliedRepository;
    @Mock
    private MesMaterialConfigBindServiceReplace mesMaterialConfigBindServiceReplace;
    @Mock
    private EntryBoxAppliedScanRepository entryBoxAppliedScanRepository;

    @Mock
    private EntryBoxAppliedOthersService entryBoxAppliedOthersService;

    @Mock
    RecalculationConvergenceRepository recalculationConvergenceRepository;


    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;


    @Test
    public void validateEntryBoxApplied() throws Exception {
        EntryBoxAppliedDTO paramDto = null;
        String empNo = "10292636";
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PARAM_EMPTY, ex.getMessage());
        }
        paramDto = new EntryBoxAppliedDTO();
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), ex.getMessage());

        }
        paramDto.setEntityName("MWNIP1000-I20200200168");
        paramDto.setPalletNo("TP2212M000024");
        List<String> boxlist = new ArrayList<>();
        boxlist.add("C202120012");
        paramDto.setBoxNumberList(boxlist);
        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length() > 0);
        }
        paramDto.setEntityName("MWNIP1000-I20200200168");
        paramDto.setPalletNo("TP2212M000024");
        paramDto.setBoxNumberList(null);
        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.ENTITY_IS_EMPTY, ex.getMessage());
        }

        paramDto.setEntityName("MWNIP1000-I20200200168");
        paramDto.setPalletNo(null);
        boxlist = new ArrayList<>();
        boxlist.add("C202120012");
        paramDto.setBoxNumberList(boxlist);
        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.ENTITY_IS_EMPTY, ex.getMessage());
        }

        List<EntryBoxAppliedDTO> dtoEntryList = new ArrayList<>();
        EntryBoxAppliedDTO dtx = new EntryBoxAppliedDTO();
        dtx.setEntityName("IPTN6150-I20171000089");
        dtx.setOrganizationID(new Long(635));
        dtx.setPalletNo("TP1711M023160");
        dtoEntryList.add(dtx);

        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(dtoEntryList);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.DT_SUB_INVCODE, ex.getMessage());
        }
        PowerMockito.when(entryBoxAppliedRepository.validateSubInvCode(anyObject())).thenReturn("SSB");
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.MAP_BOX_PALLET, ex.getMessage());
        }

        boxlist = new ArrayList<>();
        boxlist.add("C202120012");
        boxlist.add("");
        paramDto.setBoxNumberList(boxlist);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.EMPTY_BOX_ERROR, ex.getMessage());
        }

        paramDto = new EntryBoxAppliedDTO();
        paramDto.setEntityName("IPTN6150-I20171000089");
        paramDto.setPalletNo("TP1711M023160");
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PALLET_NO_SCAN_END, ex.getMessage());
        }
        List<Map<String, String>> dotListNumber = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("TP1711M023160", "C171009430690");
        dotListNumber.add(map);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dotListNumber);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PALLET_NO_SCAN_END, ex.getMessage());
        }

        List<Map<String, String>> xxxxx = new ArrayList<>();
        Map<String, String> xxMap = new HashMap<>();
        xxMap.put("BILLNUMBER", "C1233444");
        xxxxx.add(xxMap);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(xxxxx);
        List<EntryBoxAppliedScanDTO> dtoListScanEntry = new ArrayList<>();
        EntryBoxAppliedScanDTO dtxxxxx = new EntryBoxAppliedScanDTO();
        dtxxxxx.setScanFlag("正常结束");
        dtoListScanEntry.add(dtxxxxx);
        PowerMockito.when(entryBoxAppliedScanService.boxScanedEnd(anyObject())).thenReturn(dtoListScanEntry);
        PowerMockito.when(cfgCodeRuleItemService.getSequenceBox()).thenReturn("123333");
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn("12333");
        PowerMockito.when(entryBoxAppliedRepository.getEntrySequence()).thenReturn("12333");
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PALLET_NO_SCAN_END, ex.getMessage());
        }
        PowerMockito.when(entryBoxAppliedRepository.getHaveEntryBox(anyObject())).thenReturn(new Long(1));
        List<EntryBoxAppliedBoxDTO> dtoEntryBoxApplied = new ArrayList<>();
        EntryBoxAppliedBoxDTO xdt = new EntryBoxAppliedBoxDTO();
        dtoEntryBoxApplied.add(xdt);
        PowerMockito.when(entryBoxAppliedRepository.getEntryLineSequence()).thenReturn("1233");
        PowerMockito.when(entryBoxAppliedRepository.getBoxInfoToBuild(anyObject())).thenReturn(dtoEntryBoxApplied);
        try {
            paramDto.setPalletNo(null);
            List<String> BoxNumbers = new ArrayList<>();
            BoxNumbers.add("C171009430690");
            BoxNumbers.add("C171009430691");
            BoxNumbers.add("C171009430692");
            BoxNumbers.add("C171009430693");
            BoxNumbers.add("C171009430694");
            paramDto.setBoxNumberList(BoxNumbers);
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.ENTRY_WARE_HOUSE, ex.getMessage());
        }
        List<Map<String, String>> dtMapDto = new ArrayList<>();
        map = new HashMap<>();
        map.put(null, null);
        dtMapDto.add(map);
        dtoEntryList = new ArrayList<>();
        EntryBoxAppliedDTO dd = new EntryBoxAppliedDTO();
        dd.setEntityId(new BigDecimal("22"));
        dd.setOrganizationID(new Long(222));
        dd.setSubInvCode("333");
        dtoEntryList.add(dd);
        List<String> BoxNumbers = new ArrayList<>();
        BoxNumbers.add("C171009430690");
        paramDto.setBoxNumberList(BoxNumbers);
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn(null);
        PowerMockito.when(entryBoxAppliedRepository.getHaveEntryBox(anyObject())).thenReturn(Long.valueOf(0));
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dtMapDto);
        PowerMockito.when(entryBoxAppliedRepository.entityNameOrgId(anyObject())).thenReturn(dtoEntryList);
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }

        List<EntryBoxAppliedBoxDTO> dtoEntryBoxAppliedPlus = new ArrayList<>();
        EntryBoxAppliedBoxDTO dddto = new EntryBoxAppliedBoxDTO();
        dddto.setGrossWeight("33");
        dddto.setEetWeight("33");
        dddto.setMergeBillFlag("2");
        dddto.setBoxNumber("2");
        dtoEntryBoxAppliedPlus.add(dddto);
        EntryBoxAppliedBoxDTO dddto1 = new EntryBoxAppliedBoxDTO();
        dddto1.setGrossWeight("33");
        dddto1.setEetWeight("33");
        dddto1.setMergeBillFlag("2");
        dddto1.setBoxNumber("0");
        dtoEntryBoxAppliedPlus.add(dddto1);
        PowerMockito.when(entryBoxAppliedRepository.getBoxInfoToBuild(anyObject())).thenReturn(dtoEntryBoxAppliedPlus);
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn("333");
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }
        dtoEntryBoxAppliedPlus = new ArrayList<>();
        dddto.setPalletNo("123");
        dtoEntryBoxAppliedPlus.add(dddto);
        dtoEntryBoxAppliedPlus.add(dddto);
        PowerMockito.when(entryBoxAppliedRepository.getBoxInfoToBuild(anyObject())).thenReturn(dtoEntryBoxAppliedPlus);
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(anyObject())).thenReturn("333");
        try {
            entryBoxAppliedServiceImpl.validateEntryBoxApplied(paramDto, empNo);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }
    }

    @Test
    public void setEntryLine() {
        MesEntryBillsDTO objEntryHeaders = new MesEntryBillsDTO();
        List<MesEntryBillLinesDTO> dtMesEntryBillDto = new ArrayList<>();
        String useID = "1233";
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> xxxDt = new ArrayList<>();
        xxxDt.add("C1233333");
        paramDto.setBoxNumberList(xxxDt);
        paramDto.setEntityId(new BigDecimal(1233));
        paramDto.setOrganizationID(new Long(635));
        List<EntryBoxAppliedBoxDTO> dtoEntryBoxApplied = new ArrayList<>();
        EntryBoxAppliedBoxDTO xxxDV = new EntryBoxAppliedBoxDTO();
        xxxDV.setCubage("789");
        xxxDV.setGrossWeight("789");
        xxxDV.setBoxNumber("CXXXX");
        dtoEntryBoxApplied.add(xxxDV);

        PowerMockito.when(entryBoxAppliedRepository.getBoxInfoToBuild(anyObject())).thenReturn(dtoEntryBoxApplied);
        try {
            entryBoxAppliedServiceImpl.setEntryLine(objEntryHeaders, dtMesEntryBillDto, useID, paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }
        EntryBoxAppliedBoxDTO xxxDVdt = new EntryBoxAppliedBoxDTO();
        xxxDVdt.setCubage("789");
        xxxDVdt.setGrossWeight("789");
        xxxDVdt.setPalletNo("TPXXXXX90");
        dtoEntryBoxApplied.add(xxxDVdt);
        xxxDV.setPalletNo("TPXXXXX90");
        try {
            entryBoxAppliedServiceImpl.setEntryLine(objEntryHeaders, dtMesEntryBillDto, useID, paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }
    }

    @Test
    public void boxScanedEnd() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> dtx = new ArrayList<>();
        dtx.add("C123333");
        paramDto.setBoxNumberList(dtx);
        List<EntryBoxAppliedScanDTO> xc = new ArrayList<>();
        EntryBoxAppliedScanDTO cvt = new EntryBoxAppliedScanDTO();
        xc.add(cvt);
        Assert.assertNotNull(PowerMockito.when(entryBoxAppliedScanRepository.boxScanedEnd(anyObject())).thenReturn(xc));
    }

    @Test
    public void haveManyPallet() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> boxList = new ArrayList<>();
        boxList.add("C21078910");
        boxList.add("C21078911");
        paramDto.setBoxNumberList(boxList);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);

        List<Map<String, String>> dtMapDto = new ArrayList<>();
        Map<String, String> dtMap = new HashMap<>();
        dtMap.put("1233", "456");
        dtMap.put("456", "789");
        dtMapDto.add(dtMap);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dtMapDto);
        Assert.assertNotNull(boxList);
        Assert.assertNotNull(dtMap);
        Assert.assertNotNull(dtMapDto);
    }

    @Test
    public void dealPalletNo() {
        //单一托盘
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        paramDto.setEntityName("MWNIP1000-I20200200168");
        paramDto.setPalletNo("TP2212M000024");
        paramDto.setOrganizationID(new Long(635));
        try {
            List<String> palletList = new ArrayList<>();
            palletList.add("12333");
            PowerMockito.when(entryBoxAppliedRepository.getPalletNoScaned(anyObject())).thenReturn(palletList);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length() > 0);
        }
        //多个托盘的场景
        List<Map<String, String>> arrayList = new ArrayList<>();
        paramDto.setPalletNo(null);
        Map<String, String> xxdt = new HashMap<>();
        xxdt.put(Constant.PALLET_NO, "456");
        arrayList.add(xxdt);
        paramDto.setMapPalletBox(arrayList);
        try {
            PowerMockito.when(entryBoxAppliedRepository.getPalletNoScaned(anyObject())).thenReturn(null);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length() > 0);
        }

    }

    @Test
    public void insertHeadLine() {
        List<MesEntryBillLinesDTO> dtMesEntryBillDto = new ArrayList<>();
        MesEntryBillLinesDTO dtx = new MesEntryBillLinesDTO();
        dtMesEntryBillDto.add(dtx);
        MesEntryBillsDTO objEntryHeaders = new MesEntryBillsDTO();
        try {
            Mockito.doNothing().when(entryBoxAppliedRepository).insertEntryHead(Mockito.any());
            Mockito.doNothing().when(entryBoxAppliedRepository).insertEntryLine(Mockito.any());
            entryBoxAppliedServiceImpl.insertHeadLine(dtMesEntryBillDto, objEntryHeaders);
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length() > 0);
        }
    }

    @Test
    public void auxiliaryBox() {
        List<MesEntryReadyLineDTO> entryReadyLine = new ArrayList<>();
        MesEntryReadyLineDTO dtx = new MesEntryReadyLineDTO();
        dtx.setBoxAtrribute("主箱");
        dtx.setBillNumber("C1233");
        dtx.setBillId("123");
        entryReadyLine.add(dtx);
        MesEntryReadyLineDTO dtxx = new MesEntryReadyLineDTO();
        dtxx.setBoxAtrribute("主箱");
        dtxx.setBillNumber("C1233");
        dtxx.setBillId("123");
        entryReadyLine.add(dtxx);
        MesEntryReadyLineDTO dtxFuzhu = new MesEntryReadyLineDTO();
        dtxFuzhu.setBoxAtrribute("辅箱");
        dtxFuzhu.setBillNumber("C1233");
        dtxFuzhu.setBillId("123");
        entryReadyLine.add(dtxFuzhu);

        MesEntryReadyLineDTO dtxFuzhuX = new MesEntryReadyLineDTO();
        dtxFuzhuX.setBoxAtrribute("辅箱");
        dtxFuzhuX.setBillNumber("C1233");
        dtxFuzhuX.setBillId("123");
        entryReadyLine.add(dtxFuzhuX);
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        paramDto.setOrganizationID(new Long(635));
        MesEntryReadyLineDTO dtoo = new MesEntryReadyLineDTO();
        dtoo.setSunBillNumber("C12334");
        List<MesEntryReadyLineDTO> entryReadyLineMain = new ArrayList<>();
        entryReadyLineMain.add(dtoo);
        try {
            PowerMockito.when(entryBoxAppliedRepository.getEntryLineMain(anyObject())).thenReturn(entryReadyLineMain);
            entryBoxAppliedServiceImpl.auxiliaryBox(entryReadyLine, paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.AUAX_STIMULATE_SUMMIT, ex.getMessage());
        }
        dtoo.setSunBillNumber("C1233");
        try {
            entryBoxAppliedServiceImpl.auxiliaryBox(entryReadyLine, paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.AUAX_STIMULATE_SUMMIT, ex.getMessage());
        }
    }

    @Test
    public void boxScanOver() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<EntryBoxAppliedScanDTO> dtoListScanEntry = new ArrayList<>();
        EntryBoxAppliedScanDTO dto = new EntryBoxAppliedScanDTO();
        dto.setScanFlag("ccc");
        dto.setBillNumber("C20134567");
        dtoListScanEntry.add(dto);
        PowerMockito.when(entryBoxAppliedScanService.boxScanedEnd(anyObject())).thenReturn(dtoListScanEntry);
        try {
            entryBoxAppliedServiceImpl.boxScanOver(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.BOX_UNSCANED_OVER, ex.getMessage());
        }
        PowerMockito.when(entryBoxAppliedScanService.boxScanedEnd(anyObject())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.boxScanOver(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.BOX_NO_ITEM_NO_SCANED, ex.getMessage());
        }
    }

    @Test
    public void allBoxEntry() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> xxdt = new ArrayList<>();
        xxdt.add("");
        xxdt.add(null);
        paramDto.setBoxNumberList(xxdt);
        try {
            entryBoxAppliedServiceImpl.allBoxEntry(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.EMPTY_BOX_ERROR, ex.getMessage());
        }
    }

    @Test
    public void getConcurrently() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> xxdt = new ArrayList<>();
        xxdt.add("C12333");
        xxdt.add("C123333");
        paramDto.setBoxNumberList(xxdt);
        List<MesEntryReadyLineDTO> entryReadyLine = new ArrayList<>();
        MesEntryReadyLineDTO dtkl = new MesEntryReadyLineDTO();
        dtkl.setCubage("0");
        dtkl.setGrossWeight("0");
        dtkl.setBoxAtrribute("正常箱");
        entryReadyLine.add(dtkl);

        PowerMockito.when(entryBoxAppliedRepository.getEntryReadyLine(anyObject())).thenReturn(entryReadyLine);
        try {
            entryBoxAppliedServiceImpl.getConcurrently(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), ex.getMessage());

        }
    }

    @Test
    public void valiateVoulumeWeight() {
        List<EntryBoxAppliedBoxDTO> dtoEntryBoxApplied = new ArrayList<>();
        try {
            entryBoxAppliedServiceImpl.valiateVoulumeWeight(dtoEntryBoxApplied);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }
        EntryBoxAppliedBoxDTO dtx = new EntryBoxAppliedBoxDTO();
        dtx.setGrossWeight("0");
        dtx.setEetWeight("0");
        dtx.setMergeBillFlag("0");
        dtoEntryBoxApplied.add(dtx);
        try {
            entryBoxAppliedServiceImpl.valiateVoulumeWeight(dtoEntryBoxApplied);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.GROSS_NET_WEIGHT, ex.getMessage());
        }
    }

    /* Started by AICoder, pid:73b9c7dd87a84eb499de9dc16dc42060 */
    @Test
    public void validatePalletNo() throws MesBusinessException {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        paramDto.setPalletNo("TP1711M023160");
        PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.validatePalletNo(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PALLET_NO_SCAN_END, ex.getMessage());
        }
        PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn("N");
        try {
            entryBoxAppliedServiceImpl.validatePalletNo(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PALLET_NO_SCAN_END, ex.getMessage());
        }

        List<Map<String, String>> dotListNumber = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        paramDto.setEntityName("IPTN6150-I20171000089");

        map.put("TP1711M023160", "C171009430690");
        dotListNumber.add(map);
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dotListNumber);
        PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn("Y");
        entryBoxAppliedServiceImpl.validatePalletNo(paramDto);

        try {
            paramDto = new EntryBoxAppliedDTO();
            paramDto.setEntityName("IPTN6150-I20171000089");
            paramDto.setPalletNo("TP1711M02316022222222");
            PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);
            PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn("Y");
            entryBoxAppliedServiceImpl.validatePalletNo(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PALLET_NO_BOX_DT, ex.getMessage());
        }

        List<String> BoxNumbers;
        try {
            paramDto = new EntryBoxAppliedDTO();
            paramDto.setEntityName("IPTN6150-I20171000089");
            BoxNumbers = new ArrayList<>();
            BoxNumbers.add("C171009430690");
            paramDto.setBoxNumberList(BoxNumbers);
            PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);
            PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn("Y");
            entryBoxAppliedServiceImpl.validatePalletNo(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.MAP_BOX_PALLET, ex.getMessage());
        }
        try {
            paramDto = new EntryBoxAppliedDTO();
            paramDto.setEntityName("IPTN6150-I20171000089");
            BoxNumbers = new ArrayList<>();
            BoxNumbers.add("C171009430690");
            BoxNumbers.add("C171009430691");
            BoxNumbers.add("C171009430692");
            BoxNumbers.add("C171009430693");
            BoxNumbers.add("C171009430694");
            paramDto.setBoxNumberList(BoxNumbers);
            dotListNumber = new ArrayList<>();
            map = new HashMap<>();
            map.put("TP1711M023160", "C171009430690");
            map.put("TP1711M023160", "C171009430691");
            map.put("TP1711M023160", "C171009430692");
            map.put("TP1711M023160", "C171009430693");
            map.put("TP1711M023160", "C171009430694");
            dotListNumber.add(map);
            PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(dotListNumber);
            PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn("Y");
            entryBoxAppliedServiceImpl.validatePalletNo(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.MAP_BOX_PALLET, ex.getMessage());
        }
        try {
            paramDto = new EntryBoxAppliedDTO();
            paramDto.setEntityName("HPS-N20171100134");
            BoxNumbers = new ArrayList<>();
            BoxNumbers.add("C171109791929");
            BoxNumbers.add("C171109791889");
            paramDto.setBoxNumberList(BoxNumbers);
            dotListNumber = new ArrayList<>();
            map = new HashMap<>();
            map.put("TP1711M023160", "C171109791929");
            map.put(null, "C171109791889");
            dotListNumber.add(map);
            PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(anyObject())).thenReturn(null);
            PowerMockito.when(entryBoxAppliedRepository.getPalletIsScanEnd(Mockito.any())).thenReturn("Y");
            entryBoxAppliedServiceImpl.validatePalletNo(paramDto);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.MAP_BOX_PALLET, ex.getMessage());
        }
    }/* Ended by AICoder, pid:73b9c7dd87a84eb499de9dc16dc42060 */

    @Test
    public void whiteBox() throws Exception {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        paramDto.setBoxNumberList(new LinkedList<>());
        List<Map<String, String>> dtMapDto = new LinkedList<>();
        PowerMockito.when(entryBoxAppliedRepository.getAllBoxList(Mockito.any())).thenReturn(dtMapDto);
        try {
            Whitebox.invokeMethod(entryBoxAppliedServiceImpl, "validatePalletNoAndBox", paramDto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.MAP_BOX_PALLET.equals(e.getMessage()));
        }
        Map<String, String> map = new HashMap<>();
        map.put(Constant.PALLET_NO, "34");
        dtMapDto.add(map);
        paramDto.setBoxNumberList(Arrays.asList("44"));
        try {
            Whitebox.invokeMethod(entryBoxAppliedServiceImpl, "validatePalletNoAndBox", paramDto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.MAP_BOX_PALLET.equals(e.getMessage()));
        }
    }

    /* Started by AICoder, pid:ea139eb9b428484a8419782a9685a7d8 */
    @Test
    public  void  updatPurchasedOriginCountry() throws Exception{
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(recalculationConvergenceRepository.getOriginCountryNA()).thenReturn(null);
        entryBoxAppliedServiceImpl.updatePurchasedOriginCountry();
        Assert.assertEquals(true,true);

        List<String> listItemBarcoce = new ArrayList<>();
        listItemBarcoce.add("1ATSU4200916000004");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
        PowerMockito.when(recalculationConvergenceRepository.getOriginCountryNA()).thenReturn(listItemBarcoce);
        entryBoxAppliedServiceImpl.updatePurchasedOriginCountry();
        Assert.assertEquals(true,true);
    }
    /* Ended by AICoder, pid:ea139eb9b428484a8419782a9685a7d8 */

    /* Started by AICoder, pid:19e43badcc5142d19d747f67db4b83de */
    @Test
    public void  getZMatOriLand() throws RouteException, JsonProcessingException {
        PowerMockito.mockStatic(HttpClientUtil.class);
        List<String> listItemBarcoce = new ArrayList<>();
        listItemBarcoce.add("1ATSU4200916000004");

        String joson = "{\"code\":{\"code\":\"333\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"}}";
        List<EntityWeightDTO> list = new ArrayList<>();
        EntityWeightDTO dto = new EntityWeightDTO();
        dto.setLookupCode("333");
        list.add(dto);

        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.anyString())).thenReturn(list);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(joson);
        List<EntryBoxAppliedOriginDTO> result = entryBoxAppliedServiceImpl.getZMatOriLand(listItemBarcoce);
        Assert.assertEquals(result.stream().count(),0);

        list = new ArrayList<>();
        dto = new EntityWeightDTO();
        dto.setLookupCode("800600800001");
        list.add(dto);
        dto = new EntityWeightDTO();
        dto.setLookupCode("800600800002");
        list.add(dto);
        dto = new EntityWeightDTO();
        dto.setLookupCode("800600800003");
        list.add(dto);
        dto = new EntityWeightDTO();
        dto.setLookupCode("800600800007");
        list.add(dto);
        joson = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"}}";
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.anyString())).thenReturn(list);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(joson);
         result = entryBoxAppliedServiceImpl.getZMatOriLand(listItemBarcoce);
        Assert.assertEquals(result.stream().count(),0);

        joson = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"id\":\"40953958044f4810a3109e3fd6fa8020\",\"createDate\":\"2020-09-16 16:07:29\",\"lastUpdatedDate\":\"2024-07-16 19:24:45\",\"barcodeDefinitionId\":600002,\"parentCategoryCode\":\"REEL_MP_CODE\",\"parentCategoryName\":\"盘码/最小包装\",\"categoryCode\":\"SUPPLIER_REELID18\",\"categoryName\":\"原材料盘码Reel ID（SUPPLIER_18）\",\"featureCode\":null,\"featureValue\":null,\"barcode\":\"1ATSU4200916000004\",\"quantity\":\"5000\",\"unit\":\"只\",\"isLead\":null,\"itemCode\":\"045020700039\",\"itemName\":\"1206抗硫化厚膜电阻\",\"itemVersion\":null,\"useStatus\":\"USING\",\"useStatusDesc\":\"使用中\",\"controlStatus\":\"NORMAL\",\"controlStatusDesc\":\"正常\",\"supplierCode\":\"45020100\",\"supplierName\":\"\",\"sysLotCode\":\"3494681\",\"specModel\":\"RMS12DT5112\",\"modelType\":null,\"modelDesc\":null,\"brandName\":\"TA-I\",\"prodDate\":\"2020-08-13\",\"prodBatchNo\":\"2025\",\"prodAddress\":\"中国\",\"relatedContainerBarcode\":null,\"relatedReelBarcode\":null,\"relatedSnBarcode\":null,\"relatedDeviceBarcode\":null,\"relatedBoardBarcode\":null,\"sourceSystem\":\"ISCP\",\"sourceBatchNo\":\"220024957455\",\"orgId\":0,\"sbomNo\":null,\"sbomName\":null,\"sbomEnName\":null,\"remark\":\"\",\"fromExternalFlag\":\"N\",\"relatedBillType\":null,\"relatedBillNo\":\"F2009SC00000757\",\"tenantId\":10001,\"createBy\":\"A0461788380059992064\",\"createByName\":\"A0461788380059992064\",\"lastUpdatedBy\":\"10266925\",\"lastUpdatedByName\":\"10266925\",\"countryCode\":\"CN\"}],\"other\":null,\"responseRule\":\"msa\"}";
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.anyString())).thenReturn(list);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(joson);
        result = entryBoxAppliedServiceImpl.getZMatOriLand(listItemBarcoce);
        Assert.assertEquals(result.stream().count(),1);
    }/* Ended by AICoder, pid:19e43badcc5142d19d747f67db4b83de */

    @Test
    public void validateEntityNameTest(){
        EntryBoxAppliedDTO dto = new EntryBoxAppliedDTO();
        String desc = "N";
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn(desc);
        try {
            entryBoxAppliedServiceImpl.validateEntityName(dto);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        desc = "Y";
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn(desc);
        PowerMockito.when(entryBoxAppliedRepository.getItemControls(Mockito.any())).thenReturn(null);
        try {
            entryBoxAppliedServiceImpl.validateEntityName(dto);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(), null);
        }
        List<ItemControlDTO> itemControlDTOS = new ArrayList<>();
        ItemControlDTO itemControlDTO = new ItemControlDTO();
        itemControlDTO.setEntityName("111");
        itemControlDTO.setSegment1("111");
        itemControlDTOS.add(itemControlDTO);
        PowerMockito.when(entryBoxAppliedRepository.getItemControls(Mockito.any())).thenReturn(itemControlDTOS);
        try {
            entryBoxAppliedServiceImpl.validateEntityName(dto);
        } catch (Exception ex) {
            Assert.assertFalse(ex.getMessage().equals(null));
        }
    }
}
