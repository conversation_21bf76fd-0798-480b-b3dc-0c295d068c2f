package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.ZmsCommonServiceImpl;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsCommonRepository;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.IcmsRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsCommonServiceImplTest {

    @InjectMocks
    private ZmsCommonServiceImpl zmsCommonService;

    @Mock
    private ZmsCommonRepository zmsCommonRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private MdsRemoteService mdsRemoteService;

    @Mock
    IcmsRemoteService icmsRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class);
    }

    @Test
    public void getNeedUploadContractList() throws Exception {
        ReflectUtil.setFieldValue(zmsCommonService,"configNumber","config");
        List<String> zmsCommonEntityDTOList = Lists.newArrayList();
        List<SalesOrderUniformInfo> icmsList = new ArrayList<>();
        icmsList.add(new SalesOrderUniformInfo());
        icmsList.add(new SalesOrderUniformInfo(){{setConfigRelationsUniformList(Lists.newArrayList(new ConfigRelationsUniformInfo(){{setConfigNumber("config");}}));}});
        icmsList.add(new SalesOrderUniformInfo(){{setConfigRelationsUniformList(Lists.newArrayList(new ConfigRelationsUniformInfo(){{setConfigNumber("233");}}));}});
        PowerMockito.when(icmsRemoteService.getContract(anyList())).thenReturn(icmsList);
        zmsCommonService.getNeedUploadContractList(zmsCommonEntityDTOList);
        PowerMockito.when(icmsRemoteService.getContract(anyList())).thenReturn(Lists.newArrayList());
        zmsCommonService.getNeedUploadContractList(zmsCommonEntityDTOList);
        Assert.assertNotNull(zmsCommonEntityDTOList);
    }

    @Test
    public void getZmsEntityList() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setEmpNo("111");
        dto.setLookupType("111");

        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupType(new BigDecimal(2030102));
        sysLookupValues.setLookupCode(new BigDecimal("203010200001"));
        sysLookupValues.setLookupMeaning("1111");
        sysLookupValues.setDescription("1111");
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        List<ZmsCommonEntityDTO> zmsCommonEntityDTOList = new ArrayList<>();
        PowerMockito.when(zmsCommonRepository.getZmsEntityList(any())).thenReturn(zmsCommonEntityDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getZmsEntityList", dto);

        try{
            PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(null);
            Whitebox.invokeMethod(zmsCommonService, "getZmsEntityList", dto);
        }catch (Exception e){
            Assert.assertNotNull(e.toString());
        }
        Assert.assertNotNull(dto);
    }

    @Test
    public void getLookupMeanList() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        dto.setLookupType("2030102");
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupType(new BigDecimal(2030102));
        sysLookupValues.setLookupCode(new BigDecimal("203010200001"));
        sysLookupValues.setLookupMeaning("1111");
        sysLookupValues.setDescription("1111");
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsCommonService, "getLookupMeanList", dto);

        dto.setLookupType("2030101");
        PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsCommonService, "getLookupMeanList", dto);

        dto.setLookupType("2030104");
        PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsCommonService, "getLookupMeanList", dto);

        dto.setLookupType("1");
        PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsCommonService, "getLookupMeanList", dto);

        try{
            PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(null);
            Whitebox.invokeMethod(zmsCommonService, "getLookupMeanList", dto);
        }catch (Exception e){
            Assert.assertNotNull(e.toString());
        }
        Assert.assertNotNull(dto);
    }

    @Test
    public void getTencentLookup() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        dto.setLookupType("2030102");

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupCode(new BigDecimal("203010200001"));
        sysLookupValues.setDescription("1111");
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200002"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200003"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200004"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200005"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200006"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200007"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200008"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("1"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("1111");
        dto.setEntityNameList(entityNameList);
        sysLookupValues.setLookupCode(new BigDecimal("203010200002"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200005"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010200008"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("1"));
        Whitebox.invokeMethod(zmsCommonService, "getTencentLookup", dto, sysLookupValues);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getBaiduLookup() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        dto.setLookupType("2030102");

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupCode(new BigDecimal("203010100002"));
        sysLookupValues.setDescription("1111");
        Whitebox.invokeMethod(zmsCommonService, "getBaiduLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010100003"));
        Whitebox.invokeMethod(zmsCommonService, "getBaiduLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("1"));
        Whitebox.invokeMethod(zmsCommonService, "getBaiduLookup", dto, sysLookupValues);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getAlibabaLookup() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        dto.setLookupType("2030102");

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupCode(new BigDecimal("203010400001"));
        sysLookupValues.setDescription("1111");
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400002"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400003"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400004"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400005"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400006"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("1"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("1111");
        dto.setEntityNameList(entityNameList);
        sysLookupValues.setLookupCode(new BigDecimal("203010400002"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400005"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("203010400006"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        sysLookupValues.setLookupCode(new BigDecimal("1"));
        Whitebox.invokeMethod(zmsCommonService, "getAlibabaLookup", dto, sysLookupValues);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void getServerSnInfo() throws Exception{

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        dto.setEntityId("1111");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("111");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(zmsCommonRepository.getAssemblyMaterialsByEntityId(any())).thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", dto);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("4");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", dto);

        customerItemsDTOList.clear();
        customerItemsDTO.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO2);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", dto);

        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("111");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", dto);

        customerItemsDTOList.clear();
        customerItemsDTO.setZteCode("222");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", dto);

        configDetailDTOList.clear();
        PowerMockito.when(zmsCommonRepository.getAssemblyMaterialsByEntityId(any())).thenReturn(configDetailDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void getAssemblyMaterialsByEntityId() throws Exception{

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        String entityId = "";
        Whitebox.invokeMethod(zmsCommonService, "getAssemblyMaterialsByEntityId", entityId);

        entityId = "1111";
        List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList = new ArrayList<>();
        PowerMockito.when(zmsCommonRepository.getAssemblyMaterialsByEntityId(any())).thenReturn(cpmConfigItemAssembleDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getAssemblyMaterialsByEntityId", entityId);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void getBoardSnInfo() throws Exception{

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        dto.setIsQueryShelfMod("N");
        Whitebox.invokeMethod(zmsCommonService, "getBoardSnInfo", dto);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("111");
        cpmConfigItemAssembleDTO.setItemBarcode("1111");
        dto.setServerSnCpmConfigItemAssembleDTO(cpmConfigItemAssembleDTO);
        List<String> itemBarcodeList = new ArrayList<>();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("222");
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        Whitebox.invokeMethod(zmsCommonService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getBoardSnInfo", dto);

        dto.setIsQueryShelfMod("Y");
        List<CustomerItemsDTO> entityCustomerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("8");
        customerItemsDTO.setZteCode("111");
        entityCustomerItemsDTOList.add(customerItemsDTO);
        dto.setEntityCustomerItemsDTOList(entityCustomerItemsDTOList);
        List<CpmConfigItemAssembleDTO> entityConfigDetailDTOList = new ArrayList<>();
        entityConfigDetailDTOList.add(cpmConfigItemAssembleDTO);
        dto.setEntityConfigDetailDTOList(entityConfigDetailDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);
        Whitebox.invokeMethod(zmsCommonService, "getShelfModInfo", dto, wsmAssembleLinesList);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getBoardSnInfo", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void getShelfModInfo() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();

        List<CustomerItemsDTO> entityCustomerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("8");
        customerItemsDTO.setZteCode("111");
        entityCustomerItemsDTOList.add(customerItemsDTO);
        dto.setEntityCustomerItemsDTOList(entityCustomerItemsDTOList);
        List<CpmConfigItemAssembleDTO> entityConfigDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("111");
        cpmConfigItemAssembleDTO.setItemBarcode("1111");
        entityConfigDetailDTOList.add(cpmConfigItemAssembleDTO);
        dto.setEntityConfigDetailDTOList(entityConfigDetailDTOList);
        List<String> shelfModItemBarcodeList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "getThreeWsmAssembleLines", shelfModItemBarcodeList, wsmAssembleLinesList);
        Whitebox.invokeMethod(zmsCommonService, "getShelfModInfo", dto, wsmAssembleLinesList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getThreeWsmAssembleLines() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<String> itemBarcodeList = new ArrayList<>();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);

        itemBarcodeList.add("1111");
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList1 = new ArrayList<>();
        PowerMockito.when(zmsCommonRepository.getAssemblyMaterialList(any())).thenReturn(wsmAssembleLinesList1);
        Whitebox.invokeMethod(zmsCommonService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("02111");
        wsmAssembleLinesList1.add(wsmAssembleLinesEntityDTO);
        PowerMockito.when(zmsCommonRepository.getAssemblyMaterialList(any())).thenReturn(wsmAssembleLinesList1);
        Whitebox.invokeMethod(zmsCommonService, "getThreeWsmAssembleLines", itemBarcodeList, wsmAssembleLinesList);



        Assert.assertNotNull(dto);
    }

    @Test
    public void getAssemblyMaterialList() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<String> list = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "getAssemblyMaterialList", list);

        list.add("111");
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesEntityDTOList = new ArrayList<>();
        PowerMockito.when(zmsCommonRepository.getAssemblyMaterialList(any())).thenReturn(wsmAssembleLinesEntityDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getAssemblyMaterialList", list);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void getMainBoardSn() throws Exception{

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        List<CustomerItemsDTO> motherboardList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "getMainBoardSn", configDetailDTOList, motherboardList);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("222");
        cpmConfigItemAssembleDTO.setItemBarcode("222222");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("111");
        motherboardList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsCommonService, "getMainBoardSn", configDetailDTOList, motherboardList);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO2 = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO2.setItemCode("111");
        cpmConfigItemAssembleDTO2.setItemBarcode("222222");
        configDetailDTOList.add(cpmConfigItemAssembleDTO2);
        Whitebox.invokeMethod(zmsCommonService, "getMainBoardSn", configDetailDTOList, motherboardList);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void getServiceSnBoardSn() throws Exception{

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "getServiceSnBoardSn", wsmAssembleLinesList, customerItemsDTOList);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("4");
        customerItemsDTOList.add(customerItemsDTO);
        Whitebox.invokeMethod(zmsCommonService, "getServiceSnBoardSn", wsmAssembleLinesList, customerItemsDTOList);

        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setProjectType("5");
        customerItemsDTO2.setZteCode("222");
        customerItemsDTOList.add(customerItemsDTO2);
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemCode("111");
        wsmAssembleLinesEntityDTO.setItemBarcode("222");
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        Whitebox.invokeMethod(zmsCommonService, "getServiceSnBoardSn", wsmAssembleLinesList, customerItemsDTOList);

        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO2 = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO2.setItemCode("222");
        wsmAssembleLinesEntityDTO2.setItemBarcode("222");
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO2);
        Whitebox.invokeMethod(zmsCommonService, "getServiceSnBoardSn", wsmAssembleLinesList, customerItemsDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getZsInfo() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        Map<Object, Object> paramMap = new HashMap<>();
        String zsUrl = "1111";

        try {
            PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn(null);
            Whitebox.invokeMethod(zmsCommonService, "getZsInfo", paramMap, zsUrl);
        } catch (Exception e) {
            Assert.assertNotNull(e.toString());
        }

        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("123");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(result);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn(null);
        Whitebox.invokeMethod(zmsCommonService, "getZsInfo", paramMap, zsUrl);

        Assert.assertNotNull(dto);
    }

    @Test
    public void stationInfoMapping() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<ZmsStationItemDTO> stations = new ArrayList<>();
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "stationInfoMapping", stations, sysLookupValuesList);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("111");
        sysLookupValues.setDescription("111");
        sysLookupValuesList.add(sysLookupValues);
        ZmsStationItemDTO zmsStationItemDTO = new ZmsStationItemDTO();
        zmsStationItemDTO.setName("111");
        stations.add(zmsStationItemDTO);
        ZmsStationItemDTO zmsStationItemDTO2 = new ZmsStationItemDTO();
        zmsStationItemDTO2.setName("222");
        stations.add(zmsStationItemDTO2);
        Whitebox.invokeMethod(zmsCommonService, "stationInfoMapping", stations, sysLookupValuesList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void insertInternetMain() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        String dataType = "0";
        PowerMockito.when(zmsCommonRepository.insertInternetMain(any())).thenReturn(1);
        Whitebox.invokeMethod(zmsCommonService, "insertInternetMain", dto, dataType);

        Assert.assertNotNull(dto);
    }

    @Test
    public void insertMesInfoUploadLog() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);

        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setSn("111");
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadLogDTO);
        PowerMockito.when(zmsCommonRepository.insertMesInfoUploadLog(any())).thenReturn(1);
        Whitebox.invokeMethod(zmsCommonService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void pushDataToB2B() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "pushDataToB2B", dataList, false);

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setMessageType("111");
        dataList.add(customerDataLogDTO);
        Whitebox.invokeMethod(zmsCommonService, "pushDataToB2B", dataList, true);
        Whitebox.invokeMethod(zmsCommonService, "pushDataToB2B", dataList, false);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getSysLookupValues() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        String lookupCode = "111";

        try {
            PowerMockito.when(zmsCommonRepository.getSysLookupValues(any())).thenReturn(null);
            Whitebox.invokeMethod(zmsCommonService, "getSysLookupValues", lookupCode);
        } catch (Exception e) {
            Assert.assertNotNull(e.toString());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("111");
        PowerMockito.when(zmsCommonRepository.getSysLookupValues(any())).thenReturn(sysLookupValues);
        Whitebox.invokeMethod(zmsCommonService, "getSysLookupValues", lookupCode);
        Assert.assertNotNull(dto);
    }

    @Test
    public void getSysLookupValuesList() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        String lookupType = "";
        Whitebox.invokeMethod(zmsCommonService, "getSysLookupValuesList", lookupType);

        lookupType = "11";
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        PowerMockito.when(zmsCommonRepository.getSysLookupValuesList(any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsCommonService, "getSysLookupValuesList", lookupType);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getCbomInfoNoVersion() throws Exception {

        ZmsCommonEntityDTO dto = new ZmsCommonEntityDTO();
        dto.setUserAddress("阿里");
        ZmsCommonRequestDTO zmsCommonRequestDTO = new ZmsCommonRequestDTO();
        Whitebox.invokeMethod(zmsCommonService, "getCbomInfoNoVersion", zmsCommonRequestDTO);

        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("111");
        zmsCommonRequestDTO.setEntityNameList(entityNameList);
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        PowerMockito.when(zmsCommonRepository.getCbomInfoNoVersion(any())).thenReturn(zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsCommonService, "getCbomInfoNoVersion", zmsCommonRequestDTO);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getFixBomDetailDataTest() throws Exception {
        List<SysLookupValues> typeList = new ArrayList<>();
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.getFixBomDetailData("123");
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        SysLookupValues s1=new SysLookupValues();
        s1.setLookupCode(new BigDecimal("824009400006"));
        typeList.add(s1);
        SysLookupValues s2=new SysLookupValues();
        s2.setLookupCode(new BigDecimal("824009400009"));
        typeList.add(s2);
        SysLookupValues s3=new SysLookupValues();
        s3.setLookupCode(new BigDecimal("824009400001"));
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.getFixBomDetailData("123");
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        typeList = new ArrayList<>();
        s1.setDescription("123");
        typeList.add(s1);
        typeList.add(s2);
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.getFixBomDetailData("123");
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        typeList = new ArrayList<>();
        s1.setDescription("");
        s2.setDescription("123");
        typeList.add(s1);
        typeList.add(s2);
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.getFixBomDetailData("123");
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        typeList = new ArrayList<>();
        s1.setDescription("123");
        s2.setDescription("http://123.com");
        typeList.add(s1);
        typeList.add(s2);
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.getFixBomDetailData("123");
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void queryGbomListTest() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("123");
        CpqdQueryDTO cpqdQueryDTO = new CpqdQueryDTO();
        cpqdQueryDTO.setInstanceNo(list);
        List<SysLookupValues> typeList = new ArrayList<>();
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.queryGbomList(cpqdQueryDTO);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        SysLookupValues s1=new SysLookupValues();
        s1.setLookupCode(new BigDecimal("824009400006"));
        typeList.add(s1);
        SysLookupValues s2=new SysLookupValues();
        s2.setLookupCode(new BigDecimal("824009400011"));
        typeList.add(s2);
        SysLookupValues s3=new SysLookupValues();
        s3.setLookupCode(new BigDecimal("824009400001"));
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.queryGbomList(cpqdQueryDTO);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        typeList = new ArrayList<>();
        s1.setDescription("123");
        typeList.add(s1);
        typeList.add(s2);
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.queryGbomList(cpqdQueryDTO);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        typeList = new ArrayList<>();
        s1.setDescription("");
        s2.setDescription("123");
        typeList.add(s1);
        typeList.add(s2);
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.queryGbomList(cpqdQueryDTO);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        typeList = new ArrayList<>();
        s1.setDescription("123");
        s2.setDescription("http://123.com");
        typeList.add(s1);
        typeList.add(s2);
        typeList.add(s3);
        when(zmsCommonRepository.getSysLookupValuesList(anyString())).thenReturn(typeList);
        try {
            zmsCommonService.queryGbomList(cpqdQueryDTO);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        List<CpqdGbomDTO> cpqdGbomDTOS = new ArrayList<>();
        CpqdGbomDTO cpqdGbomDTO =new CpqdGbomDTO();
        cpqdGbomDTO.setCbomCode("123");
        cpqdGbomDTOS.add(cpqdGbomDTO);
        when(HttpClientUtil.httpPostWithJSON(anyString(),anyString(),any())).thenReturn(com.zte.itp.msa.util.json.JacksonJsonConverUtil.beanToJson(cpqdGbomDTOS));
        try {
            zmsCommonService.queryGbomList(cpqdQueryDTO);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void postInOneApiTest() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("2222");
        PowerMockito.when(zmsCommonRepository.getSysLookupValues(anyString())).thenReturn(sysLookupValues);
        SysLookupValues sysLookupValuesAppCode = new SysLookupValues();
        sysLookupValuesAppCode.setDescription("111");
        zmsCommonService.postInOneApi(new ZmsAlibabaUploadBackDTO(), "111", "222");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn(null);
        PowerMockito.when(zmsCommonRepository.getSysLookupValues("111")).thenReturn(sysLookupValuesAppCode);
        PowerMockito.when(zmsCommonRepository.getSysLookupValues("222")).thenReturn(sysLookupValues);
        zmsCommonService.postInOneApi(new ZmsAlibabaUploadBackDTO(), "111", "222");
        PowerMockito.when(zmsCommonRepository.getSysLookupValues(anyString())).thenReturn(sysLookupValuesAppCode);
        zmsCommonService.postInOneApi(new ZmsAlibabaUploadBackDTO(), "111", "222");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn("2111122");
        zmsCommonService.postInOneApi(new ZmsAlibabaUploadBackDTO(), "111", "222");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(null);
        String res = zmsCommonService.postInOneApi(new ZmsAlibabaUploadBackDTO(), "111", "222");
        Assert.assertNull(res);
    }
}
