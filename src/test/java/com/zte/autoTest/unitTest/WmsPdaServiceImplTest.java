package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.WmsPdaServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.WmsPadRepository;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WmsPdaServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WmsPdaServiceImpl wmsPdaService;

    @Mock
    private WmsPadRepository wmsPadRepository;

    @Test
    public void deleteMesEntryBillLinesProcess() throws Exception {
        WmsPdaDeleteEntryBillLinesInDTO dto = new WmsPdaDeleteEntryBillLinesInDTO();
        try {
            wmsPdaService.deleteMesEntryBillLinesProcess(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_DETAILIDS_NOT_NULL , e.getMessage());
        }
        try {
            List<Long> list = new ArrayList<>();
            list.add(22l);
            dto.setDetailIds(list);
            PowerMockito.when(wmsPadRepository.deleteEntryBillLine(Mockito.any())).thenReturn(0l);
            wmsPdaService.deleteMesEntryBillLinesProcess(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            PowerMockito.when(wmsPadRepository.deleteEntryBillLine(Mockito.any())).thenReturn(1l);
            wmsPdaService.deleteMesEntryBillLinesProcess(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }

        WmsPdaUpdateEntryBillLinesInDTO param = new WmsPdaUpdateEntryBillLinesInDTO();
        try {
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_DETAILIDS_NOT_NULL , e.getMessage());
        }
        try {
            List<Long> list = new ArrayList<>();
            list.add(22l);
            param.setDetailIds(list);
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.POSITION_IS_EMPTY , e.getMessage());
        }
        try {
            param.setPositionNo(2222l);
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCANDATE_IS_EMPTY , e.getMessage());
        }
        try {
            param.setScannedTime(new Date());
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            param.setScanneder("杨昆10277404");
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
        }
        try {
            PowerMockito.when(wmsPadRepository.updateEntryBillLine(Mockito.any())).thenReturn(0l);
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            PowerMockito.when(wmsPadRepository.updateEntryBillLine(Mockito.any())).thenReturn(1l);
            wmsPdaService.updateMesEntryBillLinesProcess(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }

        WmsPdaUpdateEntryBillInDTO dto1 = new WmsPdaUpdateEntryBillInDTO();
        try {
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_NOT_NULL , e.getMessage());
        }
        try {
            dto1.setBillNo("22233l");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_STATUS_NOT_NULL , e.getMessage());
        }
        try {
            dto1.setStatus("233434443434444444444444444444444444444");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_STATUS_NOT_NULL , e.getMessage());
        }
        try {
            dto1.setStatus("2222");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            dto1.setReturnReason("56666666666666666666666699888888888888888888888888888899666669999996ghfh----y5655550--0-0-5555555777755990090-9-09-9090-90-5555555555555555555555555555555555555fhfghfhfhf56666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666666--898989889896666666666");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_RETURN_REASON_GT200 , e.getMessage());
        }
        try {
            dto1.setReturnReason("344334");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            dto1.setReturnBy("3333333333333333300033333333333367777777777777777777");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_RETURNBY_GT20 , e.getMessage());
        }
        try {
            dto1.setReturnBy("43434");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            dto1.setPostBy("343444444444444444444444444444444444444444444444443333333333333");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_POSTBY_GT20 , e.getMessage());
        }
        try {
            dto1.setPostBy("2334433434");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            dto1.setSubmitBy("34444444444444444444444444444444444444444444444444444444444444");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_SUBMITBY_GT20 , e.getMessage());
        }
        try {
            dto1.setSubmitBy("343");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            dto1.setReceiveBy("34444444444444444444444444444444444444444444444444444444444444");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_RECEIVEBY_GT20 , e.getMessage());
        }
        try {
            dto1.setReceiveBy("343");
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            PowerMockito.when(wmsPadRepository.updateEntryBill(Mockito.any())).thenReturn(0l);
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            PowerMockito.when(wmsPadRepository.updateEntryBill(Mockito.any())).thenReturn(1l);
            wmsPdaService.updateMesEntryBillProcess(dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }

        WmsPdaqueryScanAndBillTotalInDTO dto2 = new WmsPdaqueryScanAndBillTotalInDTO();
        try {
            wmsPdaService.queryScanAndBillTotalProcess(dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ENTRY_BILL_NOT_NULL , e.getMessage());
        }
        try {
            dto2.setBillNo("22222");
            wmsPdaService.queryScanAndBillTotalProcess(dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }

        List<Long> result = new ArrayList<>();
        try {
            PowerMockito.when(wmsPadRepository.queryScanAndBillTotal(Mockito.any())).thenReturn(result);
            wmsPdaService.queryScanAndBillTotalProcess(dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }
        try {
            result.add(3l);
            result.add(4l);
            result.add(5l);
            result.add(6l);
            PowerMockito.when(wmsPadRepository.queryScanAndBillTotal(Mockito.any())).thenReturn(result);
            wmsPdaService.queryScanAndBillTotalProcess(dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPDATE_ROWS_IS_ZERO , e.getMessage());
        }

        WmsPdaQueryEntryBillInfoInDTO dto3 = new WmsPdaQueryEntryBillInfoInDTO();
        try {
            wmsPdaService.queryEntryBillInfoProcess(dto3);

        } catch (Exception e) {
            Assert.assertEquals(MessageId.BILLNO_OR_PALLETNO_NOT_NULL , e.getMessage());
        }
        try {
            dto3.setScanNo("33");
            wmsPdaService.queryEntryBillInfoProcess(dto3);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.STOCKNO_NOT_NULL , e.getMessage());
        }
        try {
            dto3.setScanNo("333");
            wmsPdaService.queryEntryBillInfoProcess(dto3);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.STOCKNO_NOT_NULL , e.getMessage());
        }
        try {
            List<WmsPdaQueryEntryBillInfoOutDTO> result2 = new ArrayList<>();
            PowerMockito.when(wmsPadRepository.queryEntryBillInfo(Mockito.any())).thenReturn(result2);
            wmsPdaService.queryEntryBillInfoProcess(dto3);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.STOCKNO_NOT_NULL , e.getMessage());
        }
    }
}
