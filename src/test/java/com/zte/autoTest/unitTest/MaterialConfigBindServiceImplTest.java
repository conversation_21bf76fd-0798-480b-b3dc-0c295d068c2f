package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MaterialConfigBindServiceImpl;
import com.zte.domain.model.datawb.MaterialConfigBindRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
public class MaterialConfigBindServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private MaterialConfigBindServiceImpl materialConfigBindService;

    @Mock
    private MaterialConfigBindRepository repository;

    @Test
    public void getTheCompletionTimeOfInboundAccounting() {
        materialConfigBindService.getTheCompletionTimeOfInboundAccounting("2",1);
        Assert.assertNotNull(materialConfigBindService.getTheCompletionTimeOfInboundAccounting("",1));
    }
}
