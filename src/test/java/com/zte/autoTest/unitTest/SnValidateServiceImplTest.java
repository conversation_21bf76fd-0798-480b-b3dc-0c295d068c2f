package com.zte.autoTest.unitTest;

import com.zte.application.datawb.SequenceCodeSFCService;
import com.zte.application.datawb.impl.BaItemServiceImpl;
import com.zte.application.datawb.impl.ProdPlanServiceImpl;
import com.zte.application.datawb.impl.SnValidateServiceImpl;
import com.zte.domain.model.BaItem;
import com.zte.domain.model.BaItemRepository;
import com.zte.interfaces.dto.SnValidateDto;
import com.zte.itp.msa.core.model.ServiceData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyString;

/**
 * @Author: panXu
 * @Date: 2020/8/12 17:32
 * @Description:
 */
@RunWith(PowerMockRunner.class)
public class SnValidateServiceImplTest {

    @InjectMocks
    private SnValidateServiceImpl snValidateService;

    @Mock
    private ProdPlanServiceImpl prodPlanServiceImpl;

    @InjectMocks
    private BaItemServiceImpl baItemServiceImpl;

    @Mock
    private BaItemRepository baItemRepository;
    @Mock
    private SequenceCodeSFCService sequenceCodeSFCService;
    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void createData() throws Exception {
        List<SnValidateDto> dtoList = new ArrayList<>();
        SnValidateDto snValidateDto = new SnValidateDto();
        snValidateDto.setMainProdPlanId("7039122");
        snValidateDto.setSubProdPlanId("7151512");
        snValidateDto.setSubSn("729792900494");
        snValidateDto.setWorkOrderSource("STEP");
        dtoList.add(snValidateDto);

        ServiceData<BaItem> baRet = new ServiceData<>();
        PowerMockito.when(prodPlanServiceImpl.getItemInfo(snValidateDto)).thenReturn(baRet);
        snValidateService.getItemInfo(dtoList);
        PowerMockito.when(sequenceCodeSFCService.getBasBarcodeByBarcode(anyString())).thenReturn(new BaItem());
        snValidateDto.setWorkOrderSource("WMES");
        Assert.assertNotNull(snValidateService.getItemInfo(dtoList));
    }

    @Test
    public void dealAsSequenceCode(){
        SnValidateDto dto = new SnValidateDto();
        dto.setSubSn("123456789012");
        Assert.assertNull(snValidateService.dealAsSequenceCode(dto));
    }
}
