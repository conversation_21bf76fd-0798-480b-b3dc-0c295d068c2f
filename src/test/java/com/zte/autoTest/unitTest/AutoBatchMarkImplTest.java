package com.zte.autoTest.unitTest;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.datawb.impl.AutoBatchMarkImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.CfgCodeRuleItemRepository;
import com.zte.domain.model.datawb.PDVMBarCodeLMSRepository;
import com.zte.domain.model.datawb.RecalculationConvergenceRepository;
import com.zte.domain.model.datawb.WmesWerpConEntityTraceRepository;
import com.zte.interfaces.dto.AutoBatchMarkInDTO;
import com.zte.interfaces.dto.AutoBatchMarkTaskDTO;
import com.zte.interfaces.dto.CPQDDTO;
import com.zte.interfaces.dto.GbomCsgInfosDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.km.udm.model.ServiceData;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class, HttpClientUtil.class})
@PowerMockIgnore("javax.net.ssl.*")
public class AutoBatchMarkImplTest extends BaseTestCase {

    @InjectMocks
    private AutoBatchMarkImpl autoBatchMarkImpl;
    @Mock
    private WmesWerpConEntityTraceRepository wmesWerpConEntityTraceRepository;
    @Mock
    private PDVMBarCodeLMSRepository pdvmBarCodeLMSRepository;
    @Mock
    private RecalculationConvergenceRepository recalculationConvergenceRepository;
    @Mock
    private CfgCodeRuleItemRepository cfgCodeRuleItemRepository;


    @Test
    public void AutoBatchMarkService() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        GbomCsgInfosDTO csgDto = new GbomCsgInfosDTO();
        csgDto.setTotal(1);
        csgDto.setCurrent(1);
        csgDto.setRows(new ArrayList<>());

        ServiceData<GbomCsgInfosDTO> sd = new ServiceData<GbomCsgInfosDTO>();
        sd.setBo(csgDto);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"gbomCsgInfos\": {\n" +
                "            \"current\": 1,\n" +
                "            \"total\": 1,\n" +
                "            \"rows\": [\n" +
                "            ]\n" +
                "        },\n" +
                "        \"csgExceptionItems\": null\n" +
                "    },\n" +
                "    \"other\": null\n" +
                "}");
        AutoBatchMarkInDTO dto =new AutoBatchMarkInDTO();
        try {
            dto.setSessionId("");
            autoBatchMarkImpl.autoBatchMarkProcess(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_TASKNAME, e.getMessage());
        }
        try{
            dto.setSessionId("1111");
            autoBatchMarkImpl.autoBatchMarkProcess(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_TASKNAME, e.getMessage());
        }
        try{
            dto.setSessionId("535206");
            List<AutoBatchMarkTaskDTO> sessionTaskesDTO =new ArrayList<>();
            AutoBatchMarkTaskDTO dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952328");
            dto1.setParentConfigDetailId("1274952327");
            dto1.setMark("");
            sessionTaskesDTO.add(dto1);
            dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952330");
            dto1.setParentConfigDetailId("1274952329");
            dto1.setMark("");
            sessionTaskesDTO.add(dto1);
            PowerMockito.when(wmesWerpConEntityTraceRepository.getSessionBoqNoScanTask(any())).thenReturn(sessionTaskesDTO);
            autoBatchMarkImpl.autoBatchMarkProcess(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_TASKNAME, e.getMessage());
        }


        try{
            dto.setSessionId("535206");
            List<AutoBatchMarkTaskDTO> sessionTaskesDTO =new ArrayList<>();
            AutoBatchMarkTaskDTO dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952328");
            dto1.setParentConfigDetailId("1274952327");
            dto1.setMark("");
            sessionTaskesDTO.add(dto1);
            PowerMockito.when(wmesWerpConEntityTraceRepository.getSessionBoqNoScanTask(any())).thenReturn(sessionTaskesDTO);

            PowerMockito.when(pdvmBarCodeLMSRepository.getConfigDetails(any())).thenReturn(sessionTaskesDTO);
            autoBatchMarkImpl.autoBatchMarkProcess(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_TASKNAME, e.getMessage());
        }

        try{
            List<String> list =new ArrayList<>();
            list.add("1274952328");
            autoBatchMarkImpl.getCPQD("",list);
            list = new ArrayList<>();
            autoBatchMarkImpl.getCPQD("3434434",list);
        }catch (Exception ex){}
        try{
            List<String> list =new ArrayList<>();
            list.add("1274952328");
            list.add("1274952330");
            list.add("1274952332");
            list.add("1274952334");
            list.add("1274952336");
            String url ="http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<CPQDDTO> listCPQD = autoBatchMarkImpl.getCPQD("1MY20221213001D",list);
            List<AutoBatchMarkTaskDTO> noMarkTaskesDTO = new ArrayList<>();
            AutoBatchMarkTaskDTO dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952328");
            dto1.setParentConfigDetailId("1274952327");
            dto1.setMark("");
            noMarkTaskesDTO.add(dto1);
            dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952330");
            dto1.setParentConfigDetailId("1274952329");
            dto1.setMark("");
            noMarkTaskesDTO.add(dto1);
            dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952332");
            dto1.setParentConfigDetailId("1274952331");
            dto1.setMark("");
            noMarkTaskesDTO.add(dto1);
            dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952334");
            dto1.setParentConfigDetailId("1274952333");
            dto1.setMark("");
            noMarkTaskesDTO.add(dto1);
            dto1 =new AutoBatchMarkTaskDTO();
            dto1.setEntityId("14257685");
            dto1.setEntityName("5GGNBUAI-I20221200018");
            dto1.setMfgSiteId("209332424");
            dto1.setOrg("4437");
            dto1.setConfigDetailId("1274952336");
            dto1.setParentConfigDetailId("1274952335");
            dto1.setMark("");
            noMarkTaskesDTO.add(dto1);
            autoBatchMarkImpl.handleAutoMark(listCPQD,noMarkTaskesDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_TASKNAME, e.getMessage());
        }

        try{

        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_TASKNAME, e.getMessage());
        }


        // insert into app_mes.cpm_contract_entities (ENTITYID, ENTITYNAME, MFGSITEID, ORG, CONFIGDETAILID, PARENTCONFIGDETAILID, MARK)
        // values (14257685, '5GGNBUAI-I20221200018', 209332424, 4437, 1274952328, 1274952327, null);
        // values (14257685, '5GGNBUAI-I20221200018', 209332424, 4437, 1274952330, 1274952329, null);
        // values (14257685, '5GGNBUAI-I20221200018', 209332424, 4437, 1274952332, 1274952331, null);
        // values (14257685, '5GGNBUAI-I20221200018', 209332424, 4437, 1274952334, 1274952333, null);
        // values (14257685, '5GGNBUAI-I20221200018', 209332424, 4437, 1274952336, 1274952335, null);
    }
}
