package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BaIeParamsServiceImpl;
import com.zte.domain.model.BaIeParamsRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class BaIeParamsServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    BaIeParamsServiceImpl service;

    @Mock
    BaIeParamsRepository repository;

    @Test
    public void getBaIeParams() {
        Assert.assertNotNull(service.getBaIeParams());
    }
}