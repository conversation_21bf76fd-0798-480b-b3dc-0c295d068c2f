package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.ApsItemOrgCivServiceImpl;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.MesBoxConvergenceRepository;
import com.zte.domain.model.stepdt.ApsItemOrgCivRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BarcodeListDTX;
import com.zte.interfaces.dto.BarcodeVO;
import com.zte.interfaces.dto.MesBoxConvergenceDTO;
import com.zte.interfaces.stepdt.dto.ApsItemOrgCivDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import com.zte.application.datawb.impl.MesBoxConvergenceServiceImpl;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;


@PrepareForTest({MESHttpHelper.class, HttpClientUtil.class, JacksonJsonConverUtil.class, HttpRemoteService.class, CenterfactoryRemoteService.class})
@RunWith(PowerMockRunner.class)
/**
 * <AUTHOR> 10292636
 * @Date 2021/9/10 23
 * @description:
 */
public class MesBoxConvergenceServiceImplTest extends BaseTestCase {
    @InjectMocks
    private MesBoxConvergenceServiceImpl mesBoxConvergenceServiceImpl;
    @Mock
    private MesBoxConvergenceRepository mesBoxConvergenceRepository;

    @Test
    public void insertProcess() {

        Map<String, Object> dt = new HashMap<>();
        PowerMockito.doNothing().when(mesBoxConvergenceRepository).insertProcess(dt);
        mesBoxConvergenceServiceImpl.insertProcess(dt);
        Assert.assertNotNull(dt);


    }

    @Test
    public void getNewLastDate() {
        MesBoxConvergenceDTO dto = new MesBoxConvergenceDTO();
        List<MesBoxConvergenceDTO> dtoListDto = new ArrayList<>();
        long tCurentTime = System.currentTimeMillis();
        Timestamp ssbLastUpdateTime = new Timestamp(tCurentTime);
        dto.setSendTime(ssbLastUpdateTime);
        dtoListDto.add(dto);
        String dtx = "KAFKA_MES_BOX";
        PowerMockito.when(mesBoxConvergenceRepository.getNewLastDate(dtx)).thenReturn(dtoListDto);
        MesBoxConvergenceDTO dtoNew = new MesBoxConvergenceDTO();
        dtoNew.setSendTime(ssbLastUpdateTime);
        PowerMockito.doNothing().when(mesBoxConvergenceRepository).updateNewJobTime(dtoNew);
        //PowerMockito.when(mesBoxConvergenceRepository.updateNewJobTime(dtoNew))
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getNewLastDate());

    }

    @Test
    public void getInfoStockInfor() {
        try {
            List<MesBoxConvergenceDTO> dt1 = new ArrayList<>();
            MesBoxConvergenceDTO mesBoxConvergenceDTO = new MesBoxConvergenceDTO();
            PowerMockito.when(mesBoxConvergenceRepository.getInfoStockInfor(mesBoxConvergenceDTO)).thenReturn(dt1);
            // mesBoxConvergenceServiceImpl.getInfoStockInfor(mesBoxConvergenceDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void setKafaMesBox() {
        MesBoxConvergenceDTO mesBoxConvergenceDTO = new MesBoxConvergenceDTO();
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.setKafaMesBox(mesBoxConvergenceDTO));
    }

    @Test
    public void getOneHundred() {
        List<MesBoxConvergenceDTO> dtoOneList = new ArrayList<>();
        for (int i = 0; i < 999; i++) {
            MesBoxConvergenceDTO dto = new MesBoxConvergenceDTO();
            dtoOneList.add(dto);
        }
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getOneHundred(dtoOneList));
    }

    @Test
    public void insertKafaKa() {
        try {
            List<MesBoxConvergenceDTO> dtoOneList = new ArrayList<>();
            MesBoxConvergenceDTO dxst = new MesBoxConvergenceDTO();

            for (int i = 0; i < 999; i++) {
                MesBoxConvergenceDTO dto = new MesBoxConvergenceDTO();
                dtoOneList.add(dto);
            }
            //   mesBoxConvergenceRepository.getInfoStockInfor(mesBoxConvergenceDTO);
            PowerMockito.doNothing().when(mesBoxConvergenceRepository).insertKafKaTable(dtoOneList);
            //  PowerMockito.when(mesBoxConvergenceRepository.insertKafKaTable(dxst)).thenReturn(dt1);
            PowerMockito.when(mesBoxConvergenceRepository.getInfoStockInfor(dxst)).thenReturn(dtoOneList);
            mesBoxConvergenceServiceImpl.insertKafaKa(dxst);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getUuidInfor() {
        try {
            List<MesBoxConvergenceDTO> dtoList = new ArrayList<>();
            MesBoxConvergenceDTO mesBoxConvergenceDTO = new MesBoxConvergenceDTO();
            PowerMockito.when(mesBoxConvergenceRepository.getUuidInfor(mesBoxConvergenceDTO)).thenReturn(dtoList);
            //  mesBoxConvergenceServiceImpl.getUuidInfor(mesBoxConvergenceDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    //过滤掉不在
    @Test
    public void filterUUid() {
        List<MesBoxConvergenceDTO> dtoFilter = new ArrayList<>();
        for (int i = 0; i < 999; i++) {
            MesBoxConvergenceDTO dtoNew = new MesBoxConvergenceDTO();
            dtoFilter.add(dtoNew);
        }

        Assert.assertNotNull(mesBoxConvergenceServiceImpl.filterUUid(dtoFilter));

    }

    @Test
    public void getOneHundredListDto() {
        List<MesBoxConvergenceDTO> dtoFilter = new ArrayList<>();
        for (int i = 0; i < 999; i++) {
            MesBoxConvergenceDTO dtoNew = new MesBoxConvergenceDTO();
            dtoFilter.add(dtoNew);
        }
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getOneHundredListDto(dtoFilter));
    }

    @Test
    public void formatInStr() {
        List<String> strList = new ArrayList<>();
        for (int i = 0; i < 90; i++) {
            strList.add("B12345");
        }
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.formatInStr(strList));

    }

    @Test
    public void getMapData() {
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getMapData("B12334"));

    }

    @Test
    public void getInforMesBox() {

        try {
            mesBoxConvergenceServiceImpl.getInforMesBox();
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getThreeTimeContain() {
        MesBoxConvergenceDTO mesBoxConvergenceDTO = new MesBoxConvergenceDTO();
        // mesBoxConvergenceDTO.setSendTime();
        List<MesBoxConvergenceDTO> dtos = new ArrayList<>();
        PowerMockito.when(mesBoxConvergenceRepository.getUuidInfor(mesBoxConvergenceDTO)).thenReturn(dtos);
        PowerMockito.when(mesBoxConvergenceRepository.getInfoStockInfor(mesBoxConvergenceDTO)).thenReturn(dtos);

        long tCurentTime = System.currentTimeMillis();
        Timestamp ssbLastUpdateTime = new Timestamp(tCurentTime);


        mesBoxConvergenceDTO.setSendTime(ssbLastUpdateTime);
        mesBoxConvergenceServiceImpl.getThreeTimeContain(mesBoxConvergenceDTO, true);
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getThreeTimeContain(mesBoxConvergenceDTO, false));

        //  mesBoxConvergenceRepository.getUuidInfor(mesBoxConvergenceDTO);
    }

    @Test
    public void getDecWord() {
        List<MesBoxConvergenceDTO> dtoA = new ArrayList<>();
        MesBoxConvergenceDTO dto1 = new MesBoxConvergenceDTO();
        long tCurentTime = System.currentTimeMillis();
        Timestamp ssbLastUpdateTime = new Timestamp(tCurentTime);
        dto1.setAfterTime(ssbLastUpdateTime);
        dtoA.add(dto1);
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getDecWord(dtoA));
    }

    @Test
    public void setNotSystemDate() {
        long dt = System.currentTimeMillis() - 10;
        long dtx = 2 * System.currentTimeMillis();
        Timestamp beforeTime = new Timestamp(dt);
        Timestamp afterTime = new Timestamp(dtx);
        MesBoxConvergenceDTO mesBoxConvergenceDTONew = new MesBoxConvergenceDTO();
        PowerMockito.doNothing().when(mesBoxConvergenceRepository).updateNewJobTime(mesBoxConvergenceDTONew);
        mesBoxConvergenceServiceImpl.setNotSystemDate(beforeTime);
        mesBoxConvergenceServiceImpl.setNotSystemDate(afterTime);
        Assert.assertNotNull(beforeTime);
        Assert.assertNotNull(afterTime);
    }

    @Test
    public void setReapeatComput() {
        MesBoxConvergenceDTO mesBoxConvergenceDTO = new MesBoxConvergenceDTO();
        mesBoxConvergenceDTO.setSendTime(new Timestamp(System.currentTimeMillis() + 18000));
        mesBoxConvergenceDTO.setAfterTime(new Timestamp(System.currentTimeMillis() + 28000));
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.setReapeatComput(mesBoxConvergenceDTO, true));
    }

    @Test
    public void getBarcodeCollection() {
        List<BarcodeListDTX> dtBarcode = new ArrayList<>();
        BarcodeListDTX dtxNew = new BarcodeListDTX();
        dtxNew.setBarcode("12333");
        dtBarcode.add(dtxNew);
        List<MesBoxConvergenceDTO> dtEvery = new ArrayList<>();
        MesBoxConvergenceDTO dtNew = new MesBoxConvergenceDTO();
        dtEvery.add(dtNew);
        PowerMockito.when(mesBoxConvergenceRepository.selectInterBarcode(dtEvery)).thenReturn(dtBarcode);
        Assert.assertNotNull(mesBoxConvergenceServiceImpl.getBarcodeCollection(dtEvery));
    }

    @Test
    public void postBarcodeCenter() {
        try {
            List<String> dtBarcodeListArr = new ArrayList<>();
            String postMdmUrl = "12333";
            Map<String, String> headerParamsMap = new HashMap<>();
            mesBoxConvergenceServiceImpl.postBarcodeCenter(dtBarcodeListArr, postMdmUrl, headerParamsMap);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void updateUuidTable() {
        try {
            List<BarcodeVO> barcodeVOListDT = new ArrayList<>();
            BarcodeVO dtx = new BarcodeVO();
            barcodeVOListDT.add(dtx);
            mesBoxConvergenceServiceImpl.updateUuidTable(barcodeVOListDT);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getBarcodeCenter() {
        try {
            List<BarcodeListDTX> barcodeVOListDT = new ArrayList<>();
            BarcodeListDTX dtx = new BarcodeListDTX();
            barcodeVOListDT.add(dtx);
            List<SysLookupValues> lookupValuesList = new ArrayList<>();
            SysLookupValues dtxx = new SysLookupValues();
            dtxx.setLookupMeaning("B3D18A23CBB9DD5B56E58F011E8D483EE9D6870553161776692FA5A94B73F439");
            dtxx.setLookupCode(new BigDecimal(7192001));
            lookupValuesList.add(dtxx);

            SysLookupValues dtxxx = new SysLookupValues();
            dtxxx.setLookupMeaning("http://test.ibarcode.zte.com.cn/zte-iss-barcodecenter-barcode/barcode/batchQuery");
            dtxxx.setLookupCode(new BigDecimal(7192002));
            lookupValuesList.add(dtxxx);
            PowerMockito.mockStatic(CenterfactoryRemoteService.class);
            PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(lookupValuesList);
            dtxx.setLookupMeaning("1");
            PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(lookupValuesList);
            mesBoxConvergenceServiceImpl.getBarcodeCenter(barcodeVOListDT);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }
}
