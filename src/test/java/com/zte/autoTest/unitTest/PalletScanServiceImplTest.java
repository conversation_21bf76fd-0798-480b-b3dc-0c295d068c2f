package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.PalletScanServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.PalletScanRepository;
import com.zte.util.BaseTestCase;



import com.zte.application.datawb.ConfigMaterialBindService;
import com.zte.application.datawb.impl.ConfigMaterialBindServiceImpl;

import com.zte.common.utils.Constant;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.ConfigMaterialBindServiceRepository;
import com.zte.interfaces.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.runner.RunWith;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


import com.zte.util.BaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;

import static org.mockito.Matchers.anyMap;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class PalletScanServiceImplTest extends BaseTestCase {
    @InjectMocks
    PalletScanServiceImpl palletScanServiceImpl;

    @Mock
    private PalletScanRepository palletScanRepository;

    @Test
    public void checkStatus() throws Exception {
        boolean result = palletScanServiceImpl.checkStatus(null);
        Assert.assertEquals(false, result);

        PalletScanOutDTO msg = new PalletScanOutDTO();
        result = palletScanServiceImpl.checkStatus(msg);
        Assert.assertEquals(false, result);


        ErrDTO errDTO = new ErrDTO();
        msg.setErrDTO(errDTO);
        result = palletScanServiceImpl.checkStatus(msg);
        Assert.assertEquals(false, result);

        errDTO.setProcessStatus(Constant.KEY_E);
        result = palletScanServiceImpl.checkStatus(msg);
        Assert.assertEquals(true, result);
    }
    @Test
    public void palletScan() throws Exception {
        try {
            /**
             * <AUTHOR>
             * 添加单元测试覆盖率
             **/
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            PalletScanInDTO params=new PalletScanInDTO();
            PalletScanOutDTO res=new PalletScanOutDTO();
            ErrDTO err=new ErrDTO();

            params.setBillNo("B171133828517");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("10011584");//正确
            PowerMockito.when(palletScanRepository.getUserId(anyObject())).thenReturn("4727");
            palletScanServiceImpl.palletScan(params);

            params.setBillNo("B220700477558");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("10011584");//正确
            PowerMockito.when(palletScanRepository.getUserId(anyObject())).thenReturn("4727");
            palletScanServiceImpl.palletScan(params);

            params.setBillNo("B170332560332");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("10011584");//正确
            PowerMockito.when(palletScanRepository.getUserId(anyObject())).thenReturn("4727");
            palletScanServiceImpl.palletScan(params);


            PalletScanOutDTO flags=new PalletScanOutDTO();
            ErrDTO e=new ErrDTO();
            e.setProcessStatus(Constant.KEY_E);
            e.setCode(Constant.SUCCESS_CODE);
            flags.setErrDTO(e);
            palletScanServiceImpl.varify(flags);

            params.setBillNo("B170332560332");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("");
            palletScanServiceImpl.paramInCheck(params);
            params.setBillNo("B170332560332");
            params.setScanBy("10011584");
            params.setPalletNo("TP2104M018536");
            params.setOrganizationId("");
            palletScanServiceImpl.paramInCheck(params);
            params.setBillNo("");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("10011584");
            palletScanServiceImpl.paramInCheck(params);
            params.setBillNo("B170332560332");
            params.setOrganizationId("635");
            params.setPalletNo("");
            params.setScanBy("10011584");
            palletScanServiceImpl.paramInCheck(params);
            params.setBillNo("B170332560332");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("100115");//错误
            PowerMockito.when(palletScanRepository.getUserId(anyObject())).thenReturn("");
            palletScanServiceImpl.paramInCheck(params);


            params.setBillNo("B170332560332");
            params.setOrganizationId("635");
            params.setPalletNo("TP2104M018536");
            params.setScanBy("10011584");//正确
            PowerMockito.when(palletScanRepository.getUserId(anyObject())).thenReturn("4727");
            palletScanServiceImpl.paramInCheck(params);

           //err.setProcessStatus("S");
           //res.setErrDTO(err);
           //PowerMockito.when(palletScanServiceImpl.paramInCheck(params)).thenReturn(res);
           // palletScanServiceImpl.palletScan(params);// 1 check

        /**
         *  <AUTHOR>
         *  1, 根据托盘号查询托盘信息维护中的数据
         *  @params dto 入参实体类
         */
        List<PalletInforManageDTO> palletInforManageDTO=new ArrayList<>();
        PalletInforManageDTO a=new PalletInforManageDTO();
        a.setBillNumber("B170332560332");
        palletInforManageDTO.add(a);
        PowerMockito.when(palletScanRepository.getPalletInfoManage(params)).thenReturn(palletInforManageDTO);
        palletScanServiceImpl.getPalletInfoManage(params);
        //palletScanServiceImpl.palletScan(params);

        /**
         *  <AUTHOR>
         *  2, 判断托盘号没有扫描过，保存扫描信息
         *  @params dto 入参实体类
         */

        Map<String, Object> map = new HashMap<>();
        map.put("userId",4727);
        map.put("isScan", "Y");
        map.put("isScanEnd", "N");
        map.put("palletId",123);
        map.put("organizationId",635);
        PowerMockito.when(palletScanRepository.savePalletInfo(map)).thenReturn(1);
        palletScanServiceImpl.savePalletScanInfo(params,a,"4727");
        //palletScanServiceImpl.palletScan(params);

        /**
         *  <AUTHOR>
         *  3, 获取装箱单信息
         *  @params dto 入参实体类
         */

        map.put("billNo","123");
        map.put("organizationId",635);

        List<BoxBillsDTO> boxBillsDTO=new ArrayList<>();
        BoxBillsDTO b=new BoxBillsDTO();
        b.setEntityName("1232");
        boxBillsDTO.add(b);
        PowerMockito.when(palletScanRepository.getBoxBillsInfo(map)).thenReturn(boxBillsDTO);
        palletScanServiceImpl.getBoxBillsInfo(params);
        //palletScanServiceImpl.palletScan(params);
        /**
         *
         *  3.1  3.2 校验装箱单信息
         *
         */
        List<BoxBillsDTO> boxBillsDTO2=new ArrayList<>();
        params.setBillNo("B170332560332");
        palletScanServiceImpl.validateMfgSite(boxBillsDTO2,palletInforManageDTO,params);

        params.setBillNo("B170332560332");
        b.setMfgSiteId("123");
        a.setMfgSiteId(123);
        a.setDeliveryMode("1");
        palletScanServiceImpl.validateMfgSite(boxBillsDTO,palletInforManageDTO,params);
        a.setMfgSiteId(123456);
        a.setDeliveryMode("1");
        palletScanServiceImpl.validateMfgSite(boxBillsDTO,palletInforManageDTO,params);


        //校验箱号对应的任务号3.3 ---3.7
        a.setEntityName("0332560332");
        palletScanServiceImpl.validateEntityName(boxBillsDTO, palletInforManageDTO, params);
        a.setEntityName("1232");
        palletScanServiceImpl.validateEntityName(boxBillsDTO, palletInforManageDTO, params);

        List<String> list=new ArrayList<>();
        list.add("123");
        PowerMockito.when(palletScanRepository.getBoxBillScaned(params)).thenReturn(list);
        params.setPalletNo("123");
        palletScanServiceImpl.validateEntityName(boxBillsDTO, palletInforManageDTO, params);
        palletScanServiceImpl.validateEntityName(null, palletInforManageDTO, params);
        palletScanServiceImpl.showMessage(list,params);


        params.setPalletNo("12");
        PowerMockito.when(palletScanRepository.getBoxBillScaned(params)).thenReturn(list);
        palletScanServiceImpl.validateEntityName(boxBillsDTO, palletInforManageDTO, params);
        palletScanServiceImpl.showMessage(list,params);
        //palletScanServiceImpl.palletScan(params);
        a.setPalletNo("12");
        a.setPalletId(12);
        a.setStdStackNum(12);
        a.setIsScanEnd("Y");
        a.setFillEmptyBoxNum(2);
        PowerMockito.when(palletScanRepository.getPalletInfoByPalletNo(params)).thenReturn(palletInforManageDTO);
        palletScanServiceImpl.getPalletInfoByPalletNo(params);//--完美范例，XML写一次即可
        palletScanServiceImpl.getPalletByNo(palletInforManageDTO,params);//--完美范例，先写嵌套的函数
        palletScanServiceImpl.validateStackNum(params);//完美范例，最后写外层函数

        List<PalletInforManageDTO> palletInforManageDTO2=new ArrayList<>();
        PowerMockito.when(palletScanRepository.getPalletInfoByPalletNo(params)).thenReturn(palletInforManageDTO2);
        palletScanServiceImpl.validateStackNum(params);
        //palletScanServiceImpl.palletScan(params);
        //还有覆盖的空间

        //3.6
        ArrayList<PalletInforManageDTO> palletInforManageDTO3=new ArrayList<>();
        PalletInforManageDTO p=new PalletInforManageDTO();
        p.setPalletNo("12");
        p.setBillNumber("Q000000000000");
        palletInforManageDTO3.add(p);
        params.setPalletNo("12");
        params.setBillNo("123");
        PowerMockito.when(palletScanRepository.getBoxBillScanedByPalletNo(params)).thenReturn(palletInforManageDTO3);
        palletScanServiceImpl.getBoxBillScanedByPalletNo(params,"是",0,1,1);
        palletScanServiceImpl.getBoxBillScanedByPalletNo(params,"是",1,0,1);
        palletScanServiceImpl.getBoxBillScanedByPalletNo(params,"是",11,0,11);
        palletScanServiceImpl.getBoxBillScanedByPalletNo(params,"N",11,0,11);
        //palletScanServiceImpl.palletScan(params);
/**
 *
 *  3.7 校验扫描空箱的场景
 */
        params.setBillNo("Q000000000000");
        p.setPalletId(123);
        p.setStdStackNum(2);
        p.setIsScanEnd("Y");
        p.setFillEmptyBoxNum(2);
        PowerMockito.when(palletScanRepository.getPalletInfoByPalletNo(params)).thenReturn(palletInforManageDTO3);
        palletScanServiceImpl.validateEmptyBox(params);

        params.setPalletNo("666");
        palletScanServiceImpl.validateEmptyBox(params);
        //palletScanServiceImpl.palletScan(params);
        ArrayList<PalletInforManageDTO> palletInforManageDTO33=new ArrayList<>();
        PowerMockito.when(palletScanRepository.getPalletInfoByPalletNo(params)).thenReturn(palletInforManageDTO33);
        palletScanServiceImpl.validateEmptyBox(params);
        //palletScanServiceImpl.palletScan(params);
        params.setBillNo("Q");
        PowerMockito.when(palletScanRepository.getPalletInfoByPalletNo(params)).thenReturn(palletInforManageDTO33);
        palletScanServiceImpl.validateEmptyBox(params);

        // 3.7.1 根据托盘号获取当前托盘下面的所有已扫描装箱单信息 包括空箱扫描
        p.setBillNumber("Q000000000000");
        params.setPalletNo("12");
        //写一次就行，后面都会有记录
        PowerMockito.when(palletScanRepository.getBoxBillScanedByPalletNo(params)).thenReturn(palletInforManageDTO3);
        palletScanServiceImpl.getBoxBillScanByPalletNo(params,"Y",1,11);
        palletScanServiceImpl.getBoxBillScanByPalletNo(params,"Y",11,1);
        palletScanServiceImpl.getBoxBillScanByPalletNo(params,"Y",11,11);
        palletScanServiceImpl.getBoxBillScanByPalletNo(params,"是",11,11);
        //palletScanServiceImpl.palletScan(params);
        /**
         *  4.1
         *  获取当前装箱号的毛重
         *
         */
        List<Double> list2 =new ArrayList<>();
        list2.add(11.3);
        PowerMockito.when(palletScanRepository.getCurrentBoxWeight(params)).thenReturn(list2);
        palletScanServiceImpl.getCurrentBoxWeight(params);

        List<Double> list22 =new ArrayList<>();
        list22.add(0.0);
        PowerMockito.when(palletScanRepository.getCurrentBoxWeight(params)).thenReturn(list22);
        palletScanServiceImpl.getCurrentBoxWeight(params);

       //5 校验
        params.setPalletNo("12");
        p.setPalletNo("12");
        p.setScanNum("1/2");
        PowerMockito.when(palletScanRepository.getPalletInfoByPalletNo(params)).thenReturn(palletInforManageDTO3);
        ArrayList<PalletInforManageDTO> list3 = new ArrayList<PalletInforManageDTO>();
        PalletInforManageDTO d=new PalletInforManageDTO();
        d.setScanNum("1/2");
        list3.add(d);
        palletScanServiceImpl.validateNum(list3,params);
        palletScanServiceImpl.validateScanData(params);
        //palletScanServiceImpl.palletScan(params);
        d.setStdStackNum(1);
        palletScanServiceImpl.validateNum(list3,params);
        d.setDeliveryModeName("按站址发货");
        p.setBillNumber("Q000000000000");
        p.setNewSiteAddress("1");
        d.setBillNumber("Q");
        d.setNewSiteAddress("12");
        palletInforManageDTO3.add(d);
        PalletInforManageDTO dd=new PalletInforManageDTO();
        dd.setScanNum("1/2");
        dd.setBillNumber("Q");
        dd.setNewSiteAddress("123123");
        palletInforManageDTO3.add(dd);
        PowerMockito.when(palletScanRepository.getBoxBillScanedByPalletNo(params)).thenReturn(palletInforManageDTO3);
        palletScanServiceImpl.validateSite(list3,params);
        //palletScanServiceImpl.validateScanData(params);
        //palletScanServiceImpl.palletScan(params);

        d.setStdStackNum(1);
        palletScanServiceImpl.validateNum(list3,params);
        d.setDeliveryModeName("按站址发货");
        p.setBillNumber("Q000000000000");
        p.setNewSiteAddress("1");
        d.setBillNumber("Q");
        d.setNewSiteAddress("12");
        palletInforManageDTO3.add(d);
        PalletInforManageDTO ddd=new PalletInforManageDTO();
        ddd.setScanNum("1/2");
        ddd.setBillNumber("Q");
        ddd.setNewSiteAddress("123123");
        palletInforManageDTO3.add(ddd);
        PowerMockito.when(palletScanRepository.getBoxBillScanedByPalletNo(params)).thenReturn(palletInforManageDTO3);
       // palletScanServiceImpl.palletScan(params);
        d.setDeliveryModeName("按站址发货111");
        palletScanServiceImpl.validateSite(list3,params);
        palletScanServiceImpl.validateScanData(null);
        //palletScanServiceImpl.palletScan(params);

        //5.3
        Map<String, Object> maps=new HashMap<>();
        PowerMockito.when(palletScanRepository.savePalletScanEnd(anyMap())).thenReturn(1);
        palletScanServiceImpl.savePalletScanEnd(map);

            /**
             *  <AUTHOR>
             *  check箱号是否绑定托盘
             *  @params List<String> 入参实体类
             */

            params.setBillNo("B220700477558");
            params.setOrganizationId("635");
            palletScanServiceImpl.palletScan(params);
            palletScanRepository.checkBillNoIsBindPalletNo(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            params.setBillNo("B170332560332");
            params.setOrganizationId("635");
            palletScanServiceImpl.palletScan(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            params.setBillNo("Q000000000000");
            params.setOrganizationId("635");
            palletScanServiceImpl.palletScan(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            params.setBillNo("B170332560332");
            params.setOrganizationId("31");
            palletScanServiceImpl.palletScan(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            palletScanServiceImpl.checkBillNoIsBindPalletNo(params);
            List<String> palletNOS=new ArrayList<>();
            params.setBillNo("B171133828517");
            params.setOrganizationId("635");
            palletScanServiceImpl.palletScan(params);
            PowerMockito.when(palletScanRepository.checkBillNoIsBindPalletNo(params)).thenReturn(palletNOS);
            PowerMockito.when(palletScanServiceImpl.checkBillNoIsBindPalletNo(params)).thenReturn(null);

        }
        catch (Exception e)
        {
            Assert.assertNull(e.getMessage());
        }
    }
}
