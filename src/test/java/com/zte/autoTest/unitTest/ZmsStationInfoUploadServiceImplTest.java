package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.impl.ZmsStationInfoUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsStationInfoUploadRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsStationInfoUploadServiceImplTest {

    @InjectMocks
    private ZmsStationInfoUploadServiceImpl zmsStationInfoUploadService;
    @Mock
    private ZmsStationInfoUploadRepository zmsStationInfoUploadRepository;
    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;
    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private DataServiceClientV1 dataServiceClientV1;
    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class);
    }

    @Test
    public void stationInfoUpload() throws Exception {
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);

        StationInputDTO stationInputDTO = new StationInputDTO();
        stationInputDTO.setAutoFlag("Y");
        stationInputDTO.setEmpNo("10255258");
        List<String> snList = new ArrayList<>();
        snList.add("111");
        stationInputDTO.setSnList(snList);
        List<ZmsStationInfoDTO> list = new ArrayList<>();
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(Mockito.any(), Mockito.any())).thenReturn("40");
        PowerMockito.when(zmsStationInfoUploadRepository.getEntitySnList(stationInputDTO.getSnList(), stationInputDTO.getAutoFlag(), null)).thenReturn(list);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoUpload", stationInputDTO);


        ZmsStationInfoDTO zmsStationInfoDTO = new ZmsStationInfoDTO();
        zmsStationInfoDTO.setServerSn("************");
        zmsStationInfoDTO.setOrderId("S3TJ2017110102WCNE");
        zmsStationInfoDTO.setEndUser("中国ABCDD信集WXYDDDZZ");
        zmsStationInfoDTO.setEntityName("5GGNBRRU-I20201200016");
        list.add(zmsStationInfoDTO);
        PowerMockito.when(zmsStationInfoUploadRepository.getEntitySnList(stationInputDTO.getSnList(), stationInputDTO.getAutoFlag(), null)).thenReturn(list);

        String strToken = "1609408435_ccc26eb6e62b424d8ffd47bb48b9d5a3";
        String zsUrl = "https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/queryStationTestInfo";
        Whitebox.invokeMethod(zmsStationLogUploadService, "getToken");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200006)).thenReturn(zsUrl);

        List<ZmsStationInfoDTO> mesZmsStationInfoDTOList = new ArrayList<>();
        ZmsStationInfoDTO zmsStationInfoDTO1 = new ZmsStationInfoDTO();
        zmsStationInfoDTO1.setStationId("组装1668755236");
        zmsStationInfoDTO1.setName("组装");
        zmsStationInfoDTO1.setAttribute("组装");
        zmsStationInfoDTO1.setState("PASS");
        zmsStationInfoDTO1.setStartTime(1668755236);
        zmsStationInfoDTO1.setEndTime(1669637998);
        zmsStationInfoDTO1.setEntityName("5GGNBRRU-I20201200016");
        mesZmsStationInfoDTOList.add(zmsStationInfoDTO1);
        PowerMockito.when(zmsStationInfoUploadRepository.getZmsStationInfoList(list)).thenReturn(mesZmsStationInfoDTOList);

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(PART_CODE, zmsStationInfoDTO.getServerSn());
        Whitebox.invokeMethod(zmsStationLogUploadService, "getZsStationLog", paramMap, zsUrl, strToken);
        List<ZmsStationInfoUploadDTO> zsZmsStationInfoUploadDTOList = new ArrayList<>();
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zsZmsStationInfoUploadDTOList);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoUpload", stationInputDTO);

        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO.setServerSn("************");
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zsZmsStationInfoUploadDTOList);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoMapping", zsZmsStationInfoUploadDTOList, "5456");
        Whitebox.invokeMethod(zmsStationInfoUploadService, "insertZmsStationInfo", mesZmsStationInfoDTOList, zsZmsStationInfoUploadDTOList, list, "10255258");
        Whitebox.invokeMethod(zmsStationInfoUploadService, "pushToIMESB2B", mesZmsStationInfoDTOList, zsZmsStationInfoUploadDTOList, list, "10255258");
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoUpload", stationInputDTO);
        Assert.assertNotNull(stationInputDTO);
    }

    /* Started by AICoder, pid:c0a0fbc599d84c55882bac0f02b07e38 */
    @Test
    public void setCustomerItemsInfo() throws  Exception{
        ZmsStationItemDTO dto = null;
        zmsStationInfoUploadService.setCustomerItemsInfo(dto);
        Assert.assertEquals(true,true);

        dto = new ZmsStationItemDTO();
        zmsStationInfoUploadService.setCustomerItemsInfo(dto);
        Assert.assertEquals(true,true);

        ZmsStationProblemDTO problemDTO = new ZmsStationProblemDTO();
        problemDTO.setComponentItemcode("111111");
        List<ZmsStationProblemDTO> list = new ArrayList<>();
        list.add(problemDTO);
        dto.setProblems(list);

        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        zmsStationInfoUploadService.setCustomerItemsInfo(dto);
        Assert.assertEquals(true,true);

        CustomerItemsDTO itemsDTO = new CustomerItemsDTO();
        itemsDTO.setZteCode("111111");
        customerItemsDTOList.add(itemsDTO);
        zmsStationInfoUploadService.setCustomerItemsInfo(dto);
        Assert.assertEquals(true,true);
    }/* Ended by AICoder, pid:c0a0fbc599d84c55882bac0f02b07e38 */


    /* Started by AICoder, pid:816cbn4130mf5de14ae809afb088163f8de904f2 */
    @Test
    public void stationInfoMapping() throws Exception {

        List<ZmsStationInfoUploadDTO> zsZmsStationInfoUploadDTOList = new ArrayList<>();
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoMapping", zsZmsStationInfoUploadDTOList, "111");

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("气密测试工序");
        sysLookupValues.setDescription("保压");
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO = new ZmsStationInfoUploadDTO();
        List<ZmsStationItemDTO> zmsStationItemDTOList = new ArrayList<>();
        ZmsStationItemDTO zmsStationItemDTO = new ZmsStationItemDTO();
        zmsStationItemDTO.setName("3524525");
        zmsStationItemDTOList.add(zmsStationItemDTO);
        zmsStationInfoUploadDTO.setStations(zmsStationItemDTOList);
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoMapping", zsZmsStationInfoUploadDTOList, "111");

        zsZmsStationInfoUploadDTOList.clear();
        zmsStationItemDTOList.clear();
        ZmsStationItemDTO zmsStationItemDTO2 = new ZmsStationItemDTO();
        zmsStationItemDTO2.setName("气密测试工序");
        zmsStationItemDTOList.add(zmsStationItemDTO2);
        zmsStationInfoUploadDTO.setStations(zmsStationItemDTOList);
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "stationInfoMapping", zsZmsStationInfoUploadDTOList, "111");

        Assert.assertNotNull(sysLookupValues);
    }
    /* Ended by AICoder, pid:816cbn4130mf5de14ae809afb088163f8de904f2 */

    @Test
    public void insertZmsStationInfo() throws Exception {
        List<ZmsStationInfoDTO> mesZmsStationInfoDTOList = new ArrayList<>();
        ZmsStationInfoDTO zmsStationInfoDTO1 = new ZmsStationInfoDTO();
        zmsStationInfoDTO1.setStationId("组装1668755236");
        zmsStationInfoDTO1.setName("组装");
        zmsStationInfoDTO1.setAttribute("组装");
        zmsStationInfoDTO1.setState("PASS");
        zmsStationInfoDTO1.setStartTime(1668755236);
        zmsStationInfoDTO1.setEndTime(1669637998);
        zmsStationInfoDTO1.setEntityName("5GGNBRRU-I20201200016");
        mesZmsStationInfoDTOList.add(zmsStationInfoDTO1);

        List<ZmsStationInfoDTO> list = new ArrayList<>();
        ZmsStationInfoDTO zmsStationInfoDTO = new ZmsStationInfoDTO();
        zmsStationInfoDTO.setServerSn("************");
        zmsStationInfoDTO.setOrderId("S3TJ2017110102WCNE");
        zmsStationInfoDTO.setEndUser("中国ABCDD信集WXYDDDZZ");
        zmsStationInfoDTO.setEntityName("5GGNBRRU-I20201200016");
        list.add(zmsStationInfoDTO);

        List<ZmsStationInfoUploadDTO> zsZmsStationInfoUploadDTOList = new ArrayList<>();
        List<ZmsStationInfoDTO> zmsStationInfoDTOList = new ArrayList<>(mesZmsStationInfoDTOList);
        PowerMockito.when(zmsStationInfoUploadRepository.insertOrUpdateZmsStationInfo(zmsStationInfoDTOList)).thenReturn(1);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "insertZmsStationInfo", mesZmsStationInfoDTOList, zsZmsStationInfoUploadDTOList, list, "10255258");

        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO1 = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO1.setServerSn("************");
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO1);
        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO2 = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO2.setServerSn("************");
        List<ZmsStationItemDTO> zmsStationItemDTOList2 = new ArrayList<>();
        ZmsStationItemDTO zmsStationItemDTO2 = new ZmsStationItemDTO();
        zmsStationItemDTO2.setStationId("测试1668755236");
        zmsStationItemDTO2.setName("测试");
        zmsStationItemDTO2.setAttribute("测试");
        zmsStationItemDTO2.setState("PASS");
        zmsStationItemDTO2.setStartTime(1668755236);
        zmsStationItemDTO2.setEndTime(1669637998);
        zmsStationItemDTOList2.add(zmsStationItemDTO2);
        zmsStationInfoUploadDTO2.setStations(zmsStationItemDTOList2);
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO2);
        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO3 = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO3.setServerSn("************");
        List<ZmsStationItemDTO> zmsStationItemDTOList3 = new ArrayList<>();
        List<ZmsStationProblemDTO> zmsStationProblemDTOList3 = new ArrayList<>();
        ZmsStationProblemDTO zmsStationProblemDTO3 = new ZmsStationProblemDTO();
        zmsStationProblemDTO3.setProblemId("21314");
        zmsStationProblemDTOList3.add(zmsStationProblemDTO3);
        ZmsStationItemDTO zmsStationItemDTO3 = new ZmsStationItemDTO();
        zmsStationItemDTO3.setStationId("测试1668755236");
        zmsStationItemDTO3.setName("测试");
        zmsStationItemDTO3.setAttribute("测试");
        zmsStationItemDTO3.setState("PASS");
        zmsStationItemDTO3.setStartTime(1668755236);
        zmsStationItemDTO3.setEndTime(1669637998);
        zmsStationItemDTO3.setProblems(zmsStationProblemDTOList3);
        zmsStationItemDTOList3.add(zmsStationItemDTO3);
        ZmsStationItemDTO zmsStationItemDTO4 = new ZmsStationItemDTO();
        zmsStationItemDTO4.setStationId("测试1668755236");
        zmsStationItemDTO4.setName("测试");
        zmsStationItemDTO4.setAttribute("测试");
        zmsStationItemDTO4.setState("FAIL");
        zmsStationItemDTO4.setStartTime(1668755236);
        zmsStationItemDTO4.setEndTime(1669637998);
        zmsStationItemDTO4.setProblems(zmsStationProblemDTOList3);
        zmsStationItemDTOList3.add(zmsStationItemDTO4);
        zmsStationInfoUploadDTO3.setStations(zmsStationItemDTOList3);
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO3);
        PowerMockito.when(zmsStationInfoUploadRepository.insertOrUpdateZmsStationInfo(zmsStationInfoDTOList)).thenReturn(1);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "insertZmsStationInfo", mesZmsStationInfoDTOList, zsZmsStationInfoUploadDTOList, list, "10255258");
        Assert.assertNotNull(mesZmsStationInfoDTOList);
        Assert.assertNotNull(zsZmsStationInfoUploadDTOList);
    }

    @Test
    public void pushToIMESB2B() throws Exception {
        List<ZmsStationInfoDTO> mesZmsStationInfoDTOList = new ArrayList<>();
        ZmsStationInfoDTO zmsStationInfoDTO1 = new ZmsStationInfoDTO();
        zmsStationInfoDTO1.setStationId("组装1668755236");
        zmsStationInfoDTO1.setName("组装");
        zmsStationInfoDTO1.setAttribute("组装");
        zmsStationInfoDTO1.setState("PASS");
        zmsStationInfoDTO1.setStartTime(1668755236);
        zmsStationInfoDTO1.setEndTime(1669637998);
        zmsStationInfoDTO1.setEntityName("5GGNBRRU-I20201200017");
        mesZmsStationInfoDTOList.add(zmsStationInfoDTO1);

        List<ZmsStationInfoDTO> list = new ArrayList<>();
        ZmsStationInfoDTO zmsStationInfoDTO11 = new ZmsStationInfoDTO();
        zmsStationInfoDTO11.setServerSn("************");
        zmsStationInfoDTO11.setOrderId("S3TJ2017110102WCNE");
        zmsStationInfoDTO11.setEndUser("中国ABCDD信集WXYDDDZZ");
        zmsStationInfoDTO11.setEntityName("5GGNBRRU-I20201200016");
        list.add(zmsStationInfoDTO11);
        ZmsStationInfoDTO zmsStationInfoDTO22 = new ZmsStationInfoDTO();
        zmsStationInfoDTO22.setServerSn("703932200036");
        zmsStationInfoDTO22.setOrderId("S3TJ2017110102WCNE");
        zmsStationInfoDTO22.setEndUser("中国ABCDD信集WXYDDDZZ");
        zmsStationInfoDTO22.setEntityName("5GGNBRRU-I20201200018");
        list.add(zmsStationInfoDTO22);
        ZmsStationInfoDTO zmsStationInfoDTO33 = new ZmsStationInfoDTO();
        zmsStationInfoDTO33.setServerSn("703932200037");
        zmsStationInfoDTO33.setOrderId("S3TJ2017110102WCNE");
        zmsStationInfoDTO33.setEndUser("中国ABCDD信集WXYDDDZZ");
        zmsStationInfoDTO33.setEntityName("5GGNBRRU-I20201200019");
        list.add(zmsStationInfoDTO33);


        List<ZmsStationInfoUploadDTO> zsZmsStationInfoUploadDTOList = new ArrayList<>();
        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO1 = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO1.setServerSn("************");
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO1);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "pushToIMESB2B", mesZmsStationInfoDTOList, zsZmsStationInfoUploadDTOList, list, "10255258");

        ZmsStationInfoDTO zmsStationInfoDTO2 = new ZmsStationInfoDTO();
        zmsStationInfoDTO2.setStationId("组装1668755236");
        zmsStationInfoDTO2.setName("组装");
        zmsStationInfoDTO2.setAttribute("组装");
        zmsStationInfoDTO2.setState("PASS");
        zmsStationInfoDTO2.setStartTime(1668755236);
        zmsStationInfoDTO2.setEndTime(1669637998);
        zmsStationInfoDTO2.setEntityName("5GGNBRRU-I20201200016");
        mesZmsStationInfoDTOList.add(zmsStationInfoDTO2);

        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO2 = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO2.setServerSn("703932200036");
        List<ZmsStationItemDTO> zmsStationItemDTOList2 = new ArrayList<>();
        ZmsStationItemDTO zmsStationItemDTO2 = new ZmsStationItemDTO();
        zmsStationItemDTO2.setStationId("测试1668755236");
        zmsStationItemDTO2.setName("测试");
        zmsStationItemDTO2.setAttribute("测试");
        zmsStationItemDTO2.setState("PASS");
        zmsStationItemDTO2.setStartTime(1668755236);
        zmsStationItemDTO2.setEndTime(1669637998);
        zmsStationItemDTOList2.add(zmsStationItemDTO2);
        zmsStationInfoUploadDTO2.setStations(zmsStationItemDTOList2);
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO2);
        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO3 = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO3.setServerSn("703932200037");
        List<ZmsStationItemDTO> zmsStationItemDTOList3 = new ArrayList<>();
        List<ZmsStationProblemDTO> zmsStationProblemDTOList3 = new ArrayList<>();
        ZmsStationProblemDTO zmsStationProblemDTO3 = new ZmsStationProblemDTO();
        zmsStationProblemDTO3.setProblemId("21314");
        zmsStationProblemDTOList3.add(zmsStationProblemDTO3);
        ZmsStationItemDTO zmsStationItemDTO3 = new ZmsStationItemDTO();
        zmsStationItemDTO3.setStationId("测试1668755236");
        zmsStationItemDTO3.setName("测试");
        zmsStationItemDTO3.setAttribute("测试");
        zmsStationItemDTO3.setState("PASS");
        zmsStationItemDTO3.setStartTime(1668755236);
        zmsStationItemDTO3.setEndTime(1669637998);
        zmsStationItemDTO3.setProblems(zmsStationProblemDTOList3);
        zmsStationItemDTOList3.add(zmsStationItemDTO3);
        zmsStationInfoUploadDTO3.setStations(zmsStationItemDTOList3);
        zsZmsStationInfoUploadDTOList.add(zmsStationInfoUploadDTO3);

        ZmsStationInfoUploadDTO zmsStationInfoUploadDTO = new ZmsStationInfoUploadDTO();
        zmsStationInfoUploadDTO.setServerSn("************");
        zmsStationInfoUploadDTO.setStations(zmsStationItemDTOList3);

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setId(java.util.UUID.randomUUID().toString().trim().replaceAll("-", ""));
        dto.setOrigin(MES);
        dto.setCustomerName(zmsStationInfoDTO11.getEndUser());
        dto.setContractNo(zmsStationInfoDTO11.getOrderId());
        dto.setTaskNo(zmsStationInfoDTO11.getEntityName());
        dto.setSn(zmsStationInfoDTO11.getServerSn());
        dto.setProjectName(STRING_EMPTY);
        dto.setMessageType(STATION_INFO_BYTEDANCE);
        dto.setFactoryId(INT_51);
        dto.setJsonData(JSON.toJSONString(zmsStationInfoUploadDTO));
        dto.setCreateBy(SYSTEM);
        dto.setLastUpdatedBy(SYSTEM);
        dataList.add(dto);
        Whitebox.invokeMethod(centerfactoryRemoteService, "pushDataToB2B", dataList);
        Whitebox.invokeMethod(zmsStationInfoUploadService, "pushToIMESB2B", mesZmsStationInfoDTOList, zsZmsStationInfoUploadDTOList, list, "10255258");
        Assert.assertNotNull(mesZmsStationInfoDTOList);
        Assert.assertNotNull(zsZmsStationInfoUploadDTOList);
    }

}
