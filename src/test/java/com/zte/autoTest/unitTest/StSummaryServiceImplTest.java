package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.StSummaryServiceImpl;
import com.zte.domain.model.stepdt.StItembarcodeStockRepository;
import com.zte.interfaces.stepdt.dto.StItembarcodeStockEntityDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class StSummaryServiceImplTest {
    @InjectMocks
    StSummaryServiceImpl stSummaryServiceImp;
    @Mock
    StItembarcodeStockRepository stItembarcodeStockRepository;

    @Before
    public void init(){

    }

    @Test
    public void insertBkcLog()throws Exception{
        List<StItembarcodeStockEntityDTO> taskList=new ArrayList<>();
        StItembarcodeStockEntityDTO taskDto=new StItembarcodeStockEntityDTO();
        taskList.add(taskDto);
        stSummaryServiceImp.insertBkcLog(taskList);
        Assert.assertNotNull(taskList);

    }
}
