package com.zte.autoTest.unitTest;

import com.zte.application.datawb.GtsBaSupplierService;
import com.zte.application.datawb.ZtebarcodeService;
import com.zte.application.datawb.impl.StItemBarcodeServiceImpl;
import com.zte.domain.model.datawb.StItemBarcode;
import com.zte.domain.model.datawb.StItemBarcodeRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/9 00
 * @description:
 */
public class StItemBarcodeServiceImplTest extends BaseTestCase {

    @InjectMocks
    StItemBarcodeServiceImpl stItemBarcodeServiceImpl;
    @Mock
    StItemBarcodeRepository stItemBarcodeRepository;
    @Mock
    private ZtebarcodeService ztebarcodeService;
    @Mock
    private GtsBaSupplierService gtsBaSupplierService;

    @Test
    public void getListByItemBarcode(){
        StItemBarcode dto = new StItemBarcode();
        dto.setItemBarcodeList("'123','124'");

        List<StItemBarcode> stItemBarcodes = new LinkedList<>();
        StItemBarcode a1 = new StItemBarcode();
        a1.setItemBarcode("123");
        a1.setItemUuid(2l);
        stItemBarcodes.add(a1);
        PowerMockito.when(stItemBarcodeRepository.selectVStItemBarcodeList(Mockito.any())).thenReturn(stItemBarcodes);

        Assert.assertNotNull(stItemBarcodeServiceImpl.getListByItemBarcode(dto));
    }

    @Test
    public void getItemInfoByItemBarcode(){
        StItemBarcode dto = new StItemBarcode();
        dto.setItemBarcode("'123','124'");
        Assert.assertNull(stItemBarcodeServiceImpl.getItemInfoByItemBarcode(dto));
    }

    @Test
    public void getItemInfoWithLeadFlag(){
        StItemBarcode dto = new StItemBarcode();
        dto.setItemBarcode("'123','124'");
        PowerMockito.when(stItemBarcodeRepository.getItemInfoWithLeadFlag(Mockito.any())).thenReturn(dto);
        Assert.assertNotNull(stItemBarcodeServiceImpl.getItemInfoWithLeadFlag(dto));
    }
}
