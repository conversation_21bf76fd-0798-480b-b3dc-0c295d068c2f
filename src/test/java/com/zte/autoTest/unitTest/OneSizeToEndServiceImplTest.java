package com.zte.autoTest.unitTest;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.datawb.impl.CurbNameTagsPrintServiceImpl;
import com.zte.application.datawb.impl.OneSizeToEndServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.*;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.Normalizer;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class, CommonUtils.class, Normalizer.class, GbomCsgInfosDTO.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class OneSizeToEndServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    OneSizeToEndServiceImpl oneSizeToEndServiceImpl;
    @Mock
    private OneSizeToEndSFCRepository oneSizeToEndSFCRepository;
    @Mock
    private OneSizeToEndRepository oneSizeToEndRepository;
    @Mock
    CurbNameTagsPrintRepository curbNameTagsPrintRepository;
    @Mock
    CurbNameTagsPrintServiceImpl curbNameTagsPrintService;
    @Mock
    WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private DeliverEntityCycleRepository deliverEntityCycleRepository;

    @Mock
    CfgCodeRuleItemRepository cfgCodeRuleItemRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class, GbomCsgInfosDTO.class,
                JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class, Normalizer.class);
    }

    @Test
    public void Test() throws Exception {
        OneSizeToEndOutBoxPrintOutDTO outBoxPrintOut = new OneSizeToEndOutBoxPrintOutDTO();
        List<OneSizeToEndOutBoxPrintDTO> outBoxPrint = new ArrayList<>();

        OneSizeToEndOutBoxPrintDTO entity = new OneSizeToEndOutBoxPrintDTO();
        List<CPQDDTO> listCPQD = new ArrayList<>();
        List<String> configDetailIds = new ArrayList<>();
        /* Started by AICoder, pid:sddd2edf09w87cd141760bdf901f3c375707e8d4 */
        String result1 = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"0000\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"Success\"\n" +
                "    },\n" +
                "    \"bo\": [\n" +
                "        {\n" +
                "            \"pkInfoId\": \"1733347142826033155\",\n" +
                "            \"contractId\": \"6711231\",\n" +
                "            \"contractNo\": \"81SBOM20230703077\",\n" +
                "            \"deliverySetId\": \"3270996\",\n" +
                "            \"deliverySet\": \"JF002S20230223006\",\n" +
                "            \"productInformation\": \"SPN\",\n" +
                "            \"supplyNotificationNo\": \"APO304610230200320\",\n" +
                "            \"supplyIssuanceDate\": \"2023-02-14 00:00:00\",\n" +
                "            \"requiringTel\": \"古议成：18887609736\",\n" +
                "            \"requiringUnit\": \"文山移动\",\n" +
                "            \"salesManagerInfor\": \"伏艳刚13648847412\",\n" +
                "            \"flagBatch\": \"是\",\n" +
                "            \"projectNo\": \"C23304626CA1001\",\n" +
                "            \"projectName\": \"中国移动云南公司2023年文山分公司富宁县田蓬口岸5G专网项目_文山分公司\",\n" +
                "            \"lockStatus\": \"Y\",\n" +
                "            \"enableFlag\": 1,\n" +
                "            \"createDate\": \"2023-03-08 14:01:20\",\n" +
                "            \"createBy\": \"nullundefined\",\n" +
                "            \"lastUpdateDate\": \"2023-03-08 14:17:18\",\n" +
                "            \"lastUpdateBy\": \"1009436910094369\",\n" +
                "            \"operatorType\": \"AB004\",\n" +
                "            \"representOfficeCode\": \"ORG2227654\",\n" +
                "            \"poNumber\": \"11111\",\n" +
                "            \"customerErpPoNumber\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"other\": null,\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}";
        /* Ended by AICoder, pid:sddd2edf09w87cd141760bdf901f3c375707e8d4 */
        try {
            /* Started by AICoder, pid:n6f0b18386612b114df109d820873e0980a5a6fe */
            CPQDDTO cpqd = new CPQDDTO();
            cpqd.setCbomCode("111");
            cpqd.setSplitCbomQty(1);
            cpqd.setConfigDetailId("2222");
            listCPQD.add(cpqd);
            /* Ended by AICoder, pid:n6f0b18386612b114df109d820873e0980a5a6fe */
            listCPQD.add(cpqd);
            outBoxPrintOut.setOutBoxPrint(outBoxPrint);
            outBoxPrintOut.setBillNo("B");
            oneSizeToEndServiceImpl.setOutBoxPrint(outBoxPrintOut, listCPQD, configDetailIds);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }
        /* Started by AICoder, pid:z57a8n874af4952149b10be1a0775c397b52cf6d */
        try {
            configDetailIds.add("2222");
            entity.setConfigDetailId(null);
            outBoxPrint.add(entity);
            outBoxPrintOut.setBillNo("C");

            List<OneSizeToEndConfigDetailMapDTO> oneSizeToEndConfigDetailMapDTO = new ArrayList<>();
            OneSizeToEndConfigDetailMapDTO model = new OneSizeToEndConfigDetailMapDTO();
            model.setParentConfigDetailId("33");
            oneSizeToEndConfigDetailMapDTO.add(model);
            when(oneSizeToEndRepository.getConfigDetailMap(Mockito.anyString())).thenReturn(oneSizeToEndConfigDetailMapDTO);
            oneSizeToEndServiceImpl.setOutBoxPrint(outBoxPrintOut, listCPQD, configDetailIds);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }
        try {
            entity.setConfigDetailId(null);
            outBoxPrint.add(entity);
            outBoxPrintOut.setBillNo("B1");

            List<OneSizeToEndConfigDetailMapDTO> oneSizeToEndConfigDetailMapDTO = new ArrayList<>();
            OneSizeToEndConfigDetailMapDTO model = new OneSizeToEndConfigDetailMapDTO();
            model.setParentConfigDetailId("33");
            oneSizeToEndConfigDetailMapDTO.add(model);
            when(oneSizeToEndRepository.getConfigDetailMap(Mockito.anyString())).thenReturn(oneSizeToEndConfigDetailMapDTO);
            oneSizeToEndServiceImpl.setOutBoxPrint(outBoxPrintOut, listCPQD, configDetailIds);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }

        try {

            entity.setConfigDetailId("333");
            entity.setQty(22);
            outBoxPrint.clear();
            outBoxPrint.add(entity);
            outBoxPrintOut.setBillNo("C");
            CPQDDTO cpqd = new CPQDDTO();
            cpqd.setCsSource("CUSTOMIZED333");
            cpqd.setCbomCode("111");
            cpqd.setConfigDetailId("333");
            listCPQD.add(cpqd);
            cpqd.setCsSource("CUSTOMIZED333");
            cpqd.setCbomCode("");
            cpqd.setConfigDetailId("333");
            listCPQD.add(cpqd);
            List<OneSizeToEndConfigDetailMapDTO> oneSizeToEndConfigDetailMapDTO = new ArrayList<>();
            OneSizeToEndConfigDetailMapDTO model = new OneSizeToEndConfigDetailMapDTO();
            model.setParentConfigDetailId("333");
            model.setConfigDetailId("333");
            oneSizeToEndConfigDetailMapDTO.add(model);
            when(oneSizeToEndRepository.getConfigDetailMap(Mockito.anyString())).thenReturn(oneSizeToEndConfigDetailMapDTO);
            oneSizeToEndServiceImpl.setOutBoxPrint(outBoxPrintOut, listCPQD, configDetailIds);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }
        try {
            CPQDDTO cpqd = new CPQDDTO();
            cpqd.setCsSource("CUSTOMIZED");
            cpqd.setConfigDetailId("333");
            cpqd.setSplitCbomQty(2);
            cpqd.setCbomCode("111");
            listCPQD.clear();
            listCPQD.add(cpqd);
            outBoxPrintOut.setBillNo("C");
            List<OneSizeToEndConfigDetailMapDTO> oneSizeToEndConfigDetailMapDTO = new ArrayList<>();
            OneSizeToEndConfigDetailMapDTO model = new OneSizeToEndConfigDetailMapDTO();
            model.setParentConfigDetailId("333");
            model.setConfigDetailId("333");
            oneSizeToEndConfigDetailMapDTO.add(model);
            when(oneSizeToEndRepository.getConfigDetailMap(Mockito.anyString())).thenReturn(oneSizeToEndConfigDetailMapDTO);
            oneSizeToEndServiceImpl.setOutBoxPrint(outBoxPrintOut, listCPQD, configDetailIds);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }
        /* Ended by AICoder, pid:z57a8n874af4952149b10be1a0775c397b52cf6d */

        ServiceData data = new ServiceData();
        OneSizeToEndOutBoxPrintOutDTO dddd = new OneSizeToEndOutBoxPrintOutDTO();
        dddd.setSu("Y");
        dddd.setBillNo("C3333333333");
        List<OneSizeToEndOutBoxPrintDTO> dd = new ArrayList<>();
        dddd.setOutBoxPrint(dd);
        data.setBo(dddd);
        OneSizeToEndOutBoxPrintInDTO dto = new OneSizeToEndOutBoxPrintInDTO();
        try {
            oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            dto.setBillNo("C3333333333");
            oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            dto.setOrgID("635");
            when(oneSizeToEndSFCRepository.getScanFlag(Mockito.anyString())).thenReturn("");
            oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            when(oneSizeToEndSFCRepository.getScanFlag(Mockito.anyString())).thenReturn("1");
            oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            when(oneSizeToEndSFCRepository.getScanFlag(Mockito.anyString())).thenReturn("2");
            when(oneSizeToEndRepository.selectBoxUpBillInfo(Mockito.anyString())).thenReturn(null);
            oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            List<OneSizeToEndBoxUpBillInfoDTO> boxUpBillInfo = new ArrayList<>();
            boxUpBillInfo.add(new OneSizeToEndBoxUpBillInfoDTO());
            when(oneSizeToEndRepository.selectBoxUpBillInfo(Mockito.anyString())).thenReturn(boxUpBillInfo);
            when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(null);
            oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            List<OneSizeToEndBoxUpBillDetailInfoDTO> boxUpBillDetailInfo = new ArrayList<>();
            OneSizeToEndBoxUpBillDetailInfoDTO d = new OneSizeToEndBoxUpBillDetailInfoDTO();
            d.setLevelNo("22242424");
            d.setBoqItemName("2323");
            d.setBoqItemEName("2323");
            boxUpBillDetailInfo.add(d);
            when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
            when(deliverEntityCycleRepository.getMfgSiteType(Mockito.anyString())).thenReturn("");
            when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.anyString())).thenReturn(null);
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());

        } catch (Exception ex) {
        }

        try {
            List<OneSizeToEndBoxUpBillDetailInfoDTO> boxUpBillDetailInfo = new ArrayList<>();
            OneSizeToEndBoxUpBillDetailInfoDTO d = new OneSizeToEndBoxUpBillDetailInfoDTO();
            d.setLevelNo("22242424");
            d.setItemEName("8998");
            d.setItemName("8989");
            boxUpBillDetailInfo.add(d);
            when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
            when(deliverEntityCycleRepository.getMfgSiteType(Mockito.anyString())).thenReturn(null);
            when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.anyString())).thenReturn(null);
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());

        } catch (Exception ex) {
        }

        try {
            List<OneSizeToEndBoxUpBillDetailInfoDTO> boxUpBillDetailInfo = new ArrayList<>();
            OneSizeToEndBoxUpBillDetailInfoDTO d = new OneSizeToEndBoxUpBillDetailInfoDTO();
            d.setLevelNo("0");
            boxUpBillDetailInfo.add(d);
            when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
            PowerMockito.when(deliverEntityCycleRepository.getMfgSiteType(any())).thenReturn("333");
            when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.anyString())).thenReturn(null);
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());

        } catch (Exception ex) {
        }
        /* Started by AICoder, pid:67a0c0bc11y3d4d145cb0a9f31febb14d229c1c4 */
        List<OneSizeToEndBoxUpBillInfoDTO> boxUpBillInfo = new ArrayList<>();
        OneSizeToEndBoxUpBillInfoDTO bbb = new OneSizeToEndBoxUpBillInfoDTO();
        bbb.setTemplateLanguage("中文");
        bbb.setDeliverSetNum("1111");
        bbb.setCustomerPo("1111");
        boxUpBillInfo.add(bbb);
        when(oneSizeToEndRepository.selectBoxUpBillInfo(Mockito.anyString())).thenReturn(boxUpBillInfo);
        List<OneSizeToEndBoxUpBillDetailInfoDTO> boxUpBillDetailInfo = new ArrayList<>();
        OneSizeToEndBoxUpBillDetailInfoDTO ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111.222.333");
        ddd.setItemName("111");
        ddd.setBoqItemName("111");
        ddd.setBillNo("B1");
        boxUpBillDetailInfo.add(ddd);
        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111");
        ddd.setItemName("111");
        ddd.setBoqItemName("");
        ddd.setBillNo("B2");
        boxUpBillDetailInfo.add(ddd);
        when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
        when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.anyString())).thenReturn("11");
        PowerMockito.when(deliverEntityCycleRepository.getMfgSiteType(any())).thenReturn("333");
        String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
        PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
        List<ICCDTO> iccdtos = new ArrayList<>();
        ICCDTO icc = new ICCDTO();
        icc.setPoNumber("");
        icc.setCustomerErpPoNumber("111");
        icc.setProjectNo("111");
        icc.setProjectName("111");
        icc.setRequiringUnit("11");
        icc.setRequiringTel("111");
        iccdtos.add(icc);
        PowerMockito.when(curbNameTagsPrintService.getICC(Mockito.anyString(), Mockito.anyString())).thenReturn(iccdtos);

        try {
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());
        } catch (Exception ex) {
        }

        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111.222.333.444");
        ddd.setBoqItemName("111");
        ddd.setItemName("111");
        boxUpBillDetailInfo.clear();
        boxUpBillDetailInfo.add(ddd);
        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111");
        ddd.setBoqItemName("111");
        ddd.setItemName("111");
        boxUpBillDetailInfo.add(ddd);
        when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
        PowerMockito.when(deliverEntityCycleRepository.getMfgSiteType(any())).thenReturn("");
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
        iccdtos = new ArrayList<>();
        icc = new ICCDTO();
        icc.setPoNumber("111");
        icc.setCustomerErpPoNumber("111");
        icc.setProjectNo("111");
        icc.setProjectName("111");
        icc.setRequiringUnit("11");
        icc.setRequiringTel("111");
        iccdtos.add(icc);
        PowerMockito.when(curbNameTagsPrintService.getICC(Mockito.anyString(), Mockito.anyString())).thenReturn(iccdtos);
        try {
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());
        } catch (Exception ex) {
        }

        bbb = new OneSizeToEndBoxUpBillInfoDTO();
        bbb.setTemplateLanguage("英文");
        bbb.setDeliverSetNum("1111");
        bbb.setCustomerPo("1111");
        boxUpBillInfo.clear();
        boxUpBillInfo.add(bbb);
        when(oneSizeToEndRepository.selectBoxUpBillInfo(Mockito.anyString())).thenReturn(boxUpBillInfo);
        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111.222.333.444");
        ddd.setBoqItemName("");
        ddd.setItemName("111");
        boxUpBillDetailInfo.clear();
        boxUpBillDetailInfo.add(ddd);
        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111");
        ddd.setBoqItemEName("111");
        ddd.setItemName("111");
        boxUpBillDetailInfo.add(ddd);
        when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
        PowerMockito.when(deliverEntityCycleRepository.getMfgSiteType(any())).thenReturn("222");
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
        iccdtos = new ArrayList<>();
        PowerMockito.when(curbNameTagsPrintService.getICC(Mockito.anyString(), Mockito.anyString())).thenReturn(iccdtos);
        try {
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());
        } catch (Exception ex) {
        }

        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111.222.333");
        ddd.setItemName("111");
        ddd.setBoqItemName("111");
        boxUpBillDetailInfo.clear();
        boxUpBillDetailInfo.add(ddd);
        ddd = new OneSizeToEndBoxUpBillDetailInfoDTO();
        ddd.setLevelNo("111");
        ddd.setBoqItemEName("");
        ddd.setItemEName("111");
        boxUpBillDetailInfo.add(ddd);
        when(oneSizeToEndSFCRepository.selectBoxUpBillDetailInfo(Mockito.anyString())).thenReturn(boxUpBillDetailInfo);
        when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.anyString())).thenReturn("");
        PowerMockito.when(deliverEntityCycleRepository.getMfgSiteType(any())).thenReturn("");
        result1 = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": 2222,\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"Success\"\n" +
                "    },\n" +
                "    \"bo\": null,\n" +
                "    \"other\": null,\n" +
                "    \"responseRule\": \"msa\"\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
        try {
            ServiceData result = oneSizeToEndServiceImpl.handleOutBoxPrint(dto);
            Assert.assertEquals(data.getCode().getCode(), result.getCode().getCode());
        } catch (Exception ex) {
        }
        /* Ended by AICoder, pid:67a0c0bc11y3d4d145cb0a9f31febb14d229c1c4 */
        String customerSignRemark = null;
        String result = null;
        List<SysLookupValues> lookupValues = new ArrayList<>();
        SysLookupValues model = new SysLookupValues();
        try {
            result = oneSizeToEndServiceImpl.getCustomerSignRemark(null);
            Assert.assertEquals(customerSignRemark, result);
        } catch (Exception ex) {
        }
        try {
            when(wsmAssembleLinesRepository.getSysLookupValues(Mockito.anyString())).thenReturn(null);
            result = oneSizeToEndServiceImpl.getCustomerSignRemark("33");
            Assert.assertEquals(customerSignRemark, result);
        } catch (Exception ex) {
        }
        try {
            when(wsmAssembleLinesRepository.getSysLookupValues(Mockito.anyString())).thenReturn(lookupValues);
            result = oneSizeToEndServiceImpl.getCustomerSignRemark("33");
            Assert.assertEquals(customerSignRemark, result);
        } catch (Exception ex) {
        }
        try {
            lookupValues = new ArrayList<>();
            model = new SysLookupValues();
            model.setLookupMeaning("393");
            lookupValues.add(model);
            lookupValues.add(model);
            when(wsmAssembleLinesRepository.getSysLookupValues(Mockito.anyString())).thenReturn(lookupValues);
            result = oneSizeToEndServiceImpl.getCustomerSignRemark("33");
            Assert.assertEquals(customerSignRemark, result);
        } catch (Exception ex) {
        }
        try {
            customerSignRemark = "22";
            lookupValues = new ArrayList<>();
            model = new SysLookupValues();
            model.setLookupMeaning("33");
            model.setDescription("22");
            lookupValues.add(model);
            when(wsmAssembleLinesRepository.getSysLookupValues(Mockito.anyString())).thenReturn(lookupValues);
            result = oneSizeToEndServiceImpl.getCustomerSignRemark("33");
            Assert.assertEquals(customerSignRemark, result);
        } catch (Exception ex) {
        }


        String result33 = "{\n" +
                "    \"code\": {\n" +
                "        \"code\": \"1111\",\n" +
                "        \"msgId\": \"RetCode.Success\",\n" +
                "        \"msg\": \"操作成功\"\n" +
                "    },\n" +
                "    \"bo\": {\n" +
                "        \"gbomCsgInfos\": {\n" +
                "            \"current\": 1,\n" +
                "            \"total\": 1,\n" +
                "            \"rows\": [\n" +
                "                {\n" +
                "                    \"configDetailId\": 1528678051,\n" +
                "                    \"mfgSiteId\": 226702899,\n" +
                "                    \"contractHeaderId\": 6092837,\n" +
                "                    \"contractNumber\": \"S3YD2023071701CPEF-GZP021\",\n" +
                "                    \"configNumber\": \"S3YD2023071701CPEF-GZP021D\",\n" +
                "                    \"siteAddrGuid\": 232524373,\n" +
                "                    \"modelCode\": \"180000489531\",\n" +
                "                    \"modelNameCn\": \"ZXHN F631A\",\n" +
                "                    \"modelNameEn\": \"ZXHN F631A\",\n" +
                "                    \"modelQty\": 1,\n" +
                "                    \"ptoNo\": null,\n" +
                "                    \"moduleCode\": \"180000489535\",\n" +
                "                    \"moduleQty\": 1,\n" +
                "                    \"csSource\": \"CUSTOMIZED\",\n" +
                "                    \"sbomId\": \"232524373_14989902_14989904_15250391\",\n" +
                "                    \"sbomCode\": \"180000501152\",\n" +
                "                    \"sbomNameCn\": \"GPON用户端设备ZXHN F631A\",\n" +
                "                    \"sbomNameEn\": \"GPON CPE ZXHN F631A\",\n" +
                "                    \"sbomUnitCn\": \"台\",\n" +
                "                    \"sbomUnitEn\": \"SET\",\n" +
                "                    \"sbomQty\": 500,\n" +
                "                    \"sbomTotalQty\": 500,\n" +
                "                    \"isMainSbom\": \"Y\",\n" +
                "                    \"gbomItemId\": \"33729684\",\n" +
                "                    \"gbomCode\": \"125007031196\",\n" +
                "                    \"gbomNameCn\": \"吉比特无源光纤接入用户端设备(GPON ONU)\",\n" +
                "                    \"gbomNameEn\": \"GPON ONU\",\n" +
                "                    \"gbomUnitCn\": \"件\",\n" +
                "                    \"gbomUnitEn\": \"PCS\",\n" +
                "                    \"gbomQty\": 1,\n" +
                "                    \"gbomTotalQty\": 500,\n" +
                "                    \"sgRatio\": 1,\n" +
                "                    \"createdBy\": null,\n" +
                "                    \"creationDate\": \"2023-12-18 13:11:43\",\n" +
                "                    \"lastUpdatedBy\": null,\n" +
                "                    \"lastUpdateDate\": \"2023-12-18 13:11:43\",\n" +
                "                    \"cbomList\": [\n" +
                "                        {\n" +
                "                            \"suSiteId\": null,\n" +
                "                            \"suSiteSbomQty\": null,\n" +
                "                            \"isMainSbom\": \"Y\",\n" +
                "                            \"cbomId\": null,\n" +
                "                            \"cbomCode\": \"10537825\",\n" +
                "                            \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                "                            \"cbomNameEn\": \"\",\n" +
                "                            \"cbomUnitCn\": \"套\",\n" +
                "                            \"cbomUnitEn\": null,\n" +
                "                            \"quotationMaterialCode\": \"\",\n" +
                "                            \"quotationMaterialNameCn\": \"\",\n" +
                "                            \"quotationMaterialNameEn\": \"\",\n" +
                "                            \"csRatio\": 1,\n" +
                "                            \"cgRatio\": 1,\n" +
                "                            \"isCbomSnManagement\": \"\",\n" +
                "                            \"seq\": 1\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"isMainGbom\": \"Y\",\n" +
                "                    \"cbomCode\": \"10537825\",\n" +
                "                    \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                "                    \"cbomNameEn\": \"\",\n" +
                "                    \"cbomUnitCn\": \"套\",\n" +
                "                    \"cbomUnitEn\": null,\n" +
                "                    \"sbomSplitCbomQty\": 1,\n" +
                "                    \"splitSbomQty\": 1,\n" +
                "                    \"splitCbomQty\": 1,\n" +
                "                    \"isCbomSnManagement\": \"\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"csgExceptionItems\": null\n" +
                "    },\n" +
                "    \"other\": null\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(result);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode json = objectMapper.readTree(result33);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

        listCPQD = new ArrayList<>();
        List<String> ddgg = new ArrayList<>();
        ddgg.add("33333");
        try {
            when(curbNameTagsPrintRepository.getConfigDetails(Mockito.anyMap())).thenReturn(ddgg);
            oneSizeToEndServiceImpl.getCpqdInfo(new OneSizeToEndOutBoxPrintOutDTO(), null);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }
        OneSizeToEndOutBoxPrintOutDTO fff = new OneSizeToEndOutBoxPrintOutDTO();
        List<OneSizeToEndOutBoxPrintDTO> vv = new ArrayList<>();
        OneSizeToEndOutBoxPrintDTO bb = new OneSizeToEndOutBoxPrintDTO();
        bb.setConfigDetailId(null);
        vv.add(bb);
        fff.setOutBoxPrint(vv);

        try {
            CPQDDTO cpqd = new CPQDDTO();
            listCPQD.add(cpqd);
            when(curbNameTagsPrintRepository.getConfigDetails(Mockito.anyMap())).thenReturn(new ArrayList<>());
            // when(autoBatchMarkImpl.getCPQD(Mockito.anyString(), Mockito.anyList())).thenReturn(listCPQD);

            oneSizeToEndServiceImpl.getCpqdInfo(fff, null);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
        }
        try {
            List<String> configDetail = new ArrayList<>();
            configDetail.add("3");
            listCPQD = new ArrayList<>();
            CPQDDTO cpqd = new CPQDDTO();
            cpqd.setCsSource("CUSTOMIZED");
            cpqd.setConfigDetailId("3");
            listCPQD.add(cpqd);
            when(curbNameTagsPrintRepository.getConfigDetails(Mockito.anyMap())).thenReturn(configDetail);
            //when(autoBatchMarkImpl.getCPQD(Mockito.anyString(), Mockito.anyList())).thenReturn(listCPQD);
            oneSizeToEndServiceImpl.getCpqdInfo(fff, null);
            Assert.assertEquals(true, true);

        } catch (Exception ex) {
        }
        /* Started by AICoder, pid:xcdb4x2cabme3a314792096cf1ae4355d607998d */
        boxUpBillInfo = new ArrayList<>();
        boxUpBillDetailInfo = new ArrayList<>();
        OneSizeToEndBoxUpBillDetailInfoDTO gg = null;
        try {
            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C2");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setItemBarcode("STR_ITEM_BARCODE_GT4000,ee,ee");
            gg.setItemQty("11");
            gg.setItemType("23");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("B1");
            gg.setItemBarcode("STR_ITEM_BARCODE_GT4000,ee,ee");
            gg.setItemQty("0");
            gg.setItemType("23");
            boxUpBillDetailInfo.add(gg);
            oneSizeToEndServiceImpl.handleOutBoxPrintDetail("C1", boxUpBillInfo, boxUpBillDetailInfo);
            Assert.assertEquals(true, true);

            oneSizeToEndServiceImpl.handleOutBoxPrintDetail("B1", boxUpBillInfo, boxUpBillDetailInfo);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
        }
        try {
            boxUpBillInfo = new ArrayList<>();
            boxUpBillDetailInfo = new ArrayList<>();
            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setItemBarcode("STR_ITEM_BARCODE_GT4000,ee,ee,33");
            gg.setItemQty("11");
            gg.setRealQty(1);
            gg.setItemType("23");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("B1");
            gg.setRealQty(1);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode("33");
            boxUpBillDetailInfo.add(gg);


            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setRealQty(2);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode("33 77");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("B1");
            gg.setRealQty(1);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode("33 77");
            boxUpBillDetailInfo.add(gg);


            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setRealQty(0);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode("33 77");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setRealQty(2);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode("  ");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setRealQty(1);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode(" ");
            boxUpBillDetailInfo.add(gg);

            gg = new OneSizeToEndBoxUpBillDetailInfoDTO();
            gg.setBillNo("C1");
            gg.setRealQty(1);
            gg.setItemQty("11 22");
            gg.setItemType("23 31");
            gg.setItemBarcode("  ");
            boxUpBillDetailInfo.add(gg);

            List<String> d = new ArrayList<>();
            d.add("22233233");
            when(oneSizeToEndSFCRepository.getItemBarcode(Mockito.any())).thenReturn(d);
            List<OneSizeToEndOutBoxPrintDTO> result4 = oneSizeToEndServiceImpl.handleOutBoxPrintDetail("C1", boxUpBillInfo, boxUpBillDetailInfo);
            Assert.assertEquals(result4.get(0).getQty(), null);

            List<OneSizeToEndOutBoxPrintDTO> result5 = oneSizeToEndServiceImpl.handleOutBoxPrintDetail("B1", boxUpBillInfo, boxUpBillDetailInfo);
            Assert.assertEquals(result5.get(0).getQty(), null);
        } catch (
                Exception ex) {
        }

        OneSizeToEndOutBoxPrintDTO ff = new OneSizeToEndOutBoxPrintDTO();
        List<OneSizeToEndSqlInDTO> oneSizeToEndSqlInDTO3 = new ArrayList<>();
        List<OneSizeToEndSqlInDTO> oneSizeToEndSqlInDTO4 = new ArrayList<>();
        OneSizeToEndSqlInDTO dto99 = new OneSizeToEndSqlInDTO();
        try {
            ff.setQty(2);
            OneSizeToEndOutBoxPrintDTO results = oneSizeToEndServiceImpl.getBoxItemLine("2", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", new OneSizeToEndBoxUpBillDetailInfoDTO());
            Assert.assertEquals(results.getQty(), null);
        } catch (Exception ex) {
        }
        try {
            OneSizeToEndBoxUpBillDetailInfoDTO drCur = new OneSizeToEndBoxUpBillDetailInfoDTO();
            drCur.setCmsCode("2");
            drCur.setLevelNo(null);
            OneSizeToEndOutBoxPrintDTO result2 = oneSizeToEndServiceImpl.getBoxItemLine("2", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            drCur.setLevelNo("77.888.999");
            result2 = oneSizeToEndServiceImpl.getBoxItemLine("2", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            result2 = oneSizeToEndServiceImpl.getBoxItemLine("10", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            drCur.setLevelNo("77.88.99.00");
            result2 = oneSizeToEndServiceImpl.getBoxItemLine("2", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            result2 = oneSizeToEndServiceImpl.getBoxItemLine("10", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            drCur.setLevelNo("77.88.99");
            dto99.setItemBarCode("2");
            oneSizeToEndSqlInDTO3.add(dto99);
            result2 = oneSizeToEndServiceImpl.getBoxItemLine("2", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            result2 = oneSizeToEndServiceImpl.getBoxItemLine("10", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            drCur.setLevelNo("77.88.99.00");
            dto99.setItemBarCode("2");
            oneSizeToEndSqlInDTO4.add(dto99);
            result2 = oneSizeToEndServiceImpl.getBoxItemLine("2", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

            result2 = oneSizeToEndServiceImpl.getBoxItemLine("10", oneSizeToEndSqlInDTO3, oneSizeToEndSqlInDTO4, "", drCur);
            Assert.assertEquals(result2.getQty(), null);

        } catch (Exception ex) {
        }
        /* Ended by AICoder, pid:xcdb4x2cabme3a314792096cf1ae4355d607998d */
    }

    /* Started by AICoder, pid:d2d4cb6cdc2d46fbbe20fa4c0a818d73 */
    @Test
    public void getProjectNoAndProjectName() {
        OneSizeToEndOutBoxPrintOutDTO outBoxPrintOut = new OneSizeToEndOutBoxPrintOutDTO();
        List<OneSizeToEndBoxUpBillInfoDTO> boxUpBillInfo = new ArrayList<>();
        OneSizeToEndBoxUpBillInfoDTO dto = new OneSizeToEndBoxUpBillInfoDTO();
        boxUpBillInfo.add(dto);
        String projectNo = "1111";
        String projectName = "2222";
        String templanguage = "";
        oneSizeToEndServiceImpl.getProjectNoAndProjectName(outBoxPrintOut, boxUpBillInfo, projectNo, projectName, templanguage);
        Assert.assertEquals(true, true);
        projectNo = "";
        projectName = "";
        oneSizeToEndServiceImpl.getProjectNoAndProjectName(outBoxPrintOut, boxUpBillInfo, projectNo, projectName, templanguage);
        Assert.assertEquals(true, true);
        templanguage = "中文";
        oneSizeToEndServiceImpl.getProjectNoAndProjectName(outBoxPrintOut, boxUpBillInfo, projectNo, projectName, templanguage);
        Assert.assertEquals(true, true);
        boxUpBillInfo = new ArrayList<>();
        dto = new OneSizeToEndBoxUpBillInfoDTO();
        dto.setProjectName("3333");
        dto.setProjectNumber("4444");
        boxUpBillInfo.add(dto);
        oneSizeToEndServiceImpl.getProjectNoAndProjectName(outBoxPrintOut, boxUpBillInfo, projectNo, projectName, templanguage);
        Assert.assertEquals(true, true);
    }/* Ended by AICoder, pid:d2d4cb6cdc2d46fbbe20fa4c0a818d73 */

    /* Started by AICoder, pid:ce3b8853ce114870be26307593f7353f */
    @Test
    public void getPurchaseOrderNo()  throws Exception{

        OneSizeToEndOutBoxPrintOutDTO outBoxPrintOut = new OneSizeToEndOutBoxPrintOutDTO();
        String templanguage = "";
        List<OneSizeToEndBoxUpBillInfoDTO> boxUpBillInfo = new ArrayList<>();
        String poNumber = "1111";
        String customerErpPoNumber = "2222";
        String customerPo  = "55";
        PowerMockito.when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.any())).thenReturn(null);
        oneSizeToEndServiceImpl.getPurchaseOrderNo(outBoxPrintOut, templanguage, boxUpBillInfo, poNumber, customerErpPoNumber);
        Assert.assertEquals(true, true);

        poNumber = "";
        customerErpPoNumber = "";
        OneSizeToEndBoxUpBillInfoDTO dto = new OneSizeToEndBoxUpBillInfoDTO();
        dto.setCustomerPo("333");
        boxUpBillInfo.add(dto);
        oneSizeToEndServiceImpl.getPurchaseOrderNo(outBoxPrintOut, templanguage, boxUpBillInfo, poNumber, customerErpPoNumber);
        Assert.assertEquals(true, true);

        boxUpBillInfo = new ArrayList<>();
        dto = new OneSizeToEndBoxUpBillInfoDTO();
        dto.setCustomerPo("");
        boxUpBillInfo.add(dto);
        oneSizeToEndServiceImpl.getPurchaseOrderNo(outBoxPrintOut, templanguage, boxUpBillInfo, poNumber, customerErpPoNumber);
        Assert.assertEquals(true, true);

        templanguage = "中文";
        oneSizeToEndServiceImpl.getPurchaseOrderNo(outBoxPrintOut, templanguage, boxUpBillInfo, poNumber, customerErpPoNumber);
        Assert.assertEquals(true, true);


        PowerMockito.when(oneSizeToEndSFCRepository.getPurchaseOrderNo(Mockito.any())).thenReturn(customerPo);
        oneSizeToEndServiceImpl.getPurchaseOrderNo(outBoxPrintOut, templanguage, boxUpBillInfo, poNumber, customerErpPoNumber);
        Assert.assertEquals(true, true);
    }/* Ended by AICoder, pid:ce3b8853ce114870be26307593f7353f */
}
