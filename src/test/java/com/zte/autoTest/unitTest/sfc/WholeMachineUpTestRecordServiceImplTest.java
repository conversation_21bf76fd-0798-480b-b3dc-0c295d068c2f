/* Started by AICoder, pid:64f94ba3bftc0db148980bcb628c238be4e2c5b6 */
/* Started by AICoder, pid:g2a1al1cb1l393b1417a0b5c1002d78c0d259a02 */
package com.zte.autoTest.unitTest.sfc;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.CpmContractEntitiesService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsForwardTencentService;
import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsForwardTencentServiceImpl;
import com.zte.application.sfc.BarcodeDataUploadRecordService;
import com.zte.application.sfc.WholeMachineUpTestRecordService;
import com.zte.application.sfc.impl.WholeMachineUpTestRecordServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MptTestLog;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository;
import com.zte.domain.model.datawb.ZmsForwardTencentRepository;
import com.zte.domain.model.sfc.WholeMachineUpTestRecordRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.sfc.CpmContractEntitiesQueryDTO;
import com.zte.interfaces.dto.sfc.WholeMachineUpTestRecordEntityDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.ReflectUtil;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class})
public class WholeMachineUpTestRecordServiceImplTest {

    @InjectMocks
    private WholeMachineUpTestRecordServiceImpl wholeMachineUpTestRecordService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private WholeMachineUpTestRecordRepository wholeMachineUpTestRecordRepository;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Mock
    private CpmContractEntitiesService cpmContractEntitiesService;

    @Mock
    private BarcodeDataUploadRecordService barcodeDataUploadRecordService;

    @Mock
    WholeMachineUpTestRecordService service;

    @Mock
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;

    @Mock
    private MdsRemoteService mdsRemoteService;

    @Mock
    private CfgCodeRuleItemService cfgCodeRuleItemService;
    @Mock
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadService;

    @Mock
    IdGenerator idGenerator;

    @Mock
    ZmsForwardTencentRepository zmsForwardTencentRepository;

    @Mock
    ZmsForwardTencentService zmsForwardTencentService;

    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"suffix","2");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"pageSize","2");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"statusStr","调试,检验,待包");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"descLike","-M");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"messageType","type");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"uploadPhase","0,2");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"b2bSize","20");
        ReflectUtil.setFieldValue(wholeMachineUpTestRecordService,"tencentName","腾讯");
    }

    @Test
    public void setInterceptedPartDc()throws Exception {
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        cpmContractEntitiesDTO.setCustomer("2");
        cpmContractEntitiesDTO.setBatchRuleDTOList(new ArrayList<>());
        List<MdsProblemsDTO> problemsDTOS = new ArrayList<>();
        problemsDTOS.add(new MdsProblemsDTO(){{setComponentItemcode("22");setComponentSn("22");}});
        problemsDTOS.add(new MdsProblemsDTO(){{setComponentItemcode("223");setComponentSn("22");}});

        Map<String, CustomerItemsDTO> customerItemsDTOMap = new HashMap<>();
        customerItemsDTOMap.put("22",new CustomerItemsDTO(){{setCustomerSupplier("2");setCustomerComponentType("2");}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setInterceptedPartDc",cpmContractEntitiesDTO,problemsDTOS,customerItemsDTOMap,new WholeMachineUpTestRecordEntityDTO());
        Assert.assertNotNull(cpmContractEntitiesDTO);

        PowerMockito.when(zmsForwardTencentService.getProductionBatch(anyList(),any(),any(),any())).thenReturn("2");
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setInterceptedPartDc",cpmContractEntitiesDTO,problemsDTOS,customerItemsDTOMap,new WholeMachineUpTestRecordEntityDTO());
        Assert.assertNotNull(cpmContractEntitiesDTO);
    }
    @Test
    public void setProdlemItemInfo()throws Exception {
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        cpmContractEntitiesDTO.setCustomer("2");
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setProdlemItemInfo",new ArrayList(){{add(new CustomerItemsDTO());}},new WholeMachineUpTestRecordEntityDTO());
        Assert.assertNotNull(cpmContractEntitiesDTO);
    }
    @Test
    public void getProblemsCustomerItemsInfoList()throws Exception {
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        cpmContractEntitiesDTO.setCustomer("2");
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(any(),any())).thenReturn(new ArrayList(){{add(new CustomerItemsDTO());}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"getProblemsCustomerItemsInfoList",cpmContractEntitiesDTO,new ArrayList<>());
        Assert.assertNotNull(cpmContractEntitiesDTO);
    }
    @Test
    public void testBatchInsert() {
        Assert.assertEquals(0, wholeMachineUpTestRecordService.batchInsert(Collections.emptyList()));
        List<WholeMachineUpTestRecordEntityDTO> singleItem = Collections.singletonList(new WholeMachineUpTestRecordEntityDTO());
        Assert.assertEquals(0, wholeMachineUpTestRecordService.batchInsert(singleItem));
    }

    @Test
    public void testUpdateUploadStatus() throws JsonProcessingException {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();

        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);
        // Test with empty JSON data
        customerDataLogDTO.setJsonData("{\"data\":[{\"createdBy\":\"0668000830\",\"entityName\":\"RSVRG228AD-M20240200067\",\"faultDescription\":\"000000\",\"interceptedPartDc\":\"无\",\"interceptedPartDesc\":\"Service\",\"interceptedPartPn\":\"无\",\"interceptedPartSlot\":\"无\",\"interceptedPartSn\":\"无\",\"interceptedPartTestCase\":\"无\",\"interceptedPartTestDuration\":\"\",\"itemCode\":\"129036231036\",\"lastUpdatedBy\":\"0668000830\",\"manufacturer\":\"ZTE\",\"partFaultType\":\"无\",\"partFaultVendor\":\"无\",\"productionLineInfo\":\"ZTE-中兴智能科技南京有限公司\",\"serverFaultType\":\"DZH\",\"serverSn\":\"219415761672\",\"stationId\":\"DZH22696032\",\"strDeviceClassName\":\"B72Q1-B1DD2M\",\"strVersion\":\"N2\",\"tdtName\":\"G228-AD型服务器\",\"testEndTime\":\"2024-04-16 05:20:40\",\"testLogName\":\"无\",\"testProgramVersion\":\"无\",\"testResult\":\"F\",\"testStartTime\":\"2024-04-16 05:20:30\",\"testStation\":\"DZH\",\"uploadPhase\":\"0\"}]}");
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);

        // Test with status set to "CY"
        customerDataLogDTO.setStatus("CY");
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);

        // Test with valid JSON data
        try{
            wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> dataTypeList = new ArrayList<>();
        dataTypeList.add(new SysLookupValues());
        dataTypeList.add(new SysLookupValues(){{setLookupCode(new BigDecimal("2"));}});
        dataTypeList.add(new SysLookupValues(){{setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824009200004));}});
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240092))).thenReturn(dataTypeList);
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);

        PowerMockito.when(wholeMachineUpTestRecordRepository.getCountByEntityName(any())).thenReturn(1);
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_B2B_TENCENTMACHINELOG);
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);
        customerDataLogDTO.setStatus("CY");
        customerDataLogDTO.setJsonData("{\"data\":[]}");
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
        customerDataLogDTO.setJsonData("{\"faultDescription\":\"无\",\"file\":\"https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/CLOUDMDS/ce8cb367-e30d-4289-b608-6310d2a00c64\",\"manufacturer\":\"ZTE\",\"page\":0,\"productionLineInfo\":\"ZTE-中兴智能科技南京有限公司\",\"rows\":0,\"serverSn\":\"219415761672\",\"testEndTime\":\"2024-02-20 15:03:15\",\"testProgramVersion\":\"无\",\"testResult\":\"P\",\"testStartTime\":\"2024-02-20 15:03:05\"}");
        Assert.assertNotNull(customerDataLogDTO);
        wholeMachineUpTestRecordService.updateUploadStatus(customerDataLogDTO);
    }
    /* Ended by AICoder, pid:g2a1al1cb1l393b1417a0b5c1002d78c0d259a02 */

    @Test
    public void uploadTestRecords() {
        List<String> taskNoList = new ArrayList<>();
        WholeMachineUpTestRecordEntityDTO entityDTO = new WholeMachineUpTestRecordEntityDTO();
        entityDTO.setEntityNameList(taskNoList);
        wholeMachineUpTestRecordService.uploadTestRecords("",entityDTO);
        Assert.assertEquals(taskNoList.size(),0);
        List<SysLookupValues> userAddressList = new ArrayList<>();
        userAddressList.add(new SysLookupValues(){{setLookupMeaning("腾讯");setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD));}});

        userAddressList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD));}});
        userAddressList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_ENAME));}});
        userAddressList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_STAT));}});
        userAddressList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_BOQ));}});
        userAddressList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(2));}});
        userAddressList.add(new SysLookupValues());
        try{
            wholeMachineUpTestRecordService.uploadTestRecords("",entityDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPES_3020041))).thenReturn(userAddressList);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_FORWARD_TENCENT))).thenReturn(userAddressList);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240063))).thenReturn(null);
        try{
            wholeMachineUpTestRecordService.uploadTestRecords("",entityDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> messageTypeList = new ArrayList<>();
        messageTypeList.add(new SysLookupValues());

        messageTypeList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD));}});
        messageTypeList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824009200004));}});

        messageTypeList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824006300013));}});

        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240063))).thenReturn(null);

        try{
            wholeMachineUpTestRecordService.uploadTestRecords("",entityDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240063))).thenReturn(messageTypeList);
        try{
            wholeMachineUpTestRecordService.uploadTestRecords("",entityDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240092))).thenReturn(messageTypeList);
        wholeMachineUpTestRecordService.uploadTestRecords("",entityDTO);
        Assert.assertEquals(taskNoList.size(),0);
    }

    @Test
    public void batchProcessing()throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        List<CpmContractEntitiesDTO> contractEntities = new ArrayList();
        List<SysLookupValues> userAddressList = new ArrayList<>();
        when(SpringContextUtil.getBean(WholeMachineUpTestRecordService.class)).thenReturn(service);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"batchProcessing",new CpmContractEntitiesQueryDTO(),contractEntities,userAddressList);
        Assert.assertEquals(contractEntities.size(),0);

        when(SpringContextUtil.getBean(WholeMachineUpTestRecordService.class)).thenReturn(null);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"batchProcessing",new CpmContractEntitiesQueryDTO(),contractEntities,userAddressList);
        Assert.assertEquals(contractEntities.size(),0);
    }

    @Test
    public void batchProcessingOfData() {
        List<CpmContractEntitiesDTO> contractEntities = new ArrayList();
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");}});
        List<SysLookupValues> userAddressList = new ArrayList<>();
        userAddressList.add(new SysLookupValues(){{setLookupMeaning("2");}});

        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean(WholeMachineUpTestRecordService.class)).thenReturn(service);
        wholeMachineUpTestRecordService.batchProcessingOfData(new CpmContractEntitiesQueryDTO(),contractEntities,userAddressList);
        Assert.assertEquals(userAddressList.size(),1);
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        configDescList.add(new ZmsCbomInfoDTO(){{setEntityName("taskNo1");setCbomNameCn("2");}});
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(any(),any(),any())).thenReturn(configDescList);
        wholeMachineUpTestRecordService.batchProcessingOfData(new CpmContractEntitiesQueryDTO(),contractEntities,userAddressList);
        Assert.assertEquals(userAddressList.size(),1);

    }

    @Test
    public void setServerSn()throws Exception {
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();

        List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList = new ArrayList<>();
        cpmConfigItemAssembleDTOList.add(new CpmConfigItemAssembleDTO(){{setEntityId("taskNo1");}});
        cpmConfigItemAssembleDTOList.add(new CpmConfigItemAssembleDTO(){{setItemCode("itemCode1");setItemBarcode("sn1");}});
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(any())).thenReturn(cpmConfigItemAssembleDTOList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setServerSn",cpmContractEntitiesDTO,"2");
        Assert.assertNotNull(cpmContractEntitiesDTO);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("4");}});
        PowerMockito.when(centerfactoryRemoteService
                .getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setServerSn",cpmContractEntitiesDTO,"2");
        Assert.assertNotNull(cpmContractEntitiesDTO);

        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode2");setProjectType("3");}});
        PowerMockito.when(centerfactoryRemoteService
                .getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setServerSn",cpmContractEntitiesDTO,"2");
        Assert.assertNotNull(cpmContractEntitiesDTO);
        customerItemsDTOList.clear();
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("3");}});
        PowerMockito.when(centerfactoryRemoteService
                .getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"setServerSn",cpmContractEntitiesDTO,"2");
        Assert.assertNotNull(cpmContractEntitiesDTO);
    }

    @Test
    public void writeVariousData()throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        when(SpringContextUtil.getBean(WholeMachineUpTestRecordService.class)).thenReturn(service);
        List<CpmContractEntitiesDTO> contractEntities = new ArrayList();
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");}});
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        List<WholeMachineUpTestRecordEntityDTO> records = Collections.singletonList(new WholeMachineUpTestRecordEntityDTO());
        cpmContractEntitiesDTO.setWholeMachineUpTestRecordEntityDTOList(records);
        contractEntities.add(cpmContractEntitiesDTO);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"writeVariousData",new CpmContractEntitiesQueryDTO(),contractEntities,new HashMap<>(),new HashMap<>());
        Assert.assertNotNull(cpmContractEntitiesDTO);

        cpmContractEntitiesDTO.setServiceSnCustomerItemsDTO(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("4");}});
        cpmContractEntitiesDTO.setCpmConfigItemAssembleDTO(new CpmConfigItemAssembleDTO(){{setEntityId("taskNo1");}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"writeVariousData",new CpmContractEntitiesQueryDTO(),contractEntities,new HashMap<>(),new HashMap<>());
        Assert.assertNotNull(cpmContractEntitiesDTO);
    }

    @Test
    public void generateDocumentsForPilotTesting()throws Exception {
        List<CpmContractEntitiesDTO> contractEntities = new ArrayList();
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");}});
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");setServiceSn("sn");}});
        List<WholeMachineUpTestRecordEntityDTO> records = Collections.singletonList(new WholeMachineUpTestRecordEntityDTO());
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");setServiceSn("sn");setWholeMachineUpTestRecordEntityDTOList(records);}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateDocumentsForPilotTesting",contractEntities);
        Assert.assertNotNull(contractEntities);

    }
    @Test
    public void setTestTime()throws Exception {
        Whitebox.invokeMethod(wholeMachineUpTestRecordService, "setTestTime", new MdsStationsDTO(){{setStartTime(111L);setEndTime(111L);}}, new WholeMachineUpTestRecordEntityDTO());
        Assert.assertNotNull(new MdsStationsDTO());
    }

    @Test
    public void setStrDeviceClassName()throws Exception {
        Map<String, ZmsCbomInfoDTO> configDescMap = new HashMap<>();
        configDescMap.put("1",new ZmsCbomInfoDTO());
        Whitebox.invokeMethod(wholeMachineUpTestRecordService, "setStrDeviceClassName", new CpmContractEntitiesDTO(){{setEntityName("1");}},configDescMap, new WholeMachineUpTestRecordEntityDTO());
        Assert.assertNotNull(configDescMap);
    }

    @Test
    public void filterUploadedData()throws Exception {
        List<CpmContractEntitiesDTO> contractEntities = new ArrayList();
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");}});
        List<WholeMachineUpTestRecordEntityDTO> records = Collections.singletonList(new WholeMachineUpTestRecordEntityDTO(){{setStationId("id");}});
        contractEntities.add(new CpmContractEntitiesDTO(){{setEntityName("taskNo1");setServiceSn("sn");setWholeMachineUpTestRecordEntityDTOList(records);}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService, "filterUploadedData", contractEntities);
        Assert.assertNotNull(contractEntities);

        List<WholeMachineUpTestRecordEntityDTO> tempList = new ArrayList<>();
        tempList.add(new WholeMachineUpTestRecordEntityDTO(){{setStationId("id");}});
        PowerMockito.when(wholeMachineUpTestRecordRepository.getListByStationIdList(any(),any())).thenReturn(tempList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService, "filterUploadedData", contractEntities);
        Assert.assertNotNull(contractEntities);

    }

    @Test
    public void generateWholeMachineUpTestRecordEntityDTOList()throws Exception {
        Map<String, MdsFeedbackProductionStationTestingInfoDTO> mdfMap = new HashMap<>();
        Map<String, String> configDescMap = new HashMap<>();
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        cpmContractEntitiesDTO.setServiceSn("sn");
        mdfMap.put("sn",new MdsFeedbackProductionStationTestingInfoDTO());
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        cpmContractEntitiesDTO.setCpmConfigItemAssembleDTO(new CpmConfigItemAssembleDTO());
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        cpmContractEntitiesDTO.setServiceSnCustomerItemsDTO(new CustomerItemsDTO());
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("4");}});
        cpmContractEntitiesDTO.setCustomerItemsDTOList(customerItemsDTOList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        mdfMap.put("sn",new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Collections.singletonList(new MdsStationsDTO(){{setStationId("stationName1");}}));}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        List<MdsProblemsDTO> problemsDTOS = new ArrayList<>();
        problemsDTOS.add(new MdsProblemsDTO(){{setProblemId("problemName1");}});
        mdfMap.put("sn",new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Collections.singletonList(new MdsStationsDTO(){{setProblems(problemsDTOS);setName(Constant.tencentUpload.RUN_IN);}}));}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        mdfMap.put("sn",new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Collections.singletonList(new MdsStationsDTO(){{setProblems(problemsDTOS);setName("1"+Constant.tencentUpload.RUN_IN);}}));}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

        problemsDTOS.clear();
        problemsDTOS.add(new MdsProblemsDTO(){{setComponentItemcode("itemCode1");}});
        problemsDTOS.add(new MdsProblemsDTO(){{setComponentSn("sn2");}});
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("4");setCustomerComponentType("itemCode1");setCustomerSupplier("2");}});
        cpmContractEntitiesDTO.setCustomerItemsDTOList(customerItemsDTOList);
        mdfMap.put("sn",new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Collections.singletonList(new MdsStationsDTO(){{setProblems(problemsDTOS);setName("1"+Constant.tencentUpload.RUN_IN);}}));}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"generateWholeMachineUpTestRecordEntityDTOList",mdfMap,configDescMap,cpmContractEntitiesDTO);
        Assert.assertNotNull(mdfMap);

    }

    @Test
    public void uploadMachineTestLogs() throws Exception{
        WholeMachineUpTestRecordEntityDTO record = new WholeMachineUpTestRecordEntityDTO();
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        List<WholeMachineUpTestRecordEntityDTO> pageList = new ArrayList<>();
        record.setRecordId("123");
        pageList.add(record);
        record.setUploadPhase("1");
        PowerMockito.when(wholeMachineUpTestRecordRepository.pageList(Mockito.any())).thenReturn(pageList);
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn("123");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        List<SysLookupValues> userAddressList = new ArrayList<>();
        userAddressList.add(new SysLookupValues(){{setDescription("2");}});
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(any())).thenReturn(userAddressList);
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setUploadPhase("3");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setUploadPhase("1");
        record.setIsMpt("Y");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("3");}});
        PowerMockito.when(centerfactoryRemoteService
                .getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setIsMpt("N");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = new ArrayList<>();
        MdsFeedbackProductionStationFileDTO fileDTO = new MdsFeedbackProductionStationFileDTO();
        stationFileDTOList.add(fileDTO);
        MdsFeedbackProductionStationFileDTO fileDTO1 = new MdsFeedbackProductionStationFileDTO();
        stationFileDTOList.add(fileDTO1);
        PowerMockito.when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(Mockito.any(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(stationFileDTOList);
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setIsMpt("Y");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        stationFileDTOList.remove(fileDTO1);
        stationFileDTOList.remove(fileDTO);
        fileDTO.setLogName("321");
        stationFileDTOList.add(fileDTO);
        PowerMockito.when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(Mockito.any(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(stationFileDTOList);
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setIsMpt("N");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");

        stationFileDTOList.remove(fileDTO);
        fileDTO.setStationLog("321");
        stationFileDTOList.add(fileDTO);
        PowerMockito.when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(Mockito.any(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(stationFileDTOList);
        record.setItemCode("321321");
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("321321");setProjectType("3");}});
        try {
            wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        record.setUploadPhase("4");
        try {
            wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        record.setUploadPhase("3");
        try {
            wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        configDescList.add(new ZmsCbomInfoDTO(){{setEntityName("taskNo1");setQuotationMaterialNameCn("2");}});
        configDescList.add(new ZmsCbomInfoDTO(){{setEntityName("321321");setQuotationMaterialNameCn("123");}});
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(any(),any(),any())).thenReturn(new ArrayList<>());
        try {
            wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(any(),any(),any())).thenReturn(configDescList);
        record.setItemCode("111111");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setUploadPhase("3");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setUploadPhase("6");
        try {
            wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        record.setUploadPhase("7");
        record.setIsMpt("Y");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        PowerMockito.when(zmsDeviceInventoryUploadService.getCbomInfo(Mockito.anyList(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        ZmsCbomInfoDTO infoDTO = new ZmsCbomInfoDTO();
        infoDTO.setCbomNameCn("123");
        zmsCbomInfoDTOList.add(infoDTO);
        ZmsCbomInfoDTO infoDTO1 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTOList.add(infoDTO1);
        ZmsCbomInfoDTO infoDTO2 = getZmsCbomInfoDTO();
        infoDTO2.setCbomNameCn("123");
        zmsCbomInfoDTOList.add(infoDTO2);

        PowerMockito.when(zmsDeviceInventoryUploadService.getCbomInfo(Mockito.anyList(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        PowerMockito.when(wholeMachineUpTestRecordRepository.pageList(Mockito.any())).thenReturn(pageList);
        record.setEntityName("321321");
        wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        record.setItemCode("321321");
        try {
            wholeMachineUpTestRecordService.uploadMachineTestLogs(record, "10313234");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }


    }

    private ZmsCbomInfoDTO getZmsCbomInfoDTO() {
        List<ZmsExtendedAttributeDTO> zmsExtendedAttributeDTOS = new ArrayList<>();
        ZmsExtendedAttributeDTO attributeDTO = new ZmsExtendedAttributeDTO();
        attributeDTO.setCbomExtentionZh("321312");
        zmsExtendedAttributeDTOS.add(attributeDTO);
        ZmsExtendedAttributeDTO attributeDTO1 = new ZmsExtendedAttributeDTO();
        attributeDTO1.setCbomExtentionZh("整机编码");
        attributeDTO1.setCbomExtentionValue("32131");
        zmsExtendedAttributeDTOS.add(attributeDTO1);
        ZmsCbomInfoDTO infoDTO2 = new ZmsCbomInfoDTO();
        infoDTO2.setCbomNameCn("123");
        infoDTO2.setEntityName("321321");
        infoDTO2.setExtendedAttributes(zmsExtendedAttributeDTOS);
        return infoDTO2;
    }

    @Test
    public void uploadMptTestLog() throws Exception {
        List<WholeMachineUpTestRecordEntityDTO> pageList = new ArrayList<>();
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"getVersionIdMap",new ArrayList<>());
        WholeMachineUpTestRecordEntityDTO record = new WholeMachineUpTestRecordEntityDTO();
        record.setEntityName("321321");
        pageList.add(record);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"pushMptTestLog",pageList, "Y", "1", "123123", "3213");
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"pushMachineRecordLogsAgain",pageList, "6", "123123");
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        configDescList.add(new ZmsCbomInfoDTO(){{setEntityName("taskNo1");setQuotationMaterialNameCn("2");}});
        configDescList.add(new ZmsCbomInfoDTO(){{setEntityName("321321");setQuotationMaterialNameCn("123");}});
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(any(),any(),any())).thenReturn(configDescList);

        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        ZmsCbomInfoDTO infoDTO = new ZmsCbomInfoDTO();
        infoDTO.setCbomNameCn("123");
        zmsCbomInfoDTOList.add(infoDTO);
        ZmsCbomInfoDTO infoDTO1 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTOList.add(infoDTO1);
        ZmsCbomInfoDTO infoDTO2 = getZmsCbomInfoDTO();
        infoDTO2.setCbomNameCn("123");
        zmsCbomInfoDTOList.add(infoDTO2);

        PowerMockito.when(zmsDeviceInventoryUploadService.getCbomInfo(Mockito.anyList(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);

        List<SysLookupValues> userAddressList = new ArrayList<>();
        userAddressList.add(new SysLookupValues(){{setDescription("2");}});
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(any())).thenReturn(userAddressList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"pushMptTestLog",pageList, "Y", "4", "123123", "3213");

        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("itemCode1");setProjectType("3");}});
        PowerMockito.when(centerfactoryRemoteService
                .getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        record.setItemCode("itemCode1");
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"pushMptTestLog",pageList, "Y", "4", "123123", "3213");
        customerItemsDTOList.add(new CustomerItemsDTO(){{setZteCode("321321");setProjectType("3");}});
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"pushMptTestLog",pageList, "Y", "4", "123123", "3213");
        List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = new ArrayList<>();
        MdsFeedbackProductionStationFileDTO fileDTO = new MdsFeedbackProductionStationFileDTO();
        stationFileDTOList.add(fileDTO);
        fileDTO.setLogName("321");
        stationFileDTOList.add(fileDTO);
        PowerMockito.when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(Mockito.any(), Mockito.anyList(), Mockito.anyBoolean())).thenReturn(stationFileDTOList);
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"pushMptTestLog",pageList, "Y", "4", "123123", "3213");
        Whitebox.invokeMethod(wholeMachineUpTestRecordService,"getVersionId",new ArrayList<>());
        fileDTO.setStationLog("321");
        try {
            Whitebox.invokeMethod(wholeMachineUpTestRecordService, "pushMptTestLog", pageList, "Y", "4", "123123", "3213");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

    }


    @Test
    public void updateMptUploadStatus() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        wholeMachineUpTestRecordService.updateMptUploadStatus(customerDataLogDTO);
        MptTestLog log = new MptTestLog();
        customerDataLogDTO.setJsonData(JSON.toJSONString(customerDataLogDTO));
        wholeMachineUpTestRecordService.updateMptUploadStatus(customerDataLogDTO);
        customerDataLogDTO.setStatus("CY");
        wholeMachineUpTestRecordService.updateMptUploadStatus(customerDataLogDTO);
        log.setServerSn("!23");
        customerDataLogDTO.setJsonData(JSON.toJSONString(log));
        try {
            wholeMachineUpTestRecordService.updateMptUploadStatus(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void  mptTestLogReUpload() throws Exception{
        WholeMachineUpTestRecordEntityDTO record = new WholeMachineUpTestRecordEntityDTO();
        try {
            wholeMachineUpTestRecordService.mptTestLogReUpload(record, "123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BARCODE_NEED_ENTERED, e.getMessage());
        }
        List<String> snList = new ArrayList<>();
        for (int i = 0; i < 202; i++) {
            snList.add(i + "");
        }
        record.setSnList(snList);
        try {
            wholeMachineUpTestRecordService.mptTestLogReUpload(record, "123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PAGE_TOO_BIG, e.getMessage());
        }
        snList.remove(201);
        snList.remove(200);
        snList.remove(199);
        record.setSnList(snList);
        try {
            wholeMachineUpTestRecordService.mptTestLogReUpload(record, "123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PAGE_TOO_BIG, e.getMessage());
        }
    }

    @Test
    public void pushMachineUpTestLogDataToB2B () throws Exception{
        List<MptTestLog> mptList = new ArrayList<>();
        wholeMachineUpTestRecordService.pushMachineUpTestLogDataToB2B(new ArrayList<>(), new ArrayList<>(), mptList);
        MptTestLog log = new MptTestLog();
        mptList.add(log);
        try {
            wholeMachineUpTestRecordService.pushMachineUpTestLogDataToB2B(new ArrayList<>(), new ArrayList<>(), mptList);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void getCpmContractEntitiesQueryDTO () throws Exception{
        try {
            Whitebox.invokeMethod(wholeMachineUpTestRecordService,"getCpmContractEntitiesQueryDTO",new ArrayList<>(),new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

    }
}
/* Ended by AICoder, pid:g2a1al1cb1l393b1417a0b5c1002d78c0d259a02 */

/* Ended by AICoder, pid:64f94ba3bftc0db148980bcb628c238be4e2c5b6 */