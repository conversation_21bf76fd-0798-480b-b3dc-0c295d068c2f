/* Started by AICoder, pid:sc477m8beczb49c148740a0d60fd4667bd05247d */
package com.zte.autoTest.unitTest.sfc;

import com.zte.application.sfc.impl.BarcodeDataUploadRecordServiceImpl;
import com.zte.domain.model.sfc.BarcodeDataUploadRecordRepository;
import com.zte.interfaces.dto.sfc.BarcodeDataUploadRecordEntityDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
public class BarcodeDataUploadRecordServiceImplTest {
    @InjectMocks
    private BarcodeDataUploadRecordServiceImpl barcodeDataUploadRecordService;
    @Mock
    private BarcodeDataUploadRecordRepository barcodeDataUploadRecordRepository;

    @Test
    public void getListByTaskNoList() throws Exception {
        Assert.assertEquals(0,barcodeDataUploadRecordService.getListByTaskNoList(Collections.emptyList()).size());
        Assert.assertEquals(0,barcodeDataUploadRecordService.getListByTaskNoList(new ArrayList(){{add("2");}}).size());
        List<BarcodeDataUploadRecordEntityDTO> batchResults = new ArrayList<>();
        batchResults.add(new BarcodeDataUploadRecordEntityDTO());
        PowerMockito.when(barcodeDataUploadRecordRepository.getListByTaskNoList(any())).thenReturn(batchResults);
        Assert.assertEquals(1,barcodeDataUploadRecordService.getListByTaskNoList(new ArrayList(){{add("2");}}).size());
    }

    @Test
    public void batchInsertOrUpdate() throws Exception {
        List<BarcodeDataUploadRecordEntityDTO> records = new ArrayList<>();
        barcodeDataUploadRecordService.batchInsertOrUpdate(records);
        Assert.assertEquals(0,records.size());
        records.add(new BarcodeDataUploadRecordEntityDTO());
        barcodeDataUploadRecordService.batchInsertOrUpdate(records);
        Assert.assertEquals(1,records.size());
    }
}
/* Ended by AICoder, pid:sc477m8beczb49c148740a0d60fd4667bd05247d */