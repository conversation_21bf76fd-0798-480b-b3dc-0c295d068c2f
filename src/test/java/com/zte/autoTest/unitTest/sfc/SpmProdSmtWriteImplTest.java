package com.zte.autoTest.unitTest.sfc;

import com.zte.application.sfc.impl.SpmProdSmtWriteImpl;
import com.zte.domain.model.sfc.SpmProdSmtWriteRepository;
import com.zte.interfaces.dto.kxbariii.ProdSmtWriteDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-15 11:04
 */
@RunWith(PowerMockRunner.class)
public class SpmProdSmtWriteImplTest {
    @InjectMocks
    private SpmProdSmtWriteImpl spmProdSmtWriteImpl;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private SpmProdSmtWriteRepository spmProdSmtWriteRepository;
    @Mock
    private ValueOperations<String, Object> valueOperations;

    @Before
    public void init() {
    }

    @Test
    public void insertPreBomBatch() throws Exception {
        List<ProdSmtWriteDTO> list = new LinkedList<>();
        ProdSmtWriteDTO a1 = new ProdSmtWriteDTO();
        a1.setBomNo("129122851048AOB");
        a1.setItemNo("129122851048AOB");
        list.add(a1);
        ProdSmtWriteDTO a2 = new ProdSmtWriteDTO();
        a2.setBomNo("129122851049AOB");
        a2.setItemNo("129122851049AOB");
        list.add(a2);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                Mockito.anyObject()))
                .thenReturn(true);

        List<ProdSmtWriteDTO> existList = new LinkedList<>();
        existList.add(a2);
        PowerMockito.when(spmProdSmtWriteRepository.selectPreBomBatch(Mockito.anyList()))
                .thenReturn(existList);

        spmProdSmtWriteImpl.insertPreBomBatch(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void insertPreItemBatch() throws Exception {
        List<ProdSmtWriteDTO> list = new LinkedList<>();
        ProdSmtWriteDTO a1 = new ProdSmtWriteDTO();
        a1.setBomNo("129122851048AOB");
        a1.setItemNo("129122851048AOB");
        list.add(a1);
        ProdSmtWriteDTO a2 = new ProdSmtWriteDTO();
        a2.setBomNo("129122851049AOB");
        a2.setItemNo("129122851049AOB");
        list.add(a2);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                Mockito.anyObject()))
                .thenReturn(true);

        List<ProdSmtWriteDTO> existList = new LinkedList<>();
        existList.add(a2);
        PowerMockito.when(spmProdSmtWriteRepository.selectPreItemBatch(Mockito.anyList()))
                .thenReturn(existList);


        spmProdSmtWriteImpl.insertPreItemBatch(list);
        Assert.assertNotNull(existList);
    }


    @Test
    public void selectPreBomPage() {
        Page<ProdSmtWriteDTO> page = new Page<>();
        Page<ProdSmtWriteDTO> pageInfo = spmProdSmtWriteImpl.selectPreBomPage(page);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void selectPreItemPage() {
        Page<ProdSmtWriteDTO> page = new Page<>();
        Page<ProdSmtWriteDTO> pageInfo = spmProdSmtWriteImpl.selectPreItemPage(page);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void deletePreItemByItemNoList() {
        ProdSmtWriteDTO prodSmtWriteDTO = new ProdSmtWriteDTO();
        spmProdSmtWriteImpl.deletePreItemByItemNoList(prodSmtWriteDTO);
        prodSmtWriteDTO.setItemList(Arrays.asList("123"));
        Assert.assertNotNull(prodSmtWriteDTO);
    }

    @Test
    public void deletePreBomByBomList() {
        ProdSmtWriteDTO prodSmtWriteDTO = new ProdSmtWriteDTO();
        spmProdSmtWriteImpl.deletePreItemByItemNoList(prodSmtWriteDTO);

        prodSmtWriteDTO.setBomList(Arrays.asList("123"));
        spmProdSmtWriteImpl.deletePreItemByItemNoList(prodSmtWriteDTO);
        Assert.assertNotNull(prodSmtWriteDTO);
    }

    @Test
    public void insertOrUpdateBomPre() throws Exception {
        ProdSmtWriteDTO updateData = new ProdSmtWriteDTO();
        spmProdSmtWriteImpl.insertOrUpdateBomPre(updateData);

        updateData.setBomNo("123247751000ABB");
        ProdSmtWriteDTO a1 = new ProdSmtWriteDTO();
        a1.setItemNo("123247751000");
        List<ProdSmtWriteDTO> list = new LinkedList<>();
        list.add(a1);
        ProdSmtWriteDTO a2 = new ProdSmtWriteDTO();
        a2.setItemNo("ki");
        list.add(a2);
        spmProdSmtWriteImpl.insertOrUpdateBomPre(updateData);
        updateData.setChild(list);

        List<ProdSmtWriteDTO> writeDTOList = new LinkedList<>();
        ProdSmtWriteDTO b1 = new ProdSmtWriteDTO();
        b1.setItemNo("123247751000");
        writeDTOList.add(b1);
        ProdSmtWriteDTO b2 = new ProdSmtWriteDTO();
        b2.setItemNo("1232477510001");
        writeDTOList.add(b2);
        PowerMockito.when(spmProdSmtWriteRepository.selectPreBomBatch(Mockito.anyList()))
                .thenReturn(writeDTOList)
        ;
        spmProdSmtWriteImpl.insertOrUpdateBomPre(updateData);
        Assert.assertNotNull(updateData);
    }


}
