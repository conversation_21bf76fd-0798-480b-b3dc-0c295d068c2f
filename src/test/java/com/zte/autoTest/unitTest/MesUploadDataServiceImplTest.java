package com.zte.autoTest.unitTest;

import com.zte.application.MesGetDictInforService;
import com.zte.application.datawb.impl.MesUploadDataServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.MesUploadDataRepository;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class MesUploadDataServiceImplTest extends BaseTestCase {
    @InjectMocks
    private MesUploadDataServiceImpl mesUploadDataService;

    @Mock
    private MesGetDictInforService mesGetDictInforService;

    @Mock
    private MesUploadDataRepository mesUploadDataRepository;

    @Mock
    private EmailUtils emailUtils;

    @Test
    public void updateCsrStatusTest(){
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("xxx{1}");
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setContractNo("123");
        dto.setStatus("CY");
        mesUploadDataService.updateCsrStatus(dto);
        dto.setProjectPhase(Constant.PROJECT_PHASE_WAREHOUSE);
        dto.setStatus("CN");
        PowerMockito.when(mesGetDictInforService.getDicDescription(Mockito.any())).thenReturn(null);
        PowerMockito.doNothing().when(mesUploadDataRepository).updateFinishHeaderStatus(Mockito.any());
        PowerMockito.doNothing().when(mesUploadDataRepository).updateSampleUploadStatus(Mockito.any());
        mesUploadDataService.updateCsrStatus(dto);
        dto.setProjectPhase(Constant.PROJECT_PHASE_PROTOTYPE);
        PowerMockito.when(mesGetDictInforService.getDicDescription(Mockito.any())).thenReturn("10331519");
        PowerMockito.when(emailUtils.sendMail("", "", "", "", "")).thenReturn(true);
        mesUploadDataService.updateCsrStatus(dto);
        /* Started by AICoder, pid:52ada12c0bad4a5483d873c4d6d03291 */
        dto.setProjectPhase(Constant.PROJECT_PHASE_WEIXIN);
        mesUploadDataService.updateCsrStatus(dto);
        /* Ended by AICoder, pid:52ada12c0bad4a5483d873c4d6d03291 */
        String flag = "Y";
        Assert.assertTrue(flag.equals(Constant.FLAG_Y));
    }
}



