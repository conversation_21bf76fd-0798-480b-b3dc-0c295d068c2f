package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.SpmOpProdPlanInfoServiceImpl;
import com.zte.domain.model.SpmOpProductinfoRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @date 2023-02-09 11:28
 */
@RunWith(PowerMockRunner.class)
public class SpmOpProdPlanInfoServiceImplTest {
    @InjectMocks
    private SpmOpProdPlanInfoServiceImpl spmOpProdPlanInfoServiceImpl;
    @Mock
    private SpmOpProductinfoRepository spmOpProductinfoRepository;
    @Before
    public void init(){

    }

    @Test
    public void validateSubmitStatus() {
        PowerMockito.when(spmOpProductinfoRepository.getIsSubmitFromOpProdPlanInfo(Mockito.anyString()))
                .thenReturn(2L);
        spmOpProdPlanInfoServiceImpl.validateSubmitStatus("2");
        PowerMockito.when(spmOpProductinfoRepository.getIsSubmitFromOpProdPlanInfo(Mockito.anyString()))
                .thenReturn(null);
        Assert.assertFalse(spmOpProdPlanInfoServiceImpl.validateSubmitStatus("2"));
    }
}
