package com.zte.autoTest.unitTest;

import com.zte.application.datawb.EntryBoxAppliedOthersService;
import com.zte.application.datawb.MaterialConfigBindService;
import com.zte.application.datawb.TaskToBeQueriedService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.impl.CompleteMachineDataLogServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.ZmsCompleteMachineHeadRepository;
import com.zte.domain.model.ZmsCompleteMachineLineRepository;
import com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
public class CompleteMachineDataLogServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private CompleteMachineDataLogServiceImpl completeMachineDataLogService;
    @Mock
    private ZmsCompleteMachineHeadRepository zmsCompleteMachineHeadRepository;

    @Mock
    private ZmsCompleteMachineLineRepository zmsCompleteMachineLineRepository;

    @Mock
    private TaskToBeQueriedService taskToBeQueriedService;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private MaterialConfigBindService materialConfigBindService;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;

    @Mock
    private EntryBoxAppliedOthersService entryBoxAppliedOthersService;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Mock
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;
    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;

    @Test
    public void getMacByResourceNumber() throws Exception {
        List<SpSpecialityNalDTO> list = new ArrayList<>();
        completeMachineDataLogService.getMacByResourceNumber(null);
        list.add(new SpSpecialityNalDTO());
        List<String> param = new ArrayList<>();
        param.add("2");
        PowerMockito.when(zmsCompleteMachineHeadRepository.getMacByResourceNumber(any())).thenReturn(null);
        completeMachineDataLogService.getMacByResourceNumber(param);
        PowerMockito.when(zmsCompleteMachineHeadRepository.getMacByResourceNumber(any())).thenReturn(list);
        Assert.assertNotNull(completeMachineDataLogService.getMacByResourceNumber(param));
    }

    @Test
    public void uploadResourceInfoToMes() throws Exception {
        List<SpSpecialityNalDTO> list = new ArrayList<>();
        completeMachineDataLogService.uploadResourceInfoToMes(list);
        list.add(new SpSpecialityNalDTO());
        completeMachineDataLogService.uploadResourceInfoToMes(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void setOrderId() throws Exception {
        PowerMockito.when(wsmAssembleLinesService.getCustomerPONumber(any())).thenReturn("2");
        completeMachineDataLogService.setOrderId(new CompleteMachineDataLogEntityDTO(), 2);
        PowerMockito.when(wsmAssembleLinesService.getCustomerPONumber(any())).thenReturn("");
        completeMachineDataLogService.setOrderId(new CompleteMachineDataLogEntityDTO(), 2);
        CompleteMachineDataLogEntityDTO dto = new CompleteMachineDataLogEntityDTO();
        Assert.assertNotNull(dto);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void setAcceptanceTime() throws Exception {
        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setBarcodeType(Constant.SEQUENCE_NAME);
        cpmConfigItemAssembleDTO.setItemCode("itemNo");
        cpmConfigItemAssembleDTO.setItemBarcode("itemBarcode");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(materialConfigBindService.getTheCompletionTimeOfInboundAccounting(any(), any())).thenReturn(configDetailDTOList);
        completeMachineDataLogService.setAcceptanceTime(new CpmContractEntitiesDTO(), new CompleteMachineDataLogEntityDTO());
        Assert.assertNotNull(cpmConfigItemAssembleDTO);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void batchInsert() throws Exception {
        List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList = new ArrayList<>();
        CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO = new CompleteMachineDataLogEntityDTO();
        completeMachineDataLogEntityDTO.setOrderTime(new Date());
        completeMachineDataLogEntityDTO.setCustomerItemsDTO(new CustomerItemsDTO());
        List<CompleteMachineDataLogLineEntityDTO> components = new ArrayList<>();
        components.add(new CompleteMachineDataLogLineEntityDTO());
        completeMachineDataLogEntityDTO.setComponents(components);
        completeMachineDataLogEntityDTOList.add(completeMachineDataLogEntityDTO);
        completeMachineDataLogService.batchInsert(completeMachineDataLogEntityDTOList, "22");
        completeMachineDataLogService.batchInsert(completeMachineDataLogEntityDTOList, "");
        Assert.assertNotNull(completeMachineDataLogEntityDTOList);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void setAssemblyPackagingTime() throws Exception {
        List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList = new ArrayList<>();
        CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO = new CompleteMachineDataLogEntityDTO();
        completeMachineDataLogEntityDTOList.add(completeMachineDataLogEntityDTO);
        List<TaskHistorySubStateDTO> taskHistorySubStateDTOList = new ArrayList<>();
        TaskHistorySubStateDTO taskHistorySubStateDTO = new TaskHistorySubStateDTO();
        taskHistorySubStateDTO.setSubStatus(Constant.ASSEMBLING);
        TaskHistorySubStateDTO taskHistorySubStateDTO2 = new TaskHistorySubStateDTO();
        taskHistorySubStateDTO2.setSubStatus(Constant.PACKING);
        taskHistorySubStateDTOList.add(taskHistorySubStateDTO);
        taskHistorySubStateDTOList.add(taskHistorySubStateDTO2);
        completeMachineDataLogService.setAssemblyPackagingTime(completeMachineDataLogEntityDTO, completeMachineDataLogEntityDTOList, taskHistorySubStateDTOList);
        Assert.assertNotNull(completeMachineDataLogEntityDTOList);
        Assert.assertNotNull(taskHistorySubStateDTOList);
        Assert.assertNotNull(completeMachineDataLogEntityDTO);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void generateCustomerBasicInformationDTOList() throws Exception {
        List<QueryCustomerBasicInformationDTO> queryCustomerBasicInformationDTOList = new ArrayList<>();
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesEntityDTO.setItemBarcode("itemBarcode");
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);

        Map<String, BarcodeExpandDTO> barcodeExpandDTOMap = new HashMap<>();
        barcodeExpandDTOMap.put("itemBarcode", new BarcodeExpandDTO());
        completeMachineDataLogService.generateCustomerBasicInformationDTOList(cpmContractEntitiesDTO, wsmAssembleLinesList, queryCustomerBasicInformationDTOList);
        Assert.assertNotNull(queryCustomerBasicInformationDTOList);
        Assert.assertNotNull(wsmAssembleLinesList);
        Assert.assertNotNull(barcodeExpandDTOMap);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void uploadCompleteMachineData() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, CenterfactoryRemoteService.class);
        List<SysLookupValues> sysLookupValuesList = new ArrayList();
        UploadCompleteMachineDataDTO uploadCompleteMachineDataDTO = new UploadCompleteMachineDataDTO();

        when(CenterfactoryRemoteService.getLookupValue(any())).thenReturn(sysLookupValuesList);
        when(taskToBeQueriedService.queryTaskContractInformationPage(any())).thenReturn(null);

        List<SysLookupValues> lookupValuesList = new ArrayList<>();
        SysLookupValues dtSys = new SysLookupValues();
        dtSys.setLookupMeaning("789");
        lookupValuesList.add(dtSys);
        when(wsmAssembleLinesService.getSysLookupValues(any())).thenReturn(null);
        try {
            completeMachineDataLogService.uploadCompleteMachineData(uploadCompleteMachineDataDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        when(wsmAssembleLinesService.getSysLookupValues(any())).thenReturn(lookupValuesList);
        completeMachineDataLogService.uploadCompleteMachineData(uploadCompleteMachineDataDTO);
        Assert.assertNotNull(uploadCompleteMachineDataDTO);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void uploadCompleteMachineData2() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        List<CpmContractEntitiesDTO> cpmContractEntitiesDTOList = new ArrayList();
        CpmContractEntitiesDTO cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        cpmContractEntitiesDTOList.add(cpmContractEntitiesDTO);
        UploadCompleteMachineDataDTO uploadCompleteMachineDataDTO = new UploadCompleteMachineDataDTO();
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);

        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setBarcodeType(Constant.SEQUENCE_NAME);
        cpmConfigItemAssembleDTO.setItemCode("itemNo");
        cpmConfigItemAssembleDTO.setItemBarcode("itemBarcode");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(any())).thenReturn(configDetailDTOList);

        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);

        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTOList.add(customerItemsDTO);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        List<String> modelNumberList = new ArrayList<>();
        modelNumberList.add("22");
        uploadCompleteMachineDataDTO.setModelNumberList(modelNumberList);

        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        ZmsCbomInfoDTO dto = new ZmsCbomInfoDTO();
        dto.setEntityName("22");
        configDescList.add(dto);
        cpmContractEntitiesDTOList = new ArrayList<>();
        cpmContractEntitiesDTO = new CpmContractEntitiesDTO();
        cpmContractEntitiesDTO.setEntityName("22");
        cpmContractEntitiesDTOList.add(cpmContractEntitiesDTO);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(configDescList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);

        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("itemNo");
        customerItemsDTO2.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO2);

        CustomerItemsDTO customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("itemNo2");
        customerItemsDTO3.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO3);

        CustomerItemsDTO customerItemsDTO44 = new CustomerItemsDTO();
        customerItemsDTO44.setZteCode("itemNo2");
        customerItemsDTO44.setProjectType("3");
        customerItemsDTO44.setCustomerMaterialType("22");
        customerItemsDTOList.add(customerItemsDTO44);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);
        customerItemsDTOList.clear();
        customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("itemNo2");
        customerItemsDTO3.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO3);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);

        customerItemsDTOList.clear();
        customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("itemNo");
        customerItemsDTO3.setProjectType("3");
        customerItemsDTO3.setCustomerMaterialType("2");
        customerItemsDTOList.add(customerItemsDTO3);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);

        customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("itemNo");
        customerItemsDTO3.setProjectType("5");
        customerItemsDTOList.add(customerItemsDTO3);

        CustomerItemsDTO customerItemsDTO4 = new CustomerItemsDTO();
        customerItemsDTO4.setZteCode("itemNo");
        customerItemsDTO4.setProjectType("5");
        customerItemsDTOList.add(customerItemsDTO4);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);


        customerItemsDTOList.clear();
        customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("itemNo");
        customerItemsDTO3.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO3);
        customerItemsDTO4 = new CustomerItemsDTO();
        customerItemsDTO4.setZteCode("itemNo");
        customerItemsDTO4.setProjectType("5");
        customerItemsDTOList.add(customerItemsDTO4);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);

        customerItemsDTOList.clear();
        customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("itemNo");
        customerItemsDTO3.setProjectType("3");
        customerItemsDTOList.add(customerItemsDTO3);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        when(wsmAssembleLinesService.getAssemblyMaterials(any())).thenReturn(wsmAssembleLinesList);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(null);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);
        when(centerfactoryRemoteService.getCustomerItemsInfo(any(), any())).thenReturn(customerItemsDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);
        List<CompleteMachineDataLogEntityDTO> completeMachineDataLogEntityDTOList = new ArrayList<>();
        CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO = new CompleteMachineDataLogEntityDTO();
        completeMachineDataLogEntityDTOList.add(completeMachineDataLogEntityDTO);
        when(entryBoxAppliedOthersService.getTaskContractInformation(any())).thenReturn(completeMachineDataLogEntityDTOList);
        completeMachineDataLogService.uploadCompleteMachineData(new StringBuilder(), cpmContractEntitiesDTOList, uploadCompleteMachineDataDTO);
        Assert.assertNotNull(uploadCompleteMachineDataDTO);
    }

    @Test
    @PrepareForTest({HttpClientUtil.class, CenterfactoryRemoteService.class})
    public void getCompleteMachineDataLogEntityDTO() throws Exception {
        CompleteMachineDataLogEntityDTO completeMachineDataLogEntityDTO = new CompleteMachineDataLogEntityDTO();
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTOList.add(customerItemsDTO);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        List<WsmAssembleLinesEntityDTO> wsmAssembleLinesList = new ArrayList<>();
        WsmAssembleLinesEntityDTO wsmAssembleLinesEntityDTO = new WsmAssembleLinesEntityDTO();
        wsmAssembleLinesList.add(wsmAssembleLinesEntityDTO);
        List<CustomerItemsDTO> serviceSnCustomerItemsDTOList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("itemNo");
        serviceSnCustomerItemsDTOList.add(customerItemsDTO1);
        completeMachineDataLogService.getCompleteMachineDataLogEntityDTO(completeMachineDataLogEntityDTO, customerItemsDTOList, cpmConfigItemAssembleDTO, wsmAssembleLinesList
                , new ArrayList<>());
        completeMachineDataLogEntityDTO.setMainBoardSn("22");

        completeMachineDataLogService.getCompleteMachineDataLogEntityDTO(completeMachineDataLogEntityDTO, customerItemsDTOList, cpmConfigItemAssembleDTO, wsmAssembleLinesList
                , serviceSnCustomerItemsDTOList);

        completeMachineDataLogEntityDTO.setMainBoardSn("");
        customerItemsDTO.setProjectType("5");
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("itemNo2");
        customerItemsDTO2.setProjectType("5");
        customerItemsDTOList.add(customerItemsDTO2);
        completeMachineDataLogService.getCompleteMachineDataLogEntityDTO(completeMachineDataLogEntityDTO, customerItemsDTOList, cpmConfigItemAssembleDTO, wsmAssembleLinesList
                , serviceSnCustomerItemsDTOList);

        customerItemsDTOList.clear();
        customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("itemNo2");
        customerItemsDTO2.setProjectType("5");
        customerItemsDTOList.add(customerItemsDTO2);
        completeMachineDataLogService.getCompleteMachineDataLogEntityDTO(completeMachineDataLogEntityDTO, customerItemsDTOList, cpmConfigItemAssembleDTO, wsmAssembleLinesList
                , serviceSnCustomerItemsDTOList);

        wsmAssembleLinesEntityDTO.setItemCode("itemNo2");

        Assert.assertNotNull(completeMachineDataLogService.getCompleteMachineDataLogEntityDTO(completeMachineDataLogEntityDTO, customerItemsDTOList,
                cpmConfigItemAssembleDTO, wsmAssembleLinesList
                , serviceSnCustomerItemsDTOList));

    }

    /*Started by AICoder, pid:g68fd6fc8dp1d051413d0a7c418b0a0bddc1f108*/
    @Test
    public void selectBarAccSignForSchTaskTest() {
        BarcodeNetSignDTO barcodeNetSignDTO = new BarcodeNetSignDTO();
        barcodeNetSignDTO.setRow(500);
        List<BarcodeNetSignDTO> expectedResult = new ArrayList<>();
        // Add some data to the expected result
        expectedResult.add(barcodeNetSignDTO);
        when(zmsCompleteMachineHeadRepository.selectBarAccSignForSchTask(any(BarcodeNetSignDTO.class))).thenReturn(expectedResult);
        List<BarcodeNetSignDTO> result = completeMachineDataLogService.selectBarAccSignForSchTask(barcodeNetSignDTO);
        assertEquals(expectedResult, result);

        List<BarcodeNetSignDTO> result1 = completeMachineDataLogService.selectBarAccSignForSchTask(null);
        Assert.assertTrue(result1.size() == 0);
        barcodeNetSignDTO.setRow(null);
        List<BarcodeNetSignDTO> result2 = completeMachineDataLogService.selectBarAccSignForSchTask(barcodeNetSignDTO);
        Assert.assertTrue(result2.size() == 0);
        barcodeNetSignDTO.setRow(1200);
        List<BarcodeNetSignDTO> result3 = completeMachineDataLogService.selectBarAccSignForSchTask(barcodeNetSignDTO);
        Assert.assertTrue(result3.size() == 0);
    }
    /*Ended by AICoder, pid:g68fd6fc8dp1d051413d0a7c418b0a0bddc1f108*/

    /* Started by AICoder, pid:15e85e7110c74455b3c8107092ff23b9 */
    @Test
    public void getItemCodeSNMap() {
        List<ZmsStationSSPNICListDTO> result = completeMachineDataLogService.getItemCodeSNMap(null);
        Assert.assertEquals(result.size(), 0);

        ZmsStationSSPDTO zmsStationSSPDTO = new ZmsStationSSPDTO();
        result = completeMachineDataLogService.getItemCodeSNMap(zmsStationSSPDTO);
        Assert.assertEquals(result.size(), 0);

        List<ZmsStationSSPNICListDTO> nicList = new ArrayList<>();
        List<ZmsStationSSPMemoryListDTO> memoryList = new ArrayList<>();
        List<ZmsStationSSPDiskListDTO> diskList = new ArrayList<>();
        List<ZmsStationSSPPSUListDTO> psuList = new ArrayList<>();
        List<ZmsStationSSPCpuListDTO> cpuList = new ArrayList<>();
        List<ZmsStationSSPGPUListDTO> gpuList = new ArrayList<>();
        List<ZmsStationSSPRaidListDTO> raidList = new ArrayList<>();

        ZmsStationSSPMainboardDTO mainboardDTO = new ZmsStationSSPMainboardDTO();
        zmsStationSSPDTO.setMainboard(mainboardDTO);
        zmsStationSSPDTO.setNicList(nicList);
        zmsStationSSPDTO.setCpuList(cpuList);
        zmsStationSSPDTO.setGpuList(gpuList);
        zmsStationSSPDTO.setDiskList(diskList);
        zmsStationSSPDTO.setMemoryList(memoryList);
        zmsStationSSPDTO.setPsuList(psuList);
        zmsStationSSPDTO.setRaidList(raidList);
        result = completeMachineDataLogService.getItemCodeSNMap(zmsStationSSPDTO);
        Assert.assertEquals(result.size(), 1);

        ZmsStationSSPNICListDTO nicDTO = new ZmsStationSSPNICListDTO();
        nicList.add(nicDTO);
        ZmsStationSSPMemoryListDTO memoryDTO = new ZmsStationSSPMemoryListDTO();
        memoryList.add(memoryDTO);
        ZmsStationSSPDiskListDTO diskDTO = new ZmsStationSSPDiskListDTO();
        diskList.add(diskDTO);
        ZmsStationSSPPSUListDTO psuDTO = new ZmsStationSSPPSUListDTO();
        psuList.add(psuDTO);
        ZmsStationSSPCpuListDTO cpuDTO = new ZmsStationSSPCpuListDTO();
        cpuList.add(cpuDTO);
        ZmsStationSSPGPUListDTO gpuDTO = new ZmsStationSSPGPUListDTO();
        gpuList.add(gpuDTO);
        ZmsStationSSPRaidListDTO raidDTO = new ZmsStationSSPRaidListDTO();
        raidList.add(raidDTO);
        result = completeMachineDataLogService.getItemCodeSNMap(zmsStationSSPDTO);
        Assert.assertEquals(result.size(), 8);

        nicDTO = new ZmsStationSSPNICListDTO();
        nicDTO.setSlot("1111");
        nicDTO.setExtraParams(new ZmsStationSSNICPextraParamsDTO());
        nicList.add(nicDTO);
        memoryDTO = new ZmsStationSSPMemoryListDTO();
        memoryDTO.setSlot("1111");
        memoryList.add(memoryDTO);
        diskDTO = new ZmsStationSSPDiskListDTO();
        diskDTO.setSlot("1111");
        diskList.add(diskDTO);
        psuDTO = new ZmsStationSSPPSUListDTO();
        psuDTO.setSlot("1111");
        psuList.add(psuDTO);
        cpuDTO = new ZmsStationSSPCpuListDTO();
        cpuDTO.setSlot("1111");
        cpuList.add(cpuDTO);
        gpuDTO = new ZmsStationSSPGPUListDTO();
        gpuDTO.setSlot("1111");
        gpuList.add(gpuDTO);
        raidDTO = new ZmsStationSSPRaidListDTO();
        raidDTO.setSlot("1111");
        raidList.add(raidDTO);
        mainboardDTO = new ZmsStationSSPMainboardDTO();
        mainboardDTO.setSlot("1111");
        result = completeMachineDataLogService.getItemCodeSNMap(zmsStationSSPDTO);
        Assert.assertEquals(result.size(), 15);
    }/* Ended by AICoder, pid:15e85e7110c74455b3c8107092ff23b9 */

    @Test
    public void querySspTaskInfoByte() throws Exception {
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(Mockito.any(), Mockito.any())).thenReturn("");
        ZmsStationSSPDTO result = completeMachineDataLogService.querySspTaskInfoByte("");
        Assert.assertEquals(result, null);
    }

    /* Started by AICoder, pid:32ecec28c78b41f18ea41d52ec1f0959 */
    @Test
    public void setCompleteMachineComponentsDTO() {
        List<ZmsStationSSPNICListDTO> itemCodeSNMap = new ArrayList<>();
        completeMachineDataLogService.setCompleteMachineComponentsDTO(null, itemCodeSNMap, "22", null);
        Assert.assertEquals(true, true);

        CompleteMachineDataLogLineEntityDTO dto = new CompleteMachineDataLogLineEntityDTO();
        ZmsStationSSPNICListDTO ggg = new ZmsStationSSPNICListDTO();
        ggg.setItemcode("22");
        itemCodeSNMap.add(ggg);
        ZmsStationSSPDTO hhh = new ZmsStationSSPDTO();
        completeMachineDataLogService.setCompleteMachineComponentsDTO(dto, itemCodeSNMap, "22", hhh);
        Assert.assertEquals(true, true);

        ggg = new ZmsStationSSPNICListDTO();
        ggg.setItemcode("33");
        ggg.setSlot("33435");
        itemCodeSNMap.add(ggg);
        hhh.setMainboard(new ZmsStationSSPMainboardDTO());
        completeMachineDataLogService.setCompleteMachineComponentsDTO(dto, itemCodeSNMap, "33", hhh);
        Assert.assertEquals(true, true);
    }/* Ended by AICoder, pid:32ecec28c78b41f18ea41d52ec1f0959 */

    /* Started by AICoder, pid:f6943c2aeba54d6296efd18413b10a6d */
    @Test
    public void getGrayPlanNo() {
        String result = completeMachineDataLogService.getGrayPlanNo(null);
        Assert.assertEquals(result, "");

        result = completeMachineDataLogService.getGrayPlanNo("3434");
        Assert.assertEquals(result, "");

        result = completeMachineDataLogService.getGrayPlanNo(":3434");
        Assert.assertEquals(result, "");

        result = completeMachineDataLogService.getGrayPlanNo(",:3434");
        Assert.assertEquals(result, "");

        result = completeMachineDataLogService.getGrayPlanNo(":3434,");
        Assert.assertEquals(result, "3434");
    }/* Ended by AICoder, pid:f6943c2aeba54d6296efd18413b10a6d */

}
