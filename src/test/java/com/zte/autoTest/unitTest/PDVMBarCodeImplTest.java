package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.PDVMBarCodeImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BasBarcodeInfoRepository;
import com.zte.domain.model.datawb.PDVMBarCodeLMSRepository;
import com.zte.interfaces.dto.CreatePDVMBarCodeParamterDTO;
import com.zte.interfaces.dto.PDVMInfoManageDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;


@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class PDVMBarCodeImplTest {
    @InjectMocks
    private PDVMBarCodeImpl pdvmBarCodeImpl;

    @Mock
    private PDVMBarCodeLMSRepository pdvmBarCodeLMSRepository;

    @Mock
    private BasBarcodeInfoRepository basBarcodeInfoRepository;

    @Test
    public void getMesLabelInfoQuery() {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            CreatePDVMBarCodeParamterDTO params = new CreatePDVMBarCodeParamterDTO();
            params.setItemCode("");
            params.setEntityName("MWNIP1000-M20200300024");
            params.setQTY((long) 1);
            params.setMfgSiteId("208067586");
            pdvmBarCodeImpl.createPDVMBarCode(params);

            params.setItemCode("180000121774");
            params.setEntityName("");
            params.setQTY((long) 1);
            params.setMfgSiteId("208067586");
            pdvmBarCodeImpl.createPDVMBarCode(params);

            params.setItemCode("180000121774");
            params.setEntityName("MWNIP1000-M20200300024");
            params.setQTY((long) 0);
            params.setMfgSiteId("208067586");
            pdvmBarCodeImpl.createPDVMBarCode(params);

            params.setItemCode("180000121774");
            params.setEntityName("MWNIP1000-M20200300024");
            params.setQTY((long) 1);
            params.setMfgSiteId("");
            pdvmBarCodeImpl.createPDVMBarCode(params);

            params.setItemCode("180000121774");
            params.setEntityName("MWNIP1000-M20200300024---");
            params.setQTY((long) 1);
            params.setMfgSiteId("208067586");
            pdvmBarCodeImpl.createPDVMBarCode(params);

            params.setItemCode("129201001000");//一层物料 非整机
            params.setEntityName("5GRUME-N20171000000");
            params.setQTY((long) 1);
            params.setMfgSiteId("200002012");
            pdvmBarCodeImpl.createPDVMBarCode(params);

            List<PDVMInfoManageDTO> list_PDVMInfoManageDTO = new ArrayList<PDVMInfoManageDTO>();
            PDVMInfoManageDTO DTO = new PDVMInfoManageDTO();
            DTO.setItemId(BigDecimal.valueOf(208067586));
            DTO.setEntityName("MWNIP1000-M20200300024");
            DTO.setLevelNO(4);
            DTO.setIsPDVMFlag(Constant.YES);
            list_PDVMInfoManageDTO.add(DTO);
            String ss = "XX";
            PowerMockito.when(pdvmBarCodeImpl.getSegmentInfoMange(any())).thenReturn(list_PDVMInfoManageDTO);
            PowerMockito.when(pdvmBarCodeImpl.getSpecialName(any())).thenReturn(ss);
            pdvmBarCodeImpl.isBoqBomJobQueryMark(DTO);
            pdvmBarCodeImpl.createPDVMBarCode(params);

            List<PDVMInfoManageDTO> list_PDVMInfoManageDTO1 = new ArrayList<PDVMInfoManageDTO>();
            PDVMInfoManageDTO DTO1 = new PDVMInfoManageDTO();
            DTO1.setItemId(BigDecimal.valueOf(208067511));
            DTO1.setEntityName("MWNIP1000-M20200300024");
            DTO1.setLevelNO(1);
            DTO1.setIsPDVMFlag(Constant.NO);
            list_PDVMInfoManageDTO1.add(DTO1);
            PowerMockito.when(pdvmBarCodeImpl.getSegmentInfoMange(any())).thenReturn(list_PDVMInfoManageDTO1);
            PowerMockito.when(pdvmBarCodeImpl.getSpecialName(any())).thenReturn(ss);
            pdvmBarCodeImpl.createPDVMBarCode(params);

            List<PDVMInfoManageDTO> list_PDVMInfoManageDTO2 = new ArrayList<PDVMInfoManageDTO>();
            PDVMInfoManageDTO DTO2 = new PDVMInfoManageDTO();
            DTO2.setItemId(BigDecimal.valueOf(208067522));
            DTO2.setEntityName("MWNIP1000-M20200300024");
            DTO2.setLevelNO(1);
            DTO2.setIsPDVMFlag(Constant.YES);
            list_PDVMInfoManageDTO2.add(DTO2);
            PowerMockito.when(pdvmBarCodeImpl.getSegmentInfoMange(any())).thenReturn(list_PDVMInfoManageDTO2);
            PowerMockito.when(pdvmBarCodeImpl.getSpecialName(any())).thenReturn(ss);

            list_PDVMInfoManageDTO = new ArrayList<PDVMInfoManageDTO>();
            DTO = new PDVMInfoManageDTO();
            DTO.setItemId(BigDecimal.valueOf(208067533));
            DTO.setEntityName("MWNIP1000-M20200300024");
            DTO.setLevelNO(4);
            DTO.setIsPDVMFlag(Constant.YES);
            list_PDVMInfoManageDTO.add(DTO);
            params.setItemCode("053554300184");//四层物料 标定
            params.setEntityName("5GRUME-N20171000000");
            params.setQTY((long) 1);
            params.setMfgSiteId("200002012");
            pdvmBarCodeImpl.createPDVMBarCode(params);

        } catch (Exception ex) {
            Assert.assertEquals(MessageId.SEARCH_IS_NULL_BY_ENTITYNAME_MFGSITEID_ITEMCODE_LAYER1_LAYER4, ex.getMessage());
        }
    }

}
