package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.CpmBoxupBillItemsDatailServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.CpmBoxupBillItemsDatail;
import com.zte.domain.model.datawb.CpmBoxupBillItemsDatailRepository;
import com.zte.domain.model.datawb.CpmBoxupBillItemsDatailTrans;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.MesSysInfoDTO;
import com.zte.interfaces.stepdt.dto.ApsItemOrgCivDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class})
/**
 * <AUTHOR>
 * @Date 2020/12/22 23
 * @description:
 * descritoin that  testing mes code
 */
public class CpmBoxupBillItemsDatailServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CpmBoxupBillItemsDatailServiceImpl cpmBoxupBillItemsDatailServiceImpl;
    @Mock
    private CpmBoxupBillItemsDatailRepository cpmBoxupBillItemsDatailRepository;

    @Test
    public void combineBoqEn() {
        CpmBoxupBillItemsDatail cpmBoxupBillItemDetailList = new CpmBoxupBillItemsDatail();
        cpmBoxupBillItemDetailList.setInventoryItemId(new BigDecimal(123));
        cpmBoxupBillItemDetailList.setBillNumber("456");
        cpmBoxupBillItemDetailList.setInventoryItem("789");
        cpmBoxupBillItemDetailList.setEnBoqItemName("1233");
        cpmBoxupBillItemDetailList.setBoqItemCode("1233");
        List<CpmBoxupBillItemsDatail> returnList = new ArrayList<>();
        returnList.add(cpmBoxupBillItemDetailList);
        List<CpmBoxupBillItemsDatail> otherList = new ArrayList<>();
        otherList.add(cpmBoxupBillItemDetailList);
        Map<String, String> map = new HashMap<>();
        map.put("456789", "客户物料代码");
        cpmBoxupBillItemsDatailServiceImpl.combineBoqEn(returnList, otherList, map);
        Assert.assertNotNull(returnList);
        Assert.assertNotNull(map);
    }

    @Test
    public void getList() {
        try {
            List<CpmBoxupBillItemsDatail> returnList = new ArrayList<>();
            MesSysInfoDTO dto = new MesSysInfoDTO();
            CpmBoxupBillItemsDatail cpmBoxupBillItemDetailList = new CpmBoxupBillItemsDatail();
            cpmBoxupBillItemDetailList.setInventoryItemId(new BigDecimal(123));
            cpmBoxupBillItemDetailList.setBillNumber("456");
            cpmBoxupBillItemDetailList.setInventoryItem("789");
            cpmBoxupBillItemDetailList.setEnBoqItemName("1233");
            cpmBoxupBillItemDetailList.setBoqItemCode("1233");
            returnList.add(cpmBoxupBillItemDetailList);
            PowerMockito.when(cpmBoxupBillItemsDatailRepository.getList(Mockito.any())).thenReturn(returnList);
            PowerMockito.when(cpmBoxupBillItemsDatailRepository.getCusItemCodeForBom(Mockito.any())).thenReturn(returnList);
            PowerMockito.when(cpmBoxupBillItemsDatailRepository.getCusItemCodeForBom(Mockito.any())).thenReturn(returnList);
            cpmBoxupBillItemsDatailServiceImpl.getList(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void findGbomInfoByBillIds() {
        try {
            List<CpmBoxupBillItemsDatail> returnList = new ArrayList<>();
            MesSysInfoDTO dto = new MesSysInfoDTO();
            CpmBoxupBillItemsDatail cpmBoxupBillItemDetailList = new CpmBoxupBillItemsDatail();
            cpmBoxupBillItemDetailList.setInventoryItemId(new BigDecimal(123));
            cpmBoxupBillItemDetailList.setBillNumber("456");
            cpmBoxupBillItemDetailList.setInventoryItem("789");
            cpmBoxupBillItemDetailList.setEnBoqItemName("1233");
            cpmBoxupBillItemDetailList.setBoqItemCode("1233");
            returnList.add(cpmBoxupBillItemDetailList);
            PowerMockito.when(cpmBoxupBillItemsDatailRepository.findGbomInfoByBillIds(Mockito.any())).thenReturn(returnList);
            PowerMockito.when(cpmBoxupBillItemsDatailRepository.getOtherInfoForBom(Mockito.any())).thenReturn(returnList);
            PowerMockito.when(cpmBoxupBillItemsDatailRepository.getCusItemCodeForBom(Mockito.any())).thenReturn(returnList);
            cpmBoxupBillItemsDatailServiceImpl.findGbomInfoByBillIds(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void findBolean() {
        try {
            List<String> lengthDt = new ArrayList<>();
            lengthDt.add("123");
            lengthDt.add("456");
            List<SysLookupValues> lookupValuesList = new ArrayList<>();
            SysLookupValues dtx = new SysLookupValues();
            dtx.setLookupMeaning("50");
            dtx.setLookupCode(new BigDecimal(8882001));
            lookupValuesList.add(dtx);
            PowerMockito.mockStatic(CenterfactoryRemoteService.class);
            PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(lookupValuesList);
            dtx.setLookupMeaning("1");
            PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(lookupValuesList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getDagerousList() {
        CpmBoxupBillItemsDatail cpmBoxupBillItemDetailList = new CpmBoxupBillItemsDatail();
        cpmBoxupBillItemDetailList.setInventoryItemId(new BigDecimal(123));
        cpmBoxupBillItemDetailList.setBillNumber("456");
        cpmBoxupBillItemDetailList.setInventoryItem("789");
        cpmBoxupBillItemDetailList.setEnBoqItemName("1233");
        cpmBoxupBillItemDetailList.setBoqItemCode("1233");
        List<CpmBoxupBillItemsDatail> returnList = new ArrayList<>();
        returnList.add(cpmBoxupBillItemDetailList);
        MesSysInfoDTO dto = new MesSysInfoDTO();
        List<String> packNoList = new ArrayList<>();
        packNoList.add("cccc");
        dto.setPackNo(packNoList);
        List<String> packNoListDt = new ArrayList<>();
        MesSysInfoDTO dtoDt = new MesSysInfoDTO();
        dtoDt.setPackNo(packNoListDt);
        Assert.assertNotNull(cpmBoxupBillItemDetailList);
    }

    @Test
    public void findBoleanDt() {
        List<String> lengthDt = new ArrayList<>();
        lengthDt.add("C1230899");
        lengthDt.add("C123333");
        List<SysLookupValues> lookupValuesList = new ArrayList<>();
        SysLookupValues dtx = new SysLookupValues();
        dtx.setLookupMeaning("50");
        dtx.setLookupCode(new BigDecimal(8882001));
        SysLookupValues dtxt = new SysLookupValues();
        dtxt.setLookupMeaning("200");
        dtxt.setLookupCode(new BigDecimal(8882002));
        lookupValuesList.add(dtx);
        lookupValuesList.add(dtxt);
        MesSysInfoDTO dto = new MesSysInfoDTO();
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(lookupValuesList);
        Assert.assertNotNull(cpmBoxupBillItemsDatailServiceImpl.findBoleanDt(lengthDt, dto));
    }

    @Test
    public void getGbomTrans() {
        MesSysInfoDTO dto = new MesSysInfoDTO();
        dto.setPage((long) 100);
        dto.setRows((long) 100);
        List<CpmBoxupBillItemsDatailTrans> items = new ArrayList<>();
        CpmBoxupBillItemsDatailTrans dtItems = new CpmBoxupBillItemsDatailTrans();
        dtItems.setBoxupQty("1");
        dtItems.setBatteryLabeltype("1");
        dtItems.setItemNo("1233");
        dtItems.setDangergoodstype("12333");
        List<CpmBoxupBillItemsDatailTrans> itemsList = new ArrayList<>();
        itemsList.add(dtItems);
        PowerMockito.when(cpmBoxupBillItemsDatailRepository.getTransCount(Mockito.any())).thenReturn(1);
        PowerMockito.when(cpmBoxupBillItemsDatailRepository.getTransList(Mockito.any())).thenReturn(itemsList);
        Assert.assertNotNull(cpmBoxupBillItemsDatailServiceImpl.getGbomTrans(dto));
    }
}
