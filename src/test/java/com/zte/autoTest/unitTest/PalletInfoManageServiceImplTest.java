package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.PalletInfoManageServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.CommonUtils;
import com.zte.interfaces.dto.ProductDescDTO;
import org.junit.Assert;
import org.junit.runner.RunWith;
import static org.powermock.api.mockito.PowerMockito.when;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.zte.domain.model.datawb.PalletInfoManageRepository;
import com.zte.interfaces.dto.PalletInfoManageInDTO;
import com.zte.interfaces.dto.PalletInfoManageOutDTO;
import com.zte.util.BaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class PalletInfoManageServiceImplTest extends BaseTestCase {

    @InjectMocks
    PalletInfoManageServiceImpl palletInfoManageServiceImpl;

    @Mock
    private PalletInfoManageRepository palletInfoManageRepository;

    @Test
    public void palletInfoManage() throws Exception {
        try {
        /*
        *{
  "boxType": "W0006",
  "createBy": "10289101",
  "entityName": "SMA2002050056-000",
  "organizationId": "635",
  "palletDesc": "托盘信息维护1",
  "productDesc": "托盘信息维护2",
  "stackNum": "6"
}
        * */
            /**
             * <AUTHOR>
             * 添加单元测试覆盖率
             **/
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            PalletInfoManageOutDTO res = new PalletInfoManageOutDTO();
            PalletInfoManageInDTO params = new PalletInfoManageInDTO();

            //params.setBoxType("");
            params.setCreateBy("10289101");
            params.setEntityName("SMA2002050056-000");
            params.setOrganizationId("635");
            params.setPalletDesc("托盘信息维护1");
            params.setProductDesc("托盘信息维护2");
            params.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params);
            }

            PalletInfoManageInDTO params2 = new PalletInfoManageInDTO();
            params2.setBoxType("W0006");
            params2.setCreateBy("");
            params2.setEntityName("SMA2002050056-000");
            params2.setOrganizationId("635");
            params2.setPalletDesc("托盘信息维护1");
            params2.setProductDesc("托盘信息维护2");
            params2.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params2);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params2);
            }

            PalletInfoManageInDTO params3 = new PalletInfoManageInDTO();
            params3.setBoxType("W0006");
            params3.setCreateBy("10289101");
            params3.setEntityName("");
            params3.setOrganizationId("635");
            params3.setPalletDesc("托盘信息维护1");
            params3.setProductDesc("托盘信息维护2");
            params3.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params3);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params3);
            }

            PalletInfoManageInDTO params4 = new PalletInfoManageInDTO();
            params4.setBoxType("W0006");
            params4.setCreateBy("10289101");
            params4.setEntityName("SMA2002050056-000");
            params4.setOrganizationId("");
            params4.setPalletDesc("托盘信息维护1");
            params4.setProductDesc("托盘信息维护2");
            params4.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params4);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params4);
            }

            PalletInfoManageInDTO params5 = new PalletInfoManageInDTO();
            params5.setBoxType("W0006");
            params5.setCreateBy("10289101");
            params5.setEntityName("SMA2002050056-000");
            params5.setOrganizationId("635");
            params5.setPalletDesc("");
            params5.setProductDesc("托盘信息维护2");
            params5.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params5);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params5);
            }

            PalletInfoManageInDTO params6 = new PalletInfoManageInDTO();
            params6.setBoxType("W0006");
            params6.setCreateBy("10289101");
            params6.setEntityName("SMA2002050056-000");
            params6.setOrganizationId("635");
            params6.setPalletDesc("托盘信息维护1");
            params6.setProductDesc("");
            params6.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params6);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params6);
            }

            PalletInfoManageInDTO params7 = new PalletInfoManageInDTO();
            params7.setBoxType("W0006");
            params7.setCreateBy("10289101");
            params7.setEntityName("SMA2002050056-000");
            params7.setOrganizationId("635");
            params7.setPalletDesc("托盘信息维护1");
            params7.setProductDesc("托盘信息维护2");
            params7.setStackNum("");
            res=palletInfoManageServiceImpl.palletInCheck(params7);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params7);
            }

            PalletInfoManageInDTO params8 = new PalletInfoManageInDTO();
            params8.setBoxType("W0006");
            params8.setCreateBy("102891");//less
            params8.setEntityName("SMA2002050056-000");
            params8.setOrganizationId("635");
            params8.setPalletDesc("托盘信息维护1");
            params8.setProductDesc("托盘信息维护2");
            params8.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params8);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params8);
            }



            PalletInfoManageInDTO params10 = new PalletInfoManageInDTO();
            params10.setBoxType("W0006");
            params10.setCreateBy("10289101");
            params10.setEntityName("SMA2002050056-000");
            params10.setOrganizationId("635");
            params10.setPalletDesc("托盘信息维护1,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘信息维护1,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘信息维护1,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘信息维护1,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘信息维护1,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查,托盘描述不能超过200字，请检查");
            params10.setProductDesc("托盘信息维护2");
            params10.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params10);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params10);
            }

            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("S");
            PalletInfoManageInDTO params12 = new PalletInfoManageInDTO();
            params12.setBoxType("W0006");
            params12.setCreateBy("10289101");
            params12.setEntityName("SMA2002050056-000");
            params12.setOrganizationId("635");
            params12.setPalletDesc("test托盘信息维护1");
            params12.setProductDesc("test托盘信息维护2");
            params12.setStackNum("6");
            res=palletInfoManageServiceImpl.palletInCheck(params12);
            if(res.getProcessStatus()== Constant.KEY_S) {
                palletInfoManageServiceImpl.palletInfoManage(params12);
            }

            try {
                /**
                 * <AUTHOR>
                 */
                PowerMockito.mockStatic(CommonUtils.class);
                when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

                PalletInfoManageInDTO params9 = new PalletInfoManageInDTO();
                params9.setBoxType("P0016");
                params9.setCreateBy("10289101");
                params9.setEntityName("SMA2002050056-000");//no exit
                params9.setOrganizationId("635");
                params9.setPalletDesc("托盘信息维护1");
                params9.setProductDesc("123");
                params9.setStackNum("6");
                List<ProductDescDTO> list=new ArrayList<ProductDescDTO>();
                ProductDescDTO pro=new ProductDescDTO();
                pro.setEntityId(123);
                pro.setMfgSiteName("123");
                list.add(pro);
                PowerMockito.when(palletInfoManageRepository.entityNameCount(anyString())).thenReturn(list);
                palletInfoManageServiceImpl.palletInfoManage(params9);

            }
            catch (Exception e)
            {
                Assert.assertEquals(MessageId.BOXTYPE_NOT_FIND_WIDTH_AND_LENGTH_PLEASE_CHECK, e.getMessage());
            }

            try {

                PowerMockito.mockStatic(CommonUtils.class);
                when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

                PalletInfoManageInDTO params11 = new PalletInfoManageInDTO();
                params11.setBoxType("11111");//err
                params11.setCreateBy("10289101");
                params11.setEntityName("SMA2002050056-000");
                params11.setOrganizationId("635");
                params11.setPalletDesc("托盘信息维护1");
                params11.setProductDesc("托盘信息维护2");
                params11.setStackNum("6");

                PowerMockito.when(palletInfoManageRepository.queryBoxInfo(anyString())).thenReturn(null);
                palletInfoManageServiceImpl.palletInfoManage(params11);


            }
            catch (Exception e)
            {
                Assert.assertEquals(MessageId.PALLETID_NOT_FIND_PLEASE_CHECK, e.getMessage());
            }

        } catch (Exception e) {
            Assert.assertEquals(MessageId.SAVE_PALLET_SUCCESS, e.getMessage());
        }
    }

    @Test
    public void findboxinfo() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);

    }

    @Test
    public void findentityname() {
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);

        /**
         * <AUTHOR>
         */
    }


}
