package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.MdsAccessTokenDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationFileDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationTestingInfoDTO;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2022-12-07 17:00
 */
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, MESHttpHelper.class, CenterfactoryRemoteService.class})
public class MdsRemoteServiceTest extends PowerBaseTestCase {
    @InjectMocks
    private MdsRemoteService mdsRemoteService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;

    @Test
    public void getAccessToken() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class, CenterfactoryRemoteService.class);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        when(redisTemplate.opsForValue()).thenReturn(valueOps);

        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(null);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues dto1 = new SysLookupValues();
        dto1.setLookupMeaning("1");
        dto1.setLookupCode(new BigDecimal("6732001"));
        sysLookupValuesList.add(dto1);

        when(valueOps.get(Mockito.anyString())).thenReturn("");
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupValues dto2 = new SysLookupValues();
        dto2.setLookupMeaning("1");
        dto2.setLookupCode(new BigDecimal("6732003"));
        sysLookupValuesList.add(dto2);
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupValues dto3 = new SysLookupValues();
        dto3.setLookupMeaning("1");
        dto3.setLookupCode(new BigDecimal("6732004"));
        sysLookupValuesList.add(dto3);
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues dto4 = new SysLookupValues();
        dto4.setLookupMeaning("1");
        dto4.setLookupCode(new BigDecimal("6732002"));
        sysLookupValuesList.add(dto4);
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }

    }
    @Test
    public void queryVersion() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class, CenterfactoryRemoteService.class);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        mdsRemoteService.queryVersion("");

        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();

        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(null);
        try {
            mdsRemoteService.queryVersion("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValues.setLookupCode(new BigDecimal(6732007));
        sysLookupValues.setLookupMeaning("");
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.queryVersion("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValuesList.add(sysLookupValues);
        sysLookupValues.setLookupMeaning("http://10.5.208.215/zte-scm-wms-sstock/stockLock/queryStockOccupyInfo");
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);

        when(redisTemplate.opsForValue()).thenReturn(valueOps);

        when(valueOps.get(Mockito.anyString())).thenReturn("token");

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(null));
        try {
            mdsRemoteService.queryVersion("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("[{\"partCode\":\"partCode\"}]");
        mdsRemoteService.queryVersion("2");
        when(valueOps.get(Mockito.anyString())).thenReturn("");


        SysLookupValues dto1 = new SysLookupValues();
        dto1.setLookupMeaning("1");
        dto1.setLookupCode(new BigDecimal("6732001"));
        sysLookupValuesList.add(dto1);

        SysLookupValues dto2 = new SysLookupValues();
        dto2.setLookupMeaning("1");
        dto2.setLookupCode(new BigDecimal("6732002"));
        sysLookupValuesList.add(dto2);

        SysLookupValues dto3 = new SysLookupValues();
        dto3.setLookupMeaning("1");
        dto3.setLookupCode(new BigDecimal("6732003"));
        sysLookupValuesList.add(dto3);

        SysLookupValues dto4 = new SysLookupValues();
        dto4.setLookupMeaning("1");
        dto4.setLookupCode(new BigDecimal("6732004"));
        sysLookupValuesList.add(dto4);

        SysLookupValues dto5 = new SysLookupValues();
        dto5.setLookupMeaning("1");
        dto5.setLookupCode(new BigDecimal("6732005"));
        sysLookupValuesList.add(dto5);
        SysLookupValues dto7 = new SysLookupValues();
        dto7.setLookupCode(new BigDecimal("6732007"));
        sysLookupValuesList.add(dto7);
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6732)).thenReturn(sysLookupValuesList);

        try {
            mdsRemoteService.queryVersion("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }
        dto7.setLookupMeaning("1");
        try {
            mdsRemoteService.queryVersion("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }
        MdsAccessTokenDTO mdsAccessTokenDTO = new MdsAccessTokenDTO();
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(mdsAccessTokenDTO));
        try {
            mdsRemoteService.queryVersion("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn("{\"access_token\":\"1\"}");
        mdsRemoteService.queryVersion("batchNoList");

    }

    @Test
    public void feedbackOfProductionStationTestingInformation() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class, CenterfactoryRemoteService.class);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        List<MdsFeedbackProductionStationTestingInfoDTO> resultList = mdsRemoteService.feedbackOfProductionStationTestingInformation(null);
        Assert.assertEquals(resultList.size(),0);

        when(redisTemplate.opsForValue()).thenReturn(valueOps);

        when(valueOps.get(Mockito.anyString())).thenReturn("token");

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(new ArrayList<MdsFeedbackProductionStationTestingInfoDTO>(){{add(new MdsFeedbackProductionStationTestingInfoDTO());}}));
        try {
            resultList = mdsRemoteService.feedbackOfProductionStationTestingInformation(Arrays.asList("2"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }

        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("[{\"partCode\":\"partCode\"}]");
        when(valueOps.get(Mockito.anyString())).thenReturn("2");
        resultList = mdsRemoteService.feedbackOfProductionStationTestingInformation(Arrays.asList("2"));
        Assert.assertEquals(resultList.size(),1);

    }

    @Test
    public void feedbackCompleteMachineTestingGenerateFile() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class, CenterfactoryRemoteService.class);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        List<MdsFeedbackProductionStationFileDTO> resultList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("",null,true);
        Assert.assertEquals(resultList.size(),0);

        when(redisTemplate.opsForValue()).thenReturn(valueOps);

        when(valueOps.get(Mockito.anyString())).thenReturn("token");

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(JSON.toJSONString(new ArrayList<MdsFeedbackProductionStationTestingInfoDTO>(){{add(new MdsFeedbackProductionStationTestingInfoDTO());}}));
        try {
            resultList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("",Arrays.asList("2"),true);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_MDS_INTERFACE, e.getMessage());
        }

        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("[{\"partCode\":\"partCode\"}]");
        when(valueOps.get(Mockito.anyString())).thenReturn("2");
        resultList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("2",Arrays.asList("2"),true);
        Assert.assertEquals(resultList.size(),1);

    }

}
