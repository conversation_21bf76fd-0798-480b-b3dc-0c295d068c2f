package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.datawb.impl.PkgBatchScanServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.SpringUtil;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.PkgBatchScanDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/5/16 9:41
 */
@PrepareForTest({CommonUtils.class, RetCode.class, ServiceData.class, SpringContextUtil.class, HttpClientUtil.class, JacksonJsonConverUtil.class,
        HttpRemoteUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ProductionDeliveryRemoteServiceTest extends PowerBaseTestCase {

    @InjectMocks
    private ProductionDeliveryRemoteService productionDeliveryRemoteService;

    @Mock
    ConstantInterface constantInterface;

    @Mock
    ObjectMapper objectMapper;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
    }

    @Test
    public void updateWipInfoStation() throws Exception {
        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        try {
            productionDeliveryRemoteService.updateWipInfoStation(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SELECT_ERROR,e.getMessage());
        }
        pkgBatchScanDTO.setObjectList(new ArrayList() {{
            add("1");
            add("2");
        }});
        try {
            productionDeliveryRemoteService.updateWipInfoStation(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SELECT_ERROR,e.getMessage());
        }
    }

    @Test
    public void getOriginalLpnInfoBatch() throws Exception {
        productionDeliveryRemoteService.getOriginalLpnInfoBatch(new ArrayList<>());
        List<String> originalLpnList = new ArrayList<>();
        originalLpnList.add("1");
        PowerMockito.when(constantInterface.getUrl(Mockito.any()))
                .thenReturn("22");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(result);
        productionDeliveryRemoteService.getOriginalLpnInfoBatch(originalLpnList);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(result);
        productionDeliveryRemoteService.getOriginalLpnInfoBatch(originalLpnList);
        result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[{\"lpn\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"}],\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(result);
        productionDeliveryRemoteService.getOriginalLpnInfoBatch(originalLpnList);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(objectMapper);
        PowerMockito.when(objectMapper.readTree(Mockito.anyString()))
                .thenReturn(null);
        Assert.assertNotNull(productionDeliveryRemoteService.getOriginalLpnInfoBatch(originalLpnList));
        String result1 = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.anyMap()))
                .thenReturn(result1);
        Assert.assertNotNull(productionDeliveryRemoteService.getOriginalLpnInfoBatch(originalLpnList));
    }
}
