package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.MesGetDictInforService;
import com.zte.application.datawb.ZmsAlibabaService;
import com.zte.application.datawb.ZmsCommonService;
import com.zte.application.datawb.impl.ZmsAlibabaUploadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.SpringUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsAlibabaUploadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2025年5月8日10:36:20
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class, SpringContextUtil.class,
        RetCode.class, SpringUtil.class,
        JacksonJsonConverUtil.class, JSON.class, CommonUtils.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
@ExtendWith(MockitoExtension.class)
public class ZmsAlibabaUploadServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ZmsAlibabaUploadServiceImpl zmsAlibabaUploadService;
    @Mock
    private ZmsAlibabaUploadRepository zmsAlibabaUploadRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private ZmsCommonService zmsCommonService;
    @Mock
    private MesGetDictInforService mesGetDictInforService;
    @Mock
    private ZmsAlibabaService zmsAlibabaService;
    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;

    List<ZmsAlibabaBtbDTO> zmsAlibabaBtbDTOList = new ArrayList<>();
    SysLookupValues sysLookupValues = new SysLookupValues();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        ZmsAlibabaBtbDTO zmsAlibabaBtbDTO1 = new ZmsAlibabaBtbDTO();
        zmsAlibabaBtbDTO1.setUploadStatus("-1");
        zmsAlibabaBtbDTO1.setProjectName("1");
        zmsAlibabaBtbDTO1.setJsonData("data");
        zmsAlibabaBtbDTO1.setMessageType("111");
        zmsAlibabaBtbDTO1.setRecordId("111");
        zmsAlibabaBtbDTO1.setRemark("111");
        zmsAlibabaBtbDTO1.setBillNumber("GL111");
        zmsAlibabaBtbDTO1.setBillType(0);
        zmsAlibabaBtbDTO1.setUploadFailNum(3);
        zmsAlibabaBtbDTO1.setUploadFailReason("11");
        zmsAlibabaBtbDTO1.setEntityName("123");
        zmsAlibabaBtbDTOList.add(zmsAlibabaBtbDTO1);
        ZmsAlibabaBtbDTO zmsAlibabaBtbDTO2 = new ZmsAlibabaBtbDTO();
        zmsAlibabaBtbDTO2.setUploadStatus("-1");
        zmsAlibabaBtbDTO2.setProjectName("1");
        zmsAlibabaBtbDTO2.setJsonData("data");
        zmsAlibabaBtbDTO2.setMessageType("222");
        zmsAlibabaBtbDTO2.setRemark("222");
        zmsAlibabaBtbDTO2.setBillNumber("GL222");
        zmsAlibabaBtbDTO2.setBillType(0);
        zmsAlibabaBtbDTO2.setUploadFailNum(6);
        zmsAlibabaBtbDTO2.setUploadFailReason("11");
        zmsAlibabaBtbDTO2.setEntityName("123");
        zmsAlibabaBtbDTOList.add(zmsAlibabaBtbDTO2);
        ZmsAlibabaBtbDTO zmsAlibabaBtbDTO3 = new ZmsAlibabaBtbDTO();
        zmsAlibabaBtbDTO3.setUploadStatus("0");
        zmsAlibabaBtbDTO3.setProjectName("1");
        zmsAlibabaBtbDTO3.setJsonData("data");
        zmsAlibabaBtbDTO3.setMessageType("333");
        zmsAlibabaBtbDTO3.setRecordId("333");
        zmsAlibabaBtbDTO3.setRemark("333");
        zmsAlibabaBtbDTO3.setBillNumber("GL333");
        zmsAlibabaBtbDTO3.setBillType(1);
        zmsAlibabaBtbDTO3.setUploadFailNum(0);
        zmsAlibabaBtbDTO3.setEntityName("123");
        zmsAlibabaBtbDTOList.add(zmsAlibabaBtbDTO3);
        ZmsAlibabaBtbDTO zmsAlibabaBtbDTO4 = new ZmsAlibabaBtbDTO();
        zmsAlibabaBtbDTO4.setUploadStatus("1");
        zmsAlibabaBtbDTO4.setProjectName("1");
        zmsAlibabaBtbDTO4.setJsonData("data");
        zmsAlibabaBtbDTO4.setMessageType("444");
        zmsAlibabaBtbDTO4.setRecordId("444");
        zmsAlibabaBtbDTO4.setRemark("444");
        zmsAlibabaBtbDTO4.setBillNumber("GL444");
        zmsAlibabaBtbDTO4.setBillType(2);
        zmsAlibabaBtbDTO4.setUploadFailNum(0);
        zmsAlibabaBtbDTO4.setEntityName("123");
        zmsAlibabaBtbDTOList.add(zmsAlibabaBtbDTO4);

        sysLookupValues.setLookupMeaning("111");
        sysLookupValues.setDescription("222");
        sysLookupValues.setLookupCode(new BigDecimal(111));
        sysLookupValues.setLookupType(new BigDecimal(222));
        PowerMockito.when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupValues);
    }

    @Test
    public void testUploadZmsAlibaba() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(zmsAlibabaService.queryAliMatIssueData(any())).thenReturn(null);
        PowerMockito.when(zmsAlibabaUploadRepository.getAlibabaBills(any())).thenReturn(null);
        this.zmsAlibabaUploadService.uploadZmsAlibaba(null, "22");
        PowerMockito.when(zmsAlibabaUploadRepository.getAlibabaBills(any())).thenReturn(zmsAlibabaBtbDTOList);
        this.zmsAlibabaUploadService.uploadZmsAlibaba(null, "22");
        PowerMockito.when(zmsAlibabaService.queryAliMatIssueData(any())).thenThrow(new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_DATA_FOUND));
        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        when(localeMessageSourceBean.getMessage(anyString(), any())).thenReturn("OK");
        Mockito.doThrow(new RuntimeException("An error occurred")).when(zmsCommonService).pushDataToB2B(anyList(), anyBoolean());
        this.zmsAlibabaUploadService.uploadZmsAlibaba(null, "22");
        Assert.assertNotNull(zmsAlibabaBtbDTOList);
    }

    @Test
    public void testUploadZmsAlibabaBack() throws Exception {
        PowerMockito.when(zmsAlibabaUploadRepository.updateBillLog(any())).thenReturn(1);
        PowerMockito.when(zmsAlibabaUploadRepository.selectSendEmailLog()).thenReturn(null);
        PowerMockito.when(zmsAlibabaUploadRepository.selectBillUploadLog(any())).thenReturn(1);
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(null);
        ZmsAlibabaUploadBackDTO dto = new ZmsAlibabaUploadBackDTO();
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        dto.setData("{\"data\":{},\"request_id\":\"eg59buvwmbyn\"}");
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{}\",\"success\":true},\"request_id\":\"eg59buvwmbyn\"}");
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        dto.setData("{\"data\":{\"code\":\"200\",\"result\":\"{\\\"msg\\\":\\\"业务租户ais业务分类PartTypeA,不存在编号为PONO20250403679的生产工单,请厂商自查\\\",\\\"trace_id\\\":\\\"0b7b4c8a17472765037771600d0caf\\\",\\\"total\\\":0,\\\"code\\\":\\\"5001.arowana.mo_not_exist\\\",\\\"data\\\":null,\\\"success\\\":false,\\\"pending\\\":false}\",\"success\":true},\"request_id\":\"eg59buvwmbyn\"}");
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        PowerMockito.when(zmsAlibabaUploadRepository.selectSendEmailLog()).thenReturn(zmsAlibabaBtbDTOList);
        PowerMockito.when(mesGetDictInforService.getDicDescription(anyString())).thenReturn("111.com");
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(false);
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenThrow(new NullPointerException());
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        PowerMockito.when(zmsCommonService.postInOneApi(any(), anyString(), anyString())).thenReturn("11");
        PowerMockito.when(zmsAlibabaUploadRepository.selectBillUploadLog(any())).thenReturn(0);
        this.zmsAlibabaUploadService.uploadZmsAlibabaBack(dto);
        Assert.assertNotNull(zmsAlibabaBtbDTOList);
    }

    @Test
    public void testSelectPicklistMain() throws Exception {
        PowerMockito.when(zmsAlibabaUploadRepository.selectPicklistMain(any())).thenReturn(null);
        ZmsPicklistMainInDTO zmsPicklistMainInDTO = new ZmsPicklistMainInDTO();
        this.zmsAlibabaUploadService.selectPicklistMain(zmsPicklistMainInDTO);
        Assert.assertNotNull(zmsPicklistMainInDTO);
    }

}
