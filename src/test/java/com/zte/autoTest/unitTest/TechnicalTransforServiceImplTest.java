package com.zte.autoTest.unitTest;

import com.zte.application.datawb.DecriptionSelectService;
import com.zte.application.datawb.impl.TechnicalTransforServiceImpl;
import com.zte.domain.model.datawb.TechnicalTransforRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.TechnologyModifyDTO;
import com.zte.interfaces.dto.TechnologyModifyResultDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyObject;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class})
/**
 * <AUTHOR>
 * @Date 2020/12/22 23
 * @description:
 */
public class TechnicalTransforServiceImplTest{
    @InjectMocks
    private TechnicalTransforServiceImpl technicalTransforServiceImpl;
    @Mock
    private TechnicalTransforRepository technicalTransforRepository;
    @Mock
    private DecriptionSelectService decriptionSelectService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Test
    public void arrIsEmpty() {
        String[] arrList = new String[]{"1233"};
        technicalTransforServiceImpl.arrIsEmpty(arrList);
        arrList = null;
        Assert.assertFalse(technicalTransforServiceImpl.arrIsEmpty(arrList));
    }
    @Test
    public void getArrIndex(){
        String dtView ="1233,1233";
        Assert.assertNotNull(technicalTransforServiceImpl.getArrIndex(dtView,0));
    }
    @Test
    public void getCodeList() throws Exception {
        TechnologyModifyDTO dtoParamter=new TechnologyModifyDTO();
        dtoParamter.setOrgid("635");
        dtoParamter.setItemBarcode(null);
        dtoParamter.setFormName("装箱扫描");
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        dtoParamter.setOrgid(null);
        dtoParamter.setItemBarcode("123");
        dtoParamter.setFormName("装箱扫描");
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        dtoParamter.setOrgid("635");
        dtoParamter.setItemBarcode("123");
        dtoParamter.setFormName(null);
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        dtoParamter.setOrgid("635");
        dtoParamter.setItemBarcode("123");
        dtoParamter.setFormName("装箱扫描");
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        //获取数据字典值
        List<TechnologyModifyResultDTO> dtoResult=new ArrayList<>();
        PowerMockito.when(decriptionSelectService.getDictionrayList(anyMap())).thenReturn(dtoResult);
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        TechnologyModifyResultDTO dtxCt=new TechnologyModifyResultDTO();
        dtxCt.setDescriptionCode("Y,Y,1");
        dtoResult.add(dtxCt);
        PowerMockito.when(decriptionSelectService.getDictionrayList(anyMap())).thenReturn(dtoResult);
        Assert.assertNotNull(technicalTransforServiceImpl.getCodeList(dtoParamter));
    }
    @Test
    public void getCodeListMethod() throws Exception {
        TechnologyModifyDTO dtoParamter=new TechnologyModifyDTO();
        dtoParamter.setOrgid("635");
        dtoParamter.setItemBarcode("123");
        dtoParamter.setFormName("装箱扫描");
        List<TechnologyModifyResultDTO> dtoResult=new ArrayList<>();
        TechnologyModifyResultDTO dtxCt=new TechnologyModifyResultDTO();
        dtxCt.setDescriptionCode("Y,Y,1");

        dtoResult.add(dtxCt);
        PowerMockito.when(decriptionSelectService.getDictionrayList(anyMap())).thenReturn(dtoResult);
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        //查询mes装配关系
        dtxCt.setItemBarcode("12333");
        PowerMockito.when(technicalTransforRepository.selectMesCombine(anyObject())).thenReturn(dtoResult);
        PowerMockito.when(technicalTransforRepository.validateLock(anyObject())).thenReturn(dtoResult);
        technicalTransforServiceImpl.getCodeList(dtoParamter);
        //查询kaxrii的转配关系
        PowerMockito.when(technicalTransforRepository.selectMesCombine(anyObject())).thenReturn(null);
        List<String> list = new ArrayList<>();
        list.add("777766600001");
        PowerMockito.when(centerfactoryRemoteService.getSnByParentSn(anyObject())).thenReturn(list);
        Assert.assertNotNull(technicalTransforServiceImpl.getCodeList(dtoParamter));

    }

}
