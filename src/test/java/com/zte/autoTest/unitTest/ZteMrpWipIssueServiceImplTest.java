package com.zte.autoTest.unitTest;


import com.zte.application.erpdt.impl.ZteMrpWipIssueServiceImpl;
import com.zte.domain.model.erpdt.ZteMrpWipIssue;
import com.zte.domain.model.erpdt.ZteMrpWipIssueRepository;
import com.zte.interfaces.assembler.ZteMrpWipIssueAssembler;
import com.zte.interfaces.dto.ZteMrpWipIssueDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.string.StringHelper;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doReturn;

/**
 * erp出库查询单元测试
 * <AUTHOR> 陈昭君
 */
public class ZteMrpWipIssueServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ZteMrpWipIssueServiceImpl zteMrpWipIssueServiceImplTest;
    @Mock
    private ZteMrpWipIssueRepository zteMrpWipIssueRepository;

    private RetCode retCode;

    private ServiceData serviceData;


    @Test
    @PrepareForTest({ServiceData.class, RetCode.class, ZteMrpWipIssueAssembler.class, StringHelper.class,ZteMrpWipIssueServiceImpl.class})
    public void getOutWarehouseList() throws Exception {
        ZteMrpWipIssueServiceImpl serivce = PowerMockito.spy(new ZteMrpWipIssueServiceImpl());
        serivce.setZteMrpWipIssueRepository(zteMrpWipIssueRepository);
        serviceData=PowerMockito.mock(ServiceData.class);
        retCode=PowerMockito.mock(RetCode.class);
        PowerMockito.mockStatic(StringHelper.class);
        PowerMockito.whenNew(ServiceData.class).withAnyArguments().thenReturn(serviceData);
        PowerMockito.whenNew(RetCode.class).withArguments(anyString(),anyString()).thenReturn(retCode);
        PowerMockito.when(StringHelper.isEmpty(anyObject())).thenReturn(false);
        doReturn("0000").when(retCode).getCode();
        List<ZteMrpWipIssue> wipmove = new ArrayList<ZteMrpWipIssue>();
        PowerMockito.when(zteMrpWipIssueRepository.getOutWarehouseQueryList(anyObject())).thenReturn(wipmove);
        List<ZteMrpWipIssueDTO> wip = new ArrayList<ZteMrpWipIssueDTO>();
        PowerMockito.mockStatic(ZteMrpWipIssueAssembler.class);
        PowerMockito.when(ZteMrpWipIssueAssembler.toZteMrpWipIssueDTOList(wipmove)).thenReturn(wip);
        ZteMrpWipIssueDTO conditions = new ZteMrpWipIssueDTO();
        conditions.setPage(1);
        conditions.setRows(10);
        Assert.assertNotNull(serivce.getOutWarehouseList(conditions));
    }


    @Test
    public void insertZteMrpWipIssues() throws Exception {
        List<ZteMrpWipIssue> records = new ArrayList<>();
        ZteMrpWipIssue record = new ZteMrpWipIssue();
        records.add(record);
        PowerMockito.when(zteMrpWipIssueRepository.insertBatch(anyObject())).thenReturn(1);
        zteMrpWipIssueServiceImplTest.insertZteMrpWipIssues(records);
        Assert.assertNotNull(records);
    }

    
}
