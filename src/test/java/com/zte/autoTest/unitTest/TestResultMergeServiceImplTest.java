package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.TestResultMergeServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.BoardBarcodeTestResultRepository;
import com.zte.domain.model.datawb.TbMergeResultRepository;
import com.zte.domain.model.datawb.TestResultMergeMapper;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.TestInfoDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static com.zte.common.utils.Constant.*;
import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @Date 2020/12/22 23
 * @description:
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(CenterfactoryRemoteService.class)
public class TestResultMergeServiceImplTest extends BaseTestCase {
    @InjectMocks
    private TestResultMergeServiceImpl testResultMergeService;
    @Mock
    TestResultMergeMapper testResultMergeMapper;

    @Mock
    TbMergeResultRepository tbMergeResultRepository;

    @Mock
    BoardBarcodeTestResultRepository boardBarcodeTestResultRepository;

    @Test
    public void testRecordUpload() {
        List<TestInfoDTO> list = JSON.parseArray("[{\"testType\":\"3\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"sourceSys\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"},\n" +
                "{\"testType\":\"4\",\"id\":\"11111\",\"sourceBimu\":\"124\",\"sourceSys\":\"sourceSys\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}\n" +
                ",{\"testType\":\"5\",\"id\":\"11111\",\"sourceBimu\":124,\"sourceSys\":\"sourceSys\",\"createDate\":\"2022-07-02 10:10:10\",\"createdBy\":\"李泽奎10270446\",\"testerNumber\":\"testerNumber\",\"testToolNumber\":\"testToolNumber\",\"processName\":\"单板_检焊_SMT投入\",\"sn\":\"888862100020\",\"testResult\":\"1\",\"testDescription\":\"测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试没问题时测试\"}]"
        ,TestInfoDTO.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.when(boardBarcodeTestResultRepository.insertOrUpdateBatch(any())).thenReturn(1);
        PowerMockito.when(tbMergeResultRepository.insertOrUpdateBatch(any())).thenReturn(1);
        PowerMockito.when(testResultMergeMapper.insertOrUpdateBatch(any())).thenReturn(1);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.SPM_OFFLINE_SWITCH)).thenReturn(null);
        testResultMergeService.testRecordUpload(list);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.SPM_OFFLINE_SWITCH))
                .thenReturn(Lists.newArrayList(new SysLookupValues(){{setLookupMeaning(FLAG_Y);}}));
        testResultMergeService.testRecordUpload(list);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.SPM_OFFLINE_SWITCH))
                .thenReturn(Lists.newArrayList(new SysLookupValues(){{setLookupMeaning(FLAG_N);}}));
        testResultMergeService.testRecordUpload(list);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.SPM_OFFLINE_SWITCH))
                .thenReturn(Lists.newArrayList(new SysLookupValues(){{setLookupMeaning(FLAG_BOTH);}}));
        testResultMergeService.testRecordUpload(list);
        Assert.assertNotNull(list);
    }
}
