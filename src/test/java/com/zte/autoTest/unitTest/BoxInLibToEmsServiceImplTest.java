package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.*;
import com.zte.common.CommonUtils;
import com.zte.interfaces.dto.EmsInDTO;
import org.junit.Assert;
import org.junit.runner.RunWith;
import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;





@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class BoxInLibToEmsServiceImplTest {
    @InjectMocks
    BoxInLibToEmsServiceImpl boxInLibToEmsService;

    @Test
    public void monitorMesException()  {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            EmsInDTO params=new EmsInDTO();
            params.setMsg("test123");
            boxInLibToEmsService.boxInLibToEms(params);

        }
        catch(Exception e)
        {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }
}
