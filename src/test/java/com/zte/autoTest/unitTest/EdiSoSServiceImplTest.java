package com.zte.autoTest.unitTest;


import com.zte.application.IMESLogService;
import com.zte.application.datawb.EdiSoSServiceNew;
import com.zte.application.datawb.impl.EdiSoSServiceImpl;
import com.zte.application.datawb.impl.EdiSoSServiceImplNew;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.EdiSoSRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.EdiSoSNewDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@PrepareForTest({MESHttpHelper.class, HttpClientUtil.class, JacksonJsonConverUtil.class, HttpRemoteService.class,
        Normalizer.class})
@RunWith(PowerMockRunner.class)
public class EdiSoSServiceImplTest extends BaseTestCase {

    @InjectMocks
    private EdiSoSServiceImpl ediSoSServiceImp;

    @Mock
    EdiSoSRepository ediSoSRepository;
    @Mock
    EdiSoSServiceImplNew ediSoSServiceImplNew;
    @Mock
    IMESLogService imesLogService;
    @Mock
    ConstantInterface constantInterface;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private RedisTemplate<String,Object> redisTemplate;
    @Mock
    private EdiSoSServiceNew ediSoSServiceNew;
    @Mock
    private ValueOperations<String, Object>  test;

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(Normalizer.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
        MockitoAnnotations.initMocks(this);

    }

    @Test
    public void filterByBillNoAndReelid()throws Exception{
        List<EdiSoSNewDTO> inforList=new ArrayList<>();
        Whitebox.invokeMethod(ediSoSServiceImp,"filterByBillNoAndReelid",inforList);
        EdiSoSNewDTO inforDto=new EdiSoSNewDTO();
        inforDto.setExternalorderkey2("1");
        inforDto.setSerialnumber("123");
        inforList.add(inforDto);
        EdiSoSNewDTO inforDto2=new EdiSoSNewDTO();
        inforDto2.setExternalorderkey2("1");
        inforDto2.setSerialnumber("123");
        inforList.add(inforDto2);
        Whitebox.invokeMethod(ediSoSServiceImp,"filterByBillNoAndReelid",inforList);
        Assert.assertNotNull(inforList);
    }


    @Test
    public void getInforItemInfoByTime() throws Exception {
        EdiSoSNewDTO dto = new EdiSoSNewDTO();
        dto.setWmsAddress("124");

        List<EdiSoSNewDTO> inforList = new ArrayList<>();
        inforList.add(dto);
        PowerMockito.when(ediSoSRepository.getInforItemInfo(Mockito.any())).thenReturn(inforList);

        ediSoSServiceImp.getInforItemInfoByTime(dto);
        Assert.assertNotNull(dto);

    }

    @Test
    public void getInforItemInfo() throws Exception {
        EdiSoSNewDTO dto = new EdiSoSNewDTO();
        dto.setWmsAddress("124");
        dto.setScheTaskGap("1");
        dto.setProdFlag("Y");
        dto.setDipFlag("N");
        List<EdiSoSNewDTO> inforList = new ArrayList<>();
        inforList.add(dto);

        String url = "123";
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn(url);

        String result = "";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(result);

        PowerMockito.when(ediSoSRepository.getDipInforItemInfo(Mockito.any())).thenReturn(inforList);
        PowerMockito.when(ediSoSRepository.getInforItemInfo(Mockito.any())).thenReturn(inforList);

        result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[],\"other\":null}";
        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        ediSoSServiceImp.getInforItemInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void postHisInfo() throws Exception {
        List<EdiSoSNewDTO> inforList = new ArrayList<>();
        EdiSoSNewDTO dto1 = new EdiSoSNewDTO();
        dto1.setSerialkey(new BigDecimal(1));
        inforList.add(dto1);

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);

        String url = "123";
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn(url);

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        try{
            ediSoSServiceImp.postHisInfo(inforList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.POST_INFOR_ERROR, e.getMessage());
        }
    }

    @Test
    public void getHisInfo() throws Exception {

        List<EdiSoSNewDTO> inforList = new ArrayList<>();
        EdiSoSNewDTO inforDto = new EdiSoSNewDTO();
        inforDto.setSerialkey(new BigDecimal(1));
        inforList.add(inforDto);

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);

        String url = "123";
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn(url);

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        Assert.assertNull(ediSoSServiceImp.getHisInfo(inforList));
    }

    @Test
    public void syncInfoItem() throws Exception {
        EdiSoSNewDTO ediSosDTO = new EdiSoSNewDTO();
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(test);
        PowerMockito.when(test.setIfAbsent(Mockito.any(),Mockito.any(),
                Mockito.anyLong(),Mockito.any()))
                .thenReturn(true);

        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupCode(new BigDecimal("2024006"));
        a1.setLookupType(new BigDecimal(2024));
        a1.setLookupMeaning("");
        SysLookupValues a2 = new SysLookupValues();
        a2.setLookupCode(new BigDecimal("2024004"));
        a2.setLookupType(new BigDecimal(2024));
        a2.setLookupMeaning("36");
        list.add(a1);
        list.add(a2);
        PowerMockito.when(centerfactoryRemoteService.queryLookupValue(Mockito.any()))
                .thenReturn(list);

        List<EdiSoSNewDTO> inforList = new LinkedList<>();
        EdiSoSNewDTO b1 = new EdiSoSNewDTO();
        inforList.add(b1);
        PowerMockito.when(ediSoSRepository.getInforItemInfo(Mockito.any()))
                .thenReturn(inforList);
        List<EdiSoSNewDTO> result = new LinkedList<>();
        Exception exception = new Exception("");
        PowerMockito.when(ediSoSServiceNew.pushToWms(Mockito.anyList(), Mockito.any()))
                .thenReturn(Pair.of(result,exception));

        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.any(), Mockito.anyMap(), Mockito.any(),
                Mockito.any()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "   \n" +
                        "  ]\n" +
                        "}");

        ediSoSServiceImp.syncInfoItem(ediSosDTO);
        Assert.assertNotNull(ediSosDTO);
    }

    @Test
    public void getInforComingData() {
        Assert.assertNotNull(ediSoSServiceImp.getInforComingData(new EdiSoSNewDTO()));
    }
}
