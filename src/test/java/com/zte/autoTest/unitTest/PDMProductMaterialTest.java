package com.zte.autoTest.unitTest;

import com.zte.application.datawb.PDMProductTableService;
import com.zte.application.datawb.impl.NPDMaterialImpl;
import com.zte.application.datawb.impl.PDMProductMaterialImpl;
import com.zte.application.datawb.impl.PalletScanServiceImpl;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.NPDMaterialRepository;
import com.zte.domain.model.datawb.PDMProductMaterialRepository;
import com.zte.domain.model.datawb.PDMProductTableRepository;
import com.zte.domain.model.datawb.PkgBatchScanRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static org.mockito.Matchers.anyObject;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class, SpringContextUtil.class, MicroServiceRestUtil.class, JacksonJsonConverUtil.class})
@PowerMockIgnore("javax.management.*")
public class PDMProductMaterialTest extends PowerBaseTestCase {
    @Mock
    private PDMProductTableService pDMProductTableService;
    @Mock
    PDMProductTableRepository pDMProductTableRepository;
    @InjectMocks
    PDMProductMaterialImpl pDMProductMaterialImpl;

    @Mock
    private PDMProductMaterialRepository pDMProductMaterialRepository;

    @Before
    public void init() {

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void sendToPDM() {
        try {
            List<SysLookupValues> lookupValuesList = new ArrayList<>();
            SysLookupValues sysLookupValues = new SysLookupValues();
            BigDecimal a1 = new BigDecimal(6699001);
            sysLookupValues.setLookupCode(a1);
            sysLookupValues.setLookupMeaning("180000");
            lookupValuesList.add(sysLookupValues);
            SysLookupValues sysLookupValuest = new SysLookupValues();
            BigDecimal a1t = new BigDecimal(6699002);
            sysLookupValuest.setLookupCode(a1t);
            sysLookupValuest.setLookupMeaning("http://test.pdmweb.zte.com.cn/zte-plm-pdm-api/receive/assemblyInfo");
            lookupValuesList.add(sysLookupValuest);
            SysLookupValues sysLookupValuestd = new SysLookupValues();
            BigDecimal a1te = new BigDecimal(6699003);
            sysLookupValuestd.setLookupCode(a1te);
            sysLookupValuestd.setLookupMeaning("10270446,10292636");
            lookupValuesList.add(sysLookupValuestd);
            SysLookupValues sysLookupValuestdt = new SysLookupValues();
            BigDecimal a1ted = new BigDecimal(6699004);
            sysLookupValuestdt.setLookupCode(a1ted);
            sysLookupValuestdt.setLookupMeaning("5");
            lookupValuesList.add(sysLookupValuestdt);
            PowerMockito.mockStatic(CenterfactoryRemoteService.class);
            PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_6699)).thenReturn(lookupValuesList);
            //返回最大值
            List<PDMProductMaterialResultDTO> dtList = new ArrayList<>();
            PDMProductMaterialResultDTO pDMProductMaterialResultDTO = new PDMProductMaterialResultDTO();
            Timestamp dx = new Timestamp(System.currentTimeMillis());
            pDMProductMaterialResultDTO.setMaxTime(dx);
            dtList.add(pDMProductMaterialResultDTO);
            PowerMockito.when(pDMProductTableService.getDatable(Constant.PDM_JOB_NAME)).thenReturn(dtList);
            //mock这个方法
            PDMProductMaterialParamterDTO pDMProductMaterialParamterDTO = new PDMProductMaterialParamterDTO();
            PowerMockito.when(pDMProductMaterialRepository.getReleationHeader(pDMProductMaterialParamterDTO)).thenReturn(getReleationHeader());
            pDMProductMaterialImpl.sendToPDM();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());

        }
    }

    private List<PDMProductMaterialResultDTO> getReleationHeader() {
        List<PDMProductMaterialResultDTO> listDtList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            PDMProductMaterialResultDTO df = new PDMProductMaterialResultDTO();
            df.setEntityName("1233333");
            df.setProcessMessage("待推送");
            df.setBomRevision("AG");
            df.setRecordUUid(UUID.randomUUID().toString());
            df.setItemCode("12333");
            df.setSendCount((long) 1);
            df.setLastUpdateBy((long) 999);
            df.setLastUpdateNAME("9999");
            df.setCreateBy((long) 999);
            listDtList.add(df);
        }
        return listDtList;
    }

    private List<PDMProductMaterialResultDTO> getReleationHeaderKL() {
        List<PDMProductMaterialResultDTO> listDtList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            PDMProductMaterialResultDTO df = new PDMProductMaterialResultDTO();
            df.setEntityName("1233333");
            df.setProcessMessage("已经推送");
            df.setBomRevision("AG");
            df.setRecordUUid(UUID.randomUUID().toString());
            df.setItemCode("12333");
            df.setSendCount((long) 1);
            df.setLastUpdateBy((long) 999);
            df.setLastUpdateNAME("9999");
            df.setCreateBy((long) 999);
            listDtList.add(df);
        }
        return listDtList;
    }

    @Test
    public void getAnotherData() {
        try {
            List<PDMProductMaterialResultDTO> dtoList = getReleationHeader();
            PowerMockito.when(pDMProductMaterialRepository.getAnothorCollection(dtoList)).thenReturn(dtoList);
            pDMProductMaterialImpl.getAnotherData(dtoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void insertResult() {
        try {
            List<PDMProductMaterialResultDTO> dtoList = getReleationHeader();
            PowerMockito.when(pDMProductMaterialRepository.insertResultTable(anyObject())).thenReturn(1);
            pDMProductMaterialImpl.insertResult(dtoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void gePrepareData() {
        try {
            List<PDMProductMaterialResultDTO> dtoList = getReleationHeader();
            PowerMockito.when(pDMProductMaterialRepository.getPreparedData()).thenReturn(dtoList);
            pDMProductMaterialImpl.gePrepareData();
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void setPDMDataSet() {
        try {
            List<PDMProductMaterialResultDTO> dtoList = getReleationHeader();
            Timestamp dx = new Timestamp(System.currentTimeMillis());
            pDMProductMaterialImpl.setPDMDataSet(dx, dx, dtoList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);

        }
    }

    @Test
    public void setSendCount() {
        try {
            PDMProductMaterialResultDTO p = getReleationHeader().get(0);
            pDMProductMaterialImpl.setSendCount(p);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void batchUpdate() {
        try {
            List<PDMProductMaterialResultDTO> assemblyResultEntityAllList = getReleationHeader();
            PowerMockito.when(pDMProductMaterialRepository.batchResultUpdate(anyObject())).thenReturn(1);
            pDMProductMaterialImpl.batchUpdate(assemblyResultEntityAllList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void sendToPdmAndUpdateResult() {
        try {
            Timestamp dx = new Timestamp(System.currentTimeMillis());
            pDMProductMaterialImpl.sendToPdmAndUpdateResult(dx, "1233", "1233", getReleationHeader());
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void getFailureMessage() {
        try {
            List<SysLookupValues> lookupValuesList = new ArrayList<>();
            SysLookupValues sysLookupValues = new SysLookupValues();
            BigDecimal a1 = new BigDecimal(6699005);
            sysLookupValues.setLookupCode(a1);
            sysLookupValues.setLookupMeaning("180000");
            lookupValuesList.add(sysLookupValues);
            PDMProductMaterialParamterDTO pDMProductMaterialParamterDTO = new PDMProductMaterialParamterDTO();
            PowerMockito.when(pDMProductMaterialRepository.getLoggerData(pDMProductMaterialParamterDTO)).thenReturn(1);
            pDMProductMaterialImpl.getFailureMessage();
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);

        }
    }

    @Test
    public void updateForCenter() throws Exception {
        List<PDMProductMaterialResultDTO> assemblyResultEntityDTOList = new ArrayList<>();
        PDMProductMaterialResultDTO assemblyResultEntityDTO = new PDMProductMaterialResultDTO();
        assemblyResultEntityDTO.setCombineFlag("N");
        assemblyResultEntityDTO.setItemCode("itemCode");
        assemblyResultEntityDTO.setBomRevision("ver");
        assemblyResultEntityDTOList.add(assemblyResultEntityDTO);
        PowerMockito.when(pDMProductMaterialRepository.getListByItemAndVersion(anyObject())).thenReturn(assemblyResultEntityDTOList);

        Assert.assertNotNull(pDMProductMaterialImpl.updateForCenter(assemblyResultEntityDTO));
    }

    @Test
    public void filterResultCombine() {
        try {
            List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
            PowerMockito.when(pDMProductMaterialRepository.haveCombineReleation(anyObject())).thenReturn(allCombineRelation);
            Timestamp dt=new Timestamp(System.currentTimeMillis());
            Timestamp dtx=new Timestamp(System.currentTimeMillis());
            pDMProductMaterialImpl.filterResultCombine(allCombineRelation,dt,dtx);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void setDTest() {
        try {
            List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
            PowerMockito.when(pDMProductMaterialRepository.updateResultCombine(anyObject())).thenReturn(1);
            pDMProductMaterialImpl.updateResultData(allCombineRelation);

        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void setDTestDt(){
        try {
            List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
            PowerMockito.when(pDMProductMaterialRepository.insertHistoTable(anyObject())).thenReturn(1);
            pDMProductMaterialImpl.insertHistoTable(allCombineRelation);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void setHistoGuid(){
        List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
        Timestamp sb =new Timestamp(System.currentTimeMillis());
        Timestamp sbt =new Timestamp(System.currentTimeMillis());
        pDMProductMaterialImpl.setHistoGuid(allCombineRelation,sb,sbt);
        Assert.assertNotNull(allCombineRelation);
    }
    public  List<PDMSendInfoDTO> setPDM(){
        long k=0;
        List<PDMSendInfoDTO> listDto=new ArrayList<>();
        for (int i=0;i<10;i++) {
            PDMSendInfoDTO t2 = new PDMSendInfoDTO();
            t2.setSynFlag("Y");
            t2.setSynId((long)k);
            listDto.add(t2);
            k++;
        }
        return listDto ;
    }
    @Test
    public void validateEmpty(){
        try {
            List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
            List<PDMProductMaterialResultDTO> allCombineRelationt = getReleationHeader();
            pDMProductMaterialImpl.validateEmpty(allCombineRelation, allCombineRelation);
            pDMProductMaterialImpl.getRepeatItemCode(allCombineRelationt);
            pDMProductMaterialImpl.setSendCountAndStatus(allCombineRelation, setPDM());
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void getResultData(){
        try {
            List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
            List<PDMProductMaterialResultDTO> allCombineRelationt = getReleationHeader();
            pDMProductMaterialImpl.getResultData(allCombineRelation, allCombineRelationt);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void sendPDMDiferent(){
        String url="http://test.pdmweb.zte.com.cn/zte-plm-pdm-api/receive/assemblyInfo";
        try {
            pDMProductMaterialImpl.sendToPdm(setPDM(), url);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void toHisDTO(){
        try {
            List<PDMProductMaterialResultDTO> allCombineRelation = getReleationHeaderKL();
            String logId = "123333232323";
            pDMProductMaterialImpl.toHisDTO(allCombineRelation, logId);
            pDMProductMaterialImpl.insertRecordTable(allCombineRelation);
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void filterSSB(){
        Set<String> patterListList =new HashSet<>();
        patterListList.add( "^.{2}$");
        List<PDMProductMaterialResultDTO> filterData= getReleationHeaderKL();
        Assert.assertNotNull(pDMProductMaterialImpl.filterSSB(filterData ,patterListList));
    }

    public  List<PDMProductMaterialResultDTO> getReleationHeaderKLH() {
        List<PDMProductMaterialResultDTO> listDtList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            PDMProductMaterialResultDTO df = new PDMProductMaterialResultDTO();
            df.setEntityName("1233333");
            df.setProcessMessage("已经推送");
            df.setBomRevision("AG");
            long  num =1;
            String xt =String.valueOf(num);
            df.setRecordUUid(UUID.randomUUID().toString());
            df.setItemCode(xt);
            df.setSendCount((long) 1);
            df.setLastUpdateBy((long) 999);
            df.setLastUpdateNAME("9999");
            df.setCreateBy((long) 999);
            listDtList.add(df);
        }
        return listDtList;
    }
    @Test
    public void   setHash (){
        List<PDMProductMaterialResultDTO>xtdt=    getReleationHeaderKLH() ;
        List<PDMProductMaterialResultDTO>xtdtv=    getReleationHeaderKLH() ;
        Assert.assertNotNull(pDMProductMaterialImpl.setHash(xtdt,xtdtv));

    }
    @Test
    public void getRepeatDataSystem(){
        PDMProductMaterialParamterDTO pDMProductMaterialParamterDTO =new PDMProductMaterialParamterDTO();
        pDMProductMaterialParamterDTO.settAfter( new  Timestamp( System.currentTimeMillis()));
        pDMProductMaterialParamterDTO.settBefore(new Timestamp(System.currentTimeMillis()-1000000));
        Assert.assertNotNull(pDMProductMaterialImpl.getRepeatDataSystem(pDMProductMaterialParamterDTO));
    }
    @Test
    public void updateTimeDto(){
        try{
        List<PDMProductMaterialResultDTO>dtList= getReleationHeaderKLH();
        pDMProductMaterialImpl.updateTimeDto(dtList);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);

        }
    }




}













