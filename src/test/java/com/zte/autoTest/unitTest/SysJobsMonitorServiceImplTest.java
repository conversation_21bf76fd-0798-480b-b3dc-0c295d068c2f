package com.zte.autoTest.unitTest;



import com.zte.application.datawb.impl.*;
import com.zte.domain.model.datawb.*;
import com.zte.util.BaseTestCase;
import com.zte.common.CommonUtils;
import org.junit.Assert;
import org.junit.runner.RunWith;
import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;





@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class SysJobsMonitorServiceImplTest extends BaseTestCase{

    @InjectMocks
    SysJobsMonitorServiceImpl sysJobsMonitorService;

    @Mock
    SysJobsMonitorRepository sysJobsMonitorRepository;
    @Mock
    SysJobsMonitorCmsRepository sysJobsMonitorCmsRepository;

    @Test
    public void monitorJobsException() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            PowerMockito.when(sysJobsMonitorRepository.getWmsContractRecord(any())).thenReturn(null);
            PowerMockito.when(sysJobsMonitorCmsRepository.getAPPMesContractRecord(any())).thenReturn(null);
            sysJobsMonitorService.monitorJobsException();

        }
        catch(Exception e)
        {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

}
