package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BaItemServiceImpl;
import com.zte.domain.model.BaItem;
import com.zte.domain.model.BaItemRepository;
import com.zte.domain.model.datawb.Ztebarcode;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

public class BaItemServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BaItemServiceImpl baItemService;
    @Mock
    private BaItemRepository baItemRepository;

    /* Started by AICoder, pid:79dadt6ecbkb73b1434108165012e15e4d0298e0 */
    @Test
    public void queryItemBarcodeBatch(){
        List<Ztebarcode> ztebarcodes = baItemService.queryItemBarcodeBatch(null);
        Assert.assertTrue(CollectionUtils.isEmpty(ztebarcodes));

        List<String> zteList = new LinkedList<>();
        zteList.add("212");
        List<Ztebarcode> ztebarcodes1 = baItemService.queryItemBarcodeBatch(zteList);
        ztebarcodes1 = new LinkedList<>();
        ztebarcodes1.add(new Ztebarcode());
        PowerMockito.when(baItemRepository.queryItemBarcodeBatch(Mockito.anyList()))
                .thenReturn(ztebarcodes1);
        baItemService.queryItemBarcodeBatch(zteList);
    }
    /* Ended by AICoder, pid:79dadt6ecbkb73b1434108165012e15e4d0298e0 */

    @Test
    public void getStockInfo() {
        PowerMockito.when(baItemRepository.getStockInfo(any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getStockInfo("list"));
    }

    @Test
    public void getStockPowerModuleIncluded() {
        PowerMockito.when(baItemRepository.getStockPowerModuleIncluded(any(), any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getStockPowerModuleIncluded("list", ""));
    }

    @Test
    public void getMainItemNo() {
        List<String> paramList = new ArrayList<>();
        List<String> result = baItemService.getMainItemNo(paramList);
        Assert.assertTrue(result.isEmpty());

        paramList.add("220123456");
        List<String> resultList = new ArrayList<>();
        resultList.add("220123456");
        PowerMockito.when(baItemRepository.getMainItemNo(Mockito.anyList())).thenReturn(resultList);
        result = baItemService.getMainItemNo(paramList);
        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void getStockNoPowerModuleEqual() {
        PowerMockito.when(baItemRepository.getStockNoPowerModuleEqual(any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getStockNoPowerModuleEqual("list"));
    }

    @Test
    public void getStockNoPowerModuleIncluded() {
        PowerMockito.when(baItemRepository.getStockNoPowerModuleIncluded(any(), any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getStockNoPowerModuleIncluded("list", ""));
    }
    @Test
    public void getStockNoNormalCase() {
        PowerMockito.when(baItemRepository.getStockNoNormalCase(any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getStockNoNormalCase("list"));
    }
    @Test
    public void getMpnByItembarcode() {
        PowerMockito.when(baItemRepository.getMpnByItembarcode(any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getMpnByItembarcode("list"));
    }

    @Test
    public void getTheRegion() {
        PowerMockito.when(baItemRepository.getTheRegion(any(), any())).thenReturn(new BaItem());
        Assert.assertNotNull(baItemService.getTheRegion("list111111111111111111111111", ""));
        Assert.assertNotNull(baItemService.getTheRegion("2111", ""));
    }
}
