package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.WsmAssembleLinesServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.SpringUtil;
import com.zte.domain.model.datawb.WsmAssembleLines;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.WsmAssembleLinesDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.when;


/**
 * <AUTHOR>
 * @date 2023/5/22
 */
public class WsmAssembleLinesServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private WsmAssembleLinesServiceImpl wsmAssembleLinesService;

    @Mock
    private WsmAssembleLinesRepository repository;

    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;

    @Test
    public void selectMainBarcodeBySubBarcode() throws Exception{

        WsmAssembleLines line = new WsmAssembleLines();
        PowerMockito.when(repository.selectMainBarcodeBySubBarcode(Mockito.any())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(wsmAssembleLinesService, "selectMainBarcodeBySubBarcode", line);
        Assert.assertNotNull(line);
    }

    @Test
    public void getAssemblyMaterials() throws Exception {
        wsmAssembleLinesService.getAssemblyMaterials("");
        Assert.assertNotNull(wsmAssembleLinesService.getAssemblyMaterials("2"));
    }

    @Test
    public void getAssemblyMaterialList() throws Exception{

        List<String> itemBarcodeList = new ArrayList<>();
        Whitebox.invokeMethod(wsmAssembleLinesService, "getAssemblyMaterialList", itemBarcodeList);
        itemBarcodeList.add("2342352352");
        PowerMockito.when(repository.getAssemblyMaterialList(Mockito.any())).thenReturn(new ArrayList<>());
        Whitebox.invokeMethod(wsmAssembleLinesService, "getAssemblyMaterialList", itemBarcodeList);
        Assert.assertNotNull(itemBarcodeList);
    }

    @Test
    public void getAssemblyMaterialsByEntityId() throws Exception {
        wsmAssembleLinesService.getAssemblyMaterialsByEntityId(2);
        Assert.assertNotNull(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(null));
    }

    @Test
    public void getSysLookupValues() throws Exception {
        wsmAssembleLinesService.getSysLookupValues("2");
        Assert.assertNotNull(wsmAssembleLinesService.getSysLookupValues(null));
    }

    @Test
    public void getCustomerPONumber() throws Exception {
        Assert.assertNull(wsmAssembleLinesService.getCustomerPONumber(2));
    }

    @Test
    @PrepareForTest({CommonUtils.class, RetCode.class, SpringContextUtil.class, SpringUtil.class})
    public void getWsmAssembleLinesDTOPage() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class, SpringUtil.class);
        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        PowerMockito.when(repository.getWsmAssembleLinesDTOPage(Mockito.any())).thenReturn(null);
        wsmAssembleLinesService.getWsmAssembleLinesDTOPage(new WsmAssembleLinesDTO());
        wsmAssembleLinesService.getWsmAssembleLinesDTOPage(new WsmAssembleLinesDTO() {{
            setRowsString("0");
        }});
        Assert.assertNotNull(wsmAssembleLinesService.getWsmAssembleLinesDTOPage(new WsmAssembleLinesDTO() {{
            setRowsString("10");
        }}));
    }

}
