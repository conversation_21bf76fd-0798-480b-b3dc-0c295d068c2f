package com.zte.autoTest.unitTest;

import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.MesAssemblySpecificationsInfoService;
import com.zte.application.datawb.impl.MesAssemblySpecificationsInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.MaterialConfigBindRepository;
import com.zte.domain.model.datawb.RecalculationConvergenceRepository;
import com.zte.interfaces.dto.MesAssemblySpecificationsInfoDTO;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class MesAssemblySpecificationsInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private MesAssemblySpecificationsInfoServiceImpl mesAssemblySpecificationsInfoServiceImpl;

    @Mock
    private MaterialConfigBindRepository materialConfigBindRepository;

    @Mock
    private RecalculationConvergenceRepository recalculationConvergenceRepository;

    @Mock
    private CfgCodeRuleItemService cfgCodeRuleItemService;
    @Mock
    MesAssemblySpecificationsInfoService mesAssemblySpecificationsInfoService;

    @Test
    public void checkInputParam() throws Exception {
        MesAssemblySpecificationsInfoDTO paramDto = new MesAssemblySpecificationsInfoDTO();
        paramDto.setItemBarcode(new LinkedList<>());
        paramDto.setPageSize(0);
        paramDto.setCurrentPage(0);
        try {
            PowerMockito.when(mesAssemblySpecificationsInfoServiceImpl.assemblySpecificationsInfo(paramDto, "123")).thenReturn(null);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.MANY_EVERT_PAMATER, ex.getMessage());
        }

        List<String> itemBarocd = new ArrayList<>();
        itemBarocd.add("210101598107");
        itemBarocd.add("220016275087");
        paramDto.setItemBarcode(itemBarocd);
        try {
            PowerMockito.when(mesAssemblySpecificationsInfoServiceImpl.assemblySpecificationsInfo(paramDto, "123")).thenReturn(null);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PAGE_EVERY_QUALITY, ex.getMessage());
        }
        paramDto.setPageSize(1);
        try {
            PowerMockito.when(mesAssemblySpecificationsInfoServiceImpl.assemblySpecificationsInfo(paramDto, "123")).thenReturn(null);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PAGE_EVERY_QUALITY, ex.getMessage());
        }
        paramDto.setCurrentPage(1);
        try {
            PowerMockito.when(mesAssemblySpecificationsInfoServiceImpl.assemblySpecificationsInfo(paramDto, "123")).thenReturn(null);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.PAGE_EVERY_QUALITY, ex.getMessage());
        }
    }

    @Test
    public void getMesAssemblySpecificationsInfo() {
        MesAssemblySpecificationsInfoDTO paramDto = new MesAssemblySpecificationsInfoDTO();
        paramDto.setItemBarcode(new LinkedList<>());
        Assert.assertNull(mesAssemblySpecificationsInfoServiceImpl.getMesAssemblySpecificationsInfo(paramDto));
    }

}
