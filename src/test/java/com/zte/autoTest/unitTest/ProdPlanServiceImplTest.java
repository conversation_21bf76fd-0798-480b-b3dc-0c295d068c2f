package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.ApsOpProdplanServiceImpl;
import com.zte.application.datawb.impl.ProdPlanServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.ProdPlanRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ProdPlanDetailDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

/**
 * ����������
 *
 * @Author:
 * @Date: 2020/9/17 16:52
 */
@RunWith(PowerMockRunner.class)
public class ProdPlanServiceImplTest {

    @InjectMocks
    private ProdPlanServiceImpl service;

    @Mock
    private ProdPlanRepository prodPlanRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private ApsOpProdplanServiceImpl apsOpProdplanServiceImpl;
    @Mock
    private String prodPlanFlag;

    @Test
    public void selectProdPlan()throws Exception{
        Map<String, Object> map=new HashMap<>();
        List<String> planIdList=new ArrayList<>();
        planIdList.add("123");
        map.put("planIdList",planIdList);
        service.selectProdPlan(map);
        prodPlanFlag="Y";
        Assert.assertNotNull(service.selectProdPlan(map));
    }
    @Test
    public void selectScanBarcodeList()throws Exception{
        Map<String, Object> map=new HashMap<>();
        map.put("vPlanID","1223");
        service.selectScanBarcodeList(map);
        prodPlanFlag="Y";
        Assert.assertNotNull(service.selectScanBarcodeList(map));
    }

    @Test
    public void getProdPlanGetDate() {
        Assert.assertNotNull(service.getProdPlanGetDate(Lists.newArrayList("lgj171009CF36-z")));
    }

    @Test
    public void selectPlanGroupUnion() throws Exception {
        String prodPlanId = "8899725";
        Assert.assertNull(service.selectPlanGroupUnion(prodPlanId));
    }

	@Test
	public void getItemNoByProdplanId() throws Exception {
		Set<BigDecimal> planIds = new HashSet<>();
		try {
			service.getItemNoByProdplanId(planIds);
		} catch (Exception e) {
            Assert.assertEquals(MessageId.PRODPLAN_ID_NULL, e.getMessage());
        }
		planIds.add(new BigDecimal("123132"));
		List<ProdPlanDetailDTO> planDetailDTOList = new ArrayList<>();
		ProdPlanDetailDTO dto = new ProdPlanDetailDTO();
		dto.setProdplanId(new BigDecimal("123132"));
		dto.setBomNo("12344ABB");
		planDetailDTOList.add(dto);
		PowerMockito.when(prodPlanRepository.getItemNoByProdplanId(Mockito.anyList())).thenReturn(planDetailDTOList);
		service.getItemNoByProdplanId(planIds);
	}
}
