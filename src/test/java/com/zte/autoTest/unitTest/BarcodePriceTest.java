package com.zte.autoTest.unitTest;

import com.zte.application.datawb.BarcodePriceService;
import com.zte.application.datawb.impl.BarcodePriceServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.idatashare.client.clientfactory.DataServiceClient;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.idatashare.client.dataservice.QueryResult;
import com.zte.idatashare.client.enumeration.ApiInvokeTotalEnum;
import com.zte.domain.model.datawb.BarcodePriceRepository;
import com.zte.interfaces.dto.AutoSynJobsDTO;
import com.zte.interfaces.dto.BarcodePriceDTO;
import com.zte.interfaces.dto.BarcodePriceHisDTO;
import com.zte.interfaces.dto.BarcodePriceProdPlanDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @since 2023年2月21日16:18
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, DataServiceClientV1.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class BarcodePriceTest extends PowerBaseTestCase {

    @InjectMocks
    private BarcodePriceServiceImpl barcodePriceServiceImpl;

    @Mock
    private BarcodePriceRepository barcodePriceRepository;

    @Mock
    private DataServiceClient dataServiceClient;

    @Mock
    private DataServiceClientV1 dataServiceClientV1;

    @Test
    public void postToDataXAPI() throws Exception {
        try {
            PowerMockito.mockStatic(DataServiceClientV1.class);
            PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);
            PowerMockito.when(dataServiceClientV1.invoke(anyObject())).thenReturn(null);
            barcodePriceServiceImpl.postToDataXAPI("", new BarcodePriceProdPlanDTO(), 1, ApiInvokeTotalEnum.YES);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void validateBarcodePrice() throws Exception {

        String jobName = Constant.BARCODE_PRICE_JOB_NAME;
        String sessionID = "1390731";
        SimpleDateFormat dateFormat = new SimpleDateFormat(Constant.TIME_FOMART);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) - 5);
        String startTime = dateFormat.format(calendar.getTime());
        QueryResult queryResult = new QueryResult();
        List<Map<String, Object>> list = new ArrayList<>();
        String prodPlanNoKey = "prodPlanNo";
        String comeDateKey = "comeDate";
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < 1001; i++) {
            map = new HashMap<>();
            map.put(prodPlanNoKey, "2222");
            map.put("bomNo", "4444");
            map.put("itemCode", "4444");
            map.put("itemName", "555");
            map.put("itemBarcode", "666");
            map.put("singleQty", "12");
            map.put("qty", "22");
            map.put("taskQty", "33");
            map.put("supplierName", "777888");
            map.put(comeDateKey, "2022-01-06");
            map.put("brand", "999");
            map.put("productNo", "4444");
            list.add(map);
        }
        List<String> strList = new ArrayList<>();
        strList.add(prodPlanNoKey);
        strList.add("bomNo");
        strList.add("itemCode");
        strList.add("itemName");
        strList.add("itemBarcode");
        strList.add("singleQty");
        strList.add("qty");
        strList.add("taskQty");
        strList.add("supplierName");
        strList.add(comeDateKey);
        strList.add("brand");
        strList.add("productNo");
        queryResult.setRows(list);
        queryResult.setColumns(strList);
        queryResult.setTotal(1001L);
        queryResult.setInvokeId("111222333");
        List<BarcodePriceDTO> barcodePriceDTOList = new ArrayList<>();
        BarcodePriceDTO barcodePriceDTO = new BarcodePriceDTO();
        barcodePriceDTO.setProdPlanID("11111111");
        barcodePriceDTO.setProdPlanNo("2222");
        barcodePriceDTO.setBomNo("333");
        barcodePriceDTO.setItemNo("44444");
        barcodePriceDTO.setItemName("5555");
        barcodePriceDTO.setItemBarcode("666");
        barcodePriceDTO.setSingleQty(11.22);
        barcodePriceDTO.setQty(15.2);
        barcodePriceDTO.setBoardQty(123L);
        barcodePriceDTO.setSupplierName("5555");
        barcodePriceDTO.setComeDate(new Date());
        barcodePriceDTO.setBrand("5555");
        barcodePriceDTO.setProductNo("5555");
        barcodePriceDTO.setOperationTime(new Date());
        barcodePriceDTO.setEdwCreateDate(new Date());
        barcodePriceDTOList.add(barcodePriceDTO);
        List<AutoSynJobsDTO> autoSynJobsDTOList = new ArrayList<>();
        AutoSynJobsDTO autoSynJobsDTO = new AutoSynJobsDTO();
        autoSynJobsDTO.setJobName(jobName);
        autoSynJobsDTO.setLastDate(new Date());
        autoSynJobsDTOList.add(autoSynJobsDTO);
        BarcodePriceProdPlanDTO barcodePriceProdPlanDTO = new BarcodePriceProdPlanDTO();
        List<String> prodPlanIDs = new ArrayList<>();
        prodPlanIDs.add("7317062");
        prodPlanIDs.add("7321274");
        List<String> prodPlanNos = new ArrayList<>();
        prodPlanNos.add("zry220706CF06-z");
        prodPlanNos.add("7317062");
        barcodePriceProdPlanDTO.setProdPlanIDs(prodPlanIDs);
        barcodePriceProdPlanDTO.setProdPlanNos(prodPlanNos);

        PowerMockito.mockStatic(DataServiceClientV1.class);
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);
        PowerMockito.when(dataServiceClientV1.invoke(anyObject())).thenReturn(queryResult);
        PowerMockito.when(barcodePriceRepository.getJobsInfo(anyObject())).thenReturn(null);
        PowerMockito.when(barcodePriceRepository.insertBarcodeTemp(anyObject())).thenReturn(10);
        PowerMockito.when(barcodePriceRepository.deleteBarcodeHis(anyObject())).thenReturn(10);
        PowerMockito.when(barcodePriceRepository.deleteBarcodeTempStuck(anyObject())).thenReturn(10);
        PowerMockito.when(barcodePriceRepository.insertBarcodePrice(anyObject())).thenReturn(10);
        PowerMockito.when(barcodePriceRepository.deleteBarcodeTemp(anyObject())).thenReturn(2);
        PowerMockito.when(barcodePriceRepository.updateSynJob(anyObject())).thenReturn(5);
        PowerMockito.when(barcodePriceRepository.insertSynJob(anyObject())).thenReturn(8);
        try {
            PowerMockito.when(barcodePriceRepository.getJobsInfo(anyObject())).thenReturn(null);
            barcodePriceServiceImpl.synStart();
            PowerMockito.when(barcodePriceRepository.getJobsInfo(anyObject())).thenReturn(autoSynJobsDTOList);
            barcodePriceServiceImpl.synStart();
            barcodePriceServiceImpl.synRun(startTime, autoSynJobsDTO);
            barcodePriceServiceImpl.synByProdPlan(barcodePriceProdPlanDTO);
            barcodePriceServiceImpl.mesDBOp(barcodePriceDTOList, Boolean.TRUE);
            barcodePriceServiceImpl.mesDBOp(barcodePriceDTOList, Boolean.FALSE);
            barcodePriceServiceImpl.getBarcodeListByApi(startTime, barcodePriceProdPlanDTO, Boolean.TRUE);
            barcodePriceServiceImpl.getBarcodeListByApi(startTime, barcodePriceProdPlanDTO, Boolean.FALSE);
            barcodePriceServiceImpl.getJobsInfo(jobName);
            barcodePriceServiceImpl.barcodePriceListUnion(queryResult, barcodePriceDTOList);
            barcodePriceServiceImpl.postToDataXAPI(startTime, barcodePriceProdPlanDTO,
                    Constant.INT_1, ApiInvokeTotalEnum.YES);
            barcodePriceServiceImpl.transStr(map, prodPlanNoKey);
            barcodePriceServiceImpl.transLong(map, prodPlanNoKey);
            barcodePriceServiceImpl.transDouble(map, prodPlanNoKey);
            barcodePriceServiceImpl.transDate(map, comeDateKey);
            barcodePriceServiceImpl.insertBarcodeTemp(barcodePriceDTOList);
            barcodePriceServiceImpl.deleteBarcodeHis(sessionID);
            barcodePriceServiceImpl.deleteBarcodeRepeat(sessionID);
            barcodePriceServiceImpl.deleteBarcodeTempStuck(sessionID);
            barcodePriceServiceImpl.insertBarcodePrice(sessionID);
            barcodePriceServiceImpl.deleteBarcodeTemp(sessionID);
            barcodePriceServiceImpl.updateSynJob(autoSynJobsDTO);
            barcodePriceServiceImpl.insertSynJob(autoSynJobsDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        /* Started by AICoder, pid:55bc0pf366jcf0b14b320a55003b9b57e8129f07 */
        barcodePriceDTOList.clear();

        map = new HashMap<>();
        map.put("taskQty", "");
        map.put("singleQty", "");
        try {
            barcodePriceServiceImpl.barcodePriceListUnion(null, barcodePriceDTOList);
            barcodePriceServiceImpl.insertBarcodeTemp(barcodePriceDTOList);
            barcodePriceServiceImpl.transLong(map, prodPlanNoKey);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        for(int i=0;i<200;i++) {
            barcodePriceDTO.setEdwCreateDate(new Date());
            barcodePriceDTOList.add(barcodePriceDTO);
        }
        PowerMockito.mockStatic(DataServiceClientV1.class);
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);
        PowerMockito.when(dataServiceClientV1.invoke(anyObject())).thenReturn(null);
        try{
            barcodePriceServiceImpl.synRun(startTime, autoSynJobsDTO);
            barcodePriceServiceImpl.insertBarcodeTemp(barcodePriceDTOList);
        }
        catch (Exception e){
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        queryResult.setTotal(0L);
        PowerMockito.when(dataServiceClientV1.invoke(anyObject())).thenReturn(queryResult);
        try{
            barcodePriceServiceImpl.getBarcodeListByApi(startTime, barcodePriceProdPlanDTO, Boolean.TRUE);
        }
        catch (Exception e){
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        queryResult.setTotal(500000L);
        PowerMockito.when(dataServiceClientV1.invoke(anyObject())).thenReturn(queryResult);
        try{
            barcodePriceServiceImpl.getBarcodeListByApi(startTime, barcodePriceProdPlanDTO, Boolean.TRUE);
        }
        catch (Exception e){
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        /* Ended by AICoder, pid:55bc0pf366jcf0b14b320a55003b9b57e8129f07 */
    }

    @Test
    public void insertBarcodeTemp() {
        List<BarcodePriceDTO> barcodePriceDTOList = new ArrayList<>();
        for (int i = 0; i < 120; i++) {
            BarcodePriceDTO barcodePriceDTO = new BarcodePriceDTO();
            barcodePriceDTO.setProdPlanID(String.valueOf(i));
            barcodePriceDTO.setProdPlanNo("2222");
            barcodePriceDTO.setBomNo("333");
            barcodePriceDTO.setItemNo("44444");
            barcodePriceDTO.setItemName("5555");
            barcodePriceDTO.setItemBarcode("666");
            barcodePriceDTO.setSingleQty(11.22);
            barcodePriceDTO.setQty(15.2);
            barcodePriceDTO.setBoardQty(123L);
            barcodePriceDTO.setSupplierName("5555");
            barcodePriceDTO.setComeDate(new Date());
            barcodePriceDTO.setBrand("5555");
            barcodePriceDTO.setProductNo("5555");
            barcodePriceDTO.setOperationTime(new Date());
            barcodePriceDTOList.add(barcodePriceDTO);
        }
        try {
            barcodePriceServiceImpl.insertBarcodeTemp(barcodePriceDTOList);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void synHisByID() {
        PowerMockito.mockStatic(DataServiceClientV1.class);
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);
        String sessionID = "1";
        BarcodePriceHisDTO barcodePriceHisDTO = new BarcodePriceHisDTO();
        barcodePriceHisDTO.setSessionID("1");
        barcodePriceHisDTO.setIsDone("N");
        barcodePriceHisDTO.setProdPlanNo("zry220706CF06-z");
        List<BarcodePriceHisDTO> barcodePriceHisDTOList = new ArrayList<>();
        barcodePriceHisDTOList.add(barcodePriceHisDTO);
        PowerMockito.when(barcodePriceRepository.getCalHis(anyObject())).thenReturn(barcodePriceHisDTOList);
        PowerMockito.when(barcodePriceRepository.updateHisCal(anyObject())).thenReturn(2);
        try {
            barcodePriceServiceImpl.synHisByID(sessionID);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        try {
            barcodePriceServiceImpl.synHisByID("");
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        barcodePriceHisDTOList.clear();
        PowerMockito.when(barcodePriceRepository.getCalHis(anyObject())).thenReturn(barcodePriceHisDTOList);
        try {
            barcodePriceServiceImpl.synHisByID("11");
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }
}
