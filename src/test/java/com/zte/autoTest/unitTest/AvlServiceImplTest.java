package com.zte.autoTest.unitTest;

import com.zte.application.stepdt.impl.AvlServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.stepdt.AvlRepository;
import com.zte.interfaces.dto.PdRequirementLine;
import com.zte.interfaces.stepdt.dto.AvlDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.*;

import static org.mockito.Mockito.doReturn;

/**
 * Avl查询测试类
 *
 * <AUTHOR> 袁海洋
 */
public class AvlServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private AvlServiceImpl avlService;
    @Mock
    private AvlRepository avlRepository;

    @Test
    public void setAvlRepository() throws Exception {
        avlService.setAvlRepository(avlRepository);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getAvlDTOPageRows() throws Exception {
        AvlDTO dto = new AvlDTO();
        dto.setBomNo("1");
        dto.setItemNo("2");
        dto.setPageString("1");
        dto.setRowsString("10");
        Map<String, Object> resultMap = new HashMap<>(16);
        Map<String, Object> map = new HashMap<>(16);

        map.put("bomNo", dto.getBomNo());
        map.put("itemNo", dto.getItemNo());
        long page = 1;
        long rows = 10;
        map.put("startRow", (page - 1) * rows + 1);
        map.put("endRow", page * rows);

        List<AvlDTO> avlDTOList = new ArrayList<>();
        doReturn(avlDTOList).when(avlRepository).getPage(map);
        long total = 0;
        doReturn(total).when(avlRepository).getCount(map);
        resultMap.put("status", "S");
        resultMap.put("list", avlDTOList);
        resultMap.put("currentPage", "1");
        resultMap.put("pageSize", "10");
        resultMap.put("totalPage", "1");
        resultMap.put("totalRecord", "1");
        resultMap.put("outputCollection", "");

        avlService.getAvlDTOPageRows(dto);
        dto.setRowsString(null);
        avlService.getAvlDTOPageRows(dto);
        dto.setRowsString("0");
        Assert.assertNotNull(avlService.getAvlDTOPageRows(dto));
    }


    @Test
    public void getAvlBrandBatchAll() {
        List<AvlDTO> list = new LinkedList<>();
        AvlDTO avlDTO = new AvlDTO();
        list.add(avlDTO);

        Assert.assertNotNull(avlService.getAvlBrandBatchAll(list));
    }

    @Test
    public void avlInfoBatch() {
        List<String> bomList = new LinkedList<>();
        Assert.assertNotNull(avlService.avlInfoBatch(bomList));
    }

    @Test
    public void getAvlBrandByItemNoListTest() {
        List<PdRequirementLine> pdRequirementLines = new ArrayList<>();
        PdRequirementLine pdRequirementLine = new PdRequirementLine();
        pdRequirementLine.setBomCode("bom");
        pdRequirementLine.setItemCode("item");
        pdRequirementLines.add(pdRequirementLine);
        List<AvlDTO> avlBrandByItemNoList = new ArrayList<>();
        avlBrandByItemNoList.add(new AvlDTO());
        PowerMockito.when(avlRepository.getAvlBrandByItemNoList(Mockito.anySet(), Mockito.anySet(), Mockito.anySet())).thenReturn(avlBrandByItemNoList);
        Assert.assertNotNull(avlService.getAvlBrandByItemNoList(pdRequirementLines));
        pdRequirementLine.setItemCode(null);
        Assert.assertNotNull(avlService.getAvlBrandByItemNoList(pdRequirementLines));
    }
}
