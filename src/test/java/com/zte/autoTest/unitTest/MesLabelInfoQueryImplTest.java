package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MesLabelInfoQueryServiceImpl;
import com.zte.common.utils.SpringUtil;
import com.zte.domain.model.datawb.MesLabelInfoQueryRepository;
import com.zte.interfaces.dto.BarcodeCommon;
import com.zte.interfaces.dto.MesLabelInfoQueryDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

@PrepareForTest({SpringUtil.class})
public class MesLabelInfoQueryImplTest extends PowerBaseTestCase {

    @InjectMocks
    private MesLabelInfoQueryServiceImpl mesLabelInfoQueryServiceImpl;
    @Mock
    private MesLabelInfoQueryRepository mesLabelInfoQueryRepository;

    @Test
    public void getMesLabelInfoQuery() {
        try {
            List<MesLabelInfoQueryDTO> mesLabelBarcode = new ArrayList<>();
            MesLabelInfoQueryDTO dtx = new MesLabelInfoQueryDTO();
            mesLabelBarcode.add(dtx);
            PowerMockito.when(mesLabelInfoQueryRepository.getMesLabelInfoQuery(any())).thenReturn(mesLabelBarcode);
            mesLabelInfoQueryServiceImpl.getMesLabelInfoQuery(dtx);
            dtx.setEntityName("12333");
            mesLabelInfoQueryServiceImpl.getMesLabelInfoQuery(dtx);
        } catch (Exception ex) {
            Assert.assertEquals(ex.getMessage(),ex.getMessage());
        }
    }


}
