package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.application.datawb.ZmsCommonService;
import com.zte.application.datawb.ZmsOverallUnitService;
import com.zte.application.datawb.impl.ZmsAlibabaServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.FileUtils;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsAlibabaRepository;
import com.zte.domain.model.datawb.ZmsCommonRepository;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.IcmsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class, FileUtils.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsAlibabaServiceImplTest {

    @InjectMocks
    private ZmsAlibabaServiceImpl zmsAlibabaService;

    @Mock
    private ZmsAlibabaRepository zmsAlibabaRepository;

    @Mock
    private ZmsCommonService zmsCommonService;

    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadServiceImpl;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZmsOverallUnitService zmsOverallUnitService;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    IcmsRemoteService icmsRemoteService;

    @Mock
    ZmsCommonRepository zmsCommonRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class, FileUtils.class);

    }
    @Test
    public void substringIncludingFirstPq() throws Exception {
        String input = "iulok";
        String result = Whitebox.invokeMethod(zmsAlibabaService,"substringIncludingFirstPq",input);
        Assert.assertEquals(input,"iulok");
        input = "32424Prhr";
        result = Whitebox.invokeMethod(zmsAlibabaService,"substringIncludingFirstPq",input);
        Assert.assertNotNull(input,"Prhr");
    }
    @Test
    public void uploadAlibabaTestResult() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        List<String> recordIdList = new ArrayList<>();
        recordIdList.add("11");
        dto.setRecordIdList(recordIdList);
        dto.setEmpNo("0668000680");
        Whitebox.invokeMethod(zmsAlibabaService, "uploadAlibabaTestResultByManual", recordIdList, dto.getEmpNo());
        Whitebox.invokeMethod(zmsAlibabaService, "uploadAlibabaTestResult", dto);

        dto.setRecordIdList(null);
        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("11111");
        dto.setEntityNameList(entityNameList);
        List<ZmsCommonEntityDTO> zmsCommonEntityDTOList = new ArrayList<>();
        PowerMockito.when(zmsCommonService.getZmsEntityList(Mockito.any())).thenReturn(zmsCommonEntityDTOList);
        Whitebox.invokeMethod(zmsAlibabaService, "uploadAlibabaTestResult", dto);

        ZmsCommonEntityDTO zmsCommonEntityDTO = new ZmsCommonEntityDTO();
        zmsCommonEntityDTO.setServerSn("1111");
        zmsCommonEntityDTOList.add(zmsCommonEntityDTO);
        PowerMockito.when(zmsCommonService.getZmsEntityList(Mockito.any())).thenReturn(zmsCommonEntityDTOList);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1111");
        sysLookupValues.setDescription("111");
        PowerMockito.when(zmsCommonService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValues);
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsAlibabaService, "uploadCompleteMachineData", zmsCommonEntityDTOList, zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsAlibabaService, "getCbomInfo", zmsCommonEntityDTOList, dto);
        Whitebox.invokeMethod(zmsAlibabaService, "uploadAlibabaTestResult", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void uploadCompleteMachineData() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        List<ZmsCommonEntityDTO> zmsCommonEntityDTOList = new ArrayList<>();
        ZmsCommonEntityDTO zmsCommonEntityDTO = new ZmsCommonEntityDTO();
        zmsCommonEntityDTO.setUserAddress("111");
        zmsCommonEntityDTO.setServerSn("219415761672");
        zmsCommonEntityDTOList.add(zmsCommonEntityDTO);
        Whitebox.invokeMethod(zmsCommonService, "getServerSnInfo", zmsCommonEntityDTO);
        Whitebox.invokeMethod(zmsCommonService, "getBoardSnInfo", zmsCommonEntityDTO);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1111");
        sysLookupValues.setDescription("111");
        PowerMockito.when(zmsCommonService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(zmsCommonService.getZsInfo(Mockito.any(), Mockito.any())).thenReturn("");
        Whitebox.invokeMethod(zmsAlibabaService, "uploadCompleteMachineData", zmsCommonEntityDTOList, zmsCbomInfoDTOList);

        PowerMockito.when(zmsCommonService.getZsInfo(Mockito.any(), Mockito.any())).thenReturn("[{\"stations\":[{\"name\":\"压测Pro工序\",\"startTime\":1740022215,\"attribute\":\"测试\",\"endTime\":1740022225,\"state\":\"FAIL\",\"problems\":[{\"phenomenonDetail\":\"IBMC_网卡端口协商速率和状态读取检测\",\"componentSn\":\"GPU0063660436\",\"reasonDetail\":\"老化测试工序-test1\",\"componentItemcode\":\"053101900023\",\"phenomenonType\":\"压测Pro工序\",\"problemId\":\"压测Pro工序59503\",\"reasonType\":\"更换物料\"}],\"stationId\":\"压测Pro工序22697026\"}],\"serverSn\":\"219415761672\"}]");
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(zmsCommonService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesList);
        PowerMockito.when(zmsCommonService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValues);
        Whitebox.invokeMethod(zmsAlibabaService, "uploadCompleteMachineData", zmsCommonEntityDTOList, zmsCbomInfoDTOList);
        List<ZmsStationItemDTO> stations = new ArrayList<>();
        ZmsStationItemDTO zmsStationItemDTO = new ZmsStationItemDTO();
        zmsStationItemDTO.setName("1111");
        zmsStationItemDTO.setStationId("1111");
        zmsStationItemDTO.setStartTime(100222225);
        zmsStationItemDTO.setEndTime(100222226);
        stations.add(zmsStationItemDTO);
        PowerMockito.when(zmsCommonService.stationInfoMapping(Mockito.any(), Mockito.any())).thenReturn(stations);
        List<ZmsAlibabaTestResultDTO> zmsAlibabaTestResultDTOList = new ArrayList<>();
        PowerMockito.when(zmsCommonService.getZsInfo(Mockito.any(), Mockito.any())).thenReturn("[]");
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsAlibabaService, "pushDataToB2B", zmsAlibabaTestResultDTOList, zmsCommonEntityDTO, sysLookupValues);
        Whitebox.invokeMethod(zmsAlibabaService, "uploadCompleteMachineData", zmsCommonEntityDTOList, zmsCbomInfoDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getCbomInfo() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        List<ZmsCommonEntityDTO> zmsCommonEntityDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsAlibabaService, "getCbomInfo", zmsCommonEntityDTOList, dto);

        ZmsCommonEntityDTO zmsCommonEntityDTO = new ZmsCommonEntityDTO();
        zmsCommonEntityDTO.setEntityName("1111");
        zmsCommonEntityDTOList.add(zmsCommonEntityDTO);
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOS = new ArrayList<>();
        PowerMockito.when(zmsCommonService.getCbomInfoNoVersion(Mockito.any())).thenReturn(zmsCbomInfoDTOS);
        Whitebox.invokeMethod(zmsAlibabaService, "getCbomInfo", zmsCommonEntityDTOList, dto);

        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("111");
        zmsCbomInfoDTO.setCbomNameCn("111");
        zmsCbomInfoDTOS.add(zmsCbomInfoDTO);
        PowerMockito.when(zmsCommonService.getCbomInfoNoVersion(Mockito.any())).thenReturn(zmsCbomInfoDTOS);
        Whitebox.invokeMethod(zmsAlibabaService, "getCbomInfo", zmsCommonEntityDTOList, dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getAlibabaTestResultInfo() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        List<ZmsStationItemDTO> stations = new ArrayList<>();
        ZmsCommonEntityDTO zmsCommonEntityDTO = new ZmsCommonEntityDTO();
        zmsCommonEntityDTO.setUserAddress("1111");
        zmsCommonEntityDTO.setServerSn("1111");
        zmsCommonEntityDTO.setEntityName("111");
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);

        ZmsStationItemDTO zmsStationItemDTO = new ZmsStationItemDTO();
        zmsStationItemDTO.setStationId("1111");
        zmsStationItemDTO.setStartTime(122222222);
        zmsStationItemDTO.setEndTime(122222225);
        stations.add(zmsStationItemDTO);
        ZmsStationItemDTO zmsStationItemDTO2 = new ZmsStationItemDTO();
        zmsStationItemDTO2.setStationId("2222");
        zmsStationItemDTO2.setStartTime(122222222);
        zmsStationItemDTO2.setEndTime(122222225);
        stations.add(zmsStationItemDTO2);
        List<ZmsAlibabaTestResultDTO> zmsAlibabaTestResultSuccessList = new ArrayList<>();
        ZmsAlibabaTestResultDTO zmsAlibabaTestResultDTO = new ZmsAlibabaTestResultDTO();
        zmsAlibabaTestResultDTO.setRequestId("ZTE1111");
        zmsAlibabaTestResultDTO.setChassisSn("1111");
        zmsAlibabaTestResultSuccessList.add(zmsAlibabaTestResultDTO);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setDescription("");
        PowerMockito.when(zmsCommonService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(zmsCommonService.getZsInfo(Mockito.any(), Mockito.any())).thenReturn("[]");
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);
        PowerMockito.when(zmsCommonService.getZsInfo(Mockito.any(), Mockito.any())).thenReturn("[{\"stationLog\":\"\",\"log_name\":\"219415761672_安规测试工序22694333.zip\",\"station_id\":\"1111\",\"serverSn\":\"1111\"}]");
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);

        CpmConfigItemAssembleDTO cpmDTO = new CpmConfigItemAssembleDTO();
        cpmDTO.setItemCode("12234");
        CustomerItemsDTO cusDTO =new CustomerItemsDTO();
        cusDTO.setCustomerComponentType("123");
        zmsCommonEntityDTO.setServerSnCustomerItemsDTO(cusDTO);
        zmsCommonEntityDTO.setServerSnCpmConfigItemAssembleDTO(cpmDTO);
        PowerMockito.when(zmsCommonService.getZsInfo(Mockito.any(), Mockito.any())).thenReturn("[{\"stationLog\":\"\",\"log_name\":\"219415761672_安规测试工序22694333.zip\",\"station_id\":\"1111\",\"serverSn\":\"1111\"},{\"stationLog\":\"\",\"log_name\":\"219415761672_安规测试工序22694333.zip\",\"station_id\":\"333\",\"serverSn\":\"1111\"}]");
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultList(Mockito.any(), Mockito.any())).thenReturn(zmsAlibabaTestResultSuccessList);
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setDescription("111");
        PowerMockito.when(zmsCommonService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValues1);
        List<ZmsStationProblemDTO> zmsStationProblemDtoList = new ArrayList<>();
        ZmsStationProblemDTO zmsStationProblemDto = new ZmsStationProblemDTO();
        zmsStationProblemDto.setProblemId("p123");
        zmsStationProblemDtoList.add(zmsStationProblemDto);
        stations.get(0).setProblems(zmsStationProblemDtoList);
        zmsCommonEntityDTO.setServerSnCustomerItemsDTO(null);
        zmsCommonEntityDTO.setServerSnCpmConfigItemAssembleDTO(null);
        zmsAlibabaTestResultSuccessList.clear();
        ZmsAlibabaTestResultDTO zmsAlibabaTestResultDTO2 = new ZmsAlibabaTestResultDTO();
        zmsAlibabaTestResultDTO2.setRequestId("ZTE2222");
        zmsAlibabaTestResultDTO2.setChassisSn("1111");
        zmsAlibabaTestResultSuccessList.add(zmsAlibabaTestResultDTO2);
        List<String> boardSnList = new ArrayList<>();
        boardSnList.add("111");
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultList(Mockito.any(), Mockito.any())).thenReturn(zmsAlibabaTestResultSuccessList);
        PowerMockito.when(zmsCommonService.getServiceSnBoardSn(Mockito.any(), Mockito.any())).thenReturn(boardSnList);
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);

        zmsAlibabaTestResultSuccessList.clear();
        ZmsAlibabaTestResultDTO zmsAlibabaTestResultDTO3 = new ZmsAlibabaTestResultDTO();
        zmsAlibabaTestResultDTO3.setRequestId("ZTE1111");
        zmsAlibabaTestResultDTO3.setChassisSn("2222");
        zmsAlibabaTestResultSuccessList.add(zmsAlibabaTestResultDTO3);
        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("1111");
        zmsCommonEntityDTO.setServerSnCpmConfigItemAssembleDTO(cpmConfigItemAssembleDTO);
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCustomerComponentType("111");
        zmsCommonEntityDTO.setServerSnCustomerItemsDTO(customerItemsDTO);
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultList(Mockito.any(), Mockito.any())).thenReturn(zmsAlibabaTestResultSuccessList);
        PowerMockito.when(zmsCommonService.getServiceSnBoardSn(Mockito.any(), Mockito.any())).thenReturn(boardSnList);

        ZmsAlibabaTestResultDTO zmsAlibabaTestResultDTO4 = new ZmsAlibabaTestResultDTO();
        zmsAlibabaTestResultDTO4.setRequestId("ZTE2222");
        zmsAlibabaTestResultDTO4.setChassisSn("2222");
        zmsAlibabaTestResultSuccessList.add(zmsAlibabaTestResultDTO4);
        stations.clear();
        List<ZmsStationProblemDTO> zmsStationProblemDTOS = new ArrayList<>();
        ZmsStationProblemDTO zmsStationProblemDTO = new ZmsStationProblemDTO();
        zmsStationProblemDTO.setPhenomenonDetail("111");
        zmsStationProblemDTOS.add(zmsStationProblemDTO);
        zmsStationItemDTO2.setProblems(zmsStationProblemDTOS);
        stations.add(zmsStationItemDTO2);
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultList(Mockito.any(), Mockito.any())).thenReturn(zmsAlibabaTestResultSuccessList);
        PowerMockito.when(zmsCommonService.getServiceSnBoardSn(Mockito.any(), Mockito.any())).thenReturn(boardSnList);
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);

        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO);
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultList(Mockito.any(), Mockito.any())).thenReturn(zmsAlibabaTestResultSuccessList);
        PowerMockito.when(zmsCommonService.getServiceSnBoardSn(Mockito.any(), Mockito.any())).thenReturn(boardSnList);
        Whitebox.invokeMethod(zmsAlibabaService, "getAlibabaTestResultInfo", stations, zmsCommonEntityDTO, zmsCbomInfoDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void pushDataToB2B() throws Exception {

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");

        List<ZmsAlibabaTestResultDTO> zmsAlibabaTestResultDTOList = new ArrayList<>();
        ZmsCommonEntityDTO zmsCommonEntityDTO = new ZmsCommonEntityDTO();
        SysLookupValues sysLookupValues = new SysLookupValues();
        Whitebox.invokeMethod(zmsAlibabaService, "pushDataToB2B", zmsAlibabaTestResultDTOList, zmsCommonEntityDTO, sysLookupValues);

        ZmsAlibabaTestResultDTO zmsAlibabaTestResultDTO = new ZmsAlibabaTestResultDTO();
        zmsAlibabaTestResultDTO.setChassisSn("111");
        zmsAlibabaTestResultDTOList.add(zmsAlibabaTestResultDTO);
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "pushDataToB2B", dataList, false);
        PowerMockito.when(zmsAlibabaRepository.insertAlibabaTestResult(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsCommonService, "insertInternetMain", zmsCommonEntityDTO, sysLookupValues.getLookupMeaning());
        Whitebox.invokeMethod(zmsAlibabaService, "pushDataToB2B", zmsAlibabaTestResultDTOList, zmsCommonEntityDTO, sysLookupValues);

        zmsCommonEntityDTO.setEmpNo("111");
        Whitebox.invokeMethod(zmsAlibabaService, "pushDataToB2B", zmsAlibabaTestResultDTOList, zmsCommonEntityDTO, sysLookupValues);

        Assert.assertNotNull(dto);
    }

    @Test
    public  void uploadAlibabaTestResultByManual() throws Exception{

        ZmsCommonRequestDTO dto = new ZmsCommonRequestDTO();
        dto.setUserAddress("阿里");
        List<String> recordIdList = new ArrayList<>();
        String empNo = "111";
        List<ZmsAlibabaTestResultDTO> zmsAlibabaTestResultDTOList = new ArrayList<>();
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultByRecordId(Mockito.any())).thenReturn(zmsAlibabaTestResultDTOList);
        Whitebox.invokeMethod(zmsAlibabaService, "uploadAlibabaTestResultByManual", recordIdList, empNo);

        ZmsAlibabaTestResultDTO zmsAlibabaTestResultDTO = new ZmsAlibabaTestResultDTO();
        zmsAlibabaTestResultDTO.setRecordId(new BigDecimal(1));
        zmsAlibabaTestResultDTO.setMessageId("111");
        zmsAlibabaTestResultDTOList.add(zmsAlibabaTestResultDTO);
        PowerMockito.when(zmsAlibabaRepository.getAlibabaTestResultByRecordId(Mockito.any())).thenReturn(zmsAlibabaTestResultDTOList);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("111");
        sysLookupValues.setDescription("1111");
        PowerMockito.when(zmsCommonService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValues);
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        Whitebox.invokeMethod(zmsCommonService, "pushDataToB2B", dataList, false);
        PowerMockito.when(zmsAlibabaRepository.updateAlibabaTestResultByRecordId(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsAlibabaService, "uploadAlibabaTestResultByManual", recordIdList, empNo);
        Assert.assertNotNull(dto);
    }

    /*Started by AICoder, pid:1a4f36a602m577b148a6082a3198795c0d4046ff*/
    @Test
    public void testAlibabaFileLogUpload_EmptyList() throws Exception {
        StationInputDTO stationInputDTO = new StationInputDTO();
        when(zmsAlibabaRepository.getAlibabaFileLogSn(any(StationInputDTO.class))).thenReturn(Collections.emptyList());
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setDescription("CustomerName");
        when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupValues);

        Assert.assertTrue(zmsAlibabaService.alibabaFileLogUpload(stationInputDTO));
        verify(zmsAlibabaRepository, times(1)).getAlibabaFileLogSn(any(StationInputDTO.class));
    }

    @Test
    public void testAlibabaFileLogUpload_GetTokenException() throws Exception {
        StationInputDTO stationInputDTO = new StationInputDTO();
        List<StationSnDataDTO> logList = Collections.singletonList(new StationSnDataDTO());
        when(zmsAlibabaRepository.getAlibabaFileLogSn(any(StationInputDTO.class))).thenReturn(logList);
        when(zmsAlibabaRepository.getAlibabaMainLogBySn(any())).thenReturn(Collections.emptyList());
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setDescription("CustomerName");
        when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupValues);
        doThrow(new RuntimeException("Token Error")).when(zmsStationLogUploadServiceImpl).getToken();

        try {
            zmsAlibabaService.alibabaFileLogUpload(stationInputDTO);
            fail("Expected MesBusinessException");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals(MessageId.TOKEN_VALIDATE, e.getExMsgId());
        }

        verify(zmsAlibabaRepository, times(1)).getAlibabaFileLogSn(any(StationInputDTO.class));
        verify(zmsCommonService, times(1)).getSysLookupValues(anyString());
        verify(zmsStationLogUploadServiceImpl, times(1)).getToken();
        //verify(zmsAlibabaService, times(1)).filterAlibabaMainLog(anyList(), anyString(), anyString());
    }

    @Test
    public void testAlibabaFileLogUpload_SuccessfulUpload() throws Exception {
        StationInputDTO stationInputDTO = new StationInputDTO();
        List<StationSnDataDTO> logList = new ArrayList<>();
        StationSnDataDTO stationSnDataDto = new StationSnDataDTO();
        stationSnDataDto.setRequestId("123");
        StationSnDataDTO stationSnDataDto1 = new StationSnDataDTO();
        stationSnDataDto1.setRequestId("ZTE123");
        logList.add(stationSnDataDto);
        logList.add(stationSnDataDto1);
        when(zmsAlibabaRepository.getAlibabaFileLogSn(any(StationInputDTO.class))).thenReturn(logList);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setDescription("CustomerName");
        when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupValues);
        when(zmsStationLogUploadServiceImpl.getToken()).thenReturn("validToken");
        sysLookupValues.setDescription("ZsUrl");
        when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(sysLookupValues);
        List<StationLogDTO> stationLogDtoList = new ArrayList<>();
        StationLogDTO stationLogDto = new StationLogDTO();
        stationLogDto.setStationId("出厂配置工序1");
        stationLogDto.setLogName("logName2");
        stationLogDto.setServerSn("serverSn1");
        List<StationLogDTO> stationLogDtoList2 = new ArrayList<>();
        StationLogDTO stationLogDto2 = new StationLogDTO();
        stationLogDto2.setStationId("出厂配置工序1");
        stationLogDto2.setLogName("logName2");
        stationLogDto2.setServerSn("serverSn2");
        StationLogDTO stationLogDto3 = new StationLogDTO();
        stationLogDto3.setStationId("出厂配置工序2");
        stationLogDto3.setLogName("logName3");
        stationLogDto3.setServerSn("serverSn2");
        stationLogDtoList.add(stationLogDto);
        stationLogDtoList.add(stationLogDto2);
        stationLogDtoList.add(stationLogDto3);
        stationLogDtoList2.add(stationLogDto2);
        //String zsResponse = "[{\"stationLog\":\"https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/CLOUDMDS/123456\",\"log_name\":\"logName1\",\"station_id\":\"出厂配置工序1\",\"serverSn\":\"serverSn1\"}]";
        String zsResponse = "";

        when(zmsStationLogUploadServiceImpl.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn("");
        Assert.assertTrue(zmsAlibabaService.alibabaFileLogUpload(stationInputDTO));

        zsResponse = "{\"sn\": \"219483385429\"}";

        when(zmsStationLogUploadServiceImpl.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn(zsResponse);
        when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(Collections.emptyList());
        Assert.assertTrue(zmsAlibabaService.alibabaFileLogUpload(stationInputDTO));

        zsResponse = "{\"file_name\":\"1234.txt\",\"sn\": \"219483385429\"}";

        when(cloudDiskHelper.fileUpload(anyString(),anyString(),anyInt())).thenReturn("1234");
        when(cloudDiskHelper.getFileDownloadUrl(anyString(),anyString(),anyString())).thenReturn("url123");
        String path = "/usr/test";
        when(FileUtils.getCurrFilePath()).thenReturn(path);
        when(zmsStationLogUploadServiceImpl.getZsStationLog(anyMap(), anyString(), anyString())).thenReturn(zsResponse);
        when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(Collections.emptyList());

        Assert.assertTrue(zmsAlibabaService.alibabaFileLogUpload(stationInputDTO));

        when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(stationLogDtoList);
        when(zmsAlibabaRepository.queryAlibabaUploadFileLogSuccessList(anyList(), anyInt())).thenReturn(Collections.emptyList());
        Assert.assertTrue(zmsAlibabaService.alibabaFileLogUpload(stationInputDTO));

        when(zmsAlibabaRepository.queryAlibabaUploadFileLogSuccessList(anyList(), anyInt())).thenReturn(stationLogDtoList2);
        Assert.assertTrue(zmsAlibabaService.alibabaFileLogUpload(stationInputDTO));
    }

    @Test
    public void testFilterAlibabaMainLog() {
        List<StationSnDataDTO> stationSnDataDTOList = new ArrayList<>();
        Assert.assertFalse(zmsAlibabaService.filterAlibabaMainLog(stationSnDataDTOList,"","alibaba"));

        StationSnDataDTO stationSnDataDto = new StationSnDataDTO();
        stationSnDataDto.setServerSn("server1");
        stationSnDataDTOList.add(stationSnDataDto);

        List<StationCheckLogDTO> stationCheckLogDTOList = new ArrayList<>();
        StationCheckLogDTO stationCheckLogDto = new StationCheckLogDTO();
        stationCheckLogDto.setStationId("stationId1");
        stationCheckLogDTOList.add(stationCheckLogDto);
        when(zmsAlibabaRepository.getAlibabaMainLogBySn(any())).thenReturn(stationCheckLogDTOList);

        PowerMockito.doNothing().when(zmsOverallUnitService).writeMainLog(any(),any(),any());
        Assert.assertTrue(zmsAlibabaService.filterAlibabaMainLog(stationSnDataDTOList,"","alibaba"));
    }

    /*Ended by AICoder, pid:1a4f36a602m577b148a6082a3198795c0d4046ff*/

    @Test
    public void queryAliMatIssueDataTest() throws Exception{
        ZmsAliMatIssueRequestDTO dto = new ZmsAliMatIssueRequestDTO();
        //空数据
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        dto.setBillNumber("123");
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        dto.setBillType(0);
        dto.setBillNumber("");
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        dto.setBillNumber("111");
        dto.setBillType(0);
        List<ZmsAliMatIssuePlanDTO> list = new ArrayList<>();
        when(zmsAlibabaRepository.queryPickListForPlan(anyString())).thenReturn(list);
        //空数据
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        ZmsAliMatIssuePlanDTO item = new ZmsAliMatIssuePlanDTO();
        item.setBillNumber("111");
        item.setFixBomId("123");
        item.setItemCode("899987700001");
        item.setPickType("车间库");
        list.add(item);
        ZmsAliMatIssuePlanDTO item3 = new ZmsAliMatIssuePlanDTO();
        item3.setBillNumber("111");
        item3.setFixBomId("123");
        item3.setItemCode("153");
        item3.setPickType("车间库");
        list.add(item3);
        List<CpqdGbomDTO> cpqdGbomDTOS = new ArrayList<>();
        CpqdGbomDTO cpqdGbomDTO1 = new CpqdGbomDTO();
        cpqdGbomDTO1.setCbomCode("123");
        cpqdGbomDTOS.add(cpqdGbomDTO1);
        CpqdGbomDTO cpqdGbomDTO2 = new CpqdGbomDTO();
        cpqdGbomDTO2.setInstanceNo("153");
        cpqdGbomDTOS.add(cpqdGbomDTO2);
        when(zmsCommonService.queryGbomList(any())).thenReturn(cpqdGbomDTOS);
        when(zmsAlibabaRepository.queryPickListForPlan(anyString())).thenReturn(list);
        //车间库领料
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        list = new ArrayList<>();
        item.setCustomerPartType("123");
        list.add(item);
        ZmsAliMatIssuePlanDTO item1 = new ZmsAliMatIssuePlanDTO();
        item1.setBillNumber("111");
        item.setCustomerPartType("123");
        item.setCustomerItemName("123");
        item1.setFixBomId("123");
        item1.setItemCode("899987700001");
        item1.setPickType("车间库");
        list.add(item1);
        cpqdGbomDTOS = new ArrayList<>();
        when(zmsCommonService.queryGbomList(any())).thenReturn(cpqdGbomDTOS);
        when(zmsAlibabaRepository.queryPickListForPlan(anyString())).thenReturn(list);
        //车间库领料
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        list = new ArrayList<>();
        item.setPickType("配送库");
        item.setTaskNo("123");
        item.setOid("123");
        list.add(item);
        item1.setCustomerPartType("123");
        item1.setPickType("配送库");
        item1.setCustomerItemName("123");
        item1.setItemCode("");
        list.add(item1);
        ZmsAliMatIssuePlanDTO item4 = new ZmsAliMatIssuePlanDTO();
        item4.setCustomerPartType("321");
        item4.setPickType("配送库");
        item4.setCustomerItemName("321");
        item4.setItemCode("321");
        item4.setQty(2);
        list.add(item4);
        ZmsAliMatIssuePlanDTO item5 = new ZmsAliMatIssuePlanDTO();
        item5.setCustomerPartType("321");
        item5.setPickType("配送库");
        item5.setCustomerItemName("321");
        item5.setItemCode("432");
        item5.setQty(1);
        list.add(item5);
        when(zmsAlibabaRepository.queryPickListForPlan(anyString())).thenReturn(list);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        List<ZmsAliTaskQtyDTO> tlist = new ArrayList<>();
        ZmsAliTaskQtyDTO zdto = new ZmsAliTaskQtyDTO();
        zdto.setPlanned(true);
        zdto.setEntityNo("123");
        tlist.add(zdto);
        String bo = JacksonJsonConverUtil.beanToJson(tlist);
        PowerMockito.when(zmsCommonService.queryTaskQty(any(),any())).thenReturn(null);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        PowerMockito.when(zmsCommonService.queryTaskQty(any(),any())).thenReturn("1");
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        PowerMockito.when(zmsCommonService.queryTaskQty(any(),any())).thenReturn("[{\"entityNo\":\"123\",\"planned\":true}]");
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("899987700001");
        customerItemsDTO.setCustomerCode("");
        customerItemsDTOS.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("899987700001");
        customerItemsDTO1.setCustomerCode("123456");
        customerItemsDTOS.add(customerItemsDTO1);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("321");
        customerItemsDTO2.setCustomerCode("123");
        customerItemsDTOS.add(customerItemsDTO2);
        CustomerItemsDTO customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setZteCode("432");
        customerItemsDTO3.setCustomerCode("123");
        customerItemsDTOS.add(customerItemsDTO3);
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        customerItemsDTOS= new ArrayList<>();
        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("899987700001");
        customerItemsDTO.setCustomerCode("123");
        customerItemsDTOS.add(customerItemsDTO);
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        customerItemsDTOS= new ArrayList<>();
        customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("666");
        customerItemsDTO.setCustomerCode("123");
        customerItemsDTOS.add(customerItemsDTO);
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        dto.setBillType(1);
        list = new ArrayList<>();
        when(zmsAlibabaRepository.queryReturnListForPlan(anyString())).thenReturn(list);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        item = new ZmsAliMatIssuePlanDTO();
        item.setBillNumber("111");
        item.setFixBomId("123");
        item.setItemCode("899987700001");
        item.setCustomerPartType("123");
        item.setCustomerItemName("123");
        item.setPickType("车间库");
        list.add(item);
        ZmsAliMatIssuePlanDTO item2 = new ZmsAliMatIssuePlanDTO();
        item2.setBillNumber("111");
        item2.setFixBomId("123");
        item2.setCustomerPartType("123");
        item2.setCustomerItemName("123");
        item2.setItemCode("");
        item2.setPickType("车间库");
        list.add(item2);
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        when(zmsAlibabaRepository.queryPickListForPlan(anyString())).thenReturn(list);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        customerItemsDTOS = new ArrayList<>();
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        customerItemsDTOS.add(customerItemsDTO);
        when(centerfactoryRemoteService.getCustomerItemsInfo(anyString(),any())).thenReturn(customerItemsDTOS);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        dto.setBillType(2);
        List<ZmsAliMatIssueActualDTO> aclist = new ArrayList<>();
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        ZmsAliMatIssueActualDTO acItem = new ZmsAliMatIssueActualDTO();
        acItem.setRecordId(new BigDecimal(1));
        aclist.add(acItem);
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        aclist = new ArrayList<>();
        acItem.setCustomerPartType("123");
        acItem.setItemCode("123");
        aclist.add(acItem);
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        aclist = new ArrayList<>();
        acItem.setCustomerItemName("123");
        acItem.setItemCode("153");
        aclist.add(acItem);
        cpqdGbomDTOS=new ArrayList<>();
        when(zmsCommonService.queryGbomList(any())).thenReturn(cpqdGbomDTOS);
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        aclist = new ArrayList<>();
        acItem.setFixBomId("123");
        aclist.add(acItem);
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        aclist = new ArrayList<>();
        acItem.setBarCode("123");
        aclist.add(acItem);
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        SysLookupValues lookupValues =new SysLookupValues();
        lookupValues.setDescription("test appCode");
        when(zmsCommonService.getSysLookupValues(anyString())).thenReturn(lookupValues);
        ServiceData<String> serviceData = new ServiceData<>();
        serviceData.setBo("152345646512ACB");
        String result1 = JSONObject.toJSONString(serviceData);
        PowerMockito.when(HttpClientUtil.httpGet(any(),any(),any())).thenReturn(result1);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        cpqdGbomDTOS.add(cpqdGbomDTO1);
        cpqdGbomDTOS.add(cpqdGbomDTO2);
        when(zmsCommonService.queryGbomList(any())).thenReturn(cpqdGbomDTOS);
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        aclist.get(0).setItemCode("155555555");
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
        aclist.get(0).setItemCode("");
        when(zmsAlibabaRepository.queryMatIssueActualList(anyString())).thenReturn(aclist);
        try {
            zmsAlibabaService.queryAliMatIssueData(dto);
        }
        catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    /*Started by AICoder, pid:i9bfeb51e0m0a6d1494e081911f7534b8d257149*/

    @Test
    public void testGetZmsEntityByTaskNoList_EmptyInput() {
        List<String> emptyList = Collections.emptyList();

        List<ZmsCommonEntityDTO> result = zmsCommonService.getZmsEntityByTaskNoList(emptyList);

        assertTrue(result.isEmpty());
        verify(zmsCommonRepository, never()).getZmsEntityList(any());
    }

    @Test
    public void testGetZmsEntityByTaskNoList_SingleBatchWithEmptyResult() {
        List<String> taskNoList = Lists.newArrayList("T1", "T2");
        when(zmsCommonRepository.getZmsEntityList(any())).thenReturn(Collections.emptyList());

        List<ZmsCommonEntityDTO> result = zmsCommonService.getZmsEntityByTaskNoList(taskNoList);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetZmsEntityByTaskNoList_MultiBatchMixedResults() {
        List<String> largeList = Lists.newArrayList();
        for (int i = 0; i < 501; i++) {
            largeList.add("TASK-" + i);
        }

        when(zmsCommonRepository.getZmsEntityList(any()))
                .thenReturn(Lists.newArrayList(new ZmsCommonEntityDTO(), new ZmsCommonEntityDTO()));

        List<ZmsCommonEntityDTO> result = zmsCommonService.getZmsEntityByTaskNoList(largeList);

        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testGetZmsEntityByTaskNoList_BatchWithEmptyEntries() {
        List<String> taskNoList = Lists.newArrayList("T1", "T2", "T3");
        when(zmsCommonRepository.getZmsEntityList(any()))
                .thenReturn(null)
                .thenReturn(Collections.emptyList())
                .thenReturn(Lists.newArrayList(new ZmsCommonEntityDTO()));

        List<ZmsCommonEntityDTO> result = zmsCommonService.getZmsEntityByTaskNoList(taskNoList);

        Assert.assertEquals(0, result.size());
    }
    /*Ended by AICoder, pid:i9bfeb51e0m0a6d1494e081911f7534b8d257149*/
}
