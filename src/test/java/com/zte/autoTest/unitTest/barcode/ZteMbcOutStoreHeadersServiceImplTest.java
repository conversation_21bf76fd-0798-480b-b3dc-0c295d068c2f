package com.zte.autoTest.unitTest.barcode;
/* Started by AICoder, pid:5ea4429dcc90451c8f47166aae46e29b */

import com.zte.application.impl.barcode.ZteMbcOutStoreHeadersServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.barcode.ZteMbcOutStoreHeadersRepository;
import com.zte.interfaces.dto.api.*;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.CommonRedisUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.apache.catalina.filters.RemoteIpFilter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @date 2024-04-11 19:47
 */
@PrepareForTest({AopContext.class, BeanUtils.class,RequestHeadValidationUtil.class, SpringContextUtil.class})
public class ZteMbcOutStoreHeadersServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ZteMbcOutStoreHeadersServiceImpl zteMbcOutHeadersService;
    @Mock
    private CommonRedisUtil commonRedisUtil;
    @Mock
    private ZteMbcOutStoreHeadersRepository zteMbcOutStoreHeadersRepository;
    @Mock
    LocaleMessageSourceBean lmb;

    @Before
    public void init(){
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.mockStatic(BeanUtils.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(RequestHeadValidationUtil.validaEmpno()).thenReturn("123");
        PowerMockito.when(AopContext.currentProxy()).thenReturn(zteMbcOutHeadersService);
        PowerMockito.when(SpringContextUtil.getBean(BusinessConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);
    }

    @Test
    public void getNewBoxNoTest(){
        String s = "1";
        PowerMockito.when(zteMbcOutStoreHeadersRepository.getSerialNo()).thenReturn(s);
        List<ZteMbcBoxesAllDTO> list = new ArrayList<>();
        ZteMbcBoxesAllDTO item = new ZteMbcBoxesAllDTO();
        item.setBoxNo("10101");
        list.add(item);
        for(int i=0;i<=10000;i++){
            ZteMbcBoxesAllDTO item1 = new ZteMbcBoxesAllDTO();
            item1.setBoxNo("101"+StringUtils.leftPad(String.valueOf(i), Constant.INT_4,Constant.ZERO_STRING));
            list.add(item1);
        }
        zteMbcOutHeadersService.getNewBoxNo("101",list);
        zteMbcOutHeadersService.getNewBoxNo("123",list);
        Assert.assertNotNull(list);
    }

    @Test
    public void autoOutboundList(){
        List<ZteMbcOutStoreHeadersDTO> outboundList = new LinkedList<>();
        List<ImeiInfoDTO> imeiList = new LinkedList<>();
        List<ImeiInfoDTO> imeiList2 = new LinkedList<>();
        ZteMbcOutStoreHeadersDTO a1 = new ZteMbcOutStoreHeadersDTO();
        a1.setChooseNo("123");
        a1.setImeiList(imeiList);
        outboundList.add(a1);
        ZteMbcOutStoreHeadersDTO a2 = new ZteMbcOutStoreHeadersDTO();
        a2.setChooseNo("123");
        a2.setImeiList(imeiList2);
        outboundList.add(a2);
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(com.zte.domain.model.MessageId.CUSTOMIZE_MSG.equals(e.getMessage()));
        }

        a1.setChooseNo("234");
        ImeiInfoDTO b1 = new ImeiInfoDTO();
        b1.setImei("123");
        b1.setItemCode("123");
        imeiList.add(b1);
        ImeiInfoDTO b2 = new ImeiInfoDTO();
        b2.setImei("123");
        b2.setItemCode("234");
        imeiList2.add(b2);
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(com.zte.domain.model.MessageId.CUSTOMIZE_MSG.equals(e.getMessage()));
        }

        b2.setImei("234");
        List<String> existChooseList = new LinkedList<>();
        existChooseList.add("123");
        PowerMockito.when(zteMbcOutStoreHeadersRepository.queryZteMbcOutStoreHeadersBatch(Mockito.anyList()))
                .thenReturn(existChooseList);
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CHOOSE_NO_EXIST.equals(e.getMessage()));
        }

        PowerMockito.when(zteMbcOutStoreHeadersRepository.queryZteMbcOutStoreHeadersBatch(Mockito.anyList()))
                .thenReturn(null);
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.IMEI_BOX_NOT_FIND.equals(e.getMessage()));
        }

        List<ZteMbcNewboxInfoDTO> zteMbcList = new LinkedList<>();
        ZteMbcNewboxInfoDTO c1 = new ZteMbcNewboxInfoDTO();
        c1.setImei("123");
        c1.setBoxNo("123");
        zteMbcList.add(c1);
        PowerMockito.when(zteMbcOutStoreHeadersRepository.queryZteMbcNewBoxBatch(Mockito.anyList()))
                .thenReturn(zteMbcList);
        List<ZteMbcNewboxInfoDTO> reList = new LinkedList<>();
        ZteMbcNewboxInfoDTO c2 = new ZteMbcNewboxInfoDTO();
        c2.setImei("234");
        c2.setBoxnum("234");
        c2.setBoxNo("234");
        reList.add(c2);
        PowerMockito.when(zteMbcOutStoreHeadersRepository.queryZteMbcStoreBatch(Mockito.anyList()))
                .thenReturn(reList);
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BOX_INFO_NOT_FIND.equals(e.getMessage()));
        }

        List<ZteMbcBoxesAllDTO> boxNoList = new LinkedList<>();
        ZteMbcBoxesAllDTO d1 = new ZteMbcBoxesAllDTO();
        d1.setBoxNo("123");
        boxNoList.add(d1);
        ZteMbcBoxesAllDTO d2 = new ZteMbcBoxesAllDTO();
        d2.setBoxNo("234");
        boxNoList.add(d2);
        PowerMockito.when(zteMbcOutStoreHeadersRepository.queryBoxNoInfoBatch(Mockito.anyList()))
                .thenReturn(boxNoList);
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BOX_SUBINVENTORY_NOT_FIND.equals(e.getMessage()));
        }

        d1.setSubinventory("123");
        d2.setSubinventory("234");
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.BOX_WAREHOUSE_NOT_IN.equals(e.getMessage()));
        }

        d2.setBoxStatus("在库");
        d1.setBoxStatus("在库");
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(com.zte.domain.model.MessageId.CUSTOMIZE_MSG.equals(e.getMessage()));
        }

        d1.setItemCode("123");
        d2.setItemCode("234");
        try {
            zteMbcOutHeadersService.autoOutboundList(outboundList);
        } catch (Exception e) {
            Assert.assertTrue(com.zte.domain.model.MessageId.CUSTOMIZE_MSG.equals(e.getMessage()));
        }

        b1.setSubinventory("123");
        b2.setSubinventory("234");
        zteMbcOutHeadersService.autoOutboundList(outboundList);
    }

    /*Started by AICoder, pid:6ae95k7f43j5050142e9097c805bb567f742d311*/
    @Test
    public void testReCreateBoxNo_EmptyList() {
        List<ZteMbcBoxesAllDTO> zteMbcBoxesAllList = new LinkedList<>();
        ZteMbcBoxesAllDTO dd1 = new ZteMbcBoxesAllDTO();
        dd1.setBoxNo("821016060416110055");
        zteMbcBoxesAllList.add(dd1);
        ZteMbcBoxesAllDTO dd2 = new ZteMbcBoxesAllDTO();
        dd2.setBoxNo("821016060416110056");
        zteMbcBoxesAllList.add(dd2);
        List<ZteMbcBoxesAllDTO> insertBoxList = new ArrayList<>();
        ZteMbcBoxesAllDTO d3 = new ZteMbcBoxesAllDTO();
        d3.setBoxNo("82101606041611005609");
        insertBoxList.add(d3);
        ZteMbcBoxesAllDTO d4 = new ZteMbcBoxesAllDTO();
        d4.setBoxNo("82101606041611005608");
        insertBoxList.add(d4);
        ZteMbcBoxesAllDTO d5 = new ZteMbcBoxesAllDTO();
        d5.setBoxNo("82101606041611005610");
        insertBoxList.add(d5);
        List<String> existList = new LinkedList<>();
        for (int i = 0;i<100;i++){
            existList.add("821016060416110056"+ StringUtils.leftPad(String.valueOf(i), Constant.INT_2,Constant.ZERO_STRING));
            existList.add("821016060416110057"+ StringUtils.leftPad(String.valueOf(i), Constant.INT_2,Constant.ZERO_STRING));
        }
        for (int i = 0;i<10000;i++){
            existList.add("821016060416110056"+ StringUtils.leftPad(String.valueOf(i), Constant.INT_4,Constant.ZERO_STRING));
        }
        existList.add("55555555");
        PowerMockito.when(zteMbcOutStoreHeadersRepository.queryBoxNoBatch(Mockito.any()))
                .thenReturn(existList);
        List< ZteMbcStoreinDTO > storeInUpdateList = new LinkedList<>();
        List<ZteMbcStoreinDTO> newBoxUpdateList = new LinkedList<>();
        List<ZteMbcOutStoreLinesDTO> insertLinesList = new LinkedList<>();
        ZteMbcStoreinDTO updateDto =new ZteMbcStoreinDTO();
        updateDto.setBoxNo("82101606041611005609");
        storeInUpdateList.add(updateDto);
        updateDto.setBoxNo("82101606041611005608");
        storeInUpdateList.add(updateDto);
        updateDto.setBoxNo("82101606041611005610");
        storeInUpdateList.add(updateDto);
        ZteMbcStoreinDTO boxUpdateDto = new ZteMbcStoreinDTO();
        boxUpdateDto.setBoxNo("82101606041611005609");
        newBoxUpdateList.add(boxUpdateDto);
        boxUpdateDto.setBoxNo("82101606041611005608");
        newBoxUpdateList.add(boxUpdateDto);
        boxUpdateDto.setBoxNo("82101606041611005610");
        newBoxUpdateList.add(boxUpdateDto);
        ZteMbcOutStoreLinesDTO lineDTO = new ZteMbcOutStoreLinesDTO();
        lineDTO.setBoxNo("82101606041611005609");
        insertLinesList.add(lineDTO);
        lineDTO.setBoxNo("82101606041611005608");
        insertLinesList.add(lineDTO);
        lineDTO.setBoxNo("82101606041611005610");
        insertLinesList.add(lineDTO);
        List<ZteMbcBoxesAllDTO> result = zteMbcOutHeadersService.reCreateBoxNo(zteMbcBoxesAllList, insertBoxList, storeInUpdateList,  newBoxUpdateList,  insertLinesList);
        assertTrue(CollectionUtils.isNotEmpty(result));
        insertLinesList.clear();
        lineDTO.setBoxNo("82101606041611005610");
        insertLinesList.add(lineDTO);
        result = zteMbcOutHeadersService.reCreateBoxNo(zteMbcBoxesAllList, insertBoxList, storeInUpdateList,  newBoxUpdateList,  insertLinesList);
        assertTrue(CollectionUtils.isNotEmpty(result));

    }
    /*Ended by AICoder, pid:6ae95k7f43j5050142e9097c805bb567f742d311*/
}
/* Ended by AICoder, pid:5ea4429dcc90451c8f47166aae46e29b */