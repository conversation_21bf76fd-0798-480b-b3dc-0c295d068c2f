package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.PkgBatchScanService;
import com.zte.application.datawb.impl.PkgBatchScanServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.MESContainerContentInfoDTO;
import com.zte.interfaces.dto.MESSJBoxDetailDTO;
import com.zte.interfaces.dto.PkgBatchScanDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2024/11/27 13:46
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class, HttpRemoteUtil.class})
public class PkgBatchScanServiceImplNewTest {

    @InjectMocks
    private PkgBatchScanServiceImpl pkgBatchScanServiceImpl;
    @Mock
    private PkgBatchScanService pkgBatchScanService;
    @Test
    public void packingByTrayCode() throws Exception {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, HttpRemoteUtil.class);
        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        try {
            pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TRAY_CODE_IS_NULL, e.getMessage());
        }
        pkgBatchScanDTO.setTrayCode("test");
        pkgBatchScanDTO.setFactoryId("52");
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("appCode");
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127002));
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("url");
        sysLookupValues1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127001));
        sysLookupValuesList.add(sysLookupValues);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupMeaning("test");
        sysLookupValues2.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_7192003));
        sysLookupValuesList.add(sysLookupValues2);
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(new ArrayList<>());
        try {
            pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(sysLookupValuesList);
        try {
            pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValuesList.clear();
        sysLookupValuesList.add(sysLookupValues1);
        try {
            pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(null);
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        try {
            pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TRAY_CODE_MES_INFO_IS_NULL, e.getMessage());
        }
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo("");
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        try {
            pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TRAY_CODE_MES_INFO_IS_NULL, e.getMessage());
        }
        List<MESContainerContentInfoDTO> list = new ArrayList() {{
            add(new MESContainerContentInfoDTO() {{
                setPalletNo("test11");
                setCartonSn("test11");
                setEn("test11");
            }});
        }};
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(list);
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        pkgBatchScanServiceImpl.packingByTrayCode(pkgBatchScanDTO);
    }

    @Test
    public void getMESBoxInfo() throws Exception {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, HttpRemoteUtil.class);
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("appCode");
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127002));
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("url");
        sysLookupValues1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127001));
        sysLookupValuesList.add(sysLookupValues);
        sysLookupValuesList.add(sysLookupValues1);
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(sysLookupValuesList);
        try {
            Whitebox.invokeMethod(pkgBatchScanServiceImpl, "getMESBoxInfo", new PkgBatchScanDTO(), "0");
        } catch (Exception e) {
            Assert.assertEquals("customize.msg", e.getMessage());
        }
        try {
            Whitebox.invokeMethod(pkgBatchScanServiceImpl, "getMESBoxInfo", new PkgBatchScanDTO(), "1");
        } catch (Exception e) {
            Assert.assertEquals("customize.msg", e.getMessage());
        }
    }

    @Test
    public void packingByOuterBox() throws Exception {
        try {
            pkgBatchScanServiceImpl.packingByOuterBox(new PkgBatchScanDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OUTER_BOX_LIST_IS_NULL, e.getMessage());
        }

        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        pkgBatchScanDTO.setFactoryId("52");
        pkgBatchScanDTO.setObjectList(new ArrayList() {{
            add("test");
        }});
        PowerMockito.mockStatic(CenterfactoryRemoteService.class, HttpRemoteUtil.class);
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("appCode");
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127002));
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("url");
        sysLookupValues1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127001));
        sysLookupValuesList.add(sysLookupValues);
        sysLookupValuesList.add(sysLookupValues1);
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(sysLookupValuesList);
        List<MESContainerContentInfoDTO> list = new ArrayList() {{
            add(new MESContainerContentInfoDTO() {{
                setPalletNo("test11");
                setCartonSn("test11");
                setEn("test11");
            }});
        }};
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(list);
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        pkgBatchScanServiceImpl.packingByOuterBox(pkgBatchScanDTO);
    }

    @Test
    public void packingByEnCode() throws Exception {
        try {
            pkgBatchScanServiceImpl.packingByEnCode(new PkgBatchScanDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EN_CODE_LIST_IS_NULL, e.getMessage());
        }
        pkgBatchScanServiceImpl.packingByEnCode(new PkgBatchScanDTO() {{
            setObjectList(new ArrayList() {{
                add("test");
            }});
        }});
    }

    @Test
    public void packingByCloudPAD() throws Exception {
        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        pkgBatchScanDTO.setFactoryId("52");
        pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);

        PowerMockito.mockStatic(CenterfactoryRemoteService.class, HttpRemoteUtil.class);
        pkgBatchScanDTO.setBigBoxNum("Box01");
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(new ArrayList<>());
        try {
            pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("appCode");
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127002));
        sysLookupValuesList.add(sysLookupValues);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("url");
        sysLookupValues1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_241127001));
        sysLookupValuesList.add(sysLookupValues1);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupMeaning("test");
        sysLookupValues2.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_7192003));
        sysLookupValuesList.add(sysLookupValues2);
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(sysLookupValuesList);
        try {
            pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_250320001));
        try {
            pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        sysLookupValues1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_250320002));
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(null);
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        try {
            pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MES_PACKING_INFO_IS_NULL, e.getMessage());
        }

        List<MESSJBoxDetailDTO> list = new ArrayList() {{
            add(new MESSJBoxDetailDTO() {{
                setMsn("test11");
            }});
        }};
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(list);
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        pkgBatchScanDTO.setPackingType(Constant.INT_6);
        pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);

        pkgBatchScanDTO.setBigBoxNum("");
        pkgBatchScanDTO.setObjectList(new ArrayList() {{
            add("test");
        }});
        pkgBatchScanServiceImpl.packingByCloudPAD(pkgBatchScanDTO);
    }

    @Test
    public void getContainerContentInfo() throws Exception {
        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        pkgBatchScanDTO.setBoxInfoList(new ArrayList() {{
            add(new ContainerContentInfoDTO() {{
                setItemCode("11");
            }});
        }});
        Assert.assertNotNull(pkgBatchScanDTO);
        pkgBatchScanDTO.setPackingType(Constant.INT_6);
        Whitebox.invokeMethod(pkgBatchScanServiceImpl, "getContainerContentInfo", pkgBatchScanDTO, new ArrayList<>());
        pkgBatchScanDTO.setPackingType(Constant.INT_7);
        Whitebox.invokeMethod(pkgBatchScanServiceImpl, "getContainerContentInfo", pkgBatchScanDTO, new ArrayList<>());
    }
}
