package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.WmsSelectPDVMDateServiceImpl;
import com.zte.domain.model.datawb.WmsSelectPDVMDateRepository;
import com.zte.interfaces.dto.WmsEntitySizeDTO;
import com.zte.interfaces.dto.WmsSelectPDVMDateDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;
/**
 * <AUTHOR>
 * @since 2024年3月18日
 */


@RunWith(MockitoJUnitRunner.class)
public class WmsSelectPDVMDateServiceImplTest {

    @InjectMocks
    private WmsSelectPDVMDateServiceImpl wmsSelectPDVMDateService;

    @Mock
    private WmsSelectPDVMDateRepository wmsSelectPDVMDateRepository;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test(expected = MesBusinessException.class)
    public void testSelectPDVMDate_EntitySiteCodeListIsEmpty() throws Exception {
        WmsEntitySizeDTO wmsEntitySizeDTO = new WmsEntitySizeDTO();
        wmsEntitySizeDTO.setEntitySiteList(new ArrayList<>());
        List<WmsSelectPDVMDateDTO> d1 = wmsSelectPDVMDateService.selectPDVMDate(wmsEntitySizeDTO);
        assertEquals(d1.size(),0);
    }

    @Test(expected = MesBusinessException.class)
    public void testSelectPDVMDate_EntitySiteCodeListSizeMoreThan10() throws Exception {
        WmsEntitySizeDTO wmsEntitySizeDTO = new WmsEntitySizeDTO();
        List<WmsSelectPDVMDateDTO> entitySiteCodeList = new ArrayList<>();
        for (int i = 0; i < 11; i++) {
            entitySiteCodeList.add(new WmsSelectPDVMDateDTO());
        }
        wmsEntitySizeDTO.setEntitySiteList(entitySiteCodeList);
        List<WmsSelectPDVMDateDTO> w1 = wmsSelectPDVMDateService.selectPDVMDate(wmsEntitySizeDTO);
        assertEquals(w1.size(),0);
    }

    @Test
    public void testSelectPDVMDate_NormalCase() throws Exception {
        WmsEntitySizeDTO wmsEntitySizeDTO = new WmsEntitySizeDTO();
        List<WmsSelectPDVMDateDTO> entitySiteCodeList = new ArrayList<>();
        WmsSelectPDVMDateDTO w2 = new WmsSelectPDVMDateDTO();
        entitySiteCodeList.add(w2);
        wmsEntitySizeDTO.setEntitySiteList(entitySiteCodeList);
        List<WmsSelectPDVMDateDTO> expectedResult = new ArrayList<>();
        expectedResult.add(new WmsSelectPDVMDateDTO());
        when(wmsSelectPDVMDateRepository.getWmsPDVMDate(wmsEntitySizeDTO)).thenReturn(expectedResult);
        List<WmsSelectPDVMDateDTO> actualResult = wmsSelectPDVMDateService.selectPDVMDate(wmsEntitySizeDTO);
        assertEquals(expectedResult, actualResult);
    }
}

