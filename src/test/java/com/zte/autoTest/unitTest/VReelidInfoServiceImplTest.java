package com.zte.autoTest.unitTest;

import com.zte.application.datawb.VReelidInfoService;
import com.zte.application.datawb.impl.VReelidInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.VReelidInfo;
import com.zte.domain.model.datawb.VReelidInfoRepository;
import com.zte.domain.model.datawb.Ztebarcode;
import com.zte.domain.model.datawb.ZtebarcodeRepository;
import com.zte.interfaces.dto.VReelidInfoDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class VReelidInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    VReelidInfoServiceImpl service;
    @Mock
    VReelidInfoRepository vReelidInfoRepository;
    @Mock
    ZtebarcodeRepository ztebarcodeRepository;

    @Test
    public void getLateBindList() {
        VReelidInfoDTO dto = new VReelidInfoDTO();
        dto.setLottable02("123");
        List<VReelidInfo> list = new ArrayList<>();
        VReelidInfo vReelidInfo = new VReelidInfo();
        vReelidInfo.setLottable02("123");
        list.add(vReelidInfo);
        List<Ztebarcode> ztebarcodes = new ArrayList<>();
        Ztebarcode ztebarcode = new Ztebarcode();
        ztebarcode.setItemBarcode("123");
        ztebarcode.setSupplierNo("qwe");
        ztebarcodes.add(ztebarcode);
        PowerMockito.when(vReelidInfoRepository.getList(anyObject())).thenReturn(list);
        PowerMockito.when(ztebarcodeRepository.selectZtebarcodeByIds(anyList())).thenReturn(ztebarcodes);
        Assert.assertNotNull(service.getLateBindList(dto));
    }
}