package com.zte.autoTest.unitTest;

import com.zte.application.infordt.impl.EdiOrdersStatusViewServiceImpl;
import com.zte.domain.model.infordt.EdiOrderStatusRepository;
import com.zte.domain.model.infordt.EdiOrderStatusViewDTO;
import com.zte.util.PowerBaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/31
 **/
public class EdiOrdersStatusViewServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private EdiOrdersStatusViewServiceImpl ediOrdersStatusViewServiceImpl;
    @Mock
    private EdiOrderStatusRepository ediOrderStatusRepository;

    @Before
    public void init(){

    }

    @Test
    public void queryListByCondition() {
        EdiOrderStatusViewDTO dto = new EdiOrderStatusViewDTO();
        dto.setExternorderkey("123");
        dto.setExternalorderkey2("123");
        dto.setStatus("2");
        dto.setSku("123");
        dto.setWhseId("123");
        dto.setDetailstauts("123");
        ediOrdersStatusViewServiceImpl.queryListByCondition(dto);

        dto.setExternalorderkey2List(new LinkedList<String>(){{add("123");}});
        Assert.assertNotNull(ediOrdersStatusViewServiceImpl.queryListByCondition(dto));
    }

    @Test
    public  void queryListPageByCondition(){
        EdiOrderStatusViewDTO dto  = new EdiOrderStatusViewDTO();
        Assert.assertNotNull(ediOrdersStatusViewServiceImpl.queryListPageByCondition(dto));
    }

}