package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.RecalculationConvergenceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.RecalculationConvergenceRepository;
import com.zte.interfaces.dto.RecalculationConvergenceParamterDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class RecalculationConvergenceImplTest extends BaseTestCase {

    //@InjectMocks
    //private PDVMBarCodeService pdvmBarCodeService;
    @InjectMocks
    private RecalculationConvergenceImpl recalculationConvergenceImpl;

    @Mock
    RecalculationConvergenceRepository recalculationConvergenceRepository;

    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;

    @Test
    public void recalculationConvergence() {
        try {
            PowerMockito.mockStatic(CommonUtils.class);

            //when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            RecalculationConvergenceParamterDTO params = new RecalculationConvergenceParamterDTO();
            //params.setJobNO("10277404");
            List<String> billNumbers= new ArrayList<String>();
            billNumbers.add("C221000069762");
            billNumbers.add("C221000069763");
            params.setBillNumbers(billNumbers);
            recalculationConvergenceImpl.recalculationConvergence(params);
            recalculationConvergenceImpl.inBoxlistDetailsCkLog(params);

            //params.setJobNO("10277404888");
            billNumbers= new ArrayList<String>();
            billNumbers.add("C221000069762");
            billNumbers.add("C221000069763");
            params.setBillNumbers(billNumbers);
            recalculationConvergenceImpl.recalculationConvergence(params);

            //params.setJobNO("10277404888");
            billNumbers= new ArrayList<String>();
            billNumbers.add("C221000069762000000");
            billNumbers.add("C221000069763");
            params.setBillNumbers(billNumbers);
            recalculationConvergenceImpl.recalculationConvergence(params);

            //params.setJobNO("10277404");
            billNumbers= new ArrayList<String>();
            billNumbers.add("C221000069762");
            billNumbers.add("C221000069763");
            params.setBillNumbers(billNumbers);
            recalculationConvergenceImpl.recalculationConvergence(params);

        } catch (Exception ex) {
            Assert.assertEquals(MessageId.BOXUP_AT_LEAST_ONE_IS_EXISTS, ex.getMessage());
        }
    }
}
