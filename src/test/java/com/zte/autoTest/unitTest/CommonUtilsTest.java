package com.zte.autoTest.unitTest;

import com.zte.common.CommonUtils;
import com.zte.domain.model.BaItem;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-10-25
 */
@RunWith(PowerMockRunner.class)
public class CommonUtilsTest extends BaseTestCase {

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void isEmpty() {
        Map<String, String> map = new HashMap<>();
        map.put("1", "test");
        List<String> list = new ArrayList<>();
        list.add("111");
        Assert.assertTrue(CommonUtils.isEmpty(null));
        Assert.assertTrue(CommonUtils.isEmpty(""));
        Assert.assertFalse(CommonUtils.isEmpty("123"));
        Assert.assertFalse(CommonUtils.isEmpty(map));
        Assert.assertFalse(CommonUtils.isEmpty(list));
    }

    @Test
    public void isNotEmpty() {
        Map<String, String> map = new HashMap<>();
        map.put("1", "test");
        List<String> list = new ArrayList<>();
        list.add("111");
        Assert.assertFalse(CommonUtils.isNotEmpty(null));
        Assert.assertFalse(CommonUtils.isNotEmpty(""));
        Assert.assertTrue(CommonUtils.isNotEmpty("123"));
        Assert.assertTrue(CommonUtils.isNotEmpty(map));
        Assert.assertTrue(CommonUtils.isNotEmpty(list));
    }

    @Test
    public void copyProperties() {
        BaItem fromObj = new BaItem();
        fromObj.setItemNo("123");
        BaItem toObj = new BaItem();
        CommonUtils.copyProperties(fromObj, toObj);
        Assert.assertEquals(toObj.getItemNo(), fromObj.getItemNo());
    }
}
