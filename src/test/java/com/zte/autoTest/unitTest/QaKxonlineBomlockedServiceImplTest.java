package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BaProdPlanServiceImpl;
import com.zte.application.impl.QaKxonlineBomlockedServiceImpl;
import com.zte.domain.model.BaProdPlanRepository;
import com.zte.domain.model.QaKxonlineBomlockedRepository;
import com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class QaKxonlineBomlockedServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    QaKxonlineBomlockedServiceImpl service;

    @Mock
    QaKxonlineBomlockedRepository repository;

    @Test
    public void pageList()throws Exception {
        Assert.assertNotNull(service.pageList(new QaKxonlineBomlockedEntityDTO()));
    }
}