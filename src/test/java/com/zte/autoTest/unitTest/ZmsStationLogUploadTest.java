package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.datawb.ZmsOverallUnitService;
import com.zte.application.datawb.ZmsStationLogUploadService;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;


/**
 * <AUTHOR>
 * @since 2023年5月25日
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class, CommonUtils.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsStationLogUploadTest {

    @InjectMocks
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;

    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;

    @Mock
    private DataServiceClientV1 dataServiceClientV1;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Mock
    private ZmsOverallUnitService zmsOverallUnitService;
    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class);
    }

    @Test
    public void stationLogUpload() throws Exception {
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);
        String zsUrl = "https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/queryStationLogInfo";

        String sn1 = "888888882028";
        String sn2 = "222";
        List<String> snList = new ArrayList<>();
        snList.add(sn1);
        snList.add(sn2);
        List<StationSnDataDTO> snData1 = new ArrayList<>();
        List<StationSnDataDTO> snData = new ArrayList<>();
        StationSnDataDTO dto1 = new StationSnDataDTO();
        dto1.setServerSn("1");
        dto1.setOrderId("2");
        dto1.setFactoryOrderId("3");
        dto1.setEndUser("4");
        snData.add(dto1);
        StationInputDTO stationInputDTO1 = new StationInputDTO();
        stationInputDTO1.setAutoFlag("N");
        stationInputDTO1.setSnList(snList);
        StationInputDTO stationInputDTO2 = new StationInputDTO();
        stationInputDTO2.setAutoFlag("Y");
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("partcode", "111");
        String strUrl = "https://mdsdev.zte.com.cn:29001/api/oauth2/v1/usercred/access_token";
        String strUser = "dt_byte";
        String strPass = "8*9&aUkzklnm";
        PowerMockito.when(zmsStationLogUploadRepository.getStationLogSn(stationInputDTO1)).thenReturn(snData1);
        zmsStationLogUploadService.stationLogUpload(stationInputDTO1);
        PowerMockito.when(zmsStationLogUploadRepository.getStationId("4343")).thenReturn("543534");
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("123");
        PowerMockito.when(zmsStationLogUploadRepository.getStationLogSn(stationInputDTO1)).thenReturn(snData);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("kk");
        PowerMockito.when(json.get(Constant.JSON_CODE)).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(json.get(Constant.JSON_BO)).thenReturn(json);
        List<StationLogDTO> stationLogDTOS1 = new ArrayList<>();
        List<StationLogDTO> stationLogDTOS = new ArrayList<>();
        StationLogDTO a1 = new StationLogDTO();
        stationLogDTOS.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(stationLogDTOS);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        try {
            zmsStationLogUploadService.snListValid(stationInputDTO1);
            zmsStationLogUploadService.snListValid(stationInputDTO2);
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("OK");

            zmsStationLogUploadService.stationLogUpload(stationInputDTO1);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(stationLogDTOS1);
            PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                    .thenReturn(null);
            Whitebox.invokeMethod(zmsStationLogUploadService, "getZsStationLog", paramMap, "111", "111");
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertStationLog", stationLogDTOS);
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertImesLogData", stationLogDTOS, dto1, "SYSTEM");
            Whitebox.invokeMethod(zmsStationLogUploadService, "stationLogUpload", stationInputDTO1);
            Assert.assertTrue(Objects.nonNull(stationInputDTO1));
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void getToken() throws Exception {
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("123");
        String test = zmsStationLogUploadService.getToken();
        Assert.assertTrue(Objects.nonNull(test));
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("");
        try {
            zmsStationLogUploadService.getToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TOKEN_VALIDATE, e.getMessage());
        }
    }

    @Test
    public void getZsStationLog() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("partcode", "111");
        String params = JacksonJsonConverUtil.beanToJson(paramMap);
        List<StationLogDTO> stationList = new ArrayList<>();
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        headerParamsMap.put("Z-ACCESS-TOKEN", "222");
        String zsUrl = "https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/queryStationLogInfo";
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(result);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        String test = zmsStationLogUploadService.getZsStationLog(paramMap, zsUrl, "222");
        Assert.assertTrue(Objects.nonNull(test));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn(null);
        Assert.assertNull(zmsStationLogUploadService.getZsStationLog(paramMap, zsUrl, "222"));
    }

    @Test
    public void insertStationLog() {
        List<StationLogDTO> dtos = new ArrayList<>();
        StationLogDTO dto = new StationLogDTO();
        dto.setStationId("111");
        dto.setStationLog("222");
        dto.setFailLog("333");
        dto.setServerSn("444");
        dtos.add(dto);
        List<StationLogDTO> dtos1 = new ArrayList<>();
        doNothing().when(zmsStationLogUploadRepository).insertStationLog(dtos);
        zmsStationLogUploadService.insertStationLog(dtos);
        zmsStationLogUploadService.insertStationLog(dtos1);
        Assert.assertTrue(Objects.nonNull(dto));
        try {
            Mockito.doThrow(new NullPointerException()).when(zmsStationLogUploadRepository).insertStationLog(Mockito.any());
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertStationLog", dtos);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(MessageId.GET_ZSLOG_STATION_ERROR, e.getMessage());
        }
    }

    @Test
    public void insertImesLogData() throws Exception {
        List<List<StationLogDTO>> listOfList = new ArrayList<>();
        List<StationLogDTO> dtos = new ArrayList<>();
        StationLogDTO dto = new StationLogDTO();
        dto.setStationId("111");
        dto.setStationLog("222");
        dto.setFailLog("333");
        dto.setServerSn("444");
        dtos.add(dto);
        StationLogDTO dto22 = new StationLogDTO();
        dto.setStationId("111");
        dto22.setStationLog("222");
        dto22.setFailLog("333");
        dto22.setServerSn("444");
        dtos.add(dto22);
        listOfList.add(dtos);
        List<StationLogDTO> dtos1 = new ArrayList<>();
        StationSnDataDTO dto1 = new StationSnDataDTO();
        dto1.setServerSn("1");
        dto1.setOrderId("2");
        dto1.setFactoryOrderId("3");
        dto1.setEndUser("4");
        String stationName = "";
        String stationName1 = "232";

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO cu = new CustomerDataLogDTO();
        cu.setStatus("PN");
        dataList.add(cu);
        String empNo = "000";
        String empNo1 = "";

        PowerMockito.when(zmsStationLogUploadRepository.getStationId(dto.getStationId())).thenReturn(stationName1);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-datawbsys/PS/getFactoryIdByProdplanId");

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("123");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        zmsStationLogUploadService.insertImesLogData(dtos, dto1, empNo);
        PowerMockito.when(zmsStationLogUploadRepository.getStationId(dto.getStationId())).thenReturn(stationName);
        zmsStationLogUploadService.insertImesLogData(dtos1, dto1, empNo1);
        Assert.assertTrue(Objects.nonNull(dtos));
        Assert.assertNotNull(dtos);
    }

    @Test
    public void insertLogToImes() throws RouteException {
        PowerMockito.mockStatic(HttpClientUtil.class);
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO cu = new CustomerDataLogDTO();
        cu.setStatus("PN");
        dataList.add(cu);
        Exception ex = new Exception("日志接口异常");
        StationSnDataDTO dto1 = new StationSnDataDTO();
        dto1.setServerSn("1");
        dto1.setOrderId("2");
        dto1.setFactoryOrderId("3");
        dto1.setEndUser("4");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(result);
        PowerMockito.when(constantInterface.getUrl(InterfaceEnum.pushLogToImes)).thenReturn("http://imes.dev.zte.com.cn/zte-mes-manufactureshare-centerfactory/customerDataLog/pushDataToB2B");
        zmsStationLogUploadService.insertLogToImes(dataList, dto1, ex);
        Assert.assertTrue(Objects.nonNull(cu));
        try {
            PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenThrow(new NullPointerException());
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertLogToImes", dataList, dto1, ex);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.IMPUT_PARAM_IS_NULL, e.getMessage());
        }
        Assert.assertNotNull(dataList);
    }

    /*Started by AICoder, pid:p4afdvd1bam217114bdc0b1180dc8552f03020b6*/
    @Test
    public void meiTuanFileLogUpload() throws Exception {
        PowerMockito.when(DataServiceClientV1.getInstance()).thenReturn(dataServiceClientV1);

        String sn1 = "888888882028";
        String sn2 = "222";
        List<String> snList = new ArrayList<>();
        snList.add(sn1);
        snList.add(sn2);
        List<StationSnDataDTO> snData1 = new ArrayList<>();
        List<StationSnDataDTO> snData = new ArrayList<>();
        StationSnDataDTO dto1 = new StationSnDataDTO();
        dto1.setServerSn("1");
        dto1.setOrderId("2");
        dto1.setFactoryOrderId("3");
        dto1.setEndUser("4");
        dto1.setRequestId("ZTE6");
        dto1.setStationName("MTHC");
        dto1.setResult("FAIL");
        StationInputDTO stationInputDTO = new StationInputDTO();
        stationInputDTO.setEmpNo("test");
        snData.add(dto1);
        StationSnDataDTO dto2 = new StationSnDataDTO();
        dto2.setServerSn("11");
        dto2.setOrderId("22");
        dto2.setFactoryOrderId("33");
        dto2.setEndUser("44");
        dto2.setRequestId("66");
        dto2.setStationName("MTHC");
        dto2.setResult("PASS");
        snData.add(dto2);
        stationInputDTO.setAutoFlag("N");
        stationInputDTO.setSnList(snList);
        StationInputDTO stationInputDTO2 = new StationInputDTO();
        stationInputDTO2.setAutoFlag("Y");
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("partcode", "111");
        List<StationLogDTO> slDTO = new LinkedList<>();
        StationLogDTO sldto1 = new StationLogDTO();
        sldto1.setStationId("6");
        sldto1.setServerSn("1");
        sldto1.setLogName("1");
        slDTO.add(sldto1);

        /*Started by AICoder, pid:ua772jcbfbkb6c314b5308a68039375975d209ce*/
        StationSnDataDTO dto3 = new StationSnDataDTO();
        dto3.setServerSn("11");
        dto3.setOrderId("22");
        dto3.setFactoryOrderId("33");
        dto3.setEndUser("44");
        dto3.setRequestId("66");
        dto3.setStationName("MTHC1");
        dto3.setResult("FAIL");
        snData.add(dto3);
        StationSnDataDTO dto4 = new StationSnDataDTO();
        dto4.setServerSn("11");
        dto4.setOrderId("22");
        dto4.setFactoryOrderId("33");
        dto4.setEndUser("44");
        dto4.setRequestId("66");
        dto4.setStationName("MTHC1");
        dto4.setResult("PASS");
        snData.add(dto4);
        /*Ended by AICoder, pid:ua772jcbfbkb6c314b5308a68039375975d209ce*/

        PowerMockito.when(zmsStationLogUploadRepository.getFileLogSn(stationInputDTO)).thenReturn(snData1);
        zmsStationLogUploadService.meiTuanFileLogUpload(stationInputDTO);
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("443");
        PowerMockito.when(zmsStationLogUploadRepository.getFileLogSn(stationInputDTO)).thenReturn(snData);
        PowerMockito.when(zmsStationLogUploadRepository.queryUploadFileLogSuccessList(slDTO)).thenReturn(slDTO);
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("11111");
        zmsStationLogUploadService.meiTuanFileLogUpload(stationInputDTO);
        PowerMockito.when(zmsStationLogUploadRepository.getStationId("4343")).thenReturn("543534");
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("123");
        PowerMockito.when(zmsStationLogUploadRepository.getFileLogSn(stationInputDTO)).thenReturn(snData);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("kk");
        PowerMockito.when(json.get(Constant.JSON_CODE)).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(json.get(Constant.JSON_BO)).thenReturn(json);
        List<StationLogDTO> stationLogDTOS1 = new ArrayList<>();
        List<StationLogDTO> stationLogDTOS = new ArrayList<>();
        StationLogDTO a1 = new StationLogDTO();
        a1.setStationId("1");
        a1.setServerSn("1");
        a1.setLogName("1");
        stationLogDTOS.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(stationLogDTOS);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        try {
            zmsStationLogUploadService.snListValid(stationInputDTO);
            zmsStationLogUploadService.snListValid(stationInputDTO2);
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("OK");

            zmsStationLogUploadService.meiTuanFileLogUpload(stationInputDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            Mockito.doThrow(new NullPointerException()).when(zmsStationLogUploadRepository).insertStationLog(Mockito.any());
            zmsStationLogUploadService.meiTuanFileLogUpload(stationInputDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(stationLogDTOS1);
            PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                    .thenReturn(null);
            Whitebox.invokeMethod(zmsStationLogUploadService, "getZsStationLog", paramMap, "111", "111");
            Whitebox.invokeMethod(zmsStationLogUploadService, "getInsertStationList", stationLogDTOS);
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertStationLog", stationLogDTOS);
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertImesFileLogData", stationLogDTOS, dto1, "SYSTEM");
            Whitebox.invokeMethod(zmsStationLogUploadService, "meiTuanFileLogUpload", stationInputDTO);
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertImesFileLogData", stationLogDTOS, dto1, "");
            Whitebox.invokeMethod(zmsStationLogUploadService, "meiTuanFileLogUpload", stationInputDTO);
            Whitebox.invokeMethod(zmsStationLogUploadService, "insertImesFileLogData", null, dto1, "SYSTEM");
            Whitebox.invokeMethod(zmsStationLogUploadService, "meiTuanFileLogUpload", stationInputDTO);
            Assert.assertTrue(Objects.nonNull(stationInputDTO));
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try {
            PowerMockito.when(mdsRemoteService.getAccessToken()).thenThrow(new NullPointerException());
            zmsStationLogUploadService.meiTuanFileLogUpload(stationInputDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    /*Ended by AICoder, pid:p4afdvd1bam217114bdc0b1180dc8552f03020b6*/

    @Test public void getInsertStationList() throws Exception {
        List<StationLogDTO> stationLogDTOS = new ArrayList<>();
        Whitebox.invokeMethod(zmsStationLogUploadService, "getInsertStationList", stationLogDTOS);
        StationLogDTO a1 = new StationLogDTO();
        a1.setStationId("1");
        a1.setServerSn("1");
        a1.setLogName("1");
        stationLogDTOS.add(a1);
        List<StationLogDTO> stationLogDTOSuccessList = new ArrayList<>();
        PowerMockito.when(zmsStationLogUploadRepository.queryUploadFileLogSuccessList(Mockito.any())).thenReturn(stationLogDTOSuccessList);
        Whitebox.invokeMethod(zmsStationLogUploadService, "getInsertStationList", stationLogDTOS);
        StationLogDTO a2 = new StationLogDTO();
        a2.setStationId("1");
        a2.setServerSn("2");
        a2.setLogName("1");
        stationLogDTOSuccessList.add(a2);
        PowerMockito.when(zmsStationLogUploadRepository.queryUploadFileLogSuccessList(Mockito.any())).thenReturn(stationLogDTOSuccessList);
        Whitebox.invokeMethod(zmsStationLogUploadService, "getInsertStationList", stationLogDTOS);
        stationLogDTOSuccessList.clear();
        a2.setServerSn("1");
        stationLogDTOSuccessList.add(a2);
        PowerMockito.when(zmsStationLogUploadRepository.queryUploadFileLogSuccessList(Mockito.any())).thenReturn(stationLogDTOSuccessList);
        Whitebox.invokeMethod(zmsStationLogUploadService, "getInsertStationList", stationLogDTOS);
        Assert.assertNotNull(stationLogDTOS);
    }
    @Test
    public void updateFileLogMeiTuanFromMES() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setSn("1");
        dto.setContractNo("1");
        dto.setTaskNo("1");
        PowerMockito.when(zmsStationLogUploadRepository.updateFileLogMeiTuan(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsStationLogUploadService, "updateFileLogMeiTuanFromMES", dto);
        Assert.assertNotNull(dto);
    }

    /*Started by AICoder, pid:occb4a148ft3f6714b5a0948c011ff9a3b11eaad*/
    @Test
    public void testSnListValid() throws Exception {
        StationInputDTO stationInputDTO = new StationInputDTO();
        assertFalse(zmsStationLogUploadService.snListValid(stationInputDTO));
        List<String> snList = new ArrayList<>();
        stationInputDTO.setSnList(snList);
        assertFalse(zmsStationLogUploadService.snListValid(stationInputDTO));
        for (int i = 0; i < 19; i++) {
            snList.add("SN" + i);
        }
        stationInputDTO.setSnList(snList);
        assertTrue(zmsStationLogUploadService.snListValid(stationInputDTO));
        for (int i = 0; i < 2000; i++) {
            snList.add("SN" + i);
        }
        stationInputDTO.setSnList(snList);
        assertFalse(zmsStationLogUploadService.snListValid(stationInputDTO));
    }
    /*Ended by AICoder, pid:occb4a148ft3f6714b5a0948c011ff9a3b11eaad*/

    @Test
    public void testFilterMainLog() throws Exception {
        List<StationSnDataDTO> stationSnDataDTOS = new ArrayList<>();
        zmsStationLogUploadService.filterMainLog(stationSnDataDTOS, "");
        StationSnDataDTO stationSnDataDTO1 = new StationSnDataDTO();
        stationSnDataDTO1.setServerSn("11");
        stationSnDataDTOS.add(stationSnDataDTO1);
        StationSnDataDTO stationSnDataDTO2 = new StationSnDataDTO();
        stationSnDataDTO2.setServerSn("22");
        stationSnDataDTOS.add(stationSnDataDTO2);
        List<StationCheckLogDTO> stationCheckLogDTOList = new ArrayList<>();
        StationCheckLogDTO stationCheckLogDTO1 = new StationCheckLogDTO();
        stationCheckLogDTO1.setContractNumber("11");
        stationCheckLogDTO1.setEntityName("11");
        stationCheckLogDTO1.setEntityId("11");
        stationCheckLogDTO1.setServerSn("11");
        stationCheckLogDTO1.setStationId("PI2222");
        stationCheckLogDTOList.add(stationCheckLogDTO1);
        StationCheckLogDTO stationCheckLogDTO2 = new StationCheckLogDTO();
        stationCheckLogDTO2.setContractNumber("11");
        stationCheckLogDTO2.setEntityName("11");
        stationCheckLogDTO2.setEntityId("11");
        stationCheckLogDTO2.setServerSn("11");
        stationCheckLogDTO2.setStationId("ST2222");
        stationCheckLogDTOList.add(stationCheckLogDTO2);
        StationCheckLogDTO stationCheckLogDTO3 = new StationCheckLogDTO();
        stationCheckLogDTO3.setContractNumber("22");
        stationCheckLogDTO3.setEntityName("22");
        stationCheckLogDTO3.setEntityId("22");
        stationCheckLogDTO3.setServerSn("22");
        stationCheckLogDTO3.setStationId("MTHC2222");
        stationCheckLogDTOList.add(stationCheckLogDTO3);
        PowerMockito.when(zmsStationLogUploadRepository.getMainLogBySn(Mockito.any())).thenReturn(stationCheckLogDTOList);
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn(null);
        zmsStationLogUploadService.filterMainLog(stationSnDataDTOS, "");
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("MTHC");
        zmsStationLogUploadService.filterMainLog(stationSnDataDTOS, "");
        zmsStationLogUploadService.filterMainLog(stationSnDataDTOS, "123");
        Assert.assertNotNull(stationSnDataDTOS);
    }

    @Test
    public void testExtractProcedure() throws Exception {
        String procedureCheckList = "工序1,工序2,工序3";
        Set<String> procedures = Arrays.stream(procedureCheckList.split(COMMA)).collect(Collectors.toSet());
        zmsStationLogUploadService.extractProcedure("工序3", procedures);
        zmsStationLogUploadService.extractProcedure("工序4", procedures);
        Assert.assertNotNull(procedureCheckList);
    }

    @Test
    public void testInsertImesFileLogData() throws Exception {
        CustomerDataLogInfoDTO customerDataLogInfoDto = new CustomerDataLogInfoDTO();
        customerDataLogInfoDto.setCustomerName(MEITUAN);
        List<StationLogDTO> dtos = new ArrayList<>();
        StationLogDTO stationLogDTO = new StationLogDTO();
        stationLogDTO.setStationId("stationId1");
        stationLogDTO.setFileType(3);
        stationLogDTO.setLogName("logName1");
        dtos.add(stationLogDTO);

        StationSnDataDTO stationSnDataDto = new StationSnDataDTO();
        stationSnDataDto.setServerSn("serverSn1");
        PowerMockito.doNothing().when(centerfactoryRemoteService).pushDataToB2B(any());
        Assert.assertTrue(zmsStationLogUploadService.insertImesFileLogData(dtos,stationSnDataDto,customerDataLogInfoDto));

        customerDataLogInfoDto.setCustomerName(ALIBABA);
        Assert.assertTrue(zmsStationLogUploadService.insertImesFileLogData(dtos,stationSnDataDto,customerDataLogInfoDto));
    }

}
