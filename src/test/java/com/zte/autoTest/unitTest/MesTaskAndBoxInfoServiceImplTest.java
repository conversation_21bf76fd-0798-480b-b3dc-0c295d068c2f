package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MesTaskAndBoxInfoServiceImpl;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.MesSysInfoDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Russell.pan
 * @Date: 2021/5/13 15:40
 * @Description:
 */
@RunWith(PowerMockRunner.class)
public class MesTaskAndBoxInfoServiceImplTest extends BaseTestCase
{
    @InjectMocks
    private MesTaskAndBoxInfoServiceImpl mesTaskAndBoxInfoService;

    @Mock
    private RedisTemplate<String , Object> redisTemplate;

    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Before
    public void init()
    {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @PrepareForTest({CenterfactoryRemoteService.class,RedisCacheUtils.class})
    public void findTaskAndBoxInfoByList()
    {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.when(RedisCacheUtils.get(Mockito.any() , Mockito.any())).thenReturn(1000);
        PowerMockito.when(RedisCacheUtils.getRedisTemplate()).thenReturn(redisTemplate);
        List<SysLookupValues> lookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        sysLookupValues.setLookupCode(new BigDecimal("123"));
        lookupValuesList.add(sysLookupValues);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(lookupValuesList);
        Assert.assertNotNull(mesTaskAndBoxInfoService.findTaskAndBoxInfoByList(new MesSysInfoDTO()));
    }
}
