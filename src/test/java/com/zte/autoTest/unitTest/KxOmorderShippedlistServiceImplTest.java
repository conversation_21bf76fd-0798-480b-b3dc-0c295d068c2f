package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.KxOmorderShippedlistServiceImpl;
import com.zte.common.model.ServiceRetCode;
import com.zte.domain.model.datawb.KxOmorderShippedlist;
import com.zte.domain.model.datawb.KxOmorderShippedlistRepository;
import com.zte.domain.vo.datawb.ShippedListDetailVO;
import com.zte.domain.vo.datawb.ShippedListVO;
import com.zte.interfaces.dto.DetailSearchDTO;
import com.zte.interfaces.dto.InvoiceSearchDTO;
import com.zte.interfaces.dto.ShippedListDetailDTO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

public class KxOmorderShippedlistServiceImplTest extends BaseTestCase {

    @InjectMocks
    KxOmorderShippedlistServiceImpl kxOmorderShippedlistServiceImpl;

    @Mock
    private KxOmorderShippedlistRepository kxOmorderShippedlistRepository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Test
    public void selectWareHouseListTest() {
        kxOmorderShippedlistServiceImpl.selectWareHouseList();
        Mockito.verify(kxOmorderShippedlistRepository, times(1)).selectWareHouseList();
    }


    @Test

    public void findListTest() {

        //数据准备
        InvoiceSearchDTO dto = new InvoiceSearchDTO();
        dto.setOmsalesBill("C13072900003");
        dto.setSalesContractNo("123456");
        dto.setStock("WMWHSE1");
        dto.setPage(1);
        dto.setRows(10);

        Page<ShippedListVO> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        pageInfo.setParams(dto);

        List<ShippedListVO> list = Lists.newArrayList();
        ShippedListVO vo = new ShippedListVO();
        vo.setOmsalesBill("C13072900003");
        vo.setSalesContractNo("123456");
        list.add(vo);

        //数据模拟
        when(kxOmorderShippedlistRepository.selectListByPage(pageInfo)).thenReturn(list);

        //方法测试
        pageInfo = kxOmorderShippedlistServiceImpl.findList(dto);

        //测试断言
        Assert.assertNotNull(pageInfo);
        Assert.assertNotNull(pageInfo.getRows());
        assertEquals(pageInfo.getRows().get(0).getOmsalesBill(), "C13072900003");
        assertEquals(pageInfo.getCurrent(), 1);
        assertEquals(pageInfo.getPageSize(), 10);

    }

    @Test
    public void findDetailListTest_A() throws BusiException {
        DetailSearchDTO dto = new DetailSearchDTO();
        ShippedListVO vo = new ShippedListVO();
        BeanUtils.copyProperties(dto, vo);

        KxOmorderShippedlist shippedlist = new KxOmorderShippedlist();
        shippedlist.setIsSign(BigDecimal.ONE);
        shippedlist.setDocId("338456");
        when(kxOmorderShippedlistRepository.selectOneByCondition(vo)).thenReturn(shippedlist);

        when(kxOmorderShippedlistRepository.selectDetailList(vo)).thenReturn(null);

        ShippedListDetailDTO resultDto = kxOmorderShippedlistServiceImpl.findDetailList(dto);

        Assert.assertNotNull(resultDto);
        assertEquals(resultDto.getDocId(), shippedlist.getDocId());
        assertEquals(resultDto.getIsSign(), Integer.valueOf("1"));
        Assert.assertNull(resultDto.getDataList());

    }

    @Test
    public void findDetailListTest_B() throws BusiException {
        DetailSearchDTO dto = new DetailSearchDTO();
        ShippedListVO vo = new ShippedListVO();
        BeanUtils.copyProperties(dto, vo);

        KxOmorderShippedlist shippedlist = new KxOmorderShippedlist();
        shippedlist.setIsSign(BigDecimal.ZERO);
        shippedlist.setDocId(null);
        when(kxOmorderShippedlistRepository.selectOneByCondition(vo)).thenReturn(shippedlist);

        when(kxOmorderShippedlistRepository.selectDetailList(vo)).thenReturn(null);

        ShippedListDetailDTO resultDto = kxOmorderShippedlistServiceImpl.findDetailList(dto);

        Assert.assertNotNull(resultDto);
        assertEquals(resultDto.getDocId(), null);
        assertEquals(resultDto.getIsSign(), Integer.valueOf("0"));
        Assert.assertNull(resultDto.getDataList());

    }

    @Test
    public void selectOneByDocId() throws BusiException {
        String docId = "333845";
        ShippedListVO vo = new ShippedListDetailVO();
        vo.setDocId(docId);

        KxOmorderShippedlist shippedlist = new KxOmorderShippedlist();
        shippedlist.setDocId(docId);
        when(kxOmorderShippedlistRepository.selectOneByCondition(vo)).thenReturn(shippedlist);

        shippedlist = kxOmorderShippedlistServiceImpl.selectOneByDocId(docId);

        Assert.assertNotNull(shippedlist);
        assertEquals(shippedlist.getDocId(), docId);
    }

    @Test(expected = BusiException.class)
    public void selectOneByDocId_Error() throws BusiException {
        String docId = "";
        try {
            kxOmorderShippedlistServiceImpl.selectOneByDocId(docId);
        } catch (BusiException e) {
            assertEquals(e.getExCode(), ServiceRetCode.DOCID_ISNULL_CODE);
            throw e;
        }
    }

}
