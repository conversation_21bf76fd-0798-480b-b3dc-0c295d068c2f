package com.zte.autoTest.unitTest;

import com.zte.application.datawb.MesMaterialConfigBindServiceReplace;
import com.zte.application.datawb.impl.ConfigBindInfoQueryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.enums.EnumValue;
import com.zte.common.enums.EnumValueValidator;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.ConfigBindInfoQueryRepository;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

@PrepareForTest({CommonUtils.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
public class ConfigBindInfoQueryServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ConfigBindInfoQueryServiceImpl configBindInfoQueryServiceImpl;
    @Mock
    private ConfigBindInfoQueryRepository configBindInfoQueryRepository;
    @Mock
    MesMaterialConfigBindServiceReplace mesMaterialConfigBindServiceReplace;
    @Mock
    private ConfigBindInfoQueryServiceImpl configBindInfoQueryServiceImplDT;
    @InjectMocks
    EnumValueValidator enumValueValidatorImpl;
    @EnumValue(strValues = {"SN_CODE"},intValues={1}, message = "主条码的产品大类只能是SN_CODE")
    private String userName;

    @Test
    public void getBoqBomLevelUp() {
        Map<String, Object> bindMap = new HashMap<>();
        bindMap.put("recordId", 1);
        List<BindLevelDto> bindLevelDtos = new ArrayList<>();
        BindLevelDto dtoEvery = new BindLevelDto();
        dtoEvery.setRecordId("1");
        dtoEvery.setParenyRecordId("0");
        bindLevelDtos.add(dtoEvery);
        PowerMockito.when(configBindInfoQueryRepository.selectBindInformation(any())).thenReturn(bindLevelDtos);
        Assert.assertNotNull(configBindInfoQueryServiceImpl.getBoqBomLevelUp(1, 20));
    }

    @Test
    public void getBoqBomLevelDown() {
        Map<String, Object> bindMap = new HashMap<>();
        bindMap.put("parentRecordId", 1);
        List<BindLevelDto> bindLevelDtos = new ArrayList<>();
        BindLevelDto dtoEvery = new BindLevelDto();
        dtoEvery.setRecordId("1");
        dtoEvery.setParenyRecordId("0");
        bindLevelDtos.add(dtoEvery);
        PowerMockito.when(configBindInfoQueryRepository.selectBindInformation(any())).thenReturn(null);
        Assert.assertNotNull(configBindInfoQueryServiceImpl.getBoqBomLevelDown(1, 20));
    }

    @Test
    public void checkMainFiveLevel() {
        ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
        paramsBarcodeDetail.setLo(1);
        Assert.assertNotNull(configBindInfoQueryServiceImpl.checkMainFiveLevel(1, 1, paramsBarcodeDetail));
    }

    @Test
    public void checkSubRepleaced() {
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        mainCpmConfigItemAssemble.setMainCode("12333");
        ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
        paramsBarcodeDetail.setLo(1);
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        ent.setMainCode("12333");
        ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
        ConfigLevel dtxx = new ConfigLevel();
        dtxx.setSegment1("789");
        List<ConfigLevel> dtoList = new ArrayList<>();
        dtoList.add(dtxx);
        req.setConfigLevelData(dtoList);
        BasBarcodeInfo dtu = new BasBarcodeInfo();
        dtu.setItemCode("12333");
        req.setCurrentBarcode(dtu);
        int replaceFlag = 1;
        configBindInfoQueryServiceImpl.checkSubRepleaced(mainCpmConfigItemAssemble, paramsBarcodeDetail, ent, req, replaceFlag);
        replaceFlag = 0;
        configBindInfoQueryServiceImpl.checkSubRepleaced(mainCpmConfigItemAssemble, paramsBarcodeDetail, ent, req, replaceFlag);
        paramsBarcodeDetail.setLo(0);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        Assert.assertNotNull(configBindInfoQueryServiceImpl.checkSubRepleaced(mainCpmConfigItemAssemble, paramsBarcodeDetail, ent, req, replaceFlag));
    }

    @Test
    public void checkSubErrorReleation() {
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        mainCpmConfigItemAssemble.setRecordId("1");
        mainCpmConfigItemAssemble.setMainCode("12333");
        ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
        paramsBarcodeDetail.setLo(1);
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        ent.setMainCode("12333");
        ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
        ConfigLevel dtxx = new ConfigLevel();
        dtxx.setSegment1("789");
        List<ConfigLevel> dtoList = new ArrayList<>();
        dtoList.add(dtxx);
        req.setConfigLevelData(dtoList);
        BasBarcodeInfo dtu = new BasBarcodeInfo();
        dtu.setItemCode("12333");
        req.setCurrentBarcode(dtu);
        paramsBarcodeDetail.setLo(0);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        Map<String, Long> paramMap = new HashMap<>();
        paramMap.put("recordId", (long) 123);
        paramMap.put("parentId", (long) 123);
        List<BindLevelDto> bindParentLevel = new ArrayList<>();
        BindLevelDto dtv = new BindLevelDto();
        dtv.setParenyRecordId("0");
        dtv.setRecordId("1");
        bindParentLevel.add(dtv);
        PowerMockito.when(configBindInfoQueryRepository.selectBindInformation(any())).thenReturn(bindParentLevel);
        configBindInfoQueryServiceImpl.checkSubErrorReleation(1, paramMap, "756", req, mainCpmConfigItemAssemble);
        configBindInfoQueryServiceImpl.checkSubErrorReleation(1, paramMap, "", req, mainCpmConfigItemAssemble);
        PowerMockito.when(configBindInfoQueryRepository.selectBindInformation(any())).thenReturn(null);
        paramMap.put("parentId", (long) 0);
        Assert.assertNotNull(configBindInfoQueryServiceImpl.checkSubErrorReleation(1, paramMap, "756", req, mainCpmConfigItemAssemble));
    }

    @Test
    public void CheckSubItemBoqBom() {
        try {
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            mainCpmConfigItemAssemble.setRecordId("1");
            mainCpmConfigItemAssemble.setMainCode("12333");
            ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
            paramsBarcodeDetail.setLo(1);
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            ent.setMainCode("12333");
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
            ConfigLevel dtxx = new ConfigLevel();
            dtxx.setSegment1("789");
            List<ConfigLevel> dtoList = new ArrayList<>();
            dtoList.add(dtxx);
            req.setConfigLevelData(dtoList);
            BasBarcodeInfo dtu = new BasBarcodeInfo();
            dtu.setItemCode("12333");
            req.setCurrentBarcode(dtu);
            paramsBarcodeDetail.setLo(0);
            PowerMockito.mockStatic(CommonUtils.class);
            PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
            Map<String, Long> paramMap = new HashMap<>();
            paramMap.put("recordId", (long) 123);
            paramMap.put("parentId", (long) 123);
            List<BindLevelDto> bindParentLevel = new ArrayList<>();
            BindLevelDto dtv = new BindLevelDto();
            dtv.setParenyRecordId("0");
            dtv.setRecordId("1");
            dtv.setEntityId("123");
            dtv.setConfigDetailId("1233");
            dtv.setNodeCount("1");
            dtv.setEntityName("1233");
            bindParentLevel.add(dtv);
            req.setBarcodeType("序列码");
            PowerMockito.when(configBindInfoQueryRepository.selectBindInformation(any())).thenReturn(bindParentLevel);
            configBindInfoQueryServiceImpl.checkSubItemBoqBom(req, ent, mainCpmConfigItemAssemble, paramsBarcodeDetail,true);
		    configBindInfoQueryServiceImpl.checkSubItemBoqBom(req, ent, mainCpmConfigItemAssemble, paramsBarcodeDetail,false);            
			paramsBarcodeDetail.setLo(Constant.INT_1);
            PowerMockito.when(configBindInfoQueryRepository.selectBindInformation(any())).thenReturn(bindParentLevel);
            configBindInfoQueryServiceImpl.checkSubItemBoqBom(req, ent, mainCpmConfigItemAssemble, paramsBarcodeDetail,false);
            configBindInfoQueryServiceImpl.checkSubItemBoqBom(req, ent, mainCpmConfigItemAssemble, paramsBarcodeDetail,true);
        } catch (Exception ex) {
            Assert.assertNull(ex.getMessage());
        }
    }

    @Test
    public void setEnt() {
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(any())).thenReturn("12333");
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        mainCpmConfigItemAssemble.setRecordId("1");
        mainCpmConfigItemAssemble.setMainCode("12333");
        ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
        paramsBarcodeDetail.setLo(1);
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        ent.setMainCode("12333");
        ent.setEntityId("12333");
        ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
        ConfigLevel dtxx = new ConfigLevel();
        dtxx.setSegment1("789");
        List<ConfigLevel> dtoList = new ArrayList<>();
        dtoList.add(dtxx);
        req.setConfigLevelData(dtoList);
        BasBarcodeInfo dtu = new BasBarcodeInfo();
        dtu.setItemCode("12333");
        req.setCurrentBarcode(dtu);
        paramsBarcodeDetail.setLo(0);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        Map<String, Long> paramMap = new HashMap<>();
        paramMap.put("recordId", (long) 123);
        paramMap.put("parentId", (long) 123);
        List<BindLevelDto> bindParentLevel = new ArrayList<>();
        BindLevelDto dtv = new BindLevelDto();
        dtv.setParenyRecordId("0");
        dtv.setRecordId("1");
        dtv.setEntityId("123");
        dtv.setConfigDetailId("1233");
        dtv.setNodeCount("1");
        dtv.setEntityName("1233");
        bindParentLevel.add(dtv);
        req.setBarcodeType("序列码");
        req.setLinkTask("Y");
        req.setQty(1);
        req.setEntityId((long) 1);
        Map<String, Object> paramMapXXX = new HashMap<>();
        paramMapXXX.put("recordId", 12333);
        paramMapXXX.put("parentId", "12333");
        configBindInfoQueryServiceImpl.setEnt(ent, paramMapXXX, req, paramsBarcodeDetail, 1);
        req.setLinkTask("N");
        Assert.assertNotNull(configBindInfoQueryServiceImpl.setEnt(ent, paramMapXXX, req, paramsBarcodeDetail, 1));
    }

    @Test
    public void checkSquenceNoEntity() {
        PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(any())).thenReturn("12333");
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        mainCpmConfigItemAssemble.setRecordId("1");
        mainCpmConfigItemAssemble.setMainCode("12333");
        ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
        paramsBarcodeDetail.setLo(1);
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        ent.setMainCode("12333");
        ent.setEntityId("12333");
        ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
        ConfigLevel dtxx = new ConfigLevel();
        dtxx.setSegment1("789");
        List<ConfigLevel> dtoList = new ArrayList<>();
        dtoList.add(dtxx);
        req.setConfigLevelData(dtoList);
        BasBarcodeInfo dtu = new BasBarcodeInfo();
        dtu.setItemCode("12333");
        req.setCurrentBarcode(dtu);
        paramsBarcodeDetail.setLo(0);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");
        Map<String, Long> paramMap = new HashMap<>();
        paramMap.put("recordId", (long) 123);
        paramMap.put("parentId", (long) 123);
        List<BindLevelDto> bindParentLevel = new ArrayList<>();
        BindLevelDto dtv = new BindLevelDto();
        dtv.setParenyRecordId("0");
        dtv.setRecordId("1");
        dtv.setEntityId("123");
        dtv.setConfigDetailId("1233");
        dtv.setNodeCount("1");
        dtv.setEntityName("1233");
        bindParentLevel.add(dtv);
        req.setBarcodeType("序列码");
        req.setLinkTask("Y");
        req.setQty(1);
        req.setEntityId((long) 1);
        Map<String, Object> paramMapXXX = new HashMap<>();
        paramMapXXX.put("recordId", 12333);
        paramMapXXX.put("parentId", "12333");
        paramMapXXX.put(Constant.LAST_COUNT, "1233");
        paramMapXXX.put(Constant.LAST_ENITY_ID, "1233");
        paramMapXXX.put(Constant.LAST_CONFIG_ID, "1233");
        paramMapXXX.put(Constant.ENITY_NAME, "12333");
        configBindInfoQueryServiceImpl.checkSquenceNoEntity(req, paramMapXXX, paramsBarcodeDetail);
        req.setLinkTask("N");
        Assert.assertNotNull(configBindInfoQueryServiceImpl.checkSquenceNoEntity(req, paramMapXXX, paramsBarcodeDetail));
    }

    @Test
    public void checkSequenceQuality() {
        try {
            PowerMockito.when(mesMaterialConfigBindServiceReplace.getEmpNoUserID(any())).thenReturn("12333");
            CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
            mainCpmConfigItemAssemble.setRecordId("1");
            mainCpmConfigItemAssemble.setMainCode("12333");
            ParamsBarcodeDetail paramsBarcodeDetail = new ParamsBarcodeDetail();
            paramsBarcodeDetail.setLo(1);
            CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
            ent.setMainCode("12333");
            ent.setEntityId("12333");
            ScanBoqBomBarcodeRequest req = new ScanBoqBomBarcodeRequest();
            ConfigLevel dtxx = new ConfigLevel();
            dtxx.setSegment1("789");
            dtxx.setScanedQuality("1233");
            dtxx.setStimulateSumQuality("1233");
            List<ConfigLevel> dtoList = new ArrayList<>();
            dtoList.add(dtxx);
            req.setConfigLevelData(dtoList);
            BasBarcodeInfo dtu = new BasBarcodeInfo();
            dtu.setItemCode("12333");
            req.setCurrentBarcode(dtu);
            req.setBarcodeType("序列码");
            req.setLinkTask("Y");
            req.setQty(1);
            req.setEntityId((long) 1);
            req.setLinkTask("N");
            PowerMockito.mockStatic(CommonUtils.class);
            PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("");

            req.setLinkTask("Y");
            req.setBarcodeType("序列码");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }


            req.setLinkTask("Y");
            req.setBarcodeType("序列码cc");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("Y");
            req.setBarcodeType("序列码");
            req.setBarcode("xx21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }


            req.setLinkTask("Y");
            req.setBarcodeType("dd序列码");
            req.setBarcode("dd21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType("序列码");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType("序列码cc");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType("序列码");
            req.setBarcode("xx21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }


            req.setLinkTask("N");
            req.setBarcodeType("dd序列码");
            req.setBarcode("dd21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, true);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }



            req.setLinkTask("Y");
            req.setBarcodeType("序列码");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }


            req.setLinkTask("Y");
            req.setBarcodeType("序列码cc");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("Y");
            req.setBarcodeType("序列码");
            req.setBarcode("xx21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }


            req.setLinkTask("Y");
            req.setBarcodeType("dd序列码");
            req.setBarcode("dd21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType("序列码");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType("序列码cc");
            req.setBarcode("21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType("序列码");
            req.setBarcode("xx21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }


            req.setLinkTask("N");
            req.setBarcodeType("dd序列码");
            req.setBarcode("dd21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }
            MessageErrorDTO errorDTOList =new MessageErrorDTO();
            try{
                configBindInfoQueryServiceImpl.validateRepleacedItem(req,paramsBarcodeDetail,errorDTOList);
            }catch (Exception ex){
                Assert.assertNull(ex.getMessage());
            }

            req.setLinkTask("N");
            req.setBarcodeType(Constant.BATCH_CODE);
            req.setBarcode("dd21CA");
            try {
                configBindInfoQueryServiceImpl.checkSequenceQuality(req, false);
            }catch (Exception ex){
                Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
            }

        } catch (Exception ex) {
            Assert.assertEquals(MessageId.SCANED_HAVE, ex.getMessage());
        }
    }
    @Test
    public void initialize(){
        ConfigBindInfoQueryServiceImplTest dtx = new ConfigBindInfoQueryServiceImplTest();
        //获取注解
        try {
            Field f = dtx.getClass().getDeclaredField("userName");
            EnumValue ctv=  f.getAnnotation(EnumValue.class);
            enumValueValidatorImpl.initialize(ctv);
            Object value="SN_CODE";
            ConstraintValidatorContext context =null;
            enumValueValidatorImpl.isValid(value,context);
            Object valueDT="SN_CODD";
            enumValueValidatorImpl.isValid(valueDT,context);
            Object valueDt=1;
            enumValueValidatorImpl.isValid(valueDt,context);
            Object valueZero=0;
            enumValueValidatorImpl.isValid(valueZero,context);
        }
        catch (Exception ex){
            Assert.assertTrue(ex.getMessage().length() > 0);
        }



    }


}
