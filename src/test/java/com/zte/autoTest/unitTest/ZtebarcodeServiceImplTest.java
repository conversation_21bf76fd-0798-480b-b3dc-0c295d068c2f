package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.ZtebarcodeServiceImpl;
import com.zte.application.erpdt.impl.WipEntitiesServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.ZtebarcodeRepository;
import com.zte.domain.model.erpdt.WipEntitiesRepository;
import com.zte.interfaces.dto.WipEntitiesReportGetDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2021/3/17
 */
public class ZtebarcodeServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private ZtebarcodeServiceImpl service;

    @Mock
    private ZtebarcodeRepository ztebarcodeRepository;



    @Test
    public void getSmReportCount() {
        List<String> uulist=new ArrayList<>();
        uulist.add("111");
        PowerMockito.when(ztebarcodeRepository.getUuidInfoByUuidsList(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getUuidInfoByUuidsList(uulist));
    }
}
