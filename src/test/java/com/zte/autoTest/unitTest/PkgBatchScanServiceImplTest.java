package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.MtlRelatedItemsService;
import com.zte.application.datawb.impl.PkgBatchScanServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.SpringUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.PkgBatchScanRepository;
import com.zte.domain.model.datawb.SfcBoxItemInfo;
import com.zte.domain.model.datawb.SfcItemInfo;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.ProductionDeliveryRemoteService;
import com.zte.interfaces.dto.ContainerContentInfoDTO;
import com.zte.interfaces.dto.MtlRelatedItemsEntityDTO;
import com.zte.interfaces.dto.PkgBatchScanDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2021/3/18
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, RetCode.class, SpringContextUtil.class, CenterfactoryRemoteService.class, SpringUtil.class})
public class PkgBatchScanServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PkgBatchScanServiceImpl pkgBatchScanServiceImpl;

    @Mock
    private ProductionDeliveryRemoteService productionDeliveryRemoteService;

    @Mock
    private MtlRelatedItemsService mtlRelatedItemsService;

    @Mock
    private PkgBatchScanRepository pkgBatchScanRepository;

    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;

    @Test
    public void packingByPalletNo() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class,CenterfactoryRemoteService.class,SpringUtil.class);
        PkgBatchScanDTO pkgBatchScanDTO = JSON.parseObject("{\"taskNo\":\"WDMSMQ-M20191100016\",\"boxNo\":\"B211134153255\",\"boxId\":84488486,\"palletNo\":\"ZB00000001451\",\"itemCode\":\"123350720235\",\"itemName\":\"物料脱敏I5020077O635\",\"itemType\":\"网络事业部Modem\"}",
                PkgBatchScanDTO.class);
        PkgBatchScanDTO pkgBatchScanDTO2 = JSON.parseObject("{\"taskNo\":\"WDMSMQ-M20191100016\",\"boxNo\":\"C211134153255\",\"boxId\":84488486,\"palletNo\":\"ZB00000001451\",\"itemCode\":\"123350720235\",\"itemName\":\"物料脱敏I5020077O635\",\"itemType\":\"网络事业部Modem\"}",
                PkgBatchScanDTO.class);
        pkgBatchScanDTO.setFactoryId("55");
        pkgBatchScanDTO2.setFactoryId("55");
        LocaleMessageSourceBean lm=new LocaleMessageSourceBean();
        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        when(localeMessageSourceBean.getMessage(Mockito.any())).thenReturn("OK");
        List<SfcBoxItemInfo> sfcBoxItemInfos=new ArrayList<>();
        SfcBoxItemInfo sfcBoxItemInfo=new SfcBoxItemInfo();
        sfcBoxItemInfo.setItemCode("123350720235");
        sfcBoxItemInfo.setBoxupQty("50");
        sfcBoxItemInfo.setScanedQty("5");
        sfcBoxItemInfo.setScendQty("5");
        sfcBoxItemInfos.add(sfcBoxItemInfo);
        SfcBoxItemInfo sfcBoxItemInfo1=new SfcBoxItemInfo();
        sfcBoxItemInfo1.setItemCode("123350720235");
        sfcBoxItemInfo1.setBoxupQty("50");
        sfcBoxItemInfo1.setScanedQty("5");
        sfcBoxItemInfo1.setScendQty("5");
        sfcBoxItemInfos.add(sfcBoxItemInfo1);
        when(pkgBatchScanRepository.getItemInfoByBoxNo(anyString())).thenReturn(sfcBoxItemInfos);
        SfcItemInfo sfcItemInfo=new SfcItemInfo();
        when(pkgBatchScanRepository.getItemInfo(any(),anyString())).thenReturn(sfcItemInfo);
        when(pkgBatchScanRepository.getItemsInfoByLpn(anyString())).thenReturn(sfcBoxItemInfos);
        List<ContainerContentInfoDTO> contentInfoDTOList = new ArrayList<>();
        ContainerContentInfoDTO containerContentInfoDTO=new ContainerContentInfoDTO();
        containerContentInfoDTO.setItemCode("123350720235");
        containerContentInfoDTO.setEntityIdentification("22223333");
        contentInfoDTOList.add(containerContentInfoDTO);
        when(productionDeliveryRemoteService.getLpnDetailInfo(anyString(),any())).thenReturn(contentInfoDTOList);
        PowerMockito.when(productionDeliveryRemoteService.getLpnInfo(anyString())).thenReturn(contentInfoDTOList);
        List<SysLookupValues> sysLookupValuesList=new ArrayList<>();
        SysLookupValues sysLookupValues=new SysLookupValues();
        sysLookupValues.setLookupMeaning("Y");
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_6672009));
        sysLookupValuesList.add(sysLookupValues);
        when(CenterfactoryRemoteService.getLookupValue(anyString())).thenReturn(sysLookupValuesList);
        when(pkgBatchScanRepository.getItemInfoByBoxNoForCBox(anyString(),anyString())).thenReturn(sfcBoxItemInfos);
        when(pkgBatchScanRepository.getItemInfoByBoxNoAndItemCode(anyString(),anyString())).thenReturn(sfcBoxItemInfos);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pkgBatchScanRepository).executeInvScanBarcodeNew(any());
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(pkgBatchScanRepository).executeInvScanBarcode(any());
        try {
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO2, "55", "102");

            List<ContainerContentInfoDTO> contentInfoDTOList2 = new LinkedList<>();
            contentInfoDTOList2.add(new ContainerContentInfoDTO());
            PowerMockito.when(productionDeliveryRemoteService.getLpnInfo(Mockito.anyString()))
            .thenReturn(contentInfoDTOList2);

        }catch(Exception e){}

        sysLookupValues.setLookupMeaning("N");
        SfcBoxItemInfo sfcBoxItemInfo2 = new SfcBoxItemInfo();
        sfcBoxItemInfo2.setItemCode("123350720236");
        sfcBoxItemInfo2.setBoxupQty("60");
        sfcBoxItemInfo2.setScanedQty("6");
        sfcBoxItemInfo2.setScendQty("6");
        List<SfcBoxItemInfo> sfcBoxItemInfo10 = new ArrayList<>();
        sfcBoxItemInfo10.add(sfcBoxItemInfo);
        sfcBoxItemInfo10.add(sfcBoxItemInfo2);
        when(pkgBatchScanRepository.getItemsInfoByLpn(anyString())).thenReturn(sfcBoxItemInfo10);
        try {
            pkgBatchScanDTO.setPackingType(Constant.INT_1);
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanDTO.setPackingType(Constant.INT_2);
            PowerMockito.when(productionDeliveryRemoteService.getOriginalLpnInfoBatch(Mockito.anyList()))
                    .thenReturn(new ArrayList<>());
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanDTO.setPackingType(Constant.INT_5);
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanDTO.setPackingType(Constant.INT_3);
            pkgBatchScanDTO.setObjectList(new ArrayList() {{
                add("111");
            }});
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanDTO.setPackingType(Constant.INT_4);
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO, "55", "102");
            pkgBatchScanServiceImpl.packingByPalletNo(pkgBatchScanDTO2, "55", "102");

            List<ContainerContentInfoDTO> contentInfoDTOList2 = new LinkedList<>();
            contentInfoDTOList2.add(new ContainerContentInfoDTO());
            PowerMockito.when(productionDeliveryRemoteService.getLpnInfo(Mockito.anyString()))
                    .thenReturn(contentInfoDTOList2);

        }catch(Exception e){
            Assert.assertTrue(e.getMessage().length()> 0);
        }
    }


    @Test
    public void extracted() throws Exception {
        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        pkgBatchScanDTO.setItemCode("111");
        PkgBatchScanDTO pkgBatchScanDTO1 = new PkgBatchScanDTO();
        pkgBatchScanDTO1.setItemCode("222");
        List<SfcBoxItemInfo> itemInfoList = new ArrayList<>();
        List<SfcBoxItemInfo> itemInfoList1 = new ArrayList<>();
        SfcBoxItemInfo sfcBoxItemInfo = new SfcBoxItemInfo();
        sfcBoxItemInfo.setItemCode("111");
        itemInfoList.add(sfcBoxItemInfo);
        SfcBoxItemInfo sfcBoxItemInfo1 = new SfcBoxItemInfo();
        sfcBoxItemInfo1.setItemCode("222");
        itemInfoList1.add(sfcBoxItemInfo1);
        List<ContainerContentInfoDTO> list = new ArrayList<>();
        ContainerContentInfoDTO containerContentInfoDTO = new ContainerContentInfoDTO();
        containerContentInfoDTO.setItemCode("111");
        list.add(containerContentInfoDTO);
        PowerMockito.when(productionDeliveryRemoteService.getLpnDetailInfo(any(), any())).thenReturn(list);
        List<MtlRelatedItemsEntityDTO> mtlRelatedItemsEntityDTOS = new ArrayList<>();
        MtlRelatedItemsEntityDTO mtlRelatedItemsEntityDTO = new MtlRelatedItemsEntityDTO();
        mtlRelatedItemsEntityDTO.setInventoryItemId(1);
        mtlRelatedItemsEntityDTOS.add(mtlRelatedItemsEntityDTO);
        PowerMockito.when(mtlRelatedItemsService.getItemInfoList(any())).thenReturn(mtlRelatedItemsEntityDTOS);
        PowerMockito.when(pkgBatchScanRepository.getItemInfoList(any())).thenReturn(new ArrayList<>());
        pkgBatchScanServiceImpl.extracted(pkgBatchScanDTO, itemInfoList,1);
        try {
            pkgBatchScanServiceImpl.extracted(pkgBatchScanDTO, null,1);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
        PowerMockito.when(productionDeliveryRemoteService.getLpnDetailInfo(any(), any())).thenReturn(null);
        try {
            pkgBatchScanServiceImpl.extracted(pkgBatchScanDTO1, itemInfoList,1);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
        PowerMockito.when(productionDeliveryRemoteService.getLpnDetailInfo(any(), any())).thenReturn(list);
        try {
            pkgBatchScanServiceImpl.extracted(pkgBatchScanDTO1, itemInfoList,1);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            pkgBatchScanServiceImpl.extracted(pkgBatchScanDTO, itemInfoList1,1);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void insertTemp() {
        Map<String, Object> saveMap = new HashMap<>();
        List<String> snList = new LinkedList<>();
        snList.add("123");

        pkgBatchScanServiceImpl.insertTemp(saveMap, snList);
        Assert.assertNotNull(saveMap);
    }

    @Test
    public void getSfcBoxItemInfo() throws Exception {
        pkgBatchScanServiceImpl.getSfcBoxItemInfo("2454545","C111",true);
        Assert.assertNull(pkgBatchScanServiceImpl.getSfcBoxItemInfo("2454545","B111",false));
    }

    @Test
    public void getScanListByBoxNo() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class, RetCode.class);
        String taskNo = "5G6180H-M20230500101";
        String orgID = "635";
        String boxNoC = "C230505997167";
        String boxNoB = "B230505997167";
        String itemCode = "123353751658";
        String factoryID = "55";
        String factoryIDError = "123";
        RetCode retCode = new RetCode();
        retCode.setCode(RetCode.BUSINESSERROR_CODE);
        PkgBatchScanDTO pkgBatchScanDTO = new PkgBatchScanDTO();
        pkgBatchScanDTO.setItemCode(itemCode);
        pkgBatchScanDTO.setBoxNo(boxNoC);
        pkgBatchScanDTO.setOrgId(orgID);
        pkgBatchScanServiceImpl.getTaskId(taskNo, orgID);
        try {
            pkgBatchScanServiceImpl.validParam(pkgBatchScanDTO);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertNull(null, e.getMessage());
        }
        PowerMockito.when(CommonUtils.validFactoryId(anyString())).thenReturn(retCode);
        pkgBatchScanServiceImpl.getScanListByBoxNo(boxNoC, taskNo, "123", factoryIDError);
        pkgBatchScanServiceImpl.getItemInfoByBoxNo(boxNoC, factoryIDError);
        pkgBatchScanServiceImpl.getItemInfo(itemCode, factoryIDError);
        pkgBatchScanServiceImpl.getPacking(taskNo, factoryIDError);
        pkgBatchScanServiceImpl.getItemTypeList(factoryIDError);
        retCode.setCode(RetCode.SUCCESS_CODE);
        PowerMockito.when(CommonUtils.validFactoryId(anyString())).thenReturn(retCode);
        pkgBatchScanServiceImpl.getScanListByBoxNo(boxNoC, taskNo, "123", factoryID);
        pkgBatchScanServiceImpl.getScanListByBoxNo(boxNoB, taskNo, "123", factoryID);
        pkgBatchScanServiceImpl.getItemInfoByBoxNo(boxNoC, factoryID);
        pkgBatchScanServiceImpl.getItemInfo(itemCode, factoryID);
        PowerMockito.when(pkgBatchScanRepository.getTaskInfo(anyString(), any())).thenReturn(taskNo);
        PowerMockito.when(pkgBatchScanRepository.getPacking(anyString(), any())).thenReturn(new ArrayList<>());
        pkgBatchScanServiceImpl.getPacking(taskNo, factoryID);
        PowerMockito.when(pkgBatchScanRepository.getTaskInfo(anyString(), any())).thenReturn(null);
        pkgBatchScanServiceImpl.getPacking(taskNo, factoryID);
        pkgBatchScanServiceImpl.getOrgId();
        pkgBatchScanServiceImpl.getOrgId(factoryID);
        pkgBatchScanServiceImpl.getItemTypeList(factoryID);
    }
}
