package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BaItemSupplierServiceImpl;
import com.zte.domain.model.datawb.BaItemSupplierRepository;
import com.zte.interfaces.dto.BaItemSupplierDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;
/* Started by AICoder, pid:i6566le28cwf822146ca0bec5079bb5c6287089f */
/**
 * <AUTHOR>
 * @Date 2022/6/9 14:17
 */
public class BaItemSupplierServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    BaItemSupplierServiceImpl baItemSupplierServiceImpl;
    @Mock
    BaItemSupplierRepository baItemSupplierRepository;

    @Test
    public void getIsHasDirFlag() {
        List<BaItemSupplierDTO> list = new ArrayList<>();
        BaItemSupplierDTO dto = new BaItemSupplierDTO();
        dto.setItemCode("123");
        dto.setSupplerCode("123");
        dto.setSysLotCode("123");
        list.add(dto);
        PowerMockito.when(baItemSupplierRepository.getIsHasDirFlag(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(baItemSupplierServiceImpl.getIsHasDirFlag(list));

        baItemSupplierServiceImpl.getIsHasDirFlag(null);
        list.clear();
        list.add(new BaItemSupplierDTO());
        BaItemSupplierDTO a2 = new BaItemSupplierDTO();
        a2.setItemCode("123");
        list.add(a2);
        BaItemSupplierDTO a3 = new BaItemSupplierDTO();
        a3.setItemCode("123");
        a3.setSupplerCode("123");
        list.add(a3);
        baItemSupplierServiceImpl.getIsHasDirFlag(list);

        BaItemSupplierDTO a4 = new BaItemSupplierDTO();
        a4.setItemCode("123");
        a4.setSupplerCode("123");
        a4.setSysLotCode("123");
        list.add(a4);
        baItemSupplierServiceImpl.getIsHasDirFlag(list);
    }
}
/* Ended by AICoder, pid:i6566le28cwf822146ca0bec5079bb5c6287089f */
