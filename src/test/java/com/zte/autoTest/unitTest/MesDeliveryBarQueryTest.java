package com.zte.autoTest.unitTest;

import com.zte.application.MesGetDictInforService;
import com.zte.application.datawb.impl.MesDeliveryBarQueryServiceImpl;
import com.zte.domain.model.datawb.DeliveryBarQueryRepository;
import com.zte.interfaces.dto.DeliveryBarQueryInDTO;
import com.zte.interfaces.dto.DeliveryBarQueryOutDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import java.util.*;

public class MesDeliveryBarQueryTest extends PowerBaseTestCase{
    @InjectMocks
    private MesDeliveryBarQueryServiceImpl deliveryBarQueryImpl;
    @Mock
    private DeliveryBarQueryRepository deliveryBarQueryRepository;

    @Mock
    private MesGetDictInforService mesGetDictInforService;

    @Test
    public void isEmpty(){
        //覆盖1
        DeliveryBarQueryInDTO deliveryBarQueryParmaterDTO =new DeliveryBarQueryInDTO ();
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);


        deliveryBarQueryParmaterDTO.setItemName("123");
        Map<String, Object> outMap = new HashMap<String, Object>();
        outMap.put("800033000003", "Y");

        PowerMockito.when(mesGetDictInforService.getDict("8000330")).thenReturn(outMap);
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);


        deliveryBarQueryParmaterDTO.setContractNo("123");
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setItemName("123abc星期二AS");
        deliveryBarQueryParmaterDTO.setItemNameBoq("123abc星期二A..。，,()（）{}/、S");
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);
        Assert.assertNotNull(deliveryBarQueryParmaterDTO);

    }

    @Test
    public void isEmpty22(){
        //覆盖1
        DeliveryBarQueryInDTO deliveryBarQueryParmaterDTO =new DeliveryBarQueryInDTO ();
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setOrgId("635");
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setItemName("123");
        deliveryBarQueryParmaterDTO.setOrgId(null);
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setOrgId("635");
        deliveryBarQueryParmaterDTO.setContractNo("123");
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setItemName("123abc星期二AS");
        deliveryBarQueryParmaterDTO.setItemNameBoq("123abc星期二A..。，,()（）{}/、S");
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setItemBarcode("123abc星期二AS");
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setContractNo("123abc星期二AS");
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);

        Assert.assertNotNull(deliveryBarQueryParmaterDTO);
    }

    @Test
    public void query(){
        //覆盖1
        DeliveryBarQueryInDTO dto =new DeliveryBarQueryInDTO ();

        dto.setItemName("11");
        dto.setItemBarcode("11");
        dto.setItemCode("11");
        dto.setContractNo("123");

        List<DeliveryBarQueryOutDTO> list=new ArrayList<>();
        DeliveryBarQueryOutDTO out=new DeliveryBarQueryOutDTO();
        out.setBillNumber("B123");
        out.setItemBarcode("12");
        out.setItemCode("q23");
        out.setItemName("ere");
        list.add(out);

        DeliveryBarQueryOutDTO out2=new DeliveryBarQueryOutDTO();
        out2.setBillNumber("C123");
        out2.setItemBarcode("12");
        out2.setItemCode("q23");
        out2.setItemName("ere");
        out2.setLitemBarcode("12");
        out2.setLitemCode("q23");
        out2.setLitemName("ere");
        list.add(out2);

        DeliveryBarQueryOutDTO out23=new DeliveryBarQueryOutDTO();
        out23.setBillNumber("C123");
        out23.setItemBarcode("122");
        out23.setItemCode("q232");
        out23.setItemName("ere2");
        out23.setLitemBarcode("12");
        out23.setLitemCode("q23");
        out23.setLitemName("ere");
        list.add(out23);
        dto.setPage(1);
        dto.setRows(100);
        Page<DeliveryBarQueryOutDTO> page = new Page<>(dto.getPage(),dto.getRows());
        page.setParams(dto);

        PowerMockito.when(deliveryBarQueryRepository.deliveryBarQuery(page)).thenReturn(list);
        deliveryBarQueryImpl.getDeliveryBarQuery(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void isEmpty2(){
        //覆盖1
        DeliveryBarQueryInDTO deliveryBarQueryParmaterDTO =new DeliveryBarQueryInDTO ();
        deliveryBarQueryParmaterDTO.setItemName("123");
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);


        deliveryBarQueryParmaterDTO.setItemCode("123");
        deliveryBarQueryParmaterDTO.setItemBarcode("123");
        deliveryBarQueryParmaterDTO.setContractNo("123");
        deliveryBarQueryParmaterDTO.setEndDate(new Date());
        deliveryBarQueryParmaterDTO.setStartDate(new Date());
        Map<String, Object> outMap = new HashMap<String, Object>();
        outMap.put("800033000003", "Y");

        PowerMockito.when(mesGetDictInforService.getDict("8000330")).thenReturn(outMap);
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);


        deliveryBarQueryParmaterDTO.setItemCode("");
        deliveryBarQueryImpl.checkInput(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setItemCode("123");
        deliveryBarQueryParmaterDTO.setItemBarcode("");
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);


        deliveryBarQueryParmaterDTO.setContractNo("");
        deliveryBarQueryParmaterDTO.setItemBarcode("123");
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setContractNo("123");
        deliveryBarQueryParmaterDTO.setEndDate(null);
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);

        deliveryBarQueryParmaterDTO.setStartDate(null);
        deliveryBarQueryParmaterDTO.setEndDate(new Date());
        deliveryBarQueryImpl.setPage(deliveryBarQueryParmaterDTO);
        Assert.assertNotNull(deliveryBarQueryParmaterDTO);

    }


    @Test
    public void query3(){
        List<DeliveryBarQueryOutDTO> list=new ArrayList<>();
        DeliveryBarQueryOutDTO d1=new DeliveryBarQueryOutDTO();
        DeliveryBarQueryOutDTO d2=new DeliveryBarQueryOutDTO();
        DeliveryBarQueryOutDTO d3=new DeliveryBarQueryOutDTO();
        DeliveryBarQueryOutDTO d4=new DeliveryBarQueryOutDTO();
        DeliveryBarQueryInDTO inputQueryDto=new DeliveryBarQueryInDTO();
        d1.setLitemBarcode("dto.getItemBarcode()");
        d1.setLitemCode("dto.getItemCode()");
        d1.setLitemName("dto.getItemName()");
        d1.setItemBarcode("Constant.STRING_EMPTY");
        d1.setItemCode("Constant.STRING_EMPTY");
        d1.setItemName("Constant.STRING_EMPTY");
        d1.setBillNumber("123");
        list.add(d1);

        d2.setLitemBarcode("dto.getItemBarcode()");
        d2.setLitemCode("dto.getItemCode()");
        d2.setLitemName("dto.getItemName()");
        d2.setItemBarcode("Constant.STRING_EMPTY");
        d2.setItemCode("Constant.STRING_EMPTY");
        d2.setItemName("Constant.STRING_EMPTY");
        d2.setBillNumber("B123");
        list.add(d2);

        d3.setLitemBarcode("dto.getItemBarcode()");
        d3.setLitemCode("dto.getItemCode()");
        d3.setLitemName("dto.getItemName()");
        d3.setItemBarcode("Constant.STRING_EMPTY");
        d3.setItemCode("Constant.STRING_EMPTY");
        d3.setItemName("Constant.STRING_EMPTY");
        list.add(d3);

        d4.setLitemBarcode("dto.getItemBarcode()");
        d4.setLitemCode("dto.getItemCode()");
        d4.setLitemName("dto.getItemName()");
        d4.setItemBarcode("Constant.STRING_EMPTY");
        d4.setItemCode("Constant.STRING_EMPTY");
        d4.setItemName("Constant.STRING_EMPTY");
        d4.setBillNumber("c123");
        list.add(d4);

        Assert.assertNotNull(list);
    }


}
