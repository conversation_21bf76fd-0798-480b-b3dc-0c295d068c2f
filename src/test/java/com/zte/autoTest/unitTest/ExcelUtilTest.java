package com.zte.autoTest.unitTest;

import com.alibaba.excel.ExcelWriter;
import com.zte.common.ExcelUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@RunWith(PowerMockRunner.class)
public class ExcelUtilTest {
    @Mock
    ExcelWriter excelWriter;

    @Before
    public void init() {

    }
    @Test
    public void closeExcelWriter()throws Exception {
        ExcelUtil.closeExcelWriter(excelWriter);
        Assert.assertEquals("","");
        ExcelUtil.closeExcelWriter(null);
        Assert.assertEquals("","");
    }
}
