package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BaProdPlanServiceImpl;
import com.zte.domain.model.BaProdPlan;
import com.zte.domain.model.BaProdPlanRepository;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.Assert.*;

public class BaProdPlanServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    BaProdPlanServiceImpl service;

    @Mock
    BaProdPlanRepository repository;

    @Test
    public void getProdPlan() {
        Page<BaProdPlan> page = service.getProdPlan(1, 1, 1, "1");
        Assert.assertTrue(page.getRows().size() >= 0);
    }
}