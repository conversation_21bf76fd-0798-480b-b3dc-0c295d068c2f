package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.BaItemBrandstyleServiceImpl;
import com.zte.domain.model.datawb.BaItemBrandstyle;
import com.zte.domain.model.datawb.BaItemBrandstyleRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/9 00
 * @description:
 */
public class BaItemBrandstyleServiceImplTest  extends BaseTestCase {
    @InjectMocks
    BaItemBrandstyleServiceImpl baItemBrandstyleServiceImpl;
    @Mock
    private BaItemBrandstyleRepository baItemBrandstyleRepository;

    @Test
    public void batchQueryItemBrandByItems(){
        List<String> itemNoList = new LinkedList<>();
        itemNoList.add("123");
        Assert.assertTrue(baItemBrandstyleServiceImpl.batchQueryItemBrandByItems(itemNoList).size() == 0);

        List<BaItemBrandstyle> baItemList = new LinkedList<>();
        BaItemBrandstyle a1 = new BaItemBrandstyle();
        baItemList.add(a1);
        PowerMockito.when(baItemBrandstyleRepository.batchQueryItemBrandByItems(Mockito.anyList()))
                .thenReturn(baItemList);
        Assert.assertTrue(baItemBrandstyleServiceImpl.batchQueryItemBrandByItems(itemNoList).size()>0);

    }

    @Test
    public void selBaItemBrandstyleList(){
        BaItemBrandstyle dto = new BaItemBrandstyle();
        dto.setItemUuidList("'123','124'");
        Assert.assertNotNull(baItemBrandstyleServiceImpl.selBaItemBrandstyleList(dto));
    }

    @Test
    public void queryItemMinPackage(){
        BaItemBrandstyle dto = new BaItemBrandstyle();
        dto.setItemNoList(new LinkedList<String>(){{add("123");}});
        Assert.assertNotNull(baItemBrandstyleServiceImpl.queryItemMinPackage(dto));
    }

    @Test
    public void queryItemBrandInfo(){
        BaItemBrandstyle dto = new BaItemBrandstyle();
        dto.setItemNo("123");
        Assert.assertNotNull(baItemBrandstyleServiceImpl.queryItemBrandInfo(dto));
    }

    /* Started by AICoder, pid:ic8f0c18e947df614be30828f0b10a1eea94145c */
    @Test
    public void batchQueryItemBrandByBrands(){
        Assert.assertNotNull(baItemBrandstyleServiceImpl.batchQueryItemBrandByBrands(null));
    }
    /* Ended by AICoder, pid:ic8f0c18e947df614be30828f0b10a1eea94145c */
}
