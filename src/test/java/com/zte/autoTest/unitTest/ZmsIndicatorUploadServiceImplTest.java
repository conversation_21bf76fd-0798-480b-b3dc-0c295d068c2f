package com.zte.autoTest.unitTest;

/* Started by AICoder, pid:nf9cbk631ds553b146c50a72f0479b5301c24f77 */

import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.application.datawb.ZmsDeviceInventoryUploadService;
import com.zte.application.datawb.impl.*;
import com.zte.application.sfc.WholeMachineUpTestRecordService;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.MtlSystemItemsRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.*;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.EntityWeightDTO;
import com.zte.interfaces.dto.ProdOrderDeliveryDTO;
import com.zte.interfaces.dto.ProdOrderDeliveryReqDTO;
import com.zte.interfaces.dto.StationLogDTO;
import com.zte.interfaces.dto.ZmsCbomInfoDTO;
import com.zte.interfaces.dto.ZmsExtendedAttributeDTO;
import com.zte.interfaces.dto.ZmsIndicatorDTO;
import com.zte.interfaces.dto.ZmsInputDTO;
import com.zte.interfaces.dto.ZmsMesInfoUploadLogDTO;
import com.zte.interfaces.dto.ZmsStationInfoDTO;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT;
import static com.zte.common.utils.Constant.ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2023年6月25日
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsIndicatorUploadServiceImplTest {

    @InjectMocks
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;
    @Mock
    private ZmsIndicatorUploadRepository zmsIndicatorUploadRepository;
    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Mock
    private MtlSystemItemsRepository mtlSystemItemsRepository;
    @Mock
    private ZmsDeviceInventoryUploadService zmsDeviceInventoryUploadService;
    @Mock
    private ZmsMesB2bUploadLogRepository zmsMesB2bUploadLogRepository;
    @Mock
    private ZmsForwardTencentRepository zmsForwardTencentRepository;
    @Mock
    private CfgCodeRuleItemServiceImpl cfgCodeRuleItemService;
    @Mock
    private ZmsByteDanceCCEServiceImpl zmsByteDanceCCEService;
    @Mock
    private WsmAssembleLinesService wsmAssembleLinesService;
    @Mock
    private WholeMachineUpTestRecordService wholeMachineUpTestRecordService;
    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;
    @Mock
    private ZmsAlibabaRepository zmsAlibabaRepository;
    @Mock
    private MeiTuanRepository meiTuanRepository;


    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class);
    }

    @Test
    public void testLogCallback() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setMessageType("123");
        List<SysLookupValues> messageTypeList = new ArrayList<>();
        messageTypeList.add(new SysLookupValues());
        messageTypeList.add(new SysLookupValues(){{setDescription("2");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD));}});
        messageTypeList.add(new SysLookupValues(){{setDescription("123");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824009200004));}});

        messageTypeList.add(new SysLookupValues(){{setDescription("1233");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824006300013));}});
        messageTypeList.add(new SysLookupValues(){{setDescription("32111");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824006300015));}});

        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240063))).thenReturn(messageTypeList);

        Whitebox.invokeMethod(zmsIndicatorUploadService,"testLogCallback",dto);
        Assert.assertNotNull(dto);

        List<SysLookupValues> dataTypeList = new ArrayList<>();
        dataTypeList.add(new SysLookupValues());
        dataTypeList.add(new SysLookupValues(){{setLookupCode(new BigDecimal("2"));}});
        dataTypeList.add(new SysLookupValues(){{setLookupMeaning("123");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824009200004));}});
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240092))).thenReturn(dataTypeList);
        Whitebox.invokeMethod(zmsIndicatorUploadService,"testLogCallback",dto);
        Assert.assertNotNull(dto);
        dataTypeList.add(new SysLookupValues(){{setLookupMeaning("123");setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824009200002));}});
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240092))).thenReturn(dataTypeList);
        dto.setMessageType("32111");
        Whitebox.invokeMethod(zmsIndicatorUploadService,"testLogCallback",dto);
        Assert.assertNotNull(dto);
        dto.setMessageType("1233");
        Whitebox.invokeMethod(zmsIndicatorUploadService,"testLogCallback",dto);
        Assert.assertNotNull(dto);
    }


    /*Started by AICoder, pid:572bbad205qedf4148420a9e41245a002549fcfb*/
    @Test(timeout = 8000)
    public void testUpdateMesInfoUploadLog_WhenMessageTypeIsZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT()
            throws IOException {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setMessageType("123");
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        Assert.assertNull(null);
    }

    @Test(timeout = 8000)
    public void testUpdateMesInfoUploadLog_WhenMessageTypeIsNotZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT()
            throws IOException {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setMessageType(ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        Assert.assertNull(null);
    }

    @Test//(timeout = 8000)
    public void testUpdateMesInfoUploadLog_WhenProjectNameIsDEVICE_WHOLE_INVENTORY_MOVE_ZH()
            throws IOException {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        Mockito.doNothing().when(zmsDeviceInventoryUploadService).updateCpmContractEntitiesUpload(Mockito.any());
        dto.setId("111");
        dto.setMessageType(ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT);
        dto.setProjectName(Constant.DEVICE_WHOLE_INVENTORY_MOVE_ZH);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setProjectName("222");
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType(Constant.ZTE_IMES_BYTEDANCE_SALES_ORDER);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType(Constant.MESSAGE_TYPE_B2B_TENCENTMPT);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType(Constant.ZTE_IMES_TENCENT_FORWARD_UPLOAD);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType(Constant.ZTE_IMES_ALIBABA_AIS_META_MCT);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType(Constant.MESSAGE_TYPE_TEST_FILE_ALIBABA);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType(Constant.ZTEIMES_MEITUAN_SERVER);
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        dto.setMessageType("111");
        zmsIndicatorUploadService.updateMesInfoUploadLog(dto);
        try {
            zmsIndicatorUploadService.updateMesInfoUploadLog(new CustomerDataLogDTO());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertNotNull(e.toString());
        }
        Assert.assertNotNull(dto);
    }
    /*Ended by AICoder, pid:572bbad205qedf4148420a9e41245a002549fcfb*/

    @Test
    public void indicatorUpload() throws Exception {

        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsInputDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        List<ZmsStationInfoDTO> zmsStationInfoDTOList = new ArrayList<>();

        PowerMockito.when(zmsIndicatorUploadRepository.getIndicatorZmsStationInfoList(Mockito.any())).thenReturn(zmsStationInfoDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "indicatorUpload", zmsInputDTO);

        ZmsStationInfoDTO zmsStationInfoDTO = new ZmsStationInfoDTO();
        zmsStationInfoDTO.setServerSn("219485484494");
        zmsStationInfoDTO.setStationId("气密测试工序6950347");
        zmsStationInfoDTO.setId("234252");
        zmsStationInfoDTOList.add(zmsStationInfoDTO);
        PowerMockito.when(zmsIndicatorUploadRepository.getIndicatorZmsStationInfoList(Mockito.any())).thenReturn(zmsStationInfoDTOList);
        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("");
        Whitebox.invokeMethod(zmsIndicatorUploadService, "indicatorUpload", zmsInputDTO);

        PowerMockito.when(mdsRemoteService.getAccessToken()).thenReturn("123");
        String zsUrl = "https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/querySspTestIndexParamInfo_byte";
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(Mockito.any(), Mockito.any())).thenReturn(zsUrl);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("partcode", "111");
        List<ZmsIndicatorDTO> zmsIndicatorDTOList = new ArrayList<>();

        String params = JacksonJsonConverUtil.beanToJson(paramMap);
        List<StationLogDTO> stationList = new ArrayList<>();
        Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
        headerParamsMap.put("Z-ACCESS-TOKEN", "222");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(result);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn(null);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsIndicatorDTOList);
        PowerMockito.when(zmsIndicatorUploadRepository.updateZmsStationInfo(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "indicatorUpload", zmsInputDTO);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        ZmsIndicatorDTO zmsIndicatorDTO = new ZmsIndicatorDTO();
        zmsIndicatorDTO.setServerSn("219485484494");
        zmsIndicatorDTO.setStationId("气密测试工序6950347");
        zmsIndicatorDTOList.add(zmsIndicatorDTO);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsIndicatorDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "getIndicatorData", zmsInputDTO, zmsStationInfoDTOList, zmsIndicatorDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "indicatorUpload", zmsInputDTO);

        Assert.assertNotNull(zmsInputDTO);
    }


    @Test
    public void getIndicatorData() throws Exception {

        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsInputDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);

        List<ZmsStationInfoDTO> zmsStationInfoDTOList = new ArrayList<>();
        ZmsStationInfoDTO zmsStationInfoDTO = new ZmsStationInfoDTO();
        zmsStationInfoDTO.setServerSn("219485484494");
        zmsStationInfoDTO.setStationId("气密测试工序6950347");
        zmsStationInfoDTO.setId("234252");
        zmsStationInfoDTOList.add(zmsStationInfoDTO);

        List<ZmsIndicatorDTO> zmsIndicatorDTOList = new ArrayList<>();
        ZmsIndicatorDTO zmsIndicatorDTO = new ZmsIndicatorDTO();
        zmsIndicatorDTO.setServerSn("219485484494");
        zmsIndicatorDTO.setStationId("气密测试工序6950347test");
        zmsIndicatorDTOList.add(zmsIndicatorDTO);

        List<String> updateIdList = new ArrayList<>();
        updateIdList.add("3422");
        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setContractNo("2352352");
        zmsMesInfoUploadLogDTO.setSn("325252");
        zmsMesInfoUploadLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadLogDTO);
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setContractNo("2352352");
        customerDataLogDTO.setSn("325252");
        customerDataLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        dataList.add(customerDataLogDTO);

        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateZmsStationInfo", updateIdList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "getIndicatorData", zmsInputDTO, zmsStationInfoDTOList, zmsIndicatorDTOList);

        zmsIndicatorDTOList.clear();
        ZmsIndicatorDTO zmsIndicatorDTO2 = new ZmsIndicatorDTO();
        zmsIndicatorDTO2.setServerSn("219485484494test");
        zmsIndicatorDTO2.setStationId("气密测试工序6950347");
        zmsIndicatorDTOList.add(zmsIndicatorDTO2);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateZmsStationInfo", updateIdList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "getIndicatorData", zmsInputDTO, zmsStationInfoDTOList, zmsIndicatorDTOList);

        zmsIndicatorDTOList.clear();
        ZmsIndicatorDTO zmsIndicatorDTO1 = new ZmsIndicatorDTO();
        zmsIndicatorDTO1.setServerSn("219485484494");
        zmsIndicatorDTO1.setStationId("气密测试工序6950347");
        zmsIndicatorDTOList.add(zmsIndicatorDTO1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateZmsStationInfo", updateIdList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "getIndicatorData", zmsInputDTO, zmsStationInfoDTOList, zmsIndicatorDTOList);

        Assert.assertNotNull(zmsInputDTO);
    }

    @Test
    public void updateZmsStationInfo() throws Exception {

        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsInputDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);

        List<String> updateIdList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateZmsStationInfo", updateIdList);

        updateIdList.add("3525252");
        PowerMockito.when(zmsIndicatorUploadRepository.updateZmsStationInfo(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateZmsStationInfo", updateIdList);

        Assert.assertNotNull(zmsInputDTO);
    }

    @Test
    public void insertMesInfoUploadLog() throws Exception {

        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsInputDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);

        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);

        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setContractNo("2352352");
        zmsMesInfoUploadLogDTO.setSn("325252");
        zmsMesInfoUploadLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadLogDTO);
        PowerMockito.when(zmsIndicatorUploadRepository.insertMesInfoUploadLog(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);

        Assert.assertNotNull(zmsInputDTO);
    }

    @Test
    public void pushDataToB2B() throws Exception {

        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsInputDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setContractNo("2352352");
        customerDataLogDTO.setSn("325252");
        customerDataLogDTO.setMessageType(ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        dataList.add(customerDataLogDTO);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(constantInterface.getUrl(InterfaceEnum.pushLogToImes)).thenReturn("http://imes.dev.zte.com.cn/zte-mes-manufactureshare-centerfactory/customerDataLog/pushDataToB2B");
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(result);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);

        Assert.assertNotNull(zmsInputDTO);
    }

    @Test
    public void updateMesInfoUploadLog() throws Exception {

        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setSn("3234252");
        dto.setContractNo("3463463");
        dto.setMessageType("234252");
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateMesInfoUploadLog", dto);

        dto.setMessageType(Constant.ZTE_IMES_BYTEDANCE_UPLOAD_INDICATOR);
        PowerMockito.when(zmsIndicatorUploadRepository.updateMesInfoUploadLog(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateMesInfoUploadLog", dto);

        dto.setId("32425");
        dto.setMessageType(Constant.ZTE_IMES_BYTEDANCE_INVENTORY);
        PowerMockito.when(zmsIndicatorUploadRepository.updateMesInfoUploadLog(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateMesInfoUploadLog", dto);

        dto.setId("32425");
        dto.setMessageType(Constant.ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT);
        PowerMockito.when(zmsIndicatorUploadRepository.updateMesInfoUploadLog(Mockito.any())).thenReturn(1);
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setMessageType(Constant.ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT);
        zmsMesInfoUploadLogDTO.setId("32425");
        zmsMesInfoUploadLogDTO.setUploadStatus("0");
        zmsMesInfoUploadLogDTO.setJsonData("111");
        PowerMockito.when(zmsIndicatorUploadRepository.getZmsMesInfoUploadLogDTOById(Mockito.any())).thenReturn(zmsMesInfoUploadLogDTO);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "updateCpmContractEntitiesUpload", zmsMesInfoUploadLogDTO);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateMesInfoUploadLog", dto);

        dto.setId("32425");
        dto.setMessageType(Constant.ZTE_IMES_TENCENT_FORWARD_UPLOAD);
        PowerMockito.when(zmsIndicatorUploadRepository.updateMesInfoUploadLog(Mockito.any())).thenReturn(1);
        PowerMockito.when(zmsForwardTencentRepository.updateForwardTencentByMessageId(Mockito.any())).thenReturn(1);
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn("1");
        PowerMockito.when(zmsForwardTencentRepository.updateInternetMain(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "updateMesInfoUploadLog", dto);
        Assert.assertNotNull(dto);
    }

    /* Started by AICoder, pid:e542457672404e7f8059246f2f8a1514 */
    @Test
    public  void prodOrderDelivery() throws  Exception{
        List<EntityWeightDTO> dictCompanys = new ArrayList<>();
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(dictCompanys);
        PowerMockito.when( mtlSystemItemsRepository.getProdOrderDelivery(Mockito.any())).thenReturn(null);
        zmsIndicatorUploadService.prodOrderDelivery(null,null);
        Assert.assertEquals(true,true);

        dictCompanys = new ArrayList<>();
        EntityWeightDTO dto =new EntityWeightDTO();
        dictCompanys.add(dto);
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(dictCompanys);
        List<ProdOrderDeliveryDTO> gg = new ArrayList<>();
        ProdOrderDeliveryDTO de = new ProdOrderDeliveryDTO();
        de.setEntryLineIds("33");
        gg.add(de);
        PowerMockito.when( mtlSystemItemsRepository.getProdOrderDelivery(Mockito.any())).thenReturn(gg);
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.any())).thenReturn("100");
        PowerMockito.when(zmsDeviceInventoryUploadService.getDataTransferBatchNo()).thenReturn("100");
        zmsIndicatorUploadService.prodOrderDelivery(new ProdOrderDeliveryReqDTO(),null);
        Assert.assertEquals(true,true);
    }/* Started by AICoder, pid:e542457672404e7f8059246f2f8a1514 */

    /* Started by AICoder, pid:e822bf55c7e640b9972c4507f1a4398d */
    @Test
    public  void handleProdOrderDelivery() throws  Exception{
        List<ProdOrderDeliveryDTO> listProdOrderDelivery = new ArrayList<>();
        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        PowerMockito.when(zmsDeviceInventoryUploadService.getMaterialInfo(Mockito.any(),Mockito.any())).thenReturn(null);
        zmsIndicatorUploadService.handleProdOrderDelivery(listProdOrderDelivery,null,null);
        Assert.assertEquals(true,true);

        ProdOrderDeliveryDTO dto = new ProdOrderDeliveryDTO();
        dto = new ProdOrderDeliveryDTO();
        dto.setEntityName("2222");
        dto.setEntryLineIds("11,22");
        listProdOrderDelivery.add(dto);
        dto = new ProdOrderDeliveryDTO();
        dto.setEntityName("3333");
        dto.setEntryLineIds("11,22");
        listProdOrderDelivery.add(dto);
        dto = new ProdOrderDeliveryDTO();
        dto.setEntityName("4444");
        dto.setEntryLineIds("11,22");
        listProdOrderDelivery.add(dto);
        dto = new ProdOrderDeliveryDTO();
        dto.setEntityName("5555");
        dto.setEntryLineIds("11,22");
        listProdOrderDelivery.add(dto);

        List<ZmsExtendedAttributeDTO> vv = new ArrayList<>();
        List<ZmsCbomInfoDTO> listZmsCbomInfo = new ArrayList<>();
        ZmsCbomInfoDTO dd = new ZmsCbomInfoDTO();
        dd.setEntityName("3333");
        dd.setExtendedAttributes(null);
        listZmsCbomInfo.add(dd);
        vv = new ArrayList<>();
        dd = new ZmsCbomInfoDTO();
        dd.setEntityName("4444");
        ZmsExtendedAttributeDTO nn = new ZmsExtendedAttributeDTO();
        vv.add(nn);
        dd.setExtendedAttributes(vv);
        listZmsCbomInfo.add(dd);

        vv = new ArrayList<>();
        nn = new ZmsExtendedAttributeDTO();
        nn.setCbomExtentionZh("整机编码");
        vv.add(nn);
        nn = new ZmsExtendedAttributeDTO();
        nn.setCbomExtentionZh("整机名称");
        vv.add(nn);
        dd = new ZmsCbomInfoDTO();
        dd.setEntityName("5555");
        dd.setExtendedAttributes(vv);
        listZmsCbomInfo.add(dd);
        PowerMockito.when(zmsDeviceInventoryUploadService.getMaterialInfo(Mockito.any(),Mockito.any())).thenReturn(listZmsCbomInfo);
        zmsIndicatorUploadService.handleProdOrderDelivery(listProdOrderDelivery,null,null);
        Assert.assertEquals(true,true);
    }/* Ended by AICoder, pid:e822bf55c7e640b9972c4507f1a4398d */

    /* Started by AICoder, pid:1cb6b61d1ca947f793776c2eee2d40c6 */
    @Test
    public  void pushDataToB2BToImes() throws  Exception{
        String empNo ="10277404";
        String batchNo = "ZTE1";
        List<ProdOrderDeliveryDTO> tempInsertList = new ArrayList<>();
        zmsIndicatorUploadService.pushDataToB2BToImes(empNo,tempInsertList,batchNo);
        Assert.assertEquals(true,true);
        ProdOrderDeliveryDTO dto = new ProdOrderDeliveryDTO();
        tempInsertList.add(dto);
        zmsIndicatorUploadService.pushDataToB2BToImes(empNo,tempInsertList,batchNo);
        Assert.assertEquals(true,true);
    }/* Ended by AICoder, pid:1cb6b61d1ca947f793776c2eee2d40c6 */

    @Test
    public void testUpdateInternetMain() {
        PowerMockito.when(cfgCodeRuleItemService.getSmallBoxSize(Mockito.any())).thenReturn("1");
        zmsIndicatorUploadService.updateInternetMain("1", "2", "3", "");
        zmsIndicatorUploadService.updateInternetMain("1", "2", "3", "2");
        StringBuilder failReason = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            failReason.append("1");
        }
        zmsIndicatorUploadService.updateInternetMain("1", "2", "3", failReason.toString());
        Assert.assertEquals(true, true);
    }

    @Test
    public void testTestLogCallback() throws Exception {
        List<SysLookupValues> messageTypeList = new ArrayList<>();
        messageTypeList.add(new SysLookupValues());
        messageTypeList.add(new SysLookupValues() {{
            setDescription("2");
            setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT_USRADD));
        }});
        messageTypeList.add(new SysLookupValues() {{
            setDescription("");
            setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_FORWARD_TENCENT));
        }});
        messageTypeList.add(new SysLookupValues() {{
            setDescription("22");
            setLookupCode(null);
        }});
        messageTypeList.add(new SysLookupValues() {{
            setDescription("123");
            setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824009200004));
        }});
        messageTypeList.add(new SysLookupValues() {{
            setDescription("1233");
            setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824006300013));
        }});
        messageTypeList.add(new SysLookupValues() {{
            setDescription("32111");
            setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_824006300015));
        }});
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.anyString())).thenReturn(messageTypeList);
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setMessageType("111");
        zmsIndicatorUploadService.testLogCallback(dto);
        dto.setMessageType("1233");
        zmsIndicatorUploadService.testLogCallback(dto);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(eq(Constant.LOOKUP_TYPE_8240092))).thenReturn(null);
        zmsIndicatorUploadService.testLogCallback(dto);
        Assert.assertNotNull(messageTypeList);
    }

}
/* Ended by AICoder, pid:nf9cbk631ds553b146c50a72f0479b5301c24f77 */