package com.zte.autoTest.unitTest;

import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.application.datawb.impl.EntryBoxAppliedOthersServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.domain.model.datawb.*;
import com.zte.interfaces.dto.EntryBoxAppliedDTO;
import com.zte.interfaces.dto.EntryBoxAppliedEntryBillDTO;
import com.zte.interfaces.dto.MesEntryReadyLineDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,HttpClientUtil.class})
@PowerMockIgnore("javax.net.ssl.*")
public class EntryBoxAppliedOthersServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private EntryBoxAppliedOthersServiceImpl entryBoxAppliedOthersServiceImpl;

    @Mock
    private WmesWerpConEntityTraceRepository wmesWerpConEntityTraceRepository;

    @Mock
    private EntryBoxAppliedRepository entryBoxAppliedRepository;

    @Mock
    private BasBarcodeInfoRepository basBarcodeInfoRepository;

    @Mock
    private RecalculationConvergenceRepository recalculationConvergenceRepository;

    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;

    @Mock
    private CfgCodeRuleItemRepository cfgCodeRuleItemRepository;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private CpeBoxupBillService cpeBoxupBillService;

    @Test
    public void getTaskContractInformation() throws Exception {
        Assert.assertNotNull(entryBoxAppliedOthersServiceImpl.getTaskContractInformation(1));
        entryBoxAppliedOthersServiceImpl.getEarliestTimeForAssemblyPackaging(1);
    }

    @Test
    public void callProApplyBillOperate() throws Exception {
        try {
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("Y");
            PowerMockito.when(recalculationConvergenceRepository.getMesEntryBillsByPsApplyBillNo(Mockito.anyString())).thenReturn(null);
            entryBoxAppliedOthersServiceImpl.callProApplyBillOperate("", "");
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }
        List<EntryBoxAppliedEntryBillDTO> entryBills = new ArrayList<>();
        EntryBoxAppliedEntryBillDTO dto = new EntryBoxAppliedEntryBillDTO();
        dto.setEntryStatus("POSTED");
        entryBills.add(dto);
        PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("Y");
        PowerMockito.when(recalculationConvergenceRepository.getMesEntryBillsByPsApplyBillNo(Mockito.anyString())).thenReturn(entryBills);
        try {
            entryBoxAppliedOthersServiceImpl.callProApplyBillOperate("", "");
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        entryBills = new ArrayList<>();
        dto = new EntryBoxAppliedEntryBillDTO();
        dto.setEntryStatus("SUBMITTED");
        entryBills.add(dto);
        PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("Y");
        PowerMockito.when(recalculationConvergenceRepository.getMesEntryBillsByPsApplyBillNo(Mockito.anyString())).thenReturn(entryBills);
        PowerMockito.when(pubHrvOrgRepository.getPubHrvOrgByUserName(Mockito.anyString())).thenReturn(null);
        try {
            entryBoxAppliedOthersServiceImpl.callProApplyBillOperate("", "");
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }
        entryBills = new ArrayList<>();
        dto = new EntryBoxAppliedEntryBillDTO();
        dto.setEntryStatus("SUBMITTED");
        entryBills.add(dto);
        PubHrvOrg pubHrvOrg = new PubHrvOrg();
        pubHrvOrg.setUserName("333");
        PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("Y");
        PowerMockito.when(recalculationConvergenceRepository.getMesEntryBillsByPsApplyBillNo(Mockito.anyString())).thenReturn(entryBills);
        PowerMockito.when(pubHrvOrgRepository.getPubHrvOrgByUserName(Mockito.anyString())).thenReturn(pubHrvOrg);
        try {
            entryBoxAppliedOthersServiceImpl.callProApplyBillOperate("", "");
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }
    }

    @Test
    public void CallApplyBillOperatePlus() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        try {
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("");
            entryBoxAppliedOthersServiceImpl.callApplyBillOperatePlus("", "", null);
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        List<EntryBoxAppliedEntryBillDTO> entryBills = new ArrayList<>();
        EntryBoxAppliedEntryBillDTO dto = new EntryBoxAppliedEntryBillDTO();
        entryBills.add(dto);
        try {
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("898989");
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
            entryBoxAppliedOthersServiceImpl.callApplyBillOperatePlus("", "", entryBills);
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        entryBills = new ArrayList<>();
        dto = new EntryBoxAppliedEntryBillDTO();
        entryBills.add(dto);
        try {
            String result = "{\"code\":{\"code\":\"0005\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":568578,\"other\":";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("898989");
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
            entryBoxAppliedOthersServiceImpl.callApplyBillOperatePlus("", "", entryBills);
        } catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }

        entryBills = new ArrayList<>();
        dto = new EntryBoxAppliedEntryBillDTO();
        entryBills.add(dto);
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":568578,\"other\":";
        try {
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Mockito.anyString())).thenReturn("898989");
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
            entryBoxAppliedOthersServiceImpl.callApplyBillOperatePlus("", "", entryBills);
        }catch (Exception e) {
            Assert.assertEquals(((MesBusinessException) e).getExCode(), "0005");
        }
    }

    @Test
    public void GetTaskisTerminal() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        paramDto.setEntityName("ULTELTE-M20160200727");
        paramDto.setOrganizationID(new Long(635));
        paramDto.setEntityId(new BigDecimal(9653168));
        paramDto.setEntryBillId("5609082");
        paramDto.setLastUpdateBy("10277404");
        paramDto.setEmpNo("10277404");
        paramDto.setEntryNumber("121341");
        List<String> listPallet = new ArrayList<>();
        listPallet.add("C221000069763");
        listPallet.add("C221000069764");
        paramDto.setBoxNumberList(listPallet);
        PowerMockito.when(entryBoxAppliedOthersServiceImpl.isMovePost(any())).thenReturn(null);
        entryBoxAppliedOthersServiceImpl.getOutCheckInfo(paramDto);
        entryBoxAppliedOthersServiceImpl.getTaskisTerminal(paramDto);
        entryBoxAppliedOthersServiceImpl.isEntityStatusPack(paramDto);
        entryBoxAppliedOthersServiceImpl.callPkgCollectDetailsCollectBoxdetails(paramDto);
        entryBoxAppliedOthersServiceImpl.callPkgBoxListDetailsBoxlistDetailsUUID(paramDto);
        entryBoxAppliedOthersServiceImpl.callPkgEntityConfigAssemble(paramDto);

        List<MesEntryReadyLineDTO> dsLines = new ArrayList<>();
        MesEntryReadyLineDTO ds = new MesEntryReadyLineDTO();
        ds.setEntryBillId("5609082");
        ds.setBillNumber("C171109679986");
        ds.setCubage(new BigDecimal(0.0609).toString());
        ds.setPalletId("1835646");
        ds.setGrossWeight(new BigDecimal(17.5).toString());
        ds.setHeight(new BigDecimal(250).toString());
        ds.setLength(new BigDecimal(580).toString());
        ds.setPalletNo("TP1711M031987");
        ds.setPalletVolume(new BigDecimal(1.242).toString());
        ds.setTotalWeight(new BigDecimal(300.5).toString());
        ds.setWidth(new BigDecimal(450).toString());
        dsLines.add(ds);
        //PowerMockito.when(entryBoxAppliedRepository.getAlreadyLines(any())).thenReturn(dsLines);

        entryBoxAppliedOthersServiceImpl.getConvertData(paramDto, null, null, "123");
        List<MesEntryReadyLineDTO> entryReadyLine = new ArrayList<>();
        MesEntryReadyLineDTO mesDTO = new MesEntryReadyLineDTO();
        entryReadyLine.add(mesDTO);
        //paramDto.setTermial(true);
        try {
            entryBoxAppliedOthersServiceImpl.getConvertData(paramDto, entryReadyLine, "", "123");
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length() > 0);
        }
    }

    @Test
    public void getConvertData() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<MesEntryReadyLineDTO> entryReadyLine = new ArrayList<>();
        MesEntryReadyLineDTO dtx = new MesEntryReadyLineDTO();
        dtx.setPalletNo("TP123333");
        dtx.setGrossWeight("1233");
        dtx.setCubage("12333");
        dtx.setTotalWeight("12333");
        dtx.setPalletVolume("789");
        dtx.setPalletId("1");
        dtx.setLength("1");
        dtx.setWidth("1");
        dtx.setHeight("1");
        dtx.setCubage("1");
        dtx.setGrossWeight("1");
        MesEntryReadyLineDTO dtx1 = new MesEntryReadyLineDTO();
        dtx1.setPalletNo("TP123333C");
        dtx1.setGrossWeight("1233");
        dtx1.setCubage("12333");
        dtx1.setTotalWeight("12333");
        dtx1.setPalletVolume("789");
        dtx1.setPalletId("1");
        dtx1.setLength("1");
        dtx1.setWidth("1");
        dtx1.setHeight("1");
        dtx1.setCubage("1");
        dtx1.setGrossWeight("1");
        MesEntryReadyLineDTO dtx2 = new MesEntryReadyLineDTO();
        dtx2.setPalletNo("TP123333C");
        dtx2.setGrossWeight("1233");
        dtx2.setCubage("12333");
        dtx2.setTotalWeight("12333");
        dtx2.setPalletVolume("789");
        dtx2.setPalletId("1");
        dtx2.setLength("1");
        dtx2.setWidth("1");
        dtx2.setHeight("1");
        dtx2.setCubage("1");
        dtx2.setGrossWeight("1");
        entryBoxAppliedOthersServiceImpl.getConvertData(paramDto, entryReadyLine, null, "123");
        entryReadyLine.add(dtx);
        entryReadyLine.add(dtx1);
        entryReadyLine.add(dtx2);
        try {
            entryBoxAppliedOthersServiceImpl.getConvertData(paramDto, entryReadyLine, null, "123");
        } catch (Exception ex) {
            Assert.assertTrue(ex.getMessage().length() > 0);
        }

    }

    @Test
    public void checkParam() throws Exception {

        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        try {
            PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("Y");
            entryBoxAppliedOthersServiceImpl.checkParam(paramDto);
        } catch (MesBusinessException ex) {
            Assert.assertEquals(ex.getExCode(), "0005");
        }

        try {
            PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("N");
            entryBoxAppliedOthersServiceImpl.checkParam(paramDto);
        } catch (MesBusinessException ex) {
            Assert.assertEquals(ex.getExCode(), "0005");
        }

        try {
            paramDto.setEntityName("5GRRU-M20240300901");
            PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("443");
            PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("N");
            entryBoxAppliedOthersServiceImpl.checkParam(paramDto);
        } catch (MesBusinessException ex) {
            Assert.assertEquals(ex.getExCode(), "0005");
        }

        try {
            paramDto.setEntityName("5GNBRRU-M20240400018");
            PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("N");
            PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("");
            entryBoxAppliedOthersServiceImpl.checkParam(paramDto);
        } catch (MesBusinessException ex) {
            Assert.assertEquals(ex.getExCode(), "0005");
        }

        try {
            paramDto.setEntityName("5GRRU-M20240301695");
            PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("N");
            PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("");
            entryBoxAppliedOthersServiceImpl.checkParam(paramDto);
        } catch (MesBusinessException ex) {
            Assert.assertEquals(ex.getExCode(), "0005");
        }

    }

    @Test
    public void getTaskisTerminal() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("Y");
        entryBoxAppliedOthersServiceImpl.getTaskisTerminal(paramDto);
        PowerMockito.when(wmesWerpConEntityTraceRepository.getTaskisTerminal(any())).thenReturn("N");
        Assert.assertFalse(entryBoxAppliedOthersServiceImpl.getTaskisTerminal(paramDto));
    }

    @Test
    public void getOutCheckInfo() {
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        List<String> list = null;
        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("http://srm.zte.com.cn/sppt/eai/GetOutCheckInfoSrv.asmx");
        list = entryBoxAppliedOthersServiceImpl.getOutCheckInfo(paramDto);
        Assert.assertEquals(list.size(), 4);

        PowerMockito.when(mesGetDictInforRepository.getDicDescription(Mockito.anyString())).thenReturn("");
        list = entryBoxAppliedOthersServiceImpl.getOutCheckInfo(paramDto);
        Assert.assertEquals(list.size(), 4);
    }

    @Test
    public void getIntResult() {
        List<String> list = entryBoxAppliedOthersServiceImpl.getIntResult("");
        Assert.assertEquals(list.size(), 4);

        list = entryBoxAppliedOthersServiceImpl.getIntResult("<ProcessStatus>S</ProcessStatus><BillNo>ee</BillNo><Dealer>33</Dealer><ErrorMessage>99</ErrorMessage>");
        Assert.assertEquals(list.size(), 4);

        list = entryBoxAppliedOthersServiceImpl.getIntResult("343535");
        Assert.assertEquals(list.size(), 4);
    }


    @Test
    public void isMovePost() {
        PowerMockito.when(entryBoxAppliedRepository.getBoxupBills(any())).thenReturn(1);
        EntryBoxAppliedDTO paramDto = new EntryBoxAppliedDTO();
        entryBoxAppliedOthersServiceImpl.isMovePost(paramDto);
        PowerMockito.when(entryBoxAppliedRepository.getBoxupBills(any())).thenReturn(0);
        entryBoxAppliedOthersServiceImpl.isMovePost(paramDto);
        List<Integer> iMovedFlag = new ArrayList<>();
        iMovedFlag.add(new Integer(1));
        PowerMockito.when(basBarcodeInfoRepository.getMovedFlag(paramDto)).thenReturn(iMovedFlag);
        Assert.assertNotNull(entryBoxAppliedOthersServiceImpl.isMovePost(paramDto));
    }
}
