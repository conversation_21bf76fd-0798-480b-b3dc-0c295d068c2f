package com.zte.autoTest.unitTest;


import com.alibaba.fastjson.JSONObject;
import com.zte.application.datawb.impl.*;
import com.zte.application.impl.MesGetDictInforServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.*;
import com.zte.interfaces.dto.EntityWeightDTO;
import com.zte.interfaces.dto.EntityWeightInDTO;
import com.zte.interfaces.dto.EntityWeightOutDTO;
import com.zte.interfaces.dto.ItemInfoDTO;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.util.BaseTestCase;
import com.zte.common.CommonUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.runner.RunWith;
import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,HttpClientUtil.class})
public class EntityWeightEstimateServiceImplTest extends BaseTestCase {

    @InjectMocks
    MesGetDictInforServiceImpl mesGetDictInforService;
    @InjectMocks
    EntityWeightEstimateServiceImpl entityWeightEstimateService;

    @Mock
    MesGetDictInforServiceImpl mesGetDictInforService2;
    @Mock
    MesGetDictInforRepository mesGetDictInforRepository;
    @Mock
    EntityWeightEstimateRepository entityWeightEstimateRepository;
    @Before
    public void init(){
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void coverityEntityWeightEstimate001256() throws Exception {
        try {



            PowerMockito.when(entityWeightEstimateRepository.getCount("123")).thenReturn(12L);
            Map<String,Object> in =new HashMap<>(3);
            in.put("uuid","123");
            in.put("page",1);
            in.put("maxPageSize",12);
            List<EntityWeightDTO> dsEntityWeight=new ArrayList<>();
            EntityWeightDTO e=new EntityWeightDTO();
            e.setItemCode("1");
            e.setNetWeightNew("12");
            e.setUnit("g");
            PowerMockito.when(entityWeightEstimateRepository.getPageItem(in)).thenReturn(dsEntityWeight);        ;

            entityWeightEstimateService.saveTempLines("1232");


        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);

        }

    }


    @Test
    public void coverityEntityWeightEstimate00125() throws Exception {
        try {
            List<EntityWeightDTO> itemListMDM=new ArrayList<>();
            entityWeightEstimateService.getMdmInfor("123","123",1,itemListMDM);

            Map<String,Object>  mdmMap=new HashMap<>(2);
            mdmMap.put("totalNumber",12);
            List<EntityWeightDTO> ll=new ArrayList<>();
            EntityWeightDTO ee=new EntityWeightDTO();
            ee.setNetWeight(12.0);
            ee.setItemCode("12323432");
            ll.add(ee);
            mdmMap.put("itemListMDM",ll);
            PowerMockito.when(entityWeightEstimateService.getMmdItems("123", "123",1)).thenReturn(mdmMap);
            //entityWeightEstimateService.getMmdItems("123", "123",1);
            //List<EntityWeightDTO> itemListMDM=new ArrayList<>();
            entityWeightEstimateService.getMdmInfor("123","123",1,itemListMDM);

        }
        catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void coverityEntityWeightEstimate0012() throws Exception {
        try {

            Map<String, Object> map=new HashMap<>(2);
            map.put("DESC5","g");
            map.put("DESC4",2000);
            entityWeightEstimateService.checkInputParam(map);

            map.put("DESC5","kg");
            map.put("DESC4",200);
            entityWeightEstimateService.checkInputParam(map);
        }
        catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }

    }
    @Test
    public void coverityEntityWeightEstimate001() throws Exception {
        try {
            entityWeightEstimateService.saveLog("123","123");
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void coverityEntityWeightEstimate00() throws Exception {
        try {



            EntityWeightDTO p= new EntityWeightDTO();
            p.setJobStatus("123");
            p.setJobEndTime("2022-05-07 09:00:00");
            PowerMockito.when(entityWeightEstimateRepository.selectJobStatus()).thenReturn(p);
            entityWeightEstimateService.checkJob();

            p.setJobEndTime("2022-05-07 14:00:00");
            PowerMockito.when(entityWeightEstimateRepository.selectJobStatus()).thenReturn(p);
            entityWeightEstimateService.checkJob();
        }
        catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void coverityEntityWeightEstimate() throws Exception {
        try {
            //PowerMockito.mockStatic(CommonUtils.class);
            //when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("************");
            e1.setLookupCode("************");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("************");
            e2.setLookupCode("************");
            list.add(e2);
            EntityWeightDTO e3 = new EntityWeightDTO();
            e3.setDescription("************");
            e3.setLookupCode("************");
            list.add(e3);
            EntityWeightDTO e4 = new EntityWeightDTO();
            e4.setDescription("800");
            e4.setLookupCode("************");
            list.add(e4);
            EntityWeightDTO e5 = new EntityWeightDTO();
            e5.setDescription("80");
            e5.setLookupCode("800032000005");
            list.add(e5);
            EntityWeightDTO e6 = new EntityWeightDTO();
            e6.setDescription("800032000006");
            e6.setLookupCode("800032000006");
            list.add(e6);
            EntityWeightDTO e7 = new EntityWeightDTO();
            e7.setDescription("800032000007");
            e7.setLookupCode("800032000007");
            list.add(e7);

            PowerMockito.when(mesGetDictInforRepository.getDict(any())).thenReturn(list);
            mesGetDictInforService.getDict("123");

            Map<String, Object> map = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                map.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }


            PowerMockito.when(mesGetDictInforService2.getDict("123")).thenReturn(map);
            mesGetDictInforService.getDict("123");


            entityWeightEstimateService.paramInCheck(null);

            EntityWeightInDTO params2 = new EntityWeightInDTO();
            params2.setCsFlag("Y");
            entityWeightEstimateService.paramInCheck(params2);

            params2.setTaskNo("123");
            entityWeightEstimateService.paramInCheck(params2);

            params2.setTaskNo("123,");
            entityWeightEstimateService.paramInCheck(params2);


            entityWeightEstimateService.paramInCheck(params2);



            params2.setCsFlag("N");
            EntityWeightDTO p= new EntityWeightDTO();
            p.setJobStatus("123");
            p.setJobEndTime("2022-05-07 09:00:00");
            PowerMockito.when(entityWeightEstimateRepository.selectJobStatus()).thenReturn(p);
            entityWeightEstimateService.checkJob();
            entityWeightEstimateService.paramInCheck(params2);

            entityWeightEstimateService.setOutDto(true, "123", "1233", "123");


            EntityWeightDTO ee = new EntityWeightDTO();
            ee.setUnit("G");
            ee.setNetWeight(33.3);
            List<EntityWeightDTO> e335=new ArrayList<>();
            e335.add(ee);
            //entityWeightEstimateService.saveEntityWeightTempLineMdm(e335);


            List<EntityWeightDTO> itemListPDM = new ArrayList<>();
            EntityWeightDTO e33 = new EntityWeightDTO();
            e33.setNetWeight(12.2);
            itemListPDM.add(e33);

            List<EntityWeightDTO> itemListMDM = new ArrayList<>();
            itemListMDM.add(ee);
            entityWeightEstimateService.saveEntityWeightTempLine(itemListPDM, itemListMDM);


            List<ItemInfoDTO> itemInfoDtoList = new ArrayList<ItemInfoDTO>();
            ItemInfoDTO ii = new ItemInfoDTO();
            ii.setNo("127230950241");
            ii.setVersion("AB");
            itemInfoDtoList.add(ii);
            entityWeightEstimateService.getPmdItems(itemInfoDtoList, "123");


        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void coverityEntityWeight23334() {
        try {
            entityWeightEstimateService.getPage(1,300,"123,123,123","1233");
        }
        catch(Exception e){
            Assert.assertEquals(null,e.getMessage());
        }

    }


    @Test
    public void coverityEntityWeight2333() {
        try {
            entityWeightEstimateService.getPage(1,300,"123,123,123","1233");
        }
        catch(Exception e){
            Assert.assertEquals(null, e.getMessage());
        }

    }


    @Test
    public void coverityEntityWeight23() {
        try {
            List<EntityWeightDTO> dtEntityWeight=new ArrayList<>();
            EntityWeightDTO entityWeightDTO= new EntityWeightDTO();
            entityWeightEstimateService.checkSysn(null);

            dtEntityWeight.add(entityWeightDTO);
            entityWeightEstimateService.checkSysn(dtEntityWeight);
            List<EntityWeightDTO> ddtEntityWeight=new ArrayList<>();
            EntityWeightDTO eentityWeightDTO= new EntityWeightDTO();
            eentityWeightDTO.setItemCode("-1");
            ddtEntityWeight.add(eentityWeightDTO);
            entityWeightEstimateService.checkSysn(ddtEntityWeight);

        } catch (Exception ee) {
            Assert.assertEquals(MessageId.JOB_STOP_SYSN, ee.getMessage());
        }
    }



    @Test
    public void getMdmInfor()throws Exception{
        String strItem="123";
        String uuid="123";
        int curPage=1;
        List<EntityWeightDTO> itemListMDM=new ArrayList<>();
        EntityWeightDTO itemDto=new EntityWeightDTO();
        itemListMDM.add(itemDto);

String s="{\n" +
        "    \"ESB\": {\n" +
        "        \"DATA\": {\n" +
        "            \"DATAINFOS\": {\n" +
        "                \"DATAINFO\": [\n" +
        "                    {\n" +
        "                        \"CODE\": \"100470858\",\n" +
        "                        \"DESC1\": \"446551\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20482\",\n" +
        "                        \"DESC12\": \"TOKO\",\n" +
        "                        \"DESC13\": \"37100200\",\n" +
        "                        \"DESC14\": \"ELFA LIMITED\",\n" +
        "                        \"DESC15\": \"A1100AS-100M\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"QX\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"EAR99\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"0\",\n" +
        "                        \"DESC26\": \"1\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"TOKO\",\n" +
        "                        \"DESC3\": \"\",\n" +
        "                        \"DESC30\": \"ELFA LIMITED\",\n" +
        "                        \"DESC31\": \"A1100AS-100M\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"\",\n" +
        "                        \"DESC5\": \"\",\n" +
        "                        \"DESC6\": \"\",\n" +
        "                        \"DESC7\": \"\",\n" +
        "                        \"DESC8\": \"\",\n" +
        "                        \"DESC9\": \"\",\n" +
        "                        \"UUID\": \"2BA0957FE25B40EEA077D453C4C4669F\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"*********\",\n" +
        "                        \"DESC1\": \"505538\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20469\",\n" +
        "                        \"DESC12\": \"TDK\",\n" +
        "                        \"DESC13\": \"33001300\",\n" +
        "                        \"DESC14\": \"TDK HONGKONG COMPANY LIMITED\",\n" +
        "                        \"DESC15\": \"VLS3015T- 100MR70\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"QX\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"NLR\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"\",\n" +
        "                        \"DESC26\": \"4\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"TDK\",\n" +
        "                        \"DESC3\": \"M07062200013\",\n" +
        "                        \"DESC30\": \"TDK HONGKONG COMPANY LIMITED\",\n" +
        "                        \"DESC31\": \"VLS3015T- 100MR70\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"0.04\",\n" +
        "                        \"DESC5\": \"g\",\n" +
        "                        \"DESC6\": \"3\",\n" +
        "                        \"DESC7\": \"3\",\n" +
        "                        \"DESC8\": \"1.5\",\n" +
        "                        \"DESC9\": \"mm\",\n" +
        "                        \"UUID\": \"3781830C886A4A6BA8A60704D0E361AD\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"*********\",\n" +
        "                        \"DESC1\": \"622464\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20318\",\n" +
        "                        \"DESC12\": \"MURATA\",\n" +
        "                        \"DESC13\": \"33000100\",\n" +
        "                        \"DESC14\": \"MURATA COMPANY LIMITED\",\n" +
        "                        \"DESC15\": \"LQH3NPN100MM0L\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"QX\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"NLR\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"\",\n" +
        "                        \"DESC26\": \"1\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"MURATA\",\n" +
        "                        \"DESC3\": \"\",\n" +
        "                        \"DESC30\": \"MURATA COMPANY LIMITED\",\n" +
        "                        \"DESC31\": \"LQH3NPN100MM0L\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"0.5\",\n" +
        "                        \"DESC5\": \"g\",\n" +
        "                        \"DESC6\": \"3\",\n" +
        "                        \"DESC7\": \"3\",\n" +
        "                        \"DESC8\": \"1.5\",\n" +
        "                        \"DESC9\": \"mm\",\n" +
        "                        \"UUID\": \"B72E3EB024EE44AE87A4F4E436F13941\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"*********\",\n" +
        "                        \"DESC1\": \"446551\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20482\",\n" +
        "                        \"DESC12\": \"TOKO\",\n" +
        "                        \"DESC13\": \"37100201\",\n" +
        "                        \"DESC14\": \"Toko Electronic Mfg. Co., Ltd\",\n" +
        "                        \"DESC15\": \"A1100AS-100M\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"QX\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"EAR99\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"0\",\n" +
        "                        \"DESC26\": \"1\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"TOKO\",\n" +
        "                        \"DESC3\": \"\",\n" +
        "                        \"DESC30\": \"\",\n" +
        "                        \"DESC31\": \"A1100AS-100M\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"\",\n" +
        "                        \"DESC5\": \"\",\n" +
        "                        \"DESC6\": \"\",\n" +
        "                        \"DESC7\": \"\",\n" +
        "                        \"DESC8\": \"\",\n" +
        "                        \"DESC9\": \"\",\n" +
        "                        \"UUID\": \"DFF7E616C0614F2F87F5515CE554C9B7\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"*********\",\n" +
        "                        \"DESC1\": \"446550\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20467\",\n" +
        "                        \"DESC12\": \"TAIYO YUDEN\",\n" +
        "                        \"DESC13\": \"33100703\",\n" +
        "                        \"DESC14\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
        "                        \"DESC15\": \"NR3015T100M\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"QX\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"NLR\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"\",\n" +
        "                        \"DESC26\": \"4\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"TAIYO YUDEN\",\n" +
        "                        \"DESC3\": \"M07011200019\",\n" +
        "                        \"DESC30\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
        "                        \"DESC31\": \"NR3015T100M\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"44.65\",\n" +
        "                        \"DESC5\": \"kg\",\n" +
        "                        \"DESC6\": \"3\",\n" +
        "                        \"DESC7\": \"3\",\n" +
        "                        \"DESC8\": \"1.5\",\n" +
        "                        \"DESC9\": \"mm\",\n" +
        "                        \"UUID\": \"B35FBBE176A74365A4E53D6CDF84B24A\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"*********\",\n" +
        "                        \"DESC1\": \"128652\",\n" +
        "                        \"DESC10\": \"CN\",\n" +
        "                        \"DESC11\": \"14430\",\n" +
        "                        \"DESC12\": \"中兴新地\",\n" +
        "                        \"DESC13\": \"50025900\",\n" +
        "                        \"DESC14\": \"深圳市中兴新地技术股份有限公司\",\n" +
        "                        \"DESC15\": \"G/MJPX13-B2（22）\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"PL\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"CON99\",\n" +
        "                        \"DESC2\": \"051504000024\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"\",\n" +
        "                        \"DESC26\": \"\",\n" +
        "                        \"DESC27\": \"中国\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"ZXXD\",\n" +
        "                        \"DESC3\": \"\",\n" +
        "                        \"DESC30\": \"Sindi Technologies Co.,Ltd.\",\n" +
        "                        \"DESC31\": \"G/MJPX13-B2（22）\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"\",\n" +
        "                        \"DESC5\": \"\",\n" +
        "                        \"DESC6\": \"\",\n" +
        "                        \"DESC7\": \"\",\n" +
        "                        \"DESC8\": \"\",\n" +
        "                        \"DESC9\": \"\",\n" +
        "                        \"UUID\": \"029B39AD25954395AAB39BB7041F812E\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"107624454\",\n" +
        "                        \"DESC1\": \"446550\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20467\",\n" +
        "                        \"DESC12\": \"TAIYO YUDEN\",\n" +
        "                        \"DESC13\": \"12106700\",\n" +
        "                        \"DESC14\": \"WPI INTERNATIONAL （HK） LTD\",\n" +
        "                        \"DESC15\": \"NR3015T100M\",\n" +
        "                        \"DESC16\": \"EXCHO\",\n" +
        "                        \"DESC17\": \"PL\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"NLR\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"\",\n" +
        "                        \"DESC26\": \"4\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"TAIYO YUDEN\",\n" +
        "                        \"DESC3\": \"M07011200019\",\n" +
        "                        \"DESC30\": \"WPI INTERNATIONAL （HK） LTD\",\n" +
        "                        \"DESC31\": \"NR3015T100M\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"44.65\",\n" +
        "                        \"DESC5\": \"g\",\n" +
        "                        \"DESC6\": \"\",\n" +
        "                        \"DESC7\": \"\",\n" +
        "                        \"DESC8\": \"\",\n" +
        "                        \"DESC9\": \"\",\n" +
        "                        \"UUID\": \"31B6157ED3BE4D3DB3A23FD8C22E0F55\"\n" +
        "                    },\n" +
        "                    {\n" +
        "                        \"CODE\": \"*********\",\n" +
        "                        \"DESC1\": \"4125985\",\n" +
        "                        \"DESC10\": \"JP\",\n" +
        "                        \"DESC11\": \"20467\",\n" +
        "                        \"DESC12\": \"TAIYO YUDEN\",\n" +
        "                        \"DESC13\": \"33100703\",\n" +
        "                        \"DESC14\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
        "                        \"DESC15\": \"LM NR 3015T 100M\",\n" +
        "                        \"DESC16\": \"NORMAL\",\n" +
        "                        \"DESC17\": \"PL\",\n" +
        "                        \"DESC18\": \"N/A\",\n" +
        "                        \"DESC19\": \"NLR\",\n" +
        "                        \"DESC2\": \"047020800090\",\n" +
        "                        \"DESC20\": \"0\",\n" +
        "                        \"DESC21\": \"\",\n" +
        "                        \"DESC22\": \"\",\n" +
        "                        \"DESC23\": \"\",\n" +
        "                        \"DESC24\": \"\",\n" +
        "                        \"DESC25\": \"\",\n" +
        "                        \"DESC26\": \"4\",\n" +
        "                        \"DESC27\": \"日本\",\n" +
        "                        \"DESC28\": \"\",\n" +
        "                        \"DESC29\": \"TAIYO YUDEN\",\n" +
        "                        \"DESC3\": \"M20101500018\",\n" +
        "                        \"DESC30\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
        "                        \"DESC31\": \"LM NR 3015T 100M\",\n" +
        "                        \"DESC32\": \"\",\n" +
        "                        \"DESC33\": \"\",\n" +
        "                        \"DESC34\": \"\",\n" +
        "                        \"DESC35\": \"\",\n" +
        "                        \"DESC36\": \"\",\n" +
        "                        \"DESC37\": \"\",\n" +
        "                        \"DESC4\": \"44.65\",\n" +
        "                        \"DESC5\": \"g\",\n" +
        "                        \"DESC6\": \"3\",\n" +
        "                        \"DESC7\": \"3\",\n" +
        "                        \"DESC8\": \"1.5\",\n" +
        "                        \"DESC9\": \"mm\",\n" +
        "                        \"UUID\": \"DB3DD912754642C98AB8F0A70FEC19A2\"\n" +
        "                    }\n" +
        "                ],\n" +
        "                \"PUUID\": \"E6A65F225DE84F32A2ECF0EC775E238E\"\n" +
        "            },\n" +
        "            \"SPLITPAGE\": {\n" +
        "                \"COUNTPERPAGE\": \"100\",\n" +
        "                \"CURRENTPAGE\": \"1\",\n" +
        "                \"TOTALNUMBER\": \"288\",\n" +
        "                \"TOTALPAGES\": \"1\"\n" +
        "            }\n" +
        "        },\n" +
        "        \"DESC\": \"成功查询到8条数据！\",\n" +
        "        \"RESULT\": \"S\"\n" +
        "    }\n" +
        "}";

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(s);
        entityWeightEstimateService.getMdmInfor(strItem,uuid,curPage,itemListMDM);




        String s2="{\n" +
                "    \"ESB\": {\n" +
                "        \"DATA\": {\n" +
                "            \"DATAINFOS\": {\n" +
                "                \"DATAINFO\": [\n" +
                "                    {\n" +
                "                        \"CODE\": \"100470858\",\n" +
                "                        \"DESC1\": \"446551\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20482\",\n" +
                "                        \"DESC12\": \"TOKO\",\n" +
                "                        \"DESC13\": \"37100200\",\n" +
                "                        \"DESC14\": \"ELFA LIMITED\",\n" +
                "                        \"DESC15\": \"A1100AS-100M\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"QX\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"EAR99\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"0\",\n" +
                "                        \"DESC26\": \"1\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"TOKO\",\n" +
                "                        \"DESC3\": \"\",\n" +
                "                        \"DESC30\": \"ELFA LIMITED\",\n" +
                "                        \"DESC31\": \"A1100AS-100M\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"\",\n" +
                "                        \"DESC5\": \"\",\n" +
                "                        \"DESC6\": \"\",\n" +
                "                        \"DESC7\": \"\",\n" +
                "                        \"DESC8\": \"\",\n" +
                "                        \"DESC9\": \"\",\n" +
                "                        \"UUID\": \"2BA0957FE25B40EEA077D453C4C4669F\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"*********\",\n" +
                "                        \"DESC1\": \"505538\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20469\",\n" +
                "                        \"DESC12\": \"TDK\",\n" +
                "                        \"DESC13\": \"33001300\",\n" +
                "                        \"DESC14\": \"TDK HONGKONG COMPANY LIMITED\",\n" +
                "                        \"DESC15\": \"VLS3015T- 100MR70\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"QX\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"NLR\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"\",\n" +
                "                        \"DESC26\": \"4\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"TDK\",\n" +
                "                        \"DESC3\": \"M07062200013\",\n" +
                "                        \"DESC30\": \"TDK HONGKONG COMPANY LIMITED\",\n" +
                "                        \"DESC31\": \"VLS3015T- 100MR70\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"0.04\",\n" +
                "                        \"DESC5\": \"g\",\n" +
                "                        \"DESC6\": \"3\",\n" +
                "                        \"DESC7\": \"3\",\n" +
                "                        \"DESC8\": \"1.5\",\n" +
                "                        \"DESC9\": \"mm\",\n" +
                "                        \"UUID\": \"3781830C886A4A6BA8A60704D0E361AD\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"*********\",\n" +
                "                        \"DESC1\": \"622464\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20318\",\n" +
                "                        \"DESC12\": \"MURATA\",\n" +
                "                        \"DESC13\": \"33000100\",\n" +
                "                        \"DESC14\": \"MURATA COMPANY LIMITED\",\n" +
                "                        \"DESC15\": \"LQH3NPN100MM0L\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"QX\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"NLR\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"\",\n" +
                "                        \"DESC26\": \"1\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"MURATA\",\n" +
                "                        \"DESC3\": \"\",\n" +
                "                        \"DESC30\": \"MURATA COMPANY LIMITED\",\n" +
                "                        \"DESC31\": \"LQH3NPN100MM0L\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"0.5\",\n" +
                "                        \"DESC5\": \"g\",\n" +
                "                        \"DESC6\": \"3\",\n" +
                "                        \"DESC7\": \"3\",\n" +
                "                        \"DESC8\": \"1.5\",\n" +
                "                        \"DESC9\": \"mm\",\n" +
                "                        \"UUID\": \"B72E3EB024EE44AE87A4F4E436F13941\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"*********\",\n" +
                "                        \"DESC1\": \"446551\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20482\",\n" +
                "                        \"DESC12\": \"TOKO\",\n" +
                "                        \"DESC13\": \"37100201\",\n" +
                "                        \"DESC14\": \"Toko Electronic Mfg. Co., Ltd\",\n" +
                "                        \"DESC15\": \"A1100AS-100M\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"QX\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"EAR99\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"0\",\n" +
                "                        \"DESC26\": \"1\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"TOKO\",\n" +
                "                        \"DESC3\": \"\",\n" +
                "                        \"DESC30\": \"\",\n" +
                "                        \"DESC31\": \"A1100AS-100M\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"\",\n" +
                "                        \"DESC5\": \"\",\n" +
                "                        \"DESC6\": \"\",\n" +
                "                        \"DESC7\": \"\",\n" +
                "                        \"DESC8\": \"\",\n" +
                "                        \"DESC9\": \"\",\n" +
                "                        \"UUID\": \"DFF7E616C0614F2F87F5515CE554C9B7\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"*********\",\n" +
                "                        \"DESC1\": \"446550\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20467\",\n" +
                "                        \"DESC12\": \"TAIYO YUDEN\",\n" +
                "                        \"DESC13\": \"33100703\",\n" +
                "                        \"DESC14\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
                "                        \"DESC15\": \"NR3015T100M\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"QX\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"NLR\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"\",\n" +
                "                        \"DESC26\": \"4\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"TAIYO YUDEN\",\n" +
                "                        \"DESC3\": \"M07011200019\",\n" +
                "                        \"DESC30\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
                "                        \"DESC31\": \"NR3015T100M\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"44.65\",\n" +
                "                        \"DESC5\": \"kg\",\n" +
                "                        \"DESC6\": \"3\",\n" +
                "                        \"DESC7\": \"3\",\n" +
                "                        \"DESC8\": \"1.5\",\n" +
                "                        \"DESC9\": \"mm\",\n" +
                "                        \"UUID\": \"B35FBBE176A74365A4E53D6CDF84B24A\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"*********\",\n" +
                "                        \"DESC1\": \"128652\",\n" +
                "                        \"DESC10\": \"CN\",\n" +
                "                        \"DESC11\": \"14430\",\n" +
                "                        \"DESC12\": \"中兴新地\",\n" +
                "                        \"DESC13\": \"50025900\",\n" +
                "                        \"DESC14\": \"深圳市中兴新地技术股份有限公司\",\n" +
                "                        \"DESC15\": \"G/MJPX13-B2（22）\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"PL\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"CON99\",\n" +
                "                        \"DESC2\": \"051504000024\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"\",\n" +
                "                        \"DESC26\": \"\",\n" +
                "                        \"DESC27\": \"中国\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"ZXXD\",\n" +
                "                        \"DESC3\": \"\",\n" +
                "                        \"DESC30\": \"Sindi Technologies Co.,Ltd.\",\n" +
                "                        \"DESC31\": \"G/MJPX13-B2（22）\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"\",\n" +
                "                        \"DESC5\": \"\",\n" +
                "                        \"DESC6\": \"\",\n" +
                "                        \"DESC7\": \"\",\n" +
                "                        \"DESC8\": \"\",\n" +
                "                        \"DESC9\": \"\",\n" +
                "                        \"UUID\": \"029B39AD25954395AAB39BB7041F812E\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"107624454\",\n" +
                "                        \"DESC1\": \"446550\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20467\",\n" +
                "                        \"DESC12\": \"TAIYO YUDEN\",\n" +
                "                        \"DESC13\": \"12106700\",\n" +
                "                        \"DESC14\": \"WPI INTERNATIONAL （HK） LTD\",\n" +
                "                        \"DESC15\": \"NR3015T100M\",\n" +
                "                        \"DESC16\": \"EXCHO\",\n" +
                "                        \"DESC17\": \"PL\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"NLR\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"\",\n" +
                "                        \"DESC26\": \"4\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"TAIYO YUDEN\",\n" +
                "                        \"DESC3\": \"M07011200019\",\n" +
                "                        \"DESC30\": \"WPI INTERNATIONAL （HK） LTD\",\n" +
                "                        \"DESC31\": \"NR3015T100M\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"44.65\",\n" +
                "                        \"DESC5\": \"g\",\n" +
                "                        \"DESC6\": \"\",\n" +
                "                        \"DESC7\": \"\",\n" +
                "                        \"DESC8\": \"\",\n" +
                "                        \"DESC9\": \"\",\n" +
                "                        \"UUID\": \"31B6157ED3BE4D3DB3A23FD8C22E0F55\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"CODE\": \"*********\",\n" +
                "                        \"DESC1\": \"4125985\",\n" +
                "                        \"DESC10\": \"JP\",\n" +
                "                        \"DESC11\": \"20467\",\n" +
                "                        \"DESC12\": \"TAIYO YUDEN\",\n" +
                "                        \"DESC13\": \"33100703\",\n" +
                "                        \"DESC14\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
                "                        \"DESC15\": \"LM NR 3015T 100M\",\n" +
                "                        \"DESC16\": \"NORMAL\",\n" +
                "                        \"DESC17\": \"PL\",\n" +
                "                        \"DESC18\": \"N/A\",\n" +
                "                        \"DESC19\": \"NLR\",\n" +
                "                        \"DESC2\": \"047020800090\",\n" +
                "                        \"DESC20\": \"0\",\n" +
                "                        \"DESC21\": \"\",\n" +
                "                        \"DESC22\": \"\",\n" +
                "                        \"DESC23\": \"\",\n" +
                "                        \"DESC24\": \"\",\n" +
                "                        \"DESC25\": \"\",\n" +
                "                        \"DESC26\": \"4\",\n" +
                "                        \"DESC27\": \"日本\",\n" +
                "                        \"DESC28\": \"\",\n" +
                "                        \"DESC29\": \"TAIYO YUDEN\",\n" +
                "                        \"DESC3\": \"M20101500018\",\n" +
                "                        \"DESC30\": \"HONG KONG TAIYO YUDEN COMPANY LIMITED\",\n" +
                "                        \"DESC31\": \"LM NR 3015T 100M\",\n" +
                "                        \"DESC32\": \"\",\n" +
                "                        \"DESC33\": \"\",\n" +
                "                        \"DESC34\": \"\",\n" +
                "                        \"DESC35\": \"\",\n" +
                "                        \"DESC36\": \"\",\n" +
                "                        \"DESC37\": \"\",\n" +
                "                        \"DESC4\": \"44.65\",\n" +
                "                        \"DESC5\": \"g\",\n" +
                "                        \"DESC6\": \"3\",\n" +
                "                        \"DESC7\": \"3\",\n" +
                "                        \"DESC8\": \"1.5\",\n" +
                "                        \"DESC9\": \"mm\",\n" +
                "                        \"UUID\": \"DB3DD912754642C98AB8F0A70FEC19A2\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"PUUID\": \"E6A65F225DE84F32A2ECF0EC775E238E\"\n" +
                "            },\n" +
                "            \"SPLITPAGE\": {\n" +
                "                \"COUNTPERPAGE\": \"100\",\n" +
                "                \"CURRENTPAGE\": \"1\",\n" +
                "                \"TOTALNUMBER\": \"28\",\n" +
                "                \"TOTALPAGES\": \"1\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"DESC\": \"成功查询到8条数据！\",\n" +
                "        \"RESULT\": \"S\"\n" +
                "    }\n" +
                "}";

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(s2);
        entityWeightEstimateService.getMdmInfor(strItem,uuid,curPage,itemListMDM);


        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": {\"list\":[]}\n" +
                        "}");
        entityWeightEstimateService.getMdmInfor(strItem,uuid,curPage,itemListMDM);
        Assert.assertNotNull(itemListMDM);



    }



    @Test
    public void coverityEntityWeight3() {
        try {


            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("************");
            e1.setLookupCode("************");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("************");
            e2.setLookupCode("************");
            list.add(e2);
            EntityWeightDTO e3 = new EntityWeightDTO();
            e3.setDescription("************");
            e3.setLookupCode("************");
            list.add(e3);
            EntityWeightDTO e4 = new EntityWeightDTO();
            e4.setDescription("0");
            e4.setLookupCode("************");
            list.add(e4);
            EntityWeightDTO e5 = new EntityWeightDTO();
            e5.setDescription("0");
            e5.setLookupCode("800032000005");
            list.add(e5);
            EntityWeightDTO e6 = new EntityWeightDTO();
            e6.setDescription("800032000006");
            e6.setLookupCode("800032000006");
            list.add(e6);
            EntityWeightDTO e7 = new EntityWeightDTO();
            e7.setDescription("800032000007");
            e7.setLookupCode("800032000007");
            list.add(e7);


            Map<String, Object> outMap = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                outMap.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }


            PowerMockito.when(mesGetDictInforService2.getDict("12")).thenReturn(outMap);
            entityWeightEstimateService.getDict();

        } catch (Exception ee) {
            Assert.assertTrue(ee.getMessage().length()>0);
        }
    }


    @Test
    public void coverityEntityWeight33() {
        try {
            //PowerMockito.mockStatic(HttpClientUtil.class);

            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("************");
            e1.setLookupCode("************");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("************");
            e2.setLookupCode("************");
            list.add(e2);
            EntityWeightDTO e3 = new EntityWeightDTO();
            e3.setDescription("************");
            e3.setLookupCode("************");
            list.add(e3);
            EntityWeightDTO e4 = new EntityWeightDTO();
            e4.setDescription("0");
            e4.setLookupCode("************");
            list.add(e4);
            EntityWeightDTO e5 = new EntityWeightDTO();
            e5.setDescription("0");
            e5.setLookupCode("800032000005");
            list.add(e5);
            EntityWeightDTO e6 = new EntityWeightDTO();
            e6.setDescription("800032000006");
            e6.setLookupCode("800032000006");
            list.add(e6);
            EntityWeightDTO e7 = new EntityWeightDTO();
            e7.setDescription("800032000007");
            e7.setLookupCode("800032000007");
            list.add(e7);


            Map<String, Object> map = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                map.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }


            PowerMockito.when(mesGetDictInforRepository.getDict("123")).thenReturn(list);
            mesGetDictInforService.getDict("123");


        } catch (Exception ee) {
            Assert.assertTrue(ee.getMessage().length()>0);
        }
    }


    @Test
    public void coverityEntityWeight5() {
        try {
            List<EntityWeightDTO> dtEntityWeight = new ArrayList<>();
            EntityWeightDTO ee = new EntityWeightDTO();
            ee.setItemCode("1123");
            dtEntityWeight.add(ee);

            entityWeightEstimateService.getPostInforList(dtEntityWeight, "123");

        } catch (Exception ee) {
            Assert.assertEquals(ee.getMessage(), ee.getMessage());
        }
    }

    @Test
    public void coverityEntityWeight665() {
        try {
            List<EntityWeightDTO> dtEntityWeight = new ArrayList<>();
            EntityWeightDTO ee = new EntityWeightDTO();
            ee.setItemCode("01123");
            dtEntityWeight.add(ee);

            entityWeightEstimateService.getPostInforList(dtEntityWeight, "123");

            List<EntityWeightDTO> dt = new ArrayList<>();
            entityWeightEstimateService.getPostInforList(dt, "123");
        } catch (Exception ee) {
            Assert.assertEquals(ee.getMessage(), ee.getMessage());

        }
    }

    @Test
    public void coverityEntityWeight6765() {
        try {
            List<EntityWeightDTO> dt = new ArrayList<>();
            entityWeightEstimateService.getPostInforList(dt, "123");
        } catch (Exception ee) {
            Assert.assertTrue(ee.getMessage().length()>0);
        }
    }


    @Test
    public void coverityEntityWeight55() {
        try {
            List<EntityWeightDTO> dtEntityWeight = new ArrayList<>();
            EntityWeightDTO ee = new EntityWeightDTO();
            ee.setItemCode("1123");
            dtEntityWeight.add(ee);

            entityWeightEstimateService.getItemInforAndSave(dtEntityWeight, "123");

        } catch (Exception ee) {
            Assert.assertEquals(MessageId.SERVICE_IS_ABNORMAL ,ee.getMessage());
        }
    }

    @Test
    public void coverityEntityWeight565() {
        try {
            List<EntityWeightDTO> dtEntityWeight = new ArrayList<>();
            EntityWeightDTO ee = new EntityWeightDTO();
            ee.setItemCode("1123");
            dtEntityWeight.add(ee);
            EntityWeightDTO ee2 = new EntityWeightDTO();
            ee2.setItemCode("1123");
            dtEntityWeight.add(ee2);

            entityWeightEstimateService.getItemInforAndSave(dtEntityWeight, "123");

        } catch (Exception ee) {
            Assert.assertEquals(MessageId.SERVICE_IS_ABNORMAL ,ee.getMessage());
        }
    }

    @Test
    public void coverityEntityWeight6() {
        try {
            List<EntityWeightDTO> dtEntityWeight = new ArrayList<>();
            EntityWeightDTO ee = new EntityWeightDTO();
            ee.setItemCode("1123");
            dtEntityWeight.add(ee);
            EntityWeightDTO ee2 = new EntityWeightDTO();
            ee2.setItemCode("1123");
            dtEntityWeight.add(ee2);

            entityWeightEstimateService.getItemInforAndSave(null, "123");

        } catch (Exception ee) {
            Assert.assertEquals(MessageId.SERVICE_IS_ABNORMAL , ee.getMessage());
        }
    }


    @Test
    public void coverityEntityWeight76() {
        try {

            EntityWeightInDTO params = new EntityWeightInDTO();
            params.setCsFlag("Y");
            entityWeightEstimateService.saveItemInfor(params, "123", "123");

        } catch (Exception ee) {
            Assert.assertEquals(ee.getMessage() , ee.getMessage());
        }
    }

    @Test
    public void coverityEntityWeight78() {
        try {


            EntityWeightInDTO params = new EntityWeightInDTO();
            params.setCsFlag("Y");
            params.setTaskNo("");
            entityWeightEstimateService.entityWeightEstimate(params);
        } catch (Exception e) {
            Assert.assertEquals(null , e.getMessage());
        }
    }


    @Test
    public void coverityEntityWeight99() {
        try {

            Map<String, Object> m = new HashMap<>();
            m.put("DESC4", 12);
            m.put("DESC5", "g");
            m.put("DESC2", "12312321");
            List<Map<String, Object>> mdmItemDTOList = new ArrayList<>();
            mdmItemDTOList.add(m);

            entityWeightEstimateService.saveMdmItems(mdmItemDTOList, "123");


            List<Map<String, Object>> pdmItemDTOList = new ArrayList<>();
            Map<String, Object> a = new HashMap<>();
            Map<String, Object> c = new HashMap<>();
            a.put("PART_NO", "1223232");

            List<Map<String, Object>> basic = new ArrayList<>();
            Map<String, Object> b = new HashMap<>();
            b.put("MAX_WEIGHT", 22);
            basic.add(b);
            c.put("basic", basic);

            pdmItemDTOList.add(a);
            pdmItemDTOList.add(c);
            entityWeightEstimateService.savePdmItems(pdmItemDTOList, "123");
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void coverityEntityWeight8() {
        try {


            List<EntityWeightDTO> list = new ArrayList<>();
            EntityWeightDTO e1 = new EntityWeightDTO();
            e1.setDescription("************");
            e1.setLookupCode("************");
            list.add(e1);
            EntityWeightDTO e2 = new EntityWeightDTO();
            e2.setDescription("************");
            e2.setLookupCode("************");
            list.add(e2);
            EntityWeightDTO e3 = new EntityWeightDTO();
            e3.setDescription("************");
            e3.setLookupCode("************");
            list.add(e3);
            EntityWeightDTO e4 = new EntityWeightDTO();
            e4.setDescription("800");
            e4.setLookupCode("************");
            list.add(e4);
            EntityWeightDTO e5 = new EntityWeightDTO();
            e5.setDescription("80");
            e5.setLookupCode("800032000005");
            list.add(e5);
            EntityWeightDTO e6 = new EntityWeightDTO();
            e6.setDescription("800032000006");
            e6.setLookupCode("800032000006");
            list.add(e6);
            EntityWeightDTO e7 = new EntityWeightDTO();
            e7.setDescription("800032000007");
            e7.setLookupCode("800032000007");
            list.add(e7);

            Map<String, Object> map = new HashMap<String, Object>();
            for (EntityWeightDTO entityWeightDTO : list) {
                map.put(entityWeightDTO.getLookupCode(), entityWeightDTO.getDescription());
            }


            PowerMockito.when(mesGetDictInforService2.getDict("123")).thenReturn(map);


            EntityWeightInDTO params22 = new EntityWeightInDTO();
            params22.setCsFlag("Y");
            params22.setTaskNo("123");
            entityWeightEstimateService.entityWeightEstimate(params22);

        } catch (Exception ee) {
            Assert.assertEquals(ee.getMessage(), ee.getMessage());
        }
    }

}
