package com.zte.autoTest.unitTest;

import com.zte.application.erpdt.impl.ItemListServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.erpdt.ItemListRepository;
import com.zte.interfaces.dto.CFItemSingleDosageDTO;
import com.zte.interfaces.dto.CfInventoryDTO;
import com.zte.interfaces.dto.ItemListEntityDTO;
import com.zte.itp.msa.core.model.ServiceData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
public class ItemListServiceImplTest {
    @InjectMocks
    private ItemListServiceImpl itemListService;
    @Mock
    private ItemListRepository erpTestrepository;

    @Test
    @PrepareForTest({ItemListServiceImpl.class, ItemListRepository.class})
    public void getItemSingleDosage() throws Exception {
        CFItemSingleDosageDTO dto = new CFItemSingleDosageDTO();
        dto.setTaskNo("ZZ河源A1020200731002");
        dto.setItemNo("056471200157");
        when(erpTestrepository.getItemSingleDosage(dto)).thenReturn(dto);
        Assert.assertNotNull(itemListService.getItemSingleDosage(dto));
    }

    @Test
    @PrepareForTest({ItemListServiceImpl.class, ItemListRepository.class})
    public void getTaskNoList() throws Exception {
        CFItemSingleDosageDTO dto = new CFItemSingleDosageDTO();
        Map<String, Object> map = new HashMap<>();
        map.put("taskNo", "ESS89EV2-M20160200283");
        map.put("orgId", "635");
        List<CFItemSingleDosageDTO> SingleDosageList = new ArrayList<>();
        List<Map> list = new ArrayList<>();
        list.add(map);
        map.put(Constant.LIST_STR, list);
        when(erpTestrepository.getTaskNoList(list)).thenReturn(SingleDosageList);
        Assert.assertNotNull(itemListService.getTaskNoList(map));
    }


    @Test
    @PrepareForTest({ItemListServiceImpl.class, ItemListRepository.class})
    public void getItemSingleDosageByTaskItem() throws Exception {
        ItemListEntityDTO dto = new ItemListEntityDTO();
        when(erpTestrepository.getItemiLstTaskItem("aa", "bb")).thenReturn(dto);
        Assert.assertNotNull(itemListService.getItemiLstTaskItem("aa", "bb"));
    }

    @Test
    public void testGetItemUsageCount() throws Exception {
        CfInventoryDTO dto = new CfInventoryDTO();
        dto.setItemNo("056471200157");
        ArrayList<CfInventoryDTO> cfInventoryDTOS = new ArrayList<>();
        cfInventoryDTOS.add(dto);
        when(erpTestrepository.getItemUsageCount(any())).thenReturn(cfInventoryDTOS);
        Assert.assertNotNull(itemListService.getItemUsageCount("123"));
    }

    @Test
    public void getListByTaskList(){
        ItemListEntityDTO dto = new ItemListEntityDTO();
        dto.setItemNo("056471200157");
        when(erpTestrepository.getListByTaskList(any())).thenReturn(Collections.singletonList(dto));
        Assert.assertNotNull(itemListService.getListByTaskList(Collections.singletonList("123")));
    }
}
