package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.datawb.impl.WsmAssembleLinesServiceImpl;
import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtils;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository;
import com.zte.domain.model.datawb.ZmsMesB2bUploadLogRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.CpmConfigItemAssembleDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.EntityWeightDTO;
import com.zte.interfaces.dto.ZmsCbomInfoDTO;
import com.zte.interfaces.dto.ZmsDeviceInventoryDTO;
import com.zte.interfaces.dto.ZmsDeviceInventoryMoveUploadDTO;
import com.zte.interfaces.dto.ZmsDeviceInventoryUploadDTO;
import com.zte.interfaces.dto.ZmsExtendedAttributeDTO;
import com.zte.interfaces.dto.ZmsInputDTO;
import com.zte.interfaces.dto.ZmsMesInfoUploadLogDTO;
import com.zte.interfaces.dto.bytedance.DeviceWholeInventoryDTO;
import com.zte.interfaces.dto.bytedance.MtlMaterialTransactionsDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static com.zte.common.utils.Constant.BYTE_DANCE;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR>
 * @since 2023年7月9日
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsDeviceInventoryUploadServiceImplTest {

    @InjectMocks
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadService;

    @Mock
    private ZmsDeviceInventoryUploadRepository zmsDeviceInventoryUploadRepository;
    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;
    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;
    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;
    @Mock
    private WsmAssembleLinesServiceImpl wsmAssembleLinesService;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;
    @Mock
    private ZmsMesB2bUploadLogRepository zmsMesB2bUploadLogRepository;
    @Mock
    private IdGenerator idGenerator;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
                JacksonJsonConverUtil.class, JSON.class, ServiceDataBuilderUtil.class);
        PowerMockito.field(ZmsDeviceInventoryUploadServiceImpl.class,"deviceWholeInventoryMoveBeforeMin").set(zmsDeviceInventoryUploadService,1);
    }

    /*Started by AICoder, pid:mcc45d4de4zed471475d0bf8f137cc12ac58de99*/
    @Test
    public void testDeviceInventoryUploadSuccess() throws Exception {
        List<ZmsDeviceInventoryDTO> zmsDeviceInventoryDTOList = new ArrayList<>();
        ZmsInputDTO dto = new ZmsInputDTO();
        List<SysLookupValues> sysLookupValuesList = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_202408292001));
        sysLookupValuesList.add(a1);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Constant.LOOKUP_TYPES_8240059))
                .thenReturn(sysLookupValuesList);
        zmsDeviceInventoryUploadService.deviceInventoryUpload(dto);

        List<ZmsDeviceInventoryUploadDTO> partsInventoryList = new LinkedList<>();
        ZmsDeviceInventoryUploadDTO b1 = new ZmsDeviceInventoryUploadDTO();
        partsInventoryList.add(b1);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getPartsInventoryList()).thenReturn(partsInventoryList)
        ;
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getWholeInventoryList(Mockito.anyList())).thenReturn(partsInventoryList)
        ;
        PowerMockito.when(centerfactoryRemoteService.queryLookupValue(Mockito.anyList())).thenReturn(null);
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Mockito.any(), Mockito.any(), Mockito.anyInt(),
                        Mockito.anyInt()))
                .thenReturn("ZTE120240911100041");
        zmsDeviceInventoryUploadService.deviceInventoryUpload(dto);
        Assert.assertTrue(partsInventoryList.size()>0);
    }
    /*Ended by AICoder, pid:mcc45d4de4zed471475d0bf8f137cc12ac58de99*/

    /*Started by AICoder, pid:ge77cnda2c3e17f14c58097d313b306a357444ad*/
    @Test
    public void testDeviceWholeInventoryMoveUploadWithEmptyCustomerMap() throws Exception {
        SysLookupValues sysLookupValues1 =
                new SysLookupValues();
        sysLookupValues1.setLookupType(BigDecimal.valueOf(202408291));
        sysLookupValues1.setLookupCode(BigDecimal.valueOf(202408291));
        sysLookupValues1.setLookupMeaning("meaning1");

        SysLookupValues sysLookupValues2 =
                new SysLookupValues();
        sysLookupValues2.setLookupType(BigDecimal.valueOf(202408292));
        sysLookupValues2.setLookupCode(BigDecimal.valueOf(202408292));
        sysLookupValues2.setLookupMeaning("meaning2");

        List<SysLookupValues> sysLookupValuesList = Arrays.asList(sysLookupValues1, sysLookupValues2);

        DeviceWholeInventoryDTO deviceWholeInventoryDTO = new DeviceWholeInventoryDTO();
        deviceWholeInventoryDTO.setAutoFlag("Y");
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getMessage()));
        }

        when(centerfactoryRemoteService.queryLookupValue(anyList()))
                .thenReturn(sysLookupValuesList);
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION.equals(e.getMessage()));
        }

        deviceWholeInventoryDTO.setLastUpdatedDateEnd(null);
        deviceWholeInventoryDTO.setLastUpdatedDateStart(null);
        deviceWholeInventoryDTO.setAutoFlag("N");
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.PARAMS_CAN_NOT_EMPTY.equals(e.getMessage()));
        }

        deviceWholeInventoryDTO.setLastUpdatedDateStart(new Date());
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.PARAMS_CAN_NOT_EMPTY.equals(e.getMessage()));
        }

        deviceWholeInventoryDTO.setLastUpdatedDateEnd(new Date());
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION.equals(e.getMessage()));
        }

        List<CustomerItemsDTO> customerItemsList = new LinkedList<>();
        CustomerItemsDTO a1 = new CustomerItemsDTO();
        customerItemsList.add(a1);
        CustomerItemsDTO a2 = new CustomerItemsDTO();
        a2.setCustomerCode("123");
        customerItemsList.add(a2);
        PowerMockito.when(centerfactoryRemoteService.queryCustomerItemsInfo(Arrays.asList(BYTE_DANCE)))
                .thenReturn(customerItemsList);
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION.equals(e.getMessage()));
        }

        CustomerItemsDTO a3 = new CustomerItemsDTO();
        a3.setCustomerCode("123");
        a3.setCustomerItemName("342");
        a3.setZteCode("123");
        customerItemsList.add(a3);
        zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);

        deviceWholeInventoryDTO.setAutoFlag("Y");
        sysLookupValues2.setLookupCode(new BigDecimal(202408292001L));
        sysLookupValues2.setLookupMeaning("2024-05-06 12:00:00");
        zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);

        List<MtlMaterialTransactionsDTO> mtlList = new LinkedList<>();
        MtlMaterialTransactionsDTO b1 = new MtlMaterialTransactionsDTO();
        b1.setTransactionId("123");
        b1.setSegment1("123");
        b1.setTransactionQuantity(new BigDecimal(20));
        b1.setTransferSubinventory("123");
        mtlList.add(b1);
        /* Started by AICoder, pid:04e87odcdf0b096140b20b6690efc038f9067648 */
        //branch:StringUtils.isBlank(item.getTransferSubinventory())
        MtlMaterialTransactionsDTO b12 = new MtlMaterialTransactionsDTO();
        b12.setTransactionId("123");
        b12.setSegment1("123");
        b12.setTransactionQuantity(new BigDecimal(20));
        b12.setTransferSubinventory("");
        mtlList.add(b12);
        //branch:!bytedanceQualifiedProductWarehouse.contains(item.getTransferSubinventory())
        MtlMaterialTransactionsDTO b13 = new MtlMaterialTransactionsDTO();
        b13.setTransactionId("123");
        b13.setSegment1("123");
        b13.setTransactionQuantity(new BigDecimal(20));
        b13.setTransferSubinventory("555");
        b13.setSubinventoryCode("234");
        mtlList.add(b13);
        MtlMaterialTransactionsDTO b14 = new MtlMaterialTransactionsDTO();
        b14.setTransactionId("123");
        b14.setSegment1("123");
        b14.setTransactionQuantity(new BigDecimal(-2));
        b14.setTransferSubinventory("555");
        b14.setSubinventoryCode("234");
        mtlList.add(b14);
        //branch:bytedanceDefectiveProductWarehouse.contains(item.getSubinventoryCode())
        MtlMaterialTransactionsDTO b15 = new MtlMaterialTransactionsDTO();
        b15.setTransactionId("123");
        b15.setSegment1("123");
        b15.setTransactionQuantity(new BigDecimal(-2));
        b15.setTransferSubinventory("555");
        b15.setSubinventoryCode("123");
        mtlList.add(b15);
        MtlMaterialTransactionsDTO b16 = new MtlMaterialTransactionsDTO();
        b16.setTransactionId("123");
        b16.setSegment1("123");
        b16.setTransactionQuantity(new BigDecimal(-2));
        b16.setTransferSubinventory("555");
        b16.setSubinventoryCode("333");
        mtlList.add(b16);
        /* Ended by AICoder, pid:04e87odcdf0b096140b20b6690efc038f9067648 */
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Mockito.any(), Mockito.any(), Mockito.anyInt(),
                        Mockito.anyInt()))
                .thenReturn("ZTE120240911100041");
        PowerMockito.when(zmsDeviceInventoryUploadRepository.queryMtlMaterialTransactionPage(Mockito.any()))
                .thenReturn(mtlList);
        List<String> bytedanceDefectiveProductWarehouse = new LinkedList<>();
        bytedanceDefectiveProductWarehouse.add("123");
        List<String> bytedanceQualifiedProductWarehouse = new LinkedList<>();
        bytedanceQualifiedProductWarehouse.add("234");
        PowerMockito.field(ZmsDeviceInventoryUploadServiceImpl.class,"bytedanceDefectiveProductWarehouse")
                        .set(zmsDeviceInventoryUploadService, bytedanceDefectiveProductWarehouse);
        PowerMockito.field(ZmsDeviceInventoryUploadServiceImpl.class,"bytedanceQualifiedProductWarehouse")
                        .set(zmsDeviceInventoryUploadService, bytedanceQualifiedProductWarehouse);
        zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);

        MtlMaterialTransactionsDTO b2 = new MtlMaterialTransactionsDTO();
        b2.setSegment1("124");
        b2.setTransactionId("234");
        b2.setSubinventoryCode("123");
        b2.setTransactionReference("234");
        b2.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b2);
        zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);

        MtlMaterialTransactionsDTO b3 = new MtlMaterialTransactionsDTO();
        b3.setSegment1("124");
        b3.setTransactionId("12333");
        b3.setSubinventoryCode("123");
        b3.setTransactionReference("234");
        b3.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b3);
        MtlMaterialTransactionsDTO b4 = new MtlMaterialTransactionsDTO();
        b4.setSegment1("124");
        b4.setTransactionId("123331");
        b4.setSubinventoryCode("234");
        b4.setTransactionReference("234");
        b4.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b4);
        MtlMaterialTransactionsDTO b5 = new MtlMaterialTransactionsDTO();
        b5.setSegment1("124");
        b5.setTransactionId("1233312");
        b5.setSubinventoryCode("234");
        b5.setTransferSubinventory("123");
        b5.setTransactionReference("234");
        b5.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b5);
        MtlMaterialTransactionsDTO b5b = new MtlMaterialTransactionsDTO();
        b5b.setSegment1("124");
        b5b.setTransactionId("1233312");
        b5b.setSubinventoryCode("234");
        b5b.setTransferSubinventory("234");
        b5b.setTransactionReference("234");
        b5b.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b5b);
        MtlMaterialTransactionsDTO b6 = new MtlMaterialTransactionsDTO();
        b6.setSegment1("124");
        b6.setTransactionId("1233312");
        b6.setSubinventoryCode("123");
        b6.setTransferSubinventory("123");
        b6.setTransactionReference("234");
        b6.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b6);
        MtlMaterialTransactionsDTO b7 = new MtlMaterialTransactionsDTO();
        b7.setSegment1("124");
        b7.setTransactionId("1233312");
        b7.setSubinventoryCode("123");
        b7.setTransferSubinventory("234");
        b7.setTransactionReference("234");
        b7.setTransactionQuantity(new BigDecimal(-2));
        mtlList.add(b7);
        MtlMaterialTransactionsDTO b8 = new MtlMaterialTransactionsDTO();
        b8.setSegment1("124");
        b8.setTransactionId("1233312");
        b8.setSubinventoryCode("234");
        b8.setTransferSubinventory("234");
        b8.setTransactionReference("234");
        b8.setTransactionQuantity(new BigDecimal(2));
        mtlList.add(b8);
        MtlMaterialTransactionsDTO b9 = new MtlMaterialTransactionsDTO();
        b9.setSegment1("124");
        b9.setTransactionId("1233312");
        b9.setSubinventoryCode("234");
        b9.setTransferSubinventory("123");
        b9.setTransactionReference("234");
        b9.setTransactionQuantity(new BigDecimal(2));
        mtlList.add(b9);
        MtlMaterialTransactionsDTO b10 = new MtlMaterialTransactionsDTO();
        b10.setSegment1("124");
        b10.setTransactionId("1233312");
        b10.setSubinventoryCode("123");
        b10.setTransferSubinventory("123");
        b10.setTransactionReference("234");
        b10.setTransactionQuantity(new BigDecimal(2));
        mtlList.add(b10);
        MtlMaterialTransactionsDTO b11 = new MtlMaterialTransactionsDTO();
        b11.setSegment1("124");
        b11.setTransactionId("1233312");
        b11.setSubinventoryCode("123");
        b11.setTransferSubinventory("234");
        b11.setTransactionReference("234");
        b11.setTransactionQuantity(new BigDecimal(2));
        mtlList.add(b11);

        PowerMockito.when(zmsMesB2bUploadLogRepository.selectTransactionIdList(Mockito.any())).thenReturn(Arrays.asList("12333"));
        zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
    }

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testDeviceWholeInventoryMoveUploadEmptySysLookupValuesList()
            throws Exception {
        when(centerfactoryRemoteService.queryLookupValue(anyList()))
                .thenReturn(Collections.emptyList());

        DeviceWholeInventoryDTO deviceWholeInventoryDTO = new DeviceWholeInventoryDTO();
        zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
    }

    @Test
    public void testDeviceWholeInventoryMoveUploadWithValidData() throws Exception {
        SysLookupValues sysLookupValues1 =
                new SysLookupValues();
        sysLookupValues1.setLookupType(BigDecimal.valueOf(202408291));
        sysLookupValues1.setLookupCode(BigDecimal.valueOf(202408291));
        sysLookupValues1.setLookupMeaning("meaning1");

        SysLookupValues sysLookupValues2 =
                new SysLookupValues();
        sysLookupValues2.setLookupType(BigDecimal.valueOf(202408292));
        sysLookupValues2.setLookupCode(BigDecimal.valueOf(202408292));
        sysLookupValues2.setLookupMeaning("meaning2");

        List<SysLookupValues> sysLookupValuesList = Arrays.asList(sysLookupValues1, sysLookupValues2);

        when(centerfactoryRemoteService.queryLookupValue(anyList()))
                .thenReturn(sysLookupValuesList);

        DeviceWholeInventoryDTO deviceWholeInventoryDTO = new DeviceWholeInventoryDTO();
        deviceWholeInventoryDTO.setAutoFlag("Y");
        try {
            zmsDeviceInventoryUploadService.deviceWholeInventoryMoveUpload(deviceWholeInventoryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION.equals(e.getMessage()));
        }
    }
    /*Ended by AICoder, pid:ge77cnda2c3e17f14c58097d313b306a357444ad*/

    @Test
    public void deviceInventoryUpload() throws Exception {

        ZmsInputDTO dto = new ZmsInputDTO();
        dto.setEmpNo("5323523");
        List<ZmsDeviceInventoryDTO> zmsDeviceInventoryDTOList = new ArrayList<>();
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("131241");
        sysLookupValues.setLookupCode(new BigDecimal("824005900001"));
        sysLookupValuesList.add(sysLookupValues);
        List<EntityWeightDTO> list = new ArrayList<>();
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(list);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getDeviceInventoryList(Mockito.any())).thenReturn(zmsDeviceInventoryDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "deviceInventoryUpload", dto);

        ZmsDeviceInventoryDTO zmsDeviceInventoryDTO = new ZmsDeviceInventoryDTO();
        zmsDeviceInventoryDTO.setEntityId("1111");
        zmsDeviceInventoryDTO.setProdOrderNo("111111");
        zmsDeviceInventoryDTO.setUserAddress("234252");
        zmsDeviceInventoryDTOList.add(zmsDeviceInventoryDTO);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getDeviceInventoryList(Mockito.any())).thenReturn(zmsDeviceInventoryDTOList);

        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("111111");
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        PowerMockito.when(zmsDeviceInventoryUploadService.getMaterialInfo(entityNameList, dto)).thenReturn(zmsCbomInfoDTOList);
        String dataTransferBatchNo = "ZTE120240709200001";
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Constant.MES_BATCHNO, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY, NumConstant.NUM_ONE, NumConstant.NUM_SIX)).thenReturn(dataTransferBatchNo);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);
        List<ZmsDeviceInventoryUploadDTO> zmsDeviceInventoryUploadDTOList = new ArrayList<>();
        ZmsDeviceInventoryUploadDTO zmsDeviceInventoryUploadDTO = new ZmsDeviceInventoryUploadDTO();
        zmsDeviceInventoryUploadDTO.setMaterialDesc("345252");
        zmsDeviceInventoryUploadDTO.setOdmPlantCode("235252");
        zmsDeviceInventoryUploadDTOList.add(zmsDeviceInventoryUploadDTO);
        EntityWeightDTO ewDTO = new EntityWeightDTO();
        ewDTO.setDescription("1111");
        list.add(ewDTO);
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "deviceInventoryUpload", dto);

        String responseStr = "{\"bo\":[{\"cbomCode\":\"testCbom\",\"cbomExtKey\":\"53w7-luqv6hsw -6145\",\"cbomId\":\"6145\",\"cbomNameCn\":\"终极测试SU迁移\",\"cbomNameEn\":\"终极测试SU迁移en\",\"customerId\":\"53w7-luqv6hsw \",\"deviceClass\":\"hardware\",\"lastUpdateDate\":\"5/1/20 3:54 PM\",\"lastUpdatedBy\":\"200308120933\",\"quotationMaterialCode\":\"**********\",\"quotationMaterialNameCn\":\"终极测试SU迁移\",\"quotationMaterialNameEn\":\"终极测试SU迁移EN\",\"unitCn\":\"台\",\"unitEn\":\"PCS\"}],\"code\":{\"code\":\"0000\",\"msg\":\"Success\",\"msgId\":\"RetCode.Success\"},\"responseRule\":\"msa\"}";
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("111111");
        zmsCbomInfoDTO.setCbomNameCn("111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO);
        ZmsCbomInfoDTO zmsCbomInfoDTO3 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO3.setEntityName("111111");
        zmsCbomInfoDTO3.setCbomNameCn("111111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO3);
        ZmsCbomInfoDTO zmsCbomInfoDTO4 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO4.setEntityName("111111");
        zmsCbomInfoDTO4.setCbomNameCn("2222222");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO4);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        //PowerMockito.when(zmsDeviceInventoryUploadService.getMaterialInfo(entityNameList, dto)).thenReturn(zmsCbomInfoDTOList);
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Constant.MES_BATCHNO, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY, NumConstant.NUM_ONE, NumConstant.NUM_SIX)).thenReturn(dataTransferBatchNo);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "deviceInventoryUpload", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getLookupValuesList() throws Exception {

        ZmsInputDTO dto = new ZmsInputDTO();
        dto.setMaterialCode("1111");
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getLookupValuesList", dto);

        sysLookupValuesList.clear();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("131241");
        sysLookupValues.setLookupCode(new BigDecimal("824005900000"));
        sysLookupValuesList.add(sysLookupValues);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("131241");
        sysLookupValues1.setLookupCode(new BigDecimal("824005900001"));
        sysLookupValuesList.add(sysLookupValues1);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupMeaning("131241");
        sysLookupValues2.setLookupCode(new BigDecimal("824005900002"));
        sysLookupValuesList.add(sysLookupValues2);
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupMeaning("131241");
        sysLookupValues3.setLookupCode(new BigDecimal("824005900003"));
        sysLookupValuesList.add(sysLookupValues3);
        SysLookupValues sysLookupValues4 = new SysLookupValues();
        sysLookupValues4.setLookupMeaning("131241");
        sysLookupValues4.setLookupCode(new BigDecimal("824005900004"));
        sysLookupValuesList.add(sysLookupValues4);
        SysLookupValues sysLookupValues5 = new SysLookupValues();
        sysLookupValues5.setLookupMeaning("131241");
        sysLookupValues5.setLookupCode(new BigDecimal("824005900005"));
        sysLookupValuesList.add(sysLookupValues5);
        SysLookupValues sysLookupValues6 = new SysLookupValues();
        sysLookupValues6.setLookupMeaning("131241");
        sysLookupValues6.setLookupCode(new BigDecimal("824005900006"));
        sysLookupValuesList.add(sysLookupValues6);
        SysLookupValues sysLookupValues7 = new SysLookupValues();
        sysLookupValues7.setLookupMeaning("131241");
        sysLookupValues7.setLookupCode(new BigDecimal("824005900007"));
        sysLookupValuesList.add(sysLookupValues7);
        SysLookupValues sysLookupValues8 = new SysLookupValues();
        sysLookupValues8.setLookupMeaning("131241");
        sysLookupValues8.setLookupCode(new BigDecimal("824005900008"));
        sysLookupValuesList.add(sysLookupValues8);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getLookupValuesList", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getDataTransferBatchNo() throws Exception {

        String dataTransferBatchNo = "ZTE120240709200001";
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Constant.MES_BATCHNO, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY, NumConstant.NUM_ONE, NumConstant.NUM_SIX)).thenReturn(dataTransferBatchNo);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getDataTransferBatchNo");
        Assert.assertNotNull(dataTransferBatchNo);
    }


    @Test
    public void getMaterialInfo() throws Exception {

        ZmsInputDTO dto = new ZmsInputDTO();
        dto.setDescLike("服务器DZH");
        dto.setEmpNo("5323523");
        List<String> entityNameList = new ArrayList<>();
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getMaterialInfo", entityNameList, dto);

        entityNameList.add("1111111");
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(configDescList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getMaterialInfo", entityNameList, dto);

        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("1111111");
        zmsCbomInfoDTO.setCbomNameCn("2222222");
        configDescList.add(zmsCbomInfoDTO);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getConfigDesc(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(configDescList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getMaterialInfo", entityNameList, dto);

        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        String responseStr = "{\"bo\":[{\"cbomCode\":\"testCbom\",\"cbomExtKey\":\"53w7-luqv6hsw -6145\",\"cbomId\":\"6145\",\"cbomNameCn\":\"终极测试SU迁移\",\"cbomNameEn\":\"终极测试SU迁移en\",\"customerId\":\"53w7-luqv6hsw \",\"deviceClass\":\"hardware\",\"lastUpdateDate\":\"5/1/20 3:54 PM\",\"lastUpdatedBy\":\"200308120933\",\"quotationMaterialCode\":\"**********\",\"quotationMaterialNameCn\":\"终极测试SU迁移\",\"quotationMaterialNameEn\":\"终极测试SU迁移EN\",\"unitCn\":\"台\",\"unitEn\":\"PCS\"}],\"code\":{\"code\":\"0000\",\"msg\":\"Success\",\"msgId\":\"RetCode.Success\"},\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getMaterialInfo", entityNameList, dto);

        ZmsCbomInfoDTO zmsCbomInfoDTO2 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO2.setEntityName("1111111");
        zmsCbomInfoDTO2.setCbomNameCn("2222222");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO2);
        ZmsCbomInfoDTO zmsCbomInfoDTO3 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO3.setEntityName("1111111");
        zmsCbomInfoDTO3.setCbomNameCn("3333333");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO3);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getMaterialInfo", entityNameList, dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getCbomInfo() throws Exception {

        ZmsInputDTO dto = new ZmsInputDTO();
        dto.setDescLike("服务器DZH");
        dto.setEmpNo("5323523");
        dto.setCpqdUrl("https://configuat.zte.com.cn:8443/zte-iss-cpqd-sucore/cbom/queryByCustomerCode");
        List<ZmsCbomInfoDTO> configDescList = new ArrayList<>();
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("1111111");
        zmsCbomInfoDTO.setCbomNameCn("2222222");
        configDescList.add(zmsCbomInfoDTO);
        ZmsCbomInfoDTO zmsCbomInfoDTO2 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO2.setEntityName("2222222");
        zmsCbomInfoDTO2.setCbomNameCn("终极测试SU迁移");
        configDescList.add(zmsCbomInfoDTO2);

        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        String responseStr = "{\"bo\":[{\"cbomCode\":\"testCbom\",\"cbomExtKey\":\"53w7-luqv6hsw -6145\",\"cbomId\":\"6145\",\"cbomNameCn\":\"终极测试SU迁移\",\"cbomNameEn\":\"终极测试SU迁移en\",\"customerId\":\"53w7-luqv6hsw \",\"deviceClass\":\"hardware\",\"lastUpdateDate\":\"5/1/20 3:54 PM\",\"lastUpdatedBy\":\"200308120933\",\"quotationMaterialCode\":\"**********\",\"quotationMaterialNameCn\":\"终极测试SU迁移\",\"quotationMaterialNameEn\":\"终极测试SU迁移EN\",\"unitCn\":\"台\",\"unitEn\":\"PCS\"}],\"code\":{\"code\":\"0000\",\"msg\":\"Success\",\"msgId\":\"RetCode.Success\"},\"responseRule\":\"msa\"}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("");
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getCbomInfo", configDescList, dto);

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getCbomInfo", configDescList, dto);

        ZmsCbomInfoDTO zmsCbomInfoDTO3 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO3.setCbomNameCn("111111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO3);
        ZmsCbomInfoDTO zmsCbomInfoDTO4 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO4.setCbomNameCn("2222222");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO4);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getCbomInfo", configDescList, dto);


        Assert.assertNotNull(dto);
    }

    @Test
    public void getServerSn() throws Exception {

        ZmsDeviceInventoryDTO zmsDeviceInventoryDTO = new ZmsDeviceInventoryDTO();
        zmsDeviceInventoryDTO.setEntityId("2342352");
        zmsDeviceInventoryDTO.setUserAddress("342523");

        List<CpmConfigItemAssembleDTO> configDetailDTOList = new ArrayList<>();
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(Mockito.any())).thenReturn(configDetailDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);

        CpmConfigItemAssembleDTO cpmConfigItemAssembleDTO = new CpmConfigItemAssembleDTO();
        cpmConfigItemAssembleDTO.setItemCode("22222");
        cpmConfigItemAssembleDTO.setItemBarcode("2342152526");
        configDetailDTOList.add(cpmConfigItemAssembleDTO);
        PowerMockito.when(wsmAssembleLinesService.getAssemblyMaterialsByEntityId(Mockito.any())).thenReturn(configDetailDTOList);
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("23252");
        customerItemsDTO.setZteCode("22222");
        customerItemsDTOList.add(customerItemsDTO);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);

        customerItemsDTOList.clear();
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setProjectType("3");
        customerItemsDTO2.setZteCode("22222");
        customerItemsDTOList.add(customerItemsDTO2);
        CustomerItemsDTO customerItemsDTO3 = new CustomerItemsDTO();
        customerItemsDTO3.setProjectType("3");
        customerItemsDTO3.setZteCode("22222");
        customerItemsDTOList.add(customerItemsDTO3);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);

        customerItemsDTOList.clear();
        CustomerItemsDTO customerItemsDTO4 = new CustomerItemsDTO();
        customerItemsDTO4.setProjectType("3");
        customerItemsDTO4.setZteCode("33333");
        customerItemsDTOList.add(customerItemsDTO4);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);

        customerItemsDTOList.clear();
        CustomerItemsDTO customerItemsDTO5 = new CustomerItemsDTO();
        customerItemsDTO5.setProjectType("3");
        customerItemsDTO5.setZteCode("22222");
        customerItemsDTOList.add(customerItemsDTO5);
        PowerMockito.when(centerfactoryRemoteService.getCustomerItemsInfo(Mockito.any(), Mockito.any())).thenReturn(customerItemsDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getServerSn", zmsDeviceInventoryDTO);

        Assert.assertNotNull(zmsDeviceInventoryDTO);
    }

    @Test
    public void assemblyMaterialInfo() throws Exception {

        ZmsInputDTO dto = new ZmsInputDTO();
        dto.setEmpNo("24242");
        dto.setMaterialCode("11111");
        dto.setMaterialDesc("22222");
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setCbomNameCn("2222");
        zmsCbomInfoDTO.setCbomCode("111");
        ZmsDeviceInventoryUploadDTO zmsDeviceInventoryUploadDTO = new ZmsDeviceInventoryUploadDTO();
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "assemblyMaterialInfo", zmsDeviceInventoryUploadDTO, zmsCbomInfoDTO, dto);

        List<ZmsExtendedAttributeDTO> zmsExtendedAttributeDTOList = new ArrayList<>();
        ZmsExtendedAttributeDTO zmsExtendedAttributeDTO = new ZmsExtendedAttributeDTO();
        zmsExtendedAttributeDTO.setCbomExtentionZh("33333");
        zmsExtendedAttributeDTO.setCbomExtentionValue("33333");
        zmsExtendedAttributeDTOList.add(zmsExtendedAttributeDTO);
        zmsCbomInfoDTO.setExtendedAttributes(zmsExtendedAttributeDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "assemblyMaterialInfo", zmsDeviceInventoryUploadDTO, zmsCbomInfoDTO, dto);

        ZmsExtendedAttributeDTO zmsExtendedAttributeDTO2 = new ZmsExtendedAttributeDTO();
        zmsExtendedAttributeDTO2.setCbomExtentionZh("11111");
        zmsExtendedAttributeDTO2.setCbomExtentionValue("11111");
        zmsExtendedAttributeDTOList.add(zmsExtendedAttributeDTO2);
        ZmsExtendedAttributeDTO zmsExtendedAttributeDTO3 = new ZmsExtendedAttributeDTO();
        zmsExtendedAttributeDTO3.setCbomExtentionZh("22222");
        zmsExtendedAttributeDTO3.setCbomExtentionValue("22222");
        zmsExtendedAttributeDTOList.add(zmsExtendedAttributeDTO3);
        zmsCbomInfoDTO.setExtendedAttributes(zmsExtendedAttributeDTOList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "assemblyMaterialInfo", zmsDeviceInventoryUploadDTO, zmsCbomInfoDTO, dto);

        Assert.assertNotNull(zmsCbomInfoDTO);
    }

    @Test
    public void getDeviceInventoryData() throws Exception {

        ZmsInputDTO zmsInputDTO = new ZmsInputDTO();
        zmsInputDTO.setEmpNo("23423523");
        String dataTransferBatchNo = "ZTE120240709200001";
        List<ZmsDeviceInventoryUploadDTO> zmsDeviceInventoryUploadDTOList = new ArrayList<>();

        ZmsDeviceInventoryUploadDTO zmsDeviceInventoryUploadDTO = new ZmsDeviceInventoryUploadDTO();
        zmsDeviceInventoryUploadDTO.setMaterialDesc("345252");
        zmsDeviceInventoryUploadDTO.setOdmPlantCode("235252");
        zmsDeviceInventoryUploadDTOList.add(zmsDeviceInventoryUploadDTO);

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId("34252");
        customerDataLogDTO.setOrigin("MES");
        dataList.add(customerDataLogDTO);

        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setSn("235262");
        zmsMesInfoUploadLogDTO.setMessageType("23523623");
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadLogDTO);

        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);

        Assert.assertNotNull(zmsInputDTO);
    }

    /* Started by AICoder, pid:ef3f0w515f92aa014da20b13e0cd3a730e54c45c */
    @Test
    public void getSysLookupValuesList() throws Exception {

        SysLookupValues dto = new SysLookupValues();
        dto.setLookupType(new BigDecimal("8240063"));
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1111");
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "getSysLookupValuesList", dto);

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:ef3f0w515f92aa014da20b13e0cd3a730e54c45c */

    /*Started by AICoder, pid:q6ae4df07833a2b14224085b701ed16050a8be8a*/
    @Test(timeout = 8000)
    public void testGetDataTransferBatchNo_Empty() throws Exception {
        String dataTransferBatchNo = "";
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(dataTransferBatchNo);
        try {
            zmsDeviceInventoryUploadService.getDataTransferBatchNo();
        } catch (Exception e) {
            Assert.assertEquals(RetCode.BUSINESSERROR_MSGID, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void testGetDataTransferBatchNo_NonEmpty() throws Exception {
        String dataTransferBatchNo = "20210812123456";
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(dataTransferBatchNo);
        String result = zmsDeviceInventoryUploadService.getDataTransferBatchNo();
        assertEquals("20210812123416", result);
    }
    /*Ended by AICoder, pid:q6ae4df07833a2b14224085b701ed16050a8be8a*/

	@Test
    public void deviceInventoryMoveUpload() throws Exception {

        ZmsInputDTO dto = new ZmsInputDTO();
        dto.setEmpNo("5323523");
        List<ZmsDeviceInventoryMoveUploadDTO> zmsDeviceInventoryMoveUploadDTOS = new ArrayList<>();
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("131241");
        sysLookupValues.setLookupCode(new BigDecimal("824005900001"));
        sysLookupValuesList.add(sysLookupValues);
        List<EntityWeightDTO> list = new ArrayList<>();
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(list);
        PowerMockito.when(wsmAssembleLinesService.getSysLookupValues(Mockito.any())).thenReturn(sysLookupValuesList);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getDeviceInventoryMoveList(Mockito.any())).thenReturn(zmsDeviceInventoryMoveUploadDTOS);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "deviceInventoryMoveUpload", dto);

        ZmsDeviceInventoryMoveUploadDTO zmsDeviceInventoryMoveUploadDTO = new ZmsDeviceInventoryMoveUploadDTO();
        zmsDeviceInventoryMoveUploadDTO.setSourceOrder("111111");
        zmsDeviceInventoryMoveUploadDTOS.add(zmsDeviceInventoryMoveUploadDTO);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.getDeviceInventoryMoveList(Mockito.any())).thenReturn(zmsDeviceInventoryMoveUploadDTOS);

        List<String> entityNameList = new ArrayList<>();
        entityNameList.add("111111");
        List<ZmsCbomInfoDTO> zmsCbomInfoDTOList = new ArrayList<>();
        PowerMockito.when(zmsDeviceInventoryUploadService.getMaterialInfo(entityNameList, dto)).thenReturn(zmsCbomInfoDTOList);
        String dataTransferBatchNo = "ZTE120240709200001";
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Constant.MES_BATCHNO, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY, NumConstant.NUM_ONE, NumConstant.NUM_SIX)).thenReturn(dataTransferBatchNo);

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId("34252");
        customerDataLogDTO.setOrigin("MES");
        dataList.add(customerDataLogDTO);

        List<ZmsMesInfoUploadLogDTO> zmsMesInfoUploadDTOList = new ArrayList<>();
        ZmsMesInfoUploadLogDTO zmsMesInfoUploadLogDTO = new ZmsMesInfoUploadLogDTO();
        zmsMesInfoUploadLogDTO.setSn("235262");
        zmsMesInfoUploadLogDTO.setMessageType("23523623");
        zmsMesInfoUploadDTOList.add(zmsMesInfoUploadLogDTO);
        EntityWeightDTO ewDTO = new EntityWeightDTO();
        ewDTO.setDescription("1111");
        list.add(ewDTO);
        PowerMockito.when(mesGetDictInforRepository.getDict(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "deviceInventoryMoveUpload", dto);

        String responseStr = "{\"bo\":[{\"cbomCode\":\"testCbom\",\"cbomExtKey\":\"53w7-luqv6hsw -6145\",\"cbomId\":\"6145\",\"cbomNameCn\":\"终极测试SU迁移\",\"cbomNameEn\":\"终极测试SU迁移en\",\"customerId\":\"53w7-luqv6hsw \",\"deviceClass\":\"hardware\",\"lastUpdateDate\":\"5/1/20 3:54 PM\",\"lastUpdatedBy\":\"200308120933\",\"quotationMaterialCode\":\"**********\",\"quotationMaterialNameCn\":\"终极测试SU迁移\",\"quotationMaterialNameEn\":\"终极测试SU迁移EN\",\"unitCn\":\"台\",\"unitEn\":\"PCS\"}],\"code\":{\"code\":\"0000\",\"msg\":\"Success\",\"msgId\":\"RetCode.Success\"},\"responseRule\":\"msa\"}";
        ZmsCbomInfoDTO zmsCbomInfoDTO = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO.setEntityName("111111");
        zmsCbomInfoDTO.setCbomNameCn("111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO);
        ZmsCbomInfoDTO zmsCbomInfoDTO3 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO3.setEntityName("111111");
        zmsCbomInfoDTO3.setCbomNameCn("111111");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO3);
        ZmsCbomInfoDTO zmsCbomInfoDTO4 = new ZmsCbomInfoDTO();
        zmsCbomInfoDTO4.setEntityName("111111");
        zmsCbomInfoDTO4.setCbomNameCn("2222222");
        zmsCbomInfoDTOList.add(zmsCbomInfoDTO4);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(responseStr);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"bo\":\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(zmsCbomInfoDTOList);
        PowerMockito.when(centerfactoryRemoteService.createBillNo(Constant.MES_BATCHNO, DateUtils.DATE_FORMAT_YEAR_MONTH_DAY, NumConstant.NUM_ONE, NumConstant.NUM_SIX)).thenReturn(dataTransferBatchNo);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "insertMesInfoUploadLog", zmsMesInfoUploadDTOList);
        Whitebox.invokeMethod(zmsIndicatorUploadService, "pushDataToB2B", dataList);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "deviceInventoryMoveUpload", dto);

        Assert.assertNotNull(dto);
    }

    /* Started by AICoder, pid:18fbd9777dy4246144680994e0a1795cf4591f91 */
    @Test
    public void updateCpmContractEntitiesUpload() throws Exception {

        ZmsMesInfoUploadLogDTO dto = new ZmsMesInfoUploadLogDTO();
        dto.setUploadStatus("0");
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "updateCpmContractEntitiesUpload", dto);

        dto.setUploadStatus("1");
        dto.setJsonData("{\"body\":[]}");
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "updateCpmContractEntitiesUpload", dto);

        List<ZmsDeviceInventoryMoveUploadDTO> list = new ArrayList<>();
        list.add(new ZmsDeviceInventoryMoveUploadDTO());
        String jsonData = "{\"body\":" + new ObjectMapper().writeValueAsString(list) + "}";
        dto.setJsonData(jsonData);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "updateCpmContractEntitiesUpload", dto);

        ZmsDeviceInventoryMoveUploadDTO zmsDeviceInventoryMoveUploadDTO = new ZmsDeviceInventoryMoveUploadDTO();
        zmsDeviceInventoryMoveUploadDTO.setSourceOrder("232423");
        list.add(zmsDeviceInventoryMoveUploadDTO);
        jsonData = "{\"body\":" + new ObjectMapper().writeValueAsString(list) + "}";
        dto.setJsonData(jsonData);
        PowerMockito.when(zmsDeviceInventoryUploadRepository.updateCpmContractEntitiesUpload(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(zmsDeviceInventoryUploadService, "updateCpmContractEntitiesUpload", dto);

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:18fbd9777dy4246144680994e0a1795cf4591f91 */
}
/* Ended by AICoder, pid:3e17dq46994f92914ba60af370a7fc95a6b505eb */
