package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.*;
import com.zte.domain.model.datawb.*;
import com.zte.interfaces.dto.ConfigureMonitorDTO;
import com.zte.util.BaseTestCase;
import com.zte.common.CommonUtils;
import org.junit.Assert;
import org.junit.runner.RunWith;
import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;


@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class MesConfigureMonitorServiceImplTest extends BaseTestCase
{
    @InjectMocks
    MesConfigureMonitorServiceImpl mesConfigureMonitorService;

    @Mock
    MesConfigureMonitorRepository mesConfigureMonitorRepository;

    @Test
    public void monitorException() throws Exception {
        try {
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            List<ConfigureMonitorDTO> listDt =new ArrayList<>();
            ConfigureMonitorDTO c=new ConfigureMonitorDTO();
            c.setDbName("wmes");
            listDt.add(c);
            PowerMockito.when(mesConfigureMonitorRepository.findConfigureInfor(any())).thenReturn(listDt);
            mesConfigureMonitorService.configureMonitorException("wmes_Find_Customer_Name");


            c.setErrorMessage("test000");
            c.setMonitorType(0);
            c.setSqlScript("123");
            c.setTimeOut(0);
            PowerMockito.when(mesConfigureMonitorRepository.findConfigureInfor(any())).thenReturn(listDt);
            PowerMockito.when(mesConfigureMonitorRepository.selectData(any())).thenReturn(null);
            mesConfigureMonitorService.configureMonitorException("wmes_werp_adm_import_table_log3");
            c.setErrorMessage("test111");
            c.setMonitorType(1);
            c.setSqlScript("123");
            c.setTimeOut(2000);
            PowerMockito.when(mesConfigureMonitorRepository.findConfigureInfor(any())).thenReturn(listDt);
            //PowerMockito.when(mesConfigureMonitorRepository.selectData(any())).thenReturn(void());
            mesConfigureMonitorService.configureMonitorException("wmes_Find_Customer_Name");



        }
        catch(Exception e)
        {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

}
