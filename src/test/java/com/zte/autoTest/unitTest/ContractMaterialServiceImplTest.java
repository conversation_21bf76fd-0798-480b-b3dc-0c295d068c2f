package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.ContractMaterialServiceImpl;
import com.zte.domain.model.datawb.ContractMaterialServiceRepository;
import com.zte.interfaces.dto.ContractMaterialParmaterDTO;
import com.zte.interfaces.dto.ContractMaterialResultDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @since 2021 1 21
 */
@RunWith(PowerMockRunner.class)
public class ContractMaterialServiceImplTest {
    @InjectMocks
    private ContractMaterialServiceImpl contractMaterialServiceImpl;
    @Mock
    private ContractMaterialServiceRepository contractMaterialServiceRepository;

    @Test
    public void getPageMatrialinfo() {
        ContractMaterialParmaterDTO remember = new ContractMaterialParmaterDTO();
        List<String> listc = new ArrayList<>();
        listc.add("1233");
        long num = 1;
        remember.setBoxBillNumber(listc);
        remember.setCurrentPage(num);
        remember.setPageSize(num);
        remember.setStartRow(num);
        remember.setEndRow(num);
        List<ContractMaterialResultDTO> result = new ArrayList<>();
        ContractMaterialResultDTO reDto = new ContractMaterialResultDTO();
        reDto.setConfigDetailId("10260547");
        reDto.setItemBarcode("123456");
        reDto.setSeriesNo("123456");
        reDto.setQty("1");
        result.add(reDto);
        PowerMockito.when(contractMaterialServiceRepository.getBarcoderByBillnumber(anyObject())).thenReturn(result);
        PowerMockito.when(contractMaterialServiceRepository.getBarcoderCount(anyObject())).thenReturn(num);
        try {
            contractMaterialServiceImpl.getPageMatrialinfo(remember);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }

    @Test
    public void getThirdItemCode() {

        ContractMaterialParmaterDTO remember = new ContractMaterialParmaterDTO();
        List<String> listc = new ArrayList<>();
        listc.add("1233");
        long num = 1;
        List<ContractMaterialResultDTO> result = new ArrayList<>();
        ContractMaterialResultDTO reDto = new ContractMaterialResultDTO();
        reDto.setConfigDetailId("10260547");
        reDto.setItemBarcode("123456");
        reDto.setSeriesNo("123456");
        reDto.setQty("1");
        result.add(reDto);
        PowerMockito.when(contractMaterialServiceRepository.getThirdItemCode(anyObject())).thenReturn(result);
        try {
            contractMaterialServiceImpl.getThirdItemCode(result);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }

    @Test
    public void matchItemBarcode() {
        ContractMaterialParmaterDTO remember = new ContractMaterialParmaterDTO();
        List<String> listc = new ArrayList<>();
        listc.add("1233");
        long num = 1;
        remember.setBoxBillNumber(listc);
        remember.setCurrentPage(num);
        remember.setPageSize(num);
        remember.setStartRow(num);
        remember.setEndRow(num);

        List<ContractMaterialResultDTO> result = new ArrayList<>();
        ContractMaterialResultDTO reDto = new ContractMaterialResultDTO();
        reDto.setConfigDetailId("10260547");
        reDto.setItemBarcode("123456");
        reDto.setSeriesNo("123456");
        reDto.setQty("1");
        result.add(reDto);
        PowerMockito.when(contractMaterialServiceRepository.getBarcoderByBillnumber(anyObject())).thenReturn(result);
        try {
            contractMaterialServiceImpl.matchItemBarcode(result, result);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }

    @Test
    public void isListEmpty() {
        List<String> list = new ArrayList<>();
        list.add("122");
        try {
            contractMaterialServiceImpl.isListEmpty(list);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }

    @Test
    public void callBarcodeMaterialPage() {
        ContractMaterialParmaterDTO remember = new ContractMaterialParmaterDTO();
        List<String> listc = new ArrayList<>();
        long page = 1;
        listc.add("1233");
        remember.setStartRow(page);
        remember.setEndRow(page);
        remember.setBoxBillNumber(listc);
        try {
            contractMaterialServiceImpl.callBarcodeMaterialPage(remember);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void callBarcodeMaterialCount() {
        ContractMaterialParmaterDTO remember = new ContractMaterialParmaterDTO();
        List<String> listc = new ArrayList<>();
        listc.add("1233");
        remember.setBoxBillNumber(listc);
        try {
            contractMaterialServiceImpl.callBarcodeMaterialCount(remember);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }

    @Test
    public void setParamMap() {
        ContractMaterialParmaterDTO remember = new ContractMaterialParmaterDTO();
        List<String> listc = new ArrayList<>();
        listc.add("1233");
        remember.setBoxBillNumber(listc);
        try {
            contractMaterialServiceImpl.setParamMap(remember);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void formatInStr() {
        List<String> listc = new ArrayList<>();
        listc.add("1233");
        listc.add("456");
        try {
            contractMaterialServiceImpl.formatInStr(listc);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

}
