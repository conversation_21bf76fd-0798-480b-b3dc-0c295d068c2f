package com.zte.autoTest.unitTest;


import com.alibaba.fastjson.JSON;
import com.zte.application.datawb.impl.ZmsOverallErrorDetailUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.domain.model.datawb.CfgCodeRuleItemRepository;
import com.zte.domain.model.datawb.ZmsOverallUnitRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.GbomCsgInfosDTO;
import com.zte.interfaces.dto.OverallUnitMeiTuanLogDTO;
import com.zte.interfaces.dto.ZmsOverAllRepairsLogMeituanDTO;
import com.zte.interfaces.dto.ZmsOverAllRepairsSnMeituanDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.lang.time.DateFormatUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        DateFormatUtils.class, JSON.class,
        JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class, CommonUtils.class, Normalizer.class, GbomCsgInfosDTO.class})
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class ZmsOverallErrorDetailUploadServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ZmsOverallErrorDetailUploadServiceImpl zmsOverallErrorDetailUploadServiceImpl;

    @Mock
    ZmsOverallUnitRepository zmsOverallUnitRepository;

    @Mock
    CfgCodeRuleItemRepository cfgCodeRuleItemRepository;

    @Mock
    ZmsStationLogUploadServiceImpl zmsStationLogUploadService;

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class, GbomCsgInfosDTO.class, DateFormatUtils.class,
                JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class, Normalizer.class, JSON.class);
    }

    @Test
    public void Test() throws Exception {
        ServiceData ret = new ServiceData();
        try {
            when(zmsOverallUnitRepository.queryMaxUnitTimeInOverallReparisLog()).thenReturn(null);
            when(zmsOverallUnitRepository.queryOverallUnitLog(Mockito.any())).thenReturn(null);
            ret = zmsOverallErrorDetailUploadServiceImpl.handleErrorDetailUpload(null, null);
            Assert.assertEquals(ret.getBo(), null);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.NO_DATA_FOUND, ex.getMessage());
        }

        try {
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO dto = new OverallUnitMeiTuanLogDTO();
            overallUnitLogs.add(dto);
            when(zmsOverallUnitRepository.queryMaxUnitTimeInOverallReparisLog()).thenReturn(null);
            when(zmsOverallUnitRepository.queryOverallUnitLog(Mockito.any())).thenReturn(overallUnitLogs);
            ret = zmsOverallErrorDetailUploadServiceImpl.handleErrorDetailUpload(null, null);
            Assert.assertEquals(ret.getBo(), null);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.NO_DATA_FOUND, ex.getMessage());
        }

        try {
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            String empNo = "";
            String url = "";
            when(cfgCodeRuleItemRepository.getDicDescription(Mockito.any())).thenReturn(url);
            zmsOverallErrorDetailUploadServiceImpl.sendMeituanQueryRepairInfo(overallUnitLogs, empNo);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(MessageId.NO_DATA_FOUND, ex.getMessage());
        }

        try {
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            String empNo = "";
            String url = "http://zte.com.cn";
            when(cfgCodeRuleItemRepository.getDicDescription(Mockito.any())).thenReturn(url);
            zmsOverallErrorDetailUploadServiceImpl.sendMeituanQueryRepairInfo(null, empNo);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }


        try {
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO dto = new OverallUnitMeiTuanLogDTO();
            overallUnitLogs.add(dto);
            String empNo = "";
            String url = "http://zte.com.cn";
            when(cfgCodeRuleItemRepository.getDicDescription(Mockito.any())).thenReturn(url);
            String result = null;
            when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
            zmsOverallErrorDetailUploadServiceImpl.sendMeituanQueryRepairInfo(overallUnitLogs, empNo);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }

        try {
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO dto = new OverallUnitMeiTuanLogDTO();
            overallUnitLogs.add(dto);
            String empNo = "";
            String url = "http://zte.com.cn";
            when(cfgCodeRuleItemRepository.getDicDescription(Mockito.any())).thenReturn(url);
            String result = "[{\"repair_info\":[{\"retest_station\":\"\",\"station_name\":\"MTHC\",\"failed_time\":1642296969,\"error_msg\":\"ANT1->RX1增益测试\",\"rework_time\":1708672362,\"problem_request_id\":\"定制化Pro工序22695970\",\"finish_rework_time\":1708672381,\"retest_time\":0,\"action_msg\":\"定制化Pro工序-test\"}],\"serverSn\":\"888888881117\"},{\"repair_info\":[{\"retest_station\":\"\",\"station_name\":\"MTHC\",\"failed_time\":1642296969,\"error_msg\":\"ANT1->RX1增益测试\",\"rework_time\":1708672362,\"problem_request_id\":\"定制化Pro工序22695970\",\"finish_rework_time\":1708672381,\"retest_time\":0,\"action_msg\":\"定制化Pro工序-test\"}],\"serverSn\":\"888888881117\"},{\"repair_info\":[{\"retest_station\":\"\",\"station_name\":\"MTT\",\"failed_time\":1642296969,\"error_msg\":\"ANT1->RX1增益测试\",\"rework_time\":0,\"problem_request_id\":\"压测Pro工序22695976\",\"finish_rework_time\":0,\"retest_time\":0,\"action_msg\":\"压测Pro工序-test\"}],\"serverSn\":\"888888901012\"},{\"repair_info\":[],\"serverSn\":\"210000000000\"}]";
            when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
            List<ZmsOverAllRepairsSnMeituanDTO> zsZmsStationInfoUploadDTOList = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO fgg = new ZmsOverAllRepairsSnMeituanDTO();
            zsZmsStationInfoUploadDTOList.add(fgg);
            when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(),Mockito.any())).thenReturn(zsZmsStationInfoUploadDTOList);
            zmsOverallErrorDetailUploadServiceImpl.sendMeituanQueryRepairInfo(overallUnitLogs, empNo);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }

        try {
            String empNo = "";
            List<ZmsOverAllRepairsLogMeituanDTO> repair_info = new ArrayList<>();
            ZmsOverAllRepairsLogMeituanDTO entity = new ZmsOverAllRepairsLogMeituanDTO();
            repair_info.add(entity);

            List<ZmsOverAllRepairsSnMeituanDTO> tempInsertList = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO dto = new ZmsOverAllRepairsSnMeituanDTO();
            dto.setServerSn("123");
            dto.setRepairInfo(repair_info);
            tempInsertList.add(dto);

            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO model = new OverallUnitMeiTuanLogDTO();
            model.setNodeSn("123");
            overallUnitLogs.add(model);
            zmsOverallErrorDetailUploadServiceImpl.pushDataToB2B(empNo, null, overallUnitLogs);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }

        try {
            String empNo = "";
            List<ZmsOverAllRepairsLogMeituanDTO> repair_info = new ArrayList<>();
            ZmsOverAllRepairsLogMeituanDTO entity = new ZmsOverAllRepairsLogMeituanDTO();
            entity.setFailedTime(Long.valueOf("1642296969"));
            entity.setFinishReworkTime(Long.valueOf("0"));
            entity.setReworkTime(Long.valueOf("0"));
            entity.setRetestTime(Long.valueOf("0"));
            repair_info.add(entity);

            List<ZmsOverAllRepairsSnMeituanDTO> tempInsertList = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO dto = new ZmsOverAllRepairsSnMeituanDTO();
            dto.setServerSn("123");
            dto.setRepairInfo(repair_info);
            tempInsertList.add(dto);

            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO model = new OverallUnitMeiTuanLogDTO();
            model.setNodeSn("123");
            overallUnitLogs.add(model);
            zmsOverallErrorDetailUploadServiceImpl.pushDataToB2B(empNo, tempInsertList, overallUnitLogs);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        try {
            String empNo = "";
            List<ZmsOverAllRepairsSnMeituanDTO> tempInsertList = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO dto = new ZmsOverAllRepairsSnMeituanDTO();
            dto.setServerSn("123");
            tempInsertList.add(dto);

            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO model = new OverallUnitMeiTuanLogDTO();
            model.setNodeSn("1223");
            zmsOverallErrorDetailUploadServiceImpl.pushDataToB2B(empNo, tempInsertList, overallUnitLogs);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }

        try {
            List<ZmsOverAllRepairsLogMeituanDTO> repair_info = new ArrayList<>();
            ZmsOverAllRepairsLogMeituanDTO entity = new ZmsOverAllRepairsLogMeituanDTO();
            entity.setFailedTimeDate(new Date(Long.valueOf("1642296969000")));
            entity.setRetestTimeDate(new Date(Long.valueOf("0")));
            entity.setReworkTimeDate(new Date(Long.valueOf("0")));
            entity.setFinishReworkTimeDate(new Date(Long.valueOf("0")));
            repair_info.add(entity);

            String empNo = "";
            List<ZmsOverAllRepairsSnMeituanDTO> ZmsOverAllRepairsSnMeituanDTOListAll = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO dto = new ZmsOverAllRepairsSnMeituanDTO();
            dto.setServerSn("222");
            dto.setRepairInfo(repair_info);
            ZmsOverAllRepairsSnMeituanDTOListAll.add(dto);
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            zmsOverallErrorDetailUploadServiceImpl.insertOverallRepairsLog(empNo, ZmsOverAllRepairsSnMeituanDTOListAll, overallUnitLogs);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }
        try{
            List<ZmsOverAllRepairsLogMeituanDTO> repair_info = new ArrayList<>();
            ZmsOverAllRepairsLogMeituanDTO entity = new ZmsOverAllRepairsLogMeituanDTO();
            repair_info.add(entity);

            String empNo = "";
            List<ZmsOverAllRepairsSnMeituanDTO> ZmsOverAllRepairsSnMeituanDTOListAll = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO dto = new ZmsOverAllRepairsSnMeituanDTO();
            dto.setServerSn("222");
            dto.setRepairInfo(repair_info);
            ZmsOverAllRepairsSnMeituanDTOListAll.add(dto);
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            OverallUnitMeiTuanLogDTO model = new OverallUnitMeiTuanLogDTO();
            model.setNodeSn("222");
            overallUnitLogs.add(model);
            zmsOverallErrorDetailUploadServiceImpl.insertOverallRepairsLog(empNo, ZmsOverAllRepairsSnMeituanDTOListAll, overallUnitLogs);
            Assert.assertEquals(true, true);
        }catch (Exception ex){
            Assert.assertEquals(null, ex.getMessage());
        }
        try {
            List<ZmsOverAllRepairsLogMeituanDTO> repair_info = new ArrayList<>();
            ZmsOverAllRepairsLogMeituanDTO entity = new ZmsOverAllRepairsLogMeituanDTO();
            repair_info.add(entity);

            String empNo = "";
            List<ZmsOverAllRepairsSnMeituanDTO> ZmsOverAllRepairsSnMeituanDTOListAll = new ArrayList<>();
            ZmsOverAllRepairsSnMeituanDTO dto = new ZmsOverAllRepairsSnMeituanDTO();
            dto.setServerSn("222");
            repair_info = new ArrayList<>();
            dto.setRepairInfo(repair_info);
            ZmsOverAllRepairsSnMeituanDTOListAll.add(dto);
            List<OverallUnitMeiTuanLogDTO> overallUnitLogs = new ArrayList<>();
            zmsOverallErrorDetailUploadServiceImpl.insertOverallRepairsLog(empNo, ZmsOverAllRepairsSnMeituanDTOListAll, overallUnitLogs);
            Assert.assertEquals(true, true);
        } catch (Exception ex) {
            Assert.assertEquals(null, ex.getMessage());
        }


    }
}
