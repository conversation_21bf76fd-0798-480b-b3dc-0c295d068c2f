package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.StOverlistServiceImpl;
import com.zte.domain.model.datawb.StOverlistRepository;
import com.zte.interfaces.dto.StOverlistDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-30 19:34
 */
public class StOverlistServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private StOverlistServiceImpl stOverlistServiceImpl;
    @Mock
    private StOverlistRepository stOverlistRepository;

    @Before
    public void init(){

    }

    @Test
    public void queryOverQtyList() {
        List<String> stOverList = new LinkedList<>();
        stOverList.add("11");
        List<StOverlistDTO> result = new LinkedList<>();
        StOverlistDTO b1 = new StOverlistDTO();
        result.add(b1);
        PowerMockito.when(stOverlistRepository.queryOverQtyList(Mockito.anyList()))
                .thenReturn(result);
        Assert.assertNotNull(stOverlistServiceImpl.queryOverQtyList(stOverList));
    }
}
