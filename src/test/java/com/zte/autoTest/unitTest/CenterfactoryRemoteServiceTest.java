package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerQualityDTO;
import com.zte.interfaces.dto.QualityCodeOutputDTO;
import com.zte.interfaces.dto.centerfactory.HrmPersonInfoDTO;
import com.zte.interfaces.dto.centerfactory.PsTask;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2021-11-16 16:52
 */
@PrepareForTest({HttpRemoteService.class, HttpClientUtil.class,ServiceDataBuilderUtil.class,ConstantInterface.class,CommonUtils.class,HttpRemoteUtil.class,MicroServiceRestUtil.class,
        JacksonJsonConverUtil.class,JSON.class, MESHttpHelper.class})
@RunWith(PowerMockRunner.class)
public class CenterfactoryRemoteServiceTest {
    @InjectMocks
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
    }

    @Test
    public void getRealTimeInteractiveB2B() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<String> itemCodeList = new ArrayList<>();
        CustomerQualityDTO customerQualityDTO = new CustomerQualityDTO();
        List<QualityCodeOutputDTO> bbb = centerfactoryRemoteService.getRealTimeInteractiveB2B(customerQualityDTO);
        Assert.assertEquals(bbb.size(),0);
        itemCodeList.add("2");
        customerQualityDTO.setCustomerName("111");
        customerQualityDTO.setServerSnList(itemCodeList);

        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn("");
        try {
            bbb = centerfactoryRemoteService.getRealTimeInteractiveB2B(customerQualityDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION, e.getMessage());
        }

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"total\":1,\"size\":10,\"current\":1,\"searchCount\":true,\"pages\":1,\"records\":[{\"srcBillNo\":null,\"itemNo\":\"046050200180\",\"reelid\":\"ZTE2021032200003\",\"reserveQty\":5,\"enableFlag\":\"Y\",\"warehouseNo\":\"CS1265\",\"stockNo\":\"CS1265-0002\",\"locationNo\":\"CS1265-0002-0001\",\"canUseFlag\":null,\"attribute1\":\"7775003-SMT-A5202\",\"attribute2\":\"\",\"createBy\":\"10275508\",\"createDate\":\"2021-03-23 19:07:07\",\"lastUpdateBy\":\"10275508\",\"lastUpdateDate\":\"2021-03-23 19:07:07\"}],\"hasNextPage\":false},\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.StockLockController@getPageList\",\"code\":\"0000\",\"costTime\":\"479ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue Mar 23 20:40:18 CST 2021\",\"tag\":null,\"serviceName\":\"zte-mes-manufactureshare-productiondeliverysys\",\"userId\":\"10266925\"}}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);
        PowerMockito.when(constantInterface.getUrl(Mockito.any()))
                .thenReturn("22");
        try {
            bbb = centerfactoryRemoteService.getRealTimeInteractiveB2B(customerQualityDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_CUSTOMER_BASIC_INFORMATION, e.getMessage());
        }
    }

    @Test
    public void getLookupValue() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID));
        CenterfactoryRemoteService.getLookupValue("1","");
        List<SysLookupValues> lookupValuesDTOList = new ArrayList<>();
        SysLookupValues dto = new SysLookupValues();
        dto.setLookupMeaning("123");
        dto.setLookupType(new BigDecimal("123"));
        lookupValuesDTOList.add(dto);
        serviceData.setBo(lookupValuesDTOList);
        Assert.assertNull(CenterfactoryRemoteService.getLookupValue("123",""));
    }
    @Test
    public void getBoardAssemblyRelationship()throws Exception{
        String parentSn="123";
        String url ="URL";
        PowerMockito.when( constantInterface.getUrl(Mockito.any()))
                .thenReturn(url);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(),Mockito.anyMap(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn("msg");
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

        PowerMockito.when(json.get(Constant.JSON_BO)).thenReturn(json);
        PowerMockito.when(json.get(Constant.STR_ROWS)).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("SS");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        try{
            centerfactoryRemoteService.getBoardAssemblyRelationship(parentSn);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_BOARD_ASSEMBLY_RELATIONSHIP_ERROR, e.getMessage());
        }

    }

    @Test
    public void queryLookupValue() throws Exception {
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.toString()).thenReturn("[]");
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyMap(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("56");
        List<SysLookupValues> sysLookupValues = centerfactoryRemoteService.queryLookupValue(new LinkedList<>());
        Assert.assertTrue(CollectionUtils.isEmpty(sysLookupValues));
    }

    @Test
    public void updateLookupValue() throws Exception {
        centerfactoryRemoteService.updateLookupValue(new SysLookupValues());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }


    @Test
    public void selectPlanGroupUnion() throws Exception {
        String prodPlanId = "8899731";
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString()))
                .thenReturn("[{\"prodplanId\":\"123\"}]");
        List<PsTask> psTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setAttribute1("123");
        psTasks.add(a1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(), Mockito.anyObject())).thenReturn(psTasks);
        Assert.assertNull(centerfactoryRemoteService.selectPlanGroupUnion(prodPlanId));
    }

    @Test
    public void getBomNoByProdplanId() throws Exception {
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://test.imes.zte.com.cn/zte-mes-manufactureshare-centerfactory/PS/getBomNoByProdplanId/");
        PowerMockito.mockStatic(HttpClientUtil.class);

        ServiceData<String> serviceData = new ServiceData<>();
        serviceData.setBo("152345646512ACB");
        String result = JSONObject.toJSONString(serviceData);

        PowerMockito.when(HttpClientUtil.httpGet(any(),any(),any())).thenReturn(result);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(),Mockito.anyObject()))
                .thenReturn(serviceData);

        Assert.assertNotNull(centerfactoryRemoteService.getBomNoByProdplanId("7777666"));
    }

    @Test
    public void getLeadFlagDescByProdplanId() throws Exception {
        String leadFlagDesc = centerfactoryRemoteService.getLeadFlagDescByProdplanId("");
        Assert.assertEquals("", leadFlagDesc);

        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://test.imes.zte.com.cn/zte-mes-manufactureshare-centerfactory/PS/getLeadFlagDescByProdplanId/");
        PowerMockito.mockStatic(HttpClientUtil.class);

        ServiceData<String> serviceData = new ServiceData<>();
        serviceData.setBo("ROHS");
        String result = JSONObject.toJSONString(serviceData);

        PowerMockito.when(HttpClientUtil.httpGet(any(),any(),any())).thenReturn(result);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(),Mockito.anyObject()))
                .thenReturn(serviceData);

        leadFlagDesc = centerfactoryRemoteService.getLeadFlagDescByProdplanId("7777666");
    }

    @Test
    public void getSnByParentSn() throws Exception {
        List<String> list = centerfactoryRemoteService.getSnByParentSn("");
        Assert.assertEquals(0, list.size());
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://test.imes.zte.com.cn/zte-mes-manufactureshare-centerfactory/boardAssemblyRelationShip/getSnByParentSn/");
        PowerMockito.mockStatic(HttpClientUtil.class);
        ServiceData<List<String>> serviceData = new ServiceData<>();
        List<String> subSnList = new ArrayList<>();
        subSnList.add("730731700001");
        subSnList.add("730731700002");
        serviceData.setBo(subSnList);
        String result = JSONObject.toJSONString(serviceData);

        PowerMockito.when(HttpClientUtil.httpGet(any(),any(),any())).thenReturn(result);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(),Mockito.anyObject()))
                .thenReturn(serviceData);

        list = centerfactoryRemoteService.getSnByParentSn("730731800001");
    }

    @Test
    public void pushDataToB2BTest() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        ServiceData serviceData = new ServiceData<>();
        serviceData.setBo(null);
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-datawbsys/PS/getFactoryIdByProdplanId");
        PowerMockito.mockStatic(HttpRemoteUtil.class);

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        centerfactoryRemoteService.pushDataToB2B(dataList);
        Assert.assertNotNull(dataList);

    }
	@Test
	public void getHrmPersonInfo() throws Exception {
		PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpClientUtil.class);
		PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-centerfactory/hrmUser/getHrmPersonInfo");
		Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
		HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
		hrmPersonInfoDTOMap.put("1024778", hrmPersonInfoDTO);
		hrmPersonInfoDTOMap.put("1024779", hrmPersonInfoDTO);
		String result="{\n" +
				"  \"code\": {\n" +
				"    \"code\": \"0000\",\n" +
				"    \"msgId\": \"RetCode.Success\",\n" +
				"    \"msg\": \"操作成功\"\n" +
				"  },\n" +
				"  \"bo\": [\n" +
				"    { }\n" +
				"  ],\n" +
				"  \"other\": null\n" +
				"}";
		PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
		PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
		List<String> list =new ArrayList<>();
		list.add("1024778");
		try{centerfactoryRemoteService.getHrmPersonInfo(list);}catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
	}

    @Test
    public void getWmesTaskNo() throws Exception {
//        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteUtil.class);
        List<String> taskNoList =new ArrayList<>();
        List<String> list = centerfactoryRemoteService.getWmesTaskNo(taskNoList);
        Assert.assertEquals(0,list.size());
        taskNoList.add("000");
        PowerMockito.when( constantInterface.getUrl(Mockito.any())).thenReturn("url");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("msg");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(null);
        List<String> list1 = centerfactoryRemoteService.getWmesTaskNo(taskNoList);
        Assert.assertEquals(0,list1.size());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("[{\"123\"}]");
        List<String> resultList = new ArrayList();
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(),Mockito.any())).thenReturn(resultList);
        List<String> list2 = centerfactoryRemoteService.getWmesTaskNo(taskNoList);
        Assert.assertEquals(0,list2.size());
        resultList.add("111");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[{\"123\"}]");
        List<String> list3 = centerfactoryRemoteService.getWmesTaskNo(taskNoList);
        Assert.assertEquals(1,list3.size());
    }


    @Test
    public void getPsTask() throws Exception {
        List<String> taskNoList = new ArrayList<>();
        List<PsTask> list = centerfactoryRemoteService.getPsTask(taskNoList);
        Assert.assertEquals(0, list.size());
        taskNoList.add("000");
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("url");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("msg");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(null);
        List<PsTask> list1 = centerfactoryRemoteService.getPsTask(taskNoList);
        Assert.assertEquals(0, list1.size());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("[{\"123\"}]");
        List<String> resultList = new ArrayList();
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(resultList);
        List<PsTask> list2 = centerfactoryRemoteService.getPsTask(taskNoList);
        Assert.assertEquals(0, list2.size());
        resultList.add("111");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[{\"123\"}]");
        List<PsTask> list3 = centerfactoryRemoteService.getPsTask(taskNoList);
        Assert.assertEquals(1, list3.size());
    }

    /* Started by AICoder, pid:5b1b8840fby6ddc1412b0a1ec0d14466c6c6b9ed */
    @Test
    public void createBillNo() throws Exception {

        String bizCode = "testBizCode";
        String simpleDateFormat = "yyyyMMdd";
        int billCount = 5;
        int length = 8;
        centerfactoryRemoteService.createBillNo(bizCode, simpleDateFormat, billCount, length);

        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("http://example.com/createBillNo");
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("msg");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(null);
        centerfactoryRemoteService.createBillNo(bizCode, simpleDateFormat, billCount, length);

        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[{\"123\"}]");
        List<String> resultList = new ArrayList();
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(resultList);
        centerfactoryRemoteService.createBillNo(bizCode, simpleDateFormat, billCount, length);

        resultList.add("111");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[{\"123\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any())).thenReturn(resultList);
        centerfactoryRemoteService.createBillNo(bizCode, simpleDateFormat, billCount, length);

        Assert.assertNotNull(bizCode);
    }
    /* Ended by AICoder, pid:5b1b8840fby6ddc1412b0a1ec0d14466c6c6b9ed */
}
