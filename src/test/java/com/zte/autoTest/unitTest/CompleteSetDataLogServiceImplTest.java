package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.ZmsDeviceInventoryUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsIndicatorUploadServiceImpl;
import com.zte.application.datawb.impl.ZmsOverallUnitServiceImpl;
import com.zte.application.datawb.impl.ZmsStationLogUploadServiceImpl;

import com.zte.application.impl.CompleteSetDataLogServiceImpl;

import com.zte.domain.model.datawb.TaskToSetRepository;
import com.zte.domain.model.datawb.ZmsStationLogUploadRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;

import com.zte.util.PowerBaseTestCase;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static com.zte.common.utils.Constant.LOOKUP_TYPES_8240042;
import static com.zte.common.utils.Constant.LOOKUP_TYPE_824004200010;


public class CompleteSetDataLogServiceImplTest  extends PowerBaseTestCase {
    @Mock
    private TaskToSetRepository taskToSetRepository;

    @Mock
    private ZmsIndicatorUploadServiceImpl zmsIndicatorUploadService;

    @Mock
    private ZmsOverallUnitServiceImpl zmsOverallUnitServiceImpl;


    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;

    @Mock
    private ZmsStationLogUploadServiceImpl zmsStationLogUploadService;

    @Mock
    private ZmsStationLogUploadRepository zmsStationLogUploadRepository;

    @Mock
    private ZmsDeviceInventoryUploadServiceImpl zmsDeviceInventoryUploadServiceImpl;
    @InjectMocks
    private CompleteSetDataLogServiceImpl completeSetDataLogServiceImpl;

    @Test
    @PrepareForTest({HttpClientUtil.class,CenterfactoryRemoteService.class})
    public void uploadCompleteSetDataTest() throws Exception {

        /* Started by AICoder, pid:53a16999219440f387f37d802bc75268 */
        CompleteSetDataDTO dto = new CompleteSetDataDTO();
        PowerMockito.when(taskToSetRepository.selectTaskToSetHeadInfo(Mockito.any())).thenReturn(null);
        completeSetDataLogServiceImpl.uploadCompleteSetData(dto);

        List<TaskHeadInfo> taskHeadInfoList=new ArrayList<>();
        PowerMockito.when(taskToSetRepository.selectTaskToSetHeadInfo(Mockito.any())).thenReturn(taskHeadInfoList);

        PowerMockito.when(taskToSetRepository.selectTaskToSetInfo(Mockito.any())).thenReturn(null);
        completeSetDataLogServiceImpl.uploadCompleteSetData(dto);

        taskHeadInfoList = new ArrayList<>();
        TaskHeadInfo taskHeadInfo = new TaskHeadInfo();
        taskHeadInfo.setServerSn("11");
        taskHeadInfo.setFactoryOrderId("11");
        taskHeadInfoList.add(taskHeadInfo);
        PowerMockito.when(taskToSetRepository.selectTaskToSetHeadInfo(Mockito.any())).thenReturn(taskHeadInfoList);

        List<TaskDetailInfo> taskLineInfoList = new ArrayList<>();
        TaskDetailInfo taskDetailInfo = new TaskDetailInfo();
        taskDetailInfo.setFactoryOrderId("22");
        taskDetailInfo.setServerSn("11");
        taskDetailInfo.setItemNo("006040100040");
        taskDetailInfo.setComponentType("44");
        taskDetailInfo.setOriginComponentSn("802C0F22483CEF3F81");
        taskLineInfoList.add(taskDetailInfo);
        PowerMockito.when(taskToSetRepository.selectTaskToSetInfo(Mockito.any())).thenReturn(taskLineInfoList);

        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("11111");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200010)).thenReturn("11111");
        /* Ended by AICoder, pid:53a16999219440f387f37d802bc75268 */
        String result = "{\"contract_id\":\"ContractID\",\"NICList\":[{\"manufacturer_product_name\":\"Bytedance CX-6 Dx adapter card, 100GbE OCP3.0, With Host management, Single-port DSFP, Multi Host or Socket Direct, Secure Boot, no Crypto, Thumbscrew -Aux[1]\",\"extra_params\":{\"vid\":\"15b3\",\"pcie_addr\":\"000c:01:00.0\",\"pcie_bw\":\"x8\",\"mac_addr\":\"94:6D:AE:4B:12:8D\",\"svid\":\"15b3\",\"sdid\":\"0189\",\"pcie_speed\":\"16GT/s\",\"did\":\"101d\"},\"itemcode\":\"053100800339\",\"num\":\"1\",\"part_number\":\"CX623439MS-CDAB_28\",\"item_name\":\"ByteDance AVAP专用-双端口100G OCP网卡\",\"brand_name\":\"Mellanox\",\"sn\":\"MT23064002F9\",\"slot\":\"MCIO_P1_D0C2_AB\",\"model_number\":\"CX623439MS-CDAB_28\"},{\"manufacturer_product_name\":\"Bytedance CX-6 Dx adapter card, 100GbE OCP3.0, With Host management, Single-port DSFP, Multi Host or Socket Direct, Secure Boot, no Crypto, Thumbscrew -Aux[1]\",\"extra_params\":{\"vid\":\"15b3\",\"pcie_addr\":\"000c:01:00.0\",\"pcie_bw\":\"x8\",\"mac_addr\":\"94:6D:AE:4B:12:8D\",\"svid\":\"15b3\",\"sdid\":\"0189\",\"pcie_speed\":\"16GT/s\",\"did\":\"101d\"},\"itemcode\":\"053100800339\",\"num\":\"1\",\"part_number\":\"CX623439MS-CDAB_28\",\"item_name\":\"ByteDance AVAP专用-双端口100G OCP网卡\",\"brand_name\":\"Mellanox\",\"sn\":\"MT23064002F9\",\"slot\":\"MCIO_P0_D0C2_CD\",\"model_number\":\"CX623439MS-CDAB_28\"}],\"memoryList\":[{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF43C6\",\"slot\":\"P0_C6_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3D87\",\"slot\":\"P0_C4_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4450\",\"slot\":\"P1_C7_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"manufacturer_product_name\":\"www\",\"sn\":\"802C0F22483CEF3F81\",\"slot\":\"P0_C7_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4751\",\"slot\":\"P0_C0_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF42B5\",\"slot\":\"P1_C5_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3B56\",\"slot\":\"P1_C6_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4356\",\"slot\":\"P1_C4_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3D2E\",\"slot\":\"P0_C5_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF444D\",\"slot\":\"P1_C1_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3E39\",\"slot\":\"P0_C1_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4614\",\"slot\":\"P0_C2_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4235\",\"slot\":\"P1_C2_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF434E\",\"slot\":\"P1_C3_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3BD5\",\"slot\":\"P1_C0_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"}],\"sub_package_name\":\"服务器DZH R5310G3F-DVT-S3B\",\"diskList\":[{\"extra_params\":{\"vid\":\"144d\",\"pcie_addr\":\"0007:81:00.0\",\"pcie_bw\":\"x4\",\"svid\":\"144d\",\"sdid\":\"aa1b\",\"pcie_speed\":\"16GT/s\",\"did\":\"a80a\",\"capacity\":\"3.84\"},\"itemcode\":\"006110100150\",\"num\":\"2\",\"part_number\":\"SAMSUNG MZTL23T8HCLS-00A07\",\"item_name\":\"NVMe-SSD 8T E1.s(ByteDance AVAP)\",\"brand_name\":\"Samsung\",\"sn\":\"S6RMNC0TA01913\",\"slot\":\"nvme1\",\"model_number\":\"SAMSUNG MZTL23T8HCLS-00A07\"},{\"extra_params\":{\"vid\":\"8086\",\"pcie_addr\":\"0007:41:00.0\",\"pcie_bw\":\"x4\",\"svid\":\"8086\",\"sdid\":\"900c\",\"pcie_speed\":\"16GT/s\",\"did\":\"0b60\",\"capacity\":\"7.68\"},\"itemcode\":\"006110100150\",\"num\":\"2\",\"part_number\":\"INTEL SSDPFUKX076T1\",\"item_name\":\"NVMe-SSD 8T E1.s(ByteDance AVAP)\",\"brand_name\":\"Intel\",\"sn\":\"BTAY248300L87P6CGN\",\"slot\":\"nvme0\",\"model_number\":\"INTEL SSDPFUKX076T1\"}],\"manufacture_name\":\"ZTE\",\"PSUList\":[{\"manufacturer_product_name\":\"GW-CRPS2000DW\",\"extra_params\":{\"consumption\":\"2000 W\"},\"itemcode\":\"0513010A0070\",\"num\":\"2\",\"part_number\":\"GW-CRPS2000DW\",\"item_name\":\"GW_交流2000W服务器电源无12Vsb\",\"brand_name\":\"Great Wall\",\"sn\":\"2M090018506\",\"slot\":\"PSU0\",\"model_number\":\"GW-CRPS2000DW\"},{\"manufacturer_product_name\":\"GW-CRPS2000DW\",\"extra_params\":{\"consumption\":\"2000 W\"},\"itemcode\":\"0513010A0070\",\"num\":\"2\",\"part_number\":\"GW-CRPS2000DW\",\"item_name\":\"GW_交流2000W服务器电源无12Vsb\",\"brand_name\":\"Great Wall\",\"sn\":\"2M120025269\",\"slot\":\"PSU1\",\"model_number\":\"GW-CRPS2000DW\"}],\"package_name\":\"服务器DZH R5310G3F-DVT-S3B\",\"cpuList\":[{\"itemcode\":\"008020100879\",\"num\":\"2\",\"part_number\":\"Sanechips(R) ZhuFengXin Processor\",\"item_name\":\"128核 3.0GHz 嵌入式通讯高性能ARM处理器(字节 AVAP)\",\"brand_name\":\"ZTE\",\"sn\":\"00000000000000145532473437350207\",\"slot\":\"CPU 0\"},{\"itemcode\":\"008020100879\",\"num\":\"2\",\"part_number\":\"Sanechips(R) ZhuFengXin Processor\",\"item_name\":\"128核 3.0GHz 嵌入式通讯高性能ARM处理器(字节 AVAP)\",\"brand_name\":\"ZTE\",\"sn\":\"00000000000000145532473437350802\",\"slot\":\"CPU 1\"}],\"GPUList\":[],\"sn\":\"210120123279\",\"mainboard\":{\"bios_ver\":\"0E.***********\",\"itemcode\":\"130000209834\",\"num\":\"1\",\"bmc_firmware_ver\":\"3.02.0e\",\"part_number\":\"\",\"item_name\":\"R5310G3F主板\",\"brand_name\":\"ZTE\",\"bmc_mac\":\"BC1695007E63;BC1695007E64;BC1695007E65\",\"bios_ver_date\":\"01/10/2024\",\"sn\":\"751998000014\",\"slot\":\"1\",\"cpld_ver\":\"00.00.0104\"},\"raidList\":[],\"asset_num\":\"2024-lx-srv-079905\",\"node_asset_num\":\"2024-lx-srv-079905\"}";

        /* Started by AICoder, pid:5059555edf4e46df954367c038ac78f7 */
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        List<SspTaskDetailInfoDTO> sspTaskDetailList = completeSetDataLogServiceImpl.querySspTaskInfoByte("12");

        completeSetDataLogServiceImpl.uploadCompleteSetData(dto);



        taskLineInfoList = new ArrayList<>();
        taskDetailInfo = new TaskDetailInfo();
        taskDetailInfo.setFactoryOrderId("22");
        taskDetailInfo.setServerSn("33");
        taskDetailInfo.setItemNo("33333");
        taskDetailInfo.setComponentType("44");
        taskDetailInfo.setOriginComponentSn("33333");
        taskLineInfoList.add(taskDetailInfo);
        PowerMockito.when(taskToSetRepository.selectTaskToSetInfo(Mockito.any())).thenReturn(taskLineInfoList);
        completeSetDataLogServiceImpl.uploadCompleteSetData(dto);

        taskLineInfoList = new ArrayList<>();
        taskDetailInfo = new TaskDetailInfo();
        taskDetailInfo.setFactoryOrderId("22");
        taskDetailInfo.setServerSn("11");
        taskDetailInfo.setItemNo("33333");
        taskDetailInfo.setComponentType("44");
        taskDetailInfo.setOriginComponentSn("33333");
        taskDetailInfo.setSlot("33333");
        taskLineInfoList.add(taskDetailInfo);
        PowerMockito.when(taskToSetRepository.selectTaskToSetInfo(Mockito.any())).thenReturn(taskLineInfoList);
        completeSetDataLogServiceImpl.uploadCompleteSetData(dto);
        /* Ended by AICoder, pid:5059555edf4e46df954367c038ac78f7 */

        /* Started by AICoder, pid:c895788e6c7844398b651d635516001a */
        taskLineInfoList = new ArrayList<>();
        taskDetailInfo = new TaskDetailInfo();
        taskDetailInfo.setFactoryOrderId("22");
        taskDetailInfo.setServerSn("11");
        taskDetailInfo.setItemNo("006040100040");
        taskDetailInfo.setComponentType("44");
        taskDetailInfo.setOriginComponentSn("802C0F22483CEF3F81");
        taskLineInfoList.add(taskDetailInfo);
        PowerMockito.when(taskToSetRepository.selectTaskToSetInfo(Mockito.any())).thenReturn(taskLineInfoList);
        /* Ended by AICoder, pid:c895788e6c7844398b651d635516001a */
        result = "{\"contract_id\":\"ContractID\",\"NICList\":[{\"manufacturer_product_name\":\"Bytedance CX-6 Dx adapter card, 100GbE OCP3.0, With Host management, Single-port DSFP, Multi Host or Socket Direct, Secure Boot, no Crypto, Thumbscrew -Aux[1]\",\"extra_params\":{\"vid\":\"15b3\",\"pcie_addr\":\"000c:01:00.0\",\"pcie_bw\":\"x8\",\"mac_addr\":\"94:6D:AE:4B:12:8D\",\"svid\":\"15b3\",\"sdid\":\"0189\",\"pcie_speed\":\"16GT/s\",\"did\":\"101d\"},\"itemcode\":\"053100800339\",\"num\":\"1\",\"part_number\":\"CX623439MS-CDAB_28\",\"item_name\":\"ByteDance AVAP专用-双端口100G OCP网卡\",\"brand_name\":\"Mellanox\",\"sn\":\"MT23064002F9\",\"slot\":\"MCIO_P1_D0C2_AB\",\"model_number\":\"CX623439MS-CDAB_28\"},{\"manufacturer_product_name\":\"Bytedance CX-6 Dx adapter card, 100GbE OCP3.0, With Host management, Single-port DSFP, Multi Host or Socket Direct, Secure Boot, no Crypto, Thumbscrew -Aux[1]\",\"extra_params\":{\"vid\":\"15b3\",\"pcie_addr\":\"000c:01:00.0\",\"pcie_bw\":\"x8\",\"mac_addr\":\"94:6D:AE:4B:12:8D\",\"svid\":\"15b3\",\"sdid\":\"0189\",\"pcie_speed\":\"16GT/s\",\"did\":\"101d\"},\"itemcode\":\"053100800339\",\"num\":\"1\",\"part_number\":\"CX623439MS-CDAB_28\",\"item_name\":\"ByteDance AVAP专用-双端口100G OCP网卡\",\"brand_name\":\"Mellanox\",\"sn\":\"MT23064002F9\",\"slot\":\"MCIO_P0_D0C2_CD\",\"model_number\":\"CX623439MS-CDAB_28\"}],\"memoryList\":[{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF43C6\",\"slot\":\"P0_C6_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3D87\",\"slot\":\"P0_C4_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4450\",\"slot\":\"P1_C7_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"sn\":\"802C0F22483CEF3F81\",\"slot\":\"P0_C7_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4751\",\"slot\":\"P0_C0_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF42B5\",\"slot\":\"P1_C5_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3B56\",\"slot\":\"P1_C6_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4356\",\"slot\":\"P1_C4_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3D2E\",\"slot\":\"P0_C5_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF444D\",\"slot\":\"P1_C1_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3E39\",\"slot\":\"P0_C1_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4614\",\"slot\":\"P0_C2_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4235\",\"slot\":\"P1_C2_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF434E\",\"slot\":\"P1_C3_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3BD5\",\"slot\":\"P1_C0_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"}],\"sub_package_name\":\"服务器DZH R5310G3F-DVT-S3B\",\"diskList\":[{\"extra_params\":{\"vid\":\"144d\",\"pcie_addr\":\"0007:81:00.0\",\"pcie_bw\":\"x4\",\"svid\":\"144d\",\"sdid\":\"aa1b\",\"pcie_speed\":\"16GT/s\",\"did\":\"a80a\",\"capacity\":\"3.84\"},\"itemcode\":\"006110100150\",\"num\":\"2\",\"part_number\":\"SAMSUNG MZTL23T8HCLS-00A07\",\"item_name\":\"NVMe-SSD 8T E1.s(ByteDance AVAP)\",\"brand_name\":\"Samsung\",\"sn\":\"S6RMNC0TA01913\",\"slot\":\"nvme1\",\"model_number\":\"SAMSUNG MZTL23T8HCLS-00A07\"},{\"extra_params\":{\"vid\":\"8086\",\"pcie_addr\":\"0007:41:00.0\",\"pcie_bw\":\"x4\",\"svid\":\"8086\",\"sdid\":\"900c\",\"pcie_speed\":\"16GT/s\",\"did\":\"0b60\",\"capacity\":\"7.68\"},\"itemcode\":\"006110100150\",\"num\":\"2\",\"part_number\":\"INTEL SSDPFUKX076T1\",\"item_name\":\"NVMe-SSD 8T E1.s(ByteDance AVAP)\",\"brand_name\":\"Intel\",\"sn\":\"BTAY248300L87P6CGN\",\"slot\":\"nvme0\",\"model_number\":\"INTEL SSDPFUKX076T1\"}],\"manufacture_name\":\"ZTE\",\"PSUList\":[{\"manufacturer_product_name\":\"GW-CRPS2000DW\",\"extra_params\":{\"consumption\":\"2000 W\"},\"itemcode\":\"0513010A0070\",\"num\":\"2\",\"part_number\":\"GW-CRPS2000DW\",\"item_name\":\"GW_交流2000W服务器电源无12Vsb\",\"brand_name\":\"Great Wall\",\"sn\":\"2M090018506\",\"slot\":\"PSU0\",\"model_number\":\"GW-CRPS2000DW\"},{\"manufacturer_product_name\":\"GW-CRPS2000DW\",\"extra_params\":{\"consumption\":\"2000 W\"},\"itemcode\":\"0513010A0070\",\"num\":\"2\",\"part_number\":\"GW-CRPS2000DW\",\"item_name\":\"GW_交流2000W服务器电源无12Vsb\",\"brand_name\":\"Great Wall\",\"sn\":\"2M120025269\",\"slot\":\"PSU1\",\"model_number\":\"GW-CRPS2000DW\"}],\"package_name\":\"服务器DZH R5310G3F-DVT-S3B\",\"cpuList\":[{\"itemcode\":\"008020100879\",\"num\":\"2\",\"part_number\":\"Sanechips(R) ZhuFengXin Processor\",\"item_name\":\"128核 3.0GHz 嵌入式通讯高性能ARM处理器(字节 AVAP)\",\"brand_name\":\"ZTE\",\"sn\":\"00000000000000145532473437350207\",\"slot\":\"CPU 0\"},{\"itemcode\":\"008020100879\",\"num\":\"2\",\"part_number\":\"Sanechips(R) ZhuFengXin Processor\",\"item_name\":\"128核 3.0GHz 嵌入式通讯高性能ARM处理器(字节 AVAP)\",\"brand_name\":\"ZTE\",\"sn\":\"00000000000000145532473437350802\",\"slot\":\"CPU 1\"}],\"GPUList\":[],\"sn\":\"210120123279\",\"mainboard\":{\"bios_ver\":\"0E.***********\",\"itemcode\":\"130000209834\",\"num\":\"1\",\"bmc_firmware_ver\":\"3.02.0e\",\"part_number\":\"\",\"item_name\":\"R5310G3F主板\",\"brand_name\":\"ZTE\",\"bmc_mac\":\"BC1695007E63;BC1695007E64;BC1695007E65\",\"bios_ver_date\":\"01/10/2024\",\"sn\":\"751998000014\",\"slot\":\"1\",\"cpld_ver\":\"00.00.0104\"},\"raidList\":[],\"asset_num\":\"2024-lx-srv-079905\",\"node_asset_num\":\"2024-lx-srv-079905\"}";


        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        sspTaskDetailList = completeSetDataLogServiceImpl.querySspTaskInfoByte("12");

        completeSetDataLogServiceImpl.uploadCompleteSetData(dto);
        Assert.assertNull(null);


    }

    @Test
    @PrepareForTest({HttpClientUtil.class,CenterfactoryRemoteService.class})
    public void querySspTaskInfoByteTest() throws Exception {
        PowerMockito.when(zmsStationLogUploadService.getToken()).thenReturn("11111");
        PowerMockito.when(zmsStationLogUploadRepository.getZSUrl(LOOKUP_TYPES_8240042, LOOKUP_TYPE_824004200010)).thenReturn("11111");
        String result = "{\"contract_id\":\"ContractID\",\"NICList\":[{\"manufacturer_product_name\":\"Bytedance CX-6 Dx adapter card, 100GbE OCP3.0, With Host management, Single-port DSFP, Multi Host or Socket Direct, Secure Boot, no Crypto, Thumbscrew -Aux[1]\",\"extra_params\":{\"vid\":\"15b3\",\"pcie_addr\":\"000c:01:00.0\",\"pcie_bw\":\"x8\",\"mac_addr\":\"94:6D:AE:4B:12:8D\",\"svid\":\"15b3\",\"sdid\":\"0189\",\"pcie_speed\":\"16GT/s\",\"did\":\"101d\"},\"itemcode\":\"053100800339\",\"num\":\"1\",\"part_number\":\"CX623439MS-CDAB_28\",\"item_name\":\"ByteDance AVAP专用-双端口100G OCP网卡\",\"brand_name\":\"Mellanox\",\"sn\":\"MT23064002F9\",\"slot\":\"MCIO_P1_D0C2_AB\",\"model_number\":\"CX623439MS-CDAB_28\"},{\"manufacturer_product_name\":\"Bytedance CX-6 Dx adapter card, 100GbE OCP3.0, With Host management, Single-port DSFP, Multi Host or Socket Direct, Secure Boot, no Crypto, Thumbscrew -Aux[1]\",\"extra_params\":{\"vid\":\"15b3\",\"pcie_addr\":\"000c:01:00.0\",\"pcie_bw\":\"x8\",\"mac_addr\":\"94:6D:AE:4B:12:8D\",\"svid\":\"15b3\",\"sdid\":\"0189\",\"pcie_speed\":\"16GT/s\",\"did\":\"101d\"},\"itemcode\":\"053100800339\",\"num\":\"1\",\"part_number\":\"CX623439MS-CDAB_28\",\"item_name\":\"ByteDance AVAP专用-双端口100G OCP网卡\",\"brand_name\":\"Mellanox\",\"sn\":\"MT23064002F9\",\"slot\":\"MCIO_P0_D0C2_CD\",\"model_number\":\"CX623439MS-CDAB_28\"}],\"memoryList\":[{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF43C6\",\"slot\":\"P0_C6_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3D87\",\"slot\":\"P0_C4_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4450\",\"slot\":\"P1_C7_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"manufacturer_product_name\":\"www\",\"sn\":\"802C0F22483CEF3F81\",\"slot\":\"P0_C7_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4751\",\"slot\":\"P0_C0_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF42B5\",\"slot\":\"P1_C5_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3B56\",\"slot\":\"P1_C6_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4356\",\"slot\":\"P1_C4_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3D2E\",\"slot\":\"P0_C5_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF444D\",\"slot\":\"P1_C1_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3E39\",\"slot\":\"P0_C1_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4614\",\"slot\":\"P0_C2_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF4235\",\"slot\":\"P1_C2_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF434E\",\"slot\":\"P1_C3_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"},{\"extra_params\":{\"capacity\":\"64\",\"frequency\":\"4800\"},\"itemcode\":\"006040100040\",\"num\":\"15\",\"part_number\":\"MTC40F2046S1RC48BA1\",\"item_name\":\"64G DDR5 4800\",\"brand_name\":\"Micron\",\"sn\":\"802C0F22483CEF3BD5\",\"slot\":\"P1_C0_D0\",\"model_number\":\"MTC40F2046S1RC48BA1\"}],\"sub_package_name\":\"服务器DZH R5310G3F-DVT-S3B\",\"diskList\":[{\"extra_params\":{\"vid\":\"144d\",\"pcie_addr\":\"0007:81:00.0\",\"pcie_bw\":\"x4\",\"svid\":\"144d\",\"sdid\":\"aa1b\",\"pcie_speed\":\"16GT/s\",\"did\":\"a80a\",\"capacity\":\"3.84\"},\"itemcode\":\"006110100150\",\"num\":\"2\",\"part_number\":\"SAMSUNG MZTL23T8HCLS-00A07\",\"item_name\":\"NVMe-SSD 8T E1.s(ByteDance AVAP)\",\"brand_name\":\"Samsung\",\"sn\":\"S6RMNC0TA01913\",\"slot\":\"nvme1\",\"model_number\":\"SAMSUNG MZTL23T8HCLS-00A07\"},{\"extra_params\":{\"vid\":\"8086\",\"pcie_addr\":\"0007:41:00.0\",\"pcie_bw\":\"x4\",\"svid\":\"8086\",\"sdid\":\"900c\",\"pcie_speed\":\"16GT/s\",\"did\":\"0b60\",\"capacity\":\"7.68\"},\"itemcode\":\"006110100150\",\"num\":\"2\",\"part_number\":\"INTEL SSDPFUKX076T1\",\"item_name\":\"NVMe-SSD 8T E1.s(ByteDance AVAP)\",\"brand_name\":\"Intel\",\"sn\":\"BTAY248300L87P6CGN\",\"slot\":\"nvme0\",\"model_number\":\"INTEL SSDPFUKX076T1\"}],\"manufacture_name\":\"ZTE\",\"PSUList\":[{\"manufacturer_product_name\":\"GW-CRPS2000DW\",\"extra_params\":{\"consumption\":\"2000 W\"},\"itemcode\":\"0513010A0070\",\"num\":\"2\",\"part_number\":\"GW-CRPS2000DW\",\"item_name\":\"GW_交流2000W服务器电源无12Vsb\",\"brand_name\":\"Great Wall\",\"sn\":\"2M090018506\",\"slot\":\"PSU0\",\"model_number\":\"GW-CRPS2000DW\"},{\"manufacturer_product_name\":\"GW-CRPS2000DW\",\"extra_params\":{\"consumption\":\"2000 W\"},\"itemcode\":\"0513010A0070\",\"num\":\"2\",\"part_number\":\"GW-CRPS2000DW\",\"item_name\":\"GW_交流2000W服务器电源无12Vsb\",\"brand_name\":\"Great Wall\",\"sn\":\"2M120025269\",\"slot\":\"PSU1\",\"model_number\":\"GW-CRPS2000DW\"}],\"package_name\":\"服务器DZH R5310G3F-DVT-S3B\",\"cpuList\":[{\"itemcode\":\"008020100879\",\"num\":\"2\",\"part_number\":\"Sanechips(R) ZhuFengXin Processor\",\"item_name\":\"128核 3.0GHz 嵌入式通讯高性能ARM处理器(字节 AVAP)\",\"brand_name\":\"ZTE\",\"sn\":\"00000000000000145532473437350207\",\"slot\":\"CPU 0\"},{\"itemcode\":\"008020100879\",\"num\":\"2\",\"part_number\":\"Sanechips(R) ZhuFengXin Processor\",\"item_name\":\"128核 3.0GHz 嵌入式通讯高性能ARM处理器(字节 AVAP)\",\"brand_name\":\"ZTE\",\"sn\":\"00000000000000145532473437350802\",\"slot\":\"CPU 1\"}],\"GPUList\":[],\"sn\":\"210120123279\",\"mainboard\":{\"bios_ver\":\"0E.***********\",\"itemcode\":\"130000209834\",\"num\":\"1\",\"bmc_firmware_ver\":\"3.02.0e\",\"part_number\":\"\",\"item_name\":\"R5310G3F主板\",\"brand_name\":\"ZTE\",\"bmc_mac\":\"BC1695007E63;BC1695007E64;BC1695007E65\",\"bios_ver_date\":\"01/10/2024\",\"sn\":\"751998000014\",\"slot\":\"1\",\"cpld_ver\":\"00.00.0104\"},\"raidList\":[],\"asset_num\":\"2024-lx-srv-079905\",\"node_asset_num\":\"2024-lx-srv-079905\"}";
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        completeSetDataLogServiceImpl.querySspTaskInfoByte("12");

        result=null;
        PowerMockito.when(zmsStationLogUploadService.getZsStationLog(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        Assert.assertNotNull(completeSetDataLogServiceImpl.querySspTaskInfoByte("12"));

    }

    @Test
    @PrepareForTest({HttpClientUtil.class,CenterfactoryRemoteService.class})
    public void pushDataToB2B() throws Exception {
        TaskHeadInfo taskHeadInfo =new TaskHeadInfo();
        taskHeadInfo.setServerSn("11");
        completeSetDataLogServiceImpl.pushDataToB2B(taskHeadInfo,"11","22");
        Assert.assertNull(null);
    }
}
