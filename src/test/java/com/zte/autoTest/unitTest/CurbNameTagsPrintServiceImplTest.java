package com.zte.autoTest.unitTest;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.datawb.CfgCodeRuleItemService;
import com.zte.application.datawb.impl.CurbNameTagsPrintServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.domain.model.datawb.CfgCodeRuleItemRepository;
import com.zte.domain.model.datawb.CurbNameTagsPrintRepository;
import com.zte.domain.model.datawb.DeliverEntityCycleRepository;
import com.zte.domain.model.datawb.MesEntryKafkaSfcRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.Normalizer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;


@RunWith(PowerMockRunner.class)
@PrepareForTest({DataServiceClientV1.class, CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class,CommonUtils.class, Normalizer.class,GbomCsgInfosDTO.class })
@PowerMockIgnore({"javax.net.ssl.*", "javax.security.*"})
public class CurbNameTagsPrintServiceImplTest {

    @InjectMocks
    CurbNameTagsPrintServiceImpl curbNameTagsPrintService;

    @Mock
    CurbNameTagsPrintRepository curbNameTagsPrintRepository ;

    @Mock
    CfgCodeRuleItemService cfgCodeRuleItemService;

    @Mock
    CfgCodeRuleItemRepository cfgCodeRuleItemRepository;
    @Mock
    DeliverEntityCycleRepository deliverEntityCycleRepository;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private MesEntryKafkaSfcRepository mesEntryKafkaSfcRepository;


    @Before
    public void init() {
        PowerMockito.mockStatic( MESHttpHelper.class, HttpClientUtil.class,GbomCsgInfosDTO.class,
                JacksonJsonConverUtil.class, ServiceDataBuilderUtil.class,Normalizer.class);
    }
    @Test
    public void getCPQDTest() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(Normalizer.class);
        PowerMockito.mockStatic(GbomCsgInfosDTO.class);
        try {
            String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            // configDetailIds.add("1274911163");
            String result = "{\n" +
                    "    \"code\": {\n" +
                    "        \"code\": \"0000\",\n" +
                    "        \"msgId\": \"RetCode.Success\",\n" +
                    "        \"msg\": \"操作成功\"\n" +
                    "    },\n" +
                    "    \"bo\": {\n" +
                    "        \"gbomCsgInfos\": {\n" +
                    "            \"current\": 1,\n" +
                    "            \"total\": 1,\n" +
                    "            \"rows\": [\n" +
                    "                {\n" +
                    "                    \"configDetailId\": 1528678051,\n" +
                    "                    \"mfgSiteId\": 226702899,\n" +
                    "                    \"contractHeaderId\": 6092837,\n" +
                    "                    \"contractNumber\": \"S3YD2023071701CPEF-GZP021\",\n" +
                    "                    \"configNumber\": \"S3YD2023071701CPEF-GZP021D\",\n" +
                    "                    \"siteAddrGuid\": 232524373,\n" +
                    "                    \"modelCode\": \"180000489531\",\n" +
                    "                    \"modelNameCn\": \"ZXHN F631A\",\n" +
                    "                    \"modelNameEn\": \"ZXHN F631A\",\n" +
                    "                    \"modelQty\": 1,\n" +
                    "                    \"ptoNo\": null,\n" +
                    "                    \"moduleCode\": \"180000489535\",\n" +
                    "                    \"moduleQty\": 1,\n" +
                    "                    \"csSource\": \"Mapping\",\n" +
                    "                    \"sbomId\": \"232524373_14989902_14989904_15250391\",\n" +
                    "                    \"sbomCode\": \"180000501152\",\n" +
                    "                    \"sbomNameCn\": \"GPON用户端设备ZXHN F631A\",\n" +
                    "                    \"sbomNameEn\": \"GPON CPE ZXHN F631A\",\n" +
                    "                    \"sbomUnitCn\": \"台\",\n" +
                    "                    \"sbomUnitEn\": \"SET\",\n" +
                    "                    \"sbomQty\": 500,\n" +
                    "                    \"sbomTotalQty\": 500,\n" +
                    "                    \"isMainSbom\": \"Y\",\n" +
                    "                    \"gbomItemId\": \"33729684\",\n" +
                    "                    \"gbomCode\": \"125007031196\",\n" +
                    "                    \"gbomNameCn\": \"吉比特无源光纤接入用户端设备(GPON ONU)\",\n" +
                    "                    \"gbomNameEn\": \"GPON ONU\",\n" +
                    "                    \"gbomUnitCn\": \"件\",\n" +
                    "                    \"gbomUnitEn\": \"PCS\",\n" +
                    "                    \"gbomQty\": 1,\n" +
                    "                    \"gbomTotalQty\": 500,\n" +
                    "                    \"sgRatio\": 1,\n" +
                    "                    \"createdBy\": null,\n" +
                    "                    \"creationDate\": \"2023-12-18 13:11:43\",\n" +
                    "                    \"lastUpdatedBy\": null,\n" +
                    "                    \"lastUpdateDate\": \"2023-12-18 13:11:43\",\n" +
                    "                    \"cbomList\": [\n" +
                    "                        {\n" +
                    "                            \"suSiteId\": null,\n" +
                    "                            \"suSiteSbomQty\": null,\n" +
                    "                            \"isMainSbom\": \"Y\",\n" +
                    "                            \"cbomId\": null,\n" +
                    "                            \"cbomCode\": \"10537825\",\n" +
                    "                            \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                    "                            \"cbomNameEn\": \"\",\n" +
                    "                            \"cbomUnitCn\": \"套\",\n" +
                    "                            \"cbomUnitEn\": null,\n" +
                    "                            \"quotationMaterialCode\": \"\",\n" +
                    "                            \"quotationMaterialNameCn\": \"\",\n" +
                    "                            \"quotationMaterialNameEn\": \"\",\n" +
                    "                            \"csRatio\": 1,\n" +
                    "                            \"cgRatio\": 1,\n" +
                    "                            \"isCbomSnManagement\": \"\",\n" +
                    "                            \"seq\": 1\n" +
                    "                        }\n" +
                    "                    ],\n" +
                    "                    \"isMainGbom\": \"Y\",\n" +
                    "                    \"cbomCode\": \"10537825\",\n" +
                    "                    \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                    "                    \"cbomNameEn\": \"\",\n" +
                    "                    \"cbomUnitCn\": \"套\",\n" +
                    "                    \"cbomUnitEn\": null,\n" +
                    "                    \"sbomSplitCbomQty\": 1,\n" +
                    "                    \"splitSbomQty\": 1,\n" +
                    "                    \"splitCbomQty\": 1,\n" +
                    "                    \"isCbomSnManagement\": \"\"\n" +
                    "                }\n" +
                    "            ]\n" +
                    "        },\n" +
                    "        \"csgExceptionItems\": null\n" +
                    "    },\n" +
                    "    \"other\": null\n" +
                    "}";
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                    .thenReturn(result);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode json = objectMapper.readTree(result);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", configDetailIds);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}
        try {
            String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            // configDetailIds.add("1274911163");
            String result = "{\n" +
                    "    \"code\": {\n" +
                    "        \"code\": \"0000\",\n" +
                    "        \"msgId\": \"RetCode.Success\",\n" +
                    "        \"msg\": \"操作成功\"\n" +
                    "    },\n" +
                    "    \"bo\": {\n" +
                    "        \"gbomCsgInfos\": {\n" +
                    "            \"current\": 1,\n" +
                    "            \"total\": 1,\n" +
                    "            \"rows\": [\n" +
                    "                {\n" +
                    "                    \"configDetailId\": 1528678051,\n" +
                    "                    \"mfgSiteId\": 226702899,\n" +
                    "                    \"contractHeaderId\": 6092837,\n" +
                    "                    \"contractNumber\": \"S3YD2023071701CPEF-GZP021\",\n" +
                    "                    \"configNumber\": \"S3YD2023071701CPEF-GZP021D\",\n" +
                    "                    \"siteAddrGuid\": 232524373,\n" +
                    "                    \"modelCode\": \"180000489531\",\n" +
                    "                    \"modelNameCn\": \"ZXHN F631A\",\n" +
                    "                    \"modelNameEn\": \"ZXHN F631A\",\n" +
                    "                    \"modelQty\": 1,\n" +
                    "                    \"ptoNo\": null,\n" +
                    "                    \"moduleCode\": \"180000489535\",\n" +
                    "                    \"moduleQty\": 1,\n" +
                    "                    \"csSource\": \"CUSTOMIZED\",\n" +
                    "                    \"sbomId\": \"232524373_14989902_14989904_15250391\",\n" +
                    "                    \"sbomCode\": \"180000501152\",\n" +
                    "                    \"sbomNameCn\": \"GPON用户端设备ZXHN F631A\",\n" +
                    "                    \"sbomNameEn\": \"GPON CPE ZXHN F631A\",\n" +
                    "                    \"sbomUnitCn\": \"台\",\n" +
                    "                    \"sbomUnitEn\": \"SET\",\n" +
                    "                    \"sbomQty\": 500,\n" +
                    "                    \"sbomTotalQty\": 500,\n" +
                    "                    \"isMainSbom\": \"Y\",\n" +
                    "                    \"gbomItemId\": \"33729684\",\n" +
                    "                    \"gbomCode\": \"125007031196\",\n" +
                    "                    \"gbomNameCn\": \"吉比特无源光纤接入用户端设备(GPON ONU)\",\n" +
                    "                    \"gbomNameEn\": \"GPON ONU\",\n" +
                    "                    \"gbomUnitCn\": \"件\",\n" +
                    "                    \"gbomUnitEn\": \"PCS\",\n" +
                    "                    \"gbomQty\": 1,\n" +
                    "                    \"gbomTotalQty\": 500,\n" +
                    "                    \"sgRatio\": 1,\n" +
                    "                    \"createdBy\": null,\n" +
                    "                    \"creationDate\": \"2023-12-18 13:11:43\",\n" +
                    "                    \"lastUpdatedBy\": null,\n" +
                    "                    \"lastUpdateDate\": \"2023-12-18 13:11:43\",\n" +
                    "                    \"cbomList\": [\n" +
                    "                        {\n" +
                    "                            \"suSiteId\": null,\n" +
                    "                            \"suSiteSbomQty\": null,\n" +
                    "                            \"isMainSbom\": \"Y\",\n" +
                    "                            \"cbomId\": null,\n" +
                    "                            \"cbomCode\": \"10537825\",\n" +
                    "                            \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                    "                            \"cbomNameEn\": \"\",\n" +
                    "                            \"cbomUnitCn\": \"套\",\n" +
                    "                            \"cbomUnitEn\": null,\n" +
                    "                            \"quotationMaterialCode\": \"\",\n" +
                    "                            \"quotationMaterialNameCn\": \"\",\n" +
                    "                            \"quotationMaterialNameEn\": \"\",\n" +
                    "                            \"csRatio\": 1,\n" +
                    "                            \"cgRatio\": 1,\n" +
                    "                            \"isCbomSnManagement\": \"\",\n" +
                    "                            \"seq\": 1\n" +
                    "                        }\n" +
                    "                    ],\n" +
                    "                    \"isMainGbom\": \"Y\",\n" +
                    "                    \"cbomCode\": \"10537825\",\n" +
                    "                    \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                    "                    \"cbomNameEn\": \"\",\n" +
                    "                    \"cbomUnitCn\": \"套\",\n" +
                    "                    \"cbomUnitEn\": null,\n" +
                    "                    \"sbomSplitCbomQty\": 1,\n" +
                    "                    \"splitSbomQty\": 1,\n" +
                    "                    \"splitCbomQty\": 1,\n" +
                    "                    \"isCbomSnManagement\": \"\"\n" +
                    "                }\n" +
                    "            ]\n" +
                    "        },\n" +
                    "        \"csgExceptionItems\": null\n" +
                    "    },\n" +
                    "    \"other\": null\n" +
                    "}";
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                    .thenReturn(result);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode json = objectMapper.readTree(result);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

            List<CPQDDTO> result1 = curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", configDetailIds);
            List<CPQDDTO> result2 = new ArrayList<>();
            CPQDDTO dto = new CPQDDTO();
            dto.setConfigDetailId("1528678051");
            dto.setCsSource("CUSTOMIZED");
            dto.setCbomCode("10537825");
            dto.setCbomNameCn("融合企业网关\\中兴\\ZXHN F631A");
            dto.setCbomUnitCn("套");
            dto.setCbomUnitEn(null);
            dto.setSplitCbomQty(1.0f);
            dto.setCbomNameEn("");
            dto.setSplitSbomQty(1.0f);
            dto.setIsMainGbom("Y");
            result2.add(dto);
            Assert.assertEquals(result2.get(0).getConfigDetailId(),result1.get(0).getConfigDetailId());
        }catch (Exception e){}

        try {
            String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            // configDetailIds.add("1274911163");
            String result = "{\n" +
                    "    \"code\": {\n" +
                    "        \"code\": \"1111\",\n" +
                    "        \"msgId\": \"RetCode.Success\",\n" +
                    "        \"msg\": \"操作成功\"\n" +
                    "    },\n" +
                    "    \"bo\": {\n" +
                    "        \"gbomCsgInfos\": {\n" +
                    "            \"current\": 1,\n" +
                    "            \"total\": 1,\n" +
                    "            \"rows\": [\n" +
                    "                {\n" +
                    "                    \"configDetailId\": 1528678051,\n" +
                    "                    \"mfgSiteId\": 226702899,\n" +
                    "                    \"contractHeaderId\": 6092837,\n" +
                    "                    \"contractNumber\": \"S3YD2023071701CPEF-GZP021\",\n" +
                    "                    \"configNumber\": \"S3YD2023071701CPEF-GZP021D\",\n" +
                    "                    \"siteAddrGuid\": 232524373,\n" +
                    "                    \"modelCode\": \"180000489531\",\n" +
                    "                    \"modelNameCn\": \"ZXHN F631A\",\n" +
                    "                    \"modelNameEn\": \"ZXHN F631A\",\n" +
                    "                    \"modelQty\": 1,\n" +
                    "                    \"ptoNo\": null,\n" +
                    "                    \"moduleCode\": \"180000489535\",\n" +
                    "                    \"moduleQty\": 1,\n" +
                    "                    \"csSource\": \"CUSTOMIZED\",\n" +
                    "                    \"sbomId\": \"232524373_14989902_14989904_15250391\",\n" +
                    "                    \"sbomCode\": \"180000501152\",\n" +
                    "                    \"sbomNameCn\": \"GPON用户端设备ZXHN F631A\",\n" +
                    "                    \"sbomNameEn\": \"GPON CPE ZXHN F631A\",\n" +
                    "                    \"sbomUnitCn\": \"台\",\n" +
                    "                    \"sbomUnitEn\": \"SET\",\n" +
                    "                    \"sbomQty\": 500,\n" +
                    "                    \"sbomTotalQty\": 500,\n" +
                    "                    \"isMainSbom\": \"Y\",\n" +
                    "                    \"gbomItemId\": \"33729684\",\n" +
                    "                    \"gbomCode\": \"125007031196\",\n" +
                    "                    \"gbomNameCn\": \"吉比特无源光纤接入用户端设备(GPON ONU)\",\n" +
                    "                    \"gbomNameEn\": \"GPON ONU\",\n" +
                    "                    \"gbomUnitCn\": \"件\",\n" +
                    "                    \"gbomUnitEn\": \"PCS\",\n" +
                    "                    \"gbomQty\": 1,\n" +
                    "                    \"gbomTotalQty\": 500,\n" +
                    "                    \"sgRatio\": 1,\n" +
                    "                    \"createdBy\": null,\n" +
                    "                    \"creationDate\": \"2023-12-18 13:11:43\",\n" +
                    "                    \"lastUpdatedBy\": null,\n" +
                    "                    \"lastUpdateDate\": \"2023-12-18 13:11:43\",\n" +
                    "                    \"cbomList\": [\n" +
                    "                        {\n" +
                    "                            \"suSiteId\": null,\n" +
                    "                            \"suSiteSbomQty\": null,\n" +
                    "                            \"isMainSbom\": \"Y\",\n" +
                    "                            \"cbomId\": null,\n" +
                    "                            \"cbomCode\": \"10537825\",\n" +
                    "                            \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                    "                            \"cbomNameEn\": \"\",\n" +
                    "                            \"cbomUnitCn\": \"套\",\n" +
                    "                            \"cbomUnitEn\": null,\n" +
                    "                            \"quotationMaterialCode\": \"\",\n" +
                    "                            \"quotationMaterialNameCn\": \"\",\n" +
                    "                            \"quotationMaterialNameEn\": \"\",\n" +
                    "                            \"csRatio\": 1,\n" +
                    "                            \"cgRatio\": 1,\n" +
                    "                            \"isCbomSnManagement\": \"\",\n" +
                    "                            \"seq\": 1\n" +
                    "                        }\n" +
                    "                    ],\n" +
                    "                    \"isMainGbom\": \"Y\",\n" +
                    "                    \"cbomCode\": \"10537825\",\n" +
                    "                    \"cbomNameCn\": \"融合企业网关\\\\中兴\\\\ZXHN F631A\",\n" +
                    "                    \"cbomNameEn\": \"\",\n" +
                    "                    \"cbomUnitCn\": \"套\",\n" +
                    "                    \"cbomUnitEn\": null,\n" +
                    "                    \"sbomSplitCbomQty\": 1,\n" +
                    "                    \"splitSbomQty\": 1,\n" +
                    "                    \"splitCbomQty\": 1,\n" +
                    "                    \"isCbomSnManagement\": \"\"\n" +
                    "                }\n" +
                    "            ]\n" +
                    "        },\n" +
                    "        \"csgExceptionItems\": null\n" +
                    "    },\n" +
                    "    \"other\": null\n" +
                    "}";
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                    .thenReturn(result);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode json = objectMapper.readTree(result);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", configDetailIds);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}

        try {
            String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            // configDetailIds.add("1274911163");
            String result = "{\n" +
                    "    \"code\": {\n" +
                    "        \"code\": \"1111\",\n" +
                    "        \"msgId\": \"RetCode.Success\",\n" +
                    "        \"msg\": \"操作成功\"\n" +
                    "    },\n" +
                    "    \"bo\": {\n" +
                    "        \"gbomCsgInfos\": {\n" +
                    "            \"current\": 1,\n" +
                    "            \"total\": 1,\n" +
                    "            \"rows\": [\n" +
                    "            ]\n" +
                    "        },\n" +
                    "        \"csgExceptionItems\": null\n" +
                    "    },\n" +
                    "    \"other\": null\n" +
                    "}";
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                    .thenReturn(result);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode json = objectMapper.readTree(result);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", configDetailIds);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}


        try {
            String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            // configDetailIds.add("1274911163");
            String result = "{\n" +
                    "    \"code\": {\n" +
                    "        \"code\": \"0000\",\n" +
                    "        \"msgId\": \"RetCode.Success\",\n" +
                    "        \"msg\": \"操作成功\"\n" +
                    "    },\n" +
                    "    \"other\": null\n" +
                    "}";
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                    .thenReturn(result);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode json = objectMapper.readTree(result);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", configDetailIds);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}

        try {
            String url = "http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
            String PageSize = "2";
            PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            // configDetailIds.add("1274911163");
            String result = "{\n" +
                    "    \"bo\": null,\n" +
                    "    \"other\": null\n" +
                    "}";
            PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                    .thenReturn(result);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode json = objectMapper.readTree(result);
            PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
            PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);

            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", configDetailIds);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}

        try {

            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD(null, configDetailIds);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}

        try {

            List<String> configDetailIds = new ArrayList<>();
            configDetailIds.add("1274911166");
            List<CPQDDTO> result1= curbNameTagsPrintService.getCPQD("DIGI2022090901Z1", null);
            Assert.assertEquals(new ArrayList<>(),result1);
        }catch (Exception e){}

    }
    @Test
    public void testCurbNameTagsPrintService() throws Exception {
        try {

            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            curbNameTagsPrintService.curbNameTagsPrint(null);
            CurbInfoUploadDTO params11=new CurbInfoUploadDTO();
            params11.setBillId("");
            params11.setBillNo("");
            curbNameTagsPrintService.curbNameTagsPrint(params11);

            params11=new CurbInfoUploadDTO();
            params11.setBillNo("123");
            curbNameTagsPrintService.curbNameTagsPrint(params11);

            params11=new CurbInfoUploadDTO();
            params11.setBillId("123");
            params11.setBillNo("123");
            curbNameTagsPrintService.curbNameTagsPrint(params11);

            params11=new CurbInfoUploadDTO();
            params11.setBillId("123");
            curbNameTagsPrintService.curbNameTagsPrint(params11);

            try
            {
                List<BoqBomDTO> boqBomList =new ArrayList<>();
                BoqBomDTO model = new BoqBomDTO();
                model.setMaterialQty(333);
                model.setConfigDetailId("22");
                boqBomList.add(model);
                List<CPQDDTO> listCPQD = new ArrayList<>();
                CPQDDTO cpqd =new CPQDDTO();
                cpqd.setSplitCbomQty(22);
                cpqd.setConfigDetailId("22");
                listCPQD.add(cpqd);
                curbNameTagsPrintService.getBoqBomList(boqBomList,listCPQD);
                Assert.assertEquals(true,true);
                boqBomList =new ArrayList<>();
                model = new BoqBomDTO();
                model.setConfigDetailId("1111");
                boqBomList.add(model);
                listCPQD = new ArrayList<>();
                cpqd =new CPQDDTO();
                cpqd.setConfigDetailId("1111");
                listCPQD.add(cpqd);
                curbNameTagsPrintService.getBoqBomList(boqBomList,listCPQD);


                boqBomList =new ArrayList<>();
                model = new BoqBomDTO();
                model.setConfigDetailId("1111");
                boqBomList.add(model);
                listCPQD = new ArrayList<>();
                cpqd =new CPQDDTO();
                cpqd.setConfigDetailId("2222");
                listCPQD.add(cpqd);
                curbNameTagsPrintService.getBoqBomList(boqBomList,listCPQD);
                Assert.assertEquals(true,true);

                boqBomList =new ArrayList<>();
                model = new BoqBomDTO();
                model.setConfigDetailId("1111");
                model.setMaterialQty(1);
                boqBomList.add(model);
                listCPQD = new ArrayList<>();
                cpqd =new CPQDDTO();
                cpqd.setConfigDetailId("1111");
                listCPQD.add(cpqd);
                curbNameTagsPrintService.getBoqBomList(boqBomList,listCPQD);
                Assert.assertEquals(true,true);

                boqBomList =new ArrayList<>();
                model = new BoqBomDTO();
                model.setConfigDetailId("1111");
                boqBomList.add(model);
                listCPQD = new ArrayList<>();
                cpqd =new CPQDDTO();
                cpqd.setConfigDetailId("1111");
                cpqd.setSplitCbomQty(22);
                listCPQD.add(cpqd);
                curbNameTagsPrintService.getBoqBomList(boqBomList,listCPQD);
                Assert.assertEquals(true,true);
            }
            catch (Exception e){
                Assert.assertEquals(e.getMessage(), e.getMessage());
            }

            try
            {
                List<BoxNameDTO> boxNameList =new ArrayList<>();
                BoxNameDTO model= new BoxNameDTO();
                model.setDevicesCode("2355");
                boxNameList.add(model);
                List<ICCDTO > iccdto = new ArrayList<>();
                ICCDTO icc = new ICCDTO();
                icc.setProjectName("2355");
                icc.setCustomerErpPoNumber("2355");
                icc.setProjectNo("2355");
                iccdto.add(icc);

                String mfgSiteType ="22";
                String boqName="123";
                curbNameTagsPrintService.getBoxNameList(boxNameList,iccdto,mfgSiteType, boqName);
                boxNameList =new ArrayList<>();
                model= new BoxNameDTO();
                boxNameList.add(model);
                curbNameTagsPrintService.getBoxNameList(boxNameList,iccdto,mfgSiteType, boqName);
                iccdto=null;
                curbNameTagsPrintService.getBoxNameList(boxNameList,iccdto,mfgSiteType, boqName);
                iccdto = new ArrayList<>();
                icc = new ICCDTO();
                icc.setProjectNo("");
                icc.setProjectName("");
                iccdto.add(icc);
                curbNameTagsPrintService.getBoxNameList(boxNameList,iccdto,mfgSiteType, boqName);



                Assert.assertEquals(true,true);
            }
            catch (Exception e){
                Assert.assertTrue(e.getMessage().length()>0);
            }

            try {
                curbNameTagsPrintService.getCPQD("DIGI2022090901Z1",null);
                Assert.assertEquals(true,true);

                List<CPQDDTO> listCPQD=new ArrayList<>();
                CPQDDTO cpqddto=new CPQDDTO();
                cpqddto.setCbomNameCn("12312");
                listCPQD.add(cpqddto);




                String url ="http://configst.zte.com.cn/zte-crm-cpqd-csg/csgInfo/containError";
                PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_URL_LOOK_UP_CODE)).thenReturn(url);
                String PageSize = "2";
                PowerMockito.when(cfgCodeRuleItemRepository.getDicDescription(Constant.CSG_PAGESIZE_LOOK_UP_CODE)).thenReturn(PageSize);
                List<String> configDetailIds =new ArrayList<>();
                configDetailIds.add("1274911166");
                configDetailIds.add("1274911163");
                curbNameTagsPrintService.getCPQD("DIGI2022090901Z1",configDetailIds);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertEquals(null, e.getMessage());
            }

            try {
                String url ="http://icc.zte.com.cn/zte-sac-icc-logisticscoordination/external/delivery/note/info/mes";
                PowerMockito.when( cfgCodeRuleItemRepository.getDicDescription(any())).thenReturn(url);
                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93","JF002S20230223006");
                Assert.assertEquals(true,true);
                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93777","JF002S20230223006");
                Assert.assertEquals(true,true);

                /* Started by AICoder, pid:5a2b3dfd03594692a249076f467d471f */
                // 使用PowerMockito来模拟HttpClientUtil.httpGet方法的返回值
                String result1 = "{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"Success\"\n" +
                        "    },\n" +
                        "    \"bo\": [\n" +
                        "        {\n" +
                        "            \"pkInfoId\": \"1733347142826033155\",\n" +
                        "            \"contractId\": \"6711231\",\n" +
                        "            \"contractNo\": \"81SBOM20230703077\",\n" +
                        "            \"deliverySetId\": \"3270996\",\n" +
                        "            \"deliverySet\": \"JF002S20230223006\",\n" +
                        "            \"productInformation\": \"SPN\",\n" +
                        "            \"supplyNotificationNo\": \"APO304610230200320\",\n" +
                        "            \"supplyIssuanceDate\": \"2023-02-14 00:00:00\",\n" +
                        "            \"requiringTel\": \"古议成：18887609736\",\n" +
                        "            \"requiringUnit\": \"文山移动\",\n" +
                        "            \"salesManagerInfor\": \"伏艳刚13648847412\",\n" +
                        "            \"flagBatch\": \"是\",\n" +
                        "            \"projectNo\": \"C23304626CA1001\",\n" +
                        "            \"projectName\": \"中国移动云南公司2023年文山分公司富宁县田蓬口岸5G专网项目_文山分公司\",\n" +
                        "            \"lockStatus\": \"Y\",\n" +
                        "            \"enableFlag\": 1,\n" +
                        "            \"createDate\": \"2023-03-08 14:01:20\",\n" +
                        "            \"createBy\": \"nullundefined\",\n" +
                        "            \"lastUpdateDate\": \"2023-03-08 14:17:18\",\n" +
                        "            \"lastUpdateBy\": \"1009436910094369\",\n" +
                        "            \"operatorType\": \"AB004\",\n" +
                        "            \"representOfficeCode\": \"ORG2227654\",\n" +
                        "            \"poNumber\": null,\n" +
                        "            \"customerErpPoNumber\": null\n" +
                        "        }\n" +
                        "    ],\n" +
                        "    \"other\": null,\n" +
                        "    \"responseRule\": \"msa\"\n" +
                        "}";
                PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);

                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93777", "JF002S20230223006");

                PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);

                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93777", "JF002S20230223006");



                /* Ended by AICoder, pid:5a2b3dfd03594692a249076f467d471f */

                result1 = "{\n" +
                        "    \"bo\": null,\n" +
                        "    \"other\": null,\n" +
                        "    \"responseRule\": \"msa\"\n" +
                        "}";
                PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93777", "JF002S20230223006");

                result1 = "{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": 2222,\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"Success\"\n" +
                        "    },\n" +
                        "    \"bo\": null,\n" +
                        "    \"other\": null,\n" +
                        "    \"responseRule\": \"msa\"\n" +
                        "}";
                PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93777", "JF002S20230223006");

                result1 = "{\n" +
                        "    \"code\": {\n" +
                        "        \"code\": \"0000\",\n" +
                        "        \"msgId\": \"RetCode.Success\",\n" +
                        "        \"msg\": \"Success\"\n" +
                        "    },\n" +
                        "    \"bo\": null,\n" +
                        "    \"other\": null,\n" +
                        "    \"responseRule\": \"msa\"\n" +
                        "}";
                PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result1);
                curbNameTagsPrintService.getICC("S3KM2021070601PTNFP93777", "JF002S20230223006");

            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }

            PowerMockito.when(deliverEntityCycleRepository.getMfgSiteType(any())).thenReturn("333");
            CurbInfoUploadDTO params=new CurbInfoUploadDTO();
            //params=null;
            try {
                curbNameTagsPrintService.curbNameTagsPrint(null);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }
            params.setBillId("71560097");
            try{
                curbNameTagsPrintService.curbNameTagsPrint(params);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }
            params.setBillId("12");
            Long ll=Long.parseLong("0");
            PowerMockito.when(curbNameTagsPrintRepository.querryBoxNameCount(any())).thenReturn(ll);
            try{
                curbNameTagsPrintService.curbNameTagsPrint(params);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }


            ll=Long.parseLong("1");
            PowerMockito.when(curbNameTagsPrintRepository.querryBoxNameCount(any())).thenReturn(ll);
            PowerMockito.when(curbNameTagsPrintRepository.boxNameSelect(any())).thenReturn(null);
            try{
                curbNameTagsPrintService.curbNameTagsPrint(params);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }

            params.setBillId("12");
            Long l=Long.parseLong("12");
            PowerMockito.when(curbNameTagsPrintRepository.querryBoxNameCount(any())).thenReturn(l);

            List<BoxNameDTO> boxNameList=new ArrayList<>();
            BoxNameDTO b=new BoxNameDTO();
            b.setTempLamguage("123");
            boxNameList.add(b);
            PowerMockito.when(curbNameTagsPrintRepository.boxNameSelect(any())).thenReturn(boxNameList);
            List<BoqBomDTO> boqBomList=new ArrayList<>();
            PowerMockito.when(curbNameTagsPrintRepository.boqBomSelect(any())).thenReturn(boqBomList);

            BoqBomDTO bomDTO=new BoqBomDTO();
            bomDTO.setConfigDetailId("12333");
            bomDTO.setBoqLevel("12333");
            boqBomList.add(bomDTO);
            PowerMockito.when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);
            //Map<String, Object> map = new HashMap<>();
           // Map<String, Object> inMap = new HashMap<>();
           // map.put("box_id", "1111");
          //  map.put("resultCursor", inMap);
           // doNothing().when(mesEntryKafkaSfcRepository).checkTallySfc(map);
            try{
                curbNameTagsPrintService.curbNameTagsPrint(params);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }

            boqBomList=new ArrayList<>();
            bomDTO=new BoqBomDTO();
            bomDTO.setConfigDetailId("12333");
            bomDTO.setBoqLevel(".12333");
            boqBomList.add(bomDTO);
            PowerMockito.when(mesEntryKafkaSfcRepository.boqBomSelectSfc(Mockito.any())).thenReturn(boqBomList);



           // List<String> list = new ArrayList<>();
          //  list.add("11111");
          //  inMap.put("111", list);
          //  map.put("resultCursor", inMap);
          //  Mockito.doNothing().when(mesEntryKafkaSfcRepository).checkTallySfc(map);
            try{
                curbNameTagsPrintService.curbNameTagsPrint(params);
                Assert.assertEquals(true,true);
            }
            catch (Exception e)
            {
                Assert.assertTrue(e.getMessage().length()>0);
            }

        }
        catch (Exception e){
             e.printStackTrace();
             Assert.assertTrue(e.getMessage().length()>0);
        }
    }


}
