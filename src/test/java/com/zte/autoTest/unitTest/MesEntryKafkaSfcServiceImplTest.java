package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.MesEntryKafkaSfcServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.MesEntryKafkaSfcRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2021年7月14日17:47:50
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class MesEntryKafkaSfcServiceImplTest extends BaseTestCase {

    @InjectMocks
    MesEntryKafkaSfcServiceImpl mesEntryKafkaSfcService;

    @Mock
    MesEntryKafkaSfcRepository mesEntryKafkaSfcRepository;

    @Test
    public void getBoxScanList() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
        Assert.assertNotNull(mesEntryKafkaSfcService.getBoxScanList("123"));
        mesEntryKafkaSfcRepository.selectBoxupScanKeys("123");

    }


}
