package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.RepairServiceImpl;
import com.zte.domain.model.datawb.RepairRepository;
import com.zte.interfaces.dto.RepairRestoreDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;


public class RepairServiceImplTest extends BaseTestCase {
    @InjectMocks
    RepairServiceImpl service;
    @Mock
    private RepairRepository repairRepository;

    @Test
    public void repairRestore() {
        List<RepairRestoreDTO> repairRestoreDTOS = new ArrayList<>();

        // 测试1
        service.repairRestore(repairRestoreDTOS);

        // 测试2
        RepairRestoreDTO p1 = new RepairRestoreDTO();
        repairRestoreDTOS.add(p1);
        p1.setSn("711567200340");
        p1.setResult("良品");
        RepairRestoreDTO p2 = new RepairRestoreDTO();
        repairRestoreDTOS.add(p2);
        p2.setSn("711567200341");
        p2.setResult("报废");
        PowerMockito.when(repairRepository.restoreBak(Mockito.any())).thenReturn(2);
        PowerMockito.when(repairRepository.restoreUpdate(Mockito.any(), Mockito.any())).thenReturn(2);
        service.repairRestore(repairRestoreDTOS);
        Assert.assertNotNull(repairRestoreDTOS);
    }
}
