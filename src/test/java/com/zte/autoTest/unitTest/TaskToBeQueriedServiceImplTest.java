package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.TaskToBeQueriedServiceImpl;
import com.zte.interfaces.dto.TaskToBeQueriedDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

public class TaskToBeQueriedServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    TaskToBeQueriedServiceImpl service;

    @Test
    public void checkInputParam() {
        Assert.assertNotNull(service.checkInputParam(new TaskToBeQueriedDTO(){{
            setContractHeaderId("1");
            setEntityName("1");
            setEntityId("1");
        }}));
    }
}