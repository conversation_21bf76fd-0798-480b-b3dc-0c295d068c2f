package com.zte.autoTest.unitTest;


import com.zte.application.datawb.impl.BoqBomBoxPrintInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BoqBomBoxPrintInfoRepository;
import com.zte.interfaces.dto.BoxUpBillInfoDTO;
import com.zte.interfaces.dto.EuItemDTO;
import com.zte.interfaces.dto.ItemDTO;
import com.zte.interfaces.dto.PrintInfoInDTO;
import com.zte.interfaces.dto.PrintInfoOutDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest( { CommonUtils.class })
public class BoqBomBoxPrintInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    BoqBomBoxPrintInfoServiceImpl boqBomBoxPrintInfoService;

    @Mock
    BoqBomBoxPrintInfoRepository boqBomBoxPrintInfoRepository;

    @Test
    public void boqBomBoxPrint() throws Exception {
        try {



            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");

            Map<String, Object> outMap=new HashMap<String,Object>();
            PrintInfoOutDTO result=new PrintInfoOutDTO();
            boqBomBoxPrintInfoService.getBoqPrintInfo(outMap, result);
           boqBomBoxPrintInfoService.getBoqPrintInfoEu(outMap, result);

            outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_N);
            boqBomBoxPrintInfoService.getBoqPrintInfo(outMap, result);
            boqBomBoxPrintInfoService.getBoqPrintInfoEu(outMap, result);

            outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_Y);
            outMap.put(Constant.STRING_YY, Constant.FLAG_N);
            boqBomBoxPrintInfoService.getBoqPrintInfo(outMap, result);
            outMap.put(Constant.STRING_IS_INFOC, Constant.FLAG_N);
           boqBomBoxPrintInfoService.getBoqPrintInfoEu(outMap, result);

            outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_Y);
            outMap.put(Constant.STRING_YY, Constant.FLAG_Y);
            outMap.put(Constant.STRING_ZC, Constant.FLAG_Y);
            outMap.put(Constant.STRING_XN, Constant.FLAG_Y);
            boqBomBoxPrintInfoService.getBoqPrintInfo(outMap, result);
            outMap.put(Constant.STRING_IS_INFOC, Constant.FLAG_Y);
            boqBomBoxPrintInfoService.getBoqPrintInfoEu(outMap, result);

            outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_Y);
            outMap.put(Constant.STRING_YY, Constant.FLAG_Y);
            outMap.put(Constant.STRING_ZC, Constant.FLAG_N);
            outMap.put(Constant.STRING_XN, Constant.FLAG_N);
            boqBomBoxPrintInfoService.getBoqPrintInfo(outMap, result);

            outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_Y);
            outMap.put(Constant.STRING_YY, Constant.FLAG_Y);
            outMap.put(Constant.STRING_ZC, Constant.FLAG_N);
            outMap.put(Constant.STRING_XN, Constant.FLAG_N);
            boqBomBoxPrintInfoService.getBoqPrintInfo(outMap, result);

            PrintInfoInDTO params=new PrintInfoInDTO();
            params.setOrgId("635");
            boqBomBoxPrintInfoService.paramInCheck(params);

            params.setBillNo("123");
            params.setOrgId(null);
            boqBomBoxPrintInfoService.paramInCheck(params);

            params.setBillNo("123");
            params.setOrgId("635");
            boqBomBoxPrintInfoService.paramInCheck(params);

            List< BoxUpBillInfoDTO > ds=new ArrayList<>();
            BoxUpBillInfoDTO b=new BoxUpBillInfoDTO();
            ds.add(b);
            BoxUpBillInfoDTO b1=new BoxUpBillInfoDTO();
            b1.setLevel("BOXUP_FLAG");
            ds.add(b1);
            BoxUpBillInfoDTO b11=new BoxUpBillInfoDTO();
            b11.setLevel("");
            b11.setPtoBoqName("");
            b11.setItemCode("123");
            ds.add(b11);
            BoxUpBillInfoDTO b2=new BoxUpBillInfoDTO();
            b2.setLevel("1.1.1.1");
            b2.setItemCode("12");
            ds.add(b2);
            BoxUpBillInfoDTO b3=new BoxUpBillInfoDTO();
            b3.setLevel("1.1.1");
            b3.setItemCode("12");
            ds.add(b3);
            b11.setEccBrand("");
            b11.setMemo("12,");
            b11.setCusProjectName("213");
            b11.setMfgSiteCode("3e");
            b11.setModuleZyCode("12");
            b11.setUserName("123");
            b2.setEccBrand(",");
            b2.setMemo("12,");
            b2.setCusProjectName("213");
            b2.setMfgSiteCode("3e");
            b2.setModuleZyCode("12");
            b2.setUserName("123");
            b3.setEccBrand(",");
            b3.setMemo("12");
            b3.setCusProjectName("213");
            b3.setMfgSiteCode("3e");
            b3.setModuleZyCode("12");
            b3.setUserName("123");
            BoxUpBillInfoDTO drBoxUp=new BoxUpBillInfoDTO();
            drBoxUp.setBillNumber("12");
            drBoxUp.setMfgSiteNameEquipment("123");
            drBoxUp.setContractNumber("q23");
            drBoxUp.setEndUser("22");
            drBoxUp.setEntityName("12");
            //?????????boqBomBoxPrintInfoService.boqTableInfo(ds, drBoxUp,  0, "中文","");
            //??boqBomBoxPrintInfoService.boqTableInfo(ds, drBoxUp,  0, "","1");


            List<BoxUpBillInfoDTO> ds2=new ArrayList<>();
            BoxUpBillInfoDTO bb= new BoxUpBillInfoDTO();
            bb.setLevel("BOXUP_FLAG");
            ds2.add(bb);

            BoxUpBillInfoDTO newRow=new BoxUpBillInfoDTO();
            newRow.setItemName("123");
            boqBomBoxPrintInfoService.setPtoBoqNameCn(0,"123",ds2,newRow,"中文");
            bb.setLevel("BF");
            boqBomBoxPrintInfoService.setPtoBoqNameCn(0,"123",ds2,newRow,"中文");
            bb.setLevel("BOXUPFLAG");
            newRow.setItemEnglishName("123");
            boqBomBoxPrintInfoService.setPtoBoqNameCn(0,"123",ds2,newRow,"中");

            BoxUpBillInfoDTO newRow1=new BoxUpBillInfoDTO();
            newRow1.setBxoNoAllItem("123");
            ItemDTO drTemp=new ItemDTO();
            boqBomBoxPrintInfoService.setItemCodeBoq("1.2",drTemp,newRow1);
            boqBomBoxPrintInfoService.setItemCodeBoq("1.2.3",drTemp,newRow1);
            boqBomBoxPrintInfoService.setItemCodeBoq("*******",drTemp,newRow1);

            BoxUpBillInfoDTO drBoxUp2=new BoxUpBillInfoDTO();
            BoxUpBillInfoDTO newRow2=new BoxUpBillInfoDTO();
            PrintInfoOutDTO printInfoOutDTO=new PrintInfoOutDTO();
            drBoxUp2.setUserName("12");
            drBoxUp2.setEmpNo("132");
            drBoxUp2.setClientPo("123");

            newRow2.setImportItemType("Z");
            newRow2.setSiteCityAddress("123");
            boqBomBoxPrintInfoService.setParams("中文",drBoxUp2,printInfoOutDTO,drTemp,newRow2);
            newRow2.setDevicesSiteCode("4220");
            drBoxUp2.setMfgSiteCode("123");
            boqBomBoxPrintInfoService.setParams("123",drBoxUp2,printInfoOutDTO,drTemp,newRow2);







            PrintInfoInDTO params2=new PrintInfoInDTO();
            params.setBillNo("12");
            params.setOrgId("q123");
            ArrayList<BoxUpBillInfoDTO> boxUpBillInfoDTOList3=new ArrayList<>();
            PowerMockito.when(boqBomBoxPrintInfoRepository.boxBarcodePrint(anyObject())).thenReturn(boxUpBillInfoDTOList3);
            boqBomBoxPrintInfoService.getBoxUpBillInfo(params2);
            BoxUpBillInfoDTO cc=new BoxUpBillInfoDTO();
            boxUpBillInfoDTOList3.add(cc);
            boqBomBoxPrintInfoService.getBoxUpBillInfo(params2);

            ItemDTO drTemp5=new ItemDTO();
            drTemp5.setLevel("42");
            BoxUpBillInfoDTO newRow5=new BoxUpBillInfoDTO();
            newRow5.setItemEnglishName("12");
            newRow5.setPtoBoqEnglishName("q3");
            newRow5.setPtoBoqEnglishName("");
            boqBomBoxPrintInfoService.setBomProItemBoq("英文",drTemp5,newRow5);
            drTemp5.setLevel("4");
            boqBomBoxPrintInfoService.setBomProItemBoq("英文",drTemp5,newRow5);
            drTemp5.setLevel("42");
            newRow5.setItemEnglishName("23");
            newRow5.setPtoBoqEnglishName("3e");
            boqBomBoxPrintInfoService.setBomProItemBoq("英文",drTemp5,newRow5);

            ItemDTO drTemp6=new ItemDTO();
            drTemp6.setLevel("4");
            BoxUpBillInfoDTO newRow6=new BoxUpBillInfoDTO();
            newRow6.setItemName("2q3");
            boqBomBoxPrintInfoService.setBomProItemBoqCn(drTemp6,newRow6);
            drTemp6.setLevel("42");
            newRow6.setPtoBoqName(null);
            boqBomBoxPrintInfoService.setBomProItemBoqCn(drTemp6,newRow6);
            newRow6.setPtoBoqName("3er4");
            boqBomBoxPrintInfoService.setBomProItemBoqCn(drTemp6,newRow6);

            newRow6.setEccBrand("");
            newRow6.setMemo("");
            boqBomBoxPrintInfoService.setItemMemoBoq(drTemp6,newRow6);
            newRow6.setEccBrand(",wer");
            newRow6.setMemo("we3r,");
            boqBomBoxPrintInfoService.setItemMemoBoq(drTemp6,newRow6);
            newRow6.setEccBrand(",wer");
            newRow6.setMemo("we3r");
            boqBomBoxPrintInfoService.setItemMemoBoq(drTemp6,newRow6);
            newRow6.setEccBrand("wer");
            newRow6.setMemo("we3r,");
            boqBomBoxPrintInfoService.setItemMemoBoq(drTemp6,newRow6);
            newRow6.setEccBrand("wer");
            newRow6.setMemo("we3r");
            boqBomBoxPrintInfoService.setItemMemoBoq(drTemp6,newRow6);

            Map<String, Object> map=new HashMap<>();
            boqBomBoxPrintInfoService.siteCityAddr(",,er,,rt,,",map);
            boqBomBoxPrintInfoService.siteCityAddr("er,,",map);

            newRow6.setImportItemType("B");
            newRow6.setSiteCityAddress("dstfds");
            boqBomBoxPrintInfoService.getBoqProductItemData(newRow6);
            newRow6.setImportItemType("Z");
            boqBomBoxPrintInfoService.getBoqProductItemData(newRow6);
            newRow6.setImportItemType("Y");
            boqBomBoxPrintInfoService.getBoqProductItemData(newRow6);
            newRow6.setImportItemType("30");
            boqBomBoxPrintInfoService.getBoqProductItemData(newRow6);
            newRow6.setImportItemType("Bsddgfg");
            boqBomBoxPrintInfoService.getBoqProductItemData(newRow6);


////////////////////////////////////////////eu
            List<BoxUpBillInfoDTO> dt=new ArrayList<>();
            BoxUpBillInfoDTO c=new BoxUpBillInfoDTO();
            BoxUpBillInfoDTO c2=new BoxUpBillInfoDTO();
            c.setItemId(23);
            c2.setItemId(23);
            c.setSuCode("we3r");
            c2.setSuCode("we3r");
            c.setcBomCode("er");
            c2.setcBomCode("er");
            c.setItemBarcode("wer");
            c2.setItemBarcode("wer");
            c.setZyLineNumber(1);
            c2.setZyLineNumber(1);
            dt.add(c);
            dt.add(c2);
            boqBomBoxPrintInfoService.mergeCodeItem(dt);
            //boqBomBoxPrintInfoService.validateParam(dt,0,1);


            List<BoxUpBillInfoDTO> mDsItem=new ArrayList<>();
            BoxUpBillInfoDTO bt=new BoxUpBillInfoDTO();
            bt.setModuleZyCode("12");
            bt.setZyLineNumber(12);
            mDsItem.add(bt);
            List<BoxUpBillInfoDTO> drCBomList =new ArrayList<>();
            boqBomBoxPrintInfoService.addParam(mDsItem,"12","12",drCBomList);


            bt.setMemo("531");
            bt.setMark("");
            drCBomList.add(bt);
            EuItemDTO drTemp3=new EuItemDTO();
            boqBomBoxPrintInfoService.setItemMemoBoqEu(drCBomList,"中文",drTemp3);

            bt.setIsMask("中文");
            drCBomList.add(bt);
            boqBomBoxPrintInfoService.setItemMemoBoqEu(drCBomList,"中文",drTemp3);

            bt.setIsMask("中文");
            drCBomList.add(bt);
            boqBomBoxPrintInfoService.setItemMemoBoqEu(drCBomList,"23",drTemp3);





            ArrayList<EuItemDTO> euItemDTOList=new ArrayList<>();
            //euItemDTOList.add(drTemp3);
            bt.setSiteCityAddress("1536");
            bt.setImportItemType("Y");
            bt.setBoxUpFlag("BOXUP_FLAG");
            bt.setItemCode("12");
            bt.setItemBarcode("42");
            drCBomList.add(bt);
            boqBomBoxPrintInfoService.euItemDTOListAdd2(drCBomList,"中文",euItemDTOList);
            boqBomBoxPrintInfoService.euItemDTOListAdd2(drCBomList,"中",euItemDTOList);


            BoxUpBillInfoDTO dr=new BoxUpBillInfoDTO();
            dr.setMemo("531");
            dr.setMark("");
            boqBomBoxPrintInfoService.setItemMemoBoqEu2(dr,"中文",drTemp3);

            dr.setIsMask("中文");
            boqBomBoxPrintInfoService.setItemMemoBoqEu2(dr,"中文",drTemp3);

            dr.setIsMask("中文");
            boqBomBoxPrintInfoService.setItemMemoBoqEu2(dr,"23",drTemp3);


            params.setOrgId("122");
            params.setBillNo("452");
            PowerMockito.when(boqBomBoxPrintInfoRepository.boxBarcodePrint(anyObject())).thenReturn(null);
            boqBomBoxPrintInfoService.boqBomBoxPrintInfo(params);


            ArrayList<BoxUpBillInfoDTO> boxUpBillInfoDTOList=new ArrayList<>();
            BoxUpBillInfoDTO bo=new BoxUpBillInfoDTO();
            bo.setTemplateLanguage("");
            boxUpBillInfoDTOList.add(bo);
            Map<String, Object> outMap2=new HashMap<>();
            boqBomBoxPrintInfoService.getBoxInfo( boxUpBillInfoDTOList,  outMap2);

            List<BoxUpBillInfoDTO> ds5=new ArrayList<>();
            boqBomBoxPrintInfoService.boqTableInfo(ds5, drBoxUp,  0, "123");


            PowerMockito.when(boqBomBoxPrintInfoRepository.getBoxBOQInfoForEU(anyMap())).thenReturn(null);
            boqBomBoxPrintInfoService.getBoxBOQInfoForEU( boxUpBillInfoDTOList,outMap2);



        }
        catch (Exception e
        )
        {
         e.printStackTrace();
         Assert.assertTrue(e.getMessage().length()>0);
        }
    }



}
