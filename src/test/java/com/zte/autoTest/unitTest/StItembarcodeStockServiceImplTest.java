package com.zte.autoTest.unitTest;


import com.zte.application.datawb.EdiSoSService;
import com.zte.application.stepdt.StItemAddressService;
import com.zte.application.stepdt.StSummaryService;
import com.zte.application.stepdt.impl.StItembarcodeStockServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ExcelCommonUtils;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.stepdt.StItembarcodeStockRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.EdiSoSNewDTO;
import com.zte.interfaces.dto.StSummaryEntityDTO;
import com.zte.interfaces.stepdt.dto.StItembarcodeStockEntityDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.math3.analysis.function.Pow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.rules.TestRule;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.management.openmbean.TabularType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CenterfactoryRemoteService.class, MESHttpHelper.class, HttpClientUtil.class,
        CommonUtils.class})
public class StItembarcodeStockServiceImplTest {
    @InjectMocks
    StItembarcodeStockServiceImpl stItembarcodeStockService;
    @Mock
    RedisTemplate<String, Object> redisTemplate;
    @Mock
    StSummaryService stSummaryService;
    @Mock
    private ValueOperations<String, Object> test;
    @Mock
    private StItembarcodeStockRepository stItembarcodeStockRepository;
    @Mock
    private EdiSoSService ediSoSService;
    @Mock
    private StItemAddressService stItemAddressService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private CloudDiskHelper cloudDiskHelper;


    @Before
    public void init() {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(CommonUtils.class);

    }

    @Test
    public void monitorBarcodeByExcelEmail() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        dto.setCreateBy("123");
        stItembarcodeStockService.monitorBarcodeByExcelEmail(dto);

        List<StItembarcodeStockEntityDTO> barCodeNagList = new ArrayList<>();
        StItembarcodeStockEntityDTO barcodeNagDto = new StItembarcodeStockEntityDTO();
        barcodeNagDto.setItemBarcode("123");
        barcodeNagDto.setBalanceQty(new BigDecimal("1"));
        barcodeNagDto.setBeginQty(new BigDecimal("1"));
        barcodeNagDto.setIncomeQty(new BigDecimal("1"));
        barcodeNagDto.setOutcomeQty(new BigDecimal("1"));
        barCodeNagList.add(barcodeNagDto);
        PowerMockito.when(stItembarcodeStockRepository.getItemBarcodeNag()).thenReturn(barCodeNagList);

        stItembarcodeStockService.monitorBarcodeByExcelEmail(dto);
        String fileKey = "key";
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(fileKey);
        String fileUrl = "url";
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(fileUrl);
        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupMeaning("123");
        sysDto.setLookupCode(new BigDecimal("2032011"));
        sysList.add(sysDto);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any()))
                .thenReturn(sysList);
        stItembarcodeStockService.monitorBarcodeByExcelEmail(dto);
        Assert.assertNull(null);

    }

    @Test
    public void monitorBarcodeByEmail() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        stItembarcodeStockService.monitorBarcodeByEmail(dto);

        List<StItembarcodeStockEntityDTO> barCodeNagList = new ArrayList<>();
        StItembarcodeStockEntityDTO barcodeNagDto = new StItembarcodeStockEntityDTO();
        barcodeNagDto.setItemBarcode("123");
        barcodeNagDto.setBalanceQty(new BigDecimal("1"));
        barcodeNagDto.setBeginQty(new BigDecimal("1"));
        barcodeNagDto.setIncomeQty(new BigDecimal("1"));
        barcodeNagDto.setOutcomeQty(new BigDecimal("1"));
        barCodeNagList.add(barcodeNagDto);
        PowerMockito.when(stItembarcodeStockRepository.getItemBarcodeNag())
                .thenReturn(barCodeNagList);
        stItembarcodeStockService.monitorBarcodeByEmail(dto);

        dto.setScheduledTaskFlag(Constant.FLAG_Y);
        List<SysLookupValues> sysList = new ArrayList<>();
        stItembarcodeStockService.monitorBarcodeByEmail(dto);
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupMeaning("123");
        sysDto.setLookupCode(new BigDecimal("2032011"));
        sysList.add(sysDto);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any()))
                .thenReturn(sysList);

        Assert.assertNotNull(stItembarcodeStockService.monitorBarcodeByEmail(dto));

    }

    @Test
    public void monitorApsByEmail() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        List<StItembarcodeStockEntityDTO> errorList = new ArrayList<>();
        PowerMockito.when(stItembarcodeStockRepository.getApsEorroLog())
                .thenReturn(errorList);
        stItembarcodeStockService.monitorApsByEmail(dto);
        StItembarcodeStockEntityDTO errorDto = new StItembarcodeStockEntityDTO();
        errorDto.setLogDate(new Date());
        errorDto.setTaskNo("123");
        errorList.add(errorDto);
        List<SysLookupValues> sysList = new ArrayList<>();
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any()))
                .thenReturn(sysList);
        stItembarcodeStockService.monitorApsByEmail(dto);
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupCode(new BigDecimal(Constant.LOOKUP_VALUE_2032009));
        sysList.add(sysDto);
        stItembarcodeStockService.monitorApsByEmail(dto);
        Assert.assertEquals(Constant.FLAG_Y,"Y");
    }

    @Test
    public void getItemBarcodeQtyAddr() throws Exception {
        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupMeaning("1223");
        sysDto.setLookupCode(new BigDecimal("2032006"));
        sysList.add(sysDto);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_2032))
                .thenReturn(sysList);
        Assert.assertNotNull(stItembarcodeStockService.getItemBarcodeQtyAddr());
    }

    @Test
    public void getZtCompareQtyPage() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        dto.setPage(1);
        dto.setRows(1);
        Assert.assertNotNull(stItembarcodeStockService.getZtCompareQtyPage(dto));
    }

    @Test
    public void getBarcodeZtQty() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        dto.setStockNo("123");
        dto.setItemBarcode("123");
        dto.setWarehouseId("123");

        StItembarcodeStockEntityDTO itemBarcodeDto = new StItembarcodeStockEntityDTO();
        itemBarcodeDto.setStockNo("123");
        itemBarcodeDto.setItemBarcode("123");
        PowerMockito.when(stItembarcodeStockRepository.getItemBarcodeQty(Mockito.any()))
                .thenReturn(itemBarcodeDto);

        EdiSoSNewDTO inforOutQtyDto = new EdiSoSNewDTO();
        inforOutQtyDto.setHref08(dto.getStockNo());
        inforOutQtyDto.setLottable02(dto.getItemBarcode());
        inforOutQtyDto.setQty(new BigDecimal(1));
        PowerMockito.when(ediSoSService.getBarcodeQty(Mockito.any()))
                .thenReturn(inforOutQtyDto);

        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysDto2 = new SysLookupValues();
        sysDto2.setLookupCode(new BigDecimal("2032006"));
        sysDto2.setLookupMeaning("1");
        sysList.add(sysDto2);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_2032))
                .thenReturn(sysList);


        String result = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "    {\n" +
                "      \"itemBarcode\": \"220015044049\",\n" +
                "      \"qtyChange\": 18246\n" +
                "  \n" +
                "    }\n" +
                "  ],\n" +
                "  \"other\": null\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        Assert.assertNotNull(stItembarcodeStockService.getBarcodeZtQty(dto));
    }

    @Test
    public void deleteBkcLog() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        stItembarcodeStockService.deleteBkcLog(dto);
        dto.setDeleteMonthParam(-1);
        try {
            stItembarcodeStockService.deleteBkcLog(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.DELETE_PARAM_ERROR,e.getMessage());
        }
        dto.setDeleteMonthParam(1);
        stItembarcodeStockService.deleteBkcLog(dto);

        Assert.assertEquals(Constant.FLAG_Y,"Y");
    }

    @Test
    public void getItemTransfer() throws Exception {
        PowerMockito.when(stItembarcodeStockRepository.getItemTransfer(Mockito.anyString())).thenReturn("TEST");
        Assert.assertNotNull(stItembarcodeStockService.getItemTransfer("TEST"));
    }

    @Test
    public void updateBarcodeZtQty() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        dto.setStockNo("123");
        dto.setWarehouseId("123");

        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupCode(new BigDecimal("2032005"));
        sysDto.setLookupMeaning("1");
        sysList.add(sysDto);
        SysLookupValues sysDto2 = new SysLookupValues();
        sysDto2.setLookupCode(new BigDecimal("2032006"));
        sysDto2.setLookupMeaning("1");
        sysList.add(sysDto2);
        SysLookupValues sysDto3 = new SysLookupValues();
        sysDto3.setLookupCode(new BigDecimal("2032007"));
        sysDto3.setLookupMeaning("Y");
        sysList.add(sysDto3);

        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_TYPE_2032))
                .thenReturn(sysList);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(test);
        PowerMockito.when(test.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.any()))
                .thenReturn(true);

        List<StItembarcodeStockEntityDTO> barcodeList = new ArrayList<>();
        StItembarcodeStockEntityDTO barcodeDto = new StItembarcodeStockEntityDTO();
        barcodeDto.setItemBarcode("220015044049");
        barcodeList.add(barcodeDto);
        PowerMockito.when(stItembarcodeStockRepository.getItembarcodeByRownum(Mockito.any()))
                .thenReturn(barcodeList);

        EdiSoSNewDTO inforQtyDto = new EdiSoSNewDTO();
        inforQtyDto.setQty(new BigDecimal("3"));
        PowerMockito.when(ediSoSService.getBarcodeQtyWithBack(Mockito.any()))
                .thenReturn(inforQtyDto);

        EdiSoSNewDTO inforIncodeQtyDto = new EdiSoSNewDTO();
        inforIncodeQtyDto.setQty(new BigDecimal("1"));
        PowerMockito.when(ediSoSService.getBarcodeIocodeQty(Mockito.any()))
                .thenReturn(inforQtyDto);

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);

        String result = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "    {\n" +
                "      \"itemBarcode\": \"220015044049\",\n" +
                "      \"qtyChange\": 18246\n" +
                "  \n" +
                "    }\n" +
                "  ],\n" +
                "  \"other\": null\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        stItembarcodeStockService.updateBarcodeZtQty(dto);
        String result2 = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\":null,\n" +
                "  \"other\": null\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result2);
        Assert.assertNotNull(stItembarcodeStockService.updateBarcodeZtQty(dto));
    }

    @Test
    public void updateItemAndBarcodeQty() throws Exception {
        StItembarcodeStockEntityDTO dto = new StItembarcodeStockEntityDTO();
        List<StItembarcodeStockEntityDTO> taskList = new ArrayList<>();
        StItembarcodeStockEntityDTO taskDto = new StItembarcodeStockEntityDTO();
        taskDto.setWarehouseIdListKey("123");
        taskDto.setId("123");
        taskList.add(taskDto);
        dto.setTaskList(taskList);


        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(test);
        PowerMockito.when(test.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito.any()))
                .thenReturn(true);
//        PowerMockito.when(test.setIfAbsent(Mockito.any(),Mockito.any(),Mockito.anyInt(), Mockito.any()))
//                .thenReturn(true);

        List<StItembarcodeStockEntityDTO> barcodeList = new ArrayList<>();
        StItembarcodeStockEntityDTO barCodeDto = new StItembarcodeStockEntityDTO();
        barCodeDto.setQty(new BigDecimal(1));
        barCodeDto.setStockNo("123");
        barcodeList.add(barCodeDto);
        List<StSummaryEntityDTO> itemList = new ArrayList<>();
        StSummaryEntityDTO itemDto = new StSummaryEntityDTO();
        itemDto.setQty(new BigDecimal("1"));
        itemDto.setStockNo("123");
        itemList.add(itemDto);
        dto.setBarcodeList(barcodeList);
        dto.setItemList(itemList);

        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupCode(new BigDecimal(1004037026));
        sysDto.setLookupMeaning("123");
        sysList.add(sysDto);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(sysList);

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        try {
            stItembarcodeStockService.updateItemAndBarcodeQty(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues sysDto1 = new SysLookupValues();
        sysDto1.setLookupCode(new BigDecimal(2032010));
        sysDto1.setLookupMeaning("123");
        sysList.add(sysDto1);
        stItembarcodeStockService.updateItemAndBarcodeQty(dto);

    }

    @Test
    public void updateStockFlowStatus() throws Exception {
        List<StItembarcodeStockEntityDTO> taskList = new ArrayList<>();
        StItembarcodeStockEntityDTO taskDto = new StItembarcodeStockEntityDTO();
        taskDto.setId("123");
        taskList.add(taskDto);

        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysDto = new SysLookupValues();
        sysDto.setLookupCode(new BigDecimal(1004037026));
        sysDto.setLookupMeaning("123");
        sysList.add(sysDto);
        PowerMockito.when(CenterfactoryRemoteService.getLookupValue(Mockito.any())).thenReturn(sysList);

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":null,\"other\":null}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);

        stItembarcodeStockService.updateStockFlowStatus(taskList);
        Assert.assertNull(null);

    }
}
