package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.IMESLogService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2021-11-16 16:52
 */
@RunWith(PowerMockRunner.class)
public class BarcodeCenterRemoteServiceTest {
    @InjectMocks
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private IMESLogService imesLogService;



    @Test
    @PrepareForTest({CenterfactoryRemoteService.class,MESHttpHelper.class,HttpRemoteUtil.class})
    public void expandQuery()throws Exception {
        PowerMockito.mockStatic(CenterfactoryRemoteService.class,HttpRemoteUtil.class);
        barcodeCenterRemoteService.expandQuery(null);
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        List<String> barcodeList = new ArrayList<>();
        barcodeList.add("2");
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);

        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues dto1 = new SysLookupValues();
        dto1.setLookupCode(new BigDecimal("6732001"));
        sysLookupValuesList.add(dto1);
        SysLookupValues dto2 = new SysLookupValues();
        dto2.setLookupCode(new BigDecimal(Constant.BARCODE_TENANT_ID));
        dto2.setLookupMeaning("6732001");
        sysLookupValuesList.add(dto2);

        SysLookupValues dto3 = new SysLookupValues();
        dto3.setLookupCode(new BigDecimal(Constant.BARCODE_LOOKUPCODE));
        dto3.setLookupMeaning("6732001");
        sysLookupValuesList.add(dto3);

        SysLookupValues dto4 = new SysLookupValues();
        dto4.setLookupCode(new BigDecimal(Constant.BARCODE_APPID));
        dto4.setLookupMeaning("6732001");
        sysLookupValuesList.add(dto4);

        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_6679, Constant.LOOKUP_6679005)).thenReturn(null);
        try {
            barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_6679, Constant.LOOKUP_6679005)).thenReturn(dto1);
        try {
            barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_GET_BARCODE_CENTER_URL, e.getMessage());
        }
        when(CenterfactoryRemoteService.getLookupValue(Constant.LOOKUP_6679, Constant.LOOKUP_6679005)).thenReturn(dto1);

        dto1.setLookupMeaning("1");
        try {
            barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        when(CenterfactoryRemoteService.getLookupValue(Constant.MP_CODE_KEY_1004052)).thenReturn(sysLookupValuesList);
        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(null);
        try {
            barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        try {
            barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);
        }catch (Exception e){
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }

        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        barcodeExpandDTOList.add(new BarcodeExpandDTO());
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(
                        new ServiceData() {{
                            setBo(
                                    barcodeExpandDTOList
                            );
                            setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
                        }}));
        barcodeCenterRemoteService.expandQuery(barcodeExpandQueryDTO);

    }

}
