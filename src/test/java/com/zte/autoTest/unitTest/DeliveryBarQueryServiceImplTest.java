package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.DeliveryBarQueryImpl;
import  com.zte.application.datawb.impl.DeliveryBarQueryEUImpl;
import com.zte.domain.model.datawb.DeliveryBarQueryEURepository;
import com.zte.domain.model.datawb.DeliveryBarQueryRepository;
import  com.zte.interfaces.dto.DeliveryBarQueryEUParamterDTO;
import com.zte.interfaces.dto.DeliveryBarQueryEUResultDTO;
import com.zte.interfaces.dto.DeliveryBarQueryParmaterDTO;
import com.zte.interfaces.dto.DeliveryBarQueryResultDTO;
import  com.zte.application.datawb.DeliveryBarQueryEUService;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.List;

public class DeliveryBarQueryServiceImplTest extends PowerBaseTestCase {

    /**
     * mock方法写法 10292636 彭国
     */
    @InjectMocks
    private DeliveryBarQueryImpl deliveryBarQueryImpl;
    @InjectMocks
    private DeliveryBarQueryEUImpl deliveryBarQueryEUImpl;
    @Mock
    private DeliveryBarQueryEURepository deliveryBarQueryEURepository;
    @Mock
    private DeliveryBarQueryRepository deliveryBarQueryRepository;


    @Test
    public void isListEmpty() throws Exception {
      //覆盖1
        DeliveryBarQueryParmaterDTO   deliveryBarQueryParmaterDTO =new DeliveryBarQueryParmaterDTO ();
        Assert.assertFalse(deliveryBarQueryImpl.isListEmpty(deliveryBarQueryParmaterDTO));
    }
    @Test
    public void setPage() throws Exception {
        //覆盖2
        DeliveryBarQueryParmaterDTO inputQueryDto =new  DeliveryBarQueryParmaterDTO();
        inputQueryDto.setCurrentPage((long)10);
        inputQueryDto.setPageSize((long)1);
        deliveryBarQueryImpl.setPage(inputQueryDto);
        Assert.assertNotNull(inputQueryDto);

    }
    @Test
    public void getDeliveryBarQuery() throws Exception {
        //覆盖3
        DeliveryBarQueryParmaterDTO inputQueryDto =new DeliveryBarQueryParmaterDTO();
        inputQueryDto.setCurrentPage((long)1);
        inputQueryDto.setPageSize((long)10);
        List<String> barcode=new ArrayList<>();
        barcode.add("220015279984");
        inputQueryDto.setLitemBarcode(barcode);
        inputQueryDto.setOrganizationId((long)635);
        deliveryBarQueryRepository.getDeliveryBarQuery(inputQueryDto);
        Assert.assertNotNull(deliveryBarQueryImpl.getDeliveryBarQuery(inputQueryDto));
    }
    @Test
    public void  setEuParamterDto() throws Exception{
        //覆盖4
        DeliveryBarQueryResultDTO deliveryBarQueryResultDTO  =new DeliveryBarQueryResultDTO();
        List<DeliveryBarQueryResultDTO> inputParams =new ArrayList<>();
        deliveryBarQueryResultDTO.setBillid((long)10);
        deliveryBarQueryResultDTO.setBillNumber("E12333");
        inputParams.add(deliveryBarQueryResultDTO);
        Assert.assertNotNull(deliveryBarQueryImpl.setEuParamterDto(inputParams));
    }
    @Test
   public void  setSubComEu ()throws Exception {
        DeliveryBarQueryResultDTO t1 =new DeliveryBarQueryResultDTO();
        DeliveryBarQueryEUResultDTO t2=new DeliveryBarQueryEUResultDTO();
        t1.setBillid((long)1234);
        t2.setBillid((long)1234);
        t1.setOrganizationId("635");
        t2.setOrgnizationid("635");
        t2.setSuCode("pengguo");
        t2.setSuName("222");
        t1.setItemBarcode("");
        t2.setRelationidentifier((long)3);
        t2.setCbomCode("1233");
        t2.setItemName("1233");
        deliveryBarQueryImpl.setSubComEu(t1,t2);
        t2.setRelationidentifier((long)4);
        deliveryBarQueryImpl.setSubComEu(t1,t2);
        Assert.assertNotNull(t1);
        Assert.assertNotNull(t2);
    }
    @Test
  public void  setoutPamaterEu () throws Exception {
        List<DeliveryBarQueryResultDTO> deliveryBarQueryResultDTOList =new ArrayList<>();
        DeliveryBarQueryResultDTO t1 =new DeliveryBarQueryResultDTO();
        List<DeliveryBarQueryEUResultDTO> outputEuPamater=new ArrayList<>();
        DeliveryBarQueryEUResultDTO t2 =new DeliveryBarQueryEUResultDTO ();
        t1.setBillid((long)1234);
        t2.setBillid((long)1234);
        t1.setOrganizationId("635");
        t2.setOrgnizationid("635");
        t2.setSuCode("pengguo");
        t2.setSuName("222");
        t1.setItemBarcode("");
        t2.setRelationidentifier((long)3);
        t2.setCbomCode("1233");
        t2.setItemName("1233");
        deliveryBarQueryImpl.setSubComEu(t1,t2);
        t2.setRelationidentifier((long)4);
        deliveryBarQueryResultDTOList.add(t1);
        outputEuPamater.add(t2);
        deliveryBarQueryImpl.setoutPamaterEu(deliveryBarQueryResultDTOList,outputEuPamater);
        Assert.assertNotNull(deliveryBarQueryResultDTOList);
    }
    @Test
   public  void getDeliveryBarQueryEU()throws Exception {
        DeliveryBarQueryEUParamterDTO inputQueryDto =new DeliveryBarQueryEUParamterDTO();
        deliveryBarQueryEURepository.getDeliveryBarQueryEU(inputQueryDto);
        Assert.assertNotNull(deliveryBarQueryEUImpl.getDeliveryBarQueryEU(inputQueryDto));
    }
}
