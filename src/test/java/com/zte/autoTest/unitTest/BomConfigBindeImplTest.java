package com.zte.autoTest.unitTest;

import com.zte.application.datawb.BomConfigCheckEnService;
import com.zte.application.datawb.ConfigMaterialBaindDqasNewService;
import com.zte.application.datawb.impl.BomConfigBindInfoImpl;
import com.zte.application.datawb.impl.BomConfigCheckEnImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BomConfigBindInfoRepository;
import com.zte.domain.model.datawb.BomConfigCheckEnRepository;
import com.zte.domain.model.datawb.DeliveryBarQueryRepository;
import com.zte.interfaces.dto.BomBindCombineDTO;
import com.zte.interfaces.dto.BomConfigBindCollectionDTO;
import com.zte.interfaces.dto.BomConfigBindInfoParamterDTO;
import com.zte.interfaces.dto.BomConfigBindInfoResultDTO;
import com.zte.interfaces.dto.BomConfigBindInfoResultvDTO;
import com.zte.interfaces.dto.BomConfigCheckEnDTO;
import com.zte.interfaces.dto.CheckInDTO;
import com.zte.interfaces.dto.CheckOutDTO;
import com.zte.interfaces.dto.WebStructPamater;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.context.MessageSource;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;

//@Component("localeResolverMes")
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({SpringContextUtil.class})
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//public class BomConfigBindeImplTest extends PowerBaseTestCase {
@RunWith(PowerMockRunner.class)
@PrepareForTest( { SpringContextUtil.class})
public class BomConfigBindeImplTest extends BaseTestCase {

    /**
     * mock方法写法 10292636 彭国
     */
    @InjectMocks
    private BomConfigBindInfoImpl bomConfigBindInfoImpl;
    @InjectMocks
    private BomConfigCheckEnImpl bomConfigCheckEnImpl;
    @Mock
    private BomConfigBindInfoRepository bomConfigBindInfoRepository;
    @Mock
    private BomConfigCheckEnRepository bomConfigCheckEnRepository;
    @Mock
    private DeliveryBarQueryRepository deliveryBarQueryRepository;

    @Mock
    private BomConfigCheckEnService bomConfigCheckEnService;
    @Mock
    ConfigMaterialBaindDqasNewService   configMaterialBaindDqasNewService;
    @Mock
    private Map<String, Object> map;
    @Mock
    private ServiceData serviceData;
    @Resource
    private MessageSource messageSource;


    @Test
    public void isListEmpty() throws Exception {
        //覆盖1
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        bomConfigBindInfoParamterDTO.setProcedule("装配");
        Assert.assertFalse(bomConfigBindInfoImpl.isListEmpty(bomConfigBindInfoParamterDTO));

    }

    @Test
    public void jobNumber() throws Exception {

        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        bomConfigBindInfoParamterDTO.setProcedule("装配");
        Long tt = new Long(152673);
        PowerMockito.when(bomConfigBindInfoRepository.selectJobNumber(bomConfigBindInfoParamterDTO.getCreateBy())).thenReturn(tt);
        bomConfigBindInfoImpl.jobNumber(bomConfigBindInfoParamterDTO);
        Long tt1 = new Long(0);
        PowerMockito.when(bomConfigBindInfoRepository.selectJobNumber(bomConfigBindInfoParamterDTO.getCreateBy())).thenReturn(tt1);


        try {
            LocaleMessageSourceBean lmb1 = new LocaleMessageSourceBean();
            PowerMockito.when(lmb1.getMessage("1233")).thenReturn("error");
            bomConfigBindInfoImpl.jobNumber(bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void valiteInputPamater() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        bomConfigBindInfoParamterDTO.setProcedule("装配");
        Long tt = new Long(152673);
        PowerMockito.when(bomConfigBindInfoRepository.selectJobNumber(bomConfigBindInfoParamterDTO.getCreateBy())).thenReturn(tt);
        Assert.assertNull(bomConfigBindInfoImpl.valiteInputPamater(bomConfigBindInfoParamterDTO));
    }

    @Test
    public void selectEntityName() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        bomConfigBindInfoParamterDTO.setProcedule("装配");
        BomConfigBindInfoResultDTO bomConfigBindInfoResultDTO = new BomConfigBindInfoResultDTO();
        bomConfigBindInfoResultDTO.setOrgnizationId((long) 635);
        bomConfigBindInfoResultDTO.setContractNumber("12333");
        bomConfigBindInfoResultDTO.setEntiyId((long) 12333);
        bomConfigBindInfoResultDTO.setEntityName("123333");
        bomConfigBindInfoResultDTO.setMfgSiteId((long) 1233);
        List<BomConfigBindInfoResultDTO> bomList = new ArrayList<>();
        bomList.add(bomConfigBindInfoResultDTO);
        PowerMockito.when(bomConfigBindInfoRepository.getEntityInformation(bomConfigBindInfoParamterDTO)).thenReturn(bomList);
        Long tt = new Long(152673);
        PowerMockito.when(bomConfigBindInfoRepository.selectJobNumber(bomConfigBindInfoParamterDTO.getCreateBy())).thenReturn(tt);

        PowerMockito.when(bomConfigBindInfoRepository.getMifgSiteIdList(bomConfigBindInfoParamterDTO)).thenReturn(tt);
        bomConfigBindInfoImpl.selectEntityName(bomConfigBindInfoParamterDTO);

        try {
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO1 = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoImpl.selectEntityName(bomConfigBindInfoParamterDTO1);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void getValidateSiteDate() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        Long tt = new Long(1);
        bomConfigBindInfoParamterDTO.setProcedule("装配");
        PowerMockito.when(bomConfigBindInfoRepository.getMifgSiteIdList(bomConfigBindInfoParamterDTO)).thenReturn(tt);
        Assert.assertNull(bomConfigBindInfoImpl.getValidateSiteDate(bomConfigBindInfoParamterDTO));

    }

    @Test
    public void selectCommonEntityDay() {

        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        // this.selectEntityName();
        BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
        bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
        bomConfigBindInfoResultvDTO.setAttribute10("12333");
        bomConfigBindInfoResultvDTO.setBoxQty("2");
        bomConfigBindInfoResultvDTO.setcNUnit("1233");
        bomConfigBindInfoResultvDTO.setCode("1233");
        List<BomConfigBindInfoResultvDTO> bomList = new ArrayList<>();
        bomList.add(bomConfigBindInfoResultvDTO);
        PowerMockito.when(bomConfigBindInfoRepository.getConfigPageLone(bomConfigBindInfoParamterDTO)).thenReturn(bomList);

        Long tt = new Long(152673);
        PowerMockito.when(bomConfigBindInfoRepository.selectJobNumber(bomConfigBindInfoParamterDTO.getCreateBy())).thenReturn(tt);

        BomConfigBindInfoResultDTO bomConfigBindInfoResultDTO = new BomConfigBindInfoResultDTO();
        bomConfigBindInfoResultDTO.setOrgnizationId((long) 635);
        bomConfigBindInfoResultDTO.setContractNumber("12333");
        bomConfigBindInfoResultDTO.setEntiyId((long) 12333);
        bomConfigBindInfoResultDTO.setEntityName("123333");
        bomConfigBindInfoResultDTO.setMfgSiteId((long) 1233);
        List<BomConfigBindInfoResultDTO> bomLis1 = new ArrayList<>();
        bomLis1.add(bomConfigBindInfoResultDTO);
        PowerMockito.when(bomConfigBindInfoRepository.getEntityInformation(bomConfigBindInfoParamterDTO)).thenReturn(bomLis1);

        PowerMockito.when(bomConfigBindInfoRepository.getMifgSiteIdList(bomConfigBindInfoParamterDTO)).thenReturn(tt);
        Assert.assertNull(bomConfigBindInfoImpl.selectCommonEntityDay(bomConfigBindInfoParamterDTO));
    }

    @Test
    public void getCollectionL2() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);

        BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
        bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
        bomConfigBindInfoResultvDTO.setAttribute10("12333");
        bomConfigBindInfoResultvDTO.setBoxQty("2");
        bomConfigBindInfoResultvDTO.setcNUnit("1233");
        bomConfigBindInfoResultvDTO.setCode("1233");
        List<BomConfigBindInfoResultvDTO> bomList = new ArrayList<>();
        bomList.add(bomConfigBindInfoResultvDTO);
        PowerMockito.when(bomConfigBindInfoRepository.getConfigPageLone(bomConfigBindInfoParamterDTO)).thenReturn(bomList);
        Assert.assertNull(bomConfigBindInfoImpl.getCollectionL2(bomConfigBindInfoParamterDTO));
    }

    @Test
    public void valiateEnviroment() {
        try {
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            Map<String, Object> map = new HashMap<>();
            doNothing().when(bomConfigCheckEnService).callCheckEnviroment(map);
            String dxx = "Y";

            PowerMockito.when(map.get("1233")).thenReturn(dxx);
            doNothing().when(serviceData).setCode(null);
            // PowerMockito.when(serviceData.setCode(new RetCode(RetCode.SERVERERROR_CODE, MessageId.ENTITY_NO_MATCH_EN))
            // PowerMockito.when(bomConfigCheckEnService.callCheckEnviroment(map));
            bomConfigBindInfoImpl.valiateEnviroment(bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void validateBarcode() {
        try {
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoImpl.validateBarcode(bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertNull(MessageId.NO_BARCODE_INFORMATION, e.getMessage());
        }
    }

    @Test
    public void selectConfigInfo() {
        try {
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);


            //环保属性
            Map<String, Object> map = new HashMap<>();
            doNothing().when(bomConfigCheckEnService).callCheckEnviroment(map);
            String dxx = "Y";

            PowerMockito.when(map.get("1233")).thenReturn(dxx);
            doNothing().when(serviceData).setCode(null);
            // PowerMockito.when(serviceData.setCode(new RetCode(RetCode.SERVERERROR_CODE, MessageId.ENTITY_NO_MATCH_EN))
            // PowerMockito.when(bomConfigCheckEnService.callCheckEnviroment(map));
            Long tt = new Long(152673);
            PowerMockito.when(bomConfigBindInfoRepository.selectJobNumber(bomConfigBindInfoParamterDTO.getCreateBy())).thenReturn(tt);

            bomConfigBindInfoImpl.selectConfigInfo(bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void comomMethod() {
        try {
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoImpl.comomMethod(bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void explainXml() {
        String fty = "<?xml version=\"1.0\" encoding=\"utf-8\"?><soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><soapenv:Body><ns1:getSparePartInfoListResponse soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\" xmlns:ns1=\"http://asm.zte.com.cn/\"><getSparePartInfoListReturn xsi:type=\"soapenc:string\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\"><?xml version=\"1.0\" encoding=\"utf-8\"?><sparepartinfolist></sparepartinfolist></getSparePartInfoListReturn></ns1:getSparePartInfoListResponse></soapenv:Body></soapenv:Envelope>";
        String fty1 = "<?xml version=\"1.0\" encoding=\"utf-8\"?><soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><soapenv:Body><ns1:getSparePartInfoListResponse soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\" xmlns:ns1=\"http://asm.zte.com.cn/\"><getSparePartInfoListReturn xsi:type=\"soapenc:string\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\"><?xml version=\"1.0\" encoding=\"utf-8\"?><sparepartinfolist><ITEM_CODE>1233</ITEM_CODE><ITEM_NAME>ssb</ITEM_NAME></sparepartinfolist></getSparePartInfoListReturn></ns1:getSparePartInfoListResponse></soapenv:Body></soapenv:Envelope>";
        String fty2 = "<?xml version=\"1.0\" encoding=\"utf-8\"?><soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><soapenv:Body><ns1:getSparePartInfoListResponse soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\" xmlns:ns1=\"http://asm.zte.com.cn/\"><getSparePartInfoListReturn xsi:type=\"soapenc:string\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\"><?xml version=\"1.0\" encoding=\"utf-8\"?><sparepartinfolist><ITEM_CODE>1233</ITEM_CODE><ITEM_NAME>ssb</ITEM_NAME><ITEM_ID>1233</ITEM_ID></sparepartinfolist><sparepartinfolist><ITEM_CODE>4445</ITEM_CODE><ITEM_NAME>ss6</ITEM_NAME><ITEM_ID>1237</ITEM_ID></sparepartinfolist></getSparePartInfoListReturn></ns1:getSparePartInfoListResponse></soapenv:Body></soapenv:Envelope>";
        String ctu = "<sparepartinfolist>";
        String ctv = "</sparepartinfolist>";
        List<WebStructPamater> webList = new ArrayList<>();
        WebStructPamater t1 = new WebStructPamater();
        t1.setStartv("<ITEM_CODE>");
        t1.setStartEnd("</ITEM_CODE>");
        webList.add(t1);
        WebStructPamater t2 = new WebStructPamater();
        t2.setStartv("<ITEM_NAME>");
        t2.setStartEnd("</ITEM_NAME>");
        webList.add(t2);
        WebStructPamater t3 = new WebStructPamater();
        t3.setStartv("<ITEM_ID>");
        t3.setStartEnd("</ITEM_ID>");
        webList.add(t3);
        bomConfigBindInfoImpl.explainXml(fty, webList, ctu, ctv);
        bomConfigBindInfoImpl.explainXml(fty1, webList, ctv, ctv);
        Assert.assertNotNull(bomConfigBindInfoImpl.explainXml(fty2, webList, ctv, ctv));
    }

    @Test
    public void selectWebserice() {
        String ssb1 = "<getSparePartInfoList  xmlns=\"http://asm.zte.com.cn/\">";
        String ssb2 = "</getSparePartInfoList>";
        String ssb3 = "http://asm.zte.com.cn/service/services/SparePartInfoWebservice?wsdl";
        String dvio = "<in0>77788888</in0>";
        List<String> ddt = new ArrayList<>();
        ddt.add(dvio);
        try {
            bomConfigBindInfoImpl.selectWebserice(ssb1, ssb2, ssb3, ddt);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void postWebservice() {
        String ssb1 = "<getSparePartInfoList  xmlns=\"http://asm.zte.com.cn/\">";
        String ssb2 = "</getSparePartInfoList>";
        String ssb3 = "http://asm.zte.com.cn/service/services/SparePartInfoWebservice?wsdl";
        String dvio = "<in0>77788888</in0>";
        List<String> ddt = new ArrayList<>();
        ddt.add(dvio);
        Assert.assertNull(bomConfigBindInfoImpl.postWebservice(ssb1, ssb2, ssb3, ddt));
    }

    @Test
    public void getDtDataWeb() {
        String barcode = "12333";
        Assert.assertNotNull(bomConfigBindInfoImpl.getDtDataWeb(barcode));
    }

    @Test
    public void setResultTv() {
        Map<Integer, List<String>> linkedHashMap = new LinkedHashMap<>();
        List<String> xxt0 = new ArrayList<>();
        String t10 = "1233";
        String t20 = "ssb";
        String t30 = "1233";
        xxt0.add(t10);
        xxt0.add(t20);
        xxt0.add(t30);
        linkedHashMap.put(0, xxt0);
        List<String> xxt1 = new ArrayList<>();
        String t11 = "4445";
        String t21 = "ss6";
        String t31 = "1237";
        xxt1.add(t11);
        xxt1.add(t21);
        xxt1.add(t31);
        linkedHashMap.put(1, xxt1);
        Assert.assertNotNull(bomConfigBindInfoImpl.setResultTv(linkedHashMap));


    }

    @Test
    public void getReturnBarcode() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("10144021");
        bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
        bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
        bomConfigBindInfoParamterDTO.setNoLinkTask(false);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        Assert.assertNull(bomConfigBindInfoImpl.getReturnBarcode(bomConfigBindInfoParamterDTO));
    }

    @Test
    public void setItemMaxSSB() {
        List<BomConfigCheckEnDTO> resultTvt = new ArrayList<>();
        BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();
        bomConfigCheckEnDTO.setItemId((long) 1);
        bomConfigCheckEnDTO.setItemName("1233");
        bomConfigCheckEnDTO.setItemCode("12333");
        resultTvt.add(bomConfigCheckEnDTO);
        BomConfigCheckEnDTO bomConfigCheckEnDTODT = new BomConfigCheckEnDTO();
        bomConfigCheckEnDTODT.setItemId((long) 2);
        bomConfigCheckEnDTODT.setItemName("12334");
        bomConfigCheckEnDTODT.setItemCode("12335");
        resultTvt.add(bomConfigCheckEnDTODT);
        bomConfigBindInfoImpl.resultTv = resultTvt;
        Assert.assertNotNull(bomConfigBindInfoImpl.setItemMaxSSB());

    }

    @Test
    public void setBomConfigBindCollectionT() {
        List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
        List<BomConfigBindCollectionDTO> bomConfigBindCollection = new ArrayList<>();
        bomConfigBindInfoImpl.setBomConfigBindCollectionT(bomConfigBindCollectionT, bomConfigBindCollection);
        Assert.assertNotNull(bomConfigBindCollectionT);
    }

    @Test
    public void getEntiyReliance() {
        try {
            BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();

            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();

            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setEntityId((long) 2);
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 1233);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionT.add(bomConfigBindCollectionDTO);

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionTT = new ArrayList<>();
            PowerMockito.when(bomConfigBindInfoRepository.getBindCollection(bomConfigCheckEnDTO)).thenReturn(bomConfigBindCollectionT);
            bomConfigBindInfoImpl.getEntiyReliance(bomConfigCheckEnDTO, bomConfigBindInfoParamterDTO,
                    bomConfigBindCollectionTT);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void getEntiyReliance1() {
        try {
            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 1);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 123);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");

            bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
            opvvv.add(bomConfigBindInfoResultvDTO);
            bomConfigBindInfoImpl.bomResultListInterface = opvvv;
            BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();

            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();

            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setEntityId((long) 1);
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 1233);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionT.add(bomConfigBindCollectionDTO);

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionTT = new ArrayList<>();
            PowerMockito.when(bomConfigBindInfoRepository.getBindCollection(bomConfigCheckEnDTO)).thenReturn(bomConfigBindCollectionT);
            bomConfigBindInfoImpl.getEntiyReliance(bomConfigCheckEnDTO, bomConfigBindInfoParamterDTO,
                    bomConfigBindCollectionTT);
        } catch (Exception e) {
            int sdfff = 0;
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void getEntiyReliance2() {
        try {
            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 1);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 3);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 3);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");

            bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
            opvvv.add(bomConfigBindInfoResultvDTO);
            bomConfigBindInfoImpl.bomResultListInterface = opvvv;
            BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();

            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();

            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setEntityId((long) 1);
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 3);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionT.add(bomConfigBindCollectionDTO);

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionTT = new ArrayList<>();
            PowerMockito.when(bomConfigBindInfoRepository.getBindCollection(bomConfigCheckEnDTO)).thenReturn(bomConfigBindCollectionT);
            bomConfigBindInfoImpl.getEntiyReliance(bomConfigCheckEnDTO, bomConfigBindInfoParamterDTO,
                    bomConfigBindCollectionTT);
        } catch (Exception e) {
            int sdfff = 0;
            Assert.assertEquals(MessageId.NO_ENTITY_NO_SITE_BIND, e.getMessage());
        }
    }

    @Test
    public void repleaceMaterial() {
        try {
            List<BomConfigCheckEnDTO> resultTvt = new ArrayList<>();
            BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();
            bomConfigCheckEnDTO.setItemId((long) 1);
            bomConfigCheckEnDTO.setItemName("1233");
            bomConfigCheckEnDTO.setItemCode("12333");
            bomConfigCheckEnDTO.setItemBarcode("12233");
            resultTvt.add(bomConfigCheckEnDTO);
            BomConfigCheckEnDTO bomConfigCheckEnDTODT = new BomConfigCheckEnDTO();
            bomConfigCheckEnDTODT.setItemId((long) 2);
            bomConfigCheckEnDTODT.setItemName("12334");
            bomConfigCheckEnDTODT.setItemCode("12335");
            bomConfigCheckEnDTO.setItemBarcode("12233");
            resultTvt.add(bomConfigCheckEnDTODT);
            bomConfigBindInfoImpl.resultTv = resultTvt;
            bomConfigBindInfoImpl.setItemMaxSSB();

            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 1);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 3);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 3);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");

            bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
            opvvv.add(bomConfigBindInfoResultvDTO);
            bomConfigBindInfoImpl.bomResultListInterface = opvvv;

            List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 3);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionT.add(bomConfigBindCollectionDTO);

            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoParamterDTO.setProcedule("装配");
            bomConfigBindInfoImpl.repleaceMaterial(bomConfigBindInfoParamterDTO, bomConfigBindCollectionT);
        } catch (Exception e) {
            int c = 0;
            Assert.assertNull(e.getMessage());
        }

    }

    @Test
    public void saveEnviromentFlags() {
        BomBindCombineDTO xy = new BomBindCombineDTO();
        doNothing().when(bomConfigBindInfoRepository).saveEnviromentFlag(xy);
        Assert.assertNotNull(xy);
    }

    @Test
    public void insertCombine() {
        try {
            //参数
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoParamterDTO.setProcedule("装配");


            BomBindCombineDTO xy = new BomBindCombineDTO();
            xy.setRecordId((long) 0);
            BomBindCombineDTO xy1 = new BomBindCombineDTO();
            doNothing().when(bomConfigBindInfoRepository).updateDigital(xy1);
            BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();
            doNothing().when(bomConfigBindInfoRepository).updateDigital(xy);

            //返回的参数
            List<BomConfigBindCollectionDTO> bomConfigColletion = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 3);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionDTO.setLastUpdateDate("2021/12/11 12:30:40");
            bomConfigColletion.add(bomConfigBindCollectionDTO);
            PowerMockito.when(bomConfigBindInfoRepository.getBindCollection(any())).thenReturn(bomConfigColletion);


            bomConfigBindInfoImpl.updateFlag = 1;
            bomConfigBindInfoImpl.insertCombine(xy, bomConfigBindInfoParamterDTO);
            bomConfigBindInfoImpl.updateFlag = 2;
            bomConfigBindInfoImpl.insertCombine(xy, bomConfigBindInfoParamterDTO);

            PowerMockito.when(bomConfigBindInfoRepository.getBindCollection(bomConfigCheckEnDTO)).thenReturn(bomConfigColletion);
            bomConfigBindInfoImpl.updateFlag = 1;
            bomConfigBindInfoImpl.insertCombine(xy, bomConfigBindInfoParamterDTO);

        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }

    @Test
    public void insertCommonQuality() {
        try {
            doNothing().when(bomConfigBindInfoRepository).insertDigital(any());
            bomConfigBindInfoImpl.insertCommonQuality(any());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void callCheckEnviroment() {

        doNothing().when(bomConfigCheckEnRepository).callCheckEnviroment(any());
        bomConfigCheckEnImpl.callCheckEnviroment(any());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void callCheckItemBarcode() {
        try {
            doNothing().when(bomConfigCheckEnRepository).callCheckItemBarcode(any());
            bomConfigCheckEnImpl.callCheckItemBarcode(any());
            PowerMockito.when(bomConfigCheckEnRepository.callItemId(any())).thenReturn(any());
            bomConfigCheckEnImpl.callItemId(any());
            PowerMockito.when(bomConfigCheckEnRepository.controlTechology(any())).thenReturn(any());
            bomConfigCheckEnImpl.controlTechology(any());

            PowerMockito.when(bomConfigCheckEnRepository.jobsBoxupBillsBind(any())).thenReturn(any());
            bomConfigCheckEnImpl.jobsBoxupBillsBind(any());
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void reduceSelectRelepace() {
        try {
            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 1);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 123);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");

            bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
            opvvv.add(bomConfigBindInfoResultvDTO);
            bomConfigBindInfoImpl.bomResultListInterface = opvvv;


            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoParamterDTO.setProcedule("装配");

            List<BomConfigBindCollectionDTO> bomConfigColletion = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 3);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionDTO.setLastUpdateDate("2021/12/11 12:30:40");
            bomConfigColletion.add(bomConfigBindCollectionDTO);

            bomConfigBindInfoImpl.selectRelepace(bomConfigColletion, bomConfigBindInfoParamterDTO);

            List<BomConfigCheckEnDTO> bomConfigCheckEnDTOCollection = new ArrayList<>();
            BomConfigCheckEnDTO ggg = new BomConfigCheckEnDTO();
            bomConfigCheckEnDTOCollection.add(ggg);
            PowerMockito.when(bomConfigCheckEnService.jobsBoxupBillsBind(any())).thenReturn(bomConfigCheckEnDTOCollection);
            bomConfigBindInfoImpl.reduceSelectRelepace(bomConfigBindInfoResultvDTO, bomConfigBindInfoParamterDTO, bomConfigCheckEnDTOCollection);

        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }

    @Test
    public void reduceValidatelity() {
        List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

        BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
        bomConfigBindInfoResultvDTO.setRowCount((long) 1);
        bomConfigBindInfoResultvDTO.setLevelno((long) 1);
        bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
        bomConfigBindInfoResultvDTO.setConfigDetailId((long) 1233);
        bomConfigBindInfoResultvDTO.setLevelNumber((long) 123);
        bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
        bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
        bomConfigBindInfoResultvDTO.setConfigSumlate("122");
        bomConfigBindInfoResultvDTO.setConfigUnit("1233");
        bomConfigBindInfoResultvDTO.setBoxQty("12");
        bomConfigBindInfoResultvDTO.setScanNum((long) 12);
        bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
        bomConfigBindInfoResultvDTO.setItemId((long) 123);
        bomConfigBindInfoResultvDTO.setSegmemt1("1233");
        bomConfigBindInfoResultvDTO.setDescription("1233");
        bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
        bomConfigBindInfoResultvDTO.setUnitOLD("1233");
        bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
        bomConfigBindInfoResultvDTO.setUnit("1233");
        bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
        bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
        bomConfigBindInfoResultvDTO.setMesName("1233");
        bomConfigBindInfoResultvDTO.setImportItemName("1233");
        bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

        bomConfigBindInfoResultvDTO.setcNUnit("1233");
        bomConfigBindInfoResultvDTO.setEnUNIT("1233");
        bomConfigBindInfoResultvDTO.setCode("1233");
        bomConfigBindInfoResultvDTO.setOperationDescription("1233");
        bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
        bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
        bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
        bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
        bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
        bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
        bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
        bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
        bomConfigBindInfoResultvDTO.setSiteAddress("1233");
        bomConfigBindInfoResultvDTO.setSiteCode("1233");
        bomConfigBindInfoResultvDTO.setSiteSN("1233");
        bomConfigBindInfoResultvDTO.setPtoCode("1233");
        bomConfigBindInfoResultvDTO.setPtoName("1233");
        bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
        bomConfigBindInfoResultvDTO.setMemo("1233");
        bomConfigBindInfoResultvDTO.setImportItemType("1233");
        bomConfigBindInfoResultvDTO.setLicensePrint("1233");
        bomConfigBindInfoResultvDTO.setAttribute10("1233");

        bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
        opvvv.add(bomConfigBindInfoResultvDTO);
        Assert.assertTrue(bomConfigBindInfoImpl.reduceValidatelity(opvvv));
    }

    @Test
    public void setcurrConfigDetailInfo() {
        try {
            List<List<BomConfigBindInfoResultvDTO>> opxxtt = new ArrayList<>();
            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 1);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 123);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");
            opvvv.add(bomConfigBindInfoResultvDTO);
            opxxtt.add(opvvv);
            bomConfigBindInfoImpl.setcurrConfigDetailInfo(opxxtt);

        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }

    @Test
    public void setAnatherInformation() {
        try {
            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 1);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 123);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");

            bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
            opvvv.add(bomConfigBindInfoResultvDTO);
            bomConfigBindInfoImpl.bomResultListInterface = opvvv;
            bomConfigBindInfoImpl.setAnatherInformation();
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }


    }

    @Test
    public void selectItemCodeItemID() {
        try {
            List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 1233);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionT.add(bomConfigBindCollectionDTO);
            //参数
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoParamterDTO.setProcedule("装配");
            bomConfigBindInfoImpl.selectItemCodeItemID(bomConfigBindCollectionT, bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void noBindCollection() {
        try {
            List<BomConfigBindInfoResultvDTO> opvvv = new ArrayList<>();

            BomConfigBindInfoResultvDTO bomConfigBindInfoResultvDTO = new BomConfigBindInfoResultvDTO();
            bomConfigBindInfoResultvDTO.setRowCount((long) 1);
            bomConfigBindInfoResultvDTO.setLevelno((long) 3);
            bomConfigBindInfoResultvDTO.setParentMaterial((long) 1233);
            bomConfigBindInfoResultvDTO.setConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setLevelNumber((long) 3);
            bomConfigBindInfoResultvDTO.setParentConfigDetailId((long) 1233);
            bomConfigBindInfoResultvDTO.setPoMaterialCode("1233");
            bomConfigBindInfoResultvDTO.setConfigSumlate("122");
            bomConfigBindInfoResultvDTO.setConfigUnit("1233");
            bomConfigBindInfoResultvDTO.setBoxQty("12");
            bomConfigBindInfoResultvDTO.setScanNum((long) 12);
            bomConfigBindInfoResultvDTO.setEnbaleFlag("Y");
            bomConfigBindInfoResultvDTO.setItemId((long) 123);
            bomConfigBindInfoResultvDTO.setSegmemt1("1233");
            bomConfigBindInfoResultvDTO.setDescription("1233");
            bomConfigBindInfoResultvDTO.setMaterialVersion("1233");
            bomConfigBindInfoResultvDTO.setUnitOLD("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglishOLD("1233");
            bomConfigBindInfoResultvDTO.setUnit("1233");
            bomConfigBindInfoResultvDTO.setUnitEnglish("1233");
            bomConfigBindInfoResultvDTO.setMaterialEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMesName("1233");
            bomConfigBindInfoResultvDTO.setImportItemName("1233");
            bomConfigBindInfoResultvDTO.setBoqItemNameEng("1233");

            bomConfigBindInfoResultvDTO.setcNUnit("1233");
            bomConfigBindInfoResultvDTO.setEnUNIT("1233");
            bomConfigBindInfoResultvDTO.setCode("1233");
            bomConfigBindInfoResultvDTO.setOperationDescription("1233");
            bomConfigBindInfoResultvDTO.setMidShiftModuleCode("1233");
            bomConfigBindInfoResultvDTO.setOrderNoOfChinaMobile("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteName("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteCode("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteEquipMent("1233");
            bomConfigBindInfoResultvDTO.setMfgSiteId((long) 123);
            bomConfigBindInfoResultvDTO.setMfgSiteCodeD("1233");
            bomConfigBindInfoResultvDTO.setSiteID((long) 1233);
            bomConfigBindInfoResultvDTO.setSiteAddress("1233");
            bomConfigBindInfoResultvDTO.setSiteCode("1233");
            bomConfigBindInfoResultvDTO.setSiteSN("1233");
            bomConfigBindInfoResultvDTO.setPtoCode("1233");
            bomConfigBindInfoResultvDTO.setPtoName("1233");
            bomConfigBindInfoResultvDTO.setPtoEnglishName("1233");
            bomConfigBindInfoResultvDTO.setMemo("1233");
            bomConfigBindInfoResultvDTO.setImportItemType("1233");
            bomConfigBindInfoResultvDTO.setLicensePrint("1233");
            bomConfigBindInfoResultvDTO.setAttribute10("1233");

            bomConfigBindInfoResultvDTO.setBoqConfigDetailId((long) 1233);
            opvvv.add(bomConfigBindInfoResultvDTO);
            bomConfigBindInfoImpl.bomResultListInterface = opvvv;
            bomConfigBindInfoImpl.getMaterilaItemNo("1233");
            bomConfigBindInfoImpl.getContailConfigDetail((long) 1233);
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoParamterDTO.setCreateBy("10144021");
            bomConfigBindInfoParamterDTO.setEntityId((long) 123333);
            bomConfigBindInfoParamterDTO.setEntityName("WDMSMQ-M20191100001");
            bomConfigBindInfoParamterDTO.setItemBarcode("219994100337");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 208053796);
            bomConfigBindInfoParamterDTO.setNoLinkTask(false);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoParamterDTO.setProcedule("装配");


            List<BomConfigBindCollectionDTO> bomConfigBindCollectionT = new ArrayList<>();
            BomConfigBindCollectionDTO bomConfigBindCollectionDTO = new BomConfigBindCollectionDTO();
            bomConfigBindCollectionDTO.setEntityId((long) 1);
            bomConfigBindCollectionDTO.setBarcodeQty((long) 1);
            bomConfigBindCollectionDTO.setBarcodeType("123");
            bomConfigBindCollectionDTO.setConfigDetailId((long) 1233);
            bomConfigBindCollectionDTO.setEntityName("12333");
            bomConfigBindCollectionDTO.setItemBarcode("12333");
            bomConfigBindCollectionDTO.setRelatedItemName("123333");
            bomConfigBindCollectionDTO.setItemCode("12333");
            bomConfigBindCollectionDTO.setLastUpdateDate("12333");
            bomConfigBindCollectionDTO.setNodeCount((long) 12333);
            bomConfigBindCollectionDTO.setOrganizationId((long) 635);
            bomConfigBindCollectionDTO.setParentRecordID("1");
            bomConfigBindCollectionDTO.setRecordId((long) 233);
            bomConfigBindCollectionDTO.setRelatedItemCode("123333");
            bomConfigBindCollectionDTO.setRelatedItemId((long) 1233);
            bomConfigBindCollectionT.add(bomConfigBindCollectionDTO);
            bomConfigBindInfoImpl.setParamMap(bomConfigBindInfoParamterDTO);
            bomConfigBindInfoImpl.combineDto(bomConfigBindCollectionT, bomConfigBindInfoParamterDTO, false);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void isNotEmpty() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();

        bomConfigBindInfoParamterDTO.setCreateBy("12333");
        bomConfigBindInfoParamterDTO.setEntityName("12333");
        bomConfigBindInfoParamterDTO.setEntityName("1233333");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 1233);
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 879);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        bomConfigBindInfoParamterDTO.setOrgId((long) 123);
        bomConfigBindInfoParamterDTO.setItemBarcode("789");
        bomConfigBindInfoParamterDTO.setItemBarcode("12333");
        Assert.assertFalse(bomConfigBindInfoImpl.isListEmpty(bomConfigBindInfoParamterDTO));
    }

    @Test
    public void valiateEnviromentdt() {
        BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
        bomConfigBindInfoParamterDTO.setCreateBy("12333");
        bomConfigBindInfoParamterDTO.setEntityName("12333");
        bomConfigBindInfoParamterDTO.setEntityName("1233333");
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 1233);
        bomConfigBindInfoParamterDTO.setMfgSiteId((long) 879);
        bomConfigBindInfoParamterDTO.setOrgId((long) 635);
        bomConfigBindInfoParamterDTO.setOrgId((long) 123);
        bomConfigBindInfoParamterDTO.setItemBarcode("789");
        bomConfigBindInfoParamterDTO.setItemBarcode("12333");
        Assert.assertNull(bomConfigBindInfoImpl.valiateEnviroment(bomConfigBindInfoParamterDTO));
    }

    @Test
    public void getLinkedBug() {
        try {
            BomConfigCheckEnDTO bomConfigCheckEnDTO = new BomConfigCheckEnDTO();
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            bomConfigBindInfoImpl.getLinkedBug(bomConfigCheckEnDTO, bomConfigBindInfoParamterDTO);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
    @Test
    public void getDqAs(){
        try {
            BomConfigBindInfoParamterDTO bomConfigBindInfoParamterDTO = new BomConfigBindInfoParamterDTO();
            List<BomConfigBindCollectionDTO> bomConfigBindCollection = new ArrayList<>();
            BomConfigBindCollectionDTO dtx = new BomConfigBindCollectionDTO();
            bomConfigBindCollection.add(dtx);
            bomConfigBindInfoParamterDTO.setCreateBy("12333");
            bomConfigBindInfoParamterDTO.setEntityName("12333");
            bomConfigBindInfoParamterDTO.setEntityName("1233333");
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 1233);
            bomConfigBindInfoParamterDTO.setMfgSiteId((long) 879);
            bomConfigBindInfoParamterDTO.setOrgId((long) 635);
            bomConfigBindInfoParamterDTO.setOrgId((long) 123);
            bomConfigBindInfoParamterDTO.setItemBarcode("789");
            bomConfigBindInfoParamterDTO.setItemBarcode("12333");
            CheckInDTO chekInDTO = new CheckInDTO();
            CheckOutDTO checkOutDTO = new CheckOutDTO();
            PowerMockito.when(configMaterialBaindDqasNewService.checkDqas(Mockito.any())).thenReturn(checkOutDTO);
            PowerMockito.when(configMaterialBaindDqasNewService.checkDqasOld(chekInDTO)).thenReturn(checkOutDTO);
            bomConfigBindInfoImpl.currConfigDetailInfo = new ArrayList<>();
            List<BomConfigBindInfoResultvDTO> currConfigDetailInfo = new ArrayList<>();
            BomConfigBindInfoResultvDTO kl = new BomConfigBindInfoResultvDTO();
            kl.setSegmemt1("12333");
            currConfigDetailInfo.add(kl);
            bomConfigBindInfoImpl.currConfigDetailInfo = currConfigDetailInfo;
            bomConfigBindInfoImpl.getDqAs(bomConfigBindInfoParamterDTO, bomConfigBindCollection);
        }
        catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
    }



}
