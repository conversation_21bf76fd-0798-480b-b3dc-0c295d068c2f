package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.OpProdPlanInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.OpProductinfoRepository;
import com.zte.util.PowerBaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-08-31 16:27
 */
public class OpProdPlanInfoServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    private OpProdPlanInfoServiceImpl opProdPlanInfoServiceImpl;
    @Mock
    private OpProductinfoRepository opProductinfoRepository;

    @Before
    public void init(){

    }

    @Test
    public void getSubmitStatusBatch() {
        List<String> prodPlanIdList = new LinkedList<>();
        prodPlanIdList.add("123");
        PowerMockito.when(opProductinfoRepository.getSubmitStatusBatch(Mockito.anyList()))
                .thenReturn(prodPlanIdList);
        Assert.assertNotNull(opProdPlanInfoServiceImpl.getSubmitStatusBatch(prodPlanIdList));
    }


}
