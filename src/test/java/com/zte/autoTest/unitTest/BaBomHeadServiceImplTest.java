package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.datawb.impl.BaBomHeadServiceImpl;
import com.zte.application.stepdt.impl.AvlServiceImpl;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.BaBomHeadRepository;
import com.zte.domain.model.stepdt.AvlRepository;
import com.zte.interfaces.stepdt.dto.AvlDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doReturn;

public class BaBomHeadServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private BaBomHeadServiceImpl baBomHeadServiceImpl;
    @Mock
    private BaBomHeadRepository baBomHeadRepository;

    @Test
    public void getBomInfoByBomNo() throws Exception {
        BaBomHead BaBomHead=new BaBomHead();
        Assert.assertNotNull(baBomHeadServiceImpl.getBomInfoByBomNo(BaBomHead));
    }

    @Test
    public void getBomInfoByBomNoList() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("name");
        List<BaBomHead> baBomHeadList = new ArrayList<>();
        baBomHeadList.add(new BaBomHead());
        PowerMockito.when(baBomHeadRepository.getBomInfoByBomNoList(any())).thenReturn(baBomHeadList);
        Assert.assertNotNull(baBomHeadServiceImpl.getBomInfoByBomNoList(list));
    }

    @Test
    public void getParamByItemId() throws Exception {
        BaBomHead BaBomHead=new BaBomHead();
        List<String> itemIdList=new ArrayList<>();
        itemIdList.add("1");
        BaBomHead.setItemIdList(itemIdList);
        Assert.assertNotNull(baBomHeadServiceImpl.getParamByItemId(BaBomHead));
    }

    @Test
    public void getInStrFromList() {
        Assert.assertNotNull(baBomHeadServiceImpl.getInStrFromList(Lists.newArrayList("1")));
    }
}
