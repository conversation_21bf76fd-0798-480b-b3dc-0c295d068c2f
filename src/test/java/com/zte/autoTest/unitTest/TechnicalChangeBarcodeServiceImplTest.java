package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.TechnicalChangeBarcodeServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.TechnicalChangeBarcodeRepository;
import com.zte.interfaces.dto.TechnicalChangeBarcodeDTO;
import com.zte.interfaces.dto.TechnicalChangeExecInfoEntityDTO;
import com.zte.interfaces.dto.TechnicalSummaryInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

/**
 * 锁定条码信息池测试类
 * <AUTHOR> 袁海洋
 */
public class TechnicalChangeBarcodeServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private TechnicalChangeBarcodeServiceImpl technicalChangeBarcodeService;
    @Mock
    private TechnicalChangeBarcodeRepository technicalChangeBarcodeRepository;

    @Test
    public void setTechnicalChangeBarcodeRepository() throws Exception {
        technicalChangeBarcodeService.setTechnicalChangeBarcodeRepository(technicalChangeBarcodeRepository);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getTechnicalChangeBarcode() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("1");
        List<TechnicalChangeBarcodeDTO> technicalChangeBarcodeDTOList  = new ArrayList<>();
//        technicalChangeBarcodeService.getTechnicalChangeBarcode(list);
//        doReturn(technicalChangeBarcodeDTOList).when(technicalChangeBarcodeRepository).selectTechnicalChangeList(list);
        Assert.assertNotNull(list);
    }

    @Test(expected = NullPointerException.class)
    public void getBarAttr() throws Exception {
        technicalChangeBarcodeService = PowerMockito.spy(new TechnicalChangeBarcodeServiceImpl());
        TechnicalChangeBarcodeDTO dto = new TechnicalChangeBarcodeDTO();
        dto.setOrganizationId(new BigDecimal(1));
        dto.setBarcode("111111111111111111");
        dto.setEntityBarAttr("2222222");
        String snBarAttr = "2222222";
        when(technicalChangeBarcodeService.getSnBarAttr(dto.getBarcode())).thenReturn(snBarAttr);
        doThrow(new RuntimeException()).when(technicalChangeBarcodeRepository).checkEntityAndSnBarAttr(dto);
        //technicalChangeBarcodeService.getBarAttr(dto);
    }

    @Test()
    public void deleteTechnicalInfoBySn() throws Exception {
        List<TechnicalChangeExecInfoEntityDTO> dtoList = new ArrayList<>();
        try{
            technicalChangeBarcodeService.deleteTechnicalInfoBySn(dtoList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR,e.getExMsgId());
        }
        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setChgReqNo("123");
        dto.setSn("777766600001");
        dtoList.add(dto);
        technicalChangeBarcodeService.deleteTechnicalInfoBySn(dtoList);
    }

    @Test()
    public void deleteTechnicalInfo() throws Exception {
        TechnicalSummaryInfoDTO dto = new TechnicalSummaryInfoDTO();
        technicalChangeBarcodeService.deleteTechnicalInfo(dto);
        Assert.assertNotNull(dto);
    }
}
