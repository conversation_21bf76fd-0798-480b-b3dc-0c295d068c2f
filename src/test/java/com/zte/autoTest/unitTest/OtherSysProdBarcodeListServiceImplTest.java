package com.zte.autoTest.unitTest;

import com.zte.application.datawb.impl.OtherSysProdBarcodeListServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.datawb.BarSubmitRepository;
import com.zte.domain.model.datawb.BoardOnlinelist;
import com.zte.domain.model.datawb.OtherSysProdBarcodeListRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class OtherSysProdBarcodeListServiceImplTest {

    @InjectMocks
    OtherSysProdBarcodeListServiceImpl otherSysProdBarcodeListServiceImpl;

    @Mock
    private OtherSysProdBarcodeListRepository otherSysProdBarcodeListRepository;
    @Mock
    private Boolean snWriteSpm ;

    @Mock
    BarSubmitRepository barSubmitRepository;


    @Test
    public void deleteByBillNoAndProdplanIdBatch() throws Exception {
        List<WarehouseEntryInfo> list = new ArrayList<>();
        otherSysProdBarcodeListServiceImpl.deleteByBillNoAndProdplanIdBatch(list);
        Assert.assertNotNull(list);
        list.add(new WarehouseEntryInfo());
        otherSysProdBarcodeListServiceImpl.deleteByBillNoAndProdplanIdBatch(list);
        Assert.assertNotNull(list);
        for (int i = 0; i < 10005; i++) {
            list.add(new WarehouseEntryInfo());
        }
        try{
            otherSysProdBarcodeListServiceImpl.deleteByBillNoAndProdplanIdBatch(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR,e.getMessage());
        }
    }


    @Test
    public void add() throws Exception {
        otherSysProdBarcodeListServiceImpl.add(Mockito.anyObject());
        Mockito.verify(otherSysProdBarcodeListRepository, Mockito.times(1))
                .insertOtherSysProdBarcodeList(Mockito.anyObject());
    }

    @Test
    public void updateImuStatusBatch() {
        List<WarehouseEntryInfo> list = new LinkedList<>();
        WarehouseEntryInfo a1 = new WarehouseEntryInfo();
        a1.setBoardOnlinelist(new LinkedList<>());
        list.add(a1);
        otherSysProdBarcodeListServiceImpl.updateImuStatusBatch(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void updateImuBySn() throws Exception{
        List<BoardOnlinelist> list = new LinkedList<>();
        BoardOnlinelist a1 = new BoardOnlinelist();
        a1.setProdplanId(new BigDecimal(888));
        a1.setBoardSn(new BigDecimal(1));
        list.add(a1);

        PowerMockito.when(otherSysProdBarcodeListRepository.selectBoardOnline(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(Arrays.asList("1"));

        otherSysProdBarcodeListServiceImpl.updateImuBySn(list,9);
        Assert.assertNotNull(list);
    }

}