package com.zte.interfaces.assembler;

import com.zte.common.utils.Constant;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.dto.ArchiveBomLockingBillDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
public class ArchiveBomLockingBillAssemblerTest extends PowerBaseTestCase {

    @Test
    public void initItemContent(){
        ArchiveBomLockingBillAssembler.initItemContent(null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
    @Test
    public void initItemContent1(){
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        ArchiveBomLockingBillDTO dto = new ArchiveBomLockingBillDTO();
        PubHrvOrg person = new PubHrvOrg();
        ArchiveBomLockingBillAssembler.initItemContent(item,dto,person);
        Assert.assertNotNull(dto);
    }
}
