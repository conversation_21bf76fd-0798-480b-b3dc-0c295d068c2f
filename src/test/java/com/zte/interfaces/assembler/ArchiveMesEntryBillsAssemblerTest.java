package com.zte.interfaces.assembler;

import com.zte.common.utils.Constant;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/14
 * @description :
 */
public class ArchiveMesEntryBillsAssemblerTest extends PowerBaseTestCase {

    @Test
    public void initItemContent(){
        ArchiveMesEntryBillsAssembler.initItemContent(null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
    @Test
    public void initItemContent1(){
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        ArchiveMesEntryBillsDTO dto = new ArchiveMesEntryBillsDTO();
        PubHrvOrg user = new PubHrvOrg();
        ArchiveMesEntryBillsAssembler.initItemContent(item,dto,user);
        Assert.assertNotNull(dto);
    }
}
