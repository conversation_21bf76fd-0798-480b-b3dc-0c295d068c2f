package com.zte.interfaces.assembler;

import com.zte.common.utils.Constant;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
public class ArchiveBoqBoxMakeAssemblerTest extends PowerBaseTestCase {

    @Test
    public void initItemContent(){
        ArchiveBoqBoxMakeAssembler.initItemContent(null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
    @Test
    public void initItemContent1(){
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        ArchiveBoqBoxMakeDTO dto = new ArchiveBoqBoxMakeDTO();
        PubHrvOrg person = new PubHrvOrg();
        ArchiveBoqBoxMakeAssembler.initItemContent(item,dto,person);
        Assert.assertNotNull(dto);
    }

    @Test
    public void setDatabaseType(){
        ArchiveBoqBoxMakeDTO dateItem = new ArchiveBoqBoxMakeDTO();
        Assert.assertNotNull(ArchiveBoqBoxMakeAssembler.getArchiveTaskSend(dateItem,"",""));
    }
}
