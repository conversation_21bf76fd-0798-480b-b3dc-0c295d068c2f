package com.zte.interfaces.assembler;

import com.zte.common.utils.Constant;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.dto.ArchiveMesBackBillsDTO;
import com.zte.interfaces.dto.ArchivePsEntityPlanDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/16
 * @description :
 */
public class ArchiveMesBackBillsAssemblerTest extends PowerBaseTestCase {
    @Test
    public void initItemContent(){
        ArchiveMesBackBillsAssembler.initItemContent(null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
    @Test
    public void initItemContent1(){
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        ArchiveMesBackBillsDTO dto = new ArchiveMesBackBillsDTO();
        PubHrvOrg person = new PubHrvOrg();
        ArchiveMesBackBillsAssembler.initItemContent(item,dto,person);
        Assert.assertNotNull(dto);
    }
}
