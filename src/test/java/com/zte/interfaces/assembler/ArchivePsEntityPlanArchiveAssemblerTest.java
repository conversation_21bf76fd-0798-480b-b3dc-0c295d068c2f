package com.zte.interfaces.assembler;

import com.zte.common.utils.Constant;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.assembler.ArchivePsEntityPlanAssembler;
import com.zte.interfaces.dto.ArchivePsEntityPlanDTO;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/27
 * @description :
 */
public class ArchivePsEntityPlanArchiveAssemblerTest extends BaseTestCase {

    @Test
    public void initItemContent(){
        ArchivePsEntityPlanAssembler.initItemContent(null,null,null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
    @Test
    public void initItemContent1(){
        ArchiveReq.ArchiveItem item = new ArchiveReq.ArchiveItem();
        ArchivePsEntityPlanDTO dto = new ArchivePsEntityPlanDTO();
        PubHrvOrg person = new PubHrvOrg();
        ArchivePsEntityPlanAssembler.initItemContent(item,dto,person);
        Assert.assertNotNull(dto);
    }
}
