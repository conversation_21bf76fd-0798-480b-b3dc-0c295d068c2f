package com.zte.common.utils;

import com.zte.application.ArchiveBoqBoxMakeDataService;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/10/18
 * @description :
 */
public class BoxMakeUtilTest extends BaseTestCase {
    @Mock
    ArchiveBoqBoxMakeDataService archiveBoqBoxMakeDataService;

    @Test
    public void getArchiveDataList(){
        ArchiveBoqBoxMakeDTO dto = new ArchiveBoqBoxMakeDTO();
        Page<ArchiveBoqBoxMakeDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(archiveBoqBoxMakeDataService.getPageByDateRange(any(),any()))
                .thenReturn(page);

        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setPage(1);
        query.setRows(500);

        Page<ArchiveTaskSend> pageInfo = BoxMakeUtil.getArchiveDataList(query,archiveBoqBoxMakeDataService,"boq","123");
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
}
