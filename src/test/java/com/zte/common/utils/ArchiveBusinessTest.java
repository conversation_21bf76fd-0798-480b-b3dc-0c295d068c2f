package com.zte.common.utils;

import com.zte.common.utils.ArchiveBusiness;
import com.zte.domain.model.PubHrvOrg;
import com.zte.interfaces.dto.ArchiveBusinessVo;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/4/11
 */
public class ArchiveBusinessTest extends BaseTestCase {

    @Test
    public void  generateXmlData(){
        ArchiveBusinessVo business=new ArchiveBusinessVo();
        ArchiveBusiness.generateXmlData(business);
        business.setOther("a");
        business.setOtherTwo("b");
        business.setOtherThree("c");
        business.setOtherFour("d");
        Assert.assertNotNull(ArchiveBusiness.generateXmlData(business));
    }

    @Test
    public void buildArchiveItemDocVo(){
        List<AttachmentUploadVo> attachmentList=new ArrayList<>();
        ArchiveBusiness.buildArchiveItemDocVo(attachmentList);

        AttachmentUploadVo attachmentUploadVo=new AttachmentUploadVo();
        attachmentUploadVo.setFileName("file");
        attachmentUploadVo.setDiskKey("1234");
        attachmentUploadVo.setFileSize(12345l);
        attachmentList.add(attachmentUploadVo);
        ArchiveBusiness.buildArchiveItemDocVo(attachmentList);

        Assert.assertNotNull(ArchiveBusiness.buildArchiveItemDocVo(attachmentUploadVo));
    }

    @Test
    public  void buildArchiveItemDoc(){
        AttachmentUploadVo attachmentUploadVo=new AttachmentUploadVo();
        attachmentUploadVo.setFileName("file.txt");
        attachmentUploadVo.setDiskKey("1234");
        attachmentUploadVo.setFileSize(12345l);
        ArchiveBusiness.buildArchiveItemDoc(attachmentUploadVo);

        Assert.assertNull(ArchiveBusiness.buildArchiveItemDoc(null));
    }

    @Test
    public void buildArchiveItemVo(){
        Assert.assertNotNull(ArchiveBusiness.buildArchiveItemVo("1","fileName",""));
    }

    @Test
    public void buildAttachmentUpload(){
        List<AttachmentUploadVo> attachmentList=new ArrayList<>();
        AttachmentUploadVo attachmentUploadVo=new AttachmentUploadVo();
        attachmentUploadVo.setFileName("file");
        attachmentUploadVo.setDiskKey("1234");
        attachmentUploadVo.setFileSize(12345l);
        attachmentList.add(attachmentUploadVo);

        AttachmentUploadVo attachmentUpload=new AttachmentUploadVo();
        attachmentUpload.setFileName("file");
        attachmentUpload.setDiskKey("1234");
        attachmentUploadVo.setFileSize(12345l);
        ArchiveBusiness.buildAttachmentUpload("123",null,attachmentUpload);
        ArchiveBusiness.buildAttachmentUpload("123",attachmentList,attachmentUpload);
        Assert.assertNotNull(ArchiveBusiness.buildAttachmentUpload("",attachmentList,attachmentUpload));
    }

    @Test
    public void setDefaultDept(){
        ArchiveBusiness.setDefaultDept(null);
        PubHrvOrg pubHrvOrg = new PubHrvOrg();
        ArchiveBusiness.setDefaultDept(pubHrvOrg);
        pubHrvOrg.setOrgNo("12345");
        Assert.assertNotNull(ArchiveBusiness.setDefaultDept(pubHrvOrg));
    }

    @Test
    public void setDefaultDeptGz(){
        ArchiveBusiness.setDefaultDeptGz(null);
        PubHrvOrg pubHrvOrg = new PubHrvOrg();
        ArchiveBusiness.setDefaultDeptGz(pubHrvOrg);
        pubHrvOrg.setOrgNo("12345");
        Assert.assertNotNull(ArchiveBusiness.setDefaultDeptGz(pubHrvOrg));
    }

    @Test
    public void attachmentFromMapToVO(){
        ArchiveBusiness.attachmentFromMapToVO(null);
        List<Map<String,String>> attachments=new ArrayList<>();
        Map<String,String> attachment=new HashMap<>();
        attachment.put("docId","12345");
        attachment.put("docName","文件1");
        attachment.put("docSize","1234");
        attachment.put("Encrypt","abc");
        attachment.put("createdDate","2020-01-01 01:01:01");
        attachments.add(attachment);
        Assert.assertNotNull(ArchiveBusiness.attachmentFromMapToVO(attachments));
    }

    @Test
    public void convertFileSize(){
        ArchiveBusiness.convertFileSize("0");
        Assert.assertNotNull(ArchiveBusiness.convertFileSize("123456"));
    }

}
