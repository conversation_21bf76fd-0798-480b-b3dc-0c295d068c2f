package com.zte.common.utils;

import com.zte.common.utils.FunctionUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class FunctionUtilTest extends BaseTestCase {

    @Test
    public void execute(){
        List<ArchiveTaskSend> list = new ArrayList<>();
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        list.add(archiveTaskSend);
        FunctionUtil.execute(list,splitList->{});
        Assert.assertNotNull(list);
    }



    @Test
    public void executeBatch(){
        List<ArchiveTaskSend> list = new ArrayList<>();
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        list.add(archiveTaskSend);
        FunctionUtil.executeBatch(list,2,splitList->{});
        Assert.assertNotNull(list);
    }

    @Test
    public void executeWithReturn(){
        List<ArchiveTaskSend> list = new ArrayList<>();
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        list.add(archiveTaskSend);
        Assert.assertNotNull(FunctionUtil.executeWithReturn(list,splitList->{return splitList;}));
    }

    @Test
    public void executeBatchWithReturn(){
        List<ArchiveTaskSend> list = new ArrayList<>();
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        list.add(archiveTaskSend);
        Assert.assertNotNull(FunctionUtil.executeBatchWithReturn(list,2,splitList->{return splitList;}));
    }
}
