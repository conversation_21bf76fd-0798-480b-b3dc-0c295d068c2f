package com.zte.common.utils;

import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import static com.zte.common.utils.DateUtil.DATE_FORMATE_FULL;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/1
 * @description :
 */
public class DateUtilTest extends BaseTestCase {
    @Test
    public void convertStringToDate() {
        try{
            DateUtil.convertStringToDate("2023-09-12 00:00:00", DATE_FORMATE_FULL);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }
    }

    @Test
    public void convertStringToDateError() {
        try{
            DateUtil.convertStringToDate("2023-09-12 00:00:", DATE_FORMATE_FULL);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),e.getMessage());
        }

    }


    @Test
    public void convertDateToString(){
        Assert.assertNotNull(DateUtil.convertDateToString(new Date(),DATE_FORMATE_FULL));
    }

    @Test
    public void convertNowToString(){
        Assert.assertNotNull(DateUtil.convertNowToString(DATE_FORMATE_FULL));
    }

    @Test
    public void convertNowToString2(){
        Assert.assertNotNull(DateUtil.convertNowToString(Calendar.getInstance(),DATE_FORMATE_FULL));
    }

    @Test
    public void convertNowToString3(){
        Assert.assertNotNull(DateUtil.convertNowToString());
    }

    @Test
    public void convertNowToString4(){
        Assert.assertNotNull(DateUtil.convertNowToString(Calendar.getInstance()));
    }

    @Test
    public void addDay(){
        Assert.assertNotNull(DateUtil.addDay(new Date(),5));
    }

    @Test
    public void addDay2(){
        Assert.assertNotNull( DateUtil.addDay(5));
    }

    @Test
    public void getNextDate(){
        Assert.assertNotNull(DateUtil.getNextDate(new Date()));
    }

    @Test
    public void getYear(){
        Assert.assertNotNull(DateUtil.getYear(new Date()));
    }

    @Test
    public void getCurYear(){
        Assert.assertNotNull(DateUtil.getCurYear());
    }

    @Test
    public void getNextYear(){
        Assert.assertNotNull(DateUtil.getNextYear());
    }

    @Test
    public void getQuarter(){
        Assert.assertNotNull(DateUtil.getQuarter(new Date()));
    }

    @Test
    public void getCurQuarter(){
        Calendar cal=Calendar.getInstance();
        DateUtil.getCurQuarter(cal);

        cal.set(Calendar.MONTH,1);
        DateUtil.getCurQuarter(cal);

        cal.set(Calendar.MONTH,4);
        DateUtil.getCurQuarter(cal);

        cal.set(Calendar.MONTH,6);
        DateUtil.getCurQuarter(cal);

        cal.set(Calendar.MONTH,10);
        Assert.assertNotNull(DateUtil.getCurQuarter(cal));

    }

    @Test
    public void getCurQuarter2(){
        Assert.assertNotNull(DateUtil.getCurQuarter());
    }

    @Test
    public void getCurYearQuarter(){
        Assert.assertNotNull(DateUtil.getCurYearQuarter());
    }

    @Test
    public void getCurDay(){
        Assert.assertNotNull(DateUtil.getCurDay());
    }

    @Test
    public void getAfterMonthFirstDate(){
        Assert.assertNotNull(DateUtil.getAfterMonthFirstDate(1));
    }

    @Test
    public void addDateTimeMinute(){
        Assert.assertNotNull(DateUtil.addDateTimeMinute(new Date(),1));
    }

    @Test
    public void addDateTimeSecond(){
        Assert.assertNotNull(DateUtil.addDateTimeSecond(new Date(),1));
    }

    @Test
    public void caldaysByDate(){
        Assert.assertNotNull(DateUtil.caldaysByDate(new Date(),new Date(),1));
    }

    @Test
    public void caldaysByDate2(){
        Assert.assertNotNull(DateUtil.caldaysByDate(null,null,1));
    }

    @Test
    public void getMonth(){
        Assert.assertNotNull(DateUtil.getMonth(new Date()));
    }

    @Test
    public void getLastDayOfMonth(){
        DateUtil.getLastDayOfMonth(null);
        Assert.assertNotNull(DateUtil.getLastDayOfMonth("2023-09-12"));
    }
}
