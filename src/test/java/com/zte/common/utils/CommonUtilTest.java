package com.zte.common.utils;

import com.zte.common.utils.CommonUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/1
 * @description :
 */
public class CommonUtilTest extends BaseTestCase {

    @Test
    public void getString(){
        String str1 = "123";
        Assert.assertNotNull(CommonUtil.getString(str1));
    }
    @Test
    public void getString1(){
        String str2 = "";
        Assert.assertNotNull(CommonUtil.getString(str2));
    }


    @Test
    public void join(){
        Assert.assertNotNull(CommonUtil.join("-","123","123"));
    }
    @Test
    public void join1(){
        Assert.assertNotNull(CommonUtil.join("-"));
    }

    @Test
    public void joinFilterNull(){
        Assert.assertNotNull(CommonUtil.join("",null));
    }

    @Test
    public void joinFilterNull1(){
        Assert.assertNotNull(CommonUtil.join("","123"," ","345",null));
    }

    @Test
    public void joinFilterNull2(){
        Assert.assertNotNull(CommonUtil.joinFilterNull(";",null));
    }
    @Test
    public void joinFilterNull3(){
        Assert.assertNotNull(CommonUtil.joinFilterNull(";","a;b;c"));
    }
    @Test
    public void joinFilterNull4(){
        Assert.assertNotNull(CommonUtil.joinFilterNull(";",""));
    }

    @Test
    public void regexUserId(){
        Assert.assertNotNull(CommonUtil.regexUserId(""));
    }

    @Test
    public void regexUserId2(){
        Assert.assertNotNull(CommonUtil.regexUserId("张三123456"));
    }
}
