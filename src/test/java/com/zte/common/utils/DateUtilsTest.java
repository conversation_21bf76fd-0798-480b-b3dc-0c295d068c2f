package com.zte.common.utils;

import com.zte.common.utils.DateUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

import java.text.ParseException;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/1
 * @description :
 */
public class DateUtilsTest extends BaseTestCase {
    @Test
    public void now(){
        Assert.assertNotNull(DateUtils.now());
    }

    @Test
    public void getDate() throws ParseException {
        String date = "2022-03-01 00:00:00";
        Assert.assertNotNull(DateUtils.getDate(date,DateUtils.DATE_FORMAT_FULL));
    }
    @Test
    public void getDate1() throws ParseException {
        String date = "2022-03-01 00:00:00";
        Assert.assertNotNull(DateUtils.getDate(date,null));
    }

    @Test
    public void getDate2() throws ParseException {
        Assert.assertNull(DateUtils.getDate(null,DateUtils.DATE_FORMAT_FULL));
    }

    @Test
    public void format(){
        Assert.assertNotNull(DateUtils.format(new Date(),DateUtils.DATE_FORMAT_FULL));
    }
    @Test
    public void format1(){
        Assert.assertNotNull(DateUtils.format(new Date(),null));
    }

    @Test
    public void format2(){
        Assert.assertNotNull(DateUtils.format(null,DateUtils.DATE_FORMAT_FULL));
    }

    @Test
    public void getSpecificDate(){
        Assert.assertNotNull(DateUtils.getSpecificDate(new Date(),1));
    }

    @Test
    public void getSpecificDate2(){
        Assert.assertNotNull(DateUtils.getSpecificDate(null,1));
    }
}
