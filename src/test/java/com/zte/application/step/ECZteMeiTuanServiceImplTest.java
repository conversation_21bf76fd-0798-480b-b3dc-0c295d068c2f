package com.zte.application.step;

import com.zte.application.step.impl.ECZteMeiTuanServiceImpl;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.ECLotxlocxid;
import com.zte.interfaces.infor.dto.ECLotxlocxidDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.utils.CommonUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.zte.common.utils.Constant.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisSerialNoUtil.class, CommonUtils.class, CollectionUtils.class})
public class ECZteMeiTuanServiceImplTest {

    @InjectMocks
    private ECZteMeiTuanServiceImpl ecZteMeiTuanService;

    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;

    @Mock
    private InforBarcodeCenterRepository barcodeCenterRepository;

    @Mock
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;

    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(RedisSerialNoUtil.class, CommonUtils.class, CollectionUtils.class);
    }

    /**
     * Test normal flow - complete process with all data
     */
    @Test
    public void uploadStockInfo_normalFlow() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023", "045020200075");
        String empNo = "12345";

        // Mock customer items
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOList();
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(customerItemsDTOList);

        // Mock warehouse info
        List<String> inventoryHoldWmwhids = Arrays.asList("WMWHSE1", "WMWHSE2");
        List<String> badInventoryHoldWmwhids = Arrays.asList("WMWHSE3");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(inventoryHoldWmwhids);
        PowerMockito.when(barcodeCenterRepository.getBadInventoryHoldWmwhids()).thenReturn(badInventoryHoldWmwhids);

        // Mock inventory data
        List<ECLotxlocxid> inventoryHoldList = createInventoryHoldList();
        List<ECLotxlocxid> badInventoryHoldList = createBadInventoryHoldList();
        PowerMockito.when(inventoryDiffQueryRepository.getStockQtyWithWisId(any(ECLotxlocxidDTO.class)))
                .thenReturn(inventoryHoldList).thenReturn(badInventoryHoldList);

        // Mock utility methods
        mockCommonUtilsSplitList();
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(ZTE1), eq(INT_6)))
                .thenReturn("ZTE120240621000001");

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify calls
        Mockito.verify(imesCenterfactoryRemoteService).getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo));
        Mockito.verify(barcodeCenterRepository).getInventoryHoldWmwhids();
        Mockito.verify(barcodeCenterRepository).getBadInventoryHoldWmwhids();
        Mockito.verify(inventoryDiffQueryRepository, Mockito.times(2)).getStockQtyWithWisId(any(ECLotxlocxidDTO.class));
        Mockito.verify(zteStockInfoUploadRepository, Mockito.times(2)).insertStockUploadSnapshot(any());
        Mockito.verify(zteStockInfoUploadRepository).insertStockUploadLog(any());
        Mockito.verify(imesCenterfactoryRemoteService).pushDataToB2B(any(), eq(empNo));
    }

    /**
     * Test boundary condition - empty customer items (null)
     */
    @Test
    public void uploadStockInfo_emptyCustomerItems_null() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023");
        String empNo = "12345";

        // Mock return null customer items
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(null);

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify only customer items query was called, other methods should not be called
        Mockito.verify(imesCenterfactoryRemoteService).getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo));
        Mockito.verify(barcodeCenterRepository, Mockito.never()).getInventoryHoldWmwhids();
        Mockito.verify(barcodeCenterRepository, Mockito.never()).getBadInventoryHoldWmwhids();
        Mockito.verify(inventoryDiffQueryRepository, Mockito.never()).getStockQtyWithWisId(any());
        Mockito.verify(zteStockInfoUploadRepository, Mockito.never()).insertStockUploadSnapshot(any());
        Mockito.verify(zteStockInfoUploadRepository, Mockito.never()).insertStockUploadLog(any());
        Mockito.verify(imesCenterfactoryRemoteService, Mockito.never()).pushDataToB2B(any(), any());
    }

    /**
     * Test boundary condition - empty customer items list
     */
    @Test
    public void uploadStockInfo_emptyCustomerItems_emptyList() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023");
        String empNo = "12345";

        // Mock return empty collection
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(new ArrayList<>());

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify only customer items query was called
        Mockito.verify(imesCenterfactoryRemoteService).getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo));
        Mockito.verify(barcodeCenterRepository, Mockito.never()).getInventoryHoldWmwhids();
        Mockito.verify(inventoryDiffQueryRepository, Mockito.never()).getStockQtyWithWisId(any());
    }

    /**
     * Test boundary condition - empty inventory data
     */
    @Test
    public void uploadStockInfo_emptyInventoryData() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023");
        String empNo = "12345";

        // Mock customer items
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOList();
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(customerItemsDTOList);

        // Mock warehouse info
        List<String> inventoryHoldWmwhids = Arrays.asList("WMWHSE1");
        List<String> badInventoryHoldWmwhids = Arrays.asList("WMWHSE3");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(inventoryHoldWmwhids);
        PowerMockito.when(barcodeCenterRepository.getBadInventoryHoldWmwhids()).thenReturn(badInventoryHoldWmwhids);

        // Mock return empty inventory data
        PowerMockito.when(inventoryDiffQueryRepository.getStockQtyWithWisId(any(ECLotxlocxidDTO.class)))
                .thenReturn(new ArrayList<>());

        // Mock utility methods
        mockCommonUtilsSplitList();
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(ZTE1), eq(INT_6)))
                .thenReturn("ZTE120240621000001");

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify calls
        Mockito.verify(inventoryDiffQueryRepository, Mockito.times(2)).getStockQtyWithWisId(any(ECLotxlocxidDTO.class));
        // Since inventory data is empty, should not call save snapshot method
        Mockito.verify(zteStockInfoUploadRepository, Mockito.never()).insertStockUploadSnapshot(any());
        // Should still call log and B2B push with empty data
        Mockito.verify(zteStockInfoUploadRepository).insertStockUploadLog(any());
        Mockito.verify(imesCenterfactoryRemoteService).pushDataToB2B(any(), eq(empNo));
    }

    /**
     * Test boundary condition - customer items with null ZTE code
     */
    @Test
    public void uploadStockInfo_withNullZteCode() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023");
        String empNo = "12345";

        // Mock customer items with null ZTE code
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOListWithNullZteCode();
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(customerItemsDTOList);

        // Mock warehouse info
        List<String> inventoryHoldWmwhids = Arrays.asList("WMWHSE1");
        List<String> badInventoryHoldWmwhids = Arrays.asList("WMWHSE3");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(inventoryHoldWmwhids);
        PowerMockito.when(barcodeCenterRepository.getBadInventoryHoldWmwhids()).thenReturn(badInventoryHoldWmwhids);

        // Mock inventory data
        PowerMockito.when(inventoryDiffQueryRepository.getStockQtyWithWisId(any(ECLotxlocxidDTO.class)))
                .thenReturn(new ArrayList<>());

        // Mock utility methods
        mockCommonUtilsSplitList();
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(ZTE1), eq(INT_6)))
                .thenReturn("ZTE120240621000001");

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify calls - should still process but skip items with null ZTE code
        Mockito.verify(imesCenterfactoryRemoteService).getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo));
        Mockito.verify(barcodeCenterRepository).getInventoryHoldWmwhids();
        Mockito.verify(barcodeCenterRepository).getBadInventoryHoldWmwhids();
    }

    /**
     * Test boundary condition - mixed valid and invalid customer items
     */
    @Test
    public void uploadStockInfo_mixedValidInvalidItems() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023", "045020200075");
        String empNo = "12345";

        // Mock customer items with mixed valid/invalid data
        List<ECCustomerItemsDTO> customerItemsDTOList = createMixedCustomerItemsDTOList();
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(customerItemsDTOList);

        // Mock warehouse info
        List<String> inventoryHoldWmwhids = Arrays.asList("WMWHSE1");
        List<String> badInventoryHoldWmwhids = Arrays.asList("WMWHSE3");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(inventoryHoldWmwhids);
        PowerMockito.when(barcodeCenterRepository.getBadInventoryHoldWmwhids()).thenReturn(badInventoryHoldWmwhids);

        // Mock inventory data
        List<ECLotxlocxid> inventoryHoldList = createInventoryHoldList();
        List<ECLotxlocxid> badInventoryHoldList = createBadInventoryHoldList();
        PowerMockito.when(inventoryDiffQueryRepository.getStockQtyWithWisId(any(ECLotxlocxidDTO.class)))
                .thenReturn(inventoryHoldList).thenReturn(badInventoryHoldList);

        // Mock utility methods
        mockCommonUtilsSplitList();
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(ZTE1), eq(INT_6)))
                .thenReturn("ZTE120240621000001");

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify calls - should process valid items and skip invalid ones
        Mockito.verify(imesCenterfactoryRemoteService).getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo));
        Mockito.verify(zteStockInfoUploadRepository, Mockito.times(2)).insertStockUploadSnapshot(any());
        Mockito.verify(zteStockInfoUploadRepository).insertStockUploadLog(any());
        Mockito.verify(imesCenterfactoryRemoteService).pushDataToB2B(any(), eq(empNo));
    }

    /**
     * Test getDataTransferBatchNo method
     */
    @Test
    public void getDataTransferBatchNo() throws Exception {
        // Prepare test data
        String batchNoType = ZTE1;
        String mockSerialNo = "ZTE120240621000001";

        // Mock RedisSerialNoUtil
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(batchNoType), eq(INT_6)))
                .thenReturn(mockSerialNo);

        // Execute test
        String result = ecZteMeiTuanService.getDataTransferBatchNo(batchNoType);

        // Verify result
        String expected = "ZTE120240621" + "2" + "000001";
        Assert.assertEquals(expected, result);
    }

    /**
     * Test saveStockSnapshot method - normal flow
     */
    @Test
    public void saveStockSnapshot_normalFlow() throws Exception {
        // Prepare test data
        List<ECLotxlocxid> ecLotxlocxids = createInventoryHoldList();

        // Mock utility methods
        List<List<ZteStockInfoDTO>> splitResult = Arrays.asList(
                Arrays.asList(createZteStockInfoDTO())
        );
        PowerMockito.when(CommonUtils.splitList(any(List.class), eq(500))).thenReturn(splitResult);

        // Execute test
        Whitebox.invokeMethod(ecZteMeiTuanService, "saveStockSnapshot", ecLotxlocxids);

        // Verify calls
        Mockito.verify(zteStockInfoUploadRepository).insertStockUploadSnapshot(any());
    }

    /**
     * Test saveStockSnapshot method - empty list
     */
    @Test
    public void saveStockSnapshot_emptyList() throws Exception {
        // Prepare test data
        List<ECLotxlocxid> ecLotxlocxids = new ArrayList<>();

        // Execute test
        Whitebox.invokeMethod(ecZteMeiTuanService, "saveStockSnapshot", ecLotxlocxids);

        // Verify save method should not be called
        Mockito.verify(zteStockInfoUploadRepository, Mockito.never()).insertStockUploadSnapshot(any());
    }

    /**
     * Test saveStockSnapshot method - null list
     */
    @Test
    public void saveStockSnapshot_nullList() throws Exception {
        // Execute test
        Whitebox.invokeMethod(ecZteMeiTuanService, "saveStockSnapshot", (Object) null);

        // Verify save method should not be called
        Mockito.verify(zteStockInfoUploadRepository, Mockito.never()).insertStockUploadSnapshot(any());
    }

    /**
     * Test getCustomerStockInfo method - normal flow
     */
    @Test
    public void getCustomerStockInfo_normalFlow() throws Exception {
        // Prepare test data
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOList();
        List<ECLotxlocxid> inventoryHoldList = createInventoryHoldList();
        List<ECLotxlocxid> badInventoryHoldList = createBadInventoryHoldList();

        // Execute test
        List<ECCustomerStockInfoDataDTO> result = Whitebox.invokeMethod(ecZteMeiTuanService,
                "getCustomerStockInfo", customerItemsDTOList, inventoryHoldList, badInventoryHoldList);

        // Verify result
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());

        ECCustomerStockInfoDataDTO firstItem = result.get(0);
        Assert.assertEquals("MT001", firstItem.getManufacturerPartNo());
        Assert.assertEquals(Integer.valueOf(100), firstItem.getQuantity());
        Assert.assertEquals(Integer.valueOf(10), firstItem.getBadQuantity());
    }

    /**
     * Test getCustomerStockInfo method - empty inventory data
     */
    @Test
    public void getCustomerStockInfo_emptyInventory() throws Exception {
        // Prepare test data
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOList();
        List<ECLotxlocxid> inventoryHoldList = new ArrayList<>();
        List<ECLotxlocxid> badInventoryHoldList = new ArrayList<>();

        // Execute test
        List<ECCustomerStockInfoDataDTO> result = Whitebox.invokeMethod(ecZteMeiTuanService,
                "getCustomerStockInfo", customerItemsDTOList, inventoryHoldList, badInventoryHoldList);

        // Verify result
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());

        // Inventory quantity should be 0
        ECCustomerStockInfoDataDTO firstItem = result.get(0);
        Assert.assertEquals(Integer.valueOf(0), firstItem.getQuantity());
        Assert.assertEquals(Integer.valueOf(0), firstItem.getBadQuantity());
    }

    /**
     * Test getCustomerStockInfo method - null inventory lists
     */
    @Test
    public void getCustomerStockInfo_nullInventory() throws Exception {
        // Prepare test data
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOList();

        // Execute test with null inventory lists
        List<ECCustomerStockInfoDataDTO> result = Whitebox.invokeMethod(ecZteMeiTuanService,
                "getCustomerStockInfo", customerItemsDTOList, null, null);

        // Verify result
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());

        // Inventory quantity should be 0 when inventory lists are null
        ECCustomerStockInfoDataDTO firstItem = result.get(0);
        Assert.assertEquals(Integer.valueOf(0), firstItem.getQuantity());
        Assert.assertEquals(Integer.valueOf(0), firstItem.getBadQuantity());
    }

    /**
     * Test getCustomerStockInfo method - with null values in inventory data
     */
    @Test
    public void getCustomerStockInfo_withNullValues() throws Exception {
        // Prepare test data
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOList();
        List<ECLotxlocxid> inventoryHoldList = createInventoryHoldListWithNullValues();
        List<ECLotxlocxid> badInventoryHoldList = new ArrayList<>();

        // Execute test
        List<ECCustomerStockInfoDataDTO> result = Whitebox.invokeMethod(ecZteMeiTuanService,
                "getCustomerStockInfo", customerItemsDTOList, inventoryHoldList, badInventoryHoldList);

        // Verify result - should filter out null values and process valid ones
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
    }

    /**
     * Test getCustomerStockInfo method - customer items with null ZTE code
     */
    @Test
    public void getCustomerStockInfo_customerItemsWithNullZteCode() throws Exception {
        // Prepare test data
        List<ECCustomerItemsDTO> customerItemsDTOList = createCustomerItemsDTOListWithNullZteCode();
        List<ECLotxlocxid> inventoryHoldList = createInventoryHoldList();
        List<ECLotxlocxid> badInventoryHoldList = createBadInventoryHoldList();

        // Execute test
        List<ECCustomerStockInfoDataDTO> result = Whitebox.invokeMethod(ecZteMeiTuanService,
                "getCustomerStockInfo", customerItemsDTOList, inventoryHoldList, badInventoryHoldList);

        // Verify result - should skip items with null ZTE code
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size()); // Only one valid item should be processed

        ECCustomerStockInfoDataDTO validItem = result.get(0);
        Assert.assertEquals("MT002", validItem.getManufacturerPartNo());
    }

    /**
     * Test getCustomerDataLogDTO method
     */
    @Test
    public void getCustomerDataLogDTO() throws Exception {
        // Prepare test data
        String empNo = "12345";
        List<ECCustomerStockInfoDataDTO> tempList = Arrays.asList(createCustomerStockInfoDataDTO());

        // Mock RedisSerialNoUtil
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(ZTE1), eq(INT_6)))
                .thenReturn("ZTE120240621000001");

        // Execute test
        CustomerDataLogDTO result = Whitebox.invokeMethod(ecZteMeiTuanService,
                "getCustomerDataLogDTO", empNo, tempList);

        // Verify result
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getId());
        Assert.assertEquals(INFOR_WMS, result.getOrigin());
        Assert.assertEquals(CUSTOMER_NAME_MEITUAN, result.getCustomerName());
        Assert.assertEquals(STOCK_INFO_ZH, result.getProjectName());
        Assert.assertEquals(ZTE_IMES_MEITUAN_INVENTORY, result.getMessageType());
        Assert.assertEquals("ZTE120240621" + "2" + "000001", result.getTaskNo());
        Assert.assertEquals(empNo, result.getCreateBy());
        Assert.assertEquals(empNo, result.getLastUpdatedBy());
        Assert.assertNotNull(result.getJsonData());
    }

    /**
     * Test exception handling - when remote service throws exception
     */
    @Test(expected = Exception.class)
    public void uploadStockInfo_remoteServiceException() throws Exception {
        // Prepare test data
        List<String> itemNoList = Arrays.asList("047020900023");
        String empNo = "12345";

        // Mock remote service to throw exception
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenThrow(new RuntimeException("Remote service error"));

        // Execute test - should throw exception
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);
    }

    /**
     * Test large data set processing
     */
    @Test
    public void uploadStockInfo_largeDataSet() throws Exception {
        // Prepare test data with large item list
        List<String> itemNoList = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            itemNoList.add("ITEM" + String.format("%06d", i));
        }
        String empNo = "12345";

        // Mock customer items
        List<ECCustomerItemsDTO> customerItemsDTOList = createLargeCustomerItemsDTOList();
        PowerMockito.when(imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo)))
                .thenReturn(customerItemsDTOList);

        // Mock warehouse info
        List<String> inventoryHoldWmwhids = Arrays.asList("WMWHSE1");
        List<String> badInventoryHoldWmwhids = Arrays.asList("WMWHSE3");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(inventoryHoldWmwhids);
        PowerMockito.when(barcodeCenterRepository.getBadInventoryHoldWmwhids()).thenReturn(badInventoryHoldWmwhids);

        // Mock inventory data
        List<ECLotxlocxid> inventoryHoldList = createLargeInventoryHoldList();
        PowerMockito.when(inventoryDiffQueryRepository.getStockQtyWithWisId(any(ECLotxlocxidDTO.class)))
                .thenReturn(inventoryHoldList).thenReturn(new ArrayList<>());

        // Mock utility methods for large data
        mockCommonUtilsSplitListForLargeData();
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(eq(ZTE1), eq(INT_6)))
                .thenReturn("ZTE120240621000001");

        // Execute test
        ecZteMeiTuanService.uploadStockInfo(itemNoList, empNo);

        // Verify calls - should handle large data set properly
        Mockito.verify(imesCenterfactoryRemoteService).getCustomerItemsByCustomerAndMode(
                eq(CUSTOMER_NAME_MEITUAN), eq(COOPERATION_MODE_AVAP), eq(itemNoList), eq(empNo));
        // Should call split methods multiple times for large data
        Mockito.verify(zteStockInfoUploadRepository, Mockito.atLeastOnce()).insertStockUploadSnapshot(any());
        Mockito.verify(zteStockInfoUploadRepository, Mockito.atLeastOnce()).insertStockUploadLog(any());
        Mockito.verify(imesCenterfactoryRemoteService, Mockito.atLeastOnce()).pushDataToB2B(any(), eq(empNo));
    }

    // ==================== Helper Methods ====================

    /**
     * Mock CommonUtils.splitList for normal scenarios
     */
    private void mockCommonUtilsSplitList() {
        List<List<Object>> splitResult = Arrays.asList(
                Arrays.asList((Object) createCustomerStockInfoDataDTO())
        );
        List<List<Object>> splitSnapshotResult = Arrays.asList(
                Arrays.asList((Object) createZteStockInfoDTO())
        );
        List<List<Object>> splitLogResult = Arrays.asList(
                Arrays.asList((Object) createZteStockInfoUploadLogDTO())
        );
        List<List<Object>> splitCustomerResult = Arrays.asList(
                Arrays.asList((Object) createCustomerDataLogDTO())
        );

        PowerMockito.when(CommonUtils.splitList(any(), eq(500)))
                .thenReturn(splitResult, splitSnapshotResult, splitSnapshotResult, splitLogResult, splitCustomerResult);
    }

    /**
     * Mock CommonUtils.splitList for large data scenarios
     */
    private void mockCommonUtilsSplitListForLargeData() {
        // Create multiple batches for large data
        List<List<Object>> splitResult = Arrays.asList(
                Arrays.asList((Object) createCustomerStockInfoDataDTO()),
                Arrays.asList((Object) createCustomerStockInfoDataDTO())
        );
        List<List<Object>> splitSnapshotResult = Arrays.asList(
                Arrays.asList((Object) createZteStockInfoDTO()),
                Arrays.asList((Object) createZteStockInfoDTO())
        );
        List<List<Object>> splitLogResult = Arrays.asList(
                Arrays.asList((Object) createZteStockInfoUploadLogDTO()),
                Arrays.asList((Object) createZteStockInfoUploadLogDTO())
        );
        List<List<Object>> splitCustomerResult = Arrays.asList(
                Arrays.asList((Object) createCustomerDataLogDTO()),
                Arrays.asList((Object) createCustomerDataLogDTO())
        );

        PowerMockito.when(CommonUtils.splitList(any(), eq(500)))
                .thenReturn(splitResult, splitSnapshotResult, splitSnapshotResult, splitLogResult, splitCustomerResult);
    }

    /**
     * Create customer items DTO list
     */
    private List<ECCustomerItemsDTO> createCustomerItemsDTOList() {
        List<ECCustomerItemsDTO> list = new ArrayList<>();

        ECCustomerItemsDTO item1 = new ECCustomerItemsDTO();
        item1.setZteCode("047020900023");
        item1.setCustomerMaterialType("MT001");
        item1.setCustomerSupplier("Supplier1");
        item1.setCustomerItemName("Item Name 1");
        list.add(item1);

        ECCustomerItemsDTO item2 = new ECCustomerItemsDTO();
        item2.setZteCode("045020200075");
        item2.setCustomerMaterialType("MT002");
        item2.setCustomerSupplier("Supplier2");
        item2.setCustomerItemName("Item Name 2");
        list.add(item2);

        return list;
    }

    /**
     * Create customer items DTO list with null ZTE code
     */
    private List<ECCustomerItemsDTO> createCustomerItemsDTOListWithNullZteCode() {
        List<ECCustomerItemsDTO> list = new ArrayList<>();

        ECCustomerItemsDTO item1 = new ECCustomerItemsDTO();
        item1.setZteCode(null); // Set to null
        item1.setCustomerMaterialType("MT001");
        item1.setCustomerSupplier("Supplier1");
        item1.setCustomerItemName("Item Name 1");
        list.add(item1);

        ECCustomerItemsDTO item2 = new ECCustomerItemsDTO();
        item2.setZteCode("045020200075");
        item2.setCustomerMaterialType("MT002");
        item2.setCustomerSupplier("Supplier2");
        item2.setCustomerItemName("Item Name 2");
        list.add(item2);

        return list;
    }

    /**
     * Create mixed customer items DTO list (valid and invalid)
     */
    private List<ECCustomerItemsDTO> createMixedCustomerItemsDTOList() {
        List<ECCustomerItemsDTO> list = new ArrayList<>();

        // Valid item
        ECCustomerItemsDTO item1 = new ECCustomerItemsDTO();
        item1.setZteCode("047020900023");
        item1.setCustomerMaterialType("MT001");
        item1.setCustomerSupplier("Supplier1");
        item1.setCustomerItemName("Item Name 1");
        list.add(item1);

        // Invalid item with null ZTE code
        ECCustomerItemsDTO item2 = new ECCustomerItemsDTO();
        item2.setZteCode(null);
        item2.setCustomerMaterialType("MT002");
        item2.setCustomerSupplier("Supplier2");
        item2.setCustomerItemName("Item Name 2");
        list.add(item2);

        // Valid item
        ECCustomerItemsDTO item3 = new ECCustomerItemsDTO();
        item3.setZteCode("045020200075");
        item3.setCustomerMaterialType("MT003");
        item3.setCustomerSupplier("Supplier3");
        item3.setCustomerItemName("Item Name 3");
        list.add(item3);

        return list;
    }

    /**
     * Create large customer items DTO list for performance testing
     */
    private List<ECCustomerItemsDTO> createLargeCustomerItemsDTOList() {
        List<ECCustomerItemsDTO> list = new ArrayList<>();

        for (int i = 0; i < 1000; i++) {
            ECCustomerItemsDTO item = new ECCustomerItemsDTO();
            item.setZteCode("ITEM" + String.format("%06d", i));
            item.setCustomerMaterialType("MT" + String.format("%03d", i % 100));
            item.setCustomerSupplier("Supplier" + (i % 10));
            item.setCustomerItemName("Item Name " + i);
            list.add(item);
        }

        return list;
    }

    /**
     * Create available inventory list
     */
    private List<ECLotxlocxid> createInventoryHoldList() {
        List<ECLotxlocxid> list = new ArrayList<>();

        ECLotxlocxid item1 = new ECLotxlocxid();
        item1.setWisId("WMWHSE1");
        item1.setSku("047020900023");
        item1.setQty(100);
        list.add(item1);

        ECLotxlocxid item2 = new ECLotxlocxid();
        item2.setWisId("WMWHSE2");
        item2.setSku("045020200075");
        item2.setQty(50);
        list.add(item2);

        return list;
    }

    /**
     * Create bad inventory list
     */
    private List<ECLotxlocxid> createBadInventoryHoldList() {
        List<ECLotxlocxid> list = new ArrayList<>();

        ECLotxlocxid item1 = new ECLotxlocxid();
        item1.setWisId("WMWHSE3");
        item1.setSku("047020900023");
        item1.setQty(10);
        list.add(item1);

        return list;
    }

    /**
     * Create inventory list with null values for testing filter logic
     */
    private List<ECLotxlocxid> createInventoryHoldListWithNullValues() {
        List<ECLotxlocxid> list = new ArrayList<>();

        ECLotxlocxid item1 = new ECLotxlocxid();
        item1.setWisId("WMWHSE1");
        item1.setSku(null); // null sku
        item1.setQty(100);
        list.add(item1);

        ECLotxlocxid item2 = new ECLotxlocxid();
        item2.setWisId("WMWHSE2");
        item2.setSku("045020200075");
        item2.setQty(null); // null qty
        list.add(item2);

        ECLotxlocxid item3 = new ECLotxlocxid();
        item3.setWisId("WMWHSE2");
        item3.setSku("047020900023");
        item3.setQty(30);
        list.add(item3);

        return list;
    }

    /**
     * Create large inventory list for performance testing
     */
    private List<ECLotxlocxid> createLargeInventoryHoldList() {
        List<ECLotxlocxid> list = new ArrayList<>();

        for (int i = 0; i < 1000; i++) {
            ECLotxlocxid item = new ECLotxlocxid();
            item.setWisId("WMWHSE" + (i % 5 + 1));
            item.setSku("ITEM" + String.format("%06d", i));
            item.setQty(100 + i % 500);
            list.add(item);
        }

        return list;
    }

    /**
     * Create customer stock info data DTO
     */
    private ECCustomerStockInfoDataDTO createCustomerStockInfoDataDTO() {
        ECCustomerStockInfoDataDTO dto = new ECCustomerStockInfoDataDTO();
        dto.setDate("2024-06-21");
        dto.setManufacturerPartNo("MT001");
        dto.setManufacturer("Supplier1");
        dto.setDescription("Item Name 1");
        dto.setQuantity(100);
        dto.setBadQuantity(10);
        return dto;
    }

    /**
     * Create ZTE stock info DTO
     */
    private ZteStockInfoDTO createZteStockInfoDTO() {
        ZteStockInfoDTO dto = new ZteStockInfoDTO();
        dto.setItemNo("047020900023");
        dto.setStockNo("WMWHSE1");
        dto.setStockQuantity(100);
        return dto;
    }

    /**
     * Create ZTE stock info upload log DTO
     */
    private ZteStockInfoUploadLogDTO createZteStockInfoUploadLogDTO() {
        ZteStockInfoUploadLogDTO dto = new ZteStockInfoUploadLogDTO();
        dto.setId(UUID.randomUUID().toString());
        dto.setOrigin(INFOR_WMS);
        dto.setCustomerName(CUSTOMER_NAME_MEITUAN);
        dto.setProjectName(STOCK_INFO_ZH);
        dto.setMessageType(ZTE_IMES_MEITUAN_INVENTORY);
        dto.setTaskNo("ZTE1202406212000001");
        dto.setFactoryId(51);
        dto.setCreateBy("12345");
        dto.setLastUpdatedBy("12345");
        return dto;
    }

    /**
     * Create customer data log DTO
     */
    private CustomerDataLogDTO createCustomerDataLogDTO() {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dto.setId(UUID.randomUUID().toString());
        dto.setOrigin(INFOR_WMS);
        dto.setCustomerName(CUSTOMER_NAME_MEITUAN);
        dto.setProjectName(STOCK_INFO_ZH);
        dto.setProjectPhase("");
        dto.setCooperationMode("");
        dto.setMessageType(ZTE_IMES_MEITUAN_INVENTORY);
        dto.setContractNo("");
        dto.setTaskNo("ZTE1202406212000001");
        dto.setItemNo("");
        dto.setSn("");
        dto.setJsonData("{\"data\":[]}");
        dto.setFactoryId(51);
        dto.setCreateBy("12345");
        dto.setLastUpdatedBy("12345");
        return dto;
    }
}
