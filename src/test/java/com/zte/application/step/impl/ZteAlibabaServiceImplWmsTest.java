package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.*;
import com.zte.interfaces.infor.dto.AliOrderSubmitDetailDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.remoteservice.MicroServiceNameEum;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

// 导入常量
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;

/**
 * ZteAlibabaServiceImpl WMS和仓储中心集成方法单元测试
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class, RemoteServiceDataUtil.class, HttpClientUtil.class, 
                 JacksonJsonConverUtil.class, JsonUtil.class, StringUtils.class})
public class ZteAlibabaServiceImplWmsTest {

    @InjectMocks
    private ZteAlibabaServiceImpl zteAlibabaService;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;

    @Mock
    private ZteStockMoveInfoUploadRepository zteStockMoveInfoUploadRepository;

    @Mock
    private InforIwmsIscpRepository inforIwmsIscpRepository;

    @Mock
    private ZteSnBoundPkgIdRepository zteSnBoundPkgIdRepository;

    private List<IscpEdiLog> iscpEdiLogs;
    private List<AliOrderSubmitDetailDTO> orderSubmitDetails;
    private List<SysLookupValuesDTO> lookupValues;
    private Map<String, String> configMap;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(BusiAssertException.class, RemoteServiceDataUtil.class, 
                               HttpClientUtil.class, JsonUtil.class, StringUtils.class);
        
        // 初始化测试数据
        initTestData();
    }

    private void initTestData() {
        // 初始化IscpEdiLog测试数据
        iscpEdiLogs = new ArrayList<>();
        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        iscpEdiLog.setExternkey("TEST_BILL_001");
        iscpEdiLog.setIsSend(1);
        iscpEdiLogs.add(iscpEdiLog);
        PowerMockito.mockStatic(HttpClientUtil.class);

        // 初始化AliOrderSubmitDetailDTO测试数据
        orderSubmitDetails = new ArrayList<>();
        AliOrderSubmitDetailDTO detail = new AliOrderSubmitDetailDTO();
        detail.setItemCode("ITEM001");
        detail.setQty(10);
        orderSubmitDetails.add(detail);

        // 初始化SysLookupValuesDTO测试数据
        lookupValues = new ArrayList<>();
        SysLookupValuesDTO warehouseConfig = new SysLookupValuesDTO();
        warehouseConfig.setLookupCode("SUBMIT_WAREHOUSE_ID");
        warehouseConfig.setLookupMeaning("1945025616638590978");
        lookupValues.add(warehouseConfig);

        SysLookupValuesDTO stockConfig = new SysLookupValuesDTO();
        stockConfig.setLookupCode("SUBMIT_STOCK_ID");
        stockConfig.setLookupMeaning("1945030203139375106");
        lookupValues.add(stockConfig);

        SysLookupValuesDTO locationConfig = new SysLookupValuesDTO();
        locationConfig.setLookupCode("SUBMIT_LOCATION_ID");
        locationConfig.setLookupMeaning("1945030380566822913");
        lookupValues.add(locationConfig);

        // 初始化配置Map
        configMap = new HashMap<>();
        configMap.put("SUBMIT_WAREHOUSE_ID", "1945025616638590978");
        configMap.put("SUBMIT_STOCK_ID", "1945030203139375106");
        configMap.put("SUBMIT_LOCATION_ID", "1945030380566822913");
    }

    @Test
    public void testExecuteTransferInfoToWmsJob_WithValidData() throws Exception {
        // 准备测试数据
        String userId = "10267738";
        String xAuthValue = "test_auth_value";

        // Mock repository方法
        when(inventoryholdRecordRepository.getLookupValues(any(SysLookupValuesDTO.class)))
                .thenReturn(lookupValues);
        when(zteStockMoveInfoUploadRepository.getSubmitMaterialInfo(any(IscpEdiLog.class)))
                .thenReturn(orderSubmitDetails);
        when(inforIwmsIscpRepository.updateIscpEdiLogTimes(any(IscpEdiLog.class)))
                .thenReturn(1);

        // 执行测试方法
        assertThrows(Exception.class, () -> ReflectionTestUtils.invokeMethod(zteAlibabaService, "executeTransferInfoToWmsJob",
                             iscpEdiLogs, userId, xAuthValue));

        // 验证调用
        verify(inventoryholdRecordRepository, times(1)).getLookupValues(any(SysLookupValuesDTO.class));
        verify(zteStockMoveInfoUploadRepository, times(1)).getSubmitMaterialInfo(any(IscpEdiLog.class));
    }

    @Test
    public void testExecuteTransferInfoToWmsJob_WithEmptyLookupValues() throws Exception {
        // Mock空配置
        when(inventoryholdRecordRepository.getLookupValues(any(SysLookupValuesDTO.class)))
                .thenReturn(new ArrayList<>());

        // 执行测试方法，应该抛出异常
        try {
            ReflectionTestUtils.invokeMethod(zteAlibabaService, "executeTransferInfoToWmsJob", 
                                 iscpEdiLogs, "userId", "xAuthValue");
            fail("应该抛出异常");
        } catch (Exception e) {
            // 验证异常
            assertTrue(true);
        }
    }

    @Test
    public void testExecuteTransferInfoToWmsJob_WithEmptyExternkeyList() throws Exception {
        // Mock空的外部单据列表
        List list  = new ArrayList();
        when(zteSnBoundPkgIdRepository.getAlibabaExternkey(any()))
                .thenReturn(list);

        when(inventoryholdRecordRepository.getLookupValues(any(SysLookupValuesDTO.class)))
                .thenReturn(lookupValues);
        // 执行测试方法
        zteAlibabaService.executeTransferInfoToWmsJob("userId", "xAuthValue");
        list.add(new IscpEdiLog());
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);

        ServiceData<StoragePageRow<ZteSnInventoryQueryDTO>> serviceData = new ServiceData<>();
        Mockito.when(RemoteServiceDataUtil.invokeService(anyString(),
                anyString(), anyString(), anyString(), any(), any())).thenReturn(serviceData);
        serviceData.setBo(new StoragePageRow<>());
        zteAlibabaService.executeTransferInfoToWmsJob("userId", "xAuthValue");

        assertNotNull(serviceData);
    }

    @Test
    public void testSubmitToStorageCenter_Success() throws Exception {
        // 准备测试数据
        ReqHeadAndDetailParamsDTO request = new ReqHeadAndDetailParamsDTO();
        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        String xEmpNo = "10267738";
        String xAuthValue = "test_auth_value";

        // Mock成功响应
        ServiceData<StoragePageRow<?>> successResponse = new ServiceData<>();
        RetCode successCode = new RetCode();
        successCode.setCode("0000");
        successCode.setMsg("Success");
        successResponse.setCode(successCode);
        successResponse.setBo(new StoragePageRow<>());

        when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), 
                                               anyString(), anyString(), anyMap()))
                .thenReturn(successResponse);

        // 执行测试方法
        assertThrows(Exception.class, () -> ReflectionTestUtils.invokeMethod(zteAlibabaService, "submitToStorageCenter",
                                             request, iscpEdiLog, xEmpNo, xAuthValue));


    }

    @Test
    public void testSubmitToStorageCenter_Failure() throws Exception {
        // 准备测试数据
        ReqHeadAndDetailParamsDTO request = new ReqHeadAndDetailParamsDTO();
        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        String xEmpNo = "10267738";
        String xAuthValue = "test_auth_value";

        // Mock失败响应
        ServiceData<?> failureResponse = new ServiceData<>();
        RetCode failureCode = new RetCode();
        failureCode.setCode("9999");
        failureCode.setMsg("Failure");
        failureResponse.setCode(failureCode);

        when(RemoteServiceDataUtil.invokeService(any(), any(), any(),
                any(), any(), any()))
                .thenReturn(failureResponse);

        // 执行测试方法
        zteAlibabaService.submitToStorageCenter(request, iscpEdiLog, xEmpNo, xAuthValue);
        failureCode.setCode("0000");
        boolean b = zteAlibabaService.submitToStorageCenter(request, iscpEdiLog, xEmpNo, xAuthValue);
        assertTrue(b);
    }

    @Test
    public void testSubmitToStorageCenter_Exception() throws Exception {
        // 准备测试数据
        ReqHeadAndDetailParamsDTO request = new ReqHeadAndDetailParamsDTO();
        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        String xEmpNo = "10267738";
        String xAuthValue = "test_auth_value";

        // Mock异常
        when(RemoteServiceDataUtil.invokeService(any(), any(), any(),
                any(), any(), any()))
                .thenThrow(new RuntimeException("Service unavailable"));

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(zteAlibabaService, "submitToStorageCenter",
                                             request, iscpEdiLog, xEmpNo, xAuthValue);
        assertNotNull(request);
    }

    @Test
    public void testQueryInventoryFromStorageCenter() throws Exception {
        // 准备测试数据
        ZteDeductionPlanParamDTO requestDto = new ZteDeductionPlanParamDTO();
        requestDto.setEmpNo("10267738");

        // Mock查询结果
        List<ZteSnInventoryQueryDTO> type0Results = new ArrayList<>();
        ZteSnInventoryQueryDTO type0Result = new ZteSnInventoryQueryDTO();
        type0Result.setInventoryType("0");
        type0Results.add(type0Result);

        ZteSnInventoryQueryDTO type5Result = new ZteSnInventoryQueryDTO();
        type5Result.setInventoryType("5");

        PowerMockito.mockStatic(RemoteServiceDataUtil.class);

        ServiceData<StoragePageRow<ZteSnInventoryQueryDTO>> serviceData = new ServiceData<>();
        Mockito.when(RemoteServiceDataUtil.invokeService(any(),
                any(), any(), any(), any(), any())).thenReturn(serviceData);
        serviceData.setBo(new StoragePageRow<>());
        serviceData.getBo().setRows(new ArrayList<>());
        zteAlibabaService.queryInventoryFromStorageCenter("userId");
        serviceData.getBo().setRows(null);
        // 执行测试方法
        zteAlibabaService.queryInventoryFromStorageCenter("userId");
        assertNotNull(serviceData);
    }

    @Test
    public void testQueryAllPageStorageInventoryByType() throws Exception {
        // 准备测试数据
        InventoryQueryReqDTO query = InventoryQueryReqDTO.builder()
                .inventoryType("0")
                .pageIndex(1)
                .pageSize(500)
                .build();
        String xEmpNo = "10267738";

        // Mock分页结果
        StoragePageRow<ZteSnInventoryQueryDTO> pageRows1 = new StoragePageRow<>();
        pageRows1.setTotal(1000L);
        pageRows1.setRows(new ArrayList<>());

        StoragePageRow<ZteSnInventoryQueryDTO> pageRows2 = new StoragePageRow<>();
        pageRows2.setTotal(1000L);
        pageRows2.setRows(new ArrayList<>());

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(zteAlibabaService,
                                                                   "queryAllPageStorageInventoryByType", 
                                                                   query, xEmpNo);
        assertNotNull(pageRows2);
    }

    @Test
    public void testQueryStorageInventoryByType() throws Exception {
        // 准备测试数据
        InventoryQueryReqDTO request = InventoryQueryReqDTO.builder()
                .inventoryType("0")
                .pageIndex(1)
                .pageSize(500)
                .build();
        String xEmpNo = "10267738";

        // Mock成功响应
        ServiceData<StoragePageRow> successResponse = new ServiceData<>();
        RetCode successCode = new RetCode();
        successCode.setCode("0000");
        successCode.setMsg("Success");
        successResponse.setCode(successCode);

        StoragePageRow<ZteSnInventoryQueryDTO> mockPageRows = new StoragePageRow<>();
        mockPageRows.setTotal(100L);
        mockPageRows.setRows(new ArrayList<>());
        successResponse.setBo(mockPageRows);

        when(RemoteServiceDataUtil.invokeService(anyString(), anyString(), anyString(), 
                                               anyString(), anyString(), anyMap()))
                .thenReturn(successResponse);

        // 执行测试方法
         zteAlibabaService.queryStorageInventoryByType(request, xEmpNo);
         assertNotNull(request);
    }

    @Test
    public void testSendRequisitionDetail_Success() throws Exception {
        // 准备测试数据
        List<RequisitionDetailSendDTO> reqInfoList = new ArrayList<>();
        RequisitionDetailSendDTO reqInfo = new RequisitionDetailSendDTO();
        reqInfoList.add(reqInfo);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        String xEmpNo = "10267738";

        // Mock成功响应
        String successResponse = "{\"code\":{\"code\":\"0000\",\"msg\":\"Success\"}}";
        when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), anyMap()))
                .thenReturn(successResponse);

        // Mock JsonNode解析
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        JsonNode mockJsonNode = Mockito.mock(JsonNode.class);
        JsonNode mockCodeNode = Mockito.mock(JsonNode.class);
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(null);
        when(mockJsonNode.get("code")).thenReturn(mockCodeNode);
        when(mockCodeNode.get("code")).thenReturn(mockCodeNode);
        when(mockCodeNode.asText()).thenReturn("0000");

        // 执行测试方法
         ReflectionTestUtils.invokeMethod(zteAlibabaService, "sendRequisitionDetail",
                                             reqInfoList, iscpEdiLog, xEmpNo);
        assertNotNull(reqInfoList);
    }

    @Test
    public void testSendRequisitionDetail_HttpException() throws Exception {
        // 准备测试数据
        List<RequisitionDetailSendDTO> reqInfoList = new ArrayList<>();
        RequisitionDetailSendDTO reqInfo = new RequisitionDetailSendDTO();
        reqInfoList.add(reqInfo);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        String xEmpNo = "10267738";

        // Mock HTTP异常
        when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), anyMap()))
                .thenThrow(new RuntimeException("HTTP connection failed"));

        // 执行测试方法
        boolean result = ReflectionTestUtils.invokeMethod(zteAlibabaService, "sendRequisitionDetail", 
                                             reqInfoList, iscpEdiLog, xEmpNo);

        // 验证结果
        assertFalse(result);
        assertEquals(3, iscpEdiLog.getIsSend());
    }

    @Test
    public void testSendRequisitionDetail_InvalidResponse() throws Exception {
        // 准备测试数据
        List<RequisitionDetailSendDTO> reqInfoList = new ArrayList<>();
        RequisitionDetailSendDTO reqInfo = new RequisitionDetailSendDTO();
        reqInfoList.add(reqInfo);

        IscpEdiLog iscpEdiLog = new IscpEdiLog();
        String xEmpNo = "10267738";

        // Mock无效响应
        String invalidResponse = "{\"code\":{\"code\":\"9999\",\"msg\":\"Failure\"}}";
        when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(invalidResponse);

        // Mock JsonNode解析
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        JsonNode mockJsonNode = Mockito.mock(JsonNode.class);
        JsonNode mockCodeNode = Mockito.mock(JsonNode.class);
        when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(null);
        when(mockJsonNode.get("code")).thenReturn(mockCodeNode);
        when(mockCodeNode.get("code")).thenReturn(mockCodeNode);
        when(mockCodeNode.asText()).thenReturn("9999");

        // 执行测试方法
        ReflectionTestUtils.invokeMethod(zteAlibabaService, "sendRequisitionDetail",
                                             reqInfoList, iscpEdiLog, xEmpNo);

        assertNotNull(reqInfoList);
    }

    @Test
    public void queryStorageInventoryByType() {
        InventoryQueryReqDTO query = new InventoryQueryReqDTO();
        PowerMockito.mockStatic(RemoteServiceDataUtil.class);

        ServiceData<StoragePageRow<ZteSnInventoryQueryDTO>> serviceData = new ServiceData<>();
        Mockito.when(RemoteServiceDataUtil.invokeService(anyString(),
                anyString(), anyString(), anyString(), any(), any())).thenReturn(serviceData);
        serviceData.setBo(new StoragePageRow<>());
        assertNotNull(zteAlibabaService.queryStorageInventoryByType(query, ""));
    }

    @Test
    public void queryAllPageStorageInventoryByType() {
        ServiceData<StoragePageRow<ZteSnInventoryQueryDTO>> objData = new ServiceData<>();
        objData.setBo(new StoragePageRow<>());
        objData.getBo().setRows(new ArrayList<>());
        objData.getBo().setTotal(1500L);
        InventoryQueryReqDTO reqDTO = new InventoryQueryReqDTO();
        reqDTO.setPageIndex(1);
        reqDTO.setPageSize(500);
        Mockito.when(RemoteServiceDataUtil.invokeService(any(),
                any(), any(), any(), any(), any())).then(o ->{
             objData.getBo().setCurrent(objData.getBo().getCurrent() +1);
            return objData;
        });

        zteAlibabaService.queryAllPageStorageInventoryByType(reqDTO, "");
        assertNotNull(objData);
    }
}