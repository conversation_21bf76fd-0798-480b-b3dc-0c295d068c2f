package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.infor.impl.InventoryholdRecordServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.domain.model.infor.InventoryholdRecord;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.step.ApprovalProcessInfoRepository;
import com.zte.domain.model.step.StepIscpRepository;
import com.zte.interfaces.infor.dto.HoldFlowStartDTO;
import com.zte.interfaces.infor.dto.InventoryholdDTO;
import com.zte.interfaces.infor.dto.InventoryholdRecordDTO;
import com.zte.interfaces.infor.dto.SysLookupValuesDTO;
import com.zte.interfaces.infor.vo.InventoryholdRecordEmailVO;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,Executor.class,ExcelUtil.class,EmailUtil.class, FileUtil.class,
        SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class,JSON.class,Tools.class, ApprovalFlowClient.class, HttpClientUtil.class})
public class InventoryholdRecordServiceImplTest {

    @InjectMocks
    private InventoryholdRecordServiceImpl inventoryholdRecordService;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private InforBarcodeCenterRepository barcodeCenterRepository;
    @Mock
    private StepIscpRepository stepIscpRepository;

    @Mock
    private EmailUtil emailUtil;
    @Mock
    private ApprovalProcessInfoRepository approvalProcessInfoRepository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    private ThreadPoolExecutor thplExecutor = new ThreadPoolExecutor(10,10,
            10, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,EmailUtil.class,FileUtil.class,
                SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class, ApprovalFlowClient.class, HttpClientUtil.class);
        PowerMockito.field(InventoryholdRecordServiceImpl.class, "thplExecutor")
                .set(inventoryholdRecordService, thplExecutor);


    }
    @Test
    public void synInforInventoryholdRecord() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        List<InventoryholdDTO> list = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235");
            inventoryholdDTO.setHoldReason("SAMPLEHOLD");
            list.add(inventoryholdDTO);
        }
        int num = 0;
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list);
        PowerMockito.when(inventoryholdRecordRepository.insertInventoryholdRecordLog(Mockito.any())).thenReturn(num);

        List<String> wms = new ArrayList<>();
        PowerMockito.when( barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        Whitebox.invokeMethod(inventoryholdRecordService, "synInforInventoryholdRecord", list);

        wms.add("WMWHSE1");
        PowerMockito.when( barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        PowerMockito.when(CommonUtils.splitList(Mockito.any())).thenReturn(null);
        num = 1;
        PowerMockito.when( inventoryholdRecordRepository.insertInventoryholdRecord(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "synInforInventoryholdRecord", list);

        List<InventoryholdDTO> list1 = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235");
            inventoryholdDTO.setHoldReason("SPOTCHECK");
            list1.add(inventoryholdDTO);
        }
        PowerMockito.when( barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        PowerMockito.when(CommonUtils.splitList(Mockito.any())).thenReturn(null);
        PowerMockito.when( inventoryholdRecordRepository.insertInventoryholdRecord(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "synInforInventoryholdRecord", list1);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void checkUnfreezeInventoryhold() throws Exception {
        try {
            Whitebox.invokeMethod(inventoryholdRecordService, "checkUnfreezeInventoryhold", null);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        InventoryholdDTO dto = new InventoryholdDTO();
        dto.setItemNo("234235235");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkUnfreezeInventoryhold", dto);

        dto.setItembarcode("234235235");
        try {
            inventoryholdRecordService.checkUnfreezeInventoryhold(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_DATA_CAN_NOT_NULL, e.getMessage());
        }

        dto.setHoldReason("SPOTCHECK");
        try {
            inventoryholdRecordService.checkUnfreezeInventoryhold(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_DATA_CAN_NOT_NULL, e.getMessage());
        }

        dto.setApplyByName("234235235");
        try {
            inventoryholdRecordService.checkUnfreezeInventoryhold(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_DATA_CAN_NOT_NULL, e.getMessage());
        }

        dto.setApplyByNo("234235235");
        try {
            inventoryholdRecordService.checkUnfreezeInventoryhold(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_DATA_CAN_NOT_NULL, e.getMessage());
        }

        dto.setApplyDeptName("234235235");
        try {
            inventoryholdRecordService.checkUnfreezeInventoryhold(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INPUT_DATA_CAN_NOT_NULL, e.getMessage());
        }

        dto.setApplyDeptNo("234235235");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkUnfreezeInventoryhold", dto);

        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "checkUnfreezeInventoryhold", dto);
        PowerMockito.doReturn(0).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "checkUnfreezeInventoryhold", dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void unfreezeInventoryhold() throws Exception {
        InventoryholdDTO dto = new InventoryholdDTO();
        dto.setItembarcode("234235235");
        dto.setHoldReason("SPOTCHECK");
        try {
            Whitebox.invokeMethod(inventoryholdRecordService, "unfreezeInventoryhold", dto);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("SPOTCHECK");
        inventoryholdDTOTest.setApplyByName("234235235");
        inventoryholdDTOTest.setApplyByNo("234235235");
        inventoryholdDTOTest.setApplyDeptName("234235235");
        inventoryholdDTOTest.setApplyDeptNo("234235235");
        try {
            Whitebox.invokeMethod(inventoryholdRecordService, "unfreezeInventoryhold", inventoryholdDTOTest);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void checkInforInventoryholdRecord() throws Exception {

        List<InventoryholdDTO> list = new ArrayList<>();
        try {
            Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", null);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }

        List<InventoryholdDTO> list1 = new ArrayList<>();
        for (int i=0; i<51; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235"+i);
            list1.add(inventoryholdDTO);
        }
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list1);

        List<InventoryholdDTO> list2 = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235");
            inventoryholdDTO.setHoldReason("FUJ1");
            list2.add(inventoryholdDTO);
        }
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list2);

        List<InventoryholdDTO> list4 = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235");
            inventoryholdDTO.setHoldReason("FUJ1");
            inventoryholdDTO.setHoldReasonDsc("测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试11");
            list4.add(inventoryholdDTO);
        }
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list4);
        /* Started by AICoder, pid:782d467ab3ba4a57aaca6835b420a88a */
        List<InventoryholdDTO> list8 = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235");
            inventoryholdDTO.setHoldReason("FUJ1");
            inventoryholdDTO.setHoldReasonDsc("测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试11");
            list8.add(inventoryholdDTO);
        }
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list8);
        /* Ended by AICoder, pid:782d467ab3ba4a57aaca6835b420a88a */

        List<InventoryholdDTO> list3 = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("234235235");
            inventoryholdDTO.setHoldReason("SAMPLEHOLD");
            list3.add(inventoryholdDTO);
        }
        int num =1;
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list3);
        num = 0;
        PowerMockito.doReturn(0).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list3);

        List<InventoryholdDTO> list5 = new ArrayList<>();
        for (int i=0; i<10; i++) {
            InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
            inventoryholdDTO.setItembarcode("2344");
            inventoryholdDTO.setHoldReason("SPOTCHECK");
            list5.add(inventoryholdDTO);
        }
        PowerMockito.doReturn(num).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "checkInforInventoryholdRecord", list3);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void deleteInventoryholdRecord() throws Exception {

        List<InventoryholdDTO> list = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = InventoryholdRecordDTO.builder().build().setHoldReason(SAMPLE_HOLD);
        Whitebox.invokeMethod(inventoryholdRecordService, "deleteInventoryholdRecord", list);

        InventoryholdDTO inventoryholdDTO = new InventoryholdDTO();
        inventoryholdDTO.setItemNo("234235235");
        list.add(inventoryholdDTO);
        Whitebox.invokeMethod(inventoryholdRecordService, "deleteInventoryholdRecord", list);

        InventoryholdDTO inventoryholdDTO2 = new InventoryholdDTO();
        inventoryholdDTO2.setItembarcode("234235235");
        list.add(inventoryholdDTO2);
        int num =1;
        PowerMockito.when(inventoryholdRecordRepository.deleteInventoryholdRecord(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "deleteInventoryholdRecord", list);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);

    }

    @Test
    public void getInventoryholdCodeList() throws Exception {

        SysLookupValuesDTO dto = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000037);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(inventoryholdRecordService, "getInventoryholdCodeList", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void getInventoryholdRecordList() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        int num =1;
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordListVOTotal(Mockito.any())).thenReturn(num);
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordList(Mockito.any())).thenReturn(inventoryholdRecordDTOList);
        Whitebox.invokeMethod(inventoryholdRecordService, "getInventoryholdRecordList", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void getItemNoByItemBarcode() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        PowerMockito.when(inventoryholdRecordRepository.getItemNoByItemBarcode(Mockito.any())).thenReturn(inventoryholdRecordDTO);
        InventoryholdRecordDTO inventoryholdRecordDTO1 = new InventoryholdRecordDTO();
        inventoryholdRecordDTO1.setItemNo("352362");
        PowerMockito.when(stepIscpRepository.getItemNoByBomId(Mockito.any())).thenReturn(inventoryholdRecordDTO1);
        Whitebox.invokeMethod(inventoryholdRecordService, "getItemNoByItemBarcode", dto);

        inventoryholdRecordDTO.setItemNo("3423532");
        inventoryholdRecordDTO.setSupplyNo("111");
        PowerMockito.when(inventoryholdRecordRepository.getItemNoByItemBarcode(Mockito.any())).thenReturn(inventoryholdRecordDTO);
        PowerMockito.when(stepIscpRepository.getItemNoByItemId(Mockito.any())).thenReturn(inventoryholdRecordDTO1);
        Whitebox.invokeMethod(inventoryholdRecordService, "getItemNoByItemBarcode", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void saveInventoryholdRecord() throws Exception {
        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setItemBarcode("35262");
        dto.setHoldReason("FUJ1");
        dto.setHoldStatus(BigDecimal.ONE);
        dto.setEditwho("yhy00326075025");
        dto.setEditDeptNo("yhy00326075025");
        dto.setInforFailTimes(BigDecimal.ZERO);

        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        InventoryholdRecordDTO inventoryholdRecordDTO1 = new InventoryholdRecordDTO();
        inventoryholdRecordDTO1.setItemBarcode("35262");
        inventoryholdRecordDTO1.setHoldReason("FUJ1");
        inventoryholdRecordDTO1.setHoldStatus(BigDecimal.ONE);
        inventoryholdRecordDTO1.setEditwho("yhy");
        inventoryholdRecordDTO1.setInforFailTimes(BigDecimal.ZERO);
        inventoryholdRecordDTO1.setWhseid("WMWHSE1");
        inventoryholdRecordDTO1.setSerialkey(BigDecimal.ONE);

        dto.setSerialkey(BigDecimal.ONE);
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);

        dto.setSerialkey(null);
        dto.setWhseid("WMWHSE1");
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).insertInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);

        dto.setSerialkey(null);
        dto.setWhseid(null);
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);

        dto.setSerialkey(null);
        dto.setWhseid(null);
        PowerMockito.doReturn(0).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        List<String> wms = new ArrayList<>();
        wms.add("WMWHSE1");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).insertInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setItemBarcode("2343252");
        inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);
        List<SysLookupValuesDTO> sysLookupValuesDTOList1 = new ArrayList<>();
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysDto = SysLookupValuesDTO.builder().build().setLookupMeaning("GELI");
        sysLookupValuesDTOList.add(sysDto);
        PowerMockito.doReturn(sysLookupValuesDTOList).when(inventoryholdRecordRepository).getIsNeedApproved(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);
        dto.setApprovedByNo("aaa");
        PowerMockito.doReturn(sysLookupValuesDTOList).when(inventoryholdRecordRepository).getIsNeedApproved(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);
        PowerMockito.doReturn(sysLookupValuesDTOList1).when(inventoryholdRecordRepository).getIsNeedApproved(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "saveInventoryholdRecord", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void inventoryholdRecordSendMail() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class,ExcelUtil.class);
        String empNo="10346719";
        List<SysLookupValuesDTO> sysLookupValuesDTOList =new ArrayList<>();
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        SysLookupValuesDTO dto=new SysLookupValuesDTO();
        dto.setLookupType(LOOKUP_TYPE_1000038);
        SysLookupValuesDTO dto1=new SysLookupValuesDTO();
        dto1.setLookupType(LOOKUP_TYPE_1000045);
        sysLookupValuesDTOList.add(dto1);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.doReturn(0).when(inventoryholdRecordRepository).getInventoryholdRecordSendMailTotal(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        String expectedFileKey = "fileKey";
        dto.setAttribute1(IWMS_DB_QUALITY_HOLD_HEAD);
        sysLookupValuesDTOList.add(dto);

        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).getInventoryholdRecordSendMailTotal(Mockito.any());
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        List<SysLookupValuesDTO> sysLookupValuesDTOList1 =new ArrayList<>();
        SysLookupValuesDTO dto10=new SysLookupValuesDTO();
        dto10.setLookupType(LOOKUP_TYPE_1000038);
        sysLookupValuesDTOList1.add(dto10);
        PowerMockito.doReturn(sysLookupValuesDTOList1).when(inventoryholdRecordRepository).getLookupValues(Mockito.any());
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).getInventoryholdRecordSendMailTotal(Mockito.any());
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        SysLookupValuesDTO dto11=new SysLookupValuesDTO();
        List<String> stringList=new ArrayList<>();
        stringList.add(LOOKUP_TYPE_1000038);
        stringList.add(LOOKUP_TYPE_1000045);
        dto11.setLookupTypeList(stringList);
        PowerMockito.doReturn(sysLookupValuesDTOList).when(inventoryholdRecordRepository).getLookupValues(Mockito.any());
        PowerMockito.doReturn(0).when(inventoryholdRecordRepository).getInventoryholdRecordSendMailTotal(Mockito.any());
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        SysLookupValuesDTO dtos=new SysLookupValuesDTO();
        dtos.setLookupType(LOOKUP_TYPE_1000038);
        dtos.setAttribute1(IWMS_QUALITY_HOLD_HEAD);
        sysLookupValuesDTOList.add(dtos);
        PowerMockito.doReturn(1).when(inventoryholdRecordRepository).getInventoryholdRecordSendMailTotal(Mockito.any());
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        SysLookupValuesDTO dto2=new SysLookupValuesDTO();
        dto2.setLookupType(LOOKUP_TYPE_1000038);
        dto2.setAttribute1(IWMS_DB_INVENTORY_HOLD_FAILED_HEAD);
        sysLookupValuesDTOList.add(dto2);
        List<String> list=new ArrayList<>();
        list.add("10346719");
        PowerMockito.doReturn(list).when(inventoryholdRecordRepository).getInventoryholdRecordSendApplyByNo(Mockito.any());
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        SysLookupValuesDTO dto3=new SysLookupValuesDTO();
        dto3.setLookupType(LOOKUP_TYPE_1000038);
        dto3.setAttribute1(IWMS_CL_INVENTORY_HOLD_FAILED_HEAD);
        sysLookupValuesDTOList.add(dto3);
        PowerMockito.doReturn(list).when(inventoryholdRecordRepository).getInventoryholdRecordSendApplyByNo(Mockito.any());
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);

        SysLookupValuesDTO dto4=new SysLookupValuesDTO();
        dto4.setLookupType(LOOKUP_TYPE_1000038);
        dto4.setAttribute1(IWMS_INVENTORY_HOLD_FAILED_HEAD);
        sysLookupValuesDTOList.add(dto4);
        try {
            PowerMockito.doReturn(list).when(inventoryholdRecordRepository).getInventoryholdRecordSendApplyByNo(Mockito.any());
            PowerMockito.mockStatic(SpringContextUtil.class, Tools.class, ExcelUtil.class);
            PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
            PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
            Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordSendMail", empNo);
        }catch (Exception e){}

        Assert.assertNotNull(empNo);

    }

    @Test
    public void getInventoryholdRecordSendMail() throws Exception {
        List<SysLookupValuesDTO> sysLookupValuesDTOList=new ArrayList<>();
        SysLookupValuesDTO lookupValuesDTO=new SysLookupValuesDTO();
        lookupValuesDTO.setLookupType("12332");
        sysLookupValuesDTOList.add(lookupValuesDTO);
        String fileName="234343";
        String title="234343";
        InventoryholdRecordDTO dto=new InventoryholdRecordDTO();
        dto.setWhseid("1");
        dto.setEmpNo("2342344");
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordSendMailTotal(Mockito.any())).thenReturn(0);
        Whitebox.invokeMethod(inventoryholdRecordService, "getInventoryholdRecordSendMail",sysLookupValuesDTOList, dto,fileName,title);

        String expectedFileKey = "fileKey";
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordSendMailTotal(Mockito.any())).thenReturn(1);
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "getInventoryholdRecordSendMail",sysLookupValuesDTOList, dto,fileName,title);

        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordSendMailTotal(Mockito.any())).thenReturn(20008);
        PowerMockito.mockStatic(SpringContextUtil.class,Tools.class,ExcelUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(CLOUD_DISK)).thenReturn(cloudDiskHelper);
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(expectedFileKey);
        Whitebox.invokeMethod(inventoryholdRecordService, "getInventoryholdRecordSendMail",sysLookupValuesDTOList, dto,fileName,title);

        Assert.assertNotNull(dto);
    }

    @Test
    public void importInventoryHold() throws Exception {

        List<InventoryholdRecordDTO> list = new ArrayList<>();
        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setItemBarcode("35262");
        list.add(dto);
        int num = 1;
        PowerMockito.doReturn(num).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "checkImportInventoryHold", list);
        Whitebox.invokeMethod(inventoryholdRecordService, "getBatchItemNoByItemBarcode", list);
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordNum(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);
        num = 0;
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordNum(Mockito.any())).thenReturn(num);
        List<String> wms = new ArrayList<>();
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);

        wms.add("WMWHSE1");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        num = 1;
        PowerMockito.when(inventoryholdRecordRepository.insertInventoryholdRecord(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);


        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        dto.setSerialkey(BigDecimal.ONE);
        dto.setWhseid("WMWHSE1");
        dto.setItemBarcode("35262");
        dto.setHoldReason("FUJ1");
        dto.setHoldStatus(BigDecimal.ONE);
        dto.setEditwho("yhy00254784");
        dto.setInforFailTimes(BigDecimal.ZERO);
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", inventoryholdRecordDTO);
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);
        List<SysLookupValuesDTO> sysLookupValuesDTOLis1 = new ArrayList<>();
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysDto = SysLookupValuesDTO.builder().build().setLookupMeaning("GELI");
        sysLookupValuesDTOList.add(sysDto);
        PowerMockito.doReturn(sysLookupValuesDTOList).when(inventoryholdRecordRepository).getIsNeedApproved(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);
        dto.setApprovedByNo("aaa");
        PowerMockito.doReturn(sysLookupValuesDTOList).when(inventoryholdRecordRepository).getIsNeedApproved(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);
        PowerMockito.doReturn(sysLookupValuesDTOLis1).when(inventoryholdRecordRepository).getIsNeedApproved(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "importInventoryHold", list);

        Assert.assertNotNull(list);
    }

    @Test
    public void getBatchItemNoByItemBarcode() throws Exception {
        List<InventoryholdRecordDTO> list=new ArrayList<>();
        InventoryholdRecordDTO dto=new InventoryholdRecordDTO();
        dto.setWhseid("1");
        dto.setEmpNo("2342344");
        dto.setItemBarcode("1111");
        dto.setItemId("1111");
        dto.setSupplyNo("1111");
        list.add(dto);
        List<InventoryholdRecordDTO> list0 = new ArrayList<>();
        list0.add(dto);
        PowerMockito.when(inventoryholdRecordRepository.getBatchItemNoByItemBarcode(Mockito.any())).thenReturn(list0);
        List<InventoryholdRecordDTO> itemNoList0 = new ArrayList<>();
        InventoryholdRecordDTO itemNo1 = new InventoryholdRecordDTO();
        itemNo1.setItemId("1111");
        itemNo1.setItemNo("1111");
        itemNoList0.add(itemNo1);
        InventoryholdRecordDTO itemNo2 = new InventoryholdRecordDTO();
        itemNo2.setItemId("2222");
        itemNo2.setItemNo("2222");
        itemNoList0.add(itemNo2);
        PowerMockito.when(stepIscpRepository.getBatchItemNoByItemId(Mockito.any())).thenReturn(itemNoList0);
        Whitebox.invokeMethod(inventoryholdRecordService, "getBatchItemNoByItemBarcode",list);

        InventoryholdRecordDTO dto1 = new InventoryholdRecordDTO();
        dto1.setWhseid("2");
        dto1.setEmpNo("343");
        dto1.setItemBarcode("1111");
        dto1.setItemId("2222");
        list0.clear();
        list0.add(dto1);
        PowerMockito.when(inventoryholdRecordRepository.getBatchItemNoByItemBarcode(Mockito.any())).thenReturn(list0);
        PowerMockito.when(stepIscpRepository.getBatchItemNoByBomId(Mockito.any())).thenReturn(itemNoList0);
        Whitebox.invokeMethod(inventoryholdRecordService, "getBatchItemNoByItemBarcode",list);

        list0.clear();
        list0.add(dto);
        list0.add(dto1);
        PowerMockito.when(inventoryholdRecordRepository.getBatchItemNoByItemBarcode(Mockito.any())).thenReturn(list0);
        PowerMockito.when(stepIscpRepository.getBatchItemNoByItemId(Mockito.any())).thenReturn(itemNoList0);
        PowerMockito.when(stepIscpRepository.getBatchItemNoByBomId(Mockito.any())).thenReturn(itemNoList0);
        Whitebox.invokeMethod(inventoryholdRecordService, "getBatchItemNoByItemBarcode",list);

        Assert.assertNotNull(dto);
    }

    @Test
    public void checkImportInventoryHold() throws Exception {
        List<InventoryholdRecordDTO> list=new ArrayList<>();
        InventoryholdRecordDTO dto=new InventoryholdRecordDTO();
        dto.setAddwho("12");
        list.add(dto);
        Whitebox.invokeMethod(inventoryholdRecordService, "checkImportInventoryHold", list);

        List<InventoryholdRecordDTO> list1=new ArrayList<>();
        InventoryholdRecordDTO dto1=new InventoryholdRecordDTO();
        dto1.setAddwho("12");
        dto1.setEditwho("123");
        list1.add(dto1);
        InventoryholdRecordDTO dto2=new InventoryholdRecordDTO();
        dto2.setAddwho("12");
        dto2.setEditwho("123");
        list1.add(dto2);
        Whitebox.invokeMethod(inventoryholdRecordService, "checkImportInventoryHold", list1);

        List<InventoryholdRecordDTO> list2=new ArrayList<>();
        list2.add(dto1);
        Whitebox.invokeMethod(inventoryholdRecordService, "checkImportInventoryHold", list2);

        Assert.assertNotNull(list);
    }

    /*Started by AICoder, pid:8d209fa393884ae5a0c590a764428406*/
    @Test
    public void checkDTO() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setItemBarcode("35262");
        dto.setHoldReason("FUJ1");
        dto.setHoldStatus(BigDecimal.ONE);
        dto.setInforFailTimes(BigDecimal.ZERO);
        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        dto.setAddwho("yhy");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        dto.setEditwho("yhy");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        dto.setEditDeptNo("yhy");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        dto.setRemark("测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试11");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        dto.setRemark("123");
        Whitebox.invokeMethod(inventoryholdRecordService, "checkDTO", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }
    /*Ended by AICoder, pid:8d209fa393884ae5a0c590a764428406*/

    @Test
    public void effectiveInventoryholdRecord() throws Exception {

        List<InventoryholdRecordDTO> list = new ArrayList<>();
        Whitebox.invokeMethod(inventoryholdRecordService, "effectiveInventoryholdRecord", list);

        List<InventoryholdRecordDTO> list1 = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setSerialkey(BigDecimal.ONE);
        inventoryholdRecordDTO.setItemBarcode("35262");
        inventoryholdRecordDTO.setHoldReason("FUJ1");
        inventoryholdRecordDTO.setHoldStatus(BigDecimal.ONE);
        inventoryholdRecordDTO.setEditwho("yhy");
        inventoryholdRecordDTO.setInforFailTimes(BigDecimal.ZERO);
        inventoryholdRecordDTO.setWhseid("WMWHSE1");
        list1.add(inventoryholdRecordDTO);
        BusiAssertException.isTrue(Tools.isEmpty(list1.get(list1.size()- Constant.INT_1).getEditDeptNo()), MessageId.PLEASE_LOGIN_FIRST);
        Whitebox.invokeMethod(inventoryholdRecordService, "effectiveInventoryholdRecord", list1);

        inventoryholdRecordDTO.setEditDeptNo("yhy");
        list.add(inventoryholdRecordDTO);
        int count = 0;
        PowerMockito.doReturn(count).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        BusiAssertException.isTrue(count != list.size(), MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EFFECTED);
        Whitebox.invokeMethod(inventoryholdRecordService, "effectiveInventoryholdRecord", list);

        count = 1;
        PowerMockito.doReturn(count).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
        List<String> lotList = new ArrayList<>();
        lotList.add("32423");
        PowerMockito.doReturn(lotList).when(inventoryholdRecordRepository).getLotByItemBarcode(Mockito.any());
        ServiceData<Map<String, String>> result = new ServiceData<>();
        try {
            PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                    Mockito.any())).thenReturn(result);
            Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", inventoryholdRecordDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Map<String, String> map = new HashMap<>();
        map.put("354252", "");
        result.setBo(map);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                Mockito.any())).thenReturn(result);
        PowerMockito.doReturn(0).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", inventoryholdRecordDTO);
        Whitebox.invokeMethod(inventoryholdRecordService, "effectiveInventoryholdRecord", list);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    /* Started by AICoder, pid:90226e224b0b404aaaf1f47fc1d1343a */
    public void expireInventoryholdRecord() throws Exception {
        try {
            List<InventoryholdRecordDTO> list = new ArrayList<>();
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list);

            List<InventoryholdRecordDTO> list10 = new ArrayList<>();
            InventoryholdRecordDTO inventoryholdRecordDTO10 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO10.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO10.setItemBarcode("35262");
            inventoryholdRecordDTO10.setHoldReason("FUJ1");
            inventoryholdRecordDTO10.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO10.setEditwho("yhy00326075");
            inventoryholdRecordDTO10.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO10.setWhseid("WMWHSE1");
            inventoryholdRecordDTO10.setHoldReasonType(STR_QUALITY_HOLD);
            inventoryholdRecordDTO10.setItemNo("22");
            inventoryholdRecordDTO10.setEditDeptName("配送部");
            list10.add(inventoryholdRecordDTO10);
            BusiAssertException.isTrue(Tools.isEmpty(list10.get(list10.size()- Constant.INT_1).getEditDeptNo()), MessageId.PLEASE_LOGIN_FIRST);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list10);

            InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
            inventoryholdRecordDTO.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO.setItemBarcode("35262");
            inventoryholdRecordDTO.setHoldReason("FUJ1");
            inventoryholdRecordDTO.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO.setEditwho("yhy00326075");
            inventoryholdRecordDTO.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO.setWhseid("WMWHSE1");
            inventoryholdRecordDTO.setHoldReasonType(STR_QUALITY_HOLD);
            inventoryholdRecordDTO.setItemNo("22");
            inventoryholdRecordDTO.setEditDeptName("22");
            List<InventoryholdRecordDTO> list1 = new ArrayList<>();
            list1.add(inventoryholdRecordDTO);
            BusiAssertException.isTrue(Tools.isEmpty(list1.get(list1.size()- Constant.INT_1).getEditDeptNo()), MessageId.PLEASE_LOGIN_FIRST);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list1);

            inventoryholdRecordDTO.setEditDeptNo("yhy");
            list.add(inventoryholdRecordDTO);
            InventoryholdRecordDTO inventoryholdRecordDTO1 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO1.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO1.setItemBarcode("35262");
            inventoryholdRecordDTO1.setHoldReason("FUJ1");
            inventoryholdRecordDTO1.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO1.setEditwho("yhy00326075");
            inventoryholdRecordDTO1.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO1.setWhseid("WMWHSE1");
            inventoryholdRecordDTO1.setHoldReasonType("323");
            inventoryholdRecordDTO1.setItemNo("22");
            inventoryholdRecordDTO1.setEditDeptName("22");
            list.add(inventoryholdRecordDTO1);
            InventoryholdRecordDTO inventoryholdRecordDTO2 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO2.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO2.setItemBarcode("15262");
            inventoryholdRecordDTO2.setHoldReason("FUJ1");
            inventoryholdRecordDTO2.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO2.setEditwho("yhy00326075");
            inventoryholdRecordDTO2.setApplyBy("yhy00326175");
            inventoryholdRecordDTO2.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO2.setWhseid("WMWHSE1");
            inventoryholdRecordDTO2.setHoldReasonType(STR_PLAN_HOLD);
            inventoryholdRecordDTO2.setItemNo("122");
            inventoryholdRecordDTO2.setEditDeptName("22");
            list.add(inventoryholdRecordDTO2);
            InventoryholdRecordDTO inventoryholdRecordDTO3 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO3.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO3.setItemBarcode("35262");
            inventoryholdRecordDTO3.setHoldReason("FUJ1");
            inventoryholdRecordDTO3.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO3.setEditwho("yhy0032607534");
            inventoryholdRecordDTO3.setApplyBy("yhy00326075");
            inventoryholdRecordDTO3.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO3.setWhseid("WMWHSE1");
            inventoryholdRecordDTO3.setHoldReasonType(STR_PLAN_HOLD);
            inventoryholdRecordDTO3.setItemNo("22");
            inventoryholdRecordDTO3.setEditDeptName("配送部");
            list.add(inventoryholdRecordDTO3);
            InventoryholdRecordDTO inventoryholdRecordDTO6 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO6.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO6.setItemBarcode("35262");
            inventoryholdRecordDTO6.setHoldReason("FUJ1");
            inventoryholdRecordDTO6.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO6.setEditwho("yhy0032607534");
            inventoryholdRecordDTO6.setApplyBy("yhy00326175");
            inventoryholdRecordDTO6.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO6.setWhseid("WMWHSE1");
            inventoryholdRecordDTO6.setHoldReasonType(STR_PLAN_HOLD);
            inventoryholdRecordDTO6.setItemNo("22");
            inventoryholdRecordDTO6.setEditDeptName("配送部11");
            list.add(inventoryholdRecordDTO6);
            InventoryholdRecordDTO inventoryholdRecordDTO7 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO7.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO7.setItemBarcode("25262");
            inventoryholdRecordDTO7.setHoldReason("FUJ1");
            inventoryholdRecordDTO7.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO7.setEditwho("yhy00326075");
            inventoryholdRecordDTO7.setApplyBy("yhy00326075");
            inventoryholdRecordDTO7.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO7.setWhseid("WMWHSE1");
            inventoryholdRecordDTO7.setHoldReasonType(STR_PLAN_HOLD);
            inventoryholdRecordDTO7.setItemNo("22");
            inventoryholdRecordDTO7.setEditDeptName("22");
            list.add(inventoryholdRecordDTO7);
            List<HoldFlowStartDTO> holdFlowStartDTOList1 = new ArrayList<>();
            List<HoldFlowStartDTO> holdFlowStartDTOList = new ArrayList<>();
            HoldFlowStartDTO holdFlowStartDTO = HoldFlowStartDTO.builder().build().setApproverId1("1").setApproverId2("2").setItemNo("22");
            holdFlowStartDTOList.add(holdFlowStartDTO);
            int count = 0;
            ServiceData<Map<String, String>> result = new ServiceData<>();
            Map<String, String> map = new HashMap<>();
            map.put("354252", "");
            result.setBo(map);
            PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                    Mockito.any())).thenReturn(result);
            PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordNum(Mockito.any())).thenReturn(count);
            BusiAssertException.isTrue(count != list.size(), MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EXPIRED);
            PowerMockito.when(stepIscpRepository.getApprovedByItemNo(Mockito.any())).thenReturn(holdFlowStartDTOList1);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list);
            PowerMockito.when(stepIscpRepository.getApprovedByItemNo(Mockito.any())).thenReturn(holdFlowStartDTOList);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list);

            List<String> lotList = new ArrayList<>();
            lotList.add("32423");
            int num = 1;
            PowerMockito.doReturn(num).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
            PowerMockito.doReturn(lotList).when(inventoryholdRecordRepository).getLotByItemBarcode(Mockito.any());
            count = 4;
            PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordNum(Mockito.any())).thenReturn(count);
            BusiAssertException.isTrue(count != list.size(), MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EXPIRED);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list);

            list = new ArrayList<>();
            inventoryholdRecordDTO1 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO1.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO1.setItemBarcode("35262");
            inventoryholdRecordDTO1.setHoldReason("FUJ1");
            inventoryholdRecordDTO1.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO1.setEditwho("yhy00326075");
            inventoryholdRecordDTO1.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO1.setWhseid("WMWHSE1");
            inventoryholdRecordDTO1.setHoldReasonType("323");
            list.add(inventoryholdRecordDTO1);
            InventoryholdRecordDTO inventoryholdRecordDTO4 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO4.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO4.setItemBarcode("15262");
            inventoryholdRecordDTO4.setHoldReason("FUJ1");
            inventoryholdRecordDTO4.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO4.setEditwho("yhy0032607534");
            inventoryholdRecordDTO4.setApplyBy("yhy00326075");
            inventoryholdRecordDTO4.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO4.setWhseid("WMWHSE1");
            inventoryholdRecordDTO4.setHoldReasonType(STR_SAMPLE_HOLD);
            inventoryholdRecordDTO4.setItemNo("12");
            inventoryholdRecordDTO4.setEditDeptName("11");
            list.add(inventoryholdRecordDTO4);
            InventoryholdRecordDTO inventoryholdRecordDTO5 = new InventoryholdRecordDTO();
            inventoryholdRecordDTO5.setSerialkey(BigDecimal.ONE);
            inventoryholdRecordDTO5.setItemBarcode("25262");
            inventoryholdRecordDTO5.setHoldReason("FUJ1");
            inventoryholdRecordDTO5.setHoldStatus(BigDecimal.ONE);
            inventoryholdRecordDTO5.setEditwho("yhy0032607534");
            inventoryholdRecordDTO5.setApplyBy("yhy00326075");
            inventoryholdRecordDTO5.setInforFailTimes(BigDecimal.ZERO);
            inventoryholdRecordDTO5.setWhseid("WMWHSE1");
            inventoryholdRecordDTO5.setHoldReasonType(STR_SAMPLE_HOLD);
            inventoryholdRecordDTO5.setItemNo("22");
            inventoryholdRecordDTO5.setEditDeptName("配送部");
            list.add(inventoryholdRecordDTO5);
            count = 0;
            lotList = new ArrayList<>();
            lotList.add("32423");
            PowerMockito.doReturn(num).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
            PowerMockito.doReturn(lotList).when(inventoryholdRecordRepository).getLotByItemBarcode(Mockito.any());
            PowerMockito.doReturn(count).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
            BusiAssertException.isTrue(count != list.size(), MessageId.CHOOSE_INVENTORY_HOLD_RECORD_EXPIRED);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list);

            count = 1;
            PowerMockito.doReturn(count).when(inventoryholdRecordRepository).getInventoryholdRecordNum(Mockito.any());
            PowerMockito.doReturn(1).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
            Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", inventoryholdRecordDTO);
            Whitebox.invokeMethod(inventoryholdRecordService, "expireInventoryholdRecord", list);

            ServiceData<Map<String, String>> result2 = new ServiceData<>();
            try {
                PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                        Mockito.any())).thenReturn(result2);
                Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", inventoryholdRecordDTO);
            } catch (Exception e) {
                e.printStackTrace();
            }

            InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
            inventoryholdDTOTest.setItembarcode("234235235");
            inventoryholdDTOTest.setHoldReason("FUJ1");
            Assert.assertNotNull(inventoryholdDTOTest);
        }catch (Exception e){}
    }
    /* Ended by AICoder, pid:90226e224b0b404aaaf1f47fc1d1343a */

    @Test
    public void holdInventory() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setSerialkey(BigDecimal.ONE);
        dto.setWhseid("WMWHSE1");
        dto.setItemBarcode("35262");
        dto.setHoldReason("FUJ1");
        dto.setHoldStatus(BigDecimal.ONE);
        dto.setEditwho("yhy");
        dto.setInforFailTimes(BigDecimal.ZERO);

        List<String> lotList = new ArrayList<>();
        int num = 1;
        PowerMockito.doReturn(lotList).when(inventoryholdRecordRepository).getLotByItemBarcode(Mockito.any());
        PowerMockito.doReturn(num).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);

        lotList.add("354252");

        ServiceData<Map<String, String>> result = new ServiceData<>();
        try {
            PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                    Mockito.any())).thenReturn(result);
            Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, String> map = new HashMap<>();
        map.put("354252", "");
        result.setBo(map);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                Mockito.any())).thenReturn(result);
        PowerMockito.doReturn(num).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);

        map.put("354252", "35235262");
        result.setBo(map);
        PowerMockito.when(RemoteServiceDataUtil.invokeService(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),
                Mockito.any())).thenReturn(result);
        PowerMockito.doReturn(num).when(inventoryholdRecordRepository).updateInventoryholdRecord(Mockito.any());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void exportInventoryholdRecord() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        dto.setEmpNo("00123");
        int num = 1;
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordListVOTotal(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "exportInventoryholdRecord", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void exportInventoryholdException() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        dto.setEmpNo("00123");
        int num =1;
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordListVOTotal(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "exportInventoryholdException", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void batchUpdateInventoryholdRecord() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();
        int num =1;
        PowerMockito.when(inventoryholdRecordRepository.batchUpdateInventoryholdRecord(Mockito.any())).thenReturn(num);
        Whitebox.invokeMethod(inventoryholdRecordService, "batchUpdateInventoryholdRecord", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }

    @Test
    public void inventoryholdRecordJob() throws Exception {

        InventoryholdRecordDTO dto = new InventoryholdRecordDTO();

        List<String> wms = new ArrayList<>();
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordJob", dto);

        wms.add("WMWHSE1");
        PowerMockito.when(barcodeCenterRepository.getInventoryHoldWmwhids()).thenReturn(wms);

        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdJobList(Mockito.any())).thenReturn(inventoryholdRecordDTOList);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordJob", dto);

        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setSerialkey(BigDecimal.ONE);
        inventoryholdRecordDTO.setInforFailTimes(BigDecimal.ONE);
        inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
        InventoryholdRecordDTO inventoryholdRecordDTO2 = new InventoryholdRecordDTO();
        inventoryholdRecordDTO2.setSerialkey(BigDecimal.ONE);
        inventoryholdRecordDTO2.setInforFailTimes(new BigDecimal(4));
        inventoryholdRecordDTOList.add(inventoryholdRecordDTO2);
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdJobList(Mockito.any())).thenReturn(inventoryholdRecordDTOList);

        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordJob", dto);

        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setLookupMeaning("456373");
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any(SysLookupValuesDTO.class))).thenReturn(sysLookupValuesDTOList);
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventory", dto);
        Whitebox.invokeMethod(inventoryholdRecordService, "inventoryholdRecordJob", dto);

        InventoryholdDTO inventoryholdDTOTest = new InventoryholdDTO();
        inventoryholdDTOTest.setItembarcode("234235235");
        inventoryholdDTOTest.setHoldReason("FUJ1");
        Assert.assertNotNull(inventoryholdDTOTest);
    }
    @Test
    /* Started by AICoder, pid:9aceb44d891e4184bfd964f60c327e09 */
    public void holdInventoryToApprove() throws Exception {
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList1 = new ArrayList<>();
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setItemBarcode("2343252").setEditwho("254874512");
        inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        SysLookupValuesDTO sysDto = SysLookupValuesDTO.builder().build().setLookupMeaning("GELI");
        sysLookupValuesDTOList.add(sysDto);
        List<InventoryholdRecord> inventoryholdRecordList = new ArrayList<>();
        InventoryholdRecord inventoryholdRecord =InventoryholdRecord.builder().build().setItemBarcode("11");
        inventoryholdRecordList.add(inventoryholdRecord);
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventoryToApprove", inventoryholdRecordDTOList1,sysLookupValuesDTOList,inventoryholdRecordDTOList,inventoryholdRecordList);
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventoryToApprove", inventoryholdRecordDTOList,sysLookupValuesDTOList,inventoryholdRecordDTOList,inventoryholdRecordList);
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList2 = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO2 = new InventoryholdRecordDTO();
        inventoryholdRecordDTO2.setItemBarcode("2343252").setEditwho("254874512").setWhseid("WMWHSE1");
        inventoryholdRecordDTOList2.add(inventoryholdRecordDTO2);
        PowerMockito.mockStatic(JSON.class);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 4200; i++) {
            sb.append("测试");
        }
        PowerMockito.when(inventoryholdRecordRepository.getHoldWhseName(Mockito.any())).thenReturn("科技园");
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn(sb.toString());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventoryToApprove", inventoryholdRecordDTOList2,sysLookupValuesDTOList,inventoryholdRecordDTOList,inventoryholdRecordList);
        PowerMockito.when(inventoryholdRecordRepository.getHoldWhseName(Mockito.any())).thenThrow(new NullPointerException());
        Whitebox.invokeMethod(inventoryholdRecordService, "holdInventoryToApprove", inventoryholdRecordDTOList2,sysLookupValuesDTOList,inventoryholdRecordDTOList,inventoryholdRecordList);
        Assert.assertNotNull(inventoryholdRecordDTOList2);
    }
    /* Ended by AICoder, pid:9aceb44d891e4184bfd964f60c327e09 */
    @Test
    /* Started by AICoder, pid:84cb7b07667a4c739e627fb7ea84a713 */
    public void sendApprovedByQuality() throws Exception {
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList1 = new ArrayList<>();
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setItemBarcode("2343252").setEditwho("254874512").setUnfreezeRemark("test");
        inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
        Whitebox.invokeMethod(inventoryholdRecordService, "sendApprovedByQuality", inventoryholdRecordDTOList1);
        PowerMockito.mockStatic(JSON.class);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 4200; i++) {
            sb.append("测试");
        }
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn(sb.toString());
        Whitebox.invokeMethod(inventoryholdRecordService, "sendApprovedByQuality", inventoryholdRecordDTOList);
        Assert.assertTrue(sb.length() > 4000);
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn("test");
        Whitebox.invokeMethod(inventoryholdRecordService, "sendApprovedByQuality", inventoryholdRecordDTOList);
        Assert.assertEquals("test", inventoryholdRecordDTOList.get(0).getUnfreezeRemark());
    }
    /* Ended by AICoder, pid:84cb7b07667a4c739e627fb7ea84a713 */
    @Test
    /* Started by AICoder, pid:480543750b7d45c49041705b2520c4fe */
    public void sendApprovedByPlan() throws Exception {
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList1 = new ArrayList<>();
        List<InventoryholdRecordDTO> inventoryholdRecordDTOList = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setItemBarcode("2343252").setEditwho("254874512").setUnfreezeRemark("test");
        inventoryholdRecordDTOList.add(inventoryholdRecordDTO);
        Whitebox.invokeMethod(inventoryholdRecordService, "sendApprovedByPlan", inventoryholdRecordDTOList1);
        PowerMockito.mockStatic(JSON.class);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 4200; i++) {
            sb.append("测试");
        }
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn(sb.toString());
        Whitebox.invokeMethod(inventoryholdRecordService, "sendApprovedByPlan", inventoryholdRecordDTOList);
        Assert.assertTrue(sb.length() > 4000);
        PowerMockito.when(JSON.toJSONString(Mockito.any())).thenReturn("test");
        Whitebox.invokeMethod(inventoryholdRecordService, "sendApprovedByPlan", inventoryholdRecordDTOList);
        Assert.assertEquals("test", inventoryholdRecordDTOList.get(0).getUnfreezeRemark());
    }
    /* Started by AICoder, pid:08191b273fbc4f8faf17c61e32c555ef */
    @Test
    public void handleExpireData_Success() throws Exception {
        // 创建一个InventoryholdRecordDTO对象的列表
        List<InventoryholdRecordDTO> list = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = createInventoryholdRecordDTO("35262", "FUJ1", STR_QUALITY_HOLD);
        list.add(inventoryholdRecordDTO);
        InventoryholdRecordDTO inventoryholdRecordDTO1 = createInventoryholdRecordDTO("35262", "FUJ1", "323");
        list.add(inventoryholdRecordDTO1);

        Whitebox.invokeMethod(inventoryholdRecordService, "handleExpireData", list, list, list, list);

        // 验证结果，这里假设方法执行成功，没有抛出异常
        Assert.assertNotNull(list);
    }

    @Test
    public void handleExpireData_Exception() throws Exception {
        // 创建一个InventoryholdRecordDTO对象的列表
        List<InventoryholdRecordDTO> list = new ArrayList<>();
        InventoryholdRecordDTO inventoryholdRecordDTO = createInventoryholdRecordDTO("35262", "FUJ1", STR_QUALITY_HOLD);
        list.add(inventoryholdRecordDTO);
        InventoryholdRecordDTO inventoryholdRecordDTO1 = createInventoryholdRecordDTO("35262", "FUJ1", "323");
        list.add(inventoryholdRecordDTO1);

        // 模拟updateBatchInventoryholdRecord方法抛出异常
        Mockito.doThrow(new NullPointerException()).when(inventoryholdRecordRepository).updateBatchInventoryholdRecord(Mockito.any());

        try {
            Whitebox.invokeMethod(inventoryholdRecordService, "handleExpireData", list, list, list, list);
        } catch (Exception e) {
            // 验证是否捕获到期望的异常
            Assert.assertEquals(NullPointerException.class, e.getClass());
        }
    }

    private InventoryholdRecordDTO createInventoryholdRecordDTO(String itemBarcode, String holdReason, String holdReasonType) {
        InventoryholdRecordDTO inventoryholdRecordDTO = new InventoryholdRecordDTO();
        inventoryholdRecordDTO.setSerialkey(BigDecimal.ONE);
        inventoryholdRecordDTO.setItemBarcode(itemBarcode);
        inventoryholdRecordDTO.setHoldReason(holdReason);
        inventoryholdRecordDTO.setHoldStatus(BigDecimal.ONE);
        inventoryholdRecordDTO.setEditwho("yhy00326075");
        inventoryholdRecordDTO.setInforFailTimes(BigDecimal.ZERO);
        inventoryholdRecordDTO.setWhseid("WMWHSE1");
        inventoryholdRecordDTO.setHoldReasonType(holdReasonType);
        inventoryholdRecordDTO.setItemNo("22");
        inventoryholdRecordDTO.setEditDeptName("22");
        inventoryholdRecordDTO.setEditDeptNo("yhy");
        return inventoryholdRecordDTO;
    }
    /* Ended by AICoder, pid:08191b273fbc4f8faf17c61e32c555ef */

    /* Started by AICoder, pid:0cd37145af0ee2614e160891c08a3b0886322014 */
    @Test
    public void sendRedDotTask() throws Exception{
        InventoryholdRecordDTO param = new InventoryholdRecordDTO();
        List<InventoryholdRecordEmailVO> emailVOList = new ArrayList<>();
        InventoryholdRecordEmailVO inventoryholdRecordEmailVO = new InventoryholdRecordEmailVO();
        inventoryholdRecordEmailVO.setEditDeptName("配送部");
        emailVOList.add(inventoryholdRecordEmailVO);
        InventoryholdRecordEmailVO inventoryholdRecordEmailVO1 = new InventoryholdRecordEmailVO();
        inventoryholdRecordEmailVO1.setEditDeptName("其他");
        emailVOList.add(inventoryholdRecordEmailVO1);
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordSendMail(Mockito.any())).thenReturn(emailVOList);
        inventoryholdRecordService.sendRedDotTask(param);
        PowerMockito.when(inventoryholdRecordRepository.getInventoryholdRecordSendMail(Mockito.any())).thenThrow(new NullPointerException());
        inventoryholdRecordService.sendRedDotTask(param);
        Assert.assertNotNull(emailVOList);
    }
    /* Ended by AICoder, pid:0cd37145af0ee2614e160891c08a3b0886322014 */
}
