package com.zte.application.datawb.impl;

import com.zte.domain.model.datawb.VMesReelid;
import com.zte.domain.model.datawb.VMesReelidRepository;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

public class VMesReelidServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    VMesReelidServiceImpl service;
    @Mock
    VMesReelidRepository repository;

    @Test
    public void getSnbyReelid() {
        VMesReelid vMesReelid = new VMesReelid();
        vMesReelid.setQty(BigDecimal.ONE);
        vMesReelid.setLottable02("1111");
        PowerMockito.when(repository.getSnbyReelid(any(), any())).thenReturn(vMesReelid);
        service.getSnbyReelid(null, "aa");
        Assert.assertNotNull(service.getSnbyReelid("aa", "aa"));
    }

    /*Started by AICoder, pid:y94b2i52a8gcedd14c4d0b5ba012ee5a71256d34*/
    @Test
    public void testSelectByPage_EmptyList() {
        Page<VMesReelid> page = new Page<>();
        when(repository.selectByPage(page)).thenReturn(new ArrayList<>());

        Page<VMesReelid> resultPage = service.selectByPage(page);

        assertEquals(0, resultPage.getRows().size());
    }

    @Test
    public void testSelectByPage_NonEmptyList() {
        Page<VMesReelid> page = new Page<>();
        List<VMesReelid> rows = new ArrayList<>();
        rows.add(new VMesReelid());
        rows.add(new VMesReelid());
        when(repository.selectByPage(page)).thenReturn(rows);

        Page<VMesReelid> resultPage = service.selectByPage(page);

        assertEquals(2, resultPage.getRows().size());
    }
    /*Ended by AICoder, pid:y94b2i52a8gcedd14c4d0b5ba012ee5a71256d34*/
}