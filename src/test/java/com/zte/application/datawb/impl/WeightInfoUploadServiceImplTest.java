/*Started by AICoder, pid:deb4cc9cc26d46c7a50ea217c5eef38a*/
package com.zte.application.datawb.impl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.CommonUtil;
import com.zte.domain.model.WeightInfoUploadRepository;
import com.zte.interfaces.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

public class WeightInfoUploadServiceImplTest {
    @Mock
    private WeightInfoUploadRepository weightInfoUploadRepository;

    @InjectMocks
    private WeightInfoUploadServiceImpl weightInfoUploadService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWeightInfoUpload() throws Exception {
        // Setup test data
        weightInfoUploadDTO dto = new weightInfoUploadDTO();
        weightMsgheaderTypeDTO header = new weightMsgheaderTypeDTO();
        header.setUserId("1");
        dto.setWeightMsgheader(header);

        List<weightRecordTypeDTO> weightInputcollection = new ArrayList<>();
        weightRecordTypeDTO record1 = new weightRecordTypeDTO();
        record1.setBoxNum("C171009392725");
        record1.setGrossWeight(new BigDecimal("80"));
        record1.setNetWeight(new BigDecimal("0"));
        weightInputcollection.add(record1);
        record1.setBoxNum("C171009392729");
        record1.setGrossWeight(new BigDecimal("70"));
        record1.setNetWeight(new BigDecimal("50"));
        weightInputcollection.add(record1);
        dto.setWeightInputcollection(weightInputcollection);

        List<weightQueryParamsDTO> boxNumResult=new ArrayList<>();
        for(weightRecordTypeDTO weightBox : dto.getWeightInputcollection())
        {
            weightQueryParamsDTO w = new weightQueryParamsDTO();
            w.setBoxNum(weightBox.getBoxNum());
            boxNumResult.add(w);
        }
        List<weightQueryParamsDTO> boxWeightResult = new ArrayList<>();
        for(weightRecordTypeDTO weightBox : dto.getWeightInputcollection())
        {
            weightQueryParamsDTO w = new weightQueryParamsDTO();
            w.setBoxNum(weightBox.getBoxNum());
            w.setGrossWeight(weightBox.getGrossWeight());
            w.setNetWeight(weightBox.getNetWeight());
            w.setBoxWeight(new BigDecimal("50"));
            boxWeightResult.add(w);
        }
        try {
            PowerMockito.when(weightInfoUploadRepository.querryBoxNumber(Mockito.any()))
                    .thenReturn(boxNumResult);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPLODE_FAILED_BOXNUM_IS_NOT_EXIST, e.getMessage());
        }
        try {
            PowerMockito.when(weightInfoUploadRepository.querryBoxWeight(Mockito.any()))
                    .thenReturn(boxWeightResult);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPLODE_FAILED_CHECK_THE_LIMIT_WEIGHT, e.getMessage());
        }
        try {
            weightInfoUploadRepository.updateBoxWeight(boxWeightResult);
            Assert.assertEquals(true, true);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.UPLODE_FAILED_CHECK_THE_LIMIT_WEIGHT, e.getMessage());
        }

        // Call the method under test
        //weightInfoOutDTO result = weightInfoUploadService.weightInfoUpload(dto);

    }
}
/*Ended by AICoder, pid:deb4cc9cc26d46c7a50ea217c5eef38a*/