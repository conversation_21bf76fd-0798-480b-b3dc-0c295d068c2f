package com.zte.application.datawb.impl;

import com.google.common.collect.Lists;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.TbControlIMES;
import com.zte.domain.model.datawb.TbControlIMESRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class TbControlIMESServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    TbControlIMESServiceImpl service;

    @Mock
    TbControlIMESRepository repository;

    @Test
    public void syncFromIMES() {
        service.syncFromIMES(null, 1);
        service.syncFromIMES(Lists.newArrayList(new TbControlIMES()), 1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

}