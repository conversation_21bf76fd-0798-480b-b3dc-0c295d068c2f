package com.zte.application.datawb.impl;

import com.google.common.collect.Lists;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.DailyReportSyncRepository;
import com.zte.domain.model.datawb.WipDailyOuterReport;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.Assert.*;

public class DailyReportSyncServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    DailyReportSyncServiceImpl service;

    @Mock
    DailyReportSyncRepository repository;

    @Test
    public void syncOuterQryToMES() {
        service.syncOuterQryToMES(null);
        service.syncOuterQryToMES(Lists.newArrayList(new WipDailyOuterReport()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}