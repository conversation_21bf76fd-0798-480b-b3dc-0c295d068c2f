package com.zte.application.datawb.impl;

import com.zte.domain.model.ZmsBoardSnTencentRepository;
import com.zte.interfaces.dto.ZmsBoardSnTencentDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

@RunWith(PowerMockRunner.class)
public class ZmsBoardSnTencentServiceImplTest {
    @InjectMocks
    private ZmsBoardSnTencentServiceImpl service;
    @Mock
    private ZmsBoardSnTencentRepository repository;

    @Test
    public void getBoardSnInfoOfTencentTest() {
        ZmsBoardSnTencentDTO dto = new ZmsBoardSnTencentDTO();
        List<ZmsBoardSnTencentDTO> boardSnInfoOfTencent = service.getBoardSnInfoOfTencent(dto);
        Assert.assertTrue(boardSnInfoOfTencent.size() == 0);
    }
}
