package com.zte.application.datawb.impl;

import com.zte.domain.model.MtlSystemItemsRepository;
import com.zte.interfaces.dto.MtlSystemItemsDTO;
import com.zte.util.BaseTestCase;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

/**
 * MtlSystemItemsServiceErpImpl Tester.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @version 1.0
 * @since <pre>8 19, 2022</pre>
 */
public class MtlSystemItemsServiceErpImplTest extends BaseTestCase {

    @Mock
    private MtlSystemItemsRepository mtlSystemItemsRepository;

    @InjectMocks
    private MtlSystemItemsServiceErpImpl service;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: getBySegment1AndOrganizationId(String segment1, Integer organizationId)
     */
    @Test
    public void testGetBySegment1AndOrganizationId() throws Exception {
        PowerMockito.when(mtlSystemItemsRepository.getBySegment1AndOrganizationId("test",1)).thenReturn(new MtlSystemItemsDTO());
        Assert.assertNotNull(service.getBySegment1AndOrganizationId("test", 1));
    }

}
