package com.zte.application.datawb.impl;

import com.google.common.collect.Lists;
import com.zte.domain.model.datawb.SemisBox;
import com.zte.domain.model.datawb.SemisBoxRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

public class SemisBoxServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    SemisBoxServiceImpl service;

    @Mock
    private SemisBoxRepository repository;

    @Test
    public void selectBySerialIds() {
        service.selectBySerialIds(null);
        service.selectBySerialIds(Lists.newArrayList(1L));
        PowerMockito.when(repository.selectBySerialIds(any())).thenReturn(Lists.newArrayList(new SemisBox()));
        Assert.assertNotNull(service.selectBySerialIds(Lists.newArrayList(1L)));
    }
}