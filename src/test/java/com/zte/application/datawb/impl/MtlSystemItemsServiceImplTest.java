package com.zte.application.datawb.impl;

import com.zte.domain.model.MtlSystemItemsRepository;
import com.zte.domain.model.WsmAssembleHeadersRepository;
import com.zte.interfaces.dto.MtlSystemItemsDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(PowerMockRunner.class)
public class MtlSystemItemsServiceImplTest extends BaseTestCase {
    @InjectMocks
    MtlSystemItemsServiceImpl mtlSystemItemsService;

    @Mock
    private MtlSystemItemsRepository mtlSystemItemsRepository;
    @Test
    public void getItemIdBySegment1List() {
        List<String> itemNoList =new ArrayList<>();
        itemNoList.add("test123");
        mtlSystemItemsService.getItemIdBySegment1List(new ArrayList<>());
        PowerMockito.when( mtlSystemItemsRepository.getItemIdBySegment1List(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(mtlSystemItemsService.getItemIdBySegment1List(itemNoList));
        Assert.assertTrue(CollectionUtils.isEmpty(mtlSystemItemsService.getIncrementalItem(new MtlSystemItemsDTO())));
    }
}