package com.zte.application.datawb.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.IMESLogService;
import com.zte.application.datawb.VMesBoxNoNewService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.VMesBoxNoNewRepository;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.WmsRemoteService;
import com.zte.interfaces.dto.VMesBoxNoNewDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.*;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({MESHttpHelper.class, HttpClientUtil.class, JacksonJsonConverUtil.class, HttpRemoteUtil.class, InterfaceEnum.class,
        Normalizer.class, CommonUtils.class,HttpRemoteService.class, HttpRemoteUtil.class, InterfaceEnum.class, JsonConvertUtil.class})
@RunWith(PowerMockRunner.class)
public class VMesBoxNoNewServiceImplTest  extends BaseTestCase {
    @InjectMocks
    private VMesBoxNoNewServiceImpl vMesBoxNoNewServiceImpl;
    @Mock
    private VMesBoxNoNewService vMesBoxNoNewService;


    @Mock
    private VMesBoxNoNewRepository vMesBoxNoNewRepository;

    @Mock
    private RedisTemplate<String,Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> test;

    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;

    @Mock
    private IMESLogService imesLogService;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private CenterfactoryRemoteService centerfactoryRemoteService;
    @Mock
    private WmsRemoteService wmsRemoteService;
    @Test
    public void syncItemIssueInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(HttpClientUtil.class);

        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(test);
        PowerMockito.when(test.setIfAbsent(Mockito.any(),Mockito.any(),
                        Mockito.anyLong(),Mockito.any()))
                .thenReturn(true);

        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupCode(new BigDecimal("2024006"));
        a1.setLookupType(new BigDecimal(2024));
        a1.setLookupMeaning("");
        SysLookupValues a2 = new SysLookupValues();
        a2.setLookupCode(new BigDecimal("2024009"));
        a2.setLookupType(new BigDecimal(2024));
        a2.setLookupMeaning("36");
        list.add(a1);
        list.add(a2);
        PowerMockito.when(centerfactoryRemoteService.queryLookupValue(Mockito.any()))
                .thenReturn(list);

        List<VMesBoxNoNewDTO> inforList = new LinkedList<>();
        VMesBoxNoNewDTO b1 = new VMesBoxNoNewDTO();
        inforList.add(b1);
        PowerMockito.when(vMesBoxNoNewRepository.getBoxItemIssueInfo(Mockito.any()))
                .thenReturn(inforList);
        List<VMesBoxNoNewDTO> result = new ArrayList<>();
        Exception exception = new Exception("");
        PowerMockito.when(wmsRemoteService.sendToWms(Mockito.anyList(), Mockito.any()))
                .thenReturn(new ArrayList<>());

        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.any(), Mockito.anyMap(), Mockito.any(),
                        Mockito.any()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "   \n" +
                        "  ]\n" +
                        "}");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": {\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "}");
        vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        Assert.assertNotNull(vMesBoxNoNewDTO);
        List<List<VMesBoxNoNewDTO>> vMesListOfList = new ArrayList<>();
        List<List<String>> serialKeyListOfList = new ArrayList<>();
        List<String> listOfString = new ArrayList<>();
        serialKeyListOfList.add(listOfString);
        listOfString.add("202507210001");
        vMesListOfList.add(inforList);
        PowerMockito.when(CommonUtils.splitList((List<String>)Mockito.any(), Mockito.anyInt())).thenReturn(serialKeyListOfList);
        PowerMockito.when(CommonUtils.splitList((List<VMesBoxNoNewDTO>)Mockito.any(), Mockito.anyInt())).thenReturn(vMesListOfList);
        PowerMockito.when(CommonUtils.getLmbMessage((String) Mockito.any(), (String) Mockito.any())).thenReturn("error");
        vMesBoxNoNewDTO.setSerialKey("202507210001");
        b1.setSerialKey("202507210001");
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);

        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }

        SysLookupValues a3 = new SysLookupValues();
        a3.setLookupCode(new BigDecimal("2024007"));
        a3.setLookupType(new BigDecimal(2024));
        a3.setLookupMeaning("36");
        a3.setAttribute1("36");
        PowerMockito.when(emailUtils.sendMail(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(true);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
    }

    @Test
    public void syncItemIssueInfoForZHTest1() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, CommonUtils.class,JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, InterfaceEnum.class,JsonConvertUtil.class);

        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(test);
        PowerMockito.when(test.setIfAbsent(Mockito.any(),Mockito.any(),
                        Mockito.anyLong(),Mockito.any()))
                .thenReturn(true);

        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupCode(new BigDecimal("2024006"));
        a1.setLookupType(new BigDecimal(2024));
        a1.setLookupMeaning("");
        SysLookupValues a2 = new SysLookupValues();
        a2.setLookupCode(new BigDecimal("2024004"));
        a2.setLookupType(new BigDecimal(2024));
        a2.setLookupMeaning("36");
        list.add(a1);
        list.add(a2);
        PowerMockito.when(centerfactoryRemoteService.queryLookupValue(Mockito.any()))
                .thenReturn(list);

        List<VMesBoxNoNewDTO> inforList = new LinkedList<>();
        VMesBoxNoNewDTO b1 = new VMesBoxNoNewDTO();
        inforList.add(b1);
        PowerMockito.when(vMesBoxNoNewRepository.getBoxItemIssueInfo(Mockito.any()))
                .thenReturn(inforList);
        List<VMesBoxNoNewDTO> result = new ArrayList<>();
        Exception exception = new Exception("");
        PowerMockito.when(wmsRemoteService.sendToWms(Mockito.anyList(), Mockito.any()))
                .thenReturn(new ArrayList<>());

        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.any(), Mockito.anyMap(), Mockito.any(),
                        Mockito.any()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": [\n" +
                        "   \n" +
                        "  ]\n" +
                        "}");
        String jsonString = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": {\n" +
                "    \"isAllSuccess\": \"true\",\n" +
                "    \"syncFailedIncomingDetails\": [\n" +
                "    \n" +
                "  ]\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(jsonString);
        Assert.assertNotNull(vMesBoxNoNewDTO);
        List<List<VMesBoxNoNewDTO>> vMesListOfList = new ArrayList<>();
        List<List<String>> serialKeyListOfList = new ArrayList<>();
        List<String> listOfString = new ArrayList<>();
        serialKeyListOfList.add(listOfString);
        listOfString.add("202507210001");
        vMesListOfList.add(inforList);
        PowerMockito.when(CommonUtils.splitList((List<String>)Mockito.any(), Mockito.anyInt())).thenReturn(serialKeyListOfList);
        PowerMockito.when(CommonUtils.splitList((List<VMesBoxNoNewDTO>)Mockito.any(), Mockito.anyInt())).thenReturn(vMesListOfList);
        PowerMockito.when(CommonUtils.getLmbMessage((String) Mockito.any(), (String) Mockito.any())).thenReturn("error");
        vMesBoxNoNewDTO.setSerialKey("202507210001");
        b1.setSerialKey("202507210001");

        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn(jsonString);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);

        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(json.asBoolean()).thenReturn(false);
        PowerMockito.when(json.toString()).thenReturn(inforList.toString());
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }

        PowerMockito.when(json.asBoolean()).thenReturn(true);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
        b1.setExternalOrderkey2("test01");
        b1.setExternOrderkey("test01");
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
        b1.setExternOrderkey("test02");
        b1.setAllowOverPick(new BigDecimal("1"));
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(test);
        PowerMockito.when(test.setIfAbsent(Mockito.any(),Mockito.any(),
                        Mockito.anyLong(),Mockito.any()))
                .thenReturn(false);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }

        PowerMockito.when(wmsRemoteService.sendToWms(Mockito.anyList(), Mockito.any()))
                .thenReturn(null);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
        PowerMockito.when(JsonConvertUtil.jsonToBean(Mockito.any(), Mockito.any(),Mockito.any()))
                .thenReturn(inforList);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
        PowerMockito.when(centerfactoryRemoteService.getSyncInfoFromTemp(Mockito.any()))
                .thenReturn(inforList);
        try{
            vMesBoxNoNewServiceImpl.syncBoxItemIssueInfo(vMesBoxNoNewDTO);
        }catch (Exception e) {
            Assert.assertNotNull(vMesBoxNoNewDTO);
        }
    }

    @Test
    public void rePushTemporaryData() throws Exception {
        VMesBoxNoNewDTO vMesBoxNoNewDTO = new VMesBoxNoNewDTO();
        List<VMesBoxNoNewDTO> inforList = new ArrayList<>();
        List<VMesBoxNoNewDTO> hisList = new ArrayList<>();
        VMesBoxNoNewDTO b1 = new VMesBoxNoNewDTO();
        b1.setSerialKey("1");
        b1.setExternalOrderkey2("test1");
        b1.setFormId("test1");
        inforList.add(b1);
        hisList.add(b1);
        VMesBoxNoNewDTO b2 = new VMesBoxNoNewDTO();
        b2.setSerialKey("2");
        b2.setExternalOrderkey2("test1");
        b2.setFormId("test2");
        inforList.add(b2);
        hisList.add(b2);
        VMesBoxNoNewDTO b3 = new VMesBoxNoNewDTO();
        b3.setSerialKey("3");
        b3.setExternalOrderkey2("test1");
        b3.setFormId("test3");
        inforList.add(b3);
        PowerMockito.when(centerfactoryRemoteService.getSyncInfoFromTemp(Mockito.any()))
                .thenReturn(inforList);
        PowerMockito.when(centerfactoryRemoteService.getHisInfoBySerialKeyList(Mockito.any()))
                .thenReturn(hisList);
        PowerMockito.when(wmsRemoteService.sendToWms(Mockito.any(),Mockito.any()))
                .thenReturn(inforList);
        vMesBoxNoNewServiceImpl.rePushTemporaryData(vMesBoxNoNewDTO);
        Assert.assertNotNull(vMesBoxNoNewDTO);
    }
}