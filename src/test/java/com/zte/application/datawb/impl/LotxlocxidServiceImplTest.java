/*Started by AICoder, pid:22d82r15206784714c3a0b9c20482959ced83e3f*/
package com.zte.application.datawb.impl;

import com.zte.application.datawb.BaItemBrandstyleService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.BaItemBrandstyle;
import com.zte.domain.model.datawb.BaItemBrandstyleRepository;
import com.zte.domain.model.infordt.Lotxlocxid;
import com.zte.domain.model.infordt.LotxlocxidRepository;
import com.zte.interfaces.infordt.dto.LotxlocxidDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class LotxlocxidServiceImplTest extends PowerBaseTestCase {

    @Mock
    private LotxlocxidRepository lotxlocxidRepository;
    @Mock
    private BaItemBrandstyleRepository baItemBrandstyleRepository;
    @InjectMocks
    private LotxlocxidServiceImpl lotxlocxidServiceImpl;
    @Mock
    private BaItemBrandstyleService baItemBrandstyleService;

    @Before
    public void before() {
    }

    @Test
    public void getStockQty() {
        LotxlocxidDTO lotxlocxidDTO = new LotxlocxidDTO();
        try {
            lotxlocxidServiceImpl.getStockQty(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        List<String> wisIdList = new ArrayList<>();
        for (int i = 0; i < 110; i++) {
            wisIdList.add("WMWHSE" + i);
        }
        lotxlocxidDTO.setWisIdList(wisIdList);
        try {
            lotxlocxidServiceImpl.getStockQty(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        lotxlocxidDTO.setWisIdList(Arrays.asList("WMWHSE1*","WMWHSE2","WMWHSE3"));
        try {
            lotxlocxidServiceImpl.getStockQty(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SKU_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        List<String> skuList = new ArrayList<>();
        for (int i = 0; i < 110; i++) {
            skuList.add("00112345" + i);
        }
        lotxlocxidDTO.setSkuList(skuList);
        try {
            lotxlocxidServiceImpl.getStockQty(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SKU_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        lotxlocxidDTO.setSkuList(Arrays.asList("047020900023","045020200075"));
        try {
            lotxlocxidServiceImpl.getStockQty(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_CAN_ONLY_CONTAINS_NUMBER_LETTER_UNDERLINE, e.getExMsgId());
        }

        lotxlocxidDTO.setWisIdList(Arrays.asList("WMWHSE1","WMWHSE2","WMWHSE3"));
        List<Lotxlocxid> expectedResult = new ArrayList<>();
        when(lotxlocxidRepository.getStockQty(lotxlocxidDTO)).thenReturn(expectedResult);
        List<Lotxlocxid> result = lotxlocxidServiceImpl.getStockQty(lotxlocxidDTO);
        assertEquals(expectedResult, result);
    }

    @Test
    public void getStockQtyWithWisId() {
        LotxlocxidDTO lotxlocxidDTO = new LotxlocxidDTO();
        try {
            lotxlocxidServiceImpl.getStockQtyWithWisId(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        List<String> wisIdList = new ArrayList<>();
        for (int i = 0; i < 110; i++) {
            wisIdList.add("WMWHSE" + i);
        }
        lotxlocxidDTO.setWisIdList(wisIdList);
        try {
            lotxlocxidServiceImpl.getStockQtyWithWisId(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        lotxlocxidDTO.setWisIdList(Arrays.asList("WMWHSE1*","WMWHSE2","WMWHSE3"));
        try {
            lotxlocxidServiceImpl.getStockQtyWithWisId(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SKU_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        List<String> skuList = new ArrayList<>();
        for (int i = 0; i < 110; i++) {
            skuList.add("00112345" + i);
        }
        lotxlocxidDTO.setSkuList(skuList);
        try {
            lotxlocxidServiceImpl.getStockQtyWithWisId(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SKU_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        lotxlocxidDTO.setSkuList(Arrays.asList("047020900023","045020200075"));
        try {
            lotxlocxidServiceImpl.getStockQtyWithWisId(lotxlocxidDTO);
            Assert.assertTrue(1 == 2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_CAN_ONLY_CONTAINS_NUMBER_LETTER_UNDERLINE, e.getExMsgId());
        }

        lotxlocxidDTO.setWisIdList(Arrays.asList("WMWHSE1","WMWHSE2","WMWHSE3"));
        List<Lotxlocxid> expectedResult = new ArrayList<>();
        when(lotxlocxidRepository.getStockQty(lotxlocxidDTO)).thenReturn(expectedResult);
        List<Lotxlocxid> result = lotxlocxidServiceImpl.getStockQtyWithWisId(lotxlocxidDTO);
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetStockQtyByBrand() {
        LotxlocxidDTO lotxlocxidDTO = new LotxlocxidDTO();
        try {
            lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }
        List<String> wisIdList = new ArrayList<>();
        for (int i = 0; i < 110; i++) {
            wisIdList.add("WMWHSE" + i);
        }
        lotxlocxidDTO.setWisIdList(wisIdList);
        try {
            lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        lotxlocxidDTO.setWisIdList(Arrays.asList("WMWHSE1*","WMWHSE2","WMWHSE3"));
        try {
            lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SKU_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }
        List<String> skuList = new ArrayList<>();
        for (int i = 0; i < 110; i++) {
            skuList.add("00112345" + i);
        }
        lotxlocxidDTO.setBrandList(skuList);
        try {
            lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SKU_LIST_IS_EMPTY_OR_EXCEED_MAX, e.getExMsgId());
        }

        lotxlocxidDTO.setBrandList(Arrays.asList("047020900023","045020200075"));
        try {
            lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.WIS_ID_CAN_ONLY_CONTAINS_NUMBER_LETTER_UNDERLINE, e.getExMsgId());
        }

        lotxlocxidDTO.setWisIdList(Arrays.asList("WMWHSE1","WMWHSE2","WMWHSE3"));
        List<BaItemBrandstyle> brandStyles = new ArrayList<>();
        when(baItemBrandstyleRepository.batchQueryItemBrandByBrands(any())).thenReturn(brandStyles);
        lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        BaItemBrandstyle brandstyle = new BaItemBrandstyle();
        brandStyles.add(brandstyle);
        lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);

        List<Lotxlocxid> expectedResult = new ArrayList<>();
        when(lotxlocxidRepository.getStockQtyByBrand(lotxlocxidDTO)).thenReturn(expectedResult);
        lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        expectedResult.add(new Lotxlocxid());
        List<Lotxlocxid> result = lotxlocxidServiceImpl.getStockQtyByBrand(lotxlocxidDTO);
        assertEquals(expectedResult, result);
    }
}
/*Ended by AICoder, pid:22d82r15206784714c3a0b9c20482959ced83e3f*/