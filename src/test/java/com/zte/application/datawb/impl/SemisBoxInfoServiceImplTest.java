package com.zte.application.datawb.impl;

import com.zte.domain.model.datawb.SemisBoxInfo;
import com.zte.domain.model.datawb.SemisBoxInfoRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class SemisBoxInfoServiceImplTest extends BaseTestCase{
    @InjectMocks
    private SemisBoxInfoServiceImpl semisBoxInfoServiceImpl;
    @Mock
    private SemisBoxInfoRepository semisBoxInfoRepository;
    @Test
    public void selectSemisBoxInfoByApplyIds() {
        List<SemisBoxInfo> list= new ArrayList<>();
        List<Long> listParam=new ArrayList<>();
        List<Long> listParam1=new ArrayList<>();
        listParam.add(new Long(6809578));
        SemisBoxInfo semisBoxInfo=new SemisBoxInfo();
        semisBoxInfo.setApplyId(new Long(6809578));
        list.add(semisBoxInfo);
        PowerMockito.when(semisBoxInfoRepository.selectSemisBoxInfoByApplyIds(Mockito.anyList())).thenReturn(list);
        semisBoxInfoServiceImpl.selectSemisBoxInfoByApplyIds(listParam);
        Assert.assertNotNull(semisBoxInfoServiceImpl.selectSemisBoxInfoByApplyIds(listParam1));
    }
}