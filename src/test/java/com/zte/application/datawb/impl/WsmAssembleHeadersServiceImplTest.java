package com.zte.application.datawb.impl;

import com.zte.domain.model.WsmAssembleHeaders;
import com.zte.domain.model.WsmAssembleHeadersRepository;
import com.zte.domain.model.WsmAssembleLinesWriteBack;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.AssemblyWriteBackDto;
import com.zte.interfaces.dto.MtlSystemItemsDTO;
import com.zte.interfaces.dto.SendAssemblyToMesDTO;
import com.zte.interfaces.dto.WsmAssembleLinesDTO;
import com.zte.util.BaseTestCase;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
public class WsmAssembleHeadersServiceImplTest extends BaseTestCase {
    @InjectMocks
    WsmAssembleHeadersServiceImpl wsmAssembleHeadersService;

    @Mock
    private WsmAssembleHeadersRepository wsmAssembleHeadersRepository;

    @Mock
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Test
    public void batchInsertAssemble() {
        wsmAssembleHeadersService.batchInsertAssemble(new ArrayList<>());
        List<AssemblyWriteBackDto> dtoList =new ArrayList<>();
        AssemblyWriteBackDto assemblyWriteBackDto =new AssemblyWriteBackDto();
        dtoList.add(assemblyWriteBackDto);
        wsmAssembleHeadersService.batchInsertAssemble(dtoList);
        WsmAssembleHeaders mainSn = new WsmAssembleHeaders();
        assemblyWriteBackDto.setMainSn(mainSn);
        wsmAssembleHeadersService.batchInsertAssemble(dtoList);
        mainSn.setItemBarcode("test123");
        List<WsmAssembleLinesWriteBack> wsmAssembleLinesWriteBackList = new ArrayList<>();
        assemblyWriteBackDto.setSubSnList(wsmAssembleLinesWriteBackList);
        PowerMockito.when(wsmAssembleHeadersRepository.getWsmAssembleHeadersByItemBarCode(Mockito.any())).thenReturn(null);
        PowerMockito.when(wsmAssembleHeadersRepository.getHeadersId()).thenReturn(new BigDecimal("10"));
        PowerMockito.when(wsmAssembleHeadersRepository.insertWsmAssembleHeadersSelective(Mockito.any())).thenReturn(1);
        PowerMockito.when(wsmAssembleLinesRepository.batchInsertAssemble(Mockito.any())).thenReturn(1);
        wsmAssembleHeadersService.batchInsertAssemble(dtoList);
        WsmAssembleLinesWriteBack wsmAssembleLinesDTO=new WsmAssembleLinesWriteBack();
        wsmAssembleLinesDTO.setAssembleHeadersId(new BigDecimal("123"));
        wsmAssembleLinesWriteBackList.add(wsmAssembleLinesDTO);
        wsmAssembleHeadersService.batchInsertAssemble(dtoList);
        PowerMockito.when(wsmAssembleHeadersRepository.getWsmAssembleHeadersByItemBarCode(Mockito.any())).thenReturn(new BigDecimal("10"));
        wsmAssembleHeadersService.batchInsertAssemble(dtoList);
        Assert.assertNotNull(dtoList);
    }

    @Test
    public void batchDeleteAssemble() {
        wsmAssembleHeadersService.batchDeleteAssemble(new ArrayList<>());
        List<AssemblyWriteBackDto> dtoList =new ArrayList<>();
        AssemblyWriteBackDto assemblyWriteBackDto =new AssemblyWriteBackDto();
        dtoList.add(assemblyWriteBackDto);
        WsmAssembleHeaders mainSn = new WsmAssembleHeaders();
        assemblyWriteBackDto.setMainSn(mainSn);
        wsmAssembleHeadersService.batchDeleteAssemble(dtoList);

        mainSn.setItemBarcode("test123");
        List<WsmAssembleLinesWriteBack> wsmAssembleLinesWriteBackList = new ArrayList<>();
        assemblyWriteBackDto.setSubSnList(wsmAssembleLinesWriteBackList);
        wsmAssembleHeadersService.batchDeleteAssemble(dtoList);
        WsmAssembleLinesWriteBack wsmAssembleLinesDTO=new WsmAssembleLinesWriteBack();
        wsmAssembleLinesDTO.setAssembleHeadersId(new BigDecimal("123"));
        wsmAssembleLinesWriteBackList.add(wsmAssembleLinesDTO);
        PowerMockito.when(wsmAssembleLinesRepository.removeWsmAssemblyListByMainAndSub(Mockito.any())).thenReturn(1);
        PowerMockito.when(wsmAssembleLinesRepository.getWsmAssemblyListByMainAndSub(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(wsmAssembleHeadersRepository.updateWsmAssembleHeadersByItemBarcode(Mockito.any())).thenReturn(1);
        wsmAssembleHeadersService.batchDeleteAssemble(dtoList);
        Assert.assertNotNull(dtoList);
    }

    @Test
    public void batchInsertOrDeleteAssemble() {
        wsmAssembleHeadersService.batchInsertOrDeleteAssemble(new SendAssemblyToMesDTO());
        SendAssemblyToMesDTO dto = new SendAssemblyToMesDTO();
        Assert.assertNotNull(dto);

    }
}