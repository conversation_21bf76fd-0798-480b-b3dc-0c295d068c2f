package com.zte.application.datawb.impl;
import com.zte.application.MesGetDictInforService;
import com.zte.common.CommonUtils;
import com.zte.domain.model.datawb.ECAssembleWriteBackRepository;
import com.zte.domain.model.datawb.ItemBaseInfo;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.interfaces.dto.BarcodeExpandDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.interfaces.dto.ECAssembleWriteBackDTO;
import com.zte.springbootframe.util.EmailUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
/**
 * ECAssembleWriteBackServiceImpl单元测试
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class ECAssembleWriteBackServiceImplTest {
    @InjectMocks
    private ECAssembleWriteBackServiceImpl ecAssembleWriteBackService;
    @Mock
    private ECAssembleWriteBackRepository repository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private MesGetDictInforService mesGetDictInforService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    private ECAssembleWriteBackDTO ecAssembleDTO;
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        PowerMockito.mockStatic(CommonUtils.class);
        // Mock CommonUtils.getLmbMessage方法
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("条码校验失败");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("条码校验失败: {0}");
        ecAssembleDTO = new ECAssembleWriteBackDTO();
        ecAssembleDTO.setRecordId("TEST_RECORD_001");
        ecAssembleDTO.setItemBarcode("ITEM_BARCODE_001");
        ecAssembleDTO.setSubItemBarcode("SUB_ITEM_BARCODE_001");
        ecAssembleDTO.setSubItemCode("SUB_ITEM_CODE_001");
    }
    /**
     * 测试writeBack方法 - 正常情况
     */
    @Test
    public void testWriteBack_Success() throws Exception {
        // 准备测试数据
        List<BarcodeExpandDTO> barcodeExpandDTOList = Arrays.asList(
                createBarcodeExpandDTO("ITEM_BARCODE_001"),
                createBarcodeExpandDTO("SUB_ITEM_BARCODE_001")
        );
        List<ItemBaseInfo> itemBaseInfoList = Arrays.asList(createItemBaseInfo());
        // Mock方法调用
        when(barcodeCenterRemoteService.expandQuery(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandDTOList);
        when(repository.getItemBaseInfo(anyString())).thenReturn(itemBaseInfoList);
        doNothing().when(repository).insertOrUpdate(any(ECAssembleWriteBackDTO.class));
        // 执行测试
        ecAssembleWriteBackService.writeBack(ecAssembleDTO);
        // 验证调用
        verify(barcodeCenterRemoteService).expandQuery(any(BarcodeExpandQueryDTO.class));
        verify(repository).getItemBaseInfo("SUB_ITEM_CODE_001");
        verify(repository).insertOrUpdate(ecAssembleDTO);
    }
    /**
     * 测试writeBack方法 - 条码校验失败
     */
    @Test
    public void testWriteBack_BarcodeValidationFailed() throws Exception {
        // 准备测试数据 - 只返回一个条码，另一个不存在
        List<BarcodeExpandDTO> barcodeExpandDTOList = Arrays.asList(
                createBarcodeExpandDTO("ITEM_BARCODE_001")
        );
        // Mock方法调用
        when(barcodeCenterRemoteService.expandQuery(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandDTOList);
        when(mesGetDictInforService.getDicDescription(anyString())).thenReturn("<EMAIL>");
        doNothing().when(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        // 执行测试并验证异常
        try {
            ecAssembleWriteBackService.writeBack(ecAssembleDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertNotNull("异常消息不能为空", e.getMessage());
            verify(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        }
    }
    /**
     * 测试writeBack方法 - 保存时发生异常
     */
    @Test
    public void testWriteBack_SaveException() throws Exception {
        // 准备测试数据
        List<BarcodeExpandDTO> barcodeExpandDTOList = Arrays.asList(
                createBarcodeExpandDTO("ITEM_BARCODE_001"),
                createBarcodeExpandDTO("SUB_ITEM_BARCODE_001")
        );
        // Mock方法调用
        when(barcodeCenterRemoteService.expandQuery(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandDTOList);
        when(repository.getItemBaseInfo(anyString())).thenThrow(new RuntimeException("数据库异常"));
        when(mesGetDictInforService.getDicDescription(anyString())).thenReturn("<EMAIL>");
        doNothing().when(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        // 执行测试并验证异常
        try {
            ecAssembleWriteBackService.writeBack(ecAssembleDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertNotNull("异常消息不能为空", e.getMessage());
            verify(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        }
    }
    /**
     * 测试check方法 - 正常情况
     */
    @Test
    public void testCheck_Success() throws Exception {
        // 准备测试数据
        List<BarcodeExpandDTO> barcodeExpandDTOList = Arrays.asList(
                createBarcodeExpandDTO("ITEM_BARCODE_001"),
                createBarcodeExpandDTO("SUB_ITEM_BARCODE_001")
        );
        // Mock方法调用
        when(barcodeCenterRemoteService.expandQuery(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandDTOList);
        // 执行测试
        ecAssembleWriteBackService.check(ecAssembleDTO);
        // 验证调用
        verify(barcodeCenterRemoteService).expandQuery(any(BarcodeExpandQueryDTO.class));
    }
    /**
     * 测试check方法 - 条码不存在
     */
    @Test
    public void testCheck_BarcodeNotExists() throws Exception {
        // 准备测试数据 - 返回空列表，表示条码都不存在
        List<BarcodeExpandDTO> barcodeExpandDTOList = new ArrayList<>();
        // Mock方法调用
        when(barcodeCenterRemoteService.expandQuery(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandDTOList);
        // 执行测试并验证异常
        try {
            ecAssembleWriteBackService.check(ecAssembleDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertNotNull("异常消息不能为空", e.getMessage());
        }
    }
    /**
     * 测试check方法 - 部分条码不存在
     */
    @Test
    public void testCheck_PartialBarcodeNotExists() throws Exception {
        // 准备测试数据 - 只返回一个条码
        List<BarcodeExpandDTO> barcodeExpandDTOList = Arrays.asList(
                createBarcodeExpandDTO("ITEM_BARCODE_001")
        );
        // Mock方法调用
        when(barcodeCenterRemoteService.expandQuery(any(BarcodeExpandQueryDTO.class)))
                .thenReturn(barcodeExpandDTOList);
        // 执行测试并验证异常
        try {
            ecAssembleWriteBackService.check(ecAssembleDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertNotNull("异常消息不能为空", e.getMessage());
        }
    }
    /**
     * 测试sendEmailAndThrowException方法
     */
    @Test
    public void testSendEmailAndThrowException() throws Exception {
        // 准备测试数据
        Exception exception = new Exception("测试异常");
        when(mesGetDictInforService.getDicDescription(anyString())).thenReturn("<EMAIL>");
        doNothing().when(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        // 执行测试并验证异常
        try {
            ecAssembleWriteBackService.sendEmailAndThrowException(exception, ecAssembleDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertNotNull("异常消息不能为空", e.getMessage());
            verify(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        }
    }

    /**
     * 测试sendEmailAndThrowException方法 - exception.getMessage()返回null的情况
     */
    @Test
    public void testSendEmailAndThrowException_WithNullMessage() throws Exception {
        // 准备测试数据 - 创建一个getMessage()返回null的异常
        Exception exception = new Exception() {
            @Override
            public String getMessage() {
                return null;
            }
        };
        when(mesGetDictInforService.getDicDescription(anyString())).thenReturn("<EMAIL>");
        doNothing().when(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        // 执行测试并验证异常
        try {
            ecAssembleWriteBackService.sendEmailAndThrowException(exception, ecAssembleDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertNotNull("异常消息不能为空", e.getMessage());
            verify(emailUtils).sendMailAsyn(anyString(), anyString(), anyString(), anyString(), anyString());
        }
    }
    /**
     * 创建BarcodeExpandDTO测试数据
     */
    private BarcodeExpandDTO createBarcodeExpandDTO(String barcode) {
        BarcodeExpandDTO dto = new BarcodeExpandDTO();
        dto.setBarcode(barcode);
        return dto;
    }
    /**
     * 创建ItemBaseInfo测试数据
     */
    private ItemBaseInfo createItemBaseInfo() {
        ItemBaseInfo itemBaseInfo = new ItemBaseInfo();
        itemBaseInfo.setItemId("ITEM_ID_001");
        itemBaseInfo.setItemName("测试物料名称");
        return itemBaseInfo;
    }
}