package com.zte.application.datawb.impl;

import com.zte.application.datawb.BomConfigCheckEnService;
import com.zte.application.datawb.ConfigBindInfoQueryService;
import com.zte.application.datawb.ECPrecastSubBarcodeService;
import com.zte.application.datawb.MaterialConfigBindService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MtlSystemItemsRepository;
import com.zte.domain.model.datawb.ConfigMaterialBindServiceRepository;
import com.zte.domain.model.datawb.MesMaterialConfigBindRepository;
import com.zte.interfaces.dto.*;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * MesMaterialConfigBindServiceImpl 单元测试类
 * 重点测试预制机相关方法
 */
@PrepareForTest({CommonUtils.class})
public class MesMaterialConfigBindServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    MesMaterialConfigBindServiceImpl mesMaterialConfigBindServiceImpl;
    @Mock
    MaterialConfigBindService materialConfigBindService;
    @Mock
    ECPrecastSubBarcodeService ecPrecastSubBarcodeService;

    @Mock
    MesMaterialConfigBindRepository mesMaterialConfigBindRepository;

    @Mock
    BomConfigCheckEnService bomConfigCheckEnService;

    @Mock
    ConfigBindInfoQueryService configBindInfoQueryService;

    @Mock
    MtlSystemItemsRepository mtlSystemItemsRepository;
    @Mock
    private ConfigMaterialBindServiceRepository configMaterialBindServiceRepository;
    
    /**
     * 测试handlePrecastSubBarcode方法 - 成功场景
     */
    @Test
    public void testhandlePrecastSubBarcode_Success() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();
        
        List<ECPrecastSubBarcodeInfo> subBarcodes = createTestSubBarcodes();
        MtlSystemItemsDTO mtlSystemItems = new MtlSystemItemsDTO();
        mtlSystemItems.setInventoryItemId(123);  // Integer类型
        mtlSystemItems.setDescription("测试物料");

        // Mock依赖
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_SUB_BARCODE_EXCEPTION))
                .thenReturn("处理预制机子条码时发生异常");
        
        // Mock 预制机组织判断
        when(ecPrecastSubBarcodeService.isPrecastOrganization(anyString(), anyString())).thenReturn(true);
        when(ecPrecastSubBarcodeService.getPrecastSubBarcodes(params)).thenReturn(subBarcodes);
        when(ecPrecastSubBarcodeService.findConfigDetailIdByItemCode(anyString(), any(), anyString(), anyString(), anyString()))
                .thenReturn("12345");
        when(ecPrecastSubBarcodeService.validateSubBarcode(anyString(), anyString())).thenReturn(true);
        when(mtlSystemItemsRepository.getBySegment1(anyString(), any())).thenReturn(mtlSystemItems);
        
        mockOtherDependencies(ent, mainCpmConfigItemAssemble);

        // 使用反射调用私有方法
        Method method = MesMaterialConfigBindServiceImpl.class.getDeclaredMethod("handlePrecastSubBarcode", 
                MaterialConfigBindInDTO.class, String.class, CpmConfigItemAssemble.class, 
                CpmConfigItemAssemble.class, Map.class);
        method.setAccessible(true);
        MessageErrorDTO result = (MessageErrorDTO) method.invoke(mesMaterialConfigBindServiceImpl, 
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(Constant.SUCCESS_CODE, result.getBindCode());
    }

    /**
     * 测试handlePrecastSubBarcode方法 - 未找到可用子条码
     */
    @Test
    public void testhandlePrecastSubBarcode_NoAvailableSubBarcode() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();

        // Mock依赖
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_SUB_BARCODE_NOT_AVAILABLE))
                .thenReturn("未找到可用的子条码");

        // Mock 预制机组织判断和返回空的子条码列表
        when(ecPrecastSubBarcodeService.isPrecastOrganization(anyString(), anyString())).thenReturn(true);
        when(ecPrecastSubBarcodeService.getPrecastSubBarcodes(params)).thenReturn(null);

        // 使用反射调用私有方法
        Method method = MesMaterialConfigBindServiceImpl.class.getDeclaredMethod("handlePrecastSubBarcode", 
                MaterialConfigBindInDTO.class, String.class, CpmConfigItemAssemble.class, 
                CpmConfigItemAssemble.class, Map.class);
        method.setAccessible(true);
        MessageErrorDTO result = (MessageErrorDTO) method.invoke(mesMaterialConfigBindServiceImpl, 
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果
        Assert.assertNotNull(result);
    }

    /**
     * 测试handlePrecastSubBarcode方法 - 预制机未绑定完成
     */
    @Test
    public void testhandlePrecastSubBarcode_BindNotCompleted() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();

        // 创建未完成的子条码
        List<ECPrecastSubBarcodeInfo> subBarcodes = new ArrayList<>();
        ECPrecastSubBarcodeInfo subBarcode = new ECPrecastSubBarcodeInfo();
        subBarcode.setSn("SUB001");
        subBarcode.setItemCode("ITEM001");
        subBarcode.setIsFinish("N"); // 未完成绑定
        subBarcodes.add(subBarcode);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_BIND_NOT_COMPLETED))
                .thenReturn("预制机未绑定完成，禁止扫描");

        // Mock 预制机组织判断
        when(ecPrecastSubBarcodeService.isPrecastOrganization(anyString(), anyString())).thenReturn(true);
        when(ecPrecastSubBarcodeService.getPrecastSubBarcodes(params)).thenReturn(subBarcodes);

        // 使用反射调用私有方法
        Method method = MesMaterialConfigBindServiceImpl.class.getDeclaredMethod("handlePrecastSubBarcode", 
                MaterialConfigBindInDTO.class, String.class, CpmConfigItemAssemble.class, 
                CpmConfigItemAssemble.class, Map.class);
        method.setAccessible(true);
        MessageErrorDTO result = (MessageErrorDTO) method.invoke(mesMaterialConfigBindServiceImpl, 
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(Constant.BUSINESS_ERROR_CODE, result.getBindCode());
        Assert.assertEquals("预制机未绑定完成，禁止扫描", result.getBindMessage());
    }

    /**
     * 测试handlePrecastSubBarcode方法 - 物料代码不在配置明细中
     */
    @Test
    public void testhandlePrecastSubBarcode_ItemCodeNotInConfig() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();

        List<ECPrecastSubBarcodeInfo> subBarcodes = createTestSubBarcodes();

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_ITEM_CODE_NOT_IN_CONFIG))
                .thenReturn("物料代码在配置明细中不存在");

        // Mock 预制机组织判断
        when(ecPrecastSubBarcodeService.isPrecastOrganization(anyString(), anyString())).thenReturn(true);
        when(ecPrecastSubBarcodeService.getPrecastSubBarcodes(params)).thenReturn(subBarcodes);
        // Mock返回null，表示找不到配置详情ID
        when(ecPrecastSubBarcodeService.findConfigDetailIdByItemCode(anyString(), any(), anyString(), anyString(), anyString()))
                .thenReturn(null);

        // 使用反射调用私有方法
        Method method = MesMaterialConfigBindServiceImpl.class.getDeclaredMethod("handlePrecastSubBarcode", 
                MaterialConfigBindInDTO.class, String.class, CpmConfigItemAssemble.class, 
                CpmConfigItemAssemble.class, Map.class);
        method.setAccessible(true);
        MessageErrorDTO result = (MessageErrorDTO) method.invoke(mesMaterialConfigBindServiceImpl, 
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(Constant.BUSINESS_ERROR_CODE, result.getBindCode());
        Assert.assertEquals("物料代码在配置明细中不存在", result.getBindMessage());
    }

    /**
     * 测试handlePrecastSubBarcode方法 - 主子条码相同
     */
    @Test
    public void testhandlePrecastSubBarcode_SameMainAndSubBarcode() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();

        List<ECPrecastSubBarcodeInfo> subBarcodes = createTestSubBarcodes();
        // 设置子条码与主条码相同
        subBarcodes.get(0).setSn(params.getMainBarcode());

        // Mock CommonUtils.getLmbMessage - 添加所有可能用到的消息常量
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_ITEM_CODE_NOT_IN_CONFIG))
                .thenReturn("物料代码在配置明细中不存在");
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_SUB_BARCODE_EXCEPTION))
                .thenReturn("预制机子条码处理异常");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString()))
                .thenReturn("通用错误消息");

        // Mock 预制机组织判断
        when(ecPrecastSubBarcodeService.isPrecastOrganization(anyString(), anyString())).thenReturn(true);
        when(ecPrecastSubBarcodeService.getPrecastSubBarcodes(params)).thenReturn(subBarcodes);
        when(ecPrecastSubBarcodeService.findConfigDetailIdByItemCode(anyString(), any(), anyString(), anyString(), anyString()))
                .thenReturn("12345");
        when(ecPrecastSubBarcodeService.validateSubBarcode(anyString(), anyString())).thenReturn(false);

        // 使用反射调用私有方法
        Method method = MesMaterialConfigBindServiceImpl.class.getDeclaredMethod("handlePrecastSubBarcode", 
                MaterialConfigBindInDTO.class, String.class, CpmConfigItemAssemble.class, 
                CpmConfigItemAssemble.class, Map.class);
        method.setAccessible(true);
        MessageErrorDTO result = (MessageErrorDTO) method.invoke(mesMaterialConfigBindServiceImpl, 
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(Constant.BUSINESS_ERROR_CODE, result.getBindCode());
    }

    /**
     * 测试handlePrecastSubBarcode方法 - 异常处理
     */
    @Test
    public void testhandlePrecastSubBarcode_Exception() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.PRECAST_SUB_BARCODE_EXCEPTION))
                .thenReturn("处理预制机子条码时发生异常");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString()))
                .thenReturn("通用错误消息");

        // Mock 预制机组织判断
        when(ecPrecastSubBarcodeService.isPrecastOrganization(anyString(), anyString())).thenReturn(true);
        // Mock抛出异常
        when(ecPrecastSubBarcodeService.getPrecastSubBarcodes(params))
                .thenThrow(new RuntimeException("模拟异常"));

        // 使用反射调用私有方法
        Method method = MesMaterialConfigBindServiceImpl.class.getDeclaredMethod("handlePrecastSubBarcode", 
                MaterialConfigBindInDTO.class, String.class, CpmConfigItemAssemble.class, 
                CpmConfigItemAssemble.class, Map.class);
        method.setAccessible(true);
        MessageErrorDTO result = (MessageErrorDTO) method.invoke(mesMaterialConfigBindServiceImpl, 
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(Constant.BUSINESS_ERROR_CODE, result.getBindCode());
    }

    /**
     * 测试非预制机组织的情况
     */
    @Test
    public void testMesMaterialConfigBindSub_NonPrecastOrganization() throws Exception {
        // 准备测试数据
        MaterialConfigBindInDTO params = createTestParams();
        String empNo = "10289101";
        CpmConfigItemAssemble ent = new CpmConfigItemAssemble();
        CpmConfigItemAssemble mainCpmConfigItemAssemble = new CpmConfigItemAssemble();
        Map<String, Object> map = new HashMap<>();

        // Mock 非预制机组织
        when(ecPrecastSubBarcodeService.isPrecastOrganization(params.getOrgID(), params.getYzjFlag())).thenReturn(false);
        
        // Mock其他必要的依赖
        mockOtherDependencies(ent, mainCpmConfigItemAssemble);

        // 调用方法
        MessageErrorDTO result = mesMaterialConfigBindServiceImpl.mesMaterialConfigBindSub(
                params, empNo, ent, mainCpmConfigItemAssemble, map);

        // 验证结果（应该走正常流程）
        Assert.assertNotNull(result);
    }

    /**
     * 测试buildBarcodeInfo方法 - 有物料信息
     */
    @Test
    public void testBuildBarcodeInfo_WithMtlSystemItems() throws Exception {
        ECPrecastSubBarcodeInfo selectedBarcode = new ECPrecastSubBarcodeInfo();
        selectedBarcode.setSn("TEST_SN_123");
        selectedBarcode.setItemCode("TEST_ITEM_001");

        MtlSystemItemsDTO mtlSystemItems = new MtlSystemItemsDTO();
        mtlSystemItems.setInventoryItemId(12345);  // 修正为Integer类型
        mtlSystemItems.setDescription("Test Item Description");

        // 由于buildBarcodeInfo是private方法，我们通过反射来测试
        java.lang.reflect.Method method = MesMaterialConfigBindServiceImpl.class
                .getDeclaredMethod("buildBarcodeInfo", ECPrecastSubBarcodeInfo.class, MtlSystemItemsDTO.class);
        method.setAccessible(true);

        BasBarcodeInfo result = (BasBarcodeInfo) method.invoke(
                mesMaterialConfigBindServiceImpl, selectedBarcode, mtlSystemItems);

        Assert.assertNotNull(result);
        Assert.assertEquals("TEST_SN_123", result.getItemBarcode());
        Assert.assertEquals("TEST_ITEM_001", result.getItemCode());
        Assert.assertEquals("12345", result.getItemId());
        Assert.assertEquals("Test Item Description", result.getItemName());
        Assert.assertEquals(Constant.EMPTY_NO_NO, result.getSeriesNo());
    }

    /**
     * 测试buildBarcodeInfo方法 - 无物料信息
     */
    @Test
    public void testBuildBarcodeInfo_WithoutMtlSystemItems() throws Exception {
        ECPrecastSubBarcodeInfo selectedBarcode = new ECPrecastSubBarcodeInfo();
        selectedBarcode.setSn("TEST_SN_123");
        selectedBarcode.setItemCode("TEST_ITEM_001");

        // 由于buildBarcodeInfo是private方法，我们通过反射来测试
        java.lang.reflect.Method method = MesMaterialConfigBindServiceImpl.class
                .getDeclaredMethod("buildBarcodeInfo", ECPrecastSubBarcodeInfo.class, MtlSystemItemsDTO.class);
        method.setAccessible(true);

        BasBarcodeInfo result = (BasBarcodeInfo) method.invoke(
                mesMaterialConfigBindServiceImpl, selectedBarcode, null);

        Assert.assertNotNull(result);
        Assert.assertEquals("TEST_SN_123", result.getItemBarcode());
        Assert.assertEquals("TEST_ITEM_001", result.getItemCode());
        Assert.assertEquals("0", result.getItemId());
        Assert.assertEquals("TEST_ITEM_001", result.getItemName());
        Assert.assertEquals(Constant.EMPTY_NO_NO, result.getSeriesNo());
    }

    /**
     * 测试validatePrecastEnvironment方法
     */
    @Test
    public void testValidatePrecastEnvironment() throws Exception {
        MaterialConfigBindInDTO params = createTestParams();
        ECPrecastSubBarcodeInfo subBarcode = new ECPrecastSubBarcodeInfo();
        subBarcode.setSn("TEST_SN");
        subBarcode.setEnvAttr("ROHS");
        String empNo = "12345678";

        // 由于validatePrecastEnvironment是private方法，我们通过反射来测试
        java.lang.reflect.Method method = MesMaterialConfigBindServiceImpl.class
                .getDeclaredMethod("validatePrecastEnvironment", 
                        MaterialConfigBindInDTO.class, ECPrecastSubBarcodeInfo.class, String.class);
        method.setAccessible(true);

        MessageErrorDTO result = (MessageErrorDTO) method.invoke(
                mesMaterialConfigBindServiceImpl, params, subBarcode, empNo);

        Assert.assertNotNull(result);
        Assert.assertEquals(Constant.SUCCESS_CODE, result.getBindCode());
        Assert.assertEquals(Constant.OPERATE_SUCCESS, result.getBindMessage());
    }

    // ============ 辅助方法 ============

    /**
     * 创建测试参数
     */
    private MaterialConfigBindInDTO createTestParams() {
        MaterialConfigBindInDTO params = new MaterialConfigBindInDTO();
        params.setMainBarcode("MAIN_123456");
        params.setSubBarcode("SUB_123456");
        params.setOrgID("12345");
        params.setMfgSiteId("67890");
        params.setEntityName("TEST_ENTITY");
        params.setOperationName("TEST_OP");
        params.setMainConfigDetailID("11111");
        params.setSubConfigDetailID("22222");
        params.setYzjFlag("Y");
        return params;
    }

    /**
     * 创建测试子条码列表
     */
    private List<ECPrecastSubBarcodeInfo> createTestSubBarcodes() {
        List<ECPrecastSubBarcodeInfo> subBarcodes = new ArrayList<>();
        
        ECPrecastSubBarcodeInfo barcode1 = new ECPrecastSubBarcodeInfo();
        barcode1.setSn("SUB_123456");
        barcode1.setItemCode("ITEM_001");
        barcode1.setSnType("0");  // 使用snType字段而不是snTypeDesc
        barcode1.setQty(new BigDecimal(1));
        barcode1.setEnvAttr("ROHS");
        barcode1.setIsFinish("Y");  // 使用isFinish字段而不是setFinished或setAvailable
        
        subBarcodes.add(barcode1);
        return subBarcodes;
    }

    /**
     * Mock其他依赖方法
     */
    private void mockOtherDependencies(CpmConfigItemAssemble ent, CpmConfigItemAssemble mainCpmConfigItemAssemble) throws Exception {
        // Mock setSubBarcodeInfo
        MessageErrorDTO successResult = new MessageErrorDTO();
        successResult.setBindCode(Constant.SUCCESS_CODE);
        successResult.setBindMessage(Constant.OPERATE_SUCCESS);

        // Mock checkSubItemBoqBom - 添加对organizationId参数的支持
        when(configBindInfoQueryService.checkSubItemBoqBom(
                any(), any(), any(), any(), anyBoolean()))
                .thenReturn(successResult);

        // Mock saveSubBarcode 方法需要通过反射或其他方式模拟
        ent.setRecordId("12345");
        mainCpmConfigItemAssemble.setRecordId("67890");
    }
} 