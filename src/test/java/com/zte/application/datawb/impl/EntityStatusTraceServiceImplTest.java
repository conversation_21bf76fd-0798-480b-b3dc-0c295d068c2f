package com.zte.application.datawb.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.EntityStatusTraceRepository;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.domain.vo.datawb.EntitySubStatusVO;
import com.zte.infrastructure.remote.CenterfactoryRemoteService;
import com.zte.interfaces.dto.EntitySubStatusDTO;
import com.zte.interfaces.dto.centerfactory.HrmPersonInfoDTO;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * EntityStatusTraceServiceImpl单元测试
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MESHttpHelper.class})
public class EntityStatusTraceServiceImplTest {

    @InjectMocks
    private EntityStatusTraceServiceImpl entityStatusTraceService;

    @Mock
    private EntityStatusTraceRepository repository;

    @Mock
    private WsmAssembleLinesRepository lookupRepository;

    @Mock
    private CenterfactoryRemoteService hrService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        PowerMockito.mockStatic(MESHttpHelper.class);
    }

    /**
     * 测试updateSubStatus方法 - 正常情况
     */
    @Test
    public void testUpdateSubStatus_Success() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001", "TASK002"));
        entitySubStatusDTO.setSubStatus("进行中");

        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始");
        EntitySubStatusVO vo2 = createEntitySubStatusVO("CT001", "DS001", "TASK002", "李四", "待开始");
        entitySubStatusList.add(vo1);
        entitySubStatusList.add(vo2);

        // Mock方法调用
        when(repository.getIntegralEntityInfo(any(EntitySubStatusDTO.class))).thenReturn(entitySubStatusList);
        when(lookupRepository.getSysLookupValues(anyString())).thenReturn(createSysLookupValuesList());
        when(repository.getSessionId()).thenReturn(12345L);
        doNothing().when(repository).insert2EntityTraceTemp(anyLong(), anyList());
        doNothing().when(repository).callCheckEntityInfo(anyLong(), anyString(), anyString(), anyString());
        when(repository.getUpdateErrList(anyLong())).thenReturn(new ArrayList<>());

        // Mock HTTP请求头
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("X-Emp-No", "1024778");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        // Mock HR服务
        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("张三");
        hrmPersonInfoMap.put("1024778", hrmPersonInfoDTO);
        when(hrService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoMap);

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.updateSubStatus(entitySubStatusDTO);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertTrue("应该返回空列表（表示全部成功）", result.isEmpty());

        // 验证调用
        verify(repository).getIntegralEntityInfo(entitySubStatusDTO);
        verify(lookupRepository).getSysLookupValues(Constant.LOOKUP_VALUE_2020047);
        verify(repository).getSessionId();
        verify(repository).callCheckEntityInfo(12345L, "1024778", "张三", null);
        verify(repository).getUpdateErrList(12345L);
    }

    /**
     * 测试updateSubStatus方法 - 参数校验失败
     */
    @Test
    public void testUpdateSubStatus_ParameterValidationFailed() throws Exception {
        // 准备测试数据 - 交付集和任务号都为空
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setDeliverSetNum(null);
        entitySubStatusDTO.setEntityNoList(null);
        entitySubStatusDTO.setSubStatus("进行中");

        // 执行测试并验证异常
        try {
            entityStatusTraceService.updateSubStatus(entitySubStatusDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertEquals("异常消息应该匹配", Constant.ERROR_SET_AND_ENTITY, e.getMessage());
        }
    }

    /**
     * 测试updateSubStatus方法 - 未找到数据
     */
    @Test
    public void testUpdateSubStatus_NoDataFound() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001"));
        entitySubStatusDTO.setSubStatus("进行中");

        // Mock方法调用 - 返回空列表
        when(repository.getIntegralEntityInfo(any(EntitySubStatusDTO.class))).thenReturn(new ArrayList<>());

        // 执行测试并验证异常
        try {
            entityStatusTraceService.updateSubStatus(entitySubStatusDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertEquals("异常消息应该匹配", Constant.NO_DATA_BY_SET_OR_ENTITY, e.getMessage());
        }
    }

    /**
     * 测试updateSubStatus方法 - 部分任务号不存在
     */
    @Test
    public void testUpdateSubStatus_SomeEntityNotExist() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001", "TASK999"));
        entitySubStatusDTO.setSubStatus("进行中");

        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始");
        entitySubStatusList.add(vo1);

        // Mock方法调用
        when(repository.getIntegralEntityInfo(any(EntitySubStatusDTO.class))).thenReturn(entitySubStatusList);
        when(lookupRepository.getSysLookupValues(anyString())).thenReturn(createSysLookupValuesList());

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.updateSubStatus(entitySubStatusDTO);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("应该返回1个错误", 1, result.size());
        assertEquals("错误消息应该匹配", Constant.ENTITY_NO_NOT_EXIST, result.get(0).getDealRes());
        assertEquals("任务号应该匹配", "TASK999", result.get(0).getEntityNo());
    }

    /**
     * 测试updateSubStatus方法 - 非法状态变更
     */
    @Test
    public void testUpdateSubStatus_IllegalStatusChange() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001"));
        entitySubStatusDTO.setSubStatus("已完成");

        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "已完成");
        entitySubStatusList.add(vo1);

        // Mock方法调用
        when(repository.getIntegralEntityInfo(any(EntitySubStatusDTO.class))).thenReturn(entitySubStatusList);
        when(lookupRepository.getSysLookupValues(anyString())).thenReturn(createSysLookupValuesList());

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.updateSubStatus(entitySubStatusDTO);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("应该返回1个错误", 1, result.size());
        assertTrue("错误消息应该包含非法状态变更信息",
                result.get(0).getDealRes().contains(Constant.ILLEGAL_SUB_STATUS_CHANGE));
    }

    /**
     * 测试checkParam方法 - 正常情况
     */
    @Test
    public void testCheckParam_Success() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001"));
        entitySubStatusDTO.setSubStatus("进行中");

        // 执行测试 - 不应该抛出异常
        entityStatusTraceService.checkParam(entitySubStatusDTO);
        
        // 验证方法正常执行，没有抛出异常
        // 如果方法执行到这里，说明参数校验通过
        assertTrue("参数校验应该通过", true);
    }

    /**
     * 测试checkParam方法 - 参数校验失败
     */
    @Test
    public void testCheckParam_ValidationFailed() throws Exception {
        // 准备测试数据 - 交付集和任务号都为空
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setDeliverSetNum("123");
        entitySubStatusDTO.setEntityNoList(null);

        entityStatusTraceService.checkParam(entitySubStatusDTO);
        entitySubStatusDTO.setDeliverSetNum(null);
        // 执行测试并验证异常
        try {
            entityStatusTraceService.checkParam(entitySubStatusDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertEquals("异常消息应该匹配", Constant.ERROR_SET_AND_ENTITY, e.getMessage());
        }

    }

    /**
     * 测试getIntegralEntityInfo方法 - 正常情况
     */
    @Test
    public void testGetIntegralEntityInfo_Success() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001"));

        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        entitySubStatusList.add(createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始"));

        // Mock方法调用
        when(repository.getIntegralEntityInfo(any(EntitySubStatusDTO.class))).thenReturn(entitySubStatusList);

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.getIntegralEntityInfo(entitySubStatusDTO);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("应该返回1条记录", 1, result.size());
        assertEquals("任务号应该匹配", "TASK001", result.get(0).getEntityNo());
    }

    /**
     * 测试getIntegralEntityInfo方法 - 未找到数据
     */
    @Test
    public void testGetIntegralEntityInfo_NoDataFound() throws Exception {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001"));

        // Mock方法调用 - 返回空列表
        when(repository.getIntegralEntityInfo(any(EntitySubStatusDTO.class))).thenReturn(new ArrayList<>());

        // 执行测试并验证异常
        try {
            entityStatusTraceService.getIntegralEntityInfo(entitySubStatusDTO);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertEquals("异常消息应该匹配", Constant.NO_DATA_BY_SET_OR_ENTITY, e.getMessage());
        }
    }

    /**
     * 测试getErrEntitySubStatusList方法 - 任务号不存在
     */
    @Test
    public void testGetErrEntitySubStatusList_EntityNotExist() {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001", "TASK999"));
        entitySubStatusDTO.setSubStatus("进行中");

        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始");
        entitySubStatusList.add(vo1);

        // Mock方法调用
        when(lookupRepository.getSysLookupValues(anyString())).thenReturn(createSysLookupValuesList());

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.getErrEntitySubStatusList(entitySubStatusList, entitySubStatusDTO);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("应该返回1个错误", 1, result.size());
        assertEquals("错误消息应该匹配", Constant.ENTITY_NO_NOT_EXIST, result.get(0).getDealRes());
        assertEquals("任务号应该匹配", "TASK999", result.get(0).getEntityNo());
    }

    /**
     * 测试getErrEntitySubStatusList方法 - 非法状态变更
     */
    @Test
    public void testGetErrEntitySubStatusList_IllegalStatusChange() {
        // 准备测试数据
        EntitySubStatusDTO entitySubStatusDTO = new EntitySubStatusDTO();
        entitySubStatusDTO.setEntityNoList(Arrays.asList("TASK001"));
        entitySubStatusDTO.setSubStatus("已完成");

        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "已完成");
        entitySubStatusList.add(vo1);

        // Mock方法调用
        when(lookupRepository.getSysLookupValues(anyString())).thenReturn(createSysLookupValuesList());

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.getErrEntitySubStatusList(entitySubStatusList, entitySubStatusDTO);
        entitySubStatusDTO.setEntityNoList(null);
        List<EntitySubStatusVO> result2 = entityStatusTraceService.getErrEntitySubStatusList(entitySubStatusList, entitySubStatusDTO);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("应该返回1个错误", 1, result.size());
        assertTrue("错误消息应该包含非法状态变更信息",
                result.get(0).getDealRes().contains(Constant.ILLEGAL_SUB_STATUS_CHANGE));
    }

    /**
     * 测试getLegalFromStatusSet方法
     */
    @Test
    public void testGetLegalFromStatusSet() {
        // 准备测试数据
        String subStatusTo = "进行中";

        // Mock方法调用
        when(lookupRepository.getSysLookupValues(anyString())).thenReturn(createSysLookupValuesList());

        // 执行测试
        Set<String> result = entityStatusTraceService.getLegalFromStatusSet(subStatusTo);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertTrue("应该包含合法的起始状态", result.contains("待开始"));
        assertTrue("应该包含合法的起始状态", result.contains("进行中"));
    }

    /**
     * 测试insert2EntityTraceTemp方法 - 正常情况
     */
    @Test
    public void testInsert2EntityTraceTemp_Success() {
        // 准备测试数据
        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始");
        EntitySubStatusVO vo2 = createEntitySubStatusVO("CT001", "DS001", "TASK002", "李四", "待开始");
        entitySubStatusList.add(vo1);
        entitySubStatusList.add(vo2);

        // Mock方法调用
        when(repository.getSessionId()).thenReturn(12345L);
        doNothing().when(repository).insert2EntityTraceTemp(anyLong(), anyList());

        // 执行测试
        Long result = entityStatusTraceService.insert2EntityTraceTemp(entitySubStatusList, "进行中");

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("sessionId应该匹配", Long.valueOf(12345L), result);
    }

    /**
     * 测试insert2EntityTraceTemp方法 - 没有合法数据
     */
    @Test
    public void testInsert2EntityTraceTemp_NoValidData() {
        // 准备测试数据 - 所有数据都有错误信息
        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始");
        vo1.setDealRes("错误信息");
        entitySubStatusList.add(vo1);

        // 执行测试
        Long result = entityStatusTraceService.insert2EntityTraceTemp(entitySubStatusList, "进行中");

        // 验证结果
        assertNull("结果应该为空", result);

        // 验证没有调用repository方法
        verify(repository, never()).getSessionId();
        verify(repository, never()).insert2EntityTraceTemp(anyLong(), anyList());
    }

    /**
     * 测试getErrEntitySubStatusList方法 - 有错误数据
     */
    @Test
    public void testGetErrEntitySubStatusList_WithErrors() {
        // 准备测试数据
        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        EntitySubStatusVO vo1 = createEntitySubStatusVO("CT001", "DS001", "TASK001", "张三", "待开始");
        EntitySubStatusVO vo2 = createEntitySubStatusVO("CT001", "DS001", "TASK002", "李四", "待开始");
        entitySubStatusList.add(vo1);
        entitySubStatusList.add(vo2);

        List<EntitySubStatusVO> errTempList = new ArrayList<>();
        EntitySubStatusVO errVo = new EntitySubStatusVO();
        errVo.setEntityNo("TASK001");
        errVo.setDealRes("更新失败");
        errTempList.add(errVo);

        // Mock方法调用
        when(repository.getUpdateErrList(anyLong())).thenReturn(errTempList);

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.getErrEntitySubStatusList(entitySubStatusList, 12345L);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertEquals("应该返回1个错误", 1, result.size());
        assertEquals("错误消息应该匹配", "更新失败", result.get(0).getDealRes());
        assertEquals("任务号应该匹配", "TASK001", result.get(0).getEntityNo());
    }

    /**
     * 测试getErrEntitySubStatusList方法 - sessionId为空
     */
    @Test
    public void testGetErrEntitySubStatusList_NullSessionId() {
        // 准备测试数据
        List<EntitySubStatusVO> entitySubStatusList = new ArrayList<>();
        Long sessionId = null;

        // 执行测试
        List<EntitySubStatusVO> result = entityStatusTraceService.getErrEntitySubStatusList(entitySubStatusList, sessionId);

        // 验证结果
        assertNotNull("结果不能为空", result);
        assertTrue("应该返回空列表", result.isEmpty());
    }

    /**
     * 测试updateStatus方法 - 正常情况
     */
    @Test
    public void testUpdateStatus_Success() throws Exception {
        // Mock HTTP请求头
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("X-Emp-No", "1024778");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        // Mock HR服务
        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("张三");
        hrmPersonInfoMap.put("1024778", hrmPersonInfoDTO);
        when(hrService.getHrmPersonInfo(anyList())).thenReturn(hrmPersonInfoMap);

        // Mock repository方法
        doNothing().when(repository).callCheckEntityInfo(anyLong(), anyString(), anyString(), anyString());

        // 执行测试
        entityStatusTraceService.updateStatus(12345L);

        // 验证调用
        verify(repository).callCheckEntityInfo(12345L, "1024778", "张三", null);
    }

    /**
     * 测试updateStatus方法 - sessionId为空
     */
    @Test
    public void testUpdateStatus_NullSessionId() throws Exception {
        // 执行测试 - 不应该抛出异常
        entityStatusTraceService.updateStatus(null);

        // 验证没有调用repository方法
        verify(repository, never()).callCheckEntityInfo(anyLong(), anyString(), anyString(), anyString());
    }

    /**
     * 测试updateStatus方法 - 员工工号为空
     */
    @Test
    public void testUpdateStatus_EmptyEmpNo() throws Exception {
        // Mock HTTP请求头 - 员工工号为空
        Map<String, String> headerMap = new HashMap<>();
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);

        // Mock HR服务 - 返回空Map
        when(hrService.getHrmPersonInfo(anyList())).thenReturn(new HashMap<>());

        // Mock repository方法
        doNothing().when(repository).callCheckEntityInfo(anyLong(), anyString(), anyString(), anyString());

        // 执行测试
        entityStatusTraceService.updateStatus(12345L);

        // 验证调用
        verify(repository).callCheckEntityInfo(12345L, null, null, null);
    }

    /**
     * 创建EntitySubStatusVO测试数据
     */
    private EntitySubStatusVO createEntitySubStatusVO(String contractNo, String deliverSetNo,
                                                      String entityNo, String scheduler, String subStatus) {
        EntitySubStatusVO vo = new EntitySubStatusVO();
        vo.setContractNo(contractNo);
        vo.setDeliverSetNo(deliverSetNo);
        vo.setEntityNo(entityNo);
        vo.setScheduler(scheduler);
        vo.setSubStatus(subStatus);
        return vo;
    }

    /**
     * 创建SysLookupValues测试数据
     */
    private List<SysLookupValues> createSysLookupValuesList() {
        List<SysLookupValues> list = new ArrayList<>();

        SysLookupValues lookup1 = new SysLookupValues();
        lookup1.setLookupMeaning("待开始,进行中");
        lookup1.setDescription("进行中,已完成");
        list.add(lookup1);

        SysLookupValues lookup2 = new SysLookupValues();
        lookup2.setLookupMeaning("进行中");
        lookup2.setDescription("已完成");
        list.add(lookup2);

        return list;
    }
}