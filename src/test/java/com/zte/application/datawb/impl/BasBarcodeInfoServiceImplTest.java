package com.zte.application.datawb.impl;

import com.zte.application.IMESLogService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.BasBarcodeInfoRepository;
import com.zte.interfaces.dto.BarcodeQueryDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

/**
* BasBarcodeInfoServiceImpl Tester.
*
* <AUTHOR>
* @email <EMAIL>
* @since <pre>8�� 22, 2022</pre>
* @version 1.0
*/
public class BasBarcodeInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BasBarcodeInfoServiceImpl service;

    @Mock
    private BasBarcodeInfoRepository basBarcodeInfoRepository;
    @Mock
    private IMESLogService imesLogService;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }


    /**
    *
    * Method: generateBarcode(TaskBarcodeQueryDTO dto)
    *
    */
    @Test
    public void testGenerateBarcode() throws Exception {
        BarcodeQueryDTO dto = new BarcodeQueryDTO();
        dto.setBarcode(1L);
        PowerMockito.doNothing().when(basBarcodeInfoRepository).callPkgNewItemBarcode(dto);
        try{
            service.generateBarcode(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SFC_CUR_VALUE_IS_NULL, e.getMessage());
        }
    }


    @Test(expected = MesBusinessException.class)
    public void testGenerateBarcode2() throws Exception {
        BarcodeQueryDTO dto = new BarcodeQueryDTO();
        PowerMockito.doNothing().when(basBarcodeInfoRepository).callPkgNewItemBarcode(dto);
        service.generateBarcode(dto);
    }

    @Test
    public void checkCACertificateInWMESTest() {
        List<String> snList = new ArrayList<>();
        service.checkCACertificateInWMES(snList);
        for (int i = 0; i < 300; i++) {
            snList.add("test");
        }
        service.checkCACertificateInWMES(snList);
        Assert.assertNotNull(service.checkCACertificateInWMES(new ArrayList<String>(){{add("test");}}));
    }
} 
