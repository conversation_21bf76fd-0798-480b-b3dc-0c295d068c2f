/*Started by AICoder, pid:ga4c5k35d7a47061414d0849016416254a07e7d2*/
package com.zte.application.datawb.impl;
import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.domain.model.datawb.StbProducteInfoDataRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StbProducteInfoDataServiceImpl2_Test {

    @Mock
    private StbProducteInfoDataRepository stbProducteInfoDataRepository;

    @Mock
    private CpeBoxupBillService cpeBoxupBillService;

    @InjectMocks
    private StbProducteInfoDataServiceImpl service;

    @Before
    public void setUp() {
    }

    @Test
    public void testGetDhomeSnListBySnStart_SnStartBlank() {
        List<String> result = service.getDhomeSnListBySnStart("", 1, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDhomeSnListBySnStart_DaysAgoStartNull() {
        when(stbProducteInfoDataRepository.getDhomeSnListBySnStart(anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList("SN123"));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
                .thenReturn(Collections.emptyList());

        List<String> result = service.getDhomeSnListBySnStart("SN", null, 0);
        assertEquals(1, result.size());
        verify(stbProducteInfoDataRepository).getDhomeSnListBySnStart(eq("SN"), eq(1), eq(0));
    }

    @Test
    public void testGetDhomeSnListBySnStart_DaysAgoEndNull() {
        when(stbProducteInfoDataRepository.getDhomeSnListBySnStart(anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList("SN456"));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(2), eq(0)))
                .thenReturn(Collections.emptyList());

        List<String> result = service.getDhomeSnListBySnStart("SN", 2, null);
        assertEquals(1, result.size());
        verify(stbProducteInfoDataRepository).getDhomeSnListBySnStart(eq("SN"), eq(2), eq(0));
    }

    @Test
    public void testGetDhomeSnListBySnStart_RepositoryReturnsEmpty() {
        when(stbProducteInfoDataRepository.getDhomeSnListBySnStart(anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
                .thenReturn(Collections.singletonList("ITEM1"));

        List<String> result = service.getDhomeSnListBySnStart("SN", 1, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDhomeSnListBySnStart_CpeReturnsItemBarcodes() {
        List<String> itemBarcodes = new ArrayList<>();
        for (int i = 0; i < 1500; i++) {
            itemBarcodes.add("ITEM" + i);
        }
        List<String> strList = new ArrayList<>();
        strList.add("SN1");
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
                .thenReturn(itemBarcodes);
        when(stbProducteInfoDataRepository.getDhomeSnListBySnStart(anyString(), anyInt(), anyInt()))
                .thenReturn(strList);
        when(stbProducteInfoDataRepository.getDhomeSnListByItemList(anyString(), anyList()))
                .thenReturn(Collections.singletonList("SN2"));

        List<String> result = service.getDhomeSnListBySnStart("SN", 1, 0);
        assertEquals(2, result.size());
        verify(stbProducteInfoDataRepository, times(2)).getDhomeSnListByItemList(eq("SN"), anyList());
    }

    @Test
    public void testGetDhomeSnListBySnStart_DistinctSnList() {
        List<String> strList = new ArrayList<>();
        strList.add("SN1");
        strList.add("SN2");
        when(stbProducteInfoDataRepository.getDhomeSnListBySnStart(anyString(), anyInt(), anyInt()))
                .thenReturn(strList);
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
                .thenReturn(Arrays.asList("ITEM1"));
        when(stbProducteInfoDataRepository.getDhomeSnListByItemList(anyString(), anyList()))
                .thenReturn(Arrays.asList("SN2", "SN3"));

        List<String> result = service.getDhomeSnListBySnStart("SN", 1, 0);
        assertEquals(3, result.size());
    }
}
/*Ended by AICoder, pid:ga4c5k35d7a47061414d0849016416254a07e7d2*/