package com.zte.application.datawb.impl;

import com.zte.domain.model.datawb.TechItemStoreRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

public class TechItemStoreServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    TechItemStoreServiceImpl service;

    @Mock
    TechItemStoreRepository repository;

    @Test
    public void testSelectByBomId() {
        PowerMockito.when(repository.selectByBomId(Mockito.anyString())).thenReturn("TEST");
        Assert.assertNotNull(service.selectByBomId("TEST"));
    }
}