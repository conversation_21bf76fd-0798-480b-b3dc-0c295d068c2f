package com.zte.application.datawb.impl;


import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.ConfigMaterialBindServiceRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

import static org.mockito.Matchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({CommonUtils.class})
public class ConfigMaterialBindServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ConfigMaterialBindServiceImpl configMaterialBindServiceImpl;

    @Mock
    ConfigMaterialBindServiceRepository configMaterialBindServiceRepository;

    @Test
    public void ConfigMaterialBind() throws Exception {

        try {         /**
         * <AUTHOR>
         * 添加单元测试覆盖率
         **/
            PowerMockito.mockStatic(CommonUtils.class);
            when(CommonUtils.getLmbMessage(anyString())).thenReturn("hi");
            when(CommonUtils.isNotEmpty(any())).thenReturn(true);

            ConfigMaterialBindInDTO params = new ConfigMaterialBindInDTO();
            ConfigMaterialBindResultDTO result = new ConfigMaterialBindResultDTO();


            params.setEntityName(null);
            params.setItemBarcode(null);
            configMaterialBindServiceImpl.paramInCheck(params);

            params.setEntityName("");
            params.setItemBarcode("");
            configMaterialBindServiceImpl.paramInCheck(params);

            params.setEntityName("10B-M20160500009");
            params.setItemBarcode("287888400015");
            configMaterialBindServiceImpl.paramInCheck(params);
            List<ConfigMaterialBindDTO> dateList = configMaterialBindServiceImpl.paramGetDate(params);


            ConfigMaterialBindDTO c = new ConfigMaterialBindDTO();
            c.setRecordId(2193543);
            c.setMainCode("287888400033");
            c.setCustomMaterialCode("");
            c.setMainCode("122600030160");
            c.setMainItemName("ZXJ10V10.0 PDUA (SL)|ZXJ10V10.0 P电源(双路)部件装配|");
            c.setMainItemQty(1);
            c.setMainScanType("序列码");
            c.setSeqName("装配");
            c.setIsReplace("否");
            c.setQrCode(0);
            c.setRealItemName("");
            c.setRealItemCode("");
            c.setTaskNo("222333");
            c.setContractNum("DTHY2016122304SDHTH301");
            c.setSiteAdress("国网机房");
            c.setMfgSiteId(189603193);
            c.setMfgSiteCode("XT16061395134");
            c.setInvItemId(761602);
            c.setUserName("李咏10134242");
            c.setMainScanTime(null);
            c.setParentRecordId(2193542);
            c.setConfigDetailId(1043766791);
            c.setTaskVerifyFlag("N");
            c.setRealItemCode("");
            c.setRealItemName("");
            c.setRealItemNameEn("");
            List<ConfigMaterialBindDTO> dateList2 = new ArrayList<ConfigMaterialBindDTO>();
            dateList2.add(c);

            ConfigMaterialBindDTO c2 = new ConfigMaterialBindDTO();
            c2.setRecordId(21935433);
            c2.setMainCode("287888400033");
            c2.setCustomMaterialCode("");
            c2.setMainCode("122600030160");
            c2.setMainItemName("ZXJ10V10.0 PDUA (SL)|ZXJ10V10.0 P电源(双路)部件装配|");
            c2.setMainItemQty(1);
            c2.setMainScanType("序列码");
            c2.setSeqName("装配");
            c2.setIsReplace("否");
            c2.setQrCode(0);
            c2.setRealItemName("");
            c2.setRealItemCode("");
            c2.setTaskNo("222333");
            c2.setContractNum("DTHY2016122304SDHTH301");
            c2.setSiteAdress("国网机房");
            c2.setMfgSiteId(189603193);
            c2.setMfgSiteCode("XT16061395134");
            c2.setInvItemId(761602);
            c2.setUserName("李咏10134242");
            c2.setMainScanTime(null);
            c2.setParentRecordId(2193542);
            c2.setConfigDetailId(1043766791);
            c2.setTaskVerifyFlag("N");
            c2.setRealItemCode("");
            c2.setRealItemName("");
            c2.setRealItemNameEn("");
            dateList2.add(c2);

            ConfigMaterialBindDTO c3 = new ConfigMaterialBindDTO();
            c3.setRecordId(2133333);
            c3.setMainCode("287888400033");
            c3.setCustomMaterialCode("");
            c3.setMainCode("122600030160");
            c3.setMainItemName("ZXJ10V10.0 PDUA (SL)|ZXJ10V10.0 P电源(双路)部件装配|");
            c3.setMainItemQty(1);
            c3.setMainScanType("序列码");
            c3.setSeqName("装配");
            c3.setIsReplace("否");
            c3.setQrCode(0);
            c3.setRealItemName("");
            c3.setRealItemCode("");
            c3.setTaskNo("222333");
            c3.setContractNum("DTHY2016122304SDHTH301");
            c3.setSiteAdress("国网机房");
            c3.setMfgSiteId(189603193);
            c3.setMfgSiteCode("XT16061395134");
            c3.setInvItemId(761602);
            c3.setUserName("李咏10134242");
            c3.setMainScanTime(null);
            c3.setParentRecordId(2193542);
            c3.setConfigDetailId(1043766791);
            c3.setTaskVerifyFlag("N");
            c3.setRealItemCode("");
            c3.setRealItemName("");
            c3.setRealItemNameEn("");
            dateList2.add(c3);

            Map<String, Object> datemap = new HashMap<>();
            datemap.put("entityId", "8035797");
            List<Integer> resultList = new ArrayList<Integer>();
            resultList.add(123);
            datemap.put("resultList", resultList);
            datemap.put("resCount", "10");
            Map<String, Object> leverInfoMap = configMaterialBindServiceImpl.getLeverInfo(dateList2, datemap);


            Map<String, Object> dataMap = configMaterialBindServiceImpl.getInfo(dateList2, params);
            List<Integer> siteList = configMaterialBindServiceImpl.getSiteList(dateList2);
            List<Integer> siteList0 = configMaterialBindServiceImpl.getSiteList(dateList2);

            List<Integer> siteList1 = new ArrayList<>();
            siteList1.add(1);
            siteList1.add(2);
            siteList1.add(3);
            configMaterialBindServiceImpl.getLeverTotal(siteList1, datemap);
            configMaterialBindServiceImpl.getEntityId("");
            configMaterialBindServiceImpl.getEntityId("123");
            configMaterialBindServiceImpl.getLeverInfo(111);
            configMaterialBindServiceImpl.getMarkInfo(111, 222);


            Map<String, Object> datemap2 = new HashMap<>();
            List<LeverInfoDTO> leverInfoList = new ArrayList<>();
            List<LeverInfoDTO> leverInfoList1 = new ArrayList<>();
            List<MarkInfoDTO> markInfoList = new ArrayList<>();
            List<MarkInfoDTO> markInfoList1 = new ArrayList<>();

            LeverInfoDTO leverInfoDTO = new LeverInfoDTO();
            leverInfoDTO.setConfigDetailId(1043766791);
            leverInfoDTO.setLevel("1.2.3");
            leverInfoDTO.setMaterialCode("test");
            leverInfoDTO.setMaterialName("test");
            leverInfoDTO.setParentConfigDetailId(1043766791);
            leverInfoList.add(leverInfoDTO);
            leverInfoList1.add(leverInfoDTO);

            List<LeverInfoDTO> leverInfoList2 = new ArrayList<LeverInfoDTO>();
            LeverInfoDTO leverInfoDTO2 = new LeverInfoDTO();
            leverInfoDTO2.setConfigDetailId(1043766791);
            leverInfoDTO2.setLevel("1.2.3.5");
            leverInfoDTO2.setMaterialCode("test");
            leverInfoDTO2.setMaterialName("test");
            leverInfoDTO2.setParentConfigDetailId(1043766791);
            leverInfoList2.add(leverInfoDTO2);

            List<LeverInfoDTO> leverInfoList3 = new ArrayList<LeverInfoDTO>();
            LeverInfoDTO leverInfoDTO3 = new LeverInfoDTO();
            leverInfoDTO3.setConfigDetailId(1043766791);
            leverInfoDTO3.setLevel("1");
            leverInfoDTO3.setMaterialCode("test");
            leverInfoDTO3.setMaterialName("test");
            leverInfoDTO3.setParentConfigDetailId(1043766791);
            leverInfoList3.add(leverInfoDTO3);


            MarkInfoDTO markInfoDTO = new MarkInfoDTO();
            markInfoDTO.setConfigDetailId(1043766791);
            markInfoDTO.setMarkStatus("已完成");
            markInfoList.add(markInfoDTO);
            markInfoList1.add(markInfoDTO);

            PowerMockito.when(configMaterialBindServiceRepository.getMarkInfo(any())).thenReturn(markInfoList1);
            MarkInfoDTO markInfoDTO2 = new MarkInfoDTO();
            markInfoDTO2.setConfigDetailId(1043766791);
            markInfoDTO2.setMarkStatus("成");
            markInfoList.add(markInfoDTO2);
            markInfoList1.add(markInfoDTO2);
            configMaterialBindServiceImpl.getLeverTotal(siteList1, datemap);

            datemap2.put("leverInfoListToTal", leverInfoList);
            datemap2.put("markInfoListTotal", markInfoList);
            configMaterialBindServiceImpl.updateDate(dateList2, datemap2);

            datemap2.put("leverInfoListToTal", leverInfoList2);//覆盖掉
            configMaterialBindServiceImpl.updateDate(dateList2, datemap2);

            datemap2.put("leverInfoListToTal", leverInfoList3);
            configMaterialBindServiceImpl.updateDate(dateList2, datemap2);

            ConfigMaterialBindOutDTO configMaterialBindOutDTO = new ConfigMaterialBindOutDTO();
            configMaterialBindOutDTO.setParentRecordId(1);
            configMaterialBindOutDTO.setRecordId(1);
            List<LeverInfoDTO> leverList = configMaterialBindServiceImpl.findConfig(1, leverInfoList1);
            List<MarkInfoDTO> markList = configMaterialBindServiceImpl.findBoqConfig(1, markInfoList1);
            configMaterialBindServiceImpl.setBom(leverInfoList3, markInfoList, leverInfoList, 1, configMaterialBindOutDTO);

            List<LeverInfoDTO> leverInfoListThreee = new ArrayList<LeverInfoDTO>();
            LeverInfoDTO leverInfoDTOThree = new LeverInfoDTO();
            leverInfoDTOThree.setConfigDetailId(1043766791);
            leverInfoDTOThree.setLevel("1.1.1");
            leverInfoDTOThree.setMaterialCode("test");
            leverInfoDTOThree.setMaterialName("test");
            leverInfoDTOThree.setParentConfigDetailId(1043766791);
            leverInfoListThreee.add(leverInfoDTOThree);

            List<LeverInfoDTO> leverInfoList4 = new ArrayList<LeverInfoDTO>();
            LeverInfoDTO leverInfoDTO4 = new LeverInfoDTO();
            leverInfoDTO4.setConfigDetailId(1043766791);
            leverInfoDTO4.setLevel("1.1.1.2");
            leverInfoDTO4.setMaterialCode("test");
            leverInfoDTO4.setMaterialName("test");
            leverInfoDTO4.setParentConfigDetailId(1043766791);
            leverInfoList4.add(leverInfoDTO4);

            configMaterialBindServiceImpl.setBoq(leverInfoList3, markInfoList, leverInfoList, 1, configMaterialBindOutDTO);
            configMaterialBindServiceImpl.setBoq(leverInfoListThreee, markInfoList, leverInfoList, 1, configMaterialBindOutDTO);
            configMaterialBindServiceImpl.setBoq(leverInfoList4, markInfoList, leverInfoList, 1, configMaterialBindOutDTO);
            configMaterialBindServiceImpl.setBoq(leverInfoList4, markInfoList, leverInfoList, 1043766791, configMaterialBindOutDTO);

        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void getDetailByMfgSiteIDList() {
        List<ConfigDetailDTO> list = configMaterialBindServiceImpl.getDetailByMfgSiteIDList(null);
        Assert.assertEquals(0, list.size());

        List<String> mfgSiteIDList = new ArrayList<>();
        mfgSiteIDList.add("111");
        List<ConfigDetailDTO> resultList = new ArrayList<>();
        resultList.add(new ConfigDetailDTO());
        PowerMockito.when(configMaterialBindServiceRepository.getDetailByMfgSiteIDList(anyList())).thenReturn(resultList);
        list = configMaterialBindServiceImpl.getDetailByMfgSiteIDList(mfgSiteIDList);
        Assert.assertEquals(1, list.size());
    }

    @Test
    public void getInfo(){
        when(configMaterialBindServiceRepository.getEntityId(Mockito.any())).thenReturn(null);
        Map<String, Object> dd = configMaterialBindServiceImpl.getInfo("",null);
        Assert.assertEquals(dd, null);
        when(configMaterialBindServiceRepository.getEntityId(Mockito.any())).thenReturn("3434");
        dd = configMaterialBindServiceImpl.getInfo("33",null);
        Assert.assertEquals(dd.size() ,2);
    }

    @Test
    public  void getLeverInfoListToTal(){
        Object result = configMaterialBindServiceImpl.getLeverInfoListToTal("",null,"");
        Assert.assertEquals(result,null);
        result = configMaterialBindServiceImpl.getLeverInfoListToTal("3",null,"");
        Assert.assertEquals(result,null);

        Map<String, Map<String, Object>> mapLeverInfo = new HashMap<>();
        result = configMaterialBindServiceImpl.getLeverInfoListToTal("3",mapLeverInfo,"");
        Assert.assertEquals(result,null);
        mapLeverInfo.put("343",null);
        result = configMaterialBindServiceImpl.getLeverInfoListToTal("3",mapLeverInfo,"899");
        Assert.assertEquals(result,null);

        mapLeverInfo.put("3",null);
        result = configMaterialBindServiceImpl.getLeverInfoListToTal("3",mapLeverInfo,"899");
        Assert.assertEquals(result,null);

        Map<String, Object> ff = new HashMap<>();
        ff.put("3","3333");
        mapLeverInfo.put("3",ff);
        result = configMaterialBindServiceImpl.getLeverInfoListToTal("3",mapLeverInfo,"899");
        Assert.assertEquals(result,null);

        ff.put("899","3333");
        mapLeverInfo.put("3",ff);
        result = configMaterialBindServiceImpl.getLeverInfoListToTal("3",mapLeverInfo,"899");
        Assert.assertEquals(result,"3333");
    }

    @Test
    public void updateDatePlus(){
        PowerMockito.mockStatic(CommonUtils.class);
        List<ConfigMaterialBindDTO> dateList = new ArrayList<>();
        Map<String, Map<String, Object>> mapLeverInfo = new HashMap<>();
        ConfigMaterialBindResultDTO result = configMaterialBindServiceImpl.updateDatePlus(dateList,mapLeverInfo);
        Assert.assertEquals(result.getErrDTO().getCode() ,"0000");

        ConfigMaterialBindDTO dto = new ConfigMaterialBindDTO();
        dto.setTaskNo("1");
        dto.setConfigDetailId(233);
        dateList.add(dto);
        Map<String,Object> vv = new HashMap<>();
        vv.put("leverInfoListToTal",null);
        vv.put("markInfoListTotal",null);
        mapLeverInfo.put("1",vv);
        result = configMaterialBindServiceImpl.updateDatePlus(dateList,mapLeverInfo);
        Assert.assertEquals(result.getErrDTO().getCode() ,"0000");


        List<LeverInfoDTO>  bb = new ArrayList<>();
        LeverInfoDTO xx = new LeverInfoDTO();
        xx.setConfigDetailId(2);
        bb.add(xx);
        vv.put("leverInfoListToTal",bb);

        List<MarkInfoDTO> nn = new ArrayList<>();
        MarkInfoDTO mm = new MarkInfoDTO();
        mm.setConfigDetailId(33);
        nn.add(mm);
        vv.put("markInfoListTotal",nn);
        result = configMaterialBindServiceImpl.updateDatePlus(dateList,mapLeverInfo);
        Assert.assertEquals(result.getErrDTO().getCode() ,"0000");

    }
    @Test
    public void paramInCheck() {
        PowerMockito.mockStatic(CommonUtils.class);
        ConfigMaterialBindInDTO params = new ConfigMaterialBindInDTO();
        ConfigMaterialBindResultDTO result = configMaterialBindServiceImpl.paramInCheck(params);
        Assert.assertEquals(result.getConfigMaterialBindOutDTOList(), null);

        List<String> barcodes = new ArrayList<>();
        barcodes.add("22");
        params.setBarcodes(barcodes);
        result = configMaterialBindServiceImpl.paramInCheck(params);
        Assert.assertEquals(result.getConfigMaterialBindOutDTOList(), null);

        params.setItemBarcode("22");
        result = configMaterialBindServiceImpl.paramInCheck(params);
        Assert.assertEquals(result.getConfigMaterialBindOutDTOList(), null);

        params.setEntityName("22");
        result = configMaterialBindServiceImpl.paramInCheck(params);
        Assert.assertEquals(result.getConfigMaterialBindOutDTOList(), null);

        for (int i = 0; i < 102; i++) {
            barcodes.add("8989");
        }
        params.setBarcodes(barcodes);
        result = configMaterialBindServiceImpl.paramInCheck(params);
        Assert.assertEquals(result.getConfigMaterialBindOutDTOList(), null);


        params.setBarcodes(null);
        result = configMaterialBindServiceImpl.paramInCheck(params);
        Assert.assertEquals(result.getConfigMaterialBindOutDTOList(), null);
    }

    @Test
    public void paramGetDatePlus() {
        ConfigMaterialBindInDTO params = new ConfigMaterialBindInDTO();
        Map<String, Object> map = new HashMap<>();
        map.put("entityName", "");
        map.put("itemBarcode", "");
        map.put("mfgSiteId", "");
        map.put(Constant.RESULT_CURSOR, "");
        List<ConfigMaterialBindDTO> result = configMaterialBindServiceImpl.paramGetDatePlus(params);
        Assert.assertEquals(result, null);

        List<String> df = new ArrayList<>();
        df.add("33");
        params.setBarcodes(df);
        map = new HashMap<>();
        map.put("entityName", "");
        map.put("itemBarcode", "");
        map.put("mfgSiteId", "");
        map.put(Constant.RESULT_CURSOR, null);
        result = configMaterialBindServiceImpl.paramGetDatePlus(params);
        Assert.assertEquals(result, null);

    }

    @Test
    public void checkBindingRelationshipOfConfigMaterialTest() throws Exception {
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(RetCode.VALIDATIONERROR_MSGID.equals(e.getExMsgId()));
        }
        CheckBindingRelationshipDTO dto = new CheckBindingRelationshipDTO();
        dto.setInputType(0);
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.VALIDATE_INPUT_TYPE_OF_CHECK_BINDING_ERROR.equals(e.getExMsgId()));
        }
        dto.setInputType(1);
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PARAMS_LIST_IS_NULL.equals(e.getExMsgId()));
        }
        List<String> parameterList = new ArrayList<>();
        for (int i = 0; i < 51; i++) {
            parameterList.add("test");
        }
        dto.setParameterList(parameterList);
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SIZE_OUT_BOUND_PARAMS_LIST_OF_CHECK_BINDING.equals(e.getExMsgId()));
        }
        parameterList.clear();
        parameterList.add("test");
        dto.setParameterList(parameterList);
        List<CheckBindingRelationshipDTO> resultList = new ArrayList<>();
        PowerMockito.when(configMaterialBindServiceRepository.selectWholeBarcodeInfoOfTask(anyList())).thenReturn(resultList);
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.ENTITY_NAME_NOT_ALL_EXIST.equals(e.getExMsgId()));
        }
        CheckBindingRelationshipDTO dto1 = new CheckBindingRelationshipDTO();
        dto1.setEntityName("test");
        resultList.add(dto1);
        configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        dto1.setIsPDVMFlag("Y");
        List<String> resultStringList = new ArrayList<>();
        resultStringList.add("123");
        PowerMockito.when(configMaterialBindServiceRepository.filterNotBoundEntityIds(anyList())).thenReturn(resultStringList);
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.EXIST_NOT_BOUND_ENTITY_NAME.equals(e.getExMsgId()));
        }
        dto.setInputType(2);
        PowerMockito.when(configMaterialBindServiceRepository.filterNotBoundMfgSiteIds(anyList())).thenReturn(resultStringList);
        try {
            configMaterialBindServiceImpl.checkBindingRelationshipOfConfigMaterial(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.EXIST_NOT_BOUND_MFG_SITE_ID.equals(e.getExMsgId()));
        }
    }
}
