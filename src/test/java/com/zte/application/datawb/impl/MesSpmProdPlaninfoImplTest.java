package com.zte.application.datawb.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.datawb.MesSpmProdPlaninfoRepository;
import com.zte.interfaces.dto.SyncWorkInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/2/7 23:42
 * @Description
 */
public class MesSpmProdPlaninfoImplTest {
    @Mock
    MesSpmProdPlaninfoRepository mesSpmProdPlaninfoRepository;
    @InjectMocks
    MesSpmProdPlaninfoImpl mesSpmProdPlaninfoImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSyncWorkInfoToMes() throws Exception {
        when(mesSpmProdPlaninfoRepository.insertBatch(any())).thenReturn(Long.valueOf(1));
        when(mesSpmProdPlaninfoRepository.deleteByProdPlanIdList(any())).thenReturn(Long.valueOf(1));

        try{
            mesSpmProdPlaninfoImpl.syncWorkInfoToMes(Arrays.<SyncWorkInfoDTO>asList(new SyncWorkInfoDTO()));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_LIST_IS_NULL,e.getMessage());
        }
    }

    @Test(expected = MesBusinessException.class)
    public void testSyncWorkInfoToMes2() throws Exception {
        mesSpmProdPlaninfoImpl.syncWorkInfoToMes(null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme