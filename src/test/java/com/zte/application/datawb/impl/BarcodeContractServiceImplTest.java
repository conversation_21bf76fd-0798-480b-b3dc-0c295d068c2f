package com.zte.application.datawb.impl;

import com.zte.domain.model.datawb.BarcodeContract;
import com.zte.domain.model.datawb.BarcodeContractRepository;
import com.zte.domain.model.datawb.ChainPageRows;
import com.zte.domain.model.datawb.ZmsCommonRepository;
import com.zte.interfaces.dto.ContractNumberQueryDTO;
import com.zte.interfaces.dto.ECMaterialAssemblyDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * BarcodeContractServiceImpl单元测试类
 * 
 * 主要测试功能：
 * 1. 根据合同号查询条码（分页）
 * 2. 根据条码查询合同号（批量）
 * 3. 根据任务号查询合同信息
 * 
 * 测试覆盖：
 * - 成功场景测试
 * - 边界条件测试
 * - 异常场景测试
 * - 分页逻辑测试
 * - 去重逻辑测试
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class BarcodeContractServiceImplTest {

    @InjectMocks
    private BarcodeContractServiceImpl barcodeContractService;

    @Mock
    private BarcodeContractRepository barcodeContractRepository;

    @Mock
    private ZmsCommonRepository zmsCommonRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // ==================== getListByContract方法测试 ====================

    /**
     * 测试getListByContract方法 - 成功场景，有下一页
     * 验证分页查询的正常流程和下一页标识设置
     */
    @Test
    public void testGetListByContract_Success_HasNextPage() {
        // Given
        ContractNumberQueryDTO queryDTO = createMockQueryDTO(1L, 10L);
        List<BarcodeContract> mockList = createMockBarcodeContractList(11); // 多查一条

        when(barcodeContractRepository.getListByContract(any(ContractNumberQueryDTO.class)))
            .thenReturn(mockList);

        // When
        ChainPageRows<BarcodeContract> result = barcodeContractService.getListByContract(queryDTO);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("当前页码应为1", 1L, result.getCurrent());
        Assert.assertTrue("应该有下一页", result.isHasNextPage());
        Assert.assertEquals("返回数据应为10条", 10, result.getRows().size());

        // 验证分页参数计算
        Assert.assertEquals("开始行应为1", Long.valueOf(1L), queryDTO.getStartRow());
        Assert.assertEquals("结束行应为11", Long.valueOf(11L), queryDTO.getEndRow());

        // 验证Repository方法被调用
        verify(barcodeContractRepository).getListByContract(queryDTO);
    }

    /**
     * 测试getListByContract方法 - 成功场景，无下一页
     * 验证当查询结果不超过页大小时的处理
     */
    @Test
    public void testGetListByContract_Success_NoNextPage() {
        // Given
        ContractNumberQueryDTO queryDTO = createMockQueryDTO(2L, 10L);
        List<BarcodeContract> mockList = createMockBarcodeContractList(8); // 少于页大小

        when(barcodeContractRepository.getListByContract(any(ContractNumberQueryDTO.class)))
            .thenReturn(mockList);

        // When
        ChainPageRows<BarcodeContract> result = barcodeContractService.getListByContract(queryDTO);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("当前页码应为2", 2L, result.getCurrent());
        Assert.assertFalse("不应该有下一页", result.isHasNextPage());
        Assert.assertEquals("返回数据应为8条", 8, result.getRows().size());

        // 验证分页参数计算
        Assert.assertEquals("开始行应为11", Long.valueOf(11L), queryDTO.getStartRow());
        Assert.assertEquals("结束行应为21", Long.valueOf(21L), queryDTO.getEndRow());
    }

    /**
     * 测试getListByContract方法 - 查询结果为空
     * 验证空结果的处理
     */
    @Test
    public void testGetListByContract_EmptyResult() {
        // Given
        ContractNumberQueryDTO queryDTO = createMockQueryDTO(1L, 10L);
        List<BarcodeContract> mockList = new ArrayList<>();

        when(barcodeContractRepository.getListByContract(any(ContractNumberQueryDTO.class)))
            .thenReturn(mockList);

        // When
        ChainPageRows<BarcodeContract> result = barcodeContractService.getListByContract(queryDTO);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("当前页码应为1", 1L, result.getCurrent());
        Assert.assertFalse("不应该有下一页", result.isHasNextPage());
        Assert.assertTrue("返回数据应为空", result.getRows().isEmpty());
    }

    /**
     * 测试getListByContract方法 - 恰好等于页大小
     * 验证查询结果恰好等于页大小时的处理
     */
    @Test
    public void testGetListByContract_ExactPageSize() {
        // Given
        ContractNumberQueryDTO queryDTO = createMockQueryDTO(1L, 5L);
        List<BarcodeContract> mockList = createMockBarcodeContractList(5); // 恰好等于页大小

        when(barcodeContractRepository.getListByContract(any(ContractNumberQueryDTO.class)))
            .thenReturn(mockList);

        // When
        ChainPageRows<BarcodeContract> result = barcodeContractService.getListByContract(queryDTO);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertFalse("不应该有下一页", result.isHasNextPage());
        Assert.assertEquals("返回数据应为5条", 5, result.getRows().size());
    }

    /**
     * 测试getListByContract方法 - 第一页分页参数计算
     * 验证第一页的分页参数计算逻辑
     */
    @Test
    public void testGetListByContract_FirstPageCalculation() {
        // Given
        ContractNumberQueryDTO queryDTO = createMockQueryDTO(1L, 20L);
        List<BarcodeContract> mockList = createMockBarcodeContractList(15);

        when(barcodeContractRepository.getListByContract(any(ContractNumberQueryDTO.class)))
            .thenReturn(mockList);

        // When
        barcodeContractService.getListByContract(queryDTO);

        // Then - 验证分页参数计算：(page - 1) * rows + 1
        Assert.assertEquals("第一页开始行应为1", Long.valueOf(1L), queryDTO.getStartRow());
        Assert.assertEquals("第一页结束行应为21", Long.valueOf(21L), queryDTO.getEndRow());
    }

    /**
     * 测试getListByContract方法 - 中间页分页参数计算
     * 验证中间页的分页参数计算逻辑
     */
    @Test
    public void testGetListByContract_MiddlePageCalculation() {
        // Given
        ContractNumberQueryDTO queryDTO = createMockQueryDTO(3L, 15L);
        List<BarcodeContract> mockList = createMockBarcodeContractList(10);

        when(barcodeContractRepository.getListByContract(any(ContractNumberQueryDTO.class)))
            .thenReturn(mockList);

        // When
        barcodeContractService.getListByContract(queryDTO);

        // Then - 验证分页参数计算：(3 - 1) * 15 + 1 = 31
        Assert.assertEquals("第三页开始行应为31", Long.valueOf(31L), queryDTO.getStartRow());
        Assert.assertEquals("第三页结束行应为46", Long.valueOf(46L), queryDTO.getEndRow());
    }

    // ==================== getListByBarcode方法测试 ====================

    /**
     * 测试getListByBarcode方法 - 成功场景
     * 验证批量查询条码对应合同号的正常流程
     */
    @Test
    public void testGetListByBarcode_Success() {
        // Given
        List<String> barcodeList = Arrays.asList("BARCODE001", "BARCODE002", "BARCODE003");
        List<BarcodeContract> mockResult = createMockBarcodeContractListFromBarcodes(barcodeList);

        when(barcodeContractRepository.getListByBarcode(anyList()))
            .thenReturn(mockResult);

        // When
        List<BarcodeContract> result = barcodeContractService.getListByBarcode(barcodeList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为3", 3, result.size());

        // 验证Repository方法被调用
        verify(barcodeContractRepository).getListByBarcode(barcodeList);
    }

    /**
     * 测试getListByBarcode方法 - 有重复数据
     * 验证去重逻辑的正确性
     */
    @Test
    public void testGetListByBarcode_WithDuplicates() {
        // Given
        List<String> barcodeList = Arrays.asList("BARCODE001", "BARCODE002");
        List<BarcodeContract> mockResultWithDuplicates = createMockBarcodeContractListWithDuplicates();

        when(barcodeContractRepository.getListByBarcode(anyList()))
            .thenReturn(mockResultWithDuplicates);

        // When
        List<BarcodeContract> result = barcodeContractService.getListByBarcode(barcodeList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        // 去重后应该只有2条不同的记录
        Assert.assertEquals("去重后结果数量应为2", 2, result.size());
    }

    /**
     * 测试getListByBarcode方法 - 空输入列表
     * 验证空输入的处理
     */
    @Test
    public void testGetListByBarcode_EmptyInput() {
        // Given
        List<String> emptyBarcodeList = new ArrayList<>();
        List<BarcodeContract> mockResult = new ArrayList<>();

        when(barcodeContractRepository.getListByBarcode(anyList()))
            .thenReturn(mockResult);

        // When
        List<BarcodeContract> result = barcodeContractService.getListByBarcode(emptyBarcodeList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getListByBarcode方法 - Repository返回空结果
     * 验证Repository返回空结果时的处理
     */
    @Test
    public void testGetListByBarcode_EmptyRepositoryResult() {
        // Given
        List<String> barcodeList = Arrays.asList("NONEXISTENT001", "NONEXISTENT002");
        List<BarcodeContract> emptyResult = new ArrayList<>();

        when(barcodeContractRepository.getListByBarcode(anyList()))
            .thenReturn(emptyResult);

        // When
        List<BarcodeContract> result = barcodeContractService.getListByBarcode(barcodeList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getListByBarcode方法 - 单个条码查询
     * 验证单个条码查询的处理
     */
    @Test
    public void testGetListByBarcode_SingleBarcode() {
        // Given
        List<String> singleBarcodeList = Arrays.asList("BARCODE001");
        List<BarcodeContract> mockResult = createMockBarcodeContractListFromBarcodes(singleBarcodeList);

        when(barcodeContractRepository.getListByBarcode(anyList()))
            .thenReturn(mockResult);

        // When
        List<BarcodeContract> result = barcodeContractService.getListByBarcode(singleBarcodeList);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为1", 1, result.size());
        Assert.assertEquals("条码应匹配", "BARCODE001", result.get(0).getItemBarcode());
    }

    // ==================== getZmsEntityListByEntityId方法测试 ====================

    /**
     * 测试getZmsEntityListByEntityId方法 - 成功场景
     * 验证根据任务ID查询合同信息的正常流程
     */
    @Test
    public void testGetZmsEntityListByEntityId_Success() {
        // Given
        Integer entityId = 1001;
        List<ECMaterialAssemblyDTO> mockResult = createMockECMaterialAssemblyDTOList();

        when(zmsCommonRepository.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(mockResult);

        // When
        List<ECMaterialAssemblyDTO> result = barcodeContractService.getZmsEntityListByEntityId(entityId);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果数量应为2", 2, result.size());

        ECMaterialAssemblyDTO firstResult = result.get(0);
        Assert.assertEquals("合同号应匹配", "CONTRACT001", firstResult.getContractNumber());
        Assert.assertEquals("任务号应匹配", "ENTITY001", firstResult.getEntityName());
        Assert.assertEquals("组织ID应匹配", "ORG001", firstResult.getOrgId());

        // 验证Repository方法被调用
        verify(zmsCommonRepository).getZmsEntityListByEntityId(entityId);
    }

    /**
     * 测试getZmsEntityListByEntityId方法 - 空结果
     * 验证Repository返回空结果时的处理
     */
    @Test
    public void testGetZmsEntityListByEntityId_EmptyResult() {
        // Given
        Integer entityId = 9999;
        List<ECMaterialAssemblyDTO> emptyResult = new ArrayList<>();

        when(zmsCommonRepository.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(emptyResult);

        // When
        List<ECMaterialAssemblyDTO> result = barcodeContractService.getZmsEntityListByEntityId(entityId);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getZmsEntityListByEntityId方法 - null输入
     * 验证null输入的处理
     */
    @Test
    public void testGetZmsEntityListByEntityId_NullInput() {
        // Given
        Integer entityId = null;
        List<ECMaterialAssemblyDTO> mockResult = new ArrayList<>();

        when(zmsCommonRepository.getZmsEntityListByEntityId(any()))
            .thenReturn(mockResult);

        // When
        List<ECMaterialAssemblyDTO> result = barcodeContractService.getZmsEntityListByEntityId(entityId);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());

        // 验证Repository方法被调用
        verify(zmsCommonRepository).getZmsEntityListByEntityId(entityId);
    }

    /**
     * 测试getZmsEntityListByEntityId方法 - 负数输入
     * 验证负数输入的处理
     */
    @Test
    public void testGetZmsEntityListByEntityId_NegativeInput() {
        // Given
        Integer entityId = -1;
        List<ECMaterialAssemblyDTO> emptyResult = new ArrayList<>();

        when(zmsCommonRepository.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(emptyResult);

        // When
        List<ECMaterialAssemblyDTO> result = barcodeContractService.getZmsEntityListByEntityId(entityId);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试getZmsEntityListByEntityId方法 - 零值输入
     * 验证零值输入的处理
     */
    @Test
    public void testGetZmsEntityListByEntityId_ZeroInput() {
        // Given
        Integer entityId = 0;
        List<ECMaterialAssemblyDTO> emptyResult = new ArrayList<>();

        when(zmsCommonRepository.getZmsEntityListByEntityId(anyInt()))
            .thenReturn(emptyResult);

        // When
        List<ECMaterialAssemblyDTO> result = barcodeContractService.getZmsEntityListByEntityId(entityId);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空列表", result.isEmpty());
    }

    // ==================== Mock数据创建方法 ====================

    /**
     * 创建Mock的查询DTO
     */
    private ContractNumberQueryDTO createMockQueryDTO(Long page, Long rows) {
        ContractNumberQueryDTO dto = new ContractNumberQueryDTO();
        dto.setContractNumber("CONTRACT001");
        dto.setPage(page);
        dto.setRows(rows);
        return dto;
    }

    /**
     * 创建Mock的条码合同列表
     */
    private List<BarcodeContract> createMockBarcodeContractList(int count) {
        List<BarcodeContract> list = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            BarcodeContract contract = new BarcodeContract();
            contract.setItemBarcode("BARCODE" + String.format("%03d", i));
            contract.setContractNumber("CONTRACT001");
            list.add(contract);
        }
        return list;
    }

    /**
     * 根据条码列表创建Mock的条码合同列表
     */
    private List<BarcodeContract> createMockBarcodeContractListFromBarcodes(List<String> barcodes) {
        List<BarcodeContract> list = new ArrayList<>();
        int contractIndex = 1;
        for (String barcode : barcodes) {
            BarcodeContract contract = new BarcodeContract();
            contract.setItemBarcode(barcode);
            contract.setContractNumber("CONTRACT" + String.format("%03d", contractIndex++));
            list.add(contract);
        }
        return list;
    }

    /**
     * 创建包含重复数据的Mock条码合同列表
     */
    private List<BarcodeContract> createMockBarcodeContractListWithDuplicates() {
        List<BarcodeContract> list = new ArrayList<>();

        // 添加第一条记录
        BarcodeContract contract1 = new BarcodeContract();
        contract1.setItemBarcode("BARCODE001");
        contract1.setContractNumber("CONTRACT001");
        list.add(contract1);

        // 添加第二条记录
        BarcodeContract contract2 = new BarcodeContract();
        contract2.setItemBarcode("BARCODE002");
        contract2.setContractNumber("CONTRACT002");
        list.add(contract2);

        // 添加重复的第一条记录
        BarcodeContract duplicateContract1 = new BarcodeContract();
        duplicateContract1.setItemBarcode("BARCODE001");
        duplicateContract1.setContractNumber("CONTRACT001");
        list.add(duplicateContract1);

        // 添加重复的第二条记录
        BarcodeContract duplicateContract2 = new BarcodeContract();
        duplicateContract2.setItemBarcode("BARCODE002");
        duplicateContract2.setContractNumber("CONTRACT002");
        list.add(duplicateContract2);

        return list;
    }

    /**
     * 创建Mock的ECMaterialAssemblyDTO列表
     */
    private List<ECMaterialAssemblyDTO> createMockECMaterialAssemblyDTOList() {
        List<ECMaterialAssemblyDTO> list = new ArrayList<>();

        ECMaterialAssemblyDTO dto1 = new ECMaterialAssemblyDTO();
        dto1.setContractNumber("CONTRACT001");
        dto1.setEntityName("ENTITY001");
        dto1.setOrgId("ORG001");
        dto1.setServerSn("SERVER001");
        list.add(dto1);

        ECMaterialAssemblyDTO dto2 = new ECMaterialAssemblyDTO();
        dto2.setContractNumber("CONTRACT002");
        dto2.setEntityName("ENTITY002");
        dto2.setOrgId("ORG002");
        dto2.setServerSn("SERVER002");
        list.add(dto2);

        return list;
    }
}
