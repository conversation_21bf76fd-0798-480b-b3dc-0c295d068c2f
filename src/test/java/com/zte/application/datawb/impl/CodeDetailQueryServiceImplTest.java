package com.zte.application.datawb.impl;

import com.zte.domain.model.BaItem;
import com.zte.domain.model.datawb.CodeDetailInfoRepository;
import com.zte.util.BaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

public class CodeDetailQueryServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CodeDetailQueryServiceImpl codeDetailQueryService;

    @Mock
    private CodeDetailInfoRepository codeDetailInfoRepository;

    @Test
    public void testGetItemVersionBatch() {
        Assert.assertNotNull(codeDetailQueryService.getItemVersionBatch(new ArrayList<>()));
        List<BaItem> versionList = new ArrayList<>();
        versionList.add(new BaItem());
        PowerMockito.when(codeDetailInfoRepository.getItemVersionBatch(Mockito.any())).thenReturn(versionList);
        codeDetailQueryService.getItemVersionBatch(new ArrayList<>());
    }
}