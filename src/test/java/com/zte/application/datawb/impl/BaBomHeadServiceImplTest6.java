/*Started by AICoder, pid:x4469t76de2f09c1493a093dc01f4f96f7c3a1af*/
package com.zte.application.datawb.impl;
import com.google.common.collect.Lists;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.BaBomHeadRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BaBomHeadServiceImplTest6 {

    @Mock
    private BaBomHeadRepository baBomHeadRepository;

    @InjectMocks
    private BaBomHeadServiceImpl baBomHeadService;

    @Before
    public void setUp() {
        // Mockito will inject the mocks in the service instance
    }

    @Test
    public void testQueryBomInfoByBomNoList_EmptyList() {
        List<String> emptyList = Collections.emptyList();
        List<BaBomHead> result = baBomHeadService.queryBomInfoByBomNoList(emptyList);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBomInfoByBomNoList_NullList() {
        List<String> nullList = null;
        List<BaBomHead> result = baBomHeadService.queryBomInfoByBomNoList(nullList);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBomInfoByBomNoList_Empty2List() {
        List<String> emptyList = new ArrayList<>();
        emptyList.add("");
        List<BaBomHead> result = baBomHeadService.queryBomInfoByBomNoList(emptyList);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryBomInfoByBomNoList_ValidList() {
        List<String> validList = Arrays.asList("BOM1", "BOM2", "BOM3");
        List<BaBomHead> mockResult1 = Lists.newArrayList(new BaBomHead(), new BaBomHead());
        List<BaBomHead> mockResult2 = Lists.newArrayList(new BaBomHead());

        when(baBomHeadRepository.queryBomInfoByBomNoList(anyList()))
            .thenReturn(mockResult1)
            .thenReturn(mockResult2);

        List<BaBomHead> result = baBomHeadService.queryBomInfoByBomNoList(validList);

        verify(baBomHeadRepository, times(1)).queryBomInfoByBomNoList(anyList());
        assertEquals(2, result.size());
    }

    @Test
    public void testQueryBomInfoByBomNoList_WithEmptyAndDuplicateValues() {
        List<String> listWithEmptyAndDuplicates = Arrays.asList("", "BOM1", "BOM1", "BOM2");
        List<BaBomHead> mockResult = Lists.newArrayList(new BaBomHead(), new BaBomHead());

        when(baBomHeadRepository.queryBomInfoByBomNoList(anyList()))
            .thenReturn(Collections.emptyList());

        List<BaBomHead> result = baBomHeadService.queryBomInfoByBomNoList(listWithEmptyAndDuplicates);

        verify(baBomHeadRepository, times(1)).queryBomInfoByBomNoList(anyList());
        assertEquals(0, result.size());
    }

    @Test
    public void testgetBomInfoByBomNoListAndCodeType() {
        BaBomHead baBomHead = new BaBomHead();
        when(baBomHeadRepository.getBomInfoByBomNoListAndCodeType(baBomHead))
                .thenReturn(Collections.emptyList());
        List<BaBomHead> result = baBomHeadService.getBomInfoByBomNoListAndCodeType(baBomHead);
        assertEquals(0, result.size());
    }
}
/*Ended by AICoder, pid:x4469t76de2f09c1493a093dc01f4f96f7c3a1af*/