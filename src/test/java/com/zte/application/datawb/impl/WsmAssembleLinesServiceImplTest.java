package com.zte.application.datawb.impl;

import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.interfaces.dto.WsmAssembleLinesDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
public class WsmAssembleLinesServiceImplTest extends BaseTestCase {
    @InjectMocks
    WsmAssembleLinesServiceImpl wsmAssembleLinesService;

    @Mock
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;
    @Test
    public void getWsmAssemblyListByMainAndSub(){
        WsmAssembleLinesDTO map =new WsmAssembleLinesDTO();
        map.setMainItemBarcodeList(new ArrayList<>());
        map.setSubItemBarcodeList(new ArrayList<>());
        PowerMockito.when(wsmAssembleLinesRepository.getWsmAssemblyListByMainAndSub(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(wsmAssembleLinesService.getWsmAssemblyListByMainAndSub(map));
    }

    @Test
    public void deleteAssembleLineForRemoveRepeat() {
        PowerMockito.when(wsmAssembleLinesRepository.deleteAssembleLineForRemoveRepeat()).thenReturn(1);
        Assert.assertNotNull(wsmAssembleLinesService.deleteAssembleLineForRemoveRepeat());
    }
}