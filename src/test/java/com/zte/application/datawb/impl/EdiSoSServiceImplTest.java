package com.zte.application.datawb.impl;


import com.zte.domain.model.datawb.EdiSoSRepository;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.Normalizer;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@PrepareForTest({MESHttpHelper.class, HttpClientUtil.class, JacksonJsonConverUtil.class, HttpRemoteService.class,
        Normalizer.class})
@RunWith(PowerMockRunner.class)
public class EdiSoSServiceImplTest {

    @InjectMocks
    private EdiSoSServiceImpl service;

    @Mock
    EdiSoSRepository ediSoSRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(Normalizer.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class);
    }

    @Test
    public void test_getMaterialsWarehouses() {
        PowerMockito.when(ediSoSRepository.getMaterialsWarehouses(Mockito.anyMap())).thenReturn(null);
        Assert.assertNull(service.getMaterialsWarehouses(new HashMap<>(1)));
    }

    @Test
    public void test_getSendMaterials() {
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        Assert.assertTrue(service.getSendMaterials(ediSoSDTO).isEmpty());
        ediSoSDTO.setExternalorderkey2List(Collections.singletonList("GL19090300002"));
        Assert.assertTrue(service.getSendMaterials(ediSoSDTO).isEmpty());
        ediSoSDTO.setWarehouseIds("1/27");
        PowerMockito.when(ediSoSRepository.getSendMaterials(Mockito.anyMap())).thenReturn(Collections.emptyList());
        Assert.assertTrue(service.getSendMaterials(ediSoSDTO).isEmpty());
    }

    @Test
    public void queryTotalNotComplete(){
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        List<EdiSoSDTO> ediSoSDTOS = service.queryTotalNotComplete(ediSoSDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(ediSoSDTOS));

        ediSoSDTO.setExternalorderkey2List(Arrays.asList("123"));
        service.queryTotalNotComplete(ediSoSDTO);

        ediSoSDTO.setWarehouseIds("123");
        service.queryTotalNotComplete(ediSoSDTO);
    }
}
