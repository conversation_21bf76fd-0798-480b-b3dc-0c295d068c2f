package com.zte.application.datawb.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.domain.model.datawb.BsProductClassify;
import com.zte.domain.model.datawb.WmesProductionInfoBoard;
import com.zte.domain.model.datawb.WmesProductionInfoBoardRepository;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/10 20:19
 */
@RunWith(PowerMockRunner.class)
public class WmesProductionInfoBoardServiceImplTest extends BaseTestCase {
    @Mock
    Logger logger;
    @Mock
    WmesProductionInfoBoardRepository wmesProductionInfoBoardRepository;
    @InjectMocks
    WmesProductionInfoBoardServiceImpl wmesProductionInfoBoardServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @PrepareForTest({MESHttpHelper.class, MicroServiceRestUtil.class})
    public void testGetDataOfDailyProgress() throws Exception {
        when(wmesProductionInfoBoardRepository.featureComputation(any())).thenReturn(Arrays.<WmesProductionInfoBoard>asList(new WmesProductionInfoBoard()));
        when(wmesProductionInfoBoardRepository.getDay(any())).thenReturn(Arrays.<WmesProductionInfoBoard>asList(new WmesProductionInfoBoard()));

        wmesProductionInfoBoardServiceImpl.getDataOfDailyProgress("");
        wmesProductionInfoBoardServiceImpl.getDataOfDailyProgress("test");


        PowerMockito.mockStatic(MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        List<BsProductClassify> list = new ArrayList<BsProductClassify>();
        list.add(new BsProductClassify(){{setProductModelName("test");}});
        JSONObject json = new JSONObject();
        json.put("bo", list);
        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn(json.toJSONString());
        try{
            wmesProductionInfoBoardServiceImpl.getDataOfDailyProgress("test");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

}
