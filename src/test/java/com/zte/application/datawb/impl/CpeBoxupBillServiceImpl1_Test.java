/*Started by AICoder, pid:qc789icdd5ma6311490908581150fc0ef360f8ec*/
package com.zte.application.datawb.impl;
import com.zte.domain.model.datawb.CpeBoxupBillRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CpeBoxupBillServiceImpl1_Test {

    @Mock
    private CpeBoxupBillRepository cpeBoxupBillRepository;

    @InjectMocks
    private CpeBoxupBillServiceImpl service;

    @Test
    public void testGetItemBarcodesDaysAgo_WithValidData() {
        // Given
        List<String> mockData = Arrays.asList("BC123", "  ", null, "ABC456");
        when(cpeBoxupBillRepository.getItemBarcodesDaysAgo(2, 5)).thenReturn(mockData);

        // When
        List<String> result = service.getItemBarcodesDaysAgo(2, 5);

        // Then
        assertEquals(Arrays.asList("BC123", "ABC456"), result);
        verify(cpeBoxupBillRepository).getItemBarcodesDaysAgo(2, 5);
    }

    @Test
    public void testGetItemBarcodesDaysAgo_EmptyList() {
        // Given
        when(cpeBoxupBillRepository.getItemBarcodesDaysAgo(0, 0)).thenReturn(Collections.emptyList());

        // When
        List<String> result = service.getItemBarcodesDaysAgo(0, 0);

        // Then
        assertTrue(result.isEmpty());
        verify(cpeBoxupBillRepository).getItemBarcodesDaysAgo(0, 0);
    }

    @Test
    public void testGetItemBarcodesDaysAgo_AllInvalidEntries() {
        // Given
        List<String> mockData = Arrays.asList("", "   ", null);
        when(cpeBoxupBillRepository.getItemBarcodesDaysAgo(1, 3)).thenReturn(mockData);

        // When
        List<String> result = service.getItemBarcodesDaysAgo(1, 3);

        // Then
        assertTrue(result.isEmpty());
        verify(cpeBoxupBillRepository).getItemBarcodesDaysAgo(1, 3);
    }

    @Test
    public void testGetItemBarcodesDaysAgo_SingleValidEntry() {
        // Given
        List<String> mockData = Collections.singletonList("VALID123");
        when(cpeBoxupBillRepository.getItemBarcodesDaysAgo(7, 7)).thenReturn(mockData);

        // When
        List<String> result = service.getItemBarcodesDaysAgo(7, 7);

        // Then
        assertEquals(mockData, result);
        verify(cpeBoxupBillRepository).getItemBarcodesDaysAgo(7, 7);
    }
}
/*Ended by AICoder, pid:qc789icdd5ma6311490908581150fc0ef360f8ec*/