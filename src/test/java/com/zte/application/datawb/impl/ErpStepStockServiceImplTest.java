package com.zte.application.datawb.impl;

import com.zte.domain.model.datawb.ErpStepStockRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;


public class ErpStepStockServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ErpStepStockServiceImpl service;

    @Mock
    ErpStepStockRepository repository;

    @Test
    public void testSelectByStockName() {
        PowerMockito.when(repository.selectByStockName(Mockito.anyString())).thenReturn("TEST");
        Assert.assertNotNull(service.selectByStockName("TEST"));
    }
}