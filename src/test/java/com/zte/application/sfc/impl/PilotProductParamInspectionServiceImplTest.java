package com.zte.application.sfc.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.application.MesGetDictInforService;
import com.zte.application.barcode.MesOmsGetDictInforService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.domain.model.datawb.JobsSupplierProduceDataRepository;
import com.zte.domain.model.datawb.StbProducteInfoDataRepository;
import com.zte.domain.model.sfc.JobsSupplierProduceDataTacRepository;
import com.zte.domain.model.sfc.StbProducteInfoTacRepository;
import com.zte.interfaces.dto.api.DictInfoForDTO;
import com.zte.interfaces.dto.sfc.PilotProductDTO;
import com.zte.interfaces.dto.sfc.PilotProductParamVO;
import com.zte.interfaces.dto.sfc.PilotProductReqDTO;
import com.zte.interfaces.dto.sfc.PilotProductReqVO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.util.*;

/* Started by AICoder, pid:17ab6n3d98gca1e14d3409a4e084e27544c622af */

public class PilotProductParamInspectionServiceImplTest extends BaseTestCase {

    @Mock
    private MesGetDictInforService mockMesGetDictInforService;
    @Mock
    private MesGetDictInforRepository mockMesGetDictInforRepository;
    @Mock
    private MesOmsGetDictInforService mockMesOmsGetDictInforService;
    @Mock
    private JobsSupplierProduceDataRepository mockJobsSupplierProduceDataRepository;
    @Mock
    private StbProducteInfoDataRepository mockStbProducteInfoDataRepository;

    @InjectMocks
    private PilotProductParamInspectionServiceImpl pilotProductParamInspectionServiceImplUnderTest;


    @Mock
    private StbProducteInfoTacRepository stbProducteInfoTacRepository;

    @Mock
    private JobsSupplierProduceDataTacRepository jobsSupplierProduceDataTacRepository;

    @Test
    public void testDataCheckIsOnly() {
        // Setup
        final PilotProductReqDTO reqDTO = new PilotProductReqDTO();
        reqDTO.setType("OEM");
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("paramName");
        pilotProductDTO.setParamType("2");
        pilotProductDTO.setParamList(Lists.newArrayList("value"));
        PilotProductDTO pilotProductDTO2 = new PilotProductDTO();
        pilotProductDTO2.setParamName("paramName2");
        pilotProductDTO2.setParamType("2");
        pilotProductDTO2.setParamList(Lists.newArrayList("value"));
        List<PilotProductDTO> pilotProductDTOS = Lists.newArrayList(pilotProductDTO, pilotProductDTO2);
        reqDTO.setDataList(pilotProductDTOS);

        final PilotProductReqVO pilotProductReqVO = new PilotProductReqVO();
        pilotProductReqVO.setEntityName("entityName");
        pilotProductReqVO.setWorkUnit("workUnit");
        pilotProductReqVO.setParamName("paramName");
        pilotProductReqVO.setSum(1);
        pilotProductReqVO.setParamList(Lists.newArrayList("value"));
        Map<String, List<DictInfoForDTO>> map = Maps.newHashMap();
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupCode(Constant.LOOKUP_TYPE_8001040);
        dictInfoForDTO.setLookupMeaning("paramName");
        DictInfoForDTO dictInfoForDTO2 = new DictInfoForDTO();
        dictInfoForDTO2.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO2.setLookupMeaning("paramName");
        DictInfoForDTO dictInfoForDTO6 = new DictInfoForDTO();
        dictInfoForDTO6.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO6.setLookupMeaning("paramName3,MAC");
        DictInfoForDTO dictInfoForDTO3 = new DictInfoForDTO();
        dictInfoForDTO3.setLookupCode(Constant.LOOKUP_TYPE_8001040);
        dictInfoForDTO3.setLookupMeaning("paramName2,MAC");
        DictInfoForDTO dictInfoForDTO4 = new DictInfoForDTO();
        dictInfoForDTO4.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO4.setLookupMeaning("paramName2,MAC");
        DictInfoForDTO dictInfoForDTO5 = new DictInfoForDTO();
        dictInfoForDTO5.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO5.setLookupMeaning("paramName4,MAC");
        DictInfoForDTO dictInfoForDTO56 = new DictInfoForDTO();
        dictInfoForDTO56.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO56.setLookupMeaning("paramName5");
        DictInfoForDTO dictInfoForDTO66 = new DictInfoForDTO();
        dictInfoForDTO66.setLookupCode(Constant.LOOKUP_TYPE_8001040);
        dictInfoForDTO66.setLookupMeaning("paramName7,1");
        DictInfoForDTO dictInfoForDTO77 = new DictInfoForDTO();
        dictInfoForDTO77.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO77.setLookupMeaning("paramName8");
        DictInfoForDTO dictInfoForDTO67 = new DictInfoForDTO();
        dictInfoForDTO67.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO67.setLookupMeaning("paramName6,MAC");
        DictInfoForDTO dictInfoForDTO68 = new DictInfoForDTO();
        dictInfoForDTO68.setLookupCode(Constant.LOOKUP_TYPE_8001050);
        dictInfoForDTO68.setLookupMeaning("paramName68,MAC");
        map.put(Constant.LOOKUP_TYPE_8001040, Lists.newArrayList(dictInfoForDTO, dictInfoForDTO3,dictInfoForDTO5,dictInfoForDTO66,dictInfoForDTO67,dictInfoForDTO77));
        map.put(Constant.LOOKUP_TYPE_8001050, Lists.newArrayList(dictInfoForDTO2,dictInfoForDTO4,dictInfoForDTO6,dictInfoForDTO56,dictInfoForDTO68));
        map.put(Constant.LOOKUP_TYPE_8001032, Lists.newArrayList(dictInfoForDTO3,dictInfoForDTO5,dictInfoForDTO66,dictInfoForDTO67,dictInfoForDTO77));
        map.put(Constant.LOOKUP_TYPE_8001033, Lists.newArrayList(dictInfoForDTO4,dictInfoForDTO6,dictInfoForDTO56,dictInfoForDTO68));

        PowerMockito.when(mockMesOmsGetDictInforService.getDictMap(Mockito.any())).thenReturn(map);

        // Configure JobsSupplierProduceDataRepository.getProductSumMacList(...).
        final PilotProductParamVO pilotProductParamVO = new PilotProductParamVO();
        pilotProductParamVO.setEntityName("entityName");
        pilotProductParamVO.setWorkUnit("workUnit");
        pilotProductParamVO.setParamName("paramName");
        pilotProductParamVO.setParamValue("paramValue");
        pilotProductParamVO.setSum(0);
        pilotProductParamVO.setParamList("paramList");
        final List<PilotProductParamVO> pilotProductParamVOS = Lists.newArrayList(pilotProductParamVO);
        PowerMockito.when(mockJobsSupplierProduceDataRepository.getProductSumMacList(Mockito.any()))
                .thenReturn(pilotProductParamVOS);

        // Configure StbProducteInfoDataRepository.getProductSumMacCpeList(...).
        final PilotProductParamVO pilotProductParamVO1 = new PilotProductParamVO();
        pilotProductParamVO1.setEntityName("entityName");
        pilotProductParamVO1.setWorkUnit("workUnit");
        pilotProductParamVO1.setParamName("paramName");
        pilotProductParamVO1.setParamValue("paramValue");
        pilotProductParamVO1.setSum(0);
        pilotProductParamVO1.setParamList("paramList");
        final List<PilotProductParamVO> pilotProductParamVOS1 = Lists.newArrayList(pilotProductParamVO1);
        PowerMockito.when(mockStbProducteInfoDataRepository.getProductSumMacCpeList(Mockito.any()))
                .thenReturn(pilotProductParamVOS1);

        // Configure JobsSupplierProduceDataRepository.getProductSumList(...).
        final PilotProductParamVO pilotProductParamVO2 = new PilotProductParamVO();
        pilotProductParamVO2.setEntityName("entityName");
        pilotProductParamVO2.setWorkUnit("workUnit");
        pilotProductParamVO2.setParamName("paramName");
        pilotProductParamVO2.setParamValue("paramValue");
        pilotProductParamVO2.setSum(0);
        pilotProductParamVO2.setParamList("paramList");
         List<PilotProductParamVO> pilotProductParamVOS2 = Lists.newArrayList(pilotProductParamVO2);
        PowerMockito.when(mockJobsSupplierProduceDataRepository.getProductSumList(Mockito.any()))
                .thenReturn(pilotProductParamVOS2);

        // Configure StbProducteInfoDataRepository.getProductSumCpeList(...).
        final PilotProductParamVO pilotProductParamVO3 = new PilotProductParamVO();
        pilotProductParamVO3.setEntityName("entityName");
        pilotProductParamVO3.setWorkUnit("workUnit");
        pilotProductParamVO3.setParamName("paramName");
        pilotProductParamVO3.setParamValue("paramValue");
        pilotProductParamVO3.setSum(0);
        pilotProductParamVO3.setParamList("paramList");
         List<PilotProductParamVO> pilotProductParamVOS3 =Lists.newArrayList(pilotProductParamVO3);
        PowerMockito.when(mockStbProducteInfoDataRepository.getProductSumCpeList(Mockito.any()))
                .thenReturn(pilotProductParamVOS3);
        // Run the test
         List<PilotProductReqVO> result = pilotProductParamInspectionServiceImplUnderTest.dataCheckIsOnly(reqDTO);
        Assert.assertEquals(result.size(),1);

        reqDTO.setType("机顶盒");
        List<PilotProductReqVO> result2 = pilotProductParamInspectionServiceImplUnderTest.dataCheckIsOnly(reqDTO);
        Assert.assertEquals(result2.size(),1);

        reqDTO.setType(null);
        pilotProductParamVO3.setSum(3);
        PilotProductDTO pilotProductDTO3 = new PilotProductDTO();
        pilotProductDTO3.setParamName("paramName3");
        pilotProductDTO3.setParamType("2");
        pilotProductDTO3.setParamList(Lists.newArrayList("value"));
        PilotProductDTO pilotProductDTO4 = new PilotProductDTO();
        pilotProductDTO4.setParamName("paramName4");
        pilotProductDTO4.setParamType("1");
        pilotProductDTO4.setParamList(Lists.newArrayList("value","value"));
        pilotProductDTOS.add(pilotProductDTO4);
        PilotProductDTO pilotProductDTO5 = new PilotProductDTO();
        pilotProductDTO5.setParamName("paramName5");
        pilotProductDTO5.setParamType("2");
        pilotProductDTO5.setParamList(Lists.newArrayList("value"));
        pilotProductDTOS.add(pilotProductDTO5);
      PilotProductDTO pilotProductDTO6 = new PilotProductDTO();
        pilotProductDTO6.setParamName("paramName6");
        pilotProductDTO6.setParamType("2");
        pilotProductDTO6.setParamList(Lists.newArrayList("value"));
        pilotProductDTOS.add(pilotProductDTO6);
        PilotProductDTO pilotProductDTO8 = new PilotProductDTO();
        pilotProductDTO8.setParamName("paramName8");
        pilotProductDTO8.setParamType("2");
        pilotProductDTO8.setParamList(Lists.newArrayList("value"));
        pilotProductDTOS.add(pilotProductDTO8);
        PilotProductDTO pilotProductDTO68 = new PilotProductDTO();
        pilotProductDTO68.setParamName("paramName68");
        pilotProductDTO68.setParamType("2");
        pilotProductDTO68.setParamList(Lists.newArrayList("value"));
        pilotProductDTOS.add(pilotProductDTO68);

        List<PilotProductReqVO> result3 = pilotProductParamInspectionServiceImplUnderTest.dataCheckIsOnly(reqDTO);
        Assert.assertEquals(result3.size(),1);
        Assert.assertNotNull(pilotProductDTOS);

    }

    @Test
    public void testDataCheckIsOnly_isnull() {
        List<PilotProductReqVO> pilotProductReqVOS = pilotProductParamInspectionServiceImplUnderTest.dataCheckIsOnly(null);
        Assert.assertTrue(CommonUtils.isEmpty(pilotProductReqVOS));
        List<PilotProductReqVO> pilotProductReqVOS1 = pilotProductParamInspectionServiceImplUnderTest.dataCheckIsOnly(new PilotProductReqDTO());
        Assert.assertTrue(CommonUtils.isEmpty(pilotProductReqVOS1));
    }

    @Test(expected = MesBusinessException.class)
    public void testParamContrast_InvalidParamName() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_OTHER_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("SOME_FIELD");
        pilotProductDTO.setParamType(NumConstant.STR_2);
        pilotProductDTO.setParamList(Collections.singletonList("value1"));

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        pilotProductDTO.setParamName("");
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        Assert.assertNotNull(method);

    }

    @Test(expected = MesBusinessException.class)
    public void testParamContrast_nullParamName() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_OTHER_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("");
        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        Assert.assertNotNull(method);
    }

    @Test(expected = MesBusinessException.class)
    public void testParamContrast_DiscreteParamListSizeError() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("SOME_FIELD");
        pilotProductDTO.setParamType(NumConstant.STR_ONE);
        pilotProductDTO.setParamList(Collections.singletonList("value1"));

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        Assert.assertNotNull(method);

    }

    @Test(expected = MesBusinessException.class)
    public void testParamContrast_NullParamList() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("SOME_FIELD");
        pilotProductDTO.setParamType(NumConstant.STR_ONE);
        pilotProductDTO.setParamList(Collections.emptyList());

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        Assert.assertNotNull(method);

    }

    @Test(expected = MesBusinessException.class)
    public void testParamContrast_InvalidParamType() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("SOME_FIELD");
        pilotProductDTO.setParamType("INVALID_TYPE");
        pilotProductDTO.setParamList(Arrays.asList("value1", "value2"));

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        Assert.assertNotNull(method);

    }
    @Test(expected = MesBusinessException.class)
    public void testParamContrast_NullParamType() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("SOME_FIELD");
        pilotProductDTO.setParamType(null);
        pilotProductDTO.setParamList(Arrays.asList("value1", "value2"));

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        Assert.assertNotNull(method);

    }


    @Test
    public void testParamContrast_ValidCase() throws Exception {
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setLookupMeaning("SOME_FIELD");
        List<DictInfoForDTO> dictInfoForDTOS = Collections.singletonList(dictInfoForDTO);
        PilotProductDTO pilotProductDTO = new PilotProductDTO();
        pilotProductDTO.setParamName("SOME_FIELD");
        pilotProductDTO.setParamType(NumConstant.STR_ONE);
        pilotProductDTO.setParamList(Arrays.asList("value1", "value2"));

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "paramContrast", PilotProductDTO.class, List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO, dictInfoForDTOS);
        // No exception thrown, valid case
        Assert.assertNotNull(method);

    }
    /* Ended by AICoder, pid:17ab6n3d98gca1e14d3409a4e084e27544c622af */

    /* Started by AICoder, pid:n1cb8p1fbd12e5c147460a74709ebf1154d1bcd5 */
    @Test
    public void testSetParamStartEnd_EmptyParamList() throws Exception {

        PilotProductDTO pilotProductDTO = new  PilotProductDTO();
        pilotProductDTO.setParamType("1");
        pilotProductDTO.setParamList(Lists.newArrayList());
        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "setParamStartEnd", PilotProductDTO.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, pilotProductDTO);
        Assert.assertNotNull(method);
    }
    /* Ended by AICoder, pid:n1cb8p1fbd12e5c147460a74709ebf1154d1bcd5 */
    /* Started by AICoder, pid:7f517u5e22qc42a148550bd8e04fcd1369a43e94 */
    @Test
    public void testCheckType_ValidTypes() throws Exception {
        PilotProductReqDTO reqDTO = new PilotProductReqDTO();
        reqDTO.setType(Constant.OEM);

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "checkType", PilotProductReqDTO.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, reqDTO);
        Assert.assertNotNull(method);

    }
    @Test(expected = MesBusinessException.class)
    public void testCheckType_error() throws Exception {

        PilotProductReqDTO reqDTO = new PilotProductReqDTO();
        reqDTO.setType("897");

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "checkType", PilotProductReqDTO.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, reqDTO);
        Assert.assertNotNull(method);

    }
    /* Ended by AICoder, pid:7f517u5e22qc42a148550bd8e04fcd1369a43e94 */


    /* Started by AICoder, pid:w04bfz4c17jcf8c14a720a6b30d4d331c8f1866a */
    @Test
    public void testCheckParamName_ValidCase() throws Exception {
        PilotProductDTO dto1 = new PilotProductDTO();
        dto1.setParamName("param1");
        PilotProductDTO dto2 = new PilotProductDTO();;
        dto2.setParamName("param2");

        List<PilotProductDTO> dataList = Arrays.asList(dto1, dto2);

        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "checkParamName", List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, dataList);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, Lists.newArrayList());
        Assert.assertNotNull(method);

        // No exception thrown, valid case
    }

    @Test(expected = MesBusinessException.class)
    public void testCheckParamName_RepeatedParamNames() throws Exception {
        PilotProductDTO dto1 = new PilotProductDTO();
        dto1.setParamName("param1");
        PilotProductDTO dto2 = new PilotProductDTO();;
        dto2.setParamName("param1");
        PilotProductDTO dto3 = new PilotProductDTO();;
        dto3.setParamName("");
        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "checkParamName", List.class);
        method.invoke(pilotProductParamInspectionServiceImplUnderTest, Lists.newArrayList(dto1, dto2, dto3));
        Assert.assertNotNull(method);
        // Expecting MesBusinessException to be thrown
    }
    /* Ended by AICoder, pid:w04bfz4c17jcf8c14a720a6b30d4d331c8f1866a */

    /* Started by AICoder, pid:g173859c99f643f1485e0a02401c502d6653f29c */
    @Test
    public void test_getDictMap_EmptyDescription() throws Exception {
        Method method = PowerMockito.method(PilotProductParamInspectionServiceImpl.class, "getDictMap");

        method.setAccessible(true);

        // Mock the service call
        Map<String, List<DictInfoForDTO>> mockDictMap = new HashMap<>();
        List<DictInfoForDTO> list1 = Lists.newArrayList();
        DictInfoForDTO dictInfoForDTO = new DictInfoForDTO();
        dictInfoForDTO.setDescription("");
        DictInfoForDTO dictInfoForDTO2 = new DictInfoForDTO();
        dictInfoForDTO2.setDescription("looup");
        list1.add(dictInfoForDTO);
        list1.add(dictInfoForDTO2);
        mockDictMap.put(Constant.LOOKUP_TYPE_8001032, list1);
        mockDictMap.put(Constant.LOOKUP_TYPE_8001033, list1);

        Mockito.when(mockMesOmsGetDictInforService.getDictMap(Mockito.anyList())).thenReturn(mockDictMap);

        Map<String, List<DictInfoForDTO>> result = (Map<String, List<DictInfoForDTO>>) method.invoke(pilotProductParamInspectionServiceImplUnderTest);
        dictInfoForDTO2.setDescription("looup#look#2");
        method.invoke(pilotProductParamInspectionServiceImplUnderTest);
        dictInfoForDTO2.setDescription("looup#look#1");
        method.invoke(pilotProductParamInspectionServiceImplUnderTest);
        Assert.assertEquals(2, result.size());
    }
    /* Ended by AICoder, pid:g173859c99f643f1485e0a02401c502d6653f29c */
}
