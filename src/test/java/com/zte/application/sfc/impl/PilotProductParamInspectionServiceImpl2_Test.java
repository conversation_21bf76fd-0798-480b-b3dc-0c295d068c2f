/*Started by AICoder, pid:3b1539e3ea88f8614d59086be101ac246fb83f57*/
package com.zte.application.sfc.impl;
import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.domain.model.datawb.JobsSupplierProduceDataRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PilotProductParamInspectionServiceImpl2_Test {

    @Mock
    private JobsSupplierProduceDataRepository jobsSupplierProduceDataRepository;

    @Mock
    private CpeBoxupBillService cpeBoxupBillService;

    @InjectMocks
    private PilotProductParamInspectionServiceImpl service;

    @Test
    public void testGetCpeSnListBySnStart_SnStartBlank() {
        List<String> result = service.getCpeSnListBySnStart("", 1, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetCpeSnListBySnStart_DaysAgoNull() {
        when(jobsSupplierProduceDataRepository.getCpeSnListBySnStart(eq("SN123"), eq(1), eq(0)))
            .thenReturn(Arrays.asList("SN1", "SN2"));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Collections.emptyList());

        List<String> result = service.getCpeSnListBySnStart("SN123", null, null);
        assertEquals(Arrays.asList("SN1", "SN2"), result);
    }

    @Test
    public void testGetCpeSnListBySnStart_RepositoryEmpty() {
        when(jobsSupplierProduceDataRepository.getCpeSnListBySnStart(any(), anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Arrays.asList("ITEM1", "ITEM2"));

        List<String> result = service.getCpeSnListBySnStart("SN123", 2, 3);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetCpeSnListBySnStart_CpeServiceReturnsItems() {
        List<String> strList = new ArrayList<>();
        strList.add("SN1");
        strList.add("SN2");
        when(jobsSupplierProduceDataRepository.getCpeSnListBySnStart(any(), eq(1), eq(0)))
            .thenReturn(strList);
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Arrays.asList("ITEM1", "ITEM2", "ITEM3", "ITEM4"));
        when(jobsSupplierProduceDataRepository.getCpeSnListByItemBarcodes(any(), anyList()))
            .thenReturn(Arrays.asList("SN3"));

        List<String> result = service.getCpeSnListBySnStart("SN123", 1, 0);
        assertEquals(Arrays.asList("SN1", "SN2", "SN3"), result);
    }

    @Test
    public void testGetCpeSnListBySnStart_DuplicateRemoval() {
        List<String> strList = new ArrayList<>();
        strList.add("SN1");
        strList.add("SN2");
        when(jobsSupplierProduceDataRepository.getCpeSnListBySnStart(any(), eq(1), eq(0)))
            .thenReturn(strList);
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Arrays.asList("ITEM1"));
        when(jobsSupplierProduceDataRepository.getCpeSnListByItemBarcodes(any(), anyList()))
            .thenReturn(Arrays.asList("SN2", "SN3"));

        List<String> result = service.getCpeSnListBySnStart("SN123", 1, 0);
        assertEquals(Arrays.asList("SN1", "SN2", "SN3"), result);
    }
}
/*Ended by AICoder, pid:3b1539e3ea88f8614d59086be101ac246fb83f57*/