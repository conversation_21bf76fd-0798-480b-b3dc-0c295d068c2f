/*Started by AICoder, pid:ud48ctd91a9d10414aee0a92210c874686a2ca17*/
package com.zte.application.sfc.impl;
import com.zte.application.datawb.CpeBoxupBillService;
import com.zte.domain.model.datawb.JobsSupplierProduceDataRepository;
import com.zte.domain.model.datawb.StbProductSnCount;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PilotProductParamInspectionServiceImpl1_Test {

    @Mock
    private JobsSupplierProduceDataRepository jobsSupplierProduceDataRepository;

    @Mock
    private CpeBoxupBillService cpeBoxupBillService;

    @InjectMocks
    private PilotProductParamInspectionServiceImpl service;

    @Test
    public void testGetCpeSnListCount_EmptySnStartList() {
        Map<String, Integer> result = service.getCpeSnListCount(Collections.emptyList(), 1, 0);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCpeSnListCount_NullDaysAgoStart() {
        List<String> snList = Arrays.asList("SN001", "SN002");
        when(jobsSupplierProduceDataRepository.getCpeSnCount(anyList(), eq(1), eq(0)))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getCpeSnListCount(snList, null, 0);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCpeSnListCount_NullDaysAgoEnd() {
        List<String> snList = Arrays.asList("SN001");
        when(jobsSupplierProduceDataRepository.getCpeSnCount(anyList(), eq(1), eq(0)))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(eq(1), eq(0)))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getCpeSnListCount(snList, 1, null);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCpeSnListCount_RepositoryReturnsEmpty() {
        List<String> snList = Arrays.asList("SN001", "SN002");
        when(jobsSupplierProduceDataRepository.getCpeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Arrays.asList("ITEM001"));

        Map<String, Integer> result = service.getCpeSnListCount(snList, 1, 0);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetCpeSnListCount_CpeBoxupBillServiceReturnsEmpty() {
        List<String> snList = Arrays.asList("SN001");
        when(jobsSupplierProduceDataRepository.getCpeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 5)));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getCpeSnListCount(snList, 1, 0);
        assertEquals(1, result.size());
        assertEquals(5, (int) result.get("SN001"));
    }

    @Test
    public void testGetCpeSnListCount_MergeCounts() {
        List<String> snList = Arrays.asList("SN001");
        when(jobsSupplierProduceDataRepository.getCpeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 5)));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Arrays.asList("ITEM001", "ITEM002"));
        when(jobsSupplierProduceDataRepository.getCpeSnCountByItemBarcodes(anyList(), anyList()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 3)));

        Map<String, Integer> result = service.getCpeSnListCount(snList, 1, 0);
        assertEquals(8, (int) result.get("SN001"));
    }

    @Test
    public void testGetCpeSnListCount_DuplicateSnStart() {
        List<String> snList = Arrays.asList("SN001", "SN001", "SN002");
        when(jobsSupplierProduceDataRepository.getCpeSnCount(anyList(), anyInt(), anyInt()))
            .thenReturn(Arrays.asList(new StbProductSnCount("SN001", 5)));
        when(cpeBoxupBillService.getItemBarcodesDaysAgo(anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());

        Map<String, Integer> result = service.getCpeSnListCount(snList, 1, 0);
        assertEquals(1, result.size());
        assertEquals(5, (int) result.get("SN001"));
    }
}
/*Ended by AICoder, pid:ud48ctd91a9d10414aee0a92210c874686a2ca17*/