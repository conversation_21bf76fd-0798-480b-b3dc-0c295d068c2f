package com.zte.application.sfc.impl;

import com.zte.domain.model.sfc.MtnRepairLinesRepository;
import com.zte.interfaces.dto.sfc.MtnRepairLinesDTO;
import com.zte.interfaces.dto.sfc.PmRepairInfoStatDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

public class MtnRepairLinesImplTest extends BaseTestCase {

    @InjectMocks
    private MtnRepairLinesImpl service;
    @Mock
    private MtnRepairLinesRepository repository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /* Started by AICoder, pid:4e2afg95e911cb9149eb0b4990f6db32af036a56 */
    @Test
    public void statByTroubleSmallCode() {
        List<PmRepairInfoStatDTO> result = service.statByTroubleSmallCode("", "");
        Assert.assertTrue(result.isEmpty());

        result = service.statByTroubleSmallCode("129557331116", "");
        Assert.assertTrue(result.isEmpty());

        result = service.statByTroubleSmallCode("129557331116", "高温回读工序|故障失败项:解析高温记录数据");
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void statByTroubleSmallCodeAndSiteNo() {
        List<PmRepairInfoStatDTO> result = service.statByTroubleSmallCodeAndSiteNo("", "");
        Assert.assertTrue(result.isEmpty());

        result = service.statByTroubleSmallCodeAndSiteNo("129557331116", "");
        Assert.assertTrue(result.isEmpty());

        result = service.statByTroubleSmallCodeAndSiteNo("129557331116", "高温回读工序|故障失败项:解析高温记录数据");
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void getLastRepairByItemBarcode() {
        MtnRepairLinesDTO result = service.getLastRepairByItemBarcode("");
        Assert.assertNull(result);
        result = service.getLastRepairByItemBarcode("706856600055");
        Assert.assertNull(result);
    }
    /* Ended by AICoder, pid:4e2afg95e911cb9149eb0b4990f6db32af036a56 */
}
