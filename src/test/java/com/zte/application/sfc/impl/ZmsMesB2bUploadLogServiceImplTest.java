package com.zte.application.sfc.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.ZmsMesB2bUploadLogRepository;
import com.zte.util.BaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
/*Started by AICoder, pid:s525f7de1092fd514b0c0922f04156425d984f79*/
/**
 * <AUTHOR>
 * @date 2024-09-10 16:41
 */
public class ZmsMesB2bUploadLogServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ZmsMesB2bUploadLogServiceImpl service;
    @Mock
    private ZmsMesB2bUploadLogRepository repository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBatchDeleteById_EmptyList() {
        List<String> idList = new ArrayList<>();
        service.batchDeleteById(idList);
        verify(repository, never()).batchDeleteById(any());
    }

    @Test
    public void testBatchDeleteById_NonEmptyList() {
        List<String> idList = Arrays.asList("1", "2", "3");
        service.batchDeleteById(idList);
        verify(repository, times(1)).batchDeleteById(any());
    }

    @Test
    public void testBatchDeleteById_ListSizeGreaterThanConstant() {
        List<String> idList = new ArrayList<>(Collections.nCopies(Constant.BATCH_QUERY_SIZE + 1, "id"));
        service.batchDeleteById(idList);
        verify(repository, times(2)).batchDeleteById(any());
    }
    /*Ended by AICoder, pid:s525f7de1092fd514b0c0922f04156425d984f79*/
}
