package com.zte.application.erpdt.impl;

import com.zte.domain.model.erpdt.ItemListRepository;
import com.zte.interfaces.dto.ItemListEntityForAuxDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

public class ItemListServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private ItemListServiceImpl itemListService;

    @Mock
    private ItemListRepository itemListRepository;

    @Test
    public void testGetItemListByTaskList() {
        List<String> taskNos = new ArrayList<>();
        Assert.assertEquals(new ArrayList<>(), itemListService.getItemListByTaskList(taskNos));
        taskNos.add("1234");
        itemListService.getItemListByTaskList(taskNos);
        PowerMockito.when(itemListRepository.getItemListByTaskList(any())).thenReturn(new ArrayList<ItemListEntityForAuxDTO>() {{
            add(new ItemListEntityForAuxDTO());
        }});
        itemListService.getItemListByTaskList(taskNos);

    }
}