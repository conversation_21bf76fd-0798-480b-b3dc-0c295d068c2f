package com.zte.application.erpdt.impl;

import com.zte.domain.model.erpdt.ItemListRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.when;
@PrepareForTest()
public class ItemListServiceImplByTest extends PowerBaseTestCase {
    @InjectMocks
    private ItemListServiceImpl service;
    @Mock
    private ItemListRepository pdTaskService;

    @Test
    public void batchSelectByTaskNoList() throws Exception {
        List<String> taskList = new ArrayList<>();
        taskList.add("1213");
        when(pdTaskService.batchSelectByTaskNoList(taskList)).then<PERSON><PERSON><PERSON>(taskList);
        Assert.assertNotNull(service.batchSelectByTaskNoList(taskList));
    }
}