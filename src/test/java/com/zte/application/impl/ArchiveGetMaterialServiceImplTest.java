package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveBoxupBillClient;
import com.zte.infrastructure.feign.ArchiveGetMaterialClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  成品入库领验单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public class ArchiveGetMaterialServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ArchiveGetMaterialServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveGetMaterialClient archiveGetMaterialClient;


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("LLD040302478");
        selectGetMaterialByGetNo();
        selectGetMaterialBoxDetailByGetNo();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test(expected = Exception.class)
    public void archiveError() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("LLD040302478");
        selectGetMaterialByGetNoError();
        selectGetMaterialBoxDetailByGetNo();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test
    public void archiveError2() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("LLD040302478");
        selectGetMaterialByGetNo();
        selectGetMaterialBoxDetailByGetNoError();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("LLD040302478");
        selectGetMaterialByGetNo();
        selectGetMaterialBoxDetailByGetNoNull();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveBusinessOrgAndDateIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("LLD040302478");
        selectGetMaterialByGetNo();
        selectGetMaterialBoxDetailByGetNo();
        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveBusiness() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("LLD040302478");
        selectGetMaterialByGetNo();
        selectGetMaterialBoxDetailByGetNo();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    private void selectGetMaterialByGetNo(){
        ArchiveGetMaterialDTO archiveGetMaterialDTO=new ArchiveGetMaterialDTO();
        archiveGetMaterialDTO.setGetNo("LLD040302478");
        archiveGetMaterialDTO.setApplyDateStr("2023-08-25 00:00:00");
        ServiceData<ArchiveGetMaterialDTO> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(archiveGetMaterialDTO);
        Mockito.when(archiveGetMaterialClient.selectGetMaterialByGetNo(Mockito.any())).thenReturn(serviceData);
    }

    private void selectGetMaterialByGetNoError(){
        ArchiveGetMaterialDTO archiveGetMaterialDTO=new ArchiveGetMaterialDTO();
        archiveGetMaterialDTO.setGetNo("LLD040302478");
        archiveGetMaterialDTO.setApplyDateStr("2023-08-25 00:00:00");
        ServiceData<ArchiveGetMaterialDTO> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(archiveGetMaterialDTO);
        Mockito.when(archiveGetMaterialClient.selectGetMaterialByGetNo(Mockito.any())).thenReturn(serviceData);
    }

   private void selectGetMaterialBoxDetailByGetNo(){
       ArchiveGetMaterialBoxDetailDTO archiveGetMaterialBoxDetailDTO=new ArchiveGetMaterialBoxDetailDTO();
       archiveGetMaterialBoxDetailDTO.setBoxNo("001");
       List<ArchiveGetMaterialBoxDetailDTO> list = new ArrayList<>();
       list.add(archiveGetMaterialBoxDetailDTO);

       ServiceData<List<ArchiveGetMaterialBoxDetailDTO>> serviceData=new ServiceData<>();
       RetCode retCode=new RetCode();
       retCode.setCode(Constant.SUCCESS_CODE);
       serviceData.setCode(retCode);
       serviceData.setBo(list);
       Mockito.when(archiveGetMaterialClient.selectGetMaterialBoxDetailByGetNo(Mockito.any())).thenReturn(serviceData);
   }

    private void selectGetMaterialBoxDetailByGetNoError(){
        ArchiveGetMaterialBoxDetailDTO archiveGetMaterialBoxDetailDTO=new ArchiveGetMaterialBoxDetailDTO();
        archiveGetMaterialBoxDetailDTO.setBoxNo("001");
        List<ArchiveGetMaterialBoxDetailDTO> list = new ArrayList<>();
        list.add(archiveGetMaterialBoxDetailDTO);

        ServiceData<List<ArchiveGetMaterialBoxDetailDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveGetMaterialClient.selectGetMaterialBoxDetailByGetNo(Mockito.any())).thenReturn(serviceData);
    }

    private void selectGetMaterialBoxDetailByGetNoNull(){
        ArchiveGetMaterialBoxDetailDTO archiveGetMaterialBoxDetailDTO=new ArchiveGetMaterialBoxDetailDTO();
        archiveGetMaterialBoxDetailDTO.setBoxNo("001");
        List<ArchiveGetMaterialBoxDetailDTO> list = new ArrayList<>();

        ServiceData<List<ArchiveGetMaterialBoxDetailDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveGetMaterialClient.selectGetMaterialBoxDetailByGetNo(Mockito.any())).thenReturn(serviceData);
    }

    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveGetMaterialDTO archiveGetMaterialDTO=new ArchiveGetMaterialDTO();
        archiveGetMaterialDTO.setGetNo("LLD040302478");
        archiveGetMaterialDTO.setApplyDateStr("2023-08-25 00:00:00");
        List<ArchiveGetMaterialDTO> list=new ArrayList<>();
        list.add(archiveGetMaterialDTO);

        Page<ArchiveGetMaterialDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveGetMaterialDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveGetMaterialClient.selectGetMaterialList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveGetMaterialDTO archiveGetMaterialDTO=new ArchiveGetMaterialDTO();
        archiveGetMaterialDTO.setGetNo("LLD040302478");
        archiveGetMaterialDTO.setApplyDateStr("2023-08-25 00:00:00");
        List<ArchiveGetMaterialDTO> list=new ArrayList<>();
        list.add(archiveGetMaterialDTO);

        Page<ArchiveGetMaterialDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveGetMaterialDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveGetMaterialClient.selectGetMaterialList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        Page<ArchiveGetMaterialDTO> page = new Page<>();
        page.setRows(null);
        ServiceData<Page<ArchiveGetMaterialDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveGetMaterialClient.selectGetMaterialList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

}
