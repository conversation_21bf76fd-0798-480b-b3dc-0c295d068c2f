package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.domain.model.ArchiveTaskConfig;
import com.zte.domain.model.ArchiveTaskConfigRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.ArchiveTaskSendRepository;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class})
public class ArchiveExtractServiceImplTest  extends PowerBaseTestCase {
    @InjectMocks
    ArchiveExtractServiceImpl extractService;

    @Mock
    ArchiveFileProperties archiveFileProperties ;

    @Mock
    ArchiveTaskConfigRepository taskArchiveConfigRepository;

    @Mock
    ArchiveTaskSendRepository taskSendArchiveRepository;

    @Mock
    ArchiveBusinessService archiveBusinessService;

    @Mock
    private ArchiveCommonService archiveCommonService;


    @Test
    public void extractArchiveDataLock(){
        lockTrue();
        getTaskConfig();
        try{
            extractService.extractArchiveData();
        } catch (Exception e) {
            Assert.assertEquals("extractArchiveData error {}", e.getMessage());
        }
    }

    @Test(expected = MesBusinessException.class)
    public void extractArchiveDataUnLock(){
        lockFalse();
        getTaskConfig();
        unlock();
        extractService.extractArchiveData();
    }

    @Test
    public void extractArchiveDataEx(){
        lockTrue();
        getTaskConfigNull();
        try{
            extractService.extractArchiveData();
        } catch (Exception e) {
            Assert.assertEquals("extractArchiveData error {}", e.getMessage());
        }
    }

    private void lockTrue(){
        Mockito.when(archiveCommonService.lock(Mockito.anyString())).thenReturn(1);
    }

    private void lockFalse(){
        Mockito.when(archiveCommonService.lock(Mockito.anyString())).thenReturn(0);
    }

    private void unlock(){
        Mockito.when(archiveCommonService.unlock(Mockito.anyString())).thenReturn(1);
    }

    private void getTaskConfig(){
        List<ArchiveTaskConfig> configList = new ArrayList<>();
        ArchiveTaskConfig taskConfig=new ArchiveTaskConfig();
        taskConfig.setTaskType("1");

        ArchiveTaskConfig taskConfig2=new ArchiveTaskConfig();
        taskConfig2.setTaskType("2");
        configList.add(taskConfig);
        configList.add(taskConfig2);
       Mockito.when(taskArchiveConfigRepository.selectList(Mockito.any())).thenReturn(configList);
    }

    private void getTaskConfigNull(){
        Mockito.when(taskArchiveConfigRepository.selectList(Mockito.any())).thenReturn(null);
    }

    @Test
    public void syncArchiveData(){
        ArchiveTaskConfig taskConfig = new ArchiveTaskConfig();
        extractService.syncArchiveData(taskConfig);
        Assert.assertNotNull(taskConfig);
    }

    @Test
    public void syncArchiveData1(){
        ArchiveTaskConfig taskConfig = new ArchiveTaskConfig();
        taskConfig.setLastSynDate(null);
        extractService.syncArchiveData(taskConfig);
        Assert.assertNotNull(taskConfig);
    }

    @Test
    public void syncArchiveData2(){
        ArchiveTaskConfig taskConfig = new ArchiveTaskConfig();
        taskConfig.setLastSynDate(new Date());
        taskConfig.setTaskType("1");
        taskConfig.setStep(10);

        PowerMockito.mockStatic(SpringContextUtil.class);

        extractService.syncArchiveData(taskConfig);
        Assert.assertNotNull(taskConfig);
    }

    @Test
    public void syncArchiveData3(){
        ArchiveTaskConfig taskConfig = new ArchiveTaskConfig();
        taskConfig.setLastSynDate(new Date());
        taskConfig.setTaskType("1");

        PowerMockito.mockStatic(SpringContextUtil.class);

        List<ArchiveTaskSend> list = new ArrayList<>();
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("CS1712210002");

        ArchiveTaskSend archiveTaskSend2 = new ArchiveTaskSend();
        archiveTaskSend2.setTaskId("2");
        archiveTaskSend2.setTaskType("2");
        archiveTaskSend2.setBillId("262");
        archiveTaskSend2.setBillNo("CS1712210003");

        list.add(archiveTaskSend2);

        Page<ArchiveTaskSend> page = new Page<>();
        page.setRows(list);

        Mockito.when(archiveBusinessService.getArchiveDataList(Mockito.any())).thenReturn(page);
        Mockito.doNothing().when(taskSendArchiveRepository).batchSave(Mockito.any());
        extractService.syncArchiveData(taskConfig);
        Assert.assertNotNull(taskConfig);
    }

    @Test
    public void syncArchiveData4(){
        ArchiveTaskConfig taskConfig = new ArchiveTaskConfig();
        taskConfig.setLastSynDate(new Date());
        taskConfig.setTaskType("1");

        PowerMockito.mockStatic(SpringContextUtil.class);

        List<ArchiveTaskSend> list = new ArrayList<>();
        Page<ArchiveTaskSend> page = new Page<>();
        page.setRows(list);

        Mockito.when(archiveBusinessService.getArchiveDataList(Mockito.any())).thenReturn(page);
        Mockito.doNothing().when(taskSendArchiveRepository).batchSave(Mockito.any());
        extractService.syncArchiveData(taskConfig);
        Assert.assertNotNull(taskConfig);
    }

    @Test
    public void selectById(){
        Mockito.when(taskSendArchiveRepository.selectById(Mockito.anyString())).thenReturn(null);
        Assert.assertNull(extractService.selectById(""));
    }
}
