package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveBoxupBillClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  装箱清单单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public class ArchiveBoxupBillServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveBoxupBillServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveBoxupBillClient archiveBoxupBillClient;


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("6");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("ZX201906272520");
        selectBoxupBillByBoxupBillId();
        selectBoxupBillDetailByBoxupBillId();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test(expected = Exception.class)
    public void archiveNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("6");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("ZX201906272520");
        selectBoxupBillByBoxupBillIdNull();
        selectBoxupBillDetailByBoxupBillIdNull();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test(expected = RouteException.class)
    public void archiveServerError() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("6");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("ZX201906272520");
        selectBoxupBillByBoxupBillIdServerError();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test
    public void archiveServerError2() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("6");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("ZX201906272520");
        selectBoxupBillByBoxupBillId();
        selectBoxupBillDetailByBoxupBillIdServerError();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveBusinessOrgAndDateIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("6");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("ZX201906272520");
        selectBoxupBillByBoxupBillId();
        selectBoxupBillDetailByBoxupBillId();
        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveBusiness() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("6");
        taskSendArchive.setBillId("1");
        taskSendArchive.setBillNo("ZX201906272520");
        selectBoxupBillByBoxupBillId();
        selectBoxupBillDetailByBoxupBillId();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }
    private void selectBoxupBillByBoxupBillId(){
        ServiceData<List<ArchiveBoxupBillDTO>> serviceData=new ServiceData<>();
        ArchiveBoxupBillDTO archiveBoxupBillDTO = new ArchiveBoxupBillDTO();
        archiveBoxupBillDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDTO.setBoxupBillNo("ZX201906272520");
        archiveBoxupBillDTO.setLastUpdateBy("熊成超10144021");
        archiveBoxupBillDTO.setLastUpdateDateStr("2023-08-08 00:00:00");
        List<ArchiveBoxupBillDTO> list=new ArrayList<>();
        list.add(archiveBoxupBillDTO);

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveBoxupBillClient.selectBoxupBillByBoxupBillId(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectBoxupBillByBoxupBillIdServerError(){
        ServiceData<List<ArchiveBoxupBillDTO>> serviceData=new ServiceData<>();
        ArchiveBoxupBillDTO archiveBoxupBillDTO = new ArchiveBoxupBillDTO();
        archiveBoxupBillDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDTO.setBoxupBillNo("ZX201906272520");
        archiveBoxupBillDTO.setLastUpdateBy("熊成超10144021");
        archiveBoxupBillDTO.setLastUpdateDateStr("2023-08-08 00:00:00");
        List<ArchiveBoxupBillDTO> list=new ArrayList<>();
        list.add(archiveBoxupBillDTO);

        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveBoxupBillClient.selectBoxupBillByBoxupBillId(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectBoxupBillByBoxupBillIdNull(){
        ServiceData<List<ArchiveBoxupBillDTO>> serviceData=new ServiceData<>();
        ArchiveBoxupBillDTO archiveBoxupBillDTO = new ArchiveBoxupBillDTO();
        archiveBoxupBillDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDTO.setBoxupBillNo("ZX201906272520");
        archiveBoxupBillDTO.setLastUpdateBy("熊成超10144021");
        archiveBoxupBillDTO.setLastUpdateDateStr("2023-08-08 00:00:00");
        List<ArchiveBoxupBillDTO> list=new ArrayList<>();

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveBoxupBillClient.selectBoxupBillByBoxupBillId(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectBoxupBillDetailByBoxupBillId(){
        ServiceData<List<ArchiveBoxupBillDetailDTO>> serviceData=new ServiceData<>();
        ArchiveBoxupBillDetailDTO archiveBoxupBillDetailDTO = new ArchiveBoxupBillDetailDTO();
        archiveBoxupBillDetailDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDetailDTO.setBigBoxNo("ZX201906272520");
        List<ArchiveBoxupBillDetailDTO> list=new ArrayList<>();
        list.add(archiveBoxupBillDetailDTO);

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveBoxupBillClient.selectBoxupBillDetailByBoxupBillId(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectBoxupBillDetailByBoxupBillIdServerError(){
        ServiceData<List<ArchiveBoxupBillDetailDTO>> serviceData=new ServiceData<>();
        ArchiveBoxupBillDetailDTO archiveBoxupBillDetailDTO = new ArchiveBoxupBillDetailDTO();
        archiveBoxupBillDetailDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDetailDTO.setBigBoxNo("ZX201906272520");
        List<ArchiveBoxupBillDetailDTO> list=new ArrayList<>();
        list.add(archiveBoxupBillDetailDTO);

        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveBoxupBillClient.selectBoxupBillDetailByBoxupBillId(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectBoxupBillDetailByBoxupBillIdNull(){
        ServiceData<List<ArchiveBoxupBillDetailDTO>> serviceData=new ServiceData<>();
        ArchiveBoxupBillDetailDTO archiveBoxupBillDetailDTO = new ArchiveBoxupBillDetailDTO();
        archiveBoxupBillDetailDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDetailDTO.setBigBoxNo("ZX201906272520");
        List<ArchiveBoxupBillDetailDTO> list=new ArrayList<>();

//        RetCode retCode=new RetCode();
//        retCode.setCode(Constant.SUCCESS_CODE);
//        serviceData.setCode(retCode);
//        serviceData.setBo(list);
//        Mockito.when(archiveBoxupBillClient.selectBoxupBillDetailByBoxupBillId(Mockito.anyString())).thenReturn(serviceData);
    }

    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }


    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveBoxupBillDTO archiveBoxupBillDTO = new ArchiveBoxupBillDTO();
        archiveBoxupBillDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDTO.setBoxupBillNo("ZX201906272520");
        List<ArchiveBoxupBillDTO> list=new ArrayList<>();
        list.add(archiveBoxupBillDTO);

        Page<ArchiveBoxupBillDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveBoxupBillDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveBoxupBillClient.selectBoxupBillList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveBoxupBillDTO archiveBoxupBillDTO = new ArchiveBoxupBillDTO();
        archiveBoxupBillDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDTO.setBoxupBillNo("ZX201906272520");
        List<ArchiveBoxupBillDTO> list=new ArrayList<>();
        list.add(archiveBoxupBillDTO);

        Page<ArchiveBoxupBillDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveBoxupBillDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveBoxupBillClient.selectBoxupBillList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveBoxupBillDTO archiveBoxupBillDTO = new ArchiveBoxupBillDTO();
        archiveBoxupBillDTO.setBoxupBillId(new BigDecimal(1));
        archiveBoxupBillDTO.setBoxupBillNo("ZX201906272520");
        List<ArchiveBoxupBillDTO> list=new ArrayList<>();

        Page<ArchiveBoxupBillDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveBoxupBillDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveBoxupBillClient.selectBoxupBillList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }
}
