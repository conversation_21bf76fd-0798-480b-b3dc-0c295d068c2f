package com.zte.application.impl;

import com.zte.application.datawb.impl.EntryBoxAppliedServiceImpl;
import com.zte.domain.model.MesGetDictInforRepository;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.util.httpclient.HttpClientUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024-11-06 8:31
 */
/* Started by AICoder, pid:l7dd62fb04w507c1488c09d6a0f2ac202d6512ba */
@PrepareForTest({DatabaseContextHolder.class, HttpClientUtil.class,JacksonJsonConverUtil.class})
public class EntryBoxAppliedServiceImplTest extends BaseTestCase {
    @InjectMocks
    private EntryBoxAppliedServiceImpl entryBoxAppliedService;
    @Mock
    private MesGetDictInforRepository mesGetDictInforRepository;

    @Before
    public void init(){
        PowerMockito.mockStatic(DatabaseContextHolder.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void getZMatOriLand() throws Exception{
        try {
            entryBoxAppliedService.getZMatOriLand(Arrays.asList("123"));
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }

    }
}

/* Ended by AICoder, pid:l7dd62fb04w507c1488c09d6a0f2ac202d6512ba */
