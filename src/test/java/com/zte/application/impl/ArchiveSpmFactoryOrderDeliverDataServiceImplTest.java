package com.zte.application.impl;

import com.zte.application.ArchiveSpmFactoryOrderDeliverDataService;
import com.zte.domain.model.ArchiveSpmFactoryOrderDeliverRepository;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/28
 * @description :
 */
public class ArchiveSpmFactoryOrderDeliverDataServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ArchiveSpmFactoryOrderDeliverDataServiceImpl archiveSpmFactoryOrderDeliverDataService;
    @Mock
    ArchiveSpmFactoryOrderDeliverRepository repository;

    @Test
    public void getByRecordId(){
        Assert.assertNull(archiveSpmFactoryOrderDeliverDataService.getByRecordId(null));
    }
    @Test
    public void getByRecordId1(){
        Assert.assertNull(archiveSpmFactoryOrderDeliverDataService.getByRecordId("123"));
    }
    @Test
    public void getSpmFactoryReturnByRecordId(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getSpmFactoryReturnByRecordId(null));
    }
    @Test
    public void getSpmFactoryReturnByRecordId1(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getSpmFactoryReturnByRecordId("123"));
    }
    @Test
    public void getSpmProdPlanByRecordId(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getSpmProdPlanByRecordId(null,null));
    }
    @Test
    public void getSpmProdPlanByRecordId1(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getSpmProdPlanByRecordId("123","123"));
    }
    @Test
    public void getSpmFactoryProcessDefineByOrderId(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getSpmFactoryProcessDefineByOrderId(null));
    }
    @Test
    public void getSpmFactoryProcessDefineByOrderId1(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getSpmFactoryProcessDefineByOrderId("123"));
    }


    @Test
    public void getByDateRange(){
        Assert.assertNotNull(archiveSpmFactoryOrderDeliverDataService.getByDateRange(null));
    }
    @Test
    public void getByDateRange1(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        query.setStartDate(new Date());
        query.setEndDate(new Date());
        Page<ArchiveSpmFactoryOrderDeliverDTO> page = archiveSpmFactoryOrderDeliverDataService.getByDateRange(query);
        Assert.assertTrue(page.getRows().size() >= 0);
    }
}
