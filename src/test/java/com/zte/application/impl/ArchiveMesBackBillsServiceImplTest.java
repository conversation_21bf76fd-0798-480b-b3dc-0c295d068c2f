package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveMesBackBillsDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveMesBackBillsDTO;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/16
 * @description :
 */
public class ArchiveMesBackBillsServiceImplTest extends BaseTestCase {
    @InjectMocks
    ArchiveMesBackBillsServiceImpl archiveMesBackBillsService;

    @Mock
    ArchiveMesBackBillsDataService archiveMesBackBillsDataService;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    PubHrvOrgService pubHrvOrgService;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Test
    public void archive(){
        try {
            archiveMesBackBillsService.archive(null);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void archive1(){
        try {
            archiveMesBackBillsService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void archive2(){
        getByBackNumber();
        try {
            archiveMesBackBillsService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    private void getByBackNumber() {
        ArchiveMesBackBillsDTO dto = new ArchiveMesBackBillsDTO();
        Mockito.when(archiveMesBackBillsDataService.getByBackNumber(any()))
                .thenReturn(dto);
    }

    private ArchiveTaskSend getArchiveTaskSend() {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setBillNo("123");
        return archiveTaskSend;
    }

    @Test
    public void getArchiveDataList(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(400);
        query.setPage(1);
        ArchiveMesBackBillsDTO dto = new ArchiveMesBackBillsDTO();

        Page<ArchiveMesBackBillsDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(archiveMesBackBillsDataService.getPageByDateRange(any()))
                .thenReturn(page);
        Assert.assertNotNull(archiveMesBackBillsService.getArchiveDataList(query));
    }
}
