package com.zte.application.impl;

import com.zte.domain.model.SpecialNeedsRepository;
import com.zte.interfaces.dto.SpecialNeedsDetailDTO;
import com.zte.interfaces.dto.SpecialNeedsHeadDTO;
import com.zte.interfaces.dto.SpecialNeedsItemDTO;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @Date 2025/4/3 16:32
 */
public class SpecialNeedsServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    private SpecialNeedsServiceImpl service;
    @Mock
    private SpecialNeedsRepository specialNeedsRepository;

    @Test
    public void searchHeadTableData () {
        Assert.assertNotNull(service.searchHeadTableData(new SpecialNeedsHeadDTO()));
        Assert.assertNotNull(service.searchDetailTableData(new SpecialNeedsDetailDTO()));
        Assert.assertNotNull(service.searchItemTableData(new SpecialNeedsItemDTO()));
        Assert.assertNotNull(service.searchHeadTableData(new SpecialNeedsHeadDTO(){{setContractNumber("123");}}));
        Assert.assertNotNull(service.searchDetailTableData(new SpecialNeedsDetailDTO(){{setEntityId("123");}}));
        Assert.assertNotNull(service.searchItemTableData(new SpecialNeedsItemDTO(){{setMfgSiteId("123");}}));
    }
}
