package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveSemiprodRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveSemiprodDTO;
import com.zte.interfaces.dto.ArchiveSemiprodDetailDTO;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  半成品检验管理单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public class ArchiveSemiprodServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveSemiprodServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveSemiprodRepository archiveSemiprodRepository;

   @Test
    public void archive() throws Exception {
       ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
       taskSendArchive.setTaskType("3");

       ArchiveSemiprodDTO archiveSemiprodDTO=new ArchiveSemiprodDTO();
       archiveSemiprodDTO.setCheckNo("SJD171108210482");
       Mockito.when(archiveSemiprodRepository.getSemiprodById(Mockito.any())).thenReturn(archiveSemiprodDTO);

       List<ArchiveSemiprodDetailDTO> archiveSemiprodDetailDTOList = new ArrayList<>();
       ArchiveSemiprodDetailDTO archiveSemiprodDetailDTO=new ArchiveSemiprodDetailDTO();
       archiveSemiprodDetailDTO.setCheckNo("SJD171108210482");
       archiveSemiprodDetailDTO.setCheckDate(new Date());
       archiveSemiprodDetailDTO.setCheckBy("10144021");
       archiveSemiprodDetailDTOList.add(archiveSemiprodDetailDTO);

       Mockito.when(archiveSemiprodRepository.getSemiprodDetailById(Mockito.any())).thenReturn(archiveSemiprodDetailDTOList);
       createExcelAndUpload();
       buildBusiness();
       Assert.assertNotNull(service.archive(taskSendArchive));

   }


    @Test(expected = MesBusinessException.class)
    public void archiveSemiprodDetailDTONull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("3");

        ArchiveSemiprodDTO archiveSemiprodDTO=new ArchiveSemiprodDTO();
        archiveSemiprodDTO.setCheckNo("SJD171108210482");
        Mockito.when(archiveSemiprodRepository.getSemiprodById(Mockito.any())).thenReturn(archiveSemiprodDTO);

        List<ArchiveSemiprodDetailDTO> archiveSemiprodDetailDTOList = new ArrayList<>();

        Mockito.when(archiveSemiprodRepository.getSemiprodDetailById(Mockito.any())).thenReturn(archiveSemiprodDetailDTOList);
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test(expected = MesBusinessException.class)
    public void archiveSemiprodDTONull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("3");

        Mockito.when(archiveSemiprodRepository.getSemiprodById(Mockito.any())).thenReturn(null);
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test
    public void archiveOrgAndDateIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("3");

        ArchiveSemiprodDTO archiveSemiprodDTO=new ArchiveSemiprodDTO();
        archiveSemiprodDTO.setCheckNo("SJD171108210482");
        Mockito.when(archiveSemiprodRepository.getSemiprodById(Mockito.any())).thenReturn(archiveSemiprodDTO);

        List<ArchiveSemiprodDetailDTO> archiveSemiprodDetailDTOList = new ArrayList<>();
        ArchiveSemiprodDetailDTO archiveSemiprodDetailDTO=new ArchiveSemiprodDetailDTO();
        archiveSemiprodDetailDTO.setCheckNo("SJD171108210482");
        archiveSemiprodDetailDTO.setCheckDate(new Date());
        archiveSemiprodDetailDTO.setCheckBy("10144021");
        archiveSemiprodDetailDTOList.add(archiveSemiprodDetailDTO);

        Mockito.when(archiveSemiprodRepository.getSemiprodDetailById(Mockito.any())).thenReturn(archiveSemiprodDetailDTOList);
        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        ArchiveQueryParamDTO dto = new ArchiveQueryParamDTO();
        Assert.assertNotNull(dto);
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveSemiprodDTO archiveSemiprodDTO = new ArchiveSemiprodDTO();
        archiveSemiprodDTO.setCheckNo("001");

        List<ArchiveSemiprodDTO> archiveSemiprodDTOList = new ArrayList<>();
        archiveSemiprodDTOList.add(archiveSemiprodDTO);
        Page<ArchiveSemiprodDTO> page = new Page<>();
        page.setRows(archiveSemiprodDTOList);

        Mockito.when(archiveSemiprodRepository.selectSemiprodListByPage(Mockito.any())).thenReturn(archiveSemiprodDTOList);
        service.getArchiveDataList(archiveQueryParamDTO);
    }


    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        Mockito.when(archiveSemiprodRepository.selectSemiprodListByPage(Mockito.any())).thenReturn(null);
        service.getArchiveDataList(archiveQueryParamDTO);
    }
}
