package com.zte.application.impl;

import com.zte.application.ArchiveBusinessService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.MesGetDictInforService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.model.MessageId;
import com.zte.common.utils.ArchiveConstant;
import com.zte.interfaces.dto.ArchiveReq;
import com.zte.interfaces.dto.CallbackArchiveFileReq;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ArchiveSendLogRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.ArchiveTaskSendRepository;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtil.class})
public class ArchiveServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ArchiveServiceImpl archiveService;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Mock
    ArchiveTaskSendRepository taskSendArchiveRepository;


    @Mock
    ArchiveBusinessService archiveBusinessService;



    @Mock
    private ArchiveSendLogRepository sendArchiveLogRepository;


    @Mock
    private MesGetDictInforService mesGetDictInforService;

    @Test
    public void sendArchiveTask0(){
        lockTrue();
        try{
            archiveService.sendArchiveTask(null);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test(expected = MesBusinessException.class)
    public void sendArchiveTask1(){
        lockFalse();
        getDescriptionNull();
        archiveService.sendArchiveTask(null);
    }
    @Test
    public void sendArchiveTask11(){
        lockTrue();
        getDescriptionNotNull();
        selectTaskSendArchive();
        sendArchiveTask2();
        try{
            archiveService.sendArchiveTask(null);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void sendArchiveTask2(){
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        selectTaskSendArchive();
        updateTaskSendArchiveVersion();
        SpringContextUtilGetBean();
        try{
            archiveService.sendSignArchiveTask(archiveTaskSend);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void sendArchiveTask3(){
        try {
            ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
            updateTaskSendArchiveVersionError();
            SpringContextUtilGetBean();
            getArchiveItem();
            getServiceDataSuccess();
            archiveService.sendSignArchiveTask(archiveTaskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void sendArchiveTask4(){
        try {
            ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
            updateTaskSendArchiveVersion();
            SpringContextUtilGetBean();
            getArchiveItem();
            getServiceDataSuccess();
            archiveService.sendSignArchiveTask(archiveTaskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void sendArchiveTask5(){
        try {
            ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
            selectTaskSendArchive();
            updateTaskSendArchiveVersion();
            SpringContextUtilGetBean();
            getArchiveItem();
            getServiceDataSuccess();
            archiveService.sendSignArchiveTask(archiveTaskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void sendArchiveTask6() throws Exception {
            ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
            updateTaskSendArchiveVersion();
            SpringContextUtilGetBean();
            getArchiveItem();
            getServiceDataSuccess();
            archiveService.sendSignArchiveTask(archiveTaskSend);
            Assert.assertNotNull(archiveTaskSend);
    }

    @Test
    public void sendArchiveTask7() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        updateTaskSendArchiveVersion();
        SpringContextUtilGetBean();
        getArchiveItem();
        getServiceDataError();
        archiveService.sendSignArchiveTask(archiveTaskSend);
        Assert.assertNotNull(archiveTaskSend);
    }

    @Test
    public void sendArchiveTask8() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        updateTaskSendArchiveVersion();
        SpringContextUtilGetBean();
        getArchiveItem();
        getServiceDataEx();
        archiveService.sendSignArchiveTask(archiveTaskSend);
        Assert.assertNotNull(archiveTaskSend);
    }

    @Test
    public void sendArchiveTask9() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        updateTaskSendArchiveVersion();
        SpringContextUtilGetBeanNull();
        getArchiveItem();
        getServiceDataEx();
        archiveService.sendSignArchiveTask(archiveTaskSend);
        Assert.assertNotNull(archiveTaskSend);
    }

    @Test
    public void sendArchiveTask10() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        updateTaskSendArchiveVersion();
        SpringContextUtilGetBeanMesBusEx();
        getArchiveItem();
        getServiceDataEx();
        archiveService.sendSignArchiveTask(archiveTaskSend);
        Assert.assertNotNull(archiveTaskSend);
    }


    private void getServiceDataSuccess() throws Exception {
        ServiceData result = new ServiceData();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        result.setCode(retCode);
        Mockito.when(archiveCommonService.sendDataToArchive(Mockito.any(), Mockito.any())).thenReturn(result);
    }

    private void getServiceDataError() throws Exception {
        ServiceData result = new ServiceData();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        result.setCode(retCode);
        Mockito.when(archiveCommonService.sendDataToArchive(Mockito.any(), Mockito.any())).thenReturn(result);
    }

    private void getServiceDataEx() throws Exception {
        Mockito.when(archiveCommonService.sendDataToArchive(Mockito.any(), Mockito.any())).thenReturn(null);
    }

    private void getArchiveItem() throws Exception {
        ArchiveReq.ArchiveItem archiveItem = new ArchiveReq.ArchiveItem();
        Mockito.when(archiveBusinessService.archive(Mockito.any())).thenReturn(archiveItem);
    }

    private void SpringContextUtilGetBean() {
        PowerMockito.mockStatic(SpringContextUtil.class);
    }

    private void SpringContextUtilGetBeanNull() {
        PowerMockito.mockStatic(SpringContextUtil.class);
    }

    private void SpringContextUtilGetBeanMesBusEx() {
        PowerMockito.mockStatic(SpringContextUtil.class);
    }

    private void updateTaskSendArchiveVersion() {
        Mockito.when(taskSendArchiveRepository.updateTaskSendArchiveVersion(Mockito.any())).thenReturn(ArchiveConstant.INTEGER_1);
    }

    private void updateTaskSendArchiveVersionError() {
        Mockito.when(taskSendArchiveRepository.updateTaskSendArchiveVersion(Mockito.any())).thenReturn(ArchiveConstant.INTEGER_0);
    }


    private void selectTaskSendArchive(){
        List<ArchiveTaskSend> list = new ArrayList<>();
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("CS1712210002");
        list.add(archiveTaskSend);
        Mockito.when(taskSendArchiveRepository.selectTaskSendArchive(Mockito.any())).thenReturn(list);
    }

    @Test
    public void receiveCallback(){
        CallbackArchiveFileReq req = new CallbackArchiveFileReq();
        req.setFileKey("invalid");
        archiveService.receiveCallback(req);
        Assert.assertNotNull(req);
    }

    @Test
    public void receiveCallback1() {
        CallbackArchiveFileReq req = new CallbackArchiveFileReq();
        req.setFileKey("1_123_123");
        req.setStatus("SUCCESS");
        ArchiveTaskSend archive = new ArchiveTaskSend();
        archive.setBillNo("123");

        Mockito.when(taskSendArchiveRepository.getArchiveByBillId(Mockito.anyString(), Mockito.anyString())).thenReturn(archive);
        Mockito.when(archiveFileProperties.getEmpNo()).thenReturn("admin");
        Mockito.doNothing().when(sendArchiveLogRepository).insert(Mockito.any());
        archiveService.receiveCallback(req);
        Assert.assertNotNull(req);
    }

    @Test
    public void archiveClearFile(){
        try{
            archiveService.archiveClearFile("file.xml");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void archiveClearFileEx(){
        try{
            archiveService.archiveClearFile(null);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void selectById(){
        Mockito.when(taskSendArchiveRepository.selectById(Mockito.anyString())).thenReturn(null);
        Assert.assertNull(archiveService.selectById(""));
    }

    private void lockTrue(){
        Mockito.when(archiveCommonService.lock(Mockito.anyString())).thenReturn(1);
    }

    private void lockFalse(){
        Mockito.when(archiveCommonService.lock(Mockito.anyString())).thenReturn(0);
    }

    private void getDescriptionNotNull(){
        Mockito.when(archiveCommonService.getDicDescriptionByCode(Mockito.anyString())).thenReturn(10);
    }
    private void getDescriptionNull(){
        Mockito.when(archiveCommonService.getDicDescriptionByCode(Mockito.anyString())).thenReturn(null);
    }
}
