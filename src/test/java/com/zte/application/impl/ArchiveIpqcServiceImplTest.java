package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.impl.ArchiveIpqcServiceImpl;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.infrastructure.feign.ArchiveIpqcClient;
import com.zte.interfaces.dto.ArchiveIpqcDTO;
import com.zte.interfaces.dto.ArchiveIpqcDetailDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  IPQC确认单据归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public class ArchiveIpqcServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveIpqcServiceImpl archiveIpqcService;

    @Mock
    private ArchiveIpqcClient archiveIpqcClient;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;


    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("CS1712210002");

        selectIpqcByIdSuccess();
        selectIpqcDetailByHeaderIdSuccess();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(archiveIpqcService.archive(archiveTaskSend));


    }
    @Test
    public void archiveNull() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("CS1712210002");

        selectIpqcByIdDateIsNull();
        selectIpqcDetailByHeaderIdBoIsNull();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(archiveIpqcService.archive(archiveTaskSend));
    }

    @Test
    public void archiveException() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("CS1712210002");

        selectIpqcByIdServerError();
        selectIpqcDetailByHeaderIdServerError();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(archiveIpqcService.archive(archiveTaskSend));
    }

    @Test
    public void archiveDetailIsNull() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("2");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("CS1712210002");

        selectIpqcByIdSuccess();
        selectIpqcDetailByHeaderIdServerError();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(archiveIpqcService.archive(archiveTaskSend));
    }



    private void selectIpqcByIdSuccess(){
        ArchiveIpqcDTO archiveIpqcDTO = new ArchiveIpqcDTO();
        archiveIpqcDTO.setId(new BigDecimal(262));
        archiveIpqcDTO.setTransNum("CS1712210002");
        archiveIpqcDTO.setTransStatus("已确认");
        archiveIpqcDTO.setApplyDate(new Date());
        archiveIpqcDTO.setAppliedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmDate(new Date());
        ServiceData<ArchiveIpqcDTO> serviceDataObjectVO=new ServiceData<ArchiveIpqcDTO>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(archiveIpqcDTO);
        Mockito.when(archiveIpqcClient.selectIpqcById(Mockito.anyString())).thenReturn(serviceDataObjectVO);
    }

    private void selectIpqcByIdServerError(){
        ArchiveIpqcDTO archiveIpqcDTO = new ArchiveIpqcDTO();
        archiveIpqcDTO.setId(new BigDecimal(262));
        archiveIpqcDTO.setTransNum("CS1712210002");
        archiveIpqcDTO.setTransStatus("已确认");
        archiveIpqcDTO.setApplyDate(new Date());
        archiveIpqcDTO.setAppliedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmDate(new Date());
        ServiceData<ArchiveIpqcDTO> serviceDataObjectVO=new ServiceData<ArchiveIpqcDTO>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(archiveIpqcDTO);
        Mockito.when(archiveIpqcClient.selectIpqcById(Mockito.any())).thenReturn(serviceDataObjectVO);
    }

    private void selectIpqcByIdDateIsNull(){
        ArchiveIpqcDTO archiveIpqcDTO = new ArchiveIpqcDTO();
        archiveIpqcDTO.setId(new BigDecimal(262));
        archiveIpqcDTO.setTransNum("CS1712210002");
        archiveIpqcDTO.setTransStatus("已确认");
        archiveIpqcDTO.setApplyDate(null);
        archiveIpqcDTO.setAppliedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmDate(null);
        ServiceData<ArchiveIpqcDTO> serviceDataObjectVO=new ServiceData<ArchiveIpqcDTO>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(archiveIpqcDTO);
        Mockito.when(archiveIpqcClient.selectIpqcById(Mockito.any())).thenReturn(serviceDataObjectVO);
    }

   private void selectIpqcDetailByHeaderIdSuccess(){
       ArchiveIpqcDetailDTO archiveIpqcDetailDTO = new ArchiveIpqcDetailDTO();
       archiveIpqcDetailDTO.setId(new BigDecimal(1));
       archiveIpqcDetailDTO.setItemBarcode("111");
       archiveIpqcDetailDTO.setCurrStationName("");
       archiveIpqcDetailDTO.setLastStationName("");
       archiveIpqcDetailDTO.setLinesStatus("ok");
       List<ArchiveIpqcDetailDTO> archiveIpqcDetailDTOList=new ArrayList<>();
       archiveIpqcDetailDTOList.add(archiveIpqcDetailDTO);

       ServiceData<List<ArchiveIpqcDetailDTO>> serviceDataObjectVO=new ServiceData<List<ArchiveIpqcDetailDTO>>();
       RetCode retCode=new RetCode();
       retCode.setCode(Constant.SUCCESS_CODE);
       serviceDataObjectVO.setCode(retCode);
       serviceDataObjectVO.setBo(archiveIpqcDetailDTOList);
       Mockito.when(archiveIpqcClient.selectIpqcDetailByHeaderId(Mockito.any())).thenReturn(serviceDataObjectVO);
   }


    private void selectIpqcDetailByHeaderIdServerError(){
        ArchiveIpqcDetailDTO archiveIpqcDetailDTO = new ArchiveIpqcDetailDTO();
        archiveIpqcDetailDTO.setId(new BigDecimal(1));
        archiveIpqcDetailDTO.setItemBarcode("111");
        archiveIpqcDetailDTO.setCurrStationName("");
        archiveIpqcDetailDTO.setLastStationName("");
        archiveIpqcDetailDTO.setLinesStatus("ok");
        List<ArchiveIpqcDetailDTO> archiveIpqcDetailDTOList=new ArrayList<>();
        archiveIpqcDetailDTOList.add(archiveIpqcDetailDTO);

        ServiceData<List<ArchiveIpqcDetailDTO>> serviceDataObjectVO=new ServiceData<List<ArchiveIpqcDetailDTO>>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(archiveIpqcDetailDTOList);
        Mockito.when(archiveIpqcClient.selectIpqcDetailByHeaderId(Mockito.any())).thenReturn(serviceDataObjectVO);
    }

    private void selectIpqcDetailByHeaderIdBoIsNull(){
        ServiceData<List<ArchiveIpqcDetailDTO>> serviceDataObjectVO=new ServiceData<List<ArchiveIpqcDetailDTO>>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(null);
        Mockito.when(archiveIpqcClient.selectIpqcDetailByHeaderId(Mockito.any())).thenReturn(serviceDataObjectVO);
    }

    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }


    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveIpqcDTO archiveIpqcDTO = new ArchiveIpqcDTO();
        archiveIpqcDTO.setId(new BigDecimal(262));
        archiveIpqcDTO.setTransNum("CS1712210002");
        archiveIpqcDTO.setTransStatus("已确认");
        archiveIpqcDTO.setApplyDate(new Date());
        archiveIpqcDTO.setAppliedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmDate(new Date());
        List<ArchiveIpqcDTO> archiveIpqcDTOList = new ArrayList<>();
        archiveIpqcDTOList.add(archiveIpqcDTO);
        Page<ArchiveIpqcDTO> page = new Page<>();
        page.setRows(archiveIpqcDTOList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<ArchiveIpqcDTO>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveIpqcClient.selectIpqcList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveIpqcService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveIpqcDTO archiveIpqcDTO = new ArchiveIpqcDTO();
        archiveIpqcDTO.setId(new BigDecimal(262));
        archiveIpqcDTO.setTransNum("CS1712210002");
        archiveIpqcDTO.setTransStatus("已确认");
        archiveIpqcDTO.setApplyDate(new Date());
        archiveIpqcDTO.setAppliedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmedBy("熊成超10144021");
        archiveIpqcDTO.setConfirmDate(new Date());
        List<ArchiveIpqcDTO> archiveIpqcDTOList = new ArrayList<>();
        archiveIpqcDTOList.add(archiveIpqcDTO);
        Page<ArchiveIpqcDTO> page = new Page<>();
        page.setRows(archiveIpqcDTOList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<ArchiveIpqcDTO>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveIpqcClient.selectIpqcList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveIpqcService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);

        List<ArchiveIpqcDTO> archiveIpqcDTOList = new ArrayList<>();
        Page<ArchiveIpqcDTO> page = new Page<>();
        page.setRows(archiveIpqcDTOList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<ArchiveIpqcDTO>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveIpqcClient.selectIpqcList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveIpqcService.getArchiveDataList(archiveQueryParamDTO);
    }


} 
