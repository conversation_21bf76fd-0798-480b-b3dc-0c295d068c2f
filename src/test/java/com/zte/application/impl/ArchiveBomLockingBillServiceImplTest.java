package com.zte.application.impl;

import com.zte.application.ArchiveBomLockingBillDataService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveBomLockingBillDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/16
 * @description :
 */
public class ArchiveBomLockingBillServiceImplTest extends BaseTestCase {
    @InjectMocks
    ArchiveBomLockingBillServiceImpl archiveBomLockingBillService;

    @Mock
    ArchiveBomLockingBillDataService archiveBomLockingBillDataService;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    PubHrvOrgService pubHrvOrgService;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Test
    public void archive(){
        try {
            archiveBomLockingBillService.archive(null);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void archive1(){
        try {
            archiveBomLockingBillService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


    @Test
    public void archive2(){
        getByBillNo();
        try {
            archiveBomLockingBillService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    private void getByBillNo() {
        ArchiveBomLockingBillDTO dto = new ArchiveBomLockingBillDTO();
        Mockito.when(archiveBomLockingBillDataService.getByBillNo(any()))
                .thenReturn(dto);
    }

    private ArchiveTaskSend getArchiveTaskSend() {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setBillNo("123");
        return archiveTaskSend;
    }

    @Test
    public void getArchiveDataList(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(400);
        query.setPage(1);
        ArchiveBomLockingBillDTO dto = new ArchiveBomLockingBillDTO();

        Page<ArchiveBomLockingBillDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(archiveBomLockingBillDataService.getPageByDateRange(any()))
                .thenReturn(page);
        Assert.assertNotNull(archiveBomLockingBillService.getArchiveDataList(query));
    }
}
