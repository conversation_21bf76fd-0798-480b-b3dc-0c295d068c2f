package com.zte.application.impl;

import com.zte.domain.model.ArchiveMesBackBillsDataRepository;
import com.zte.interfaces.dto.ArchiveMesBackBillsDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/16
 * @description :
 */
public class ArchiveMesBackBillsDataServiceImplTest extends BaseTestCase {

    @InjectMocks
    ArchiveMesBackBillsDataServiceImpl archiveMesBackBillsDataService;

    @Mock
    ArchiveMesBackBillsDataRepository repository;

    @Test
    public void getPageByDateRange(){
        Assert.assertNotNull(archiveMesBackBillsDataService.getPageByDateRange(null));
    }
    @Test
    public void getPageByDateRange1(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        query.setStartDate(new Date());
        query.setEndDate(new Date());
        Assert.assertNotNull(archiveMesBackBillsDataService.getPageByDateRange(query));
    }

    @Test
    public void getByBackNumber(){
        Assert.assertNull(archiveMesBackBillsDataService.getByBackNumber(null));
    }

    @Test
    public void getByBackNumber1(){
        Assert.assertNull(archiveMesBackBillsDataService.getByBackNumber("123"));
    }

    @Test
    public void getListByBackBillId(){
        Assert.assertNotNull(archiveMesBackBillsDataService.getListByBackBillId(null));
    }

    @Test
    public void getListByBackBillId1(){
        Assert.assertNotNull(archiveMesBackBillsDataService.getListByBackBillId("123"));
    }
}
