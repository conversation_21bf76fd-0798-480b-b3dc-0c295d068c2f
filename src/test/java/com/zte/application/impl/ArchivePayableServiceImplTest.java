package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchivePayableRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveProductWarehouseCheckClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.RouteMatcher;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  应付款结算查询归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
public class ArchivePayableServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchivePayableServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchivePayableRepository archivePayableRepository;


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("16");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("ZTE180726441");

        List<ArchivePayableDTO> archivePayableDTOs = new ArrayList<>();
        ArchivePayableDTO archivePayableDTO = new ArchivePayableDTO();
        archivePayableDTO.setBillNo("ZTE180726441");
        archivePayableDTO.setDealingDateStr("2023-08-31 00:00:00");
        archivePayableDTO.setDealingPerson("杨永辰10312837");
        archivePayableDTOs.add(archivePayableDTO);
        Mockito.when(archivePayableRepository.getPayableByBillNo(Mockito.any())).thenReturn(archivePayableDTOs);

        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test(expected = MesBusinessException.class)
    public void archivePayableDTNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("16");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("ZTE180726441");

        List<ArchivePayableDTO> archivePayableDTOs = new ArrayList<>();
        Mockito.when(archivePayableRepository.getPayableByBillNo(Mockito.any())).thenReturn(archivePayableDTOs);

        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }


    @Test
    public void archiveBusinessOrgAndDateIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("16");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("ZTE180726441");

        List<ArchivePayableDTO> archivePayableDTOs = new ArrayList<>();
        ArchivePayableDTO archivePayableDTO = new ArchivePayableDTO();
        archivePayableDTO.setBillNo("ZTE180726441");
        archivePayableDTO.setDealingDateStr("2023-08-31 00:00:00");
        archivePayableDTO.setDealingPerson("杨永辰10312837");
        archivePayableDTOs.add(archivePayableDTO);
        Mockito.when(archivePayableRepository.getPayableByBillNo(Mockito.any())).thenReturn(archivePayableDTOs);

        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }





    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchivePayableDTO> archivePayableDTOs = new ArrayList<>();
        ArchivePayableDTO archivePayableDTO = new ArchivePayableDTO();
        archivePayableDTO.setBillNo("ZTE180726441");
        archivePayableDTO.setDealingDateStr("2023-08-31 00:00:00");
        archivePayableDTO.setDealingPerson("杨永辰10312837");
        archivePayableDTOs.add(archivePayableDTO);

        Page<ArchivePayableDTO> page = new Page<>();
        page.setRows(archivePayableDTOs);

        Mockito.when(archivePayableRepository.selectPayableListByPage(Mockito.any())).thenReturn(archivePayableDTOs);
        service.getArchiveDataList(archiveQueryParamDTO);
    }



    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchivePayableDTO> archivePayableDTOs = new ArrayList<>();

        Mockito.when(archivePayableRepository.selectPayableListByPage(Mockito.any())).thenReturn(archivePayableDTOs);
        service.getArchiveDataList(archiveQueryParamDTO);
    }
}
