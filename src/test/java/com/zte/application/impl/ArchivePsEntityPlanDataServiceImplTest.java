package com.zte.application.impl;

import com.zte.domain.model.ArchivePsEntityPlanRepository;
import com.zte.interfaces.dto.ArchivePsEntityPlanDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/28
 * @description :
 */
public class ArchivePsEntityPlanDataServiceImplTest extends BaseTestCase {

    @InjectMocks
    ArchivePsEntityPlanDataServiceImpl archivePsEntityPlanDataService;
    @Mock
    ArchivePsEntityPlanRepository psEntityPlanRepository;

    @Test
    public void getByPlanNumber(){
        Assert.assertNull(archivePsEntityPlanDataService.getByPlanNumber(null));
    }
    @Test
    public void getByPlanNumber1(){
        Assert.assertNull(archivePsEntityPlanDataService.getByPlanNumber("123"));
    }

    @Test
    public void getDetailByEntityPlan(){
        Assert.assertNotNull(archivePsEntityPlanDataService.getDetailByEntityPlan(null));
    }

    @Test
    public void getDetailByEntityPlan1(){
        ArchivePsEntityPlanDTO dto = new ArchivePsEntityPlanDTO();
        Assert.assertNull(archivePsEntityPlanDataService.getDetailByEntityPlan(dto));
    }

    @Test
    public void getByDateRange(){
        Assert.assertNotNull(archivePsEntityPlanDataService.getByDateRange(null));
    }

    @Test
    public void getPsEntityPlanTotalRowByDateRange1(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setStartDate(new Date());
        query.setEndDate(new Date());
        query.setPage(1);
        query.setRows(200);
        Assert.assertNotNull(archivePsEntityPlanDataService.getByDateRange(query));
    }
}
