package com.zte.application.impl;


import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
public class PubHrvOrgServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    PubHrvOrgServiceImpl pubHrvOrgServiceImpl;
    @Mock
    PubHrvOrgRepository pubHrvOrgRepository;
    @Test
    public void getPubHrvOrgByUserNamePlus(){

        PubHrvOrg org =  pubHrvOrgServiceImpl.getPubHrvOrgByUserNamePlus("");
        Assert.assertEquals(org.getUserId(),"002801105");

        PowerMockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNamePlus(Mockito.any())).thenReturn(null);
        org =  pubHrvOrgServiceImpl.getPubHrvOrgByUserNamePlus("555");
        Assert.assertEquals(org.getUserId(),"002801105");

        PubHrvOrg inOrg = new PubHrvOrg();
        PowerMockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNamePlus(Mockito.any())).thenReturn(inOrg);
        org =  pubHrvOrgServiceImpl.getPubHrvOrgByUserNamePlus("555");
        Assert.assertEquals(org.getOrgNo(),null);

    }
}
