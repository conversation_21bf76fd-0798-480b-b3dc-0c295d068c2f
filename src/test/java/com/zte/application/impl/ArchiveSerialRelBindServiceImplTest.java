package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveSemiprodRepository;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveSerialRelBindClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  串号预关联绑定单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public class ArchiveSerialRelBindServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveSerialRelBindServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveSerialRelBindClient archiveSerialRelBindClient;


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("5");
        taskSendArchive.setBillNo("ZL1604070704");
        selectSerialRelBindByPlanNumber();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test(expected = Exception.class)
    public void archiveNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("5");
        taskSendArchive.setBillNo("ZL1604070704");
        selectSerialRelBindByPlanNumberNull();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test(expected = RouteException.class)
    public void archiveServerError() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("5");
        taskSendArchive.setBillNo("ZL1604070704");
        selectSerialRelBindByPlanNumberServerError();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test
    public void archiveBusinessOrgAndDateIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("5");
        taskSendArchive.setBillNo("ZL1604070704");
        selectSerialRelBindByPlanNumber();
        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveBusinessOrgIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("5");
        taskSendArchive.setBillNo("ZL1604070704");
        selectSerialRelBindByPlanNumber();
        createExcelAndUpload();
        buildBusinessOrgIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }


    private void selectSerialRelBindByPlanNumber(){
        ArchiveSerialRelBindDTO archiveSerialRelBindDTO=new ArchiveSerialRelBindDTO();
        archiveSerialRelBindDTO.setPlanNumber("ZL1604070704");
        archiveSerialRelBindDTO.setCreationDateStr("2023-01-01 00:00:00");
        archiveSerialRelBindDTO.setCreatedBy("6407003392");
        List<ArchiveSerialRelBindDTO> archiveSerialRelBindDTOS=new ArrayList<>();
        archiveSerialRelBindDTOS.add(archiveSerialRelBindDTO);

        ServiceData<List<ArchiveSerialRelBindDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(archiveSerialRelBindDTOS);
        Mockito.when(archiveSerialRelBindClient.selectSerialRelBindByPlanNumber(Mockito.anyString())).thenReturn(serviceData);
    }


    private void selectSerialRelBindByPlanNumberNull(){
        ArchiveSerialRelBindDTO archiveSerialRelBindDTO=new ArchiveSerialRelBindDTO();
        archiveSerialRelBindDTO.setPlanNumber("ZL1604070704");
        archiveSerialRelBindDTO.setCreationDateStr("2023-01-01 00:00:00");
        archiveSerialRelBindDTO.setCreatedBy("6407003392");

        List<ArchiveSerialRelBindDTO> archiveSerialRelBindDTOS=new ArrayList<>();
        ServiceData<List<ArchiveSerialRelBindDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(archiveSerialRelBindDTOS);
        Mockito.when(archiveSerialRelBindClient.selectSerialRelBindByPlanNumber(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectSerialRelBindByPlanNumberServerError(){
        ArchiveSerialRelBindDTO archiveSerialRelBindDTO=new ArchiveSerialRelBindDTO();
        archiveSerialRelBindDTO.setPlanNumber("ZL1604070704");
        archiveSerialRelBindDTO.setCreationDateStr("2023-01-01 00:00:00");
        archiveSerialRelBindDTO.setCreatedBy("6407003392");

        List<ArchiveSerialRelBindDTO> archiveSerialRelBindDTOS=new ArrayList<>();
        archiveSerialRelBindDTOS.add(archiveSerialRelBindDTO);

        ServiceData<List<ArchiveSerialRelBindDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(archiveSerialRelBindDTOS);
        Mockito.when(archiveSerialRelBindClient.selectSerialRelBindByPlanNumber(Mockito.anyString())).thenReturn(serviceData);
    }

    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusinessOrgIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(null);
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }


    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();

    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchiveSerialRelBindDTO> dataList = new ArrayList<>();
        ArchiveSerialRelBindDTO archiveSerialRelBindDTO=new ArchiveSerialRelBindDTO();
        archiveSerialRelBindDTO.setPlanNumber("ZL1604070704");
        archiveSerialRelBindDTO.setCreationDateStr("2023-01-01 00:00:00");
        archiveSerialRelBindDTO.setCreatedBy("6407003392");
        dataList.add(archiveSerialRelBindDTO);
        Page<ArchiveSerialRelBindDTO> page = new Page<>();
        page.setRows(dataList);
        ServiceData<Page<ArchiveSerialRelBindDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveSerialRelBindClient.selectSerialRelBindList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchiveSerialRelBindDTO> dataList = new ArrayList<>();
        ArchiveSerialRelBindDTO archiveSerialRelBindDTO=new ArchiveSerialRelBindDTO();
        archiveSerialRelBindDTO.setPlanNumber("ZL1604070704");
        archiveSerialRelBindDTO.setCreationDateStr("2023-01-01 00:00:00");
        archiveSerialRelBindDTO.setCreatedBy("6407003392");
        dataList.add(archiveSerialRelBindDTO);
        Page<ArchiveSerialRelBindDTO> page = new Page<>();
        page.setRows(dataList);
        ServiceData<Page<ArchiveSerialRelBindDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveSerialRelBindClient.selectSerialRelBindList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchiveSerialRelBindDTO> dataList = new ArrayList<>();

        Page<ArchiveSerialRelBindDTO> page = new Page<>();
        page.setRows(dataList);
        ServiceData<Page<ArchiveSerialRelBindDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveSerialRelBindClient.selectSerialRelBindList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }
}
