package com.zte.application.impl;

import com.zte.domain.model.ArchiveBomLockingBillDataRepository;
import com.zte.domain.model.ArchiveBoqBoxMakeDataRepository;
import com.zte.interfaces.dto.ArchiveBomLockingBillDTO;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeQueryDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
public class ArchiveBomLockingBillDataServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ArchiveBomLockingBillDataServiceImpl archiveBomLockingBillDataService;
    @Mock
    ArchiveBomLockingBillDataRepository repository;

    @Test
    public void getPageByDateRange(){
        Assert.assertNotNull(archiveBomLockingBillDataService.getPageByDateRange(null));
    }
    @Test
    public void getPageByDateRange1(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        query.setStartDate(new Date());
        query.setEndDate(new Date());
        Page<ArchiveBomLockingBillDTO> page = archiveBomLockingBillDataService.getPageByDateRange(query);
        Assert.assertTrue(page.getRows().size() >= 0);

    }
    @Test
    public void getByBillNo(){
        Assert.assertNull(archiveBomLockingBillDataService.getByBillNo(null));
    }

    @Test
    public void getByBillNo1(){
        Assert.assertNull(archiveBomLockingBillDataService.getByBillNo("123"));
    }
    @Test
    public void getBarcodeByBillId(){
        Assert.assertNotNull(archiveBomLockingBillDataService.getBarcodeByBillId(null));
    }

    @Test
    public void getBarcodeByBillId1(){
        Assert.assertNotNull(archiveBomLockingBillDataService.getBarcodeByBillId("123"));
    }
    @Test
    public void getPlanByBillId(){
        Assert.assertNotNull(archiveBomLockingBillDataService.getPlanByBillId(null));
    }

    @Test
    public void getPlanByBillId1(){
        Assert.assertNotNull(archiveBomLockingBillDataService.getPlanByBillId("123"));
    }

}
