package com.zte.application.impl;

import com.zte.domain.model.ArchiveMesEntryBillsRepository;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveMesEntryBillsQueryDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/14
 * @description :
 */
public class ArchiveMesEntryBillsDataServiceImplTest extends BaseTestCase {
    @InjectMocks
    ArchiveMesEntryBillsDataServiceImpl archiveMesEntryBillsDataService;
    @Mock
    ArchiveMesEntryBillsRepository repository;

    @Test
    public void getByEntryNumber(){
        Assert.assertNull(archiveMesEntryBillsDataService.getByEntryNumber(null));
    }
    @Test
    public void getByEntryNumber1(){
        Assert.assertNull(archiveMesEntryBillsDataService.getByEntryNumber("123"));
    }

    @Test
    public void getListByEntryNumber(){
        Assert.assertNotNull(archiveMesEntryBillsDataService.getListByEntryNumber(null));
    }

    @Test
    public void getListByEntryNumber1(){
        Assert.assertNotNull(archiveMesEntryBillsDataService.getListByEntryNumber("123"));
    }

    @Test
    public void getEntryBillsByDateRange(){
        Assert.assertNotNull(archiveMesEntryBillsDataService.getEntryBillsByDateRange(null));
    }

    @Test
    public void getEntryBillsByDateRange1(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setStartDate(new Date());
        query.setEndDate(new Date());
        query.setPage(1);
        query.setRows(200);
        Page<ArchiveMesEntryBillsDTO> page =  archiveMesEntryBillsDataService.getEntryBillsByDateRange(query);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void getDetailListByEntryBillId(){
        Mockito.when(repository.getDetailListByEntryBillId(Mockito.any())).thenReturn(new ArrayList<>());
        archiveMesEntryBillsDataService.getDetailListByEntryBillId("123");

        Assert.assertNotNull(archiveMesEntryBillsDataService.getDetailListByEntryBillId(""));
    }
}
