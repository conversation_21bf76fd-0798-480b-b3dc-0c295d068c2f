/*Started by AICoder, pid:va9f969906ff0ab147e1086c412bde5cdad7de70*/
package com.zte.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.zte.application.CustomerItemsService;
import com.zte.application.PushStdModelConfirmationService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.enums.PushStatusEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.ComponentInfoDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.interfaces.dto.MdsSspMainBoardDTO;
import com.zte.interfaces.dto.MdsSspSnMainBoardDTO;
import com.zte.interfaces.dto.MdsSspTaskDtailDTO;
import com.zte.interfaces.dto.MdsSspTaskInfoDTO;
import com.zte.interfaces.dto.MdsSspTaskItemDTO;
import com.zte.interfaces.dto.ProductSnReportDTO;
import com.zte.interfaces.dto.ProductSnReportItemDTO;
import com.zte.interfaces.dto.PushStdModelConfirmationDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.SspTaskInfoDTO;
import com.zte.interfaces.dto.SubMaterialInfoDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class ProductSnReportDataHandleServiceImpl_78_Test {

    @Mock
    private String manufactureFeedBackCurrProcess;

    @InjectMocks
    private ProductSnReportDataHandleServiceImpl service;

    @Mock
    private ProductSnReportDTO mockProductSnReportDTO;

    @Mock
    private ProductSnReportItemDTO mockProductSnReportItemDTO;

    @Mock
    private MdsRemoteService mdsRemoteService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private CustomerItemsService customerItemsService;

    @Mock
    private PushStdModelConfirmationService pushStdModelConfirmationService;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private IscpRemoteService iscpRemoteService;

    @Test
    public void testValidatePushMessageData_ManufactureOrderNoIsNull() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn(null);
        when(mockProductSnReportDTO.getProductSn()).thenReturn(null);

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_MaterialCategoryIsNull() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn("directiveNo");
        when(mockProductSnReportDTO.getProductSn()).thenReturn(mockProductSnReportItemDTO);
        when(mockProductSnReportItemDTO.getMaterialCategory()).thenReturn(null);
        when(mockProductSnReportItemDTO.getMaterialQuantity()).thenReturn(new BigDecimal(10));

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_ManufactureDirectiveNoIsNull() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn(null);
        when(mockProductSnReportDTO.getProductSn()).thenReturn(null);

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_MaterialQuantityIsNull() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn("directiveNo");
        when(mockProductSnReportDTO.getProductSn()).thenReturn(mockProductSnReportItemDTO);
        when(mockProductSnReportItemDTO.getMaterialCategory()).thenReturn("materialCategory");
        when(mockProductSnReportItemDTO.getMaterialQuantity()).thenReturn(null);

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_ValidData() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn("directiveNo");
        when(mockProductSnReportDTO.getProductSn()).thenReturn(mockProductSnReportItemDTO);
        when(mockProductSnReportItemDTO.getMaterialCategory()).thenReturn("materialCategory");
        when(mockProductSnReportItemDTO.getMaterialQuantity()).thenReturn(new BigDecimal(10));

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_NotInstanceOfManufactureFeedBackDTO() {
        String currProcess = "testProcess";
        Object pushMessageData = new Object();

        boolean result = service.validatePushMessageData(currProcess, pushMessageData);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_CategoryIsNull() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn(null);
        when(mockProductSnReportDTO.getProductSn()).thenReturn(null);

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_ProductSnNull() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn("directiveNo");
        when(mockProductSnReportDTO.getProductSn()).thenReturn(null);

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_ProductSnProductSnListNotEmpty() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn("directiveNo");
        when(mockProductSnReportDTO.getProductSn()).thenReturn(mockProductSnReportItemDTO);
        when(mockProductSnReportItemDTO.getMaterialCategory()).thenReturn("materialCategory");
        when(mockProductSnReportItemDTO.getMaterialQuantity()).thenReturn(new BigDecimal(10));
        when(mockProductSnReportItemDTO.getProductSnList()).thenReturn(Lists.newArrayList(mockProductSnReportItemDTO))
                .thenReturn(Lists.newArrayList());

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ProductSnProductSnListNotEmpty1() {
        String currProcess = "testProcess";
        when(mockProductSnReportDTO.getCategory()).thenReturn("category");
        when(mockProductSnReportDTO.getManufactureOrderNo()).thenReturn("orderNo");
        when(mockProductSnReportDTO.getManufactureDirectiveNo()).thenReturn("directiveNo");
        when(mockProductSnReportDTO.getProductSn()).thenReturn(mockProductSnReportItemDTO);
        when(mockProductSnReportItemDTO.getMaterialCategory()).thenReturn("materialCategory").thenReturn(null);
        when(mockProductSnReportItemDTO.getMaterialQuantity()).thenReturn(new BigDecimal(10));
        when(mockProductSnReportItemDTO.getProductSnList()).thenReturn(Lists.newArrayList(mockProductSnReportItemDTO))
                .thenReturn(Lists.newArrayList());

        boolean result = service.validatePushMessageData(currProcess, mockProductSnReportDTO);

        assertFalse(result);
    }

    /*Started by AICoder, pid:je49cbaa67xff531436d0a32b04fc94b0905e2ee*/
    @Test
    public void testGetPushStdModelSnDataMessageType() {
        // Given: No setup needed as the method is simple and does not depend on external state.

        // When: Call the method to be tested.
        String result = service.getPushStdModelSnDataMessageType();

        // Then: Verify the result.
        assertEquals(Constant.MESSAGE_TYPE_PRODUCT_SN_REPORT, result);
    }
    /*Ended by AICoder, pid:je49cbaa67xff531436d0a32b04fc94b0905e2ee*/


    /*Started by AICoder, pid:ed48ab5cf9x9bab14c5f0acbc0c7bb6ba542f160*/
    @Test
    public void testMatch_ReturnsTrueWhenCurrProcessMatches() {
        // Given
        ReflectionTestUtils.setField(service, "productSnReportCurrProcess", "testProcess");

        // When
        boolean result = service.match("testProcess");

        // Then
        assertTrue(result);
    }

    @Test
    public void testMatch_ReturnsFalseWhenCurrProcessDoesNotMatch() {
        // Given
        ReflectionTestUtils.setField(service, "productSnReportCurrProcess", "anotherProcess");

        // When
        boolean result = service.match("testProcess");

        // Then
        assertFalse(result);
    }
    /*Ended by AICoder, pid:ed48ab5cf9x9bab14c5f0acbc0c7bb6ba542f160*/

    @Test
    public void testWrapPushMessageData_WithMatchingSn0() throws JsonProcessingException {
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setSn("SN123");
        pushStdModelSnDataExt.setTaskBillNo("TaskBillNo");
        pushStdModelSnDataExt.setTaskNo("TaskNo");
        pushStdModelSnDataExt.setTaskCustomerPartType("CustomerPartType");
        pushStdModelSnDataExt.setTaskFixBomId("FixBomId");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> fixBomDetails = new ArrayList<>();

        MdsSspTaskInfoDTO mdsSspTaskInfo = new MdsSspTaskInfoDTO();
        MdsSspTaskDtailDTO sspTaskDetail = new MdsSspTaskDtailDTO();
        sspTaskDetail.setSn("SN123");
        sspTaskDetail.setAssetNum("AssetNum");
        MdsSspTaskItemDTO mainBoard = new MdsSspTaskItemDTO();
        mainBoard.setBmcMac("BMC_MAC");
        sspTaskDetail.setMainBoard(mainBoard);
        sspTaskDetail.setCpuList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        sspTaskDetail.setMemoryList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        sspTaskDetail.setNicList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        sspTaskDetail.setDiskList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        sspTaskDetail.setGpuList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        MdsSspTaskItemDTO mdsSspTaskItemDTO = new MdsSspTaskItemDTO();
        mdsSspTaskItemDTO.setItemCode("test11");
        MdsSspTaskItemDTO mdsSspTaskItemDTO1 = new MdsSspTaskItemDTO();
        mdsSspTaskItemDTO1.setItemCode("test11");
        sspTaskDetail.setRaidList(Lists.newArrayList(mdsSspTaskItemDTO, mdsSspTaskItemDTO1));
        sspTaskDetail.setOpticalList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        sspTaskDetail.setPsuList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        sspTaskDetail.setBpList(Lists.newArrayList(new MdsSspTaskItemDTO()));
        List<MdsSspTaskDtailDTO> sspList = new ArrayList<>();
        sspList.add(sspTaskDetail);
        mdsSspTaskInfo.setSspList(sspList);

        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setCustomerComponentType("ComponentType");
        fixBomDetail.setItemName("ItemName");
        fixBomDetail.setItemSupplierNo("SupplierNo");
        fixBomDetails.add(fixBomDetail);

        when(mdsRemoteService.getSspMainBoardBySn(anyString())).thenReturn(null);
        when(mdsRemoteService.querySspTaskInfoBySn(anyString())).thenReturn(mdsSspTaskInfo);

        ProductSnReportDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        assertNotNull(result);
        assertEquals("TaskBillNo", result.getManufactureDirectiveNo());
        assertEquals("TaskNo", result.getManufactureOrderNo());
        assertEquals("CustomerPartType", result.getCategory());
        assertNotNull(result.getProductSn());

        ProductSnReportItemDTO item = result.getProductSn();
        assertEquals("SN123", item.getSnNo());
        assertEquals("ComponentType", item.getMaterialCategory());
        assertEquals("SupplierNo", item.getMaterialName());
        assertEquals("FixBomId", item.getMaterialBom());
        assertEquals(BigDecimal.ONE, item.getMaterialQuantity());
        assertEquals("", item.getOobMac());
        assertEquals("", item.getTdId());
    }

    @Test
    public void getManufactureFeedBackItem() throws Exception {
        String snNo = "777766600001";
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setNeedQty(1);
        ProductSnReportItemDTO parent = new ProductSnReportItemDTO();
        WipExtendIdentificationDTO wipExtendIdentification = null;
        Set<String> singleMpnItemNoSet = new HashSet<>();
        List<ProductSnReportItemDTO> result = Whitebox.invokeMethod(service, "getManufactureFeedBackItem", snNo, fixBomDetail, parent, wipExtendIdentification, singleMpnItemNoSet);

        wipExtendIdentification = new WipExtendIdentificationDTO();
        result = Whitebox.invokeMethod(service, "getManufactureFeedBackItem", snNo, fixBomDetail, parent, wipExtendIdentification, singleMpnItemNoSet);

        wipExtendIdentification.setFormQty(BigDecimal.TEN);
        result = Whitebox.invokeMethod(service, "getManufactureFeedBackItem", snNo, fixBomDetail, parent, wipExtendIdentification, singleMpnItemNoSet);
        Assert.assertTrue(result.get(0).getSnNo() != null);
    }

    /* Started by AICoder, pid:pab303b36dg35e314d6d0944900ea5978eb1ccaf */
    @Test
    public void getFullItemNoSnMap() throws Exception {
        Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap = new HashMap<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        Map<String, List<String>> result = Whitebox.invokeMethod(service, "getFullItemNoSnMap", itemNoSnMap, singleMpnItemNoSet);
        Assert.assertTrue(result.isEmpty());

        WipExtendIdentificationDTO wipExt1 = new WipExtendIdentificationDTO();
        wipExt1.setSn("SN1");
        WipExtendIdentificationDTO wipExt2 = new WipExtendIdentificationDTO();
        wipExt2.setSn("SN2");
        WipExtendIdentificationDTO wipExt3 = new WipExtendIdentificationDTO();
        wipExt3.setSn("SN3");
        WipExtendIdentificationDTO wipExt4 = new WipExtendIdentificationDTO();
        wipExt4.setSn("SN4");
        itemNoSnMap.put("itemNo1", new ArrayList<>(Arrays.asList(wipExt1, wipExt2)));
        itemNoSnMap.put("itemNo3", new ArrayList<>(Arrays.asList(wipExt3, wipExt4)));
        List<SysLookupValues> lookupValues = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(Constant.LOOK_UP_CODE_1004115))).thenReturn(lookupValues);
        List<CustomerItemsDTO> customerItemsList = new ArrayList<>();
        PowerMockito.when(customerItemsService.getSameItemOfZteCode(Mockito.anyList(), Mockito.anyList())).thenReturn(customerItemsList);
        result = Whitebox.invokeMethod(service, "getFullItemNoSnMap", itemNoSnMap, singleMpnItemNoSet);
        Assert.assertTrue(result.isEmpty());

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo1");
        customerItemsDTO.setCustomerCode("MPN");
        customerItemsDTO.setOriginalCustomerCode("MPN");
        customerItemsList.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setZteCode("itemNo2");
        customerItemsDTO2.setCustomerCode("MPN");
        customerItemsDTO2.setOriginalCustomerCode("MPN");
        customerItemsList.add(customerItemsDTO2);
        result = Whitebox.invokeMethod(service, "getFullItemNoSnMap", itemNoSnMap, singleMpnItemNoSet);
        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void getNodeSlotCustCompTypeAndSnListMap() throws Exception {
        Map<String, String> columeToCustCompType = new HashMap<>();
        columeToCustCompType.put("ssdList","server.ssd");
        columeToCustCompType.put("nicList", "server.nic");
        columeToCustCompType.put("memoryList", "server.mem");
        ReflectionTestUtils.setField(service,"columeToCustCompType", columeToCustCompType);
        Map<String, List<String>> result = Whitebox.invokeMethod(service, "getNodeSlotCustCompTypeAndSnListMap", "219000000000");
        Assert.assertTrue(result.isEmpty());

        ReflectionTestUtils.setField(service,"callMdsForBindInfo", true);
        List<SspTaskInfoDTO> sspTaskInfoList = new ArrayList<>();
        PowerMockito.when(mdsRemoteService.querySspTaskInfoForAli(Mockito.anyList())).thenReturn(sspTaskInfoList);
        result = Whitebox.invokeMethod(service, "getNodeSlotCustCompTypeAndSnListMap", "219000000000");
        Assert.assertTrue(result.isEmpty());

        SspTaskInfoDTO sspTaskInfoDTO = new SspTaskInfoDTO();
        sspTaskInfoDTO.setNodeNumber("CN1");
        sspTaskInfoDTO.setNodeSlot(1);
        sspTaskInfoDTO.setPartcodeType(Constant.PARTCODE_TYPE_ZB);
        SubMaterialInfoDTO subMaterialInfoDTO = new SubMaterialInfoDTO();
        ComponentInfoDTO componentInfoDTO = new ComponentInfoDTO();
        componentInfoDTO.setItemcode("0060401B0071");
        componentInfoDTO.setSn("7777666000001");
        subMaterialInfoDTO.setMemoryList(Arrays.asList(componentInfoDTO));
        sspTaskInfoDTO.setSubMaterialInfo(subMaterialInfoDTO);
        sspTaskInfoList.add(sspTaskInfoDTO);

        sspTaskInfoDTO = new SspTaskInfoDTO();
        sspTaskInfoDTO.setPartcodeType("TEST");
        sspTaskInfoList.add(sspTaskInfoDTO);
        result = Whitebox.invokeMethod(service, "getNodeSlotCustCompTypeAndSnListMap", "219000000000");
        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void wrapChildNodes() throws Exception {
        ReflectionTestUtils.setField(service,"specificItemTypeList", Arrays.asList("成品料"));
        FixBomDetailDTO parentFixBom = new FixBomDetailDTO();
        List<FixBomDetailDTO> childNodes = new ArrayList<>();
        parentFixBom.setChildNodes(childNodes);
        parentFixBom.setItemType("成品料");
        Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap = new HashMap<>();
        ProductSnReportItemDTO parent = new ProductSnReportItemDTO();
        parent.setSnNo("219518413575");
        parent.setProductSnList(Lists.newArrayList());
        List<String> snList1 = new ArrayList<>();
        snList1.add("7122222222");
        Map<String, WipExtendIdentificationDTO> snWipExtendIdentificationMap = new HashMap<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        Whitebox.invokeMethod(service, "wrapChildNodes", parentFixBom, itemNoSnMap, parent, snWipExtendIdentificationMap, singleMpnItemNoSet);

        FixBomDetailDTO childNode = new FixBomDetailDTO();
        childNode.setItemType("成品料");
        childNode.setCustomerComponentType("server.configmodel");
        childNode.setItemSeq("1-2");
        childNode.setItemNumber("2");
        FixBomDetailDTO grandSonNode = new FixBomDetailDTO();
        grandSonNode.setItemType("厂商部件");
        grandSonNode.setCustomerComponentType("server.mem");
        grandSonNode.setItemSeq("1-2-4-1-1");
        grandSonNode.setZteCode("itemNo2");
        grandSonNode.setItemNumber("1");
        FixBomDetailDTO grandSonNode2 = new FixBomDetailDTO();
        grandSonNode2.setItemType("厂商部件");
        grandSonNode2.setCustomerComponentType("server.mem");
        grandSonNode2.setItemSeq("1-2-4-1-2");
        grandSonNode2.setZteCode("itemNo3");
        grandSonNode2.setItemNumber("1");
        childNode.setChildNodes(Arrays.asList(grandSonNode, grandSonNode2));
        childNodes.add(childNode);
        Whitebox.invokeMethod(service, "wrapChildNodes", parentFixBom, itemNoSnMap, parent, snWipExtendIdentificationMap, singleMpnItemNoSet);
        List<WipExtendIdentificationDTO> wipExtList = new ArrayList<>();
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn("7122222222");
        wipExt.setVirtualSn("219518413575-01");
        wipExt.setItemNo("111");
        wipExtList.add(wipExt);
        wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn("7122222223");
        wipExt.setVirtualSn("219518413575-02");
        wipExtList.add(wipExt);
        itemNoSnMap.put("itemNo2", wipExtList);
        List<WipExtendIdentificationDTO> wipExtList1 = new ArrayList<>();
        WipExtendIdentificationDTO wipExt1 = new WipExtendIdentificationDTO();
        wipExt1.setSn("7122222224");
        wipExt1.setVirtualSn("219518413575-01");
        wipExtList1.add(wipExt1);
        wipExt1 = new WipExtendIdentificationDTO();
        wipExt1.setSn("7122222225");
        wipExt1.setVirtualSn("219518413575-02");
        wipExtList1.add(wipExt1);
        itemNoSnMap.put("itemNo3", wipExtList1);
        singleMpnItemNoSet.add("111");
        Whitebox.invokeMethod(service, "wrapChildNodes", parentFixBom, itemNoSnMap, parent, snWipExtendIdentificationMap, singleMpnItemNoSet);

        ReflectionTestUtils.setField(service,"forceFixbomMpn", true);
        Whitebox.invokeMethod(service, "wrapChildNodes", parentFixBom, itemNoSnMap, parent, snWipExtendIdentificationMap, singleMpnItemNoSet);
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:pab303b36dg35e314d6d0944900ea5978eb1ccaf */

    @Test
    public void setManufactureDirectiveNo() throws Exception {
        ProductSnReportDTO manufactureFeedBack = new ProductSnReportDTO();
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("taskNo1");
        Whitebox.invokeMethod(service, "setManufactureDirectiveNo", manufactureFeedBack, pushStdModelSnDataExt);

        pushStdModelSnDataExt.setBusinessScene(BusinessSceneEnum.BUFFER.getCode());
        Whitebox.invokeMethod(service, "setManufactureDirectiveNo", manufactureFeedBack, pushStdModelSnDataExt);

        PushStdModelConfirmationDTO pushStdModelConfirmationDTO = new PushStdModelConfirmationDTO();
        PowerMockito.when(pushStdModelConfirmationService.getByTaskNo(Mockito.anyString())).thenReturn(pushStdModelConfirmationDTO);
        Whitebox.invokeMethod(service, "setManufactureDirectiveNo", manufactureFeedBack, pushStdModelSnDataExt);

        pushStdModelConfirmationDTO.setPushStatus(PushStatusEnum.PUSHED_AND_CALLBACK.getCode());
        Whitebox.invokeMethod(service, "setManufactureDirectiveNo", manufactureFeedBack, pushStdModelSnDataExt);
        Assert.assertTrue(true);
    }

    /*Started by AICoder, pid:p90ce2bbe9re77b14c4f082e61d909139b17cea9*/
    @Test
    public void testWrapPushMessageData_WithMatchingSn() throws JsonProcessingException {
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setSn("SN123");
        pushStdModelSnDataExt.setTaskBillNo("TaskBillNo");
        pushStdModelSnDataExt.setTaskNo("TaskNo");
        pushStdModelSnDataExt.setTaskCustomerPartType("CustomerPartType");
        pushStdModelSnDataExt.setTaskFixBomId("FixBomId");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> fixBomDetails = new ArrayList<>();

        MdsSspSnMainBoardDTO mdsSspSnMainBoard = new MdsSspSnMainBoardDTO();
        mdsSspSnMainBoard.setSn("SN123");
        mdsSspSnMainBoard.setAssetNum("AssetNum");
        mdsSspSnMainBoard.setMainBoard(Lists.newArrayList(new MdsSspMainBoardDTO() {{ setBmcMac("BMC_MAC");}}));

        MdsSspTaskInfoDTO mdsSspTaskInfo = new MdsSspTaskInfoDTO();
        MdsSspTaskDtailDTO sspTaskDetail = new MdsSspTaskDtailDTO();
        sspTaskDetail.setSn("SN123");
        sspTaskDetail.setAssetNum("AssetNum");
        MdsSspTaskItemDTO mainBoard = new MdsSspTaskItemDTO();
        mainBoard.setBmcMac("BMC_MAC");
        sspTaskDetail.setMainBoard(mainBoard);
        List<MdsSspTaskDtailDTO> sspList = new ArrayList<>();
        sspList.add(sspTaskDetail);
        mdsSspTaskInfo.setSspList(sspList);

        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setCustomerComponentType("ComponentType");
        fixBomDetail.setItemName("ItemName");
        fixBomDetail.setItemSupplierNo("SupplierNo");
        fixBomDetail.setZteCode("ZTE_Code");
        fixBomDetail.setChildNodes(Lists.newArrayList(mock(FixBomDetailDTO.class)));
        fixBomDetails.add(fixBomDetail);

        when(mdsRemoteService.querySspTaskInfoBySn(anyString())).thenReturn(mdsSspTaskInfo);
        when(mdsRemoteService.getSspMainBoardBySn(anyString())).thenReturn(mdsSspSnMainBoard);

        ProductSnReportDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        assertNotNull(result);
        assertEquals("TaskBillNo", result.getManufactureDirectiveNo());
        assertEquals("TaskNo", result.getManufactureOrderNo());
        assertEquals("CustomerPartType", result.getCategory());
        assertNotNull(result.getProductSn());

        ProductSnReportItemDTO item = result.getProductSn();
        assertEquals("SN123", item.getSnNo());
        assertEquals("ComponentType", item.getMaterialCategory());
        assertEquals("SupplierNo", item.getMaterialName());
        assertEquals("FixBomId", item.getMaterialBom());
        assertEquals(BigDecimal.ONE, item.getMaterialQuantity());
        assertEquals("ZTE_Code", item.getFactoryMpn());
        assertEquals("BMC_MAC", item.getOobMac());
        assertEquals("AssetNum", item.getTdId());
    }

    @Test
    public void testWrapPushMessageData_NoMatchingSn() throws JsonProcessingException {
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setSn("SN123");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> fixBomDetails = Lists.newArrayList();

        MdsSspTaskInfoDTO mdsSspTaskInfo = new MdsSspTaskInfoDTO();
        mdsSspTaskInfo.setSspList(new ArrayList<>());

        MdsSspSnMainBoardDTO mdsSspSnMainBoard = new MdsSspSnMainBoardDTO();
        mdsSspSnMainBoard.setSn("SN123");
        mdsSspSnMainBoard.setAssetNum("AssetNum");
        mdsSspSnMainBoard.setMainBoard(Lists.newArrayList(new MdsSspMainBoardDTO() {{ setBmcMac("BMC_MAC");}}));
        when(mdsRemoteService.getSspMainBoardBySn(anyString())).thenReturn(mdsSspSnMainBoard);
        when(mdsRemoteService.querySspTaskInfoBySn(anyString())).thenReturn(mdsSspTaskInfo);

        ProductSnReportDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);
        assertNull(result);
    }

    @Before
    public void setUp() {
    }
    /*Ended by AICoder, pid:p90ce2bbe9re77b14c4f082e61d909139b17cea9*/

    @Test
    public void testsetCustomerItemStyle() throws Exception {
        // 测试空列表情况
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 测试所有条码都在singleMpnItemNoSet中的情况
        WipExtendIdentificationDTO wipExt1 = new WipExtendIdentificationDTO();
        wipExt1.setSn("SN001");
        wipExt1.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt1);
        singleMpnItemNoSet.add("ITEM001");
        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 测试正常情况 - 有多个MPN的物料
        singleMpnItemNoSet.clear();
        wipExtendIdentifications.clear();
        
        WipExtendIdentificationDTO wipExt2 = new WipExtendIdentificationDTO();
        wipExt2.setSn("SN002");
        wipExt2.setItemNo("ITEM002");
        wipExtendIdentifications.add(wipExt2);
        
        WipExtendIdentificationDTO wipExt3 = new WipExtendIdentificationDTO();
        wipExt3.setSn("SN003");
        wipExt3.setItemNo("ITEM003");
        wipExtendIdentifications.add(wipExt3);

        // Mock条码中心服务
        List<BarcodeExpandVO> barcodeExpandList = new ArrayList<>();
        BarcodeExpandVO barcodeExpand1 = new BarcodeExpandVO();
        barcodeExpand1.setBarcode("SN002");
        barcodeExpand1.setSysLotCode("UUID001");
        barcodeExpandList.add(barcodeExpand1);
        
        BarcodeExpandVO barcodeExpand2 = new BarcodeExpandVO();
        barcodeExpand2.setBarcode("SN003");
        barcodeExpand2.setSysLotCode("UUID002");
        barcodeExpandList.add(barcodeExpand2);

        // Mock采购中心服务
        List<ItemSplitInfoDTO> itemSplitInfoList = new ArrayList<>();
        ItemSplitInfoDTO itemSplit1 = new ItemSplitInfoDTO();
        itemSplit1.setItemUuid("UUID001");
        itemSplit1.setCustomerItemStyle("BRAND_STYLE_001");
        itemSplitInfoList.add(itemSplit1);
        
        ItemSplitInfoDTO itemSplit2 = new ItemSplitInfoDTO();
        itemSplit2.setItemUuid("UUID002");
        itemSplit2.setCustomerItemStyle("BRAND_STYLE_002");
        itemSplitInfoList.add(itemSplit2);

        // 使用PowerMockito来mock服务调用
        PowerMockito.when(barcodeCenterRemoteService.barcodeQueryBatch(Mockito.anyList())).thenReturn(barcodeExpandList);
        PowerMockito.when(iscpRemoteService.getItemSplitInfoByUuid(Mockito.anyList())).thenReturn(itemSplitInfoList);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果
        Assert.assertEquals("BRAND_STYLE_001", wipExt2.getCustomerItemStyle());
        Assert.assertEquals("BRAND_STYLE_002", wipExt3.getCustomerItemStyle());
    }

    @Test
    public void testsetCustomerItemStyle_WithEmptyBarcodeExpandList() throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn("SN001");
        wipExt.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt);

        // Mock条码中心返回空列表
        List<BarcodeExpandVO> barcodeExpandList = new ArrayList<>();
        PowerMockito.when(barcodeCenterRemoteService.barcodeQueryBatch(Mockito.anyList())).thenReturn(barcodeExpandList);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果 - 应该没有设置规格型号
        Assert.assertNull(wipExt.getCustomerItemStyle());
    }

    @Test
    public void testsetCustomerItemStyle_WithEmptyItemSplitInfoList() throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn("SN001");
        wipExt.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt);

        // Mock条码中心服务
        List<BarcodeExpandVO> barcodeExpandList = new ArrayList<>();
        BarcodeExpandVO barcodeExpand = new BarcodeExpandVO();
        barcodeExpand.setBarcode("SN001");
        barcodeExpand.setSysLotCode("UUID001");
        barcodeExpandList.add(barcodeExpand);

        // Mock采购中心返回空列表
        List<ItemSplitInfoDTO> itemSplitInfoList = new ArrayList<>();
        PowerMockito.when(barcodeCenterRemoteService.barcodeQueryBatch(Mockito.anyList())).thenReturn(barcodeExpandList);
        PowerMockito.when(iscpRemoteService.getItemSplitInfoByUuid(Mockito.anyList())).thenReturn(itemSplitInfoList);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果 - 应该没有设置规格型号
        Assert.assertNull(wipExt.getCustomerItemStyle());
    }

    @Test
    public void testsetCustomerItemStyle_WithMissingUuidMapping() throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn("SN001");
        wipExt.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt);

        // Mock条码中心服务
        List<BarcodeExpandVO> barcodeExpandList = new ArrayList<>();
        BarcodeExpandVO barcodeExpand = new BarcodeExpandVO();
        barcodeExpand.setBarcode("SN001");
        barcodeExpand.setSysLotCode("UUID001");
        barcodeExpandList.add(barcodeExpand);

        // Mock采购中心服务 - 返回不同的UUID
        List<ItemSplitInfoDTO> itemSplitInfoList = new ArrayList<>();
        ItemSplitInfoDTO itemSplit = new ItemSplitInfoDTO();
        itemSplit.setItemUuid("UUID002"); // 不同的UUID
        itemSplit.setBrandStyle("BRAND_STYLE_001");
        itemSplitInfoList.add(itemSplit);

        PowerMockito.when(barcodeCenterRemoteService.barcodeQueryBatch(Mockito.anyList())).thenReturn(barcodeExpandList);
        PowerMockito.when(iscpRemoteService.getItemSplitInfoByUuid(Mockito.anyList())).thenReturn(itemSplitInfoList);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果 - 应该没有设置规格型号，因为UUID不匹配
        Assert.assertNull(wipExt.getCustomerItemStyle());
    }

    @Test
    public void testsetCustomerItemStyle_WithBlankSn() throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn(""); // 空条码
        wipExt.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果 - 空条码应该被过滤掉，不会调用远程服务
        Assert.assertNull(wipExt.getCustomerItemStyle());
    }

    @Test
    public void testsetCustomerItemStyle_WithNullSn() throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn(null); // null条码
        wipExt.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果 - null条码应该被过滤掉，不会调用远程服务
        Assert.assertNull(wipExt.getCustomerItemStyle());
    }

    @Test
    public void testsetCustomerItemStyle_WithBlankBrandStyle() throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        Set<String> singleMpnItemNoSet = new HashSet<>();
        
        WipExtendIdentificationDTO wipExt = new WipExtendIdentificationDTO();
        wipExt.setSn("SN001");
        wipExt.setItemNo("ITEM001");
        wipExtendIdentifications.add(wipExt);

        // Mock条码中心服务
        List<BarcodeExpandVO> barcodeExpandList = new ArrayList<>();
        BarcodeExpandVO barcodeExpand = new BarcodeExpandVO();
        barcodeExpand.setBarcode("SN001");
        barcodeExpand.setSysLotCode("UUID001");
        barcodeExpandList.add(barcodeExpand);

        // Mock采购中心服务 - 返回空的品牌样式
        List<ItemSplitInfoDTO> itemSplitInfoList = new ArrayList<>();
        ItemSplitInfoDTO itemSplit = new ItemSplitInfoDTO();
        itemSplit.setItemUuid("UUID001");
        itemSplit.setBrandStyle(""); // 空的品牌样式
        itemSplitInfoList.add(itemSplit);

        PowerMockito.when(barcodeCenterRemoteService.barcodeQueryBatch(Mockito.anyList())).thenReturn(barcodeExpandList);
        PowerMockito.when(iscpRemoteService.getItemSplitInfoByUuid(Mockito.anyList())).thenReturn(itemSplitInfoList);

        Whitebox.invokeMethod(service, "setCustomerItemStyle", wipExtendIdentifications, singleMpnItemNoSet);

        // 验证结果 - 空的品牌样式应该被过滤掉
        Assert.assertNull(wipExt.getCustomerItemStyle());
    }

}
/*Ended by AICoder, pid:va9f969906ff0ab147e1086c412bde5cdad7de70*/