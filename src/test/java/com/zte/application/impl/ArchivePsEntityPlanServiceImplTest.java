package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchivePsEntityPlanDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/6/29
 * @description :
 */
public class ArchivePsEntityPlanServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ArchivePsEntityPlanServiceImpl archivePsEntityPlanService;

    @Mock
    ArchivePsEntityPlanDataService psEntityPlanService;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Mock
    PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    PubHrvOrgService pubHrvOrgService;

    @Test
    public void archive(){
        try {
            archivePsEntityPlanService.archive(null);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void archive1(){
        try {
            ArchiveTaskSend taskSend = new ArchiveTaskSend();
            taskSend.setBillNo("123");
            archivePsEntityPlanService.archive(taskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void archive2(){
        try {
            getArchivePsEntityPlanDto();
            ArchiveTaskSend taskSend = new ArchiveTaskSend();
            taskSend.setBillNo("123");
            archivePsEntityPlanService.archive(taskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void archive3(){
        try {
            getArchivePsEntityPlanDto();
            getArchivePsEntityPlanDetailDto();
            ArchiveTaskSend taskSend = new ArchiveTaskSend();
            taskSend.setBillNo("123");
            archivePsEntityPlanService.archive(taskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void archive4(){
        try {
            getArchivePsEntityPlanDto();
            getArchivePsEntityPlanDetailDto();
            getAttachmentUploadVo();
            ArchiveTaskSend taskSend = new ArchiveTaskSend();
            taskSend.setBillNo("123");
            archivePsEntityPlanService.archive(taskSend);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getArchiveDataList(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(400);
        query.setPage(1);

        ArchivePsEntityPlanDTO dto = new ArchivePsEntityPlanDTO();

        Page<ArchivePsEntityPlanDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(psEntityPlanService.getByDateRange(any()))
                .thenReturn(page);
        Assert.assertNotNull(archivePsEntityPlanService.getArchiveDataList(query));
    }


    public void getAttachmentUploadVo() throws Exception {
        AttachmentUploadVo uploadVo = new AttachmentUploadVo();
        uploadVo.setFileSize(1L);
        uploadVo.setFileName("123.xls");
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(uploadVo);

    }

    private void getArchivePsEntityPlanDetailDto(){
        ArchivePsEntityPlanDetailDTO dto = new ArchivePsEntityPlanDetailDTO();
        PowerMockito.when(psEntityPlanService.getDetailByEntityPlan(any())).thenReturn(Collections.singletonList(dto));
    }
    private void getArchivePsEntityPlanDto() {
        ArchivePsEntityPlanDTO dto = new ArchivePsEntityPlanDTO();
        PowerMockito.when(psEntityPlanService.getByPlanNumber(any())).thenReturn(dto);
        PubHrvOrg pubHrvOrg = new PubHrvOrg();
        PowerMockito.when(pubHrvOrgService.getPubHrvOrgByUserNameId(any())).thenReturn(pubHrvOrg);
    }
}
