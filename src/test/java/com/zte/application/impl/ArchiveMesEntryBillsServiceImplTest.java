package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveMesEntryBillsDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDTO;
import com.zte.interfaces.dto.ArchiveMesEntryBillsDetailDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/14
 * @description :
 */
public class ArchiveMesEntryBillsServiceImplTest extends BaseTestCase {
    @InjectMocks
    ArchiveMesEntryBillsServiceImpl archiveMesEntryBillsService;
    @Mock
    ArchiveMesEntryBillsDataService archiveMesEntryBillsDataService;

    @Mock
    PubHrvOrgService pubHrvOrgService;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Test
    public void archive(){
        try {
            archiveMesEntryBillsService.archive(null);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals("ArchiveMesEntryBillsServiceImpl archive error", e.getMessage());
        }
    }
    @Test
    public void archive1(){
        try {
            archiveMesEntryBillsService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals("ArchiveMesEntryBillsServiceImpl archive error", e.getMessage());
        }
    }


    @Test
    public void archive2(){
        getByEntryNumber();
        getDetailListByEntryBillId();
        try {
            archiveMesEntryBillsService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals("ArchiveMesEntryBillsServiceImpl archive error", e.getMessage());
        }
    }

    private void getDetailListByEntryBillId() {
        ArchiveMesEntryBillsDetailDTO dto = new ArchiveMesEntryBillsDetailDTO();
        ArchiveMesEntryBillsDetailDTO dto1 = new ArchiveMesEntryBillsDetailDTO();
        dto1.setBoxType("否");
        List<ArchiveMesEntryBillsDetailDTO> dtos = new ArrayList<>();
        dtos.add(dto);
        dtos.add(dto1);
        Mockito.when(archiveMesEntryBillsDataService.getDetailListByEntryBillId(any()))
                .thenReturn(dtos);
    }

    private void getByEntryNumber() {
        ArchiveMesEntryBillsDTO dto = new ArchiveMesEntryBillsDTO();
        Mockito.when(archiveMesEntryBillsDataService.getByEntryNumber(any()))
                .thenReturn(dto);
    }

    private ArchiveTaskSend getArchiveTaskSend() {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setBillNo("123");
        return archiveTaskSend;
    }

    @Test
    public void getArchiveDataList(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(400);
        query.setPage(1);
        ArchiveMesEntryBillsDTO dto = new ArchiveMesEntryBillsDTO();

        Page<ArchiveMesEntryBillsDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(archiveMesEntryBillsDataService.getEntryBillsByDateRange(any()))
                .thenReturn(page);
        Assert.assertNotNull(archiveMesEntryBillsService.getArchiveDataList(query));
    }
}
