package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveManufacturingOrderClient;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

/**
 * <p>
 *  制造通知单归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public class ArchiveManufacturingOrderServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveManufacturingOrderServiceImpl archiveManufacturingOrderService;

    @Mock
    private ArchiveManufacturingOrderClient archiveManufacturingOrderClient;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Test
    public void getFilePath() throws Exception {
        Assert.assertNotNull(archiveManufacturingOrderService.getFilePath(new HashMap<>()));
    }


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");

        selectManufacturingOrderById();
        downloadFromFtpAndUpload();
        buildBusiness();
        Assert.assertNotNull(archiveManufacturingOrderService.archive(archiveTaskSend));
    }


    @Test
    public void archiveNull() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");

        selectManufacturingOrderById();
        downloadFromFtpAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(archiveManufacturingOrderService.archive(archiveTaskSend));
    }

    @Test(expected = MesBusinessException.class)
    public void archiveServerError() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");

        selectManufacturingOrderByIdServerError();
        downloadFromFtpAndUpload();
        buildBusiness();
        Assert.assertNotNull(archiveManufacturingOrderService.archive(archiveTaskSend));
    }


    private void selectManufacturingOrderById(){
        ServiceData<Map<String,Object>> serviceData = new ServiceData<>();
        Map<String,Object> result = new HashMap<>();
        result.put("PRODUCT_SUB_CLASS","abcd");
        result.put("COUNTRY_NAME","中国");
        result.put("ITEM_NO","123456");
        result.put("MO_NO","P123");
        result.put("CREATE_BY","test123456");
        result.put("CREATION_DATE_STR","2015-01-08 00:00:00");
        result.put("FILE_PATH","ftp://10.18.178.154/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(result);
        Mockito.when(archiveManufacturingOrderClient.selectManufacturingOrderById(Mockito.any())).thenReturn(serviceData);
    }

    private void selectManufacturingOrderByIdServerError(){
        ServiceData<Map<String,Object>> serviceData = new ServiceData<>();
        Map<String,Object> result = new HashMap<>();
        result.put("PRODUCT_SUB_CLASS","abcd");
        result.put("COUNTRY_NAME","中国");
        result.put("ITEM_NO","123456");
        result.put("MO_NO","P123");
        result.put("CREATE_BY","test123456");
        result.put("CREATION_DATE_STR","2015-01-08 00:00:00");
        result.put("FILE_PATH","ftp://10.18.178.154/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(result);
        Mockito.when(archiveManufacturingOrderClient.selectManufacturingOrderById(Mockito.any())).thenReturn(serviceData);
    }

    private void downloadFromFtpAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.downloadFromFtpAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.anyString())).thenReturn(attachmentUploadVo);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("制造工程部/供应链中心/终端事业部");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }


    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test(expected = MesBusinessException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        Map<String,Object> result = new HashMap<>();
        result.put("PRODUCT_SUB_CLASS","abcd");
        result.put("COUNTRY_NAME","中国");
        result.put("ITEM_NO","123456");
        result.put("MO_NO","P123");
        result.put("CREATE_BY","test123456");
        result.put("CREATION_DATE_STR","2015-01-08 00:00:00");
        result.put("FILE_PATH","ftp://10.18.178.154/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");

        List<Map<String,Object>> dataList = new ArrayList<>();
        dataList.add(result);
        Page<Map<String,Object>> page = new Page<>();
        page.setRows(dataList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<Map<String,Object>>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveManufacturingOrderClient.selectManufacturingOrderList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveManufacturingOrderService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        Map<String,Object> result = new HashMap<>();
        result.put("PRODUCT_SUB_CLASS","abcd");
        result.put("COUNTRY_NAME","中国");
        result.put("ITEM_NO","123456");
        result.put("MO_NO","P123");
        result.put("CREATE_BY","test123456");
        result.put("CREATION_DATE_STR","2015-01-08 00:00:00");
        result.put("FILE_PATH","ftp://10.18.178.154/mes_upload/ManuNotice/MF910/126682301096/manuNoticeFile/MF910_MF910_南非MTN_白色_整机代码126682301096_机头代码126682311065 制造通知单 (A)版本.xlsx");

        List<Map<String,Object>> dataList = new ArrayList<>();
        dataList.add(result);
        Page<Map<String,Object>> page = new Page<>();
        page.setRows(dataList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<Map<String,Object>>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveManufacturingOrderClient.selectManufacturingOrderList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveManufacturingOrderService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);

        List<Map<String,Object>> dataList = new ArrayList<>();
        Page<Map<String,Object>> page = new Page<>();
        page.setRows(dataList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<Map<String,Object>>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveManufacturingOrderClient.selectManufacturingOrderList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveManufacturingOrderService.getArchiveDataList(archiveQueryParamDTO);
    }


}
