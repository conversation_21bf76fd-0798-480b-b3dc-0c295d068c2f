package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveProductWarehouseCheckClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  成品入库单据归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public class ArchiveProductWarehouseCheckServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveProductWarehouseCheckServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveProductWarehouseCheckClient archiveProductWarehouseCheckClient;


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("R3U120201028653");
        selectProductWarehouseByFinishNo();
        selectProductWarehouseBoxDetailByFinishNo();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test(expected = Exception.class)
    public void archiveNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("R3U120201028653");
        selectProductWarehouseByFinishNoNull();
        selectProductWarehouseBoxDetailByFinishNoNull();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test(expected = RouteException.class)
    public void archiveServerError() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("R3U120201028653");
        selectProductWarehouseByFinishNoError();
        selectProductWarehouseBoxDetailByFinishNoNull();
        createExcelAndUpload();
        buildBusiness();
        service.archive(taskSendArchive);
    }

    @Test
    public void archiveServerError2() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("R3U120201028653");
        selectProductWarehouseByFinishNo();
        selectProductWarehouseBoxDetailByFinishNoError();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    @Test
    public void archiveBusinessOrgAndDateIsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("7");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillNo("R3U120201028653");
        selectProductWarehouseByFinishNo();
        selectProductWarehouseBoxDetailByFinishNo();
        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(service.archive(taskSendArchive));
    }


    private void selectProductWarehouseByFinishNo(){
        ServiceData<List<ArchiveProductWarehouseCheckDTO>> serviceData=new ServiceData<>();
        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = new ArchiveProductWarehouseCheckDTO();
        archiveProductWarehouseCheckDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseCheckDTO.setApplierName("王春雨10070455");
        archiveProductWarehouseCheckDTO.setApplyDateStr("2022-01-01 00:00:00");
        archiveProductWarehouseCheckDTO.setConfirmDateStr("2023-01-01 00:00:00");
        List<ArchiveProductWarehouseCheckDTO> list=new ArrayList<>();
        list.add(archiveProductWarehouseCheckDTO);

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseByFinishNo(Mockito.anyString())).thenReturn(serviceData);
    }
    private void selectProductWarehouseByFinishNoNull(){
        ServiceData<List<ArchiveProductWarehouseCheckDTO>> serviceData=new ServiceData<>();
        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = new ArchiveProductWarehouseCheckDTO();
        archiveProductWarehouseCheckDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseCheckDTO.setApplierName("王春雨10070455");
        archiveProductWarehouseCheckDTO.setApplyDateStr("2022-01-01 00:00:00");
        archiveProductWarehouseCheckDTO.setConfirmDateStr("2023-01-01 00:00:00");

        List<ArchiveProductWarehouseCheckDTO> list=new ArrayList<>();

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseByFinishNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectProductWarehouseByFinishNoError(){
        ServiceData<List<ArchiveProductWarehouseCheckDTO>> serviceData=new ServiceData<>();
        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = new ArchiveProductWarehouseCheckDTO();
        archiveProductWarehouseCheckDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseCheckDTO.setApplierName("王春雨10070455");
        archiveProductWarehouseCheckDTO.setApplyDateStr("2022-01-01 00:00:00");
        archiveProductWarehouseCheckDTO.setConfirmDateStr("2023-01-01 00:00:00");

        List<ArchiveProductWarehouseCheckDTO> list=new ArrayList<>();

        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseByFinishNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectProductWarehouseBoxDetailByFinishNo(){
        ServiceData<List<ArchiveProductWarehouseBoxDetailDTO>> serviceData=new ServiceData<>();
        ArchiveProductWarehouseBoxDetailDTO archiveProductWarehouseBoxDetailDTO = new ArchiveProductWarehouseBoxDetailDTO();
        archiveProductWarehouseBoxDetailDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseBoxDetailDTO.setBoxNo("3200023U2010270403");
        archiveProductWarehouseBoxDetailDTO.setBoxContent("5");
        archiveProductWarehouseBoxDetailDTO.setMemo("test");
        List<ArchiveProductWarehouseBoxDetailDTO> list=new ArrayList<>();
        list.add(archiveProductWarehouseBoxDetailDTO);

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseBoxDetailByFinishNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectProductWarehouseBoxDetailByFinishNoNull(){
        ServiceData<List<ArchiveProductWarehouseBoxDetailDTO>> serviceData=new ServiceData<>();
        ArchiveProductWarehouseBoxDetailDTO archiveProductWarehouseBoxDetailDTO = new ArchiveProductWarehouseBoxDetailDTO();
        archiveProductWarehouseBoxDetailDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseBoxDetailDTO.setBoxNo("3200023U2010270403");
        archiveProductWarehouseBoxDetailDTO.setBoxContent("5");
        archiveProductWarehouseBoxDetailDTO.setMemo("test");
        List<ArchiveProductWarehouseBoxDetailDTO> list=new ArrayList<>();

        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseBoxDetailByFinishNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectProductWarehouseBoxDetailByFinishNoError(){
        ServiceData<List<ArchiveProductWarehouseBoxDetailDTO>> serviceData=new ServiceData<>();
        ArchiveProductWarehouseBoxDetailDTO archiveProductWarehouseBoxDetailDTO = new ArchiveProductWarehouseBoxDetailDTO();
        archiveProductWarehouseBoxDetailDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseBoxDetailDTO.setBoxNo("3200023U2010270403");
        archiveProductWarehouseBoxDetailDTO.setBoxContent("5");
        archiveProductWarehouseBoxDetailDTO.setMemo("test");
        List<ArchiveProductWarehouseBoxDetailDTO> list=new ArrayList<>();

        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(list);
        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseBoxDetailByFinishNo(Mockito.anyString())).thenReturn(serviceData);
    }



    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }


    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = new ArchiveProductWarehouseCheckDTO();
        archiveProductWarehouseCheckDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseCheckDTO.setApplierName("王春雨10070455");
        archiveProductWarehouseCheckDTO.setApplyDateStr("2022-01-01");
        List<ArchiveProductWarehouseCheckDTO> list=new ArrayList<>();
        list.add(archiveProductWarehouseCheckDTO);

        Page<ArchiveProductWarehouseCheckDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveProductWarehouseCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = new ArchiveProductWarehouseCheckDTO();
        archiveProductWarehouseCheckDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseCheckDTO.setApplierName("王春雨10070455");
        archiveProductWarehouseCheckDTO.setApplyDateStr("2022-01-01");
        List<ArchiveProductWarehouseCheckDTO> list=new ArrayList<>();
        list.add(archiveProductWarehouseCheckDTO);

        Page<ArchiveProductWarehouseCheckDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveProductWarehouseCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        ArchiveProductWarehouseCheckDTO archiveProductWarehouseCheckDTO = new ArchiveProductWarehouseCheckDTO();
        archiveProductWarehouseCheckDTO.setFinishNo("R3U120201028653");
        archiveProductWarehouseCheckDTO.setApplierName("王春雨10070455");
        archiveProductWarehouseCheckDTO.setApplyDateStr("2022-01-01");
        List<ArchiveProductWarehouseCheckDTO> list=new ArrayList<>();

        Page<ArchiveProductWarehouseCheckDTO> page = new Page<>();
        page.setRows(list);
        ServiceData<Page<ArchiveProductWarehouseCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveProductWarehouseCheckClient.selectProductWarehouseList(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }
}
