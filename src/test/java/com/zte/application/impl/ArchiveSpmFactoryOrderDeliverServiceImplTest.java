package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.ArchiveSpmFactoryOrderDeliverDataService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/28
 * @description :
 */
public class ArchiveSpmFactoryOrderDeliverServiceImplTest extends PowerBaseTestCase {

    @InjectMocks
    ArchiveSpmFactoryOrderDeliverServiceImpl service;

    @Mock
    ArchiveSpmFactoryOrderDeliverDataService archiveSpmFactoryOrderDeliverDataService;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    PubHrvOrgService pubHrvOrgService;

    @Test
    public void getArchiveDataList(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        query.setStartDate(new Date());
        query.setEndDate(new Date());

        ArchiveSpmFactoryOrderDeliverDTO dto = new ArchiveSpmFactoryOrderDeliverDTO();

        Page<ArchiveSpmFactoryOrderDeliverDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(archiveSpmFactoryOrderDeliverDataService.getByDateRange(any()))
                .thenReturn(page);
        Assert.assertNotNull(service.getArchiveDataList(query));
    }

    @Test
    public void archive(){
        try {
            service.archive(null);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void archive1(){
        try {
            service.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void archive2(){
        try {
            getArchiveSpmFactoryOrderDeliverDTO();
            service.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    private void getArchiveSpmFactoryOrderDeliverDTO() {
        ArchiveSpmFactoryOrderDeliverDTO dto = new ArchiveSpmFactoryOrderDeliverDTO();
        Mockito.when(archiveSpmFactoryOrderDeliverDataService.getByRecordId(any()))
                .thenReturn(dto);
    }

    private ArchiveTaskSend getArchiveTaskSend() {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setBillNo("123");
        return archiveTaskSend;
    }
}
