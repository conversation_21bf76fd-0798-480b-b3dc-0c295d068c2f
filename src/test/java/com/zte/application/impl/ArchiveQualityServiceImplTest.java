package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveQualityClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  IPQC确认单据归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
public class ArchiveQualityServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveQualityServiceImpl archiveQualityService;

    @Mock
    private ArchiveQualityClient archiveQualityClient;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;


    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("4");
        archiveTaskSend.setTaskType("4");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("SJS1912177644");

        selectQualityBySendChkNo();
        selectQualitySendCheckHeaderBySendChkNo();
        selectQualitySendCheckLineBySendChkNo();
        selectQualityCheckHeaderBySendChkNo();
        selectQualityCheckLineBySendChkNo();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(archiveQualityService.archive(archiveTaskSend));

    }

    @Test(expected = RouteException.class)
    public void archiveError() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("4");
        archiveTaskSend.setTaskType("4");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("SJS1912177644");

        selectQualityBySendChkNoError();
        selectQualitySendCheckHeaderBySendChkNoError();
        createExcelAndUpload();
        buildBusiness();
        archiveQualityService.archive(archiveTaskSend);
    }

    @Test(expected = RouteException.class)
    public void archiveError2() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("4");
        archiveTaskSend.setTaskType("4");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("SJS1912177644");

        selectQualityBySendChkNo();
        selectQualitySendCheckHeaderBySendChkNoError();
//        selectQualitySendCheckLineBySendChkNoError();
//        selectQualityCheckHeaderBySendChkNoError();
//        selectQualityCheckLineBySendChkNoError();
        createExcelAndUpload();
        buildBusiness();
        archiveQualityService.archive(archiveTaskSend);
    }

    @Test
    public void archiveError3() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("4");
        archiveTaskSend.setTaskType("4");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("SJS1912177644");

        selectQualityBySendChkNo();
        selectQualitySendCheckHeaderBySendChkNo();
        selectQualitySendCheckLineBySendChkNoError();
        selectQualityCheckHeaderBySendChkNoError();
        selectQualityCheckLineBySendChkNoError();
        createExcelAndUpload();
        buildBusiness();
        Assert.assertNotNull(archiveQualityService.archive(archiveTaskSend));
    }

    @Test
    public void archiveNull() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("4");
        archiveTaskSend.setTaskType("4");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("SJS1912177644");

        selectQualityBySendChkNoNull();
        selectQualitySendCheckHeaderBySendChkNo();
        selectQualitySendCheckLineBySendChkNoNull();
        selectQualityCheckHeaderBySendChkNoNull();
        selectQualityCheckLineBySendChkNoNull();
        createExcelAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(archiveQualityService.archive(archiveTaskSend));
    }



    private void selectQualityBySendChkNo(){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        ArchiveQualityCheckDTO archiveQualityCheckDTO=new ArchiveQualityCheckDTO();
        archiveQualityCheckDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualityCheckDTO> list=new ArrayList<>();
        list.add(archiveQualityCheckDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityBySendChkNoError(){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        ArchiveQualityCheckDTO archiveQualityCheckDTO=new ArchiveQualityCheckDTO();
        archiveQualityCheckDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualityCheckDTO> list=new ArrayList<>();
        list.add(archiveQualityCheckDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityBySendChkNoNull(){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        List<ArchiveQualityCheckDTO> list=new ArrayList<>();
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualitySendCheckHeaderBySendChkNo(){
        ServiceData<ArchiveQualityCheckDTO> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        ArchiveQualityCheckDTO archiveQualityCheckDTO=new ArchiveQualityCheckDTO();
        archiveQualityCheckDTO.setSendChkNo("SJS1912177644");
        archiveQualityCheckDTO.setCheckDateStr("2022-01-01 00:00:00");
        serviceData.setBo(archiveQualityCheckDTO);
        Mockito.when(archiveQualityClient.selectQualitySendCheckHeaderBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }


    private void selectQualitySendCheckHeaderBySendChkNoError(){
        ServiceData<ArchiveQualityCheckDTO> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        ArchiveQualityCheckDTO archiveQualityCheckDTO=new ArchiveQualityCheckDTO();
        archiveQualityCheckDTO.setSendChkNo("SJS1912177644");
        serviceData.setBo(archiveQualityCheckDTO);
        Mockito.when(archiveQualityClient.selectQualitySendCheckHeaderBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualitySendCheckLineBySendChkNo(){
        ServiceData<List<ArchiveQualitySendCheckLineDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        ArchiveQualitySendCheckLineDTO archiveQualitySendCheckLineDTO=new ArchiveQualitySendCheckLineDTO();
        archiveQualitySendCheckLineDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualitySendCheckLineDTO> list=new ArrayList<>();
        list.add(archiveQualitySendCheckLineDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualitySendCheckLineBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualitySendCheckLineBySendChkNoError(){
        ServiceData<List<ArchiveQualitySendCheckLineDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        ArchiveQualitySendCheckLineDTO archiveQualitySendCheckLineDTO=new ArchiveQualitySendCheckLineDTO();
        archiveQualitySendCheckLineDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualitySendCheckLineDTO> list=new ArrayList<>();
        list.add(archiveQualitySendCheckLineDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualitySendCheckLineBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualitySendCheckLineBySendChkNoNull(){
        ServiceData<List<ArchiveQualitySendCheckLineDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        List<ArchiveQualitySendCheckLineDTO> list=new ArrayList<>();
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualitySendCheckLineBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityCheckHeaderBySendChkNo(){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        ArchiveQualityCheckDTO archiveQualityCheckDTO=new ArchiveQualityCheckDTO();
        archiveQualityCheckDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualityCheckDTO> list=new ArrayList<>();
        list.add(archiveQualityCheckDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityCheckHeaderBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityCheckHeaderBySendChkNoError(){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        ArchiveQualityCheckDTO archiveQualityCheckDTO=new ArchiveQualityCheckDTO();
        archiveQualityCheckDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualityCheckDTO> list=new ArrayList<>();
        list.add(archiveQualityCheckDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityCheckHeaderBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityCheckHeaderBySendChkNoNull(){
        ServiceData<List<ArchiveQualityCheckDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        List<ArchiveQualityCheckDTO> list=new ArrayList<>();
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityCheckHeaderBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityCheckLineBySendChkNo(){
        ServiceData<List<ArchiveQualityCheckLineDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        ArchiveQualityCheckLineDTO archiveQualityCheckLineDTO=new ArchiveQualityCheckLineDTO();
        archiveQualityCheckLineDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualityCheckLineDTO> list=new ArrayList<>();
        list.add(archiveQualityCheckLineDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityCheckLineBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityCheckLineBySendChkNoNull(){
        ServiceData<List<ArchiveQualityCheckLineDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        List<ArchiveQualityCheckLineDTO> list=new ArrayList<>();
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityCheckLineBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void selectQualityCheckLineBySendChkNoError(){
        ServiceData<List<ArchiveQualityCheckLineDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        ArchiveQualityCheckLineDTO archiveQualityCheckLineDTO=new ArchiveQualityCheckLineDTO();
        archiveQualityCheckLineDTO.setSendChkNo("SJS1912177644");
        List<ArchiveQualityCheckLineDTO> list=new ArrayList<>();
        list.add(archiveQualityCheckLineDTO);
        serviceData.setBo(list);
        Mockito.when(archiveQualityClient.selectQualityCheckLineBySendChkNo(Mockito.anyString())).thenReturn(serviceData);
    }

    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("IT部/技术规划部/系统产品");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();

    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<String> dataList = new ArrayList<>();
        dataList.add("SJS0912310487");
        dataList.add("SJS0912224106");
        dataList.add("SJS1912167642");
        Page<String> page = new Page<>();
        page.setRows(dataList);
        ServiceData<Page<String>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        Mockito.when(archiveQualityClient.selectQualityList(Mockito.any())).thenReturn(serviceData);
        archiveQualityService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<String> dataList = new ArrayList<>();
        dataList.add("SJS0912310487");
        dataList.add("SJS0912224106");
        dataList.add("SJS1912167642");
        Page<String> page = new Page<>();
        page.setRows(dataList);
        ServiceData<Page<String>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(page);
        Mockito.when(archiveQualityClient.selectQualityList(Mockito.any())).thenReturn(serviceData);
        archiveQualityService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        Page<String> page = new Page<>();
        ServiceData<Page<String>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);
        Mockito.when(archiveQualityClient.selectQualityList(Mockito.any())).thenReturn(serviceData);
        archiveQualityService.getArchiveDataList(archiveQueryParamDTO);
    }



} 
