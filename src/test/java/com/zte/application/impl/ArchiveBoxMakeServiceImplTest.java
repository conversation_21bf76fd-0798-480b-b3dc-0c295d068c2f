package com.zte.application.impl;

import com.zte.application.ArchiveBoqBoxMakeDataService;
import com.zte.application.ArchiveCommonService;
import com.zte.application.PubHrvOrgService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.ArchiveConstant;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
public class ArchiveBoxMakeServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ArchiveBoxMakeServiceImpl archiveBoxMakeService;
    @Mock
    ArchiveBoqBoxMakeDataService archiveBoqBoxMakeDataService;

    @Mock
    PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    ArchiveFileProperties archiveFileProperties;

    @Mock
    ArchiveCommonService archiveCommonService;

    @Mock
    PubHrvOrgService pubHrvOrgService;

    @Test
    public void getArchiveDataList(){
        ArchiveBoqBoxMakeDTO dto = new ArchiveBoqBoxMakeDTO();
        Page<ArchiveBoqBoxMakeDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto));
        Mockito.when(archiveBoqBoxMakeDataService.getPageByDateRange(any(),any()))
                .thenReturn(page);
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setPage(1);
        query.setRows(500);
        Assert.assertNotNull(archiveBoxMakeService.getArchiveDataList(query));
    }

    @Test
    public void archive(){
        try {
            archiveBoxMakeService.archive(null);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void archive1(){
        try {
            archiveBoxMakeService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void archive2(){
        try {
            getPubHrvOrg();
            getByEntityName();
            getPubHrvOrgByUserNameId();
            archiveBoxMakeService.archive(getArchiveTaskSend());
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    private void getPubHrvOrg() {
        PubHrvOrg pubHrvOrg = new PubHrvOrg();
        PowerMockito.when(pubHrvOrgService.getPubHrvOrgByUserNameId(any())).thenReturn(pubHrvOrg);
    }

    private void getPubHrvOrgByUserNameId(){
        PubHrvOrg pubHrvOrg = new PubHrvOrg();
        PowerMockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(any()))
                .thenReturn(pubHrvOrg);
    }

    private void getByEntityName(){
        ArchiveBoqBoxMakeDTO dto = new ArchiveBoqBoxMakeDTO();
        PowerMockito.when(archiveBoqBoxMakeDataService.getByEntityName(any())).thenReturn(dto);
    }

    private ArchiveTaskSend getArchiveTaskSend(){
        ArchiveTaskSend taskSend = new ArchiveTaskSend();
        taskSend.setBillId("123");
        taskSend.setBillNo("123");
        return taskSend;
    }
}
