package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.application.MesGetDictInforService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.infrastructure.feign.ArchiveInvSundriesClient;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.exception.RouteException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  杂项交易明细查询归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-07
 */
public class ArchiveInvSundriesServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveInvSundriesServiceImpl service;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;


    @Mock
    private ArchiveInvSundriesClient archiveInvSundriesClient;

    @Mock
    private MesGetDictInforService mesGetDictInforService;

    @Test
    public void archive() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("17");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillReserves("202308");
        taskSendArchive.setBillNo("059162600096");

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();
        ArchiveInvSundriesDTO archiveInvSundriesDTO = new ArchiveInvSundriesDTO();
        archiveInvSundriesDTO.setBillNo("059162600096");
        archiveInvSundriesDTO.setPostDateStr("2023-08-31 00:00:00");
        archiveInvSundriesDTO.setSubInventory("西安化学品库");
        archiveInvSundriesDTOS.add(archiveInvSundriesDTO);

        getQueryParams();
        ServiceData<List<ArchiveInvSundriesDTO>> serviceData = new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(archiveInvSundriesDTOS);
        Mockito.when(archiveInvSundriesClient.selectInvSundriesByCondition(Mockito.anyMap())).thenReturn(serviceData);

        createExcelAndUpload();
        Assert.assertNotNull(service.archive(taskSendArchive));
        archiveInvSundriesDTO.setPostDateStr("");
        try {
            service.archive(taskSendArchive);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ISSUE_DATE_NOT_NULL, e.getMessage());
        }
    }

    @Test
    public void archive2() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("17");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillReserves("202308");
        taskSendArchive.setBillNo("059162600096");

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();
        ArchiveInvSundriesDTO archiveInvSundriesDTO = new ArchiveInvSundriesDTO();
        archiveInvSundriesDTO.setBillNo("059162600096");
        archiveInvSundriesDTO.setPostDateStr("2023-08-31 00:00:00");
        archiveInvSundriesDTO.setSubInventory("低值易耗品库-B2");
        archiveInvSundriesDTOS.add(archiveInvSundriesDTO);

        getQueryParams();

        ServiceData<List<ArchiveInvSundriesDTO>> serviceData = new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(archiveInvSundriesDTOS);
        Mockito.when(archiveInvSundriesClient.selectInvSundriesByCondition(Mockito.anyMap())).thenReturn(serviceData);

        createExcelAndUpload();

        Assert.assertNotNull(service.archive(taskSendArchive));
    }

    private void getQueryParams(){
        String value="a,b,c";
        Mockito.when(mesGetDictInforService.getDicDescription(Mockito.anyString())).thenReturn(value);
    }

    private void getQueryParamsNull(){
        String value="";
        Mockito.when(mesGetDictInforService.getDicDescription(Mockito.anyString())).thenReturn(value);
    }


    @Test(expected = RouteException.class)
    public void archiveServerError() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("17");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillReserves("202308");
        taskSendArchive.setBillNo("059162600096");

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();
        ArchiveInvSundriesDTO archiveInvSundriesDTO = new ArchiveInvSundriesDTO();
        archiveInvSundriesDTO.setBillNo("059162600096");
        archiveInvSundriesDTO.setPostDateStr("2023-08-31 00:00:00");
        archiveInvSundriesDTO.setSubInventory("低值易耗品库-B2");
        archiveInvSundriesDTOS.add(archiveInvSundriesDTO);

        getQueryParams();

        ServiceData<List<ArchiveInvSundriesDTO>> serviceData = new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(archiveInvSundriesDTOS);
        Mockito.when(archiveInvSundriesClient.selectInvSundriesByCondition(Mockito.anyMap())).thenReturn(serviceData);

        createExcelAndUpload();

        service.archive(taskSendArchive);
    }

    @Test(expected = RouteException.class)
    public void archiveServerNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("17");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillReserves("202308");
        taskSendArchive.setBillNo("059162600096");

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();

        getQueryParams();

        ServiceData<List<ArchiveInvSundriesDTO>> serviceData = new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(archiveInvSundriesDTOS);
        Mockito.when(archiveInvSundriesClient.selectInvSundriesByCondition(Mockito.anyMap())).thenReturn(serviceData);
        createExcelAndUpload();

        service.archive(taskSendArchive);
    }

    @Test(expected = RouteException.class)
    public void archiveParamsNull() throws Exception {
        ArchiveTaskSend taskSendArchive=new ArchiveTaskSend();
        taskSendArchive.setTaskType("17");
        taskSendArchive.setBillId("");
        taskSendArchive.setBillReserves("202308");
        taskSendArchive.setBillNo("059162600096");

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();
        ArchiveInvSundriesDTO archiveInvSundriesDTO = new ArchiveInvSundriesDTO();
        archiveInvSundriesDTO.setBillNo("059162600096");
        archiveInvSundriesDTO.setPostDateStr("2023-08-31 00:00:00");
        archiveInvSundriesDTO.setSubInventory("西安化学品库");
        archiveInvSundriesDTOS.add(archiveInvSundriesDTO);

        getQueryParamsNull();
        createExcelAndUpload();
        service.archive(taskSendArchive);
    }


    private void createExcelAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.createExcelAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.any(),Mockito.anyList())).thenReturn(attachmentUploadVo);
    }



    @Test(expected = RouteException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();
        ArchiveInvSundriesDTO archiveInvSundriesDTO = new ArchiveInvSundriesDTO();
        archiveInvSundriesDTO.setBillNo("059162600096");
        archiveInvSundriesDTO.setPostDateStr("2023-08-31 00:00:00");
        archiveInvSundriesDTO.setSubInventory("低值易耗品库-B2");
        archiveInvSundriesDTOS.add(archiveInvSundriesDTO);

        Page<ArchiveInvSundriesDTO> page = new Page<>();
        page.setRows(archiveInvSundriesDTOS);

        ServiceData<Page<ArchiveInvSundriesDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        getQueryParams();
        Mockito.when(archiveInvSundriesClient.selectInvSundriesListByPage(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }



    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();

        Page<ArchiveInvSundriesDTO> page = new Page<>();
        page.setRows(archiveInvSundriesDTOS);

        ServiceData<Page<ArchiveInvSundriesDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        getQueryParams();
        Mockito.when(archiveInvSundriesClient.selectInvSundriesListByPage(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);

        List<ArchiveInvSundriesDTO> archiveInvSundriesDTOS = new ArrayList<>();
        ArchiveInvSundriesDTO archiveInvSundriesDTO = new ArchiveInvSundriesDTO();
        archiveInvSundriesDTO.setBillNo("059162600096");
        archiveInvSundriesDTO.setPostDateStr("2023-08-31 00:00:00");
        archiveInvSundriesDTO.setSubInventory("低值易耗品库-B2");
        archiveInvSundriesDTOS.add(archiveInvSundriesDTO);

        Page<ArchiveInvSundriesDTO> page = new Page<>();
        page.setRows(archiveInvSundriesDTOS);

        ServiceData<Page<ArchiveInvSundriesDTO>> serviceData=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(page);

        getQueryParams();
        Mockito.when(archiveInvSundriesClient.selectInvSundriesListByPage(Mockito.any())).thenReturn(serviceData);
        service.getArchiveDataList(archiveQueryParamDTO);
    }
}
