package com.zte.application.impl;

import com.zte.domain.model.ArchiveBoqBoxMakeDataRepository;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeDTO;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeItemDTO;
import com.zte.interfaces.dto.ArchiveBoqBoxMakeQueryDTO;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.PowerBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/7/19
 * @description :
 */
public class ArchiveBoqBoxMakeDataServiceImplTest extends PowerBaseTestCase {
    @InjectMocks
    ArchiveBoqBoxMakeDataServiceImpl archiveBoqBoxMakeDataService;
    @Mock
    ArchiveBoqBoxMakeDataRepository repository;

    @Test
    public void getByEntityName(){
        archiveBoqBoxMakeDataService.getByEntityName(null);
        Assert.assertNull(archiveBoqBoxMakeDataService.getByEntityName("123"));
    }

    @Test
    public void getArchiveUnitBoxMakeByBillId(){
        archiveBoqBoxMakeDataService.getArchiveUnitBoxMakeByBillId(null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getArchiveUnitBoxMakeByBillId("123"));
    }

    @Test
    public void getArchiveBoqBoxMakeItemByOrganizationIdAndBillId(){
        archiveBoqBoxMakeDataService.getArchiveBoqBoxMakeItemByOrganizationIdAndBillId(null,null);
        archiveBoqBoxMakeDataService.getArchiveBoqBoxMakeItemByOrganizationIdAndBillId("123",null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getArchiveBoqBoxMakeItemByOrganizationIdAndBillId("123","123"));
    }

    @Test
    public void getArchiveBoxMakeItemByOrganizationIdAndBillId(){
        archiveBoqBoxMakeDataService.getArchiveBoxMakeItemByOrganizationIdAndBillId(null,null);
        archiveBoqBoxMakeDataService.getArchiveBoxMakeItemByOrganizationIdAndBillId("123",null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getArchiveBoxMakeItemByOrganizationIdAndBillId("123","123"));
    }

    @Test
    public void getArchiveEuBoxMakeItemByOrganizationIdAndBillId(){
        archiveBoqBoxMakeDataService.getArchiveEuBoxMakeItemByOrganizationIdAndBillId(null,null);
        archiveBoqBoxMakeDataService.getArchiveEuBoxMakeItemByOrganizationIdAndBillId("123","");
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getArchiveEuBoxMakeItemByOrganizationIdAndBillId("123","123"));
    }

    @Test
    public void getArchiveEuUnitBoxMakeByOrganizationIdAndMfgSiteId(){
        archiveBoqBoxMakeDataService.getArchiveEuUnitBoxMakeByBillid(null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getArchiveEuUnitBoxMakeByBillid("123"));
    }

    @Test
    public void getPageByDateRange(){
        ArchiveQueryParamDTO query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        archiveBoqBoxMakeDataService.getPageByDateRange(null,null);
        archiveBoqBoxMakeDataService.getPageByDateRange(query,"");

        archiveBoqBoxMakeDataService.getPageByDateRange(query,"B");
        query.setStartDate(new Date());
        archiveBoqBoxMakeDataService.getPageByDateRange(query,"B");

        query.setEndDate(new Date());
        archiveBoqBoxMakeDataService.getPageByDateRange(query,"B");

        query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        query.setStartDate(null);
        query.setEndDate(new Date());
        archiveBoqBoxMakeDataService.getPageByDateRange(query,"B");

        query = new ArchiveQueryParamDTO();
        query.setRows(500);
        query.setPage(1);
        query.setStartDate(new Date());
        query.setEndDate(null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getPageByDateRange(query,"B"));
    }
    @Test
    public void getSecondItem(){
        archiveBoqBoxMakeDataService.getSecondItem(null);
        Assert.assertNull(archiveBoqBoxMakeDataService.getSecondItem("123"));
    }
    @Test
    public void getEUAdnBoqMakeItem(){
        ArchiveBoqBoxMakeItemDTO dto = new ArchiveBoqBoxMakeItemDTO();
        dto.setBillItemId("1");
        dto.setOrganizationId("635");
        PowerMockito.when(archiveBoqBoxMakeDataService.getArchiveBoqBoxMakeItemByOrganizationIdAndBillId("123","123")).thenReturn(Collections.singletonList(dto));
        List<ArchiveBoqBoxMakeItemDTO> fourItems =  new ArrayList<>();
        ArchiveBoqBoxMakeItemDTO fourItem=new ArchiveBoqBoxMakeItemDTO();
        fourItems.add(fourItem);
        Mockito.when(repository.getFourMakeItem(Mockito.anyString(),Mockito.anyString())).thenReturn(fourItems);

        archiveBoqBoxMakeDataService.getEUAdnBoqMakeItem(null,null);
        archiveBoqBoxMakeDataService.getEUAdnBoqMakeItem("123",null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getEUAdnBoqMakeItem("123","123"));
    }

    @Test
    public void getFourMakeItem(){
        archiveBoqBoxMakeDataService.getFourMakeItem(null,null);
        archiveBoqBoxMakeDataService.getFourMakeItem("123",null);
        Assert.assertNotNull(archiveBoqBoxMakeDataService.getFourMakeItem("123","123"));
    }

    private ArchiveBoqBoxMakeQueryDTO getArchiveBoqBoxMakeQueryDTO() {
        ArchiveBoqBoxMakeQueryDTO param = new ArchiveBoqBoxMakeQueryDTO();
        param.setStartDate(new Date());
        param.setEndDate(new Date());
        return param;
    }
}
