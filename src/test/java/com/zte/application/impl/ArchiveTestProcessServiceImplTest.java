package com.zte.application.impl;

import com.zte.application.ArchiveCommonService;
import com.zte.common.config.ArchiveFileProperties;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.ArchiveTaskSend;
import com.zte.domain.model.PubHrvOrg;
import com.zte.domain.model.PubHrvOrgRepository;
import com.zte.infrastructure.feign.ArchiveTestProcessClient;
import com.zte.interfaces.dto.ArchiveQueryParamDTO;
import com.zte.interfaces.dto.ArchiveTestProcessDTO;
import com.zte.interfaces.dto.AttachmentUploadVo;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

/**
 * <p>
 *  测试工艺单归档单元测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public class ArchiveTestProcessServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ArchiveTestProcessServiceImpl archiveTestProcessService;

    @Mock
    private ArchiveTestProcessClient archiveTestProcessClient;

    @Mock
    private PubHrvOrgRepository pubHrvOrgRepository;

    @Mock
    private ArchiveCommonService archiveCommonService;

    @Mock
    private ArchiveFileProperties archiveFileProperties;


    @Test
    public void archive() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("19");
        archiveTaskSend.setBillId("142");
        archiveTaskSend.setBillNo("EE20150709210121");

        selectTestProcessById();
        downloadFromFtpAndUpload();
        buildBusiness();
        Assert.assertNotNull(archiveTestProcessService.archive(archiveTaskSend));
    }


    @Test
    public void archiveNull() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");

        selectTestProcessById();
        downloadFromFtpAndUpload();
        buildBusinessOrgAndDateIsNull();
        Assert.assertNotNull(archiveTestProcessService.archive(archiveTaskSend));
    }

    @Test(expected = MesBusinessException.class)
    public void archiveServerError() throws Exception {
        ArchiveTaskSend archiveTaskSend = new ArchiveTaskSend();
        archiveTaskSend.setTaskId("1");
        archiveTaskSend.setTaskType("18");
        archiveTaskSend.setBillId("262");
        archiveTaskSend.setBillNo("B20160415143561");

        selectTestProcessByIdServerError();
        downloadFromFtpAndUpload();
        buildBusiness();
        archiveTestProcessService.archive(archiveTaskSend);
    }


    private void selectTestProcessById(){
        ServiceData<ArchiveTestProcessDTO> serviceData = new ServiceData<>();
        ArchiveTestProcessDTO dto = new ArchiveTestProcessDTO();
        dto.setBillNumber("EE20150709210121");
        dto.setCountryName("美国");
        dto.setProductSubClass("MF97V");
        dto.setModularNo("P123");
        dto.setCreateBy("test123456");
        dto.setCreationDateStr("2015-01-08 00:00:00");
        dto.setSingleCode("126685751012");
        dto.setBillVersion("A");
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceData.setCode(retCode);
        serviceData.setBo(dto);
        Mockito.when(archiveTestProcessClient.selectTestProcessById(Mockito.any())).thenReturn(serviceData);
    }

    private void selectTestProcessByIdServerError(){
        ServiceData<ArchiveTestProcessDTO> serviceData = new ServiceData<>();
        ArchiveTestProcessDTO dto = new ArchiveTestProcessDTO();
        dto.setBillNumber("EE20150709210121");
        dto.setCountryName("美国");
        dto.setProductSubClass("MF97V");
        dto.setModularNo("P123");
        dto.setCreateBy("test123456");
        dto.setCreationDateStr("2015-01-08 00:00:00");
        dto.setSingleCode("126685751012");
        dto.setBillVersion("A");
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceData.setCode(retCode);
        serviceData.setBo(dto);
        Mockito.when(archiveTestProcessClient.selectTestProcessById(Mockito.any())).thenReturn(serviceData);
    }

    private void downloadFromFtpAndUpload() throws Exception {
        AttachmentUploadVo attachmentUploadVo = new AttachmentUploadVo();
        attachmentUploadVo.setFileName("abc");
        attachmentUploadVo.setDiskKey("123");
        attachmentUploadVo.setFileSize(12345L);
        Mockito.when(archiveCommonService.downloadFromFtpAndUpload(Mockito.any(ArchiveTaskSend.class),Mockito.anyString(),Mockito.anyString())).thenReturn(attachmentUploadVo);
    }

    private void buildBusiness(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        pubHrvOrg.setOrgFullName("制造工程部/供应链中心/终端事业部");
        pubHrvOrg.setOrgNo("004030902");
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }


    private void buildBusinessOrgAndDateIsNull(){
        Mockito.when(archiveFileProperties.getCommunicationCode()).thenReturn("commun");
        PubHrvOrg pubHrvOrg=new PubHrvOrg();
        Mockito.when(pubHrvOrgRepository.getPubHrvOrgByUserNameId(Mockito.any())).thenReturn(pubHrvOrg);
    }

    @Test(expected = MesBusinessException.class)
    public void getArchiveDataList(){
        getArchiveDataListSucess();
        getArchiveDataListIsNull();
        getArchiveDataListServerError();
    }

    private void getArchiveDataListSucess(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveTestProcessDTO dto = new ArchiveTestProcessDTO();
        dto.setBillNumber("EE20150709210121");
        dto.setCountryName("美国");
        dto.setProductSubClass("MF97V");
        dto.setModularNo("P123");
        dto.setCreateBy("test123456");
        dto.setCreationDateStr("2015-01-08 00:00:00");
        dto.setSingleCode("126685751012");
        dto.setBillVersion("A");
        List<ArchiveTestProcessDTO> dataList = new ArrayList<>();
        dataList.add(dto);
        Page<ArchiveTestProcessDTO> page = new Page<>();
        page.setRows(dataList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<ArchiveTestProcessDTO>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveTestProcessClient.selectTestProcessList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveTestProcessService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListServerError(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);
        ArchiveTestProcessDTO dto = new ArchiveTestProcessDTO();
        dto.setBillNumber("EE20150709210121");
        dto.setCountryName("美国");
        dto.setProductSubClass("MF97V");
        dto.setModularNo("P123");
        dto.setCreateBy("test123456");
        dto.setCreationDateStr("2015-01-08 00:00:00");
        dto.setSingleCode("126685751012");
        dto.setBillVersion("A");
        List<ArchiveTestProcessDTO> dataList = new ArrayList<>();
        dataList.add(dto);
        Page<ArchiveTestProcessDTO> page = new Page<>();
        page.setRows(dataList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<ArchiveTestProcessDTO>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode("");
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveTestProcessClient.selectTestProcessList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveTestProcessService.getArchiveDataList(archiveQueryParamDTO);
    }

    private void getArchiveDataListIsNull(){
        Date lastSynDate = DateUtil.convertStringToDate("2017-01-01 00:00:00", DateUtil.DATE_FORMATE_FULL);
        Date endDate = DateUtil.convertStringToDate("2017-01-11 00:00:00",DateUtil.DATE_FORMATE_FULL);

        List<ArchiveTestProcessDTO> dataList = new ArrayList<>();

        Page<ArchiveTestProcessDTO> page = new Page<>();
        page.setRows(dataList);
        page.setCurrent(1);
        page.setTotalPage(5);
        page.setPageSize(500);
        ServiceData<Page<ArchiveTestProcessDTO>> serviceDataObjectVO=new ServiceData<>();
        RetCode retCode=new RetCode();
        retCode.setCode(Constant.SUCCESS_CODE);
        serviceDataObjectVO.setCode(retCode);
        serviceDataObjectVO.setBo(page);

        Mockito.when(archiveTestProcessClient.selectTestProcessList(Mockito.any())).thenReturn(serviceDataObjectVO);

        ArchiveQueryParamDTO archiveQueryParamDTO = new ArchiveQueryParamDTO();
        archiveQueryParamDTO.setStartDate(lastSynDate);
        archiveQueryParamDTO.setEndDate(endDate);
        archiveQueryParamDTO.setPage(1);
        archiveQueryParamDTO.setRows(Constant.BATCH_QUERY_SIZE);
        archiveTestProcessService.getArchiveDataList(archiveQueryParamDTO);
    }


}
