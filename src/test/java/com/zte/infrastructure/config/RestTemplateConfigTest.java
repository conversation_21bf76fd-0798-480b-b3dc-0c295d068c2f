package com.zte.infrastructure.config;

import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RestTemplate配置测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class RestTemplateConfigTest {
    
    @Test
    public void testRestTemplateBeans() {
        // 创建Spring上下文
        AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext();
        context.register(RestTemplateConfig.class);
        context.refresh();
        
        try {
            // 获取默认RestTemplate Bean
            RestTemplate restTemplate = context.getBean(RestTemplate.class);
            assertNotNull(restTemplate, "默认RestTemplate Bean应该存在");
            
            // 获取预制机专用RestTemplate Bean
            RestTemplate precastRestTemplate = context.getBean("precastRestTemplate", RestTemplate.class);
            assertNotNull(precastRestTemplate, "预制机专用RestTemplate Bean应该存在");
            
            // 验证两个Bean不是同一个实例
            assertNotSame(restTemplate, precastRestTemplate, "两个RestTemplate应该是不同的实例");
            
            System.out.println("RestTemplate配置测试通过");
        } finally {
            context.close();
        }
    }
} 