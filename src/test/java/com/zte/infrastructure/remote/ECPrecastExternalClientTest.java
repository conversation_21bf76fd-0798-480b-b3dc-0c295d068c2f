package com.zte.infrastructure.remote;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.domain.model.datawb.SysLookupValuesRepository;
import com.zte.interfaces.dto.ECPrecastApiResponse;
import com.zte.interfaces.dto.ECPrecastSubBarcodeInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 预制机外部客户端测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
public class ECPrecastExternalClientTest {
    
    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    
    @InjectMocks
    private ECPrecastExternalClient ecPrecastExternalClient;
    
    private ObjectMapper realObjectMapper;
    
    @BeforeEach
    public void setUp() {
        realObjectMapper = new ObjectMapper();
        // 设置私有字段
        ReflectionTestUtils.setField(ecPrecastExternalClient, "retryCount", 3);
    }
    
    private void setupDatabaseMock() {
        // 只在需要的测试中设置数据库Mock
        when(sysLookupValuesRepository.getDescriptionByLookupCode("824009900004"))
                .thenReturn("http://imes.dev.zte.com.cn/zte-iscp-imes-manufacture/bindRecord/getPrecastInfoByParentSn?parentSn=");
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_Success_WithData() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST001";
        String responseBody = createSuccessResponseJson();
        
        ECPrecastApiResponse apiResponse = createSuccessApiResponse();
        List<ECPrecastSubBarcodeInfo> expectedSubBarcodes = createSubBarcodeList();
        apiResponse.setBo(expectedSubBarcodes);
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        // Mock行为
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);
        when(objectMapper.readValue(responseBody, ECPrecastApiResponse.class))
                .thenReturn(apiResponse);
        
        // 执行测试
        List<ECPrecastSubBarcodeInfo> result = ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("SUB001", result.get(0).getSn());
        assertEquals("SUB002", result.get(1).getSn());
        
        // 验证调用
        verify(restTemplate, times(1)).exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class));
        verify(objectMapper, times(1)).readValue(responseBody, ECPrecastApiResponse.class);
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_Success_WithNullData() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST002";
        String responseBody = createSuccessResponseJson();
        
        ECPrecastApiResponse apiResponse = createSuccessApiResponse();
        apiResponse.setBo(null); // 返回null数据
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        // Mock行为
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);
        when(objectMapper.readValue(responseBody, ECPrecastApiResponse.class))
                .thenReturn(apiResponse);
        
        // 执行测试
        List<ECPrecastSubBarcodeInfo> result = ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn);
        
        // 验证结果
        assertNull(result);
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_ApiError() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST003";
        String responseBody = createErrorResponseJson();
        
        ECPrecastApiResponse apiResponse = createErrorApiResponse();
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        // Mock行为
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);
        when(objectMapper.readValue(responseBody, ECPrecastApiResponse.class))
                .thenReturn(apiResponse);
        
        // 执行测试并验证异常
        ECPrecastExternalClient.ECPrecastApiException exception = assertThrows(
                ECPrecastExternalClient.ECPrecastApiException.class,
                () -> ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn)
        );
        
        assertTrue(exception.getMessage().contains("预制机外部接口返回错误: 系统内部错误"));
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_HttpError() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST004";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>("Internal Server Error", HttpStatus.INTERNAL_SERVER_ERROR);
        
        // Mock行为 - 模拟HTTP错误
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity)
                .thenReturn(responseEntity)
                .thenReturn(responseEntity);
        
        // 执行测试并验证异常
        ECPrecastExternalClient.ECPrecastApiException exception = assertThrows(
                ECPrecastExternalClient.ECPrecastApiException.class,
                () -> ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn)
        );
        
        assertFalse(exception.getMessage().contains("预制机外部接口调用失败，已重试3次"));
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_RetrySuccess() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST005";
        String responseBody = createSuccessResponseJson();
        
        ECPrecastApiResponse apiResponse = createSuccessApiResponse();
        List<ECPrecastSubBarcodeInfo> expectedSubBarcodes = createSubBarcodeList();
        apiResponse.setBo(expectedSubBarcodes);
        
        ResponseEntity<String> errorResponse = new ResponseEntity<>("Server Error", HttpStatus.INTERNAL_SERVER_ERROR);
        ResponseEntity<String> successResponse = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        // Mock行为 - 第一次失败，第二次成功
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(errorResponse)
                .thenReturn(successResponse);
        when(objectMapper.readValue(responseBody, ECPrecastApiResponse.class))
                .thenReturn(apiResponse);
        
        // 执行测试
        List<ECPrecastSubBarcodeInfo> result = ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证重试调用了2次
        verify(restTemplate, times(2)).exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class));
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_JsonParseException() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST006";
        String responseBody = "invalid json";
        
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        
        // Mock行为
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenReturn(responseEntity);
        when(objectMapper.readValue(responseBody, ECPrecastApiResponse.class))
                .thenThrow(new RuntimeException("JSON parse error"));
        
        // 执行测试并验证异常
        ECPrecastExternalClient.ECPrecastApiException exception = assertThrows(
                ECPrecastExternalClient.ECPrecastApiException.class,
                () -> ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn)
        );
        
        assertTrue(exception.getMessage().contains("预制机外部接口调用异常: JSON parse error"));
    }
    
    @Test
    public void testGetSubBarcodesByParentSn_NetworkException() throws Exception {
        setupDatabaseMock();
        
        // 准备测试数据
        String parentSn = "TEST007";
        
        // Mock行为 - 模拟网络异常
        when(restTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
                .thenThrow(new RuntimeException("Connection timeout"));
        
        // 执行测试并验证异常
        ECPrecastExternalClient.ECPrecastApiException exception = assertThrows(
                ECPrecastExternalClient.ECPrecastApiException.class,
                () -> ecPrecastExternalClient.getSubBarcodesByParentSn(parentSn)
        );
        
        assertTrue(exception.getMessage().contains("预制机外部接口调用异常: Connection timeout"));
    }
    
    // === 纯JSON解析测试（不依赖网络调用Mock） ===
    
    @Test
    public void testJsonDeserialization() throws Exception {
        // 模拟外部接口返回的JSON响应
        String jsonResponse = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"SUCCESS\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "    {\n" +
                "      \"parentSn\": \"TEST001\",\n" +
                "      \"sn\": \"SUB001\",\n" +
                "      \"itemCode\": \"ITEM001\",\n" +
                "      \"qty\": 1,\n" +
                "      \"snType\": \"0\",\n" +
                "      \"envAttr\": \"GREEN\",\n" +
                "      \"isFinish\": \"N\",\n" +
                "      \"createTime\": \"2024-01-01T10:00:00\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"parentSn\": \"TEST001\",\n" +
                "      \"sn\": \"SUB002\",\n" +
                "      \"itemCode\": \"ITEM002\",\n" +
                "      \"qty\": 2,\n" +
                "      \"snType\": \"1\",\n" +
                "      \"envAttr\": \"GREEN\",\n" +
                "      \"isFinish\": \"N\",\n" +
                "      \"createTime\": \"2024-01-01T10:00:00\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        
        // 解析JSON响应
        ECPrecastApiResponse apiResponse = realObjectMapper.readValue(jsonResponse, ECPrecastApiResponse.class);
        
        // 验证响应状态
        assertTrue(apiResponse.isSuccess(), "响应应该成功");
        assertEquals("0000", apiResponse.getCode().getCode(), "响应码应该是0000");
        
        // 验证业务数据
        List<ECPrecastSubBarcodeInfo> subBarcodes = apiResponse.getBo();
        assertNotNull(subBarcodes, "业务数据不应该为null");
        assertEquals(2, subBarcodes.size(), "应该返回2个子条码");
        
        // 验证第一个子条码
        ECPrecastSubBarcodeInfo firstBarcode = subBarcodes.get(0);
        assertEquals("TEST001", firstBarcode.getParentSn(), "主条码应该匹配");
        assertEquals("SUB001", firstBarcode.getSn(), "子条码应该匹配");
        assertEquals("ITEM001", firstBarcode.getItemCode(), "物料代码应该匹配");
        assertEquals(new BigDecimal("1"), firstBarcode.getQty(), "数量应该匹配");
        assertEquals("0", firstBarcode.getSnType(), "条码类型应该匹配");
        assertEquals("序列码", firstBarcode.getSnTypeDesc(), "条码类型描述应该匹配");
        assertEquals("N", firstBarcode.getIsFinish(), "完成状态应该匹配");
        assertFalse(firstBarcode.isFinished(), "应该未完成");
        assertFalse(firstBarcode.isAvailable(), "根据当前代码逻辑，isFinish='N'时isAvailable()返回false");
        
        // 验证第二个子条码
        ECPrecastSubBarcodeInfo secondBarcode = subBarcodes.get(1);
        assertEquals("SUB002", secondBarcode.getSn(), "第二个子条码应该匹配");
        assertEquals("批次码", secondBarcode.getSnTypeDesc(), "第二个条码类型描述应该匹配");
    }
    
    @Test
    public void testErrorResponse() throws Exception {
        // 模拟错误响应
        String errorResponse = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"500\",\n" +
                "    \"msgId\": \"ERROR\",\n" +
                "    \"msg\": \"系统错误\"\n" +
                "  },\n" +
                "  \"bo\": null\n" +
                "}";
        
        // 解析错误响应
        ECPrecastApiResponse apiResponse = realObjectMapper.readValue(errorResponse, ECPrecastApiResponse.class);
        
        // 验证错误状态
        assertFalse(apiResponse.isSuccess(), "响应应该失败");
        assertEquals("500", apiResponse.getCode().getCode(), "错误码应该是500");
        assertEquals("系统错误", apiResponse.getErrorMessage(), "错误信息应该匹配");
    }
    
    // === 辅助方法 ===
    
    private String createSuccessResponseJson() {
        return "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"SUCCESS\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": [\n" +
                "    {\n" +
                "      \"parentSn\": \"TEST001\",\n" +
                "      \"sn\": \"SUB001\",\n" +
                "      \"itemCode\": \"ITEM001\",\n" +
                "      \"qty\": 1,\n" +
                "      \"snType\": \"0\",\n" +
                "      \"envAttr\": \"GREEN\",\n" +
                "      \"isFinish\": \"N\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }
    
    private String createErrorResponseJson() {
        return "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"500\",\n" +
                "    \"msgId\": \"ERROR\",\n" +
                "    \"msg\": \"系统内部错误\"\n" +
                "  },\n" +
                "  \"bo\": null\n" +
                "}";
    }
    
    private ECPrecastApiResponse createSuccessApiResponse() {
        ECPrecastApiResponse response = new ECPrecastApiResponse();
        ECPrecastApiResponse.ECPrecastApiCode code = new ECPrecastApiResponse.ECPrecastApiCode();
        code.setCode("0000");
        code.setMsgId("SUCCESS");
        code.setMsg("操作成功");
        response.setCode(code);
        return response;
    }
    
    private ECPrecastApiResponse createErrorApiResponse() {
        ECPrecastApiResponse response = new ECPrecastApiResponse();
        ECPrecastApiResponse.ECPrecastApiCode code = new ECPrecastApiResponse.ECPrecastApiCode();
        code.setCode("500");
        code.setMsgId("ERROR");
        code.setMsg("系统内部错误");
        response.setCode(code);
        return response;
    }
    
    private List<ECPrecastSubBarcodeInfo> createSubBarcodeList() {
        List<ECPrecastSubBarcodeInfo> list = new ArrayList<>();
        
        ECPrecastSubBarcodeInfo barcode1 = new ECPrecastSubBarcodeInfo();
        barcode1.setParentSn("TEST001");
        barcode1.setSn("SUB001");
        barcode1.setItemCode("ITEM001");
        barcode1.setQty(new BigDecimal("1"));
        barcode1.setSnType("0");
        barcode1.setEnvAttr("GREEN");
        barcode1.setIsFinish("N");
        list.add(barcode1);
        
        ECPrecastSubBarcodeInfo barcode2 = new ECPrecastSubBarcodeInfo();
        barcode2.setParentSn("TEST001");
        barcode2.setSn("SUB002");
        barcode2.setItemCode("ITEM002");
        barcode2.setQty(new BigDecimal("2"));
        barcode2.setSnType("1");
        barcode2.setEnvAttr("GREEN");
        barcode2.setIsFinish("N");
        list.add(barcode2);
        
        return list;
    }
} 