package com.zte.interfaces;

import com.zte.application.BaImuService;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.BaImuDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.model.RetCode;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * IMU信息（小工序)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-30 10:15:33
 */
@RestController
@RequestMapping("/baimu")
public class BaImuController {
    @Autowired
    private BaImuService baImuService;

    /**
     * 保存
     */
    @ApiOperation("保存")
    @RequestMapping(value = "/save", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData save(HttpServletRequest request, @RequestBody List<BaImuDTO> baImuDTOList) throws Exception {
        ServiceData ret = new ServiceData();
        String factoryId = request.getHeader(SysConst.HTTP_HEADER_X_FACTORY_ID);
        if (StringUtils.isEmpty(factoryId)) {
            ret.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, MessageId.FACTORY_ID_IS_NULL));
            return ret;
        }

        baImuService.save(baImuDTOList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }


}
