package com.zte.interfaces;

import com.zte.application.CustomerItemsService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.CustomerItemsQueryDTO;
import com.zte.interfaces.dto.bytedance.CustomerImportDTO;
import com.zte.interfaces.dto.bytedance.CustomerRequestDTO;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.annotation.Export;
import com.zte.springbootframe.common.annotation.OpenApi;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/4 11:09
 */
@RestController
@RequestMapping("/customerItemsCtrl")
public class CustomerItemsController {
    @Autowired
    private CustomerItemsService customerItemsService;

    @ApiOperation("新增客户物料信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/newCustomerItems", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData newCustomerItems(HttpServletRequest request, @RequestBody CustomerItemsDTO dto) {
        RequestHeadValidationUtil.validaFactoryId(request);
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_ID);
        dto.setLastUpdatedBy(empNo);
        dto.setCreateBy(empNo);
        return ServiceDataBuilderUtil.success(customerItemsService.newCustomerItems(dto));
    }

    @ApiOperation("修改客户物料信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/updateCustomerItems", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData updateCustomerItems(HttpServletRequest request, @RequestBody CustomerItemsDTO dto) {
        RequestHeadValidationUtil.validaFactoryId(request);
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_ID);
        dto.setLastUpdatedBy(empNo);
        return ServiceDataBuilderUtil.success(customerItemsService.updateCustomerItems(dto));
    }

    @ApiOperation("删除客户物料信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/deleteCustomerItems", method = RequestMethod.DELETE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData deleteCustomerItems(HttpServletRequest request, String id) {
        RequestHeadValidationUtil.validaFactoryId(request);
        String empNo = request.getHeader(SysConst.HTTP_HEADER_X_EMP_ID);
        return ServiceDataBuilderUtil.success(customerItemsService.deleteCustomerItems(id, empNo));
    }

    @ApiOperation("分页获取客户物料信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/pageCustomerItemsInfo", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData pageCustomerItemsInfo(HttpServletRequest request, @RequestBody CustomerItemsDTO dto) {
        RequestHeadValidationUtil.validaFactoryId(request);
        return ServiceDataBuilderUtil.success(customerItemsService.pageCustomerItemsInfo(dto));
    }

    @ApiOperation("导出客户物料信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @Export
    @RequestMapping(value = "/exportCustomerItems", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData exportCustomerItems(HttpServletRequest request, HttpServletResponse response, @RequestBody CustomerItemsDTO dto) throws Exception {
        RequestHeadValidationUtil.validaFactoryId(request);
        String empNo = request.getHeader("X-Real-Emp-No");
        dto.setEmpNo(empNo);
        customerItemsService.exportCustomerItems(dto, response);
        return ServiceDataBuilderUtil.success();
    }

    @ApiOperation("按ZTE代码查客户信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/getCustomerItemsInfo", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @OpenApi(name = "getCustomerItemsInfo", describe = "按ZTE代码查客户信息-客户信息回传专用", consumer = {"MES", "客户协同"})
    public ServiceData getCustomerItemsInfo(@RequestBody CustomerItemsDTO dto) {
        return ServiceDataBuilderUtil.success(customerItemsService.getCustomerItemsInfo(dto));
    }

    @ApiOperation("按客户名称和合作模式查询客户物料信息")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/getCustomerItemsByCustomerAndMode", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @OpenApi(name = "getCustomerItemsByCustomerAndMode", describe = "按客户名称和合作模式查询客户物料信息", consumer = {"MES", "客户协同"})
    public ServiceData getCustomerItemsByCustomerAndMode(@RequestBody CustomerItemsQueryDTO dto) {
        return ServiceDataBuilderUtil.success(customerItemsService.getCustomerItemsByCustomerAndMode(dto));
    }

    @ApiOperation("按ZTE代码查供应商")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/queryItemBrandName", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData queryItemBrandName(String itemNo) throws Exception {
        return ServiceDataBuilderUtil.success(customerItemsService.queryItemBrandName(itemNo));
    }

    @ApiOperation("按ZTE代码查客户信息-客户信息回传专用")
    @PostMapping(value = "/getListByItemNoList")
    @OpenApi(name = "按ZTE代码查客户信息-客户信息回传专用", describe = "按ZTE代码查客户信息-客户信息回传专用", consumer = {"MES", "客户协同"})
    public ServiceData getListByItemNoList(@RequestBody CustomerItemsDTO customerItemsDTO) {
        return ServiceDataBuilderUtil.success(customerItemsService.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO));
    }

    @ApiOperation("按客户名称获取客户代码信息")
    @PostMapping(value = "/queryListByCustomerList")
    public ServiceData queryListByCustomerList(@RequestBody CustomerItemsDTO customerItemsDTO) {
        return ServiceDataBuilderUtil.success(customerItemsService.queryListByCustomerList(customerItemsDTO));
    }

    @ApiOperation("下载标模任务批量导入excel模板")
    @Export
    @RequestMapping(value = "/exportTaskExcel", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData exportTaskExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String tableName = request.getParameter("tableName");
        customerItemsService.exportTaskExcel(response, tableName);
        ServiceData ret = new ServiceData();
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("excel导入解析到前台")
    @RequestMapping(value = "/analyzeModelBatch", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData analyzeModelBatch(@RequestParam("file") MultipartFile file,
                                         @RequestParam("tableName") String tableName,
                                         HttpServletRequest request) throws Exception {
        ServiceData ret = new ServiceData();
        // 校验文件是否存在
        if (file == null || file.isEmpty()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.NO_FIND);
        }
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);

        // 处理文件（例如解析 Excel 并返回导入结果）
        List<CustomerImportDTO> resolveList = customerItemsService.uploadCustomerImportBatch(file.getInputStream(), tableName);

        ret.setBo(resolveList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("excel文件内容校验")
    @RequestMapping(value = "/checkTaskBatch", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData checkTaskBatch(HttpServletRequest request, @RequestBody List<CustomerImportDTO> requestBody) throws Exception {
        ServiceData ret = new ServiceData();
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        if (CollectionUtils.isEmpty(requestBody)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.DATA_IS_EMPTY));
        }
        List<CustomerImportDTO> checkList = customerItemsService.checkTaskBatch(requestBody);
        ret.setBo(checkList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("客户物料代码信息批量导入")
    @RequestMapping(value = "/batchImportExcel", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData batchImportExcel(HttpServletRequest request, @RequestBody CustomerRequestDTO customerRequestDTO) throws Exception {
        List<CustomerImportDTO> confirmResult = customerRequestDTO.getConfirmResult();
        List<CustomerImportDTO> reCheckResult = customerRequestDTO.getReCheckResult();
        ServiceData ret = new ServiceData();
        // 校验工号和工厂ID
        RequestHeadValidationUtil.validaFactoryIdAndEmpno(request);
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if (CollectionUtils.isEmpty(confirmResult) && CollectionUtils.isEmpty(reCheckResult)) {
            throw new Exception(CommonUtils.getLmbMessage(MessageId.DATA_IS_EMPTY));
        }
        customerItemsService.batchInsert(reCheckResult, confirmResult, empNo);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }

    @ApiOperation("按客户名称分页查询ZTE代码")
    @ApiResponses({@ApiResponse(code = 400, message = "请求参数没填好"),
            @ApiResponse(code = 404, message = "请求路径没有或页面跳转路径不对")})
    @RequestMapping(value = "/getZteCodeByCustomerName/{page}/{rows}", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ServiceData getZteCodeByCustomerName(@PathVariable("page") int page, @PathVariable("rows") int rows, @RequestBody List<String> customerNameList) {
        return ServiceDataBuilderUtil.success(customerItemsService.getZteCodeByCustomerName(customerNameList, rows, page));
    }


    @ApiOperation("按客户名称和料单代码前12位List获取客户代码信息")
    @PostMapping(value = "/queryListByCustomerAndItemNoList")
    public ServiceData queryListByCustomerAndItemNoList(@RequestBody CustomerItemsDTO customerItemsDTO) {
        return ServiceDataBuilderUtil.success(customerItemsService.queryListByCustomerAndItemNoList(customerItemsDTO));
    }
    @ApiOperation("根据mpn获取zteCode")
    @PostMapping(value = "/getZteCodeOfSameItem")
    public ServiceData getZteCodeOfSameItem(@RequestBody List<String> itemSupplierNoList) {
        return ServiceDataBuilderUtil.success(customerItemsService.getZteCodeOfSameItem(itemSupplierNoList));
    }

    @ApiOperation("根据zteCode获取属于同一物料的zteCode")
    @PostMapping(value = "/getSameItemOfZteCode")
    public ServiceData getSameItemOfZteCode(@RequestBody Map<String, List<String>> paramMap) {
        return ServiceDataBuilderUtil.success(customerItemsService.getSameItemOfZteCode(paramMap.get("zteCodeList"), paramMap.get("customerNumberList")));
    }
}
