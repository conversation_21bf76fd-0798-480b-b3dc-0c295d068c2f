/**
 * 项目名称 : zte-mes-manufactureshare-datawbsys
 * 创建日期 : 2019-12-04
 * 修改历史 :
 *   1. [2019-12-04] 创建文件 by 6396000647
 **/
package com.zte.interfaces.stepdt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.stepdt.StAssetStockService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.interfaces.stepdt.dto.AssetStockDTO;
import com.zte.interfaces.stepdt.dto.AssetStockResultDTO;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;

import io.swagger.annotations.Api;

/**
 * 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/assetStock")
@Api("模具库存服务")
public class StAssetStockController {

    @Autowired
    private StAssetStockService stAssetStockService;

    /**
     * 添加方法功能描述
     * @param request
     * @param dto
     * @return ServiceData
     **/
    @RequestMapping(value = "/addStock",method = RequestMethod.POST,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ServiceData addStock(HttpServletRequest request, @RequestBody List<AssetStockDTO>  list) {
        ServiceData ret = new ServiceData();
        if(list.isEmpty()){
        	ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE,RetCode.VALIDATIONERROR_MSGID)); 
        	ret.setBo(CommonUtils.getLmbMessage(MessageId.DATA_IS_NULL));
        	return ret;
        }
        Calendar calendar = Calendar.getInstance();
        Date date=new Date();
        calendar.setTime(date);
        int getDay = calendar.get(Calendar.DAY_OF_MONTH);
        if(getDay == calendar.getActualMaximum(Calendar.DAY_OF_MONTH)){
        	//最后一天
        	//判断是否18点后
        	SimpleDateFormat df = new SimpleDateFormat("HH");
        	String hour=df.format(date);
        	int hourInt=Integer.valueOf(hour);
        	int hourStatic=18;
        	if(hourInt>=hourStatic){
        		ret.setCode(new RetCode(RetCode.VALIDATIONERROR_CODE,RetCode.VALIDATIONERROR_MSGID)); 
            	ret.setBo(CommonUtils.getLmbMessage(MessageId.MONTH_COMPUTE_TIME));
            	return ret;
        	} 
        }
        //判断条码再条码库存表是否存在，如果存在，则开始操作，否则不允许操作
        List<AssetStockResultDTO> liStockResultDTOs=new ArrayList<>();
        for (AssetStockDTO aStockDTO : list) {
			//判断条码是否存在
        	String errorMsg=stAssetStockService.addAssetStock(aStockDTO);
        	AssetStockResultDTO assetStockResultDTO=new AssetStockResultDTO();
        	assetStockResultDTO.setAssetNo(aStockDTO.getAssetNo());
        	assetStockResultDTO.setStockNo(aStockDTO.getStockNo());
        	if(StringUtils.isEmpty(errorMsg)){
        		assetStockResultDTO.setSuccess(1);
        	}
        	else{
        		assetStockResultDTO.setSuccess(0);
        		assetStockResultDTO.setError(errorMsg);
        	}
        	liStockResultDTOs.add(assetStockResultDTO);
		} 
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
        ret.setBo(liStockResultDTOs);
        return ret;
    }
 
}