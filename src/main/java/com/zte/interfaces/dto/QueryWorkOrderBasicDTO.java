package com.zte.interfaces.dto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 指令表查询dto
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryWorkOrderBasicDTO implements Serializable {

    @ApiModelProperty(value = "开工开始时间")
    private Date startTimeToActualStart;

    @ApiModelProperty(value = "开工结束时间")
    private Date endTimeToActualStart;

    @ApiModelProperty(value = "完工开始时间")
    private Date startTimeToActualEndTime;

    @ApiModelProperty(value = "完工结束时间")
    private Date endTimeToActualEndTime;


    public Date getStartTimeToActualStart() {
        return startTimeToActualStart;
    }

    public void setStartTimeToActualStart(Date startTimeToActualStart) {
        this.startTimeToActualStart = startTimeToActualStart;
    }

    public Date getEndTimeToActualStart() {
        return endTimeToActualStart;
    }

    public void setEndTimeToActualStart(Date endTimeToActualStart) {
        this.endTimeToActualStart = endTimeToActualStart;
    }

    public Date getStartTimeToActualEndTime() {
        return startTimeToActualEndTime;
    }

    public void setStartTimeToActualEndTime(Date startTimeToActualEndTime) {
        this.startTimeToActualEndTime = startTimeToActualEndTime;
    }

    public Date getEndTimeToActualEndTime() {
        return endTimeToActualEndTime;
    }

    public void setEndTimeToActualEndTime(Date endTimeToActualEndTime) {
        this.endTimeToActualEndTime = endTimeToActualEndTime;
    }

    @Override
    public String toString() {
        return "QueryWorkOrderBasicDTO{" +
                "startTimeToActualStart=" + startTimeToActualStart +
                ", endTimeToActualStart=" + endTimeToActualStart +
                ", startTimeToActualEndTime=" + startTimeToActualEndTime +
                ", endTimeToActualEndTime=" + endTimeToActualEndTime +
                '}';
    }
}
