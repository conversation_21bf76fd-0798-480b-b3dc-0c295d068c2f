/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 * 1. [${date}] 创建文件 by ${user}
 **/
package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * DTO类
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CtRouteHeadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * // TODO remarks
     **/
    private String routeId;
    
    private String inRouteId;

    /**
     * // 工艺ID
     **/
    private String craftId;

    /**
     * 工艺编号
     **/
    private String craftNo;

    /**
     * 工艺名称
     **/
    private String craftName;

    /**
     * 工艺段
     **/
    private String craftSection;

    /**
     * // TODO remarks
     **/
    private String itemNo;

    /**
     * // TODO remarks
     **/
    private String processType;

    /**
     * // TODO remarks
     **/
    private String productType;

    /**
     * // TODO remarks
     **/
    private String routeDescription;

    /**
     * // TODO remarks
     **/
    private String routeDetail;

    /**
     * // TODO remarks
     **/
    private String remark;

    /**
     * // TODO remarks
     **/
    private String createBy;

    private String processCode;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date lastUpdatedDate;

    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal orgId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    /**
     * // TODO remarks
     **/
    private String attribute1;

    /**
     * // TODO remarks
     **/
    private String attribute2;

    /**
     * // TODO remarks
     **/
    private String attribute3;

    /**
     * // TODO remarks
     **/
    private String attribute4;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date attribute5;

    /**
     * 来源系统
     **/
    private String sourceSys;

    private String craftVersion;

    private String sourceSysId;    //新增字段：来源系统ID

    private String sourceSysNo;    //新增字段：来源系统No


	private String bomTemperatureFlag;  //炉温已维护标志
	private String cadHadImportFlag;  //CAD已导入标志
	private String premanuBomFlag;  //前加工已维护标志

	public String getBomTemperatureFlag() {
		return bomTemperatureFlag;
	}

	public void setBomTemperatureFlag(String bomTemperatureFlag) {
		this.bomTemperatureFlag = bomTemperatureFlag;
	}

	public String getCadHadImportFlag() {
		return cadHadImportFlag;
	}

	public void setCadHadImportFlag(String cadHadImportFlag) {
		this.cadHadImportFlag = cadHadImportFlag;
	}

	public String getPremanuBomFlag() {
		return premanuBomFlag;
	}

	public void setPremanuBomFlag(String premanuBomFlag) {
		this.premanuBomFlag = premanuBomFlag;
	}

	public String getSourceSysId() {
        return sourceSysId;
    }

    public void setSourceSysId(String sourceSysId) {
        this.sourceSysId = sourceSysId;
    }

    public String getSourceSysNo() {
        return sourceSysNo;
    }

    public void setSourceSysNo(String sourceSysNo) {
        this.sourceSysNo = sourceSysNo;
    }

    private String sort; // 排序字段(值areaId或者areaCode)

    private String order; // 排序方式(默认升序,设为desc时降序)

    private Long page = 0L; // 请求的页码

    private Long rows = 0L; // 每页条数

    private String craftStatus; // 工艺状态

    public void setRouteId(String routeId) {

        this.routeId = routeId;
    }

    public String getRouteId() {

        return routeId;
    }

    public void setCraftId(String craftId) {

        this.craftId = craftId;
    }

    public String getCraftId() {

        return craftId;
    }

    public void setItemNo(String itemNo) {

        this.itemNo = itemNo;
    }

    public String getItemNo() {

        return itemNo;
    }

    public void setProcessType(String processType) {

        this.processType = processType;
    }

    public String getProcessType() {

        return processType;
    }

    public void setProductType(String productType) {

        this.productType = productType;
    }

    public String getProductType() {

        return productType;
    }

    public void setRouteDescription(String routeDescription) {

        this.routeDescription = routeDescription;
    }

    public String getRouteDescription() {

        return routeDescription;
    }

    public void setRouteDetail(String routeDetail) {

        this.routeDetail = routeDetail;
    }

    public String getRouteDetail() {

        return routeDetail;
    }

    public void setRemark(String remark) {

        this.remark = remark;
    }

    public String getRemark() {

        return remark;
    }

    public void setCreateBy(String createBy) {

        this.createBy = createBy;
    }

    public String getCreateBy() {

        return createBy;
    }

    public void setCreateDate(java.util.Date createDate) {

        this.createDate = createDate;
    }

    public java.util.Date getCreateDate() {

        return createDate;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {

        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getLastUpdatedBy() {

        return lastUpdatedBy;
    }

    public void setLastUpdatedDate(java.util.Date lastUpdatedDate) {

        this.lastUpdatedDate = lastUpdatedDate;
    }

    public java.util.Date getLastUpdatedDate() {

        return lastUpdatedDate;
    }

    public void setEnabledFlag(String enabledFlag) {

        this.enabledFlag = enabledFlag;
    }

    public String getEnabledFlag() {

        return enabledFlag;
    }

    public void setOrgId(BigDecimal orgId) {

        this.orgId = orgId;
    }

    public BigDecimal getOrgId() {

        return orgId;
    }

    public void setFactoryId(BigDecimal factoryId) {

        this.factoryId = factoryId;
    }

    public BigDecimal getFactoryId() {

        return factoryId;
    }

    public void setEntityId(BigDecimal entityId) {

        this.entityId = entityId;
    }

    public BigDecimal getEntityId() {

        return entityId;
    }

    public void setAttribute1(String attribute1) {

        this.attribute1 = attribute1;
    }

    public String getAttribute1() {

        return attribute1;
    }

    public void setAttribute2(String attribute2) {

        this.attribute2 = attribute2;
    }

    public String getAttribute2() {

        return attribute2;
    }

    public void setAttribute3(String attribute3) {

        this.attribute3 = attribute3;
    }

    public String getAttribute3() {

        return attribute3;
    }

    public void setAttribute4(String attribute4) {

        this.attribute4 = attribute4;
    }

    public String getAttribute4() {

        return attribute4;
    }

    public void setAttribute5(java.util.Date attribute5) {

        this.attribute5 = attribute5;
    }

    public java.util.Date getAttribute5() {

        return attribute5;
    }

    public String getSort() {

        return sort;
    }

    public void setSort(String sort) {

        this.sort = sort;
    }

    public String getOrder() {

        return order;
    }

    public void setOrder(String order) {

        this.order = order;
    }

    public Long getPage() {

        return page;
    }

    public void setPage(Long page) {

        this.page = page;
    }

    public Long getRows() {

        return rows;
    }

    public void setRows(Long rows) {

        this.rows = rows;
    }

    public String getCraftNo() {

        return craftNo;
    }

    public void setCraftNo(String craftNo) {

        this.craftNo = craftNo;
    }

    public String getCraftName() {

        return craftName;
    }

    public void setCraftName(String craftName) {

        this.craftName = craftName;
    }

    public String getSourceSys() {

        return sourceSys;
    }

    public void setSourceSys(String sourceSys) {

        this.sourceSys = sourceSys;
    }

    public String getCraftSection() {

        return craftSection;
    }

    public void setCraftSection(String craftSection) {

        this.craftSection = craftSection;
    }

    /**
     * route类型 1主工艺路径 2子工艺路径
     */
    private BigDecimal routeType;

    /**
     * 使用范围物料代码/任务
     */
    private String useScope;

    /**
     * 版本
     */
    private String routeVersion;

    /**
     * 工艺分组
     */
    private BigDecimal craftGroup;

    public BigDecimal getRouteType() {

        return routeType;
    }

    public void setRouteType(BigDecimal routeType) {

        this.routeType = routeType;
    }

    public String getUseScope() {

        return useScope;
    }

    public void setUseScope(String useScope) {

        this.useScope = useScope;
    }

    public String getRouteVersion() {

        return routeVersion;
    }

    public void setRouteVersion(String routeVersion) {

        this.routeVersion = routeVersion;
    }

    public BigDecimal getCraftGroup() {

        return craftGroup;
    }

    public void setCraftGroup(BigDecimal craftGroup) {

        this.craftGroup = craftGroup;
    }

    public String getprocessCode() {

        return processCode;
    }

    public void setprocessCode(String processCode) {

        this.processCode = processCode;
    }

    public String getCraftVersion() {

        return craftVersion;
    }

    public void setCraftVersion(String craftVersion) {

        this.craftVersion = craftVersion;
    }

    public String getCraftStatus() {

        return craftStatus;
    }

    public void setCraftStatus(String craftStatus) {

        this.craftStatus = craftStatus;
    }

	public String getInRouteId() {
		return inRouteId;
	}

	public void setInRouteId(String inRouteId) {
		this.inRouteId = inRouteId;
	}

}
