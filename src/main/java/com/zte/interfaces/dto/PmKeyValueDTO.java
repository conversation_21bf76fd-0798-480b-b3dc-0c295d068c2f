package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 
 * 添加类/接口功能描述
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PmKeyValueDTO {

    private static final long serialVersionUID = 1L;

    public java.lang.String getKeyName() {

        return keyName;
    }

    public void setKeyName(java.lang.String keyName) {

        this.keyName = keyName;
    }

    public java.lang.String getKeyVaule() {

        return keyVaule;
    }

    public void setKeyVaule(java.lang.String keyVaule) {

        this.keyVaule = keyVaule;
    }

    public static long getSerialversionuid() {

        return serialVersionUID;
    }

    private java.lang.String keyName; // 工序编码

    private java.lang.String keyVaule; // 工序名称
}
