package com.zte.interfaces.dto.mbom;/**
 * @date 2025-04-28 19:48
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * packageName com.zte.interfaces.dto.mbom
 *
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/4/28
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FixBomDetailDTO {
    private String id;
    private String parentId;
    private String fixBomId;
    /**
     * 物料层级
     */
    private String itemLevel;
    /**
     * 原始物料排序
     */
    private String originalItemSeq;
    /**
     * 物料排序
     */
    private String itemSeq;
    /**
     * 物料类型，基板，封装基板等
     */
    private String itemType;
    /**
     * 中兴编码
     */
    private String zteCode;
    /**
     * 中兴编码名称
     */
    private String zteCodeName;
    /**
     * 客户部件类型
     */
    private String customerComponentType;
    /**
     * 物料编号
     */
    private String itemNo;
    /**
     * 供应商物料编号
     */
    private String itemSupplierNo;
    /**
     * 客户物料名称
     */
    private String itemName;
    /**
     * 物料分类
     */
    private String itemMaterialType;
    /**
     * 物料数量
     */
    private String itemNumber;
    /**
     * 物料组
     */
    private String itemGroup;
    /**
     * 删除标志
     */
    private String deleteFlag;
    /**
     * 是否需要上传物料
     */
    private String requireMaterialUpload;
    /**
     * 是否为定价物料
     */
    private String isPricedMaterial;
    /**
     * 是否按SN上传
     */
    private String uploadBySn;
    /**
     * BOM是否必需
     */
    private String fixBomRequired;
    /**
     * 箱BOM是否必需
     */
    private String boxBomRequired;
    /**
     * 箱优先级
     */
    private Integer boxPriority;
    private String itemVersion;

    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date creationDate;
    private String lastUpdatedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date lastUpdateDate;

    List<FixBomDetailDTO> childNodes;

    private Double itemQty;

    /**
     * 上层成品料节点料号
     */
    private String parentItemSupplierNo;

    /**
     * 上层成品料对应的虚拟条码
     */
    private String parentVirtualSn;

    /**
     * fix_bom_head主键id
     */
    private String headId;

    /**
     * 剩余需要数量
     * 在条码匹配过程中主键减少
     */
    private Integer needQty;
}
