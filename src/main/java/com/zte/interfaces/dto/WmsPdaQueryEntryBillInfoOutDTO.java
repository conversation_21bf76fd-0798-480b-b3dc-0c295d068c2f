package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class WmsPdaQueryEntryBillInfoOutDTO implements Serializable {
    private String id;
    private String boxNo;
    private String palletNo;
    private Long positionNo;
    private String scanneder;
    private Date scannedTime;
    private String billNo;
    private String status;
    private String stockNo;
}
