package com.zte.interfaces.dto.bytedance;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerImportDTO implements Serializable {

    private static final long serialVersionUID = 1871926492648290761L;

    /**
     * 属性id
     */
    private String additionalInfoId;
    /**
     * 主键id
     **/
    private String id;

    private String tableName; // 表名
    private List<CustomerImportDTO> customerImportList; // 导入的数据列表
    private String lastUpdatedBy;

    @ApiModelProperty(value = "上传文件")
    private MultipartFile file;

    // 通用属性
    @ExcelProperty(value = "客户型号")
    private String customerModel;
    @ExcelProperty(value = "客户名称")
    private String customerName; // 客户名称

    @ExcelProperty(value = "项目名称")
    private String projectName; // 项目名称

    @ExcelProperty(value = "合作模式")
    private String cooperationMode; // 合作模式

    @ExcelProperty(value = "项目阶段")
    private String projectPhase; // 项目阶段

    @ExcelProperty(value = "项目类型")
    private String projType; // 项目类型

    @ExcelProperty(value = "类型")
    private String projectType; // 类型

    @ExcelProperty(value = "ZTE代码")
    private String zteCode; // ZTE代码

    @ExcelProperty(value = "ZTE代码名称")
    private String zteCodeName; // ZTE代码名称

    @ExcelProperty(value = "客户物料型号")
    private String customerItemName; // 客户项目名称

    @ExcelProperty(value = "客户部件类型")
    private String customerComponentType; // 客户组件类型

    @ExcelProperty(value = "ZTE供应商")
    private String zteSupplier; // 客户组件类型

    @ExcelProperty(value = "ZTE规格型号")
    private String zteBrandStyle; // 客户组件类型

    // 客户部件类型校验
    private String strCustomerComponentType;

    @ExcelProperty(value = "板码类型")
    private String boardType; // 主板类型

    @ExcelProperty(value = "PN码")
    private String pnCode; // 产品编号

    // CPU信息属性
    @ExcelProperty(value = "CPU频率")
    private String cpuFrequency; // CPU频率

    @ExcelProperty(value = "CPU核数")
    private String cpuCores; // CPU核心数

    @ExcelProperty(value = "CPU线程数")
    private String cpuThreads; // CPU线程数

    @ExcelProperty(value = "CPU类型")
    private String cpuType; // CPU类型

    @ExcelProperty(value = "CPU微码版本")
    private String cpuMicrocodeVersion; // CPU微码版本

    // 内存信息属性
    @ExcelProperty(value = "容量")
    private String capacity; // 容量

    @ExcelProperty(value = "内存频率")
    private String frequency; // 频率

    @ExcelProperty(value = "内存代数")
    private String generation; // 代数

    @ExcelProperty(value = "内存等级")
    private String grade; // 等级

    // 网卡信息属性
    @ExcelProperty(value = "网卡接口")
    private String networkCardInterface; // 网卡接口

    @ExcelProperty(value = "网卡端口数量")
    private String portCount; // 端口数量

    @ExcelProperty(value = "网卡端口类型")
    private String portType; // 端口类型

    @ExcelProperty(value = "网卡芯片品牌")
    private String chipBrand; // 芯片品牌

    @ExcelProperty(value = "网卡芯片型号")
    private String chipModel; // 芯片型号

    @ExcelProperty(value = "网卡端口速率")
    private String portSpeed; // 端口速度

    // RAID卡信息属性
    @ExcelProperty(value = "RAID卡芯片品牌")
    private String raidCardBrand; // RAID卡品牌

    @ExcelProperty(value = "RAID卡芯片型号")
    private String raidCardModel; // RAID卡型号

    @ExcelProperty(value = "RAID卡接口")
    private String raidCardInterface; // RAID卡接口

    @ExcelProperty(value = "RAID卡缓存")
    private String cacheSize; // 缓存大小

    @ExcelProperty(value = "RAID卡电池")
    private String battery; // 电池

    // 硬盘信息属性
    @ExcelProperty(value = "硬盘介质")
    private String hardDiskMedia; // 硬盘介质

    @ExcelProperty(value = "硬盘尺寸")
    private String hardDiskSize; // 硬盘大小

    @ExcelProperty(value = "硬盘转速")
    private String rotationalSpeed; // 转速

    @ExcelProperty(value = "硬盘传输速率")
    private String transferRate; // 传输速率

    @ExcelProperty(value = "硬盘接口")
    private String hardDiskInterface; // 硬盘接口

    // GPU信息属性
    @ExcelProperty(value = "GPU卡显存")
    private String memory; // 显存

    @ExcelProperty(value = "GPU卡平台")
    private String platform; // 平台

    @ExcelProperty(value = "GPU卡接口")
    private String gpuInterface; // GPU接口

    // 主板信息属性
    @ExcelProperty(value = "主板版本")
    private String motherBoardVersion; // 主板版本

    // 主板信息属性
    @ExcelProperty(value = "PCB版本")
    private String pcbVersion; // 主板版本

    // VR芯片代码
    @ExcelProperty(value = "VR芯片代码")
    private String vrChipCode; // VR芯片代码

    // VR芯片功率
    @ExcelProperty(value = "VR芯片功率")
    private String vrChipPower; // VR芯片功率

    // EPLD芯片代码
    @ExcelProperty(value = "EPLD芯片代码")
    private String epldChipCode; // EPLD芯片代码

    // 机器信息属性
    @ExcelProperty(value = "服务器高度")
    private String serverHeight; // 服务器高度

    // 电源信息属性
    @ExcelProperty(value = "电源功率")
    private String powerSupply; // 电源

    // 添加校验结果字段
    @ExcelProperty(value = "校验结果")
    private String checkResult; // 校验结果

    // Getters and Setters


    public String getvrChipCode() {
        return vrChipCode;
    }

    public void setvrChipCode(String vrChipCode) {
        this.vrChipCode = vrChipCode;
    }

    public String getvrChipPower() {
        return vrChipPower;
    }

    public void setvrChipPower(String vrChipPower) {
        this.vrChipPower = vrChipPower;
    }

    public String getepldChipCode() {
        return epldChipCode;
    }

    public void setepldChipCode(String epldChipCode) {
        this.epldChipCode = epldChipCode;
    }

    public String getPcbVersion() {
        return pcbVersion;
    }

    public void setPcbVersion(String pcbVersion) {
        this.pcbVersion = pcbVersion;
    }

    public String getZteSupplier() {
        return zteSupplier;
    }

    public void setZteSupplier(String zteSupplier) {
        this.zteSupplier = zteSupplier;
    }

    public String getZteBrandStyle() {
        return zteBrandStyle;
    }

    public void setZteBrandStyle(String zteBrandStyle) {
        this.zteBrandStyle = zteBrandStyle;
    }

    public String getStrCustomerComponentType() {
        return strCustomerComponentType;
    }

    public void setStrCustomerComponentType(String strCustomerComponentType) {
        this.strCustomerComponentType = strCustomerComponentType;
    }

    public String getCustomerModel() {
        return customerModel;
    }

    public void setCustomerModel(String customerModel) {
        this.customerModel = customerModel;
    }

    public String getAdditionalInfoId() {
        return additionalInfoId;
    }

    public void setAdditionalInfoId(String additionalInfoId) {
        this.additionalInfoId = additionalInfoId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<CustomerImportDTO> getCustomerImportList() {
        return customerImportList;
    }

    public void setCustomerImportList(List<CustomerImportDTO> customerImportList) {
        this.customerImportList = customerImportList;
    }


    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCooperationMode() {
        return cooperationMode;
    }

    public void setCooperationMode(String cooperationMode) {
        this.cooperationMode = cooperationMode;
    }

    public String getProjectPhase() {
        return projectPhase;
    }

    public void setProjectPhase(String projectPhase) {
        this.projectPhase = projectPhase;
    }

    public String getProjType() {
        return projType;
    }

    public void setProjType(String projType) {
        this.projType = projType;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getZteCode() {
        return zteCode;
    }

    public void setZteCode(String zteCode) {
        this.zteCode = zteCode;
    }

    public String getZteCodeName() {
        return zteCodeName;
    }

    public void setZteCodeName(String zteCodeName) {
        this.zteCodeName = zteCodeName;
    }

    public String getCustomerItemName() {
        return customerItemName;
    }

    public void setCustomerItemName(String customerItemName) {
        this.customerItemName = customerItemName;
    }

    public String getCustomerComponentType() {
        return customerComponentType;
    }

    public void setCustomerComponentType(String customerComponentType) {
        this.customerComponentType = customerComponentType;
    }

    public String getBoardType() {
        return boardType;
    }

    public void setBoardType(String boardType) {
        this.boardType = boardType;
    }

    public String getPnCode() {
        return pnCode;
    }

    public void setPnCode(String pnCode) {
        this.pnCode = pnCode;
    }

    public String getCpuFrequency() {
        return cpuFrequency;
    }

    public void setCpuFrequency(String cpuFrequency) {
        this.cpuFrequency = cpuFrequency;
    }

    public String getCpuCores() {
        return cpuCores;
    }

    public void setCpuCores(String cpuCores) {
        this.cpuCores = cpuCores;
    }

    public String getCpuThreads() {
        return cpuThreads;
    }

    public void setCpuThreads(String cpuThreads) {
        this.cpuThreads = cpuThreads;
    }

    public String getCpuType() {
        return cpuType;
    }

    public void setCpuType(String cpuType) {
        this.cpuType = cpuType;
    }

    public String getCpuMicrocodeVersion() {
        return cpuMicrocodeVersion;
    }

    public void setCpuMicrocodeVersion(String cpuMicrocodeVersion) {
        this.cpuMicrocodeVersion = cpuMicrocodeVersion;
    }

    public String getCapacity() {
        return capacity;
    }

    public void setCapacity(String capacity) {
        this.capacity = capacity;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getGeneration() {
        return generation;
    }

    public void setGeneration(String generation) {
        this.generation = generation;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getNetworkCardInterface() {
        return networkCardInterface;
    }

    public void setNetworkCardInterface(String networkCardInterface) {
        this.networkCardInterface = networkCardInterface;
    }

    public String getPortCount() {
        return portCount;
    }

    public void setPortCount(String portCount) {
        this.portCount = portCount;
    }

    public String getPortType() {
        return portType;
    }

    public void setPortType(String portType) {
        this.portType = portType;
    }

    public String getChipBrand() {
        return chipBrand;
    }

    public void setChipBrand(String chipBrand) {
        this.chipBrand = chipBrand;
    }

    public String getChipModel() {
        return chipModel;
    }

    public void setChipModel(String chipModel) {
        this.chipModel = chipModel;
    }

    public String getPortSpeed() {
        return portSpeed;
    }

    public void setPortSpeed(String portSpeed) {
        this.portSpeed = portSpeed;
    }

    public String getRaidCardBrand() {
        return raidCardBrand;
    }

    public void setRaidCardBrand(String raidCardBrand) {
        this.raidCardBrand = raidCardBrand;
    }

    public String getRaidCardModel() {
        return raidCardModel;
    }

    public void setRaidCardModel(String raidCardModel) {
        this.raidCardModel = raidCardModel;
    }

    public String getRaidCardInterface() {
        return raidCardInterface;
    }

    public void setRaidCardInterface(String raidCardInterface) {
        this.raidCardInterface = raidCardInterface;
    }

    public String getCacheSize() {
        return cacheSize;
    }

    public void setCacheSize(String cacheSize) {
        this.cacheSize = cacheSize;
    }

    public String getBattery() {
        return battery;
    }

    public void setBattery(String battery) {
        this.battery = battery;
    }

    public String getHardDiskMedia() {
        return hardDiskMedia;
    }

    public void setHardDiskMedia(String hardDiskMedia) {
        this.hardDiskMedia = hardDiskMedia;
    }

    public String getHardDiskSize() {
        return hardDiskSize;
    }

    public void setHardDiskSize(String hardDiskSize) {
        this.hardDiskSize = hardDiskSize;
    }

    public String getRotationalSpeed() {
        return rotationalSpeed;
    }

    public void setRotationalSpeed(String rotationalSpeed) {
        this.rotationalSpeed = rotationalSpeed;
    }

    public String getTransferRate() {
        return transferRate;
    }

    public void setTransferRate(String transferRate) {
        this.transferRate = transferRate;
    }

    public String getHardDiskInterface() {
        return hardDiskInterface;
    }

    public void setHardDiskInterface(String hardDiskInterface) {
        this.hardDiskInterface = hardDiskInterface;
    }

    public String getMemory() {
        return memory;
    }

    public void setMemory(String memory) {
        this.memory = memory;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getGpuInterface() {
        return gpuInterface;
    }

    public void setGpuInterface(String gpuInterface) {
        this.gpuInterface = gpuInterface;
    }

    public String getMotherBoardVersion() {
        return motherBoardVersion;
    }

    public void setMotherBoardVersion(String motherBoardVersion) {
        this.motherBoardVersion = motherBoardVersion;
    }

    public String getServerHeight() {
        return serverHeight;
    }

    public void setServerHeight(String serverHeight) {
        this.serverHeight = serverHeight;
    }

    public String getPowerSupply() {
        return powerSupply;
    }

    public void setPowerSupply(String powerSupply) {
        this.powerSupply = powerSupply;
    }
}

