package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 ** <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class InternetCustomerQaCtrlOutDTO implements Serializable {
    private String code;
    private String qualityCode;
    private String taskno;
    private String sn;
    private String reason;
    private String customerName;
    private String pId;
}
