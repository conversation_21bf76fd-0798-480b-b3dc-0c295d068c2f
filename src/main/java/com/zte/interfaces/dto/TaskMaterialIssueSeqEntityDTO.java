package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author: 10307315
 * @Date: 2022/7/5 上午11:21
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskMaterialIssueSeqEntityDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 主键id
     */
    private BigDecimal factoryId;
    /**
     * 批次
     */
    private String prodplanId;
    /**
     * 计划跟踪单号
     */
    private String prodplanNo;
    /**
     * 物料代码
     */
    private String itemNo;
    /**
     * 物料名称
     */
    private String itemName;
    /**
     * 批属性代码
     */
    private String sysLotCode;
    /**
     * 供应商代码
     */
    private String supplerCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 顺序
     */
    private Integer seq;

    /**
     * 调整后顺序
     */
    private Integer newSeq;
    /**
     * 是否有方向性标识 Y:是,N:否,默认N
     */
    private String dirFlag;
    /**
     * 部件编带方向
     */
    private String braidDirection;
    /**
     * 包装形式
     */
    private String packType;
    /**
     * 包装规格
     */
    private String packSpec;
    /**
     * 与其他拆分物料方向标识是否相同 Y:是,N:否,默认N
     */
    private String dirFlagSame;
    /**
     * $column.comments
     */
    private BigDecimal totalCount;
    /**
     * $column.comments
     */
    private String createBy;
    /**
     * $column.comments
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
     * $column.comments
     */
    private String lastUpdatedBy;
    /**
     * $column.comments
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;
    /**
     * $column.comments
     */
    private String enabledFlag;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
    /**
     * 配送地
     */
    private String deliveryAddr;
    /**
     * 物料条码220
     */
    private String sourceBatchCode;
    /**
     * LFID
     */
    private String lfid;
    /**
     * reelId
     */
    private String reelId;

    /**
     * $column.comments
     */
    private List<String> itemNoList;
    @ApiModelProperty(value = "品牌名称")
    private String bgBrandNo;

    @ApiModelProperty(value = "料规格型号")
    private String style;

    @ApiModelProperty(value = "制造日期")
    private String productDate;

    @ApiModelProperty(value = "物料代码是否有方向性Y 是 N否")
    private String isDir;
    /**
     * empNo
     */
    private String empNo;
    /**
     * List<TaskMaterialIssueSeqEntityDTO> paramList
     */
    private List<TaskMaterialIssueSeqEntityDTO> paramList;

    private Set<String> attrList;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueSeqBeginDate;
    /**
     *编带方向的数字
     */
    private int intBraidDirection;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public String getProdplanId() {
        return prodplanId;
    }

    public void setProdplanId(String prodplanId) {
        this.prodplanId = prodplanId;
    }

    public String getProdplanNo() {
        return prodplanNo;
    }

    public void setProdplanNo(String prodplanNo) {
        this.prodplanNo = prodplanNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSysLotCode() {
        return sysLotCode;
    }

    public void setSysLotCode(String sysLotCode) {
        this.sysLotCode = sysLotCode;
    }

    public String getSupplerCode() {
        return supplerCode;
    }

    public void setSupplerCode(String supplerCode) {
        this.supplerCode = supplerCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Integer getNewSeq() {
        return newSeq;
    }

    public void setNewSeq(Integer newSeq) {
        this.newSeq = newSeq;
    }

    public String getDirFlag() {
        return dirFlag;
    }

    public void setDirFlag(String dirFlag) {
        this.dirFlag = dirFlag;
    }

    public String getBraidDirection() {
        return braidDirection;
    }

    public void setBraidDirection(String braidDirection) {
        this.braidDirection = braidDirection;
    }

    public String getPackType() {
        return packType;
    }

    public void setPackType(String packType) {
        this.packType = packType;
    }

    public String getPackSpec() {
        return packSpec;
    }

    public void setPackSpec(String packSpec) {
        this.packSpec = packSpec;
    }

    public String getDirFlagSame() {
        return dirFlagSame;
    }

    public void setDirFlagSame(String dirFlagSame) {
        this.dirFlagSame = dirFlagSame;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDeliveryAddr() {
        return deliveryAddr;
    }

    public void setDeliveryAddr(String deliveryAddr) {
        this.deliveryAddr = deliveryAddr;
    }

    public String getSourceBatchCode() {
        return sourceBatchCode;
    }

    public void setSourceBatchCode(String sourceBatchCode) {
        this.sourceBatchCode = sourceBatchCode;
    }

    public String getLfid() {
        return lfid;
    }

    public void setLfid(String lfid) {
        this.lfid = lfid;
    }

    public String getReelId() {
        return reelId;
    }

    public void setReelId(String reelId) {
        this.reelId = reelId;
    }

    public List<String> getItemNoList() {
        return itemNoList;
    }

    public void setItemNoList(List<String> itemNoList) {
        this.itemNoList = itemNoList;
    }

    public String getBgBrandNo() {
        return bgBrandNo;
    }

    public void setBgBrandNo(String bgBrandNo) {
        this.bgBrandNo = bgBrandNo;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getProductDate() {
        return productDate;
    }

    public void setProductDate(String productDate) {
        this.productDate = productDate;
    }

    public String getIsDir() {
        return isDir;
    }

    public void setIsDir(String isDir) {
        this.isDir = isDir;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public List<TaskMaterialIssueSeqEntityDTO> getParamList() {
        return paramList;
    }

    public void setParamList(List<TaskMaterialIssueSeqEntityDTO> paramList) {
        this.paramList = paramList;
    }

    public Set<String> getAttrList() {
        return attrList;
    }

    public void setAttrList(Set<String> attrList) {
        this.attrList = attrList;
    }

    public Date getIssueSeqBeginDate() {
        return issueSeqBeginDate;
    }

    public void setIssueSeqBeginDate(Date issueSeqBeginDate) {
        this.issueSeqBeginDate = issueSeqBeginDate;
    }

    public int getIntBraidDirection() {
        return intBraidDirection;
    }

    public void setIntBraidDirection(int intBraidDirection) {
        this.intBraidDirection = intBraidDirection;
    }
}
