package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 混合云库存明细表PageQuery
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-11 16:01:46
 */
@ApiModel
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HybridCloudInventoryDetailPageQueryDTO extends PageDTO {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String id;
    /**
     * 库存日期
     */
    @ApiModelProperty(value = "库存日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date inventoryDate;
    /**
     * 三段码
     */
    @ApiModelProperty(value = "三段码")
    private String configModel;
    /**
     * fixbomID
     */
    @ApiModelProperty(value = "fixbomID")
    private String mpn;
    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer inventoryQty;
    /**
     * 厂家收到PO已发货量
     */
    @ApiModelProperty(value = "厂家收到PO已发货量")
    private Integer deliveredQuantity;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String lastUpdatedBy;
    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    private Date lastUpdatedDate;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private String enabledFlag;

}

