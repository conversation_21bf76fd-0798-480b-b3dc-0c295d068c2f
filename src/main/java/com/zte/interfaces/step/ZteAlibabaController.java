package com.zte.interfaces.step;

import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.application.infor.TransferBoxService;
import com.zte.application.step.AliInventorySyncService;
import com.zte.application.step.ZteAlibabaService;
import com.zte.application.step.ZteAlibabaStockInfoUploadService;
import com.zte.application.step.ZtePkgIdBoundSnService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.step.dto.*;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ServiceDataUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@ZTERestController("/zteAlibaba")
@Api(tags = "ZTE-Alibaba对接")
public class ZteAlibabaController {

    @Autowired
    private ZteAlibabaService zteAlibabaService;

    @Autowired
    private AliInventorySyncService aliInventorySyncService;

    @Autowired
    private ZteAlibabaStockInfoUploadService zteAlibabaStockInfoUploadService;

    @Autowired
    private ZtePkgIdBoundSnService ztePkgIdBoundSnService;

    @Autowired
    private TransferBoxService transferBoxService;

    @ApiOperation("部件出⼊库计划申请&执⾏&核销")
    @PostMapping("/deductionPlan")
    public void deductionPlan(HttpServletRequest request, @RequestBody ZteDeductionPlanParamDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setEmpNo(xEmpNo);
        zteAlibabaService.deductionPlan(dto);
    }

    @ApiOperation("INFOR出入库信息定时推送阿里")
    @PostMapping("/excutedUploadJob")
    public void excutedUploadJob(HttpServletRequest request, @RequestBody ZteDeductionPlanParamDTO dto){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setEmpNo(xEmpNo);
        zteAlibabaService.excutedUploadJob(dto);
    }

    @ApiOperation("通知出⼊库审批结果")
    @PostMapping("/noticeApproveResult")
    public void noticeApproveResult(HttpServletRequest request, @RequestBody List<ZteApproveResultDTO> list){
        zteAlibabaService.noticeApproveResult(list);
    }

    @ApiOperation("接收阿里的返回消息")
    @PostMapping("/zteB2BReturnInfo")
    public void zteB2BReturnInfo(HttpServletRequest request, @RequestBody B2BCallBackDTO dto){
        zteAlibabaService.zteB2BReturnInfo(dto);
    }

    @ApiOperation("部件创建调拨单接口")
    @PostMapping("/executedCreateBIllJob")
    public void executedCreateBIllJob(HttpServletRequest request,@RequestBody List<IscpEdiLog> iscpEdiLogs){
        zteAlibabaService.doExecutedCreateMoveBill(iscpEdiLogs);
    }

    @ApiOperation("定时查询阿里库存现有量")
    @PostMapping("/executeQueryInventoryJob")
    public void executeStockJob(HttpServletRequest request,@RequestBody ZteAlibabaInventoryParamDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setEmpNo(xEmpNo);
        zteAlibabaService.executeQueryInventoryJob(dto);
    }

    @ApiOperation("部件箱包&SN库存定时推送阿里")
    @PostMapping("/executeSnInventoryUploadJob")
    public void executeSnInventoryUploadJob(HttpServletRequest request, @RequestBody ZteDeductionPlanParamDTO dto) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setEmpNo(xEmpNo);
        if (dto.getLastBatchFlag().equals(Constant.INT_1)) {
            zteAlibabaService.executeSnInventoryUploadLastBatchJob(dto);
        } else {
            zteAlibabaService.executeSnInventoryUploadJob(dto);
        }
    }
    @ApiOperation("工单扣料信息接口")
    @PostMapping("/excutedDistributeMaterial")
    public void excutedDistributeMaterial(HttpServletRequest request,@RequestBody List<IscpEdiLog> iscp){
        zteAlibabaService.doExcutedDistributeMaterial(iscp);
    }

    @ApiOperation("阿里查询领料单")
    @ResponseBody
    @PostMapping(value = "/getPicklistMain")
    public ServiceData<?> getPicklistMain(HttpServletRequest request,
                                          @RequestBody ZmsPicklistMainInDTO zmsPicklistMainInDTO) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        return ServiceDataUtil.getSuccess(zteAlibabaService.selectPicklistMain(zmsPicklistMainInDTO));
    }

    @ApiOperation("库存差异反馈定时推送阿里（补偿）")
    @PostMapping("/executedInventoryDiffFeedbackJob")
    public void executedInventoryDiffFeedbackJob(HttpServletRequest request, @RequestBody InventoryDiffFeedbackJobDTO jobDTO){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        jobDTO.setEmpNo(xEmpNo);
        zteAlibabaService.executedInventoryDiffFeedbackJob(jobDTO);
    }

    @ApiOperation("定时同步阿里库存交易")
    @ResponseBody
    @GetMapping(value = "/syncAliInventory")
    public void syncAliInventory(HttpServletRequest request) {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        aliInventorySyncService.syncAliInventoryTransData(xEmpNo);
    }

    @ApiOperation("库存定时推送阿里")
    @GetMapping("/aLiStockInfoUpload")
    public void aLiStockInfoUpload(HttpServletRequest request) throws Exception {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        zteAlibabaStockInfoUploadService.aliStockInfoUpload(xEmpNo);
    }

    @ApiOperation("库存定时推送阿里（补偿）")
    @PostMapping("/aLiStockInfoUploadManual")
    public void aLiStockInfoUploadManual(HttpServletRequest request, @RequestBody CustomerInventoryLinesDTO dto) throws Exception {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        dto.setEmpNo(xEmpNo);
        zteAlibabaStockInfoUploadService.aliStockInfoUploadManual(dto);
    }

    @ApiOperation("混箱箱包与SN绑定定时推送阿里")
    @GetMapping("/mixedBoxPkgIdBoundSnJob")
    public void mixedBoxPkgIdBoundSnJob(HttpServletRequest request){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        ztePkgIdBoundSnService.excuteUploadMixedBoxPkgIdBoundSnJob(xEmpNo);
    }

    @ApiOperation("原箱箱包与SN绑定定时推送阿里")
    @GetMapping("/originBoxPkgIdBoundSnJob")
    public void originBoxPkgIdBoundSnJob(HttpServletRequest request){
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        ztePkgIdBoundSnService.excuteModifyOriginBoxPkgIdBoundSnJob(xEmpNo);
    }

    @ApiOperation("定时生成原箱转混箱任务")
    @PostMapping(value = "/syncTransferBoxData")
    public void syncTransferBoxData() {
        transferBoxService.syncTransferBoxDataJob();
    }

    @ApiOperation("原箱转混箱定时推送阿里")
    @PostMapping(value = "/processTransfersDataJob")
    public void processTransfersDataJob() {
        transferBoxService.processTransfersDataJob();
    }


    @ApiOperation("INFOR出库数据调用标准仓入库，同时数据反馈wms")
    @GetMapping(value = "/processTransToStorageWmsJob")
    public void processTransToWmsJob(HttpServletRequest request) throws Exception {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        String xAuthValue = request.getHeader(Constant.X_AUTH_VALUE);
        zteAlibabaService.executeTransferInfoToWmsJob(xEmpNo, xAuthValue);
    }

    @ApiOperation("INFOR出库数据调用标准仓入库，同时数据反馈wms")
    @GetMapping(value = "/queryInventoryFromStorageCenter")
    public void queryInventoryFromStorageCenter(HttpServletRequest request) throws Exception {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        String xAuthValue = request.getHeader(Constant.X_AUTH_VALUE);
        zteAlibabaService.queryInventoryFromStorageCenter(xEmpNo);
    }
}
