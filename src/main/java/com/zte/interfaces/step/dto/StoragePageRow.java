package com.zte.interfaces.step.dto;

import com.zte.itp.msa.core.model.PageRows;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页对象
 *<AUTHOR>
 *@date 2025/08/13 16:46
 */
public class StoragePageRow <T>  extends PageRows {
    private int pages;
    private int size;
    private List<T> records = new ArrayList<>();

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }
}
