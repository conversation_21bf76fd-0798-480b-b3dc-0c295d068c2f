package com.zte.interfaces.step.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@ApiModel(description = "库存查询-入参")
public class InventoryQueryReqDTO {

    @ApiModelProperty(value = "库存类型：生产材料库固定0；在线不良品和维修不良品库固定5", required = true, example = "0")
    @NotBlank(message = "库存类型不能为空")
    private String inventoryType;

    @ApiModelProperty(value = "SN条码列表", required = false, example = "[\"741196400395\"]")
    private List<String> itemBarcodes;

    @ApiModelProperty(value = "分页页码，默认1", required = false, example = "1")
    @Builder.Default
    private Integer pageIndex = 1;

    @ApiModelProperty(value = "分页大小，默认500，最大1000", required = false, example = "500")
    @Builder.Default
    private Integer pageSize = 500;
}

