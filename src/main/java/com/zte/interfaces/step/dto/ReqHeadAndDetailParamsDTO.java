package com.zte.interfaces.step.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@ApiModel(description = "请求头和明细入参封装")
public class ReqHeadAndDetailParamsDTO {

    @ApiModelProperty(value = "单据头", required = true)
    @NotNull(message = "单据头不能为空")
    private ReqHeadInstanceDTO billHead;

    @ApiModelProperty(value = "单据明细列表", required = true)
    @NotNull(message = "单据明细不能为空")
    private List<ReqDetailInstanceDTO> billDetail;

    @ApiModelProperty(value = "是否需要自动装箱", example = "true")
    private Boolean needAutoPack;

    @ApiModelProperty(value = "是否需要自动登出LPN", example = "false")
    private Boolean needAutoLogoutLpn;

    public ReqHeadAndDetailParamsDTO(ReqHeadInstanceDTO requestHeader) {
    }
}

