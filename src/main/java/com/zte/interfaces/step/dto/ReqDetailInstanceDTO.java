package com.zte.interfaces.step.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.interfaces.infor.dto.AliOrderDeductionBillDTO;
import com.zte.interfaces.infor.dto.AliOrderSubmitDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.util.Date;

import static com.zte.common.utils.Constant.FLAG_Y;
import static com.zte.common.utils.Constant.SUBMIT_BILL_STATUS_0030;
import static com.zte.common.utils.NumConstant.STR_101;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@ApiModel(description = "阿里入库单-单据明细")
public class ReqDetailInstanceDTO {

    @ApiModelProperty(value = "库存类型 ('pallet', 'box', 'item', 'barcode')", required = true, example = "item")
    @NotBlank(message = "库存类型不能为空")
    private String stockTypeCode;

    @ApiModelProperty(value = "库存单元编码 (0-pallet, 1-box, 2-item, 3-barcode)", required = true, example = "2")
    @NotBlank(message = "库存单元编码不能为空")
    private String stockType;

    @ApiModelProperty(value = "托盘号", required = false)
    private String palletNo;

    @ApiModelProperty(value = "箱号", required = false)
    private String boxNo;

    @ApiModelProperty(value = "LPN类型 10-纸箱, 20-托盘", required = false, example = "10")
    private String lpnType;

    @ApiModelProperty(value = "物料代码", required = true)
    @NotBlank(message = "物料代码不能为空")
    private String itemNo;

    @ApiModelProperty(value = "物料条码", required = false)
    private String itemBarcode;

    @ApiModelProperty(value = "条码类型 0-序列码,1-REELID，2-批次码", required = false, example = "0")
    private Integer barcodeType;

    @ApiModelProperty(value = "需求明细行号", required = true, example = "1")
    @NotNull(message = "需求明细行号不能为空")
    private Integer lineIndex;

    @ApiModelProperty(value = "需求数量", required = true, example = "10.00")
    @NotNull(message = "需求数量不能为空")
    @PositiveOrZero(message = "需求数量必须大于等于0")
    private Integer reqQty;

    @ApiModelProperty(value = "明细状态 执行中0030/已执行0040", required = true, example = "0030")
    @NotBlank(message = "明细状态不能为空")
    @Default
    private String stateCode = "0030";

    @ApiModelProperty(value = "创建人(短工号)", required = false)
    private String createdBy;

    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss", required = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty(value = "更新人(短工号)", required = false)
    private String lastUpdatedBy;

    @ApiModelProperty(value = "更新时间 yyyy-MM-dd HH:mm:ss", required = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "接入业务系统标示", required = true, example = "101")
    @NotNull(message = "接入业务系统标示不能为空")
    @Default
    private Integer accessSystem = 101;

    @ApiModelProperty(value = "有效标示", required = true, example = "Y")
    @NotBlank(message = "有效标示不能为空")
    @Default
    private String enableFlag = "Y";

    public ReqDetailInstanceDTO(AliOrderSubmitDetailDTO detail, String userId) {
        this.setStockTypeCode(detail.getStockTypeCode());
        this.setStockType(detail.getStockType());
        this.setBoxNo(detail.getBoxNo());
        this.setLpnType(detail.getLpnType());
        this.setItemNo(detail.getItemCode());
        this.setItemBarcode(detail.getBarcode());
        this.setBarcodeType(0);
        this.setReqQty(detail.getQty());
        this.setStateCode(SUBMIT_BILL_STATUS_0030);
        this.setCreatedBy(userId);
        this.setCreatedDate(new Date());
        this.setLastUpdatedBy(userId);
        this.setLastUpdatedDate(new Date());
        this.setAccessSystem(Integer.valueOf(STR_101));
        this.setEnableFlag(FLAG_Y);
    }
}

