/**
 * 项目名称 : code-make2
 * 创建日期 : 2019-05-22
 * 修改历史 :
 *   1. [2019-05-22] 创建文件 by 10240788
 **/
package com.zte.interfaces;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.zte.application.ErpProjectPlangroupService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ErpProjectPlangroup;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * ERP计划组产品大类
 * 
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/erpProjectPlangroup")
@Api("ERP计划组产品大类API")
public class ErpProjectPlangroupController {

    @Autowired
    private ErpProjectPlangroupService erpProjectPlangroupService;

    @ApiOperation("查询ERP计划组列表信息")
    @ApiResponses({ @ApiResponse(code = Constant.FOUR_ZERO_ZERO, message = "请求参数没填好"),
        @ApiResponse(code = Constant.FOUR_ZERO_FOUR, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/getPlanGroupList", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData<?> getPlanGroupList(HttpServletRequest request) throws Exception {
        
        ServiceData<List<ErpProjectPlangroup>> ret = new ServiceData<List<ErpProjectPlangroup>>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        ret.setCode(retCode);
        
        List<ErpProjectPlangroup> dataList = erpProjectPlangroupService.selectPlanGroupList();
        ret.setBo(dataList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }
    
    @ApiOperation("查询ERP产品大类列表信息")
    @ApiResponses({ @ApiResponse(code = Constant.FOUR_ZERO_ZERO, message = "请求参数没填好"),
        @ApiResponse(code = Constant.FOUR_ZERO_FOUR, message = "请求路径没有或页面跳转路径不对") })
    @RequestMapping(value = "/getProjectNameList", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceData<?> getProjectNameList(HttpServletRequest request) throws Exception {
        
        ServiceData<List<ErpProjectPlangroup>> ret = new ServiceData<List<ErpProjectPlangroup>>();
        RetCode retCode = new RetCode(RetCode.BUSINESSERROR_CODE,RetCode.BUSINESSERROR_MSGID);
        ret.setCode(retCode);
        
        List<ErpProjectPlangroup> dataList = erpProjectPlangroupService.selectProjectNameList();
        ret.setBo(dataList);
        ret.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        return ret;
    }
}