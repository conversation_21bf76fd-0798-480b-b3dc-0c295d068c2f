<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.BaImuRepository">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.BaImuDTO" id="baImuMap">
        <result property="imuId" column="IMU_ID" jdbcType="INTEGER"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="workshopId" column="WORKSHOP_ID" jdbcType="INTEGER"/>
        <result property="bimuId" column="BIMU_ID" jdbcType="INTEGER"/>
        <result property="colName" column="COL_NAME" jdbcType="VARCHAR"/>
        <result property="lineName" column="LINE_NAME" jdbcType="VARCHAR"/>
        <result property="enableFlag" column="ENABLE_FLAG" jdbcType="VARCHAR"/>
        <result property="lastUpdateDate" column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        IMU_ID,
        NAME,
        WORKSHOP_ID,
        BIMU_ID,
        COL_NAME,
        LINE_NAME,
        ENABLE_FLAG,
        LAST_UPDATE_DATE
    </sql>

    <select id="getListByImuIdList" parameterType="java.util.List" resultMap="baImuMap">
        select
        <include refid="Base_Column_List"/>
        from kxbariii.BA_IMU where ENABLE_FLAG = 'Y' and imu_id in
        <foreach item="imuId" index="index" collection="list" open="(" separator="," close=")">
            #{imuId}
        </foreach>

    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            insert into kxbariii.BA_IMU (
            IMU_ID,
            NAME,
            WORKSHOP_ID,
            BIMU_ID,
            COL_NAME,
            LINE_NAME,
            ENABLE_FLAG,
            LAST_UPDATE_DATE)
            values (
            #{item.imuId,jdbcType=INTEGER},
            #{item.name,jdbcType=VARCHAR},
            #{item.workshopId,jdbcType=INTEGER},
            #{item.bimuId,jdbcType=INTEGER},
            #{item.colName,jdbcType=VARCHAR},
            #{item.lineName,jdbcType=VARCHAR},
            'Y',
            sysdate)
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            update kxbariii.BA_IMU t set
            t.NAME=#{item.name,jdbcType=VARCHAR},
            t.WORKSHOP_ID=#{item.workshopId,jdbcType=INTEGER},
            t.BIMU_ID=#{item.bimuId,jdbcType=INTEGER},
            t.COL_NAME=#{item.colName,jdbcType=VARCHAR},
            t.LINE_NAME=#{item.lineName,jdbcType=VARCHAR},
            t.LAST_UPDATE_DATE= sysdate
            where ENABLE_FLAG = 'Y'
            and IMU_ID = #{item.imuId,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>