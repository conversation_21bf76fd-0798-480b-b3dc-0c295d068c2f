<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.QaKxonlineBomlockedRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO" id="qaKxonlineBomlockedMap">
        <result property="bomlockid" column="BOMLOCKID" />
        <result property="badsheetno" column="BADSHEETNO" />
        <result property="problemdesc" column="PROBLEMDESC" />
        <result property="sender" column="SENDER" />
        <result property="bomid" column="BOMID" />
        <collection property="detailList" ofType="com.zte.interfaces.dto.QaKxonlineImulockdetailEntityDTO">
            <result property="imulockdetailid" column="IMULOCKDETAILID" />
            <result property="bomlockid" column="DETAIL_BOMLOCKID" />
            <result property="imuid" column="IMUID" />
            <result property="imuname" column="IMUNAME" />
            <result property="locker" column="LOCKER" />
            <result property="locktime" column="LOCKTIME" />
            <result property="state" column="STATE" />
            <result property="unlocker" column="UNLOCKER" />
            <result property="unlocktime" column="UNLOCKTIME" />
            <result property="unlockremark" column="UNLOCKREMARK" />
            <result property="imuqty" column="IMUQTY" />
        </collection>
    </resultMap>

    <select id="pageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="qaKxonlineBomlockedMap">
      select
        h.BOMLOCKID,
        h.BADSHEETNO,
        h.PROBLEMDESC,
        h.SENDER,
        h.BOMID,
        d.IMULOCKDETAILID,
        d.BOMLOCKID DETAIL_BOMLOCKID,
        d.IMUID,
        d.IMUNAME,
        d.LOCKER,
        d.LOCKTIME,
        d.STATE,
        d.UNLOCKER,
        d.UNLOCKTIME,
        d.UNLOCKREMARK,
        d.IMUQTY
      from kxstepiii.QA_KXONLINE_BOMLOCKED h left join kxstepiii.QA_KXONLINE_IMULOCKDETAIL d on h.BOMLOCKID = d.BOMLOCKID where 1=1
        <if test="params != null and params.notState != null and params.notState != ''">and d.state != #{params.notState}</if>
        order by d.LOCKTIME asc,d.IMULOCKDETAILID asc
    </select>

</mapper>