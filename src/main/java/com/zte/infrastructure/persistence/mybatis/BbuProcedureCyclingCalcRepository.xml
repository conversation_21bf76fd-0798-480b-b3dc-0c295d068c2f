<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.BbuProcedureCyclingCalcRepository">

    <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.BbuProcedureCyclingCalc">
        <result column="entityName" jdbcType="VARCHAR" property="entityName" />

    </resultMap>
    <resultMap id="BaseResultMapNew" type="com.zte.domain.model.datawb.BbuProcedureCyclingCalc">
        <result column="entityName" jdbcType="VARCHAR" property="entityName" />

    </resultMap>
    <sql id="Base_Column_List">
        ENTITY_NAME
    </sql>

    <resultMap id="FinalCalcResult" type="com.zte.domain.model.datawb.BbuProcedureCyclingQuery">
        <result column="entityName" jdbcType="VARCHAR" property="entityName" />
        <result column="pkgRealBeginTime" jdbcType="DATE" property="pkgRealBeginTime" />
        <result column="testRealBeginTime" jdbcType="DATE" property="testRealBeginTime" />
        <result column="testCycling" jdbcType="DECIMAL" property="testCycling" />
        <result column="taskFirstAccountTime" jdbcType="DATE" property="taskFirstAccountTime" />
        <result column="assemblyCycling" jdbcType="DECIMAL" property="assemblyCycling" />
        <result column="totalCycling" jdbcType="DECIMAL" property="totalCycling" />
        <result column="insertTime" jdbcType="DATE" property="insertTime" />

    </resultMap>

    <select id="selectEntityName"  resultMap="BaseResultMap">
        select ENTITY_NAME entityName from ENTITYTRACE_QUERY_V t where t.S30_DATE > trunc(sysdate-7) and trunc(sysdate)>t.S30_DATE
        and PLANNING_GROUP_DESC in ('5G','FDD-SYS','Microwave Product','TD-SCDMA','TS') and ENTITY_NAME like '%-M%'
    </select>
    <select id="selectEntityNameAgain"  resultMap="BaseResultMapNew" parameterType="java.util.List">
        select distinct t.任务号 entityName from WMES.CODE_DETAIL_QUERY_V t  where t.物料代码 in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>

    </select>

    <insert id="insertCyclingResult"   parameterType="java.util.List">
        insert into WMES.BBUPROCEDURECYCLINGCALC(任务号,包装实际开始时间,调试实际开始时间,调测生产周期,任务首次记账时间,装配生产周期,生产周期) select R.* from (select k.*,k.装配生产周期+k.调测生产周期 as 生产周期  from (select p.ENTITY_NAME 任务号,p.包装实际开始时间,p.调试实际开始时间,p.调测生产周期,q.任务首次记账时间,ROUND(p.调试实际开始时间-q.任务首次记账时间,2) 装配生产周期 from (select A.ENTITY_NAME ENTITY_NAME,S.PACK_REAL_BEGIN_DATE 包装实际开始时间,S.DEBUG_REAL_BEGIN_DATE 调试实际开始时间 ,ROUND(S.PACK_REAL_BEGIN_DATE-S.DEBUG_REAL_BEGIN_DATE,2) 调测生产周期 from wmes.CPM_CONTRACT_ENTITIES A,wmes.WERP_CON_ENTITY_TRACE S where A.ENTITY_ID=S.ENTITY_ID ) p full join (select t.entity_name,t.FIRST_ACCOUNT_DATE 任务首次记账时间 from proc_contractentity_v t) q
        on p.ENTITY_NAME=q.entity_name) k  where k.任务号 in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item.ENTITY_NAME,jdbcType=VARCHAR}
        </foreach>
        )R where R.包装实际开始时间 is not null and R.调试实际开始时间 is not null and R.任务首次记账时间 is not null and R.生产周期>0 and 90>R.生产周期
    </insert>

    <select id="selectFinalResult" parameterType="java.lang.String"  resultMap="FinalCalcResult">
        select t.任务号 entityName,t.包装实际开始时间 pkgRealBeginTime,t.调试实际开始时间 testRealBeginTime,t.调测生产周期 testCycling,t.任务首次记账时间 taskFirstAccountTime,t.装配生产周期 assemblyCycling,t.生产周期 totalCycling,t.插入时间 insertTime  from bbuprocedurecyclingcalc t where t.插入时间> to_date(#{beginTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss') and to_date(#{endTime,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')>t.插入时间 order by ID
    </select>


    <delete id="deleteOldData">
        delete from BBUPROCEDURECYCLINGCALC t where t.插入时间  &lt; sysdate-30
    </delete>

</mapper>
