<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.ConfigurationMaterialRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.MfgSiteDTO">
        <id column="contract_No" jdbcType="VARCHAR" property="contractNo"/>
        <result column="entity_Name" jdbcType="VARCHAR" property="entityName"/>
        <result column="entity_ID" jdbcType="VARCHAR" property="entityID"/>
        <result column="entity_SpecialName" jdbcType="VARCHAR" property="entitySpecialName"/>
        <result column="shipment_Method" jdbcType="VARCHAR" property="shipmentMethod"/>
        <result column="site_Code" jdbcType="VARCHAR" property="siteCode"/>
        <result column="site_Address" jdbcType="VARCHAR" property="siteAddress"/>
        <result column="mfg_Site_Code" jdbcType="VARCHAR" property="mfgSiteCode"/>
        <result column="mfg_Site_Equipment" jdbcType="VARCHAR" property="mfgSiteEquipment"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="mfg_Site_ID" jdbcType="VARCHAR" property="mfgSiteID"/>
        <result column="Site_ID" jdbcType="VARCHAR" property="siteID"/>
        <result column="contract_Line_ID" jdbcType="VARCHAR" property="contractLineID"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="deliverSet" jdbcType="VARCHAR" property="deliverSet"/>
    </resultMap>

    <select id="getConfigurationBasicInfoByEntityNames" parameterType="com.zte.interfaces.dto.MfgSiteDTO"
            resultMap="BaseResultMap">
        <if test="currentPage > 0 and pageSize > 0">
            select * from ( select temA.*, ROWNUM R
            from (
        </if>
        SELECT CCH.CONTRACT_NUMBER as contract_No,
        A.ENTITY_NAME,
        A.ENTITY_ID,
        deliverSet.DELIVER_SET_NUM as deliverSet,
        (SELECT nvl(SP.special_name, '')
        FROM WMES.CONTRACT_INFO_SPECIAL SP
        WHERE SP.special_name IN ('无铅','有铅','ROHS','HSF','HSF-S','HSF-S2','HSF-S3','HSF-S4')
        and SP.CONTRACT_NUMBER = cch.contract_number
        and rownum = 1) as entity_SpecialName,
        decode(nvl(wmes.Find_deliver_Shipment_Method(deliverSet.DELIVER_SET_ID),
        '0'),'0',wmes.Find_Contract_Shipment_Method(ccl.Contract_Header_id),wmes.Find_deliver_Shipment_Method(deliverSet.DELIVER_SET_ID))
        as shipment_Method,
        ccs.site_code, CCS.SITE_ADDRESS,
        ccms.mfg_site_code,
        ccms.mfg_site_equipment,
        ccms.memo,
        ccms.mfg_site_id,
        ccs.site_id,
        a.contract_line_id,
        ccs.district,
        decode(ccms.is_pdvm_flag,'Y','是','否') is_pdvm_flag,
        A.CMS_PDVM_NAME,
        case when (select count(*) from WMES.cpm_contract_mfg_sites CCMS
        where CCMS.ENTITY_ID = A.ENTITY_ID
        and CCMS.ENABLED_FLAG='Y'
        and CCMS.eu_code is not null) != 0 then 'EU'
        when dicDtValue.Description is not null  then dicDtValue.Description
        when upper(A.MFG_SITE_TYPE) = 'C' then '软件'
        when upper(A.MFG_SITE_TYPE) = 'ZC' then '否'
        when upper(ccl.BOQ_FLAG) = 'Y' then '是'
        else '否' end BOQ_FLAG
        FROM wmes.CPM_CONTRACT_ENTITIES A,
        wmes.CPM_CONTRACT_MFG_SITES ccms,
        WMES.CDM_CONTRACT_LINES ccl,
        WMES.CDM_CONTRACT_HEADERS CCH,
        wmes.cpm_contract_sites ccs,
        WMES.CPM_INNER_ORDER_HEADERS orderHead,
        WMES.CDM_DELIVER_SETS deliverSet,
        WMES.BAS_LOOKUP_VALUES_VL BLV,
        apps.SYS_LOOKUP_VALUES dicDtValue
        WHERE a.entity_id = ccms.entity_id
        and a.entity_name in
        <foreach collection="entityNameList" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and ccms.site_id = ccs.site_id
        and A.contract_line_id = ccl.contract_line_id
        and a.INNER_ORDER_HEADER_ID = orderHead.INNER_ORDER_HEADER_ID(+)
        and cch.contract_header_id = ccl.contract_header_id
        and a.ENABLED_FLAG = 'Y'
        and orderHead.DELIVER_SET_ID = deliverSet.DELIVER_SET_ID(+)
        and deliverSet.ENABLED_FLAG(+) = 'Y'
        and orderHead.ENABLED_FLAG(+) = 'Y'
        AND BLV.enabled_flag(+) = 'Y'
        AND A.Mfg_Site_Type=BLV.lookup_code(+)
        AND BLV.LOOKUP_TYPE(+) = 'CDM_EQUIPMENT_TYPE'
        and dicDtValue.Lookup_Type(+)=8240022
        and BLV.meaning =dicDtValue.Lookup_Meaning(+)
        <if test="currentPage > 0 and pageSize > 0">
            ) temA where ROWNUM &lt;= #{currentPage,jdbcType=BIGINT} * #{pageSize,jdbcType=BIGINT} ) temB
        where R &gt; (#{currentPage,jdbcType=BIGINT} - 1) * #{pageSize,jdbcType=BIGINT}
        </if>
    </select>

</mapper>