<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewRepository">

    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="querySubItemByProcedule">
        <result property="attribute" jdbcType="VARCHAR" column="ATTRIBUTE1"/>
        <result property="nextFlag" jdbcType="VARCHAR" column="next_flag"/>
    </resultMap>

    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="queryDQASConfigAsamAddr">
        <result property="meaning" jdbcType="VARCHAR" column="meaning"/>
        <result property="sectionName" jdbcType="VARCHAR" column="sectionName"/>
    </resultMap>


    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="querycheckPrefix">
        <result property="qty" jdbcType="BIGINT" column="QTY"/>
    </resultMap>


    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="querySubBarByMainBujianZuzhuang">
        <result property="subBarcode" jdbcType="VARCHAR" column="SUB_BARCODE"/>
        <result property="itemCode" jdbcType="VARCHAR" column="ITEM_CODE"/>
    </resultMap>


    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="queryDeptByEntityNo">
        <result property="attribute" jdbcType="VARCHAR" column="ATTRIBUTE1"/>
    </resultMap>


    <select id="getSubItemByProcedule" parameterType="Map" resultMap="querySubItemByProcedule">

        SELECT d.ATTRIBUTE1,d.next_flag
        FROM SFC.DQAS_SAVE_ITEM_STATION d
        WHERE d.ENABLED_FLAG = 'Y'
        and d.control_flag = 'Y'

        <if test="itemCode != null">
            AND d.ITEM_CODE = #{itemCode,jdbcType=VARCHAR}
        </if>
        <if test="controlFunc != null">
            AND d.ControlFunc = #{controlFunc,jdbcType=VARCHAR}
        </if>
        <if test="procedure != null">
            AND ( d.procedule =#{procedure,jdbcType=VARCHAR} or d.procedule ='全部')
        </if>
        and d.attribute1 in
        (SELECT t.ATTRIBUTE1
        FROM SFC.DQAS_SAVE_ENTITY_STATION t
        WHERE t.ENABLED_FLAG = 'Y'
        and t.control_flag = 'Y'
        AND t.ENTITY_PREFIX = SUBSTR(#{entityNo,jdbcType=VARCHAR}, 1, LENGTH(t.ENTITY_PREFIX)))
    </select>


    <select id="getDQASConfigAsamAddr" parameterType="Map" resultMap="queryDQASConfigAsamAddr">
        select ccode, meaning, section_name sectionName
        from sfc.sys_baseinfo_lines
        where bi_header_id in (select bi_header_id
        from sfc.sys_baseinfo_headers
        where
        bi_code = #{biCode,jdbcType=VARCHAR} )
        <if test="cCode != null">
            AND ccode = #{cCode,jdbcType=VARCHAR}
        </if>

    </select>


    <insert id="insertDqasInfoNew" parameterType="java.util.Map">
        <selectKey keyProperty="RecordId" order="BEFORE" resultType="java.lang.Long">
            SELECT SFC.JOBS_BOX_SCAN_DQAS_CONTROL_S.NEXTVAL AS value FROM DUAL
        </selectKey>
        INSERT INTO SFC.JOBS_DQAS_CONTROL_INFO
        (RECORD_ID,
        BILL_NUMBER,
        ITEM_BARCODE,

        SCAN_DATE,
        SCAN_TIME,
        ENTITY_NAME,
        CONTRACT_NAME,
        ITEM_CODE,
        PROCEDULE,
        IS_SUCCESS,
        FAIL_REASON,
        ControlFunc)

        VALUES
        (#{RecordId,jdbcType=BIGINT},
        #{billNumber,jdbcType=VARCHAR},
        #{barcode,jdbcType=VARCHAR},

        SYSDATE,
        #{scanTime,jdbcType=DECIMAL},
        #{entityNo,jdbcType=VARCHAR},
        #{contractNo,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{procedure,jdbcType=VARCHAR},
        #{isSuccess,jdbcType=INTEGER},
        #{failReason,jdbcType=VARCHAR},
        #{controlFunc,jdbcType=VARCHAR} )

    </insert>


    <insert id="insertDqasInfor" parameterType="java.util.Map">
        <selectKey keyProperty="RecordId" order="BEFORE" resultType="java.lang.Long">
            SELECT SFC.JOBS_BOX_SCAN_DQAS_CONTROL_S.NEXTVAL AS value FROM DUAL
        </selectKey>
        INSERT INTO SFC.JOBS_BOX_SCAN_DQAS_CONTROL
        (RECORD_ID, BILL_NUMBER, ITEM_BARCODE,SCAN_BY,
        SCAN_DATE,ENTITY_NAME,CONTRACT_NAME,ITEM_CODE,IS_SUCCESS,FAIL_REASON)
        VALUES
        (#{RecordId,jdbcType=BIGINT},
        #{billNumber,jdbcType=VARCHAR},
        #{barcode,jdbcType=VARCHAR},
        #{updateby,jdbcType=VARCHAR},
        SYSDATE,
        #{entityNo,jdbcType=VARCHAR},
        #{contractNo,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{isSuccess,jdbcType=INTEGER},
        #{failReason,jdbcType=VARCHAR} )
    </insert>


    <select id="checkPrefix" parameterType="Map" resultMap="querycheckPrefix">
        SELECT (SELECT COUNT(RECORD_ID)
        FROM SFC.DQAS_SAVE_ENTITY_PREFIX
        WHERE ENABLED_FLAG = 'Y'
        <if test="deptNo != null">
            AND ATTRIBUTE1= #{deptNo,jdbcType=VARCHAR}
        </if>
        AND ENTITY_PREFIX = SUBSTR(#{taskNo,jdbcType=VARCHAR}, 1, LENGTH(ENTITY_PREFIX))) *
        (SELECT COUNT(RECORD_ID)
        FROM SFC.DQAS_SAVE_ITEM
        WHERE ENABLED_FLAG = 'Y'

        <if test="deptNo != null">
            AND ATTRIBUTE1= #{deptNo,jdbcType=VARCHAR}
        </if>

        <if test="itemCode != null">
            AND ITEM_CODE= #{itemCode,jdbcType=VARCHAR}
        </if>
        ) AS QTY
        FROM DUAL
    </select>

    <select id="getSubBarByMainBujianZuzhuang" parameterType="Map" resultMap="querySubBarByMainBujianZuzhuang">
        SELECT line.item_barcode SUB_BARCODE, line.item_id, line.item_code ITEM_CODE
        FROM sfc.WSM_ASSEMBLE_HEADERS head, sfc.WSM_ASSEMBLE_LINES line
        where head.assemble_headers_id = line.assemble_headers_id
        and line.enabled_flag = 'Y'
        and head.enabled_flag = 'Y'
        <if test="strItemBarcode != null">
            AND head.item_barcode = #{strItemBarcode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getSubBarByImesMainBarcode" parameterType="Map" resultMap="querySubBarByMainBujianZuzhuang">
        SELECT line.itembarcode SUB_BARCODE,line.itemid, line.itemcode ITEM_CODE
        FROM sfc.wsm_assemable_imes_inf line
        where line.enabled_flag = 'Y' AND line.mainbarcode = #{strItemBarcode,jdbcType=VARCHAR}
    </select>


    <select id="getDeptByEntityNo" parameterType="java.lang.String" resultMap="queryDeptByEntityNo">
        SELECT ATTRIBUTE1
        FROM SFC.DQAS_SAVE_ENTITY_PREFIX
        WHERE ENABLED_FLAG = 'Y'
        AND ENTITY_PREFIX = SUBSTR(#{entityNo,jdbcType=VARCHAR}, 1, LENGTH(ENTITY_PREFIX))
    </select>

</mapper>
