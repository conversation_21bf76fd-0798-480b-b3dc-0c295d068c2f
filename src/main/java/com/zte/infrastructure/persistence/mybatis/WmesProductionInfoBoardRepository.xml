<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.WmesProductionInfoBoardRepository">
	<resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.WmesProductionInfoBoard">
    	<result column="OVERALL_UNIT_NAME" jdbcType="VARCHAR" property="model" />
		<result column="QTY" jdbcType="DECIMAL" property="qty" />
		<result column="startTime" jdbcType="VARCHAR" property="startTime" />
		<result column="endTime" jdbcType="VARCHAR" property="endTime" />
	</resultMap>
	
	<resultMap id="resultMap" type="com.zte.domain.model.datawb.WmesProductionInfoBoard">
		<result column="STARTTIME" jdbcType="VARCHAR" property="startTime" />
		<collection property="entityNames" ofType="String">
            <result column="ENTITY_NAME" jdbcType="VARCHAR"/>
        </collection>
	</resultMap>

	<select id="featureComputation" parameterType="com.zte.domain.model.datawb.WmesProductionInfoBoard" resultMap="BaseResultMap">
		SELECT
       		a.overall_unit_name,
       		SUM(item.required_quantity / b.unit_num) as QTY
  		FROM (
        	SELECT MSI.SEGMENT1 item_code,
           		ROUND(NVL(REQ.REQUIRED_QUANTITY, 0), 4) required_quantity,
           		REQ.ORGANIZATION_ID,
           		REQ.WIP_ENTITY_NAME
        	FROM APPS.MTL_SYSTEM_ITEMS@ERP_PROD MSI,
           		(SELECT A.REQUIRED_QUANTITY,
               		A.QUANTITY_ISSUED,
               		SUM(NVL(B.TRANSACTION_QUANTITY, 0)) ONHAND_QTY,
               		A.LAST_UPDATE_DATE,
               		A.LAST_UPDATED_BY,
               		A.COMMENTS,
               		A.SUPPLY_SUBINVENTORY,
               		A.WIP_SUPPLY_TYPE,
               		A.OPERATION_SEQ_NUM,
               		A.INVENTORY_ITEM_ID,
               		A.ORGANIZATION_ID,
               		A.WIP_ENTITY_ID,
               		C.WIP_ENTITY_NAME
            	FROM APPS.MTL_ONHAND_QUANTITIES@ERP_PROD     B,
               		WIP.WIP_REQUIREMENT_OPERATIONS@ERP_PROD A,
               		WIP.WIP_ENTITIES@ERP_PROD               C
           		WHERE A.ORGANIZATION_ID = B.ORGANIZATION_ID(+)
             		AND A.SUPPLY_SUBINVENTORY = B.SUBINVENTORY_CODE(+)
             		AND A.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID(+)
             		AND A.WIP_ENTITY_ID = C.WIP_ENTITY_ID
             		AND NVL(A.ATTRIBUTE15, 'Y') &lt;> 'N'
           		GROUP BY A.REQUIRED_QUANTITY,
                	A.QUANTITY_ISSUED,
                	A.LAST_UPDATE_DATE,
                	A.LAST_UPDATED_BY,
                	A.COMMENTS,
                	A.SUPPLY_SUBINVENTORY,
                	A.WIP_SUPPLY_TYPE,
                	A.OPERATION_SEQ_NUM,
                	A.INVENTORY_ITEM_ID,
                	A.ORGANIZATION_ID,
                	A.WIP_ENTITY_ID,
                	C.WIP_ENTITY_NAME) REQ
       		WHERE REQ.OPERATION_SEQ_NUM > 0
         		AND REQ.INVENTORY_ITEM_ID = MSI.INVENTORY_ITEM_ID
         		AND REQ.ORGANIZATION_ID = MSI.ORGANIZATION_ID
         		AND req.ORGANIZATION_ID = '635'
   			) item 
  		JOIN ba_overall_unit_head a on a.prod_class = #{productCategory} and a.enabled = 1
  		JOIN ba_overall_unit_detail b on a.head_id = b.head_id
			AND item.item_code = b.item_no
			AND b.enabled = 1
   		WHERE item.wip_entity_name IN 
   		<foreach item="entityName" index="index" collection="entityNames" open="(" separator="," close=")">
          #{entityName}
        </foreach>
         group by a.overall_unit_name
	</select>
	
	<!-- 获取当前年月，统一用sysdate 替换to_date('2017/10/27','yyyy/mm/dd')  -->
	<select id="getDay" resultMap="resultMap" parameterType="com.zte.interfaces.dto.WmesProductionInfoBoardDTO">
	SELECT ENTITY_NAME,STARTTIME FROM (SELECT 
	TO_CHAR((LAST_DAY(TO_DATE('2017/10/24','YYYY/MM/DD'))-ROWNUM+1),'YYYY/MM/DD') AS STARTTIME 
	FROM DUAL 
	CONNECT BY LEVEL <![CDATA[ <= ]]> TO_CHAR(LAST_DAY(to_date('2017/10/28 10:10:10', 'yyyy-MM-dd hh24:mi:ss')),'DD')) WW 
	LEFT JOIN 
	(
		SELECT A.ENTITY_NAME,WCAS.S30_DATE,wdj.SCHEDULED_COMPLETION_DATE,x.plan_date
            FROM CPM_CONTRACT_ENTITIES A
            JOIN WERP_CON_ENTITY_TRACE S ON A.ENTITY_ID = S.ENTITY_ID
            LEFT JOIN CMS_DELIVER_ENTITY_INFO C ON C.ENTITY_NUMBER = A.ENTITY_NAME
            JOIN WMES_CON_ACTIONS_SUB WCAS ON WCAS.SUBMACHINE_ID = A.ENTITY_ID
            LEFT JOIN WERP_ENTITY_PKG_PLAN_APD X ON X.ENTITY_ID = A.ENTITY_ID
            LEFT JOIN WIP_DISCRETE_JOBS WDJ ON WDJ.WIP_ENTITY_ID = A.WIP_ENTITY_ID
                                           AND WDJ.ORGANIZATION_ID = A.ORGANIZATION_ID
           WHERE
            A.ENABLED_FLAG = 'Y'
            AND A.ORGANIZATION_ID = '635'
             AND C.MFG_SITE_TYPE LIKE CONCAT(#{mfgSiteType},'%')
             AND S.PRODUCT_TYPE = #{productCategory}
      ) QQ 
      <if test="warehousingTimeStartStr != null">
    	<choose>
    		<when test="warehousingTimeStartStr=='InputQty'">ON WW.STARTTIME = TO_CHAR(QQ.S30_DATE,'YYYY/MM/DD')</when>
    		<when test="warehousingTimeStartStr=='TaskQty'">ON WW.STARTTIME = TO_CHAR(QQ.SCHEDULED_COMPLETION_DATE,'YYYY/MM/DD')</when>
    		<when test="warehousingTimeStartStr=='PackTaskQty'">ON WW.STARTTIME = TO_CHAR(QQ.plan_date,'YYYY/MM/DD') </when>
    	</choose>
      </if>
      ORDER BY WW.STARTTIME
	</select>
	<select id="getCurrentMonth" resultType="java.lang.String" parameterType="com.zte.interfaces.dto.WmesProductionInfoBoardDTO">
		 SELECT A.ENTITY_NAME
            FROM CPM_CONTRACT_ENTITIES A
            JOIN WERP_CON_ENTITY_TRACE S ON A.ENTITY_ID = S.ENTITY_ID
            LEFT JOIN CMS_DELIVER_ENTITY_INFO C ON C.ENTITY_NUMBER = A.ENTITY_NAME
            JOIN WMES_CON_ACTIONS_SUB WCAS ON WCAS.SUBMACHINE_ID = A.ENTITY_ID
           WHERE
            A.ENABLED_FLAG = 'Y'
            AND A.ORGANIZATION_ID = '635'
            AND C.MFG_SITE_TYPE LIKE CONCAT(#{mfgSiteType},'%')
            AND S.PRODUCT_TYPE = #{productCategory}
            AND WCAS.S30_DATE <![CDATA[ >= ]]>  trunc(to_date('2017/10/28 10:10:10', 'yyyy-MM-dd hh24:mi:ss'),'MM')
			AND WCAS.S30_DATE <![CDATA[ <  ]]> ADD_MONTHS(trunc(to_date('2017/10/28 10:10:10', 'yyyy-MM-dd hh24:mi:ss'),'MM'),1)
	</select>
	<select id="getMonthCount" resultMap="resultMap" parameterType="com.zte.interfaces.dto.WmesProductionInfoBoardDTO">
	 	  SELECT ENTITY_NAME,STARTTIME FROM 
	 	  (
	 	  	<if test="warehousingTimeStartStr != null">
    			<choose>
    				<when test="warehousingTimeStartStr=='ThreeMonth'">
    					SELECT TO_CHAR((to_date('2017/10/27','yyyy/mm/dd')+numtoyminterval(-ROWNUM+1, 'MONTH')),'YYYY/MM') AS startTime 
	 	   				FROM DUAL CONNECT BY LEVEL <![CDATA[ <= ]]> 3 
	 	   			</when>
    				<when test="warehousingTimeStartStr=='LastYearMonth'">
	    				SELECT 
	    				TO_CHAR((trunc(to_date('2018/10/27','yyyy/mm/dd'),'yyyy')+numtoyminterval(ROWNUM-1, 'MONTH')+numtoyminterval(-1, 'year')),'YYYY/MM') AS startTime 
						FROM DUAL 
						CONNECT BY LEVEL <![CDATA[ <= ]]> 12
					</when>
    				<when test="warehousingTimeStartStr=='BeforeThreeMonth'">
		   				SELECT 
		   				TO_CHAR((trunc(to_date('2017/6/27','yyyy/mm/dd'),'yyyy')+numtoyminterval(ROWNUM-1, 'MONTH')),'YYYY/MM') AS startTime 
		   				FROM DUAL
		   		 		CONNECT BY LEVEL <![CDATA[ <= ]]> to_char(to_date('2017/6/27','yyyy/mm/dd')+numtoyminterval(-3, 'MONTH'),'MM')
		   		    </when>
    			 </choose> 
	 	     </if>
	 	   ) WW 
	 	   LEFT JOIN 
			(
				SELECT A.ENTITY_NAME,WCAS.S30_DATE
		            FROM CPM_CONTRACT_ENTITIES A
		            JOIN WERP_CON_ENTITY_TRACE S ON A.ENTITY_ID = S.ENTITY_ID
		            LEFT JOIN CMS_DELIVER_ENTITY_INFO C ON C.ENTITY_NUMBER = A.ENTITY_NAME
		            JOIN WMES_CON_ACTIONS_SUB WCAS ON WCAS.SUBMACHINE_ID = A.ENTITY_ID
		           WHERE
		            A.ENABLED_FLAG = 'Y'
		            AND A.ORGANIZATION_ID = '635'
		            AND C.MFG_SITE_TYPE LIKE CONCAT(#{mfgSiteType},'%')
		             AND S.PRODUCT_TYPE = #{productCategory}
		      ) QQ  
		      ON WW.STARTTIME =TO_CHAR(QQ.S30_DATE,'YYYY/MM')
		      ORDER BY WW.STARTTIME 
	</select>
	
	
	
	
	
	
	
	
	
	
	
	
</mapper>