<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.KxOmorderShippedlistRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.KxOmorderShippedlist">
        <id column="SERIAL_ID" jdbcType="DECIMAL" property="serialId" />
        <result column="OMSALES_BILL" jdbcType="VARCHAR" property="omsalesBill"/>
        <result column="SALES_CONTRACT_NO" jdbcType="VARCHAR" property="salesContractNo"/>
        <result column="STOCK" jdbcType="VARCHAR" property="stock"/>
        <result column="DROP_ID" jdbcType="VARCHAR" property="dropId"/>
        <result column="SIGN_BY" jdbcType="VARCHAR" property="signBy"/>
        <result column="SIGN_DATE" jdbcType="TIMESTAMP" property="signDate"/>
        <result column="IS_SIGN" jdbcType="DECIMAL" property="isSign"/>
        <result column="DOC_NAME" jdbcType="VARCHAR" property="docName"/>
        <result column="BILL_CODE" jdbcType="VARCHAR" property="billCode"/>
        <result column="DOC_ID" jdbcType="VARCHAR" property="docId"/>
        <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="batch" jdbcType="INTEGER" property="batch"/>
    </resultMap>

    <sql id="Base_Column_List">
        SERIAL_ID,OMSALES_BILL, SALES_CONTRACT_NO, STOCK, DROP_ID, SIGN_BY,
        SIGN_DATE, IS_SIGN, DOC_NAME,BILL_CODE,DOC_ID,FILE_URL, LAST_UPDATE_DATE,batch
    </sql>

    <insert id="insertKxOmorderShippedlist" parameterType="com.zte.domain.model.datawb.KxOmorderShippedlist">
        <selectKey  keyProperty="serialId" order="BEFORE" resultType="java.math.BigDecimal">
            SELECT kxstepiii.KX_OMORDER_SHIPPEDLIST_S.nextval serialId from dual
        </selectKey>
        insert into kxstepiii.KX_OMORDER_SHIPPEDLIST (SERIAL_ID,OMSALES_BILL, SALES_CONTRACT_NO, STOCK,
        DROP_ID, SIGN_BY,SIGN_DATE, IS_SIGN, DOC_NAME,BILL_CODE,DOC_ID,FILE_URL,LAST_UPDATE_DATE,batch
        )
        values (#{serialId},#{omsalesBill,jdbcType=VARCHAR}, #{salesContractNo,jdbcType=VARCHAR},
        #{stock,jdbcType=VARCHAR}, #{dropId,jdbcType=VARCHAR}, #{signBy,jdbcType=VARCHAR},
        #{signDate,jdbcType=TIMESTAMP}, #{isSign,jdbcType=DECIMAL}, #{docName,jdbcType=VARCHAR},
        #{billCode,jdbcType=VARCHAR},#{docId,jdbcType=VARCHAR},#{fileUrl,jdbcType=VARCHAR},SYSDATE,
        #{batch,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertKxOmorderShippedlistSelective" parameterType="com.zte.domain.model.datawb.KxOmorderShippedlist">
        <selectKey  keyProperty="serialId" order="BEFORE" resultType="java.math.BigDecimal">
            SELECT kxstepiii.KX_OMORDER_SHIPPEDLIST_S.nextval serialId from dual
        </selectKey>
        insert into kxstepiii.KX_OMORDER_SHIPPEDLIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            SERIAL_ID,
            <if test="omsalesBill != null">
                OMSALES_BILL,
            </if>

            <if test="salesContractNo != null">
                SALES_CONTRACT_NO,
            </if>

            <if test="stock != null">
                STOCK,
            </if>

            <if test="dropId != null">
                DROP_ID,
            </if>

            <if test="signBy != null">
                SIGN_BY,
            </if>

            <if test="signDate != null">
                SIGN_DATE,
            </if>

            <if test="isSign != null">
                IS_SIGN,
            </if>

            <if test="docName != null">
                DOC_NAME,
            </if>

            <if test="billCode != null">
                BILL_CODE,
            </if>

            <if test="docId != null">
                DOC_ID,
            </if>
            <if test="fileUrl != null">
                FILE_URL,
            </if>

            LAST_UPDATE_DATE,
            <if test="batch != null">
                batch,
            </if>


        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{serialId},
            <if test="omsalesBill != null">
                #{omsalesBill,jdbcType=VARCHAR},
            </if>

            <if test="salesContractNo != null">
                #{salesContractNo,jdbcType=VARCHAR},
            </if>

            <if test="stock != null">
                #{stock,jdbcType=VARCHAR},
            </if>

            <if test="dropId != null">
                #{dropId,jdbcType=VARCHAR},
            </if>

            <if test="signBy != null">
                #{signBy,jdbcType=VARCHAR},
            </if>

            <if test="signDate != null">
                #{signDate,jdbcType=TIMESTAMP},
            </if>

            <if test="isSign != null">
                #{isSign,jdbcType=DECIMAL},
            </if>

            <if test="docName != null">
                #{docName,jdbcType=VARCHAR},
            </if>

            <if test="billCode != null">
                #{billCode,jdbcType=VARCHAR},
            </if>

            <if test="docId != null">
                #{docId,jdbcType=VARCHAR},
            </if>

            <if test="fileUrl != null">
                #{fileUrl,jdbcType=VARCHAR},
            </if>

            SYSDATE,
            <if test="batch != null">
                #{batch,jdbcType=INTEGER},
            </if>

        </trim>

    </insert>

    <select id="selectOneByCondition" parameterType="com.zte.domain.vo.datawb.ShippedListVO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from kxstepiii.kx_omorder_shippedlist
        <where>
            <if test="omsalesBill != null and omsalesBill != ''">
                and OMSALES_BILL = #{omsalesBill,jdbcType=VARCHAR}
            </if>
            <if test="stock != null and stock != ''">
                and STOCK = #{stock,jdbcType=VARCHAR}
            </if>
            <if test="batch != null">
                and batch = #{batch,jdbcType=INTEGER}
            </if>
            <if test="docId != null">
                and DOC_ID = #{docId,jdbcType=VARCHAR}
            </if>
            <if test="(omsalesBill == null or omsalesBill == '') and (stock == null or stock == '') and
                (batch == null ) and (docId == null)">
                and 1=2
            </if>
        </where>
    </select>

    <select id="selectWareHouseList" resultType="com.zte.domain.vo.datawb.HouseVo">
        select stock_no stockNo,stock_name stockName
        from kxstepiii.st_stock sk
        where sk.is_infor = 1 and sk.enabled_flag = 1 and sk.stock_use != 0
    </select>

    <select id="selectListByPage" resultType="com.zte.domain.vo.datawb.ShippedListVO"
            parameterType="com.zte.springbootframe.common.model.Page">
        select  omsalesBill,
                salesContractNo,
                utilityNo,
                pickedUnit,
                pickedBy,
                so.stock,
                so.batch,
                nvl(sl.is_sign, 0) isSign,
                sk.stock_name stockName
        from (
                select distinct sfe.externalorderkey2 omsalesBill,
                sfe.saleno salesContractNo,
                sfe.SALENOTYPE utilityNo,
                sfe.SO_DEP pickedUnit,
                sfe.SO_BY pickedBy,
                sfe.batch batch,
                sfe.whseid stock
                from kxstepiii.sodetail_for_elecsign sfe
                <where>
                    <if test="params.omsalesBill != null and params.omsalesBill != ''">
                        and sfe.externalorderkey2 = #{params.omsalesBill,jdbcType=VARCHAR}
                    </if>
                    <if test="params.salesContractNo != null and params.salesContractNo != ''">
                        and sfe.saleno = #{params.salesContractNo,jdbcType=VARCHAR}
                    </if>
                    <if test="params.stock != null and params.stock != ''">
                        and sfe.whseid = #{params.stock,jdbcType=VARCHAR}
                    </if>
                    <if test="params.pickedBy != null and params.pickedBy != ''">
                        and sfe.SO_BY = #{params.pickedBy,jdbcType=VARCHAR}
                    </if>
                    <if test="params.startTime != null and params.startTime != ''">
                        <![CDATA[ and sfe.Last_Update_Date >= TO_DATE(#{params.startTime},'yyyy-mm-dd hh24:mi:ss') ]]>
                    </if>
                    <if test="params.endTime != null and params.endTime != ''">
                        <![CDATA[ and sfe.Last_Update_Date <= TO_DATE(#{params.endTime},'yyyy-mm-dd hh24:mi:ss') ]]>
                    </if>
                </where>
                order by sfe.externalorderkey2 asc,sfe.whseid asc,sfe.batch asc
        ) so
        left join kxstepiii.st_stock sk on so.stock = sk.stock_no
        left join kxstepiii.kx_omorder_shippedlist sl
        on so.omsalesBill = sl.OMSALES_BILL
        and so.stock = sl.stock
        and so.batch = sl.batch
        <where>
            <if test="params.isSign != null">
                nvl(sl.is_sign, 0) = #{params.isSign,jdbcType=INTEGER}
            </if>
        </where>

    </select>

    <select id="selectDetailList" parameterType="com.zte.domain.vo.datawb.ShippedListVO"
            resultType="com.zte.domain.vo.datawb.ShippedListDetailVO">
        select  sfe.externalorderkey2 omsalesBill,
                sfe.saleno salesContractNo,
                sfe.salenotype utilityNo,
                sfe.orderkey orderKey,
                sfe.so_dep pickedUnit,
                sfe.so_by pickedBy,
                ss.stock_name stockName,
                p.dropid dropId,
                sfe.Originalqty qty,
                sfe.shippedqty shippedQty,
                sfe.sku itemNo,
                sfe.lottable02 itemBarcode ,
                su.descr itemName,
                PC.CLASSGROUP groupNo
        from kxstepiii.sodetail_for_elecsign sfe
        join ${stock}.PICKDETAIL@db_inforwms p
        on p.orderkey = sfe.orderkey and p.pickdetailkey = sfe.ref60 and p.whseid = sfe.whseid
        join ${stock}.sku@db_inforwms su
        ON sfe.SKU = SU.SKU AND SU.STORERKEY = 'ZTE'
        LEFT JOIN ${stock}.V_PUTAWAYZONE_CLASSGROUP@db_inforwms PC
        ON PC.PUTAWAYZONE = SU.PUTAWAYZONE
        JOIN kxstepiii.st_stock ss
        on ss.stock_no = sfe.whseid
        <where>
            <if test="omsalesBill != null and omsalesBill != ''">
                and sfe.externalorderkey2 = #{omsalesBill,jdbcType=VARCHAR}
            </if>
            <if test="stock != null and stock != ''">
                and sfe.whseid = #{stock,jdbcType=VARCHAR}
            </if>
            <if test="batch != null ">
                and sfe.batch = #{batch,jdbcType=INTEGER}
            </if>
            <if test="(omsalesBill == null or omsalesBill == '') and (stock == null or stock == '') and batch == null">
                and 1=2
            </if>
        </where>

    </select>

    <select id="selectFileName" resultType="java.lang.String" parameterType="com.zte.domain.vo.datawb.ShippedListVO">
        select max(DOC_NAME) docName from kxstepiii.kx_omorder_shippedlist
        where OMSALES_BILL = #{omsalesBill,jdbcType=VARCHAR}
        and STOCK = #{stock,jdbcType=VARCHAR}
        and batch = #{batch,jdbcType=INTEGER}
    </select>




</mapper>
