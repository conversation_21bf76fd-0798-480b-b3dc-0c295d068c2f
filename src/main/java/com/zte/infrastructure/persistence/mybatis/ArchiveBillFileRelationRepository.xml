<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.ArchiveBillFileRelationRepository">
    <!--插入-->
    <insert id="insert" parameterType="com.zte.domain.model.ArchiveBillFileRelation">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT SFC.ARCHIVE_BILL_FILE_RELATION_S.NEXTVAL AS value FROM DUAL
        </selectKey>
        INSERT
            INTO
            SFC.ARCHIVE_BILL_FILE_RELATION (
            ID,
            TASK_ID,
            BILL_ID,
            BILL_NO,
            CLOUDDISK_PDF_ID,
            CREATED_BY,
            LAST_UPDATED_BY,
            CREATION_DATE,
            LAST_UPDATE_DATE,
            ENABLED_FLAG)
        VALUES(
        #{id},
        #{taskId},
        #{billId},
        #{billNo},
        #{clouddiskPdfId},
        #{createdBy},
        #{lastUpdatedBy},
        sysdate,
        sysdate,
        'Y'
        )
    </insert>
</mapper>