<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ConfigMaterialBindServiceRepository">

    <resultMap id="paramResultMap" type="com.zte.interfaces.dto.ConfigMaterialBindDTO">
        <result property="recordId" jdbcType="DECIMAL" column="RECORD_ID"/>
        <result property="mainBarcode" jdbcType="VARCHAR" column="MAINBARCODE"/>
        <result property="customMaterialCode" jdbcType="VARCHAR" column="CUSTOMMATERIALCODE"/>
        <result property="mainCode" jdbcType="VARCHAR" column="MAINCODE"/>
        <result property="mainItemName" jdbcType="VARCHAR" column="MAINITEMNAME"/>
        <result property="mainScanType" jdbcType="VARCHAR" column="MAINSCANTYPE"/>
        <result property="mainItemQty" jdbcType="DECIMAL" column="MAINITEMQTY"/>
        <result property="seqName" jdbcType="VARCHAR" column="SEQNAME"/>
        <result property="isReplace" jdbcType="VARCHAR" column="ISREPLACE"/>
        <result property="qrCode" jdbcType="DECIMAL" column="QRCode"/>
        <result property="replaceItemCode" jdbcType="VARCHAR" column="REPLACEITEMCODE"/>
        <result property="replaceItemName" jdbcType="VARCHAR" column="REPLACEITEMNAME"/>
        <result property="taskNo" jdbcType="VARCHAR" column="TASKNO"/>
        <result property="contractNum" jdbcType="VARCHAR" column="CONTRACTNUM"/>
        <result property="siteAdress" jdbcType="VARCHAR" column="SITE_ADDRESS"/>
        <result property="mfgSiteId" jdbcType="DECIMAL" column="MFG_SITE_ID"/>
        <result property="mfgSiteCode" jdbcType="VARCHAR" column="MFGSITECODE"/>
        <result property="invItemId" jdbcType="DECIMAL" column="INVITEMID"/>
        <result property="userName" jdbcType="VARCHAR" column="USERNAME"/>
        <result property="mainScanTime" jdbcType="TIMESTAMP" column="MAINSCANTIME"/>
        <result property="parentRecordId" jdbcType="DECIMAL" column="PARENT_RECORD_ID"/>
        <result property="realItemCode" jdbcType="VARCHAR" column="REAL_ITEM_CODE"/>
        <result property="realItemName" jdbcType="VARCHAR" column="REAL_ITEM_NAME"/>
        <result property="realItemNameEn" jdbcType="VARCHAR" column="REAL_ITEM_NAME_EN"/>
        <result property="configDetailId" jdbcType="DECIMAL" column="CONFIG_DETAIL_ID"/>
        <result property="taskVerifyFlag" jdbcType="VARCHAR" column="TaskVerifyFlag"/>
    </resultMap>

    <resultMap id="getLeverInfoSelect" type="com.zte.interfaces.dto.LeverInfoDTO">
        <result property="parentConfigDetailId" jdbcType="DECIMAL" column="PARENT_CONFIG_DETAIL_ID"/>
        <result property="configDetailId" jdbcType="DECIMAL" column="CONFIG_DETAIL_ID"/>
        <result property="level" jdbcType="VARCHAR" column="RELATION_IDENTIFIER"/>
        <result property="materialCode" jdbcType="VARCHAR" column="SEGMENT1"/>
        <result property="materialName" jdbcType="VARCHAR" column="DESCRIPTION"/>
    </resultMap>

    <resultMap id="getMarkInfoSelect" type="com.zte.interfaces.dto.MarkInfoDTO">
        <result property="configDetailId" jdbcType="DECIMAL" column="CONFIG_DETAIL_ID"/>
        <result property="markStatus" jdbcType="VARCHAR" column="MARK_STATUS"/>
    </resultMap>

    <resultMap id="DetailMap" type="com.zte.interfaces.dto.ConfigDetailDTO">
        <id column="CONFIG_DETAIL_ID" jdbcType="VARCHAR" property="configDetailID"/>
        <result column="PARENT_CONFIG_DETAIL_ID" jdbcType="VARCHAR" property="parentConfigDetailID"/>
        <result column="RELATION_IDENTIFIER" jdbcType="VARCHAR" property="relationIdentifier"/>
        <result column="OPERATION_DESCRIPTION" jdbcType="VARCHAR" property="operationDescription"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="IMPORT_ITEM_TYPE" jdbcType="VARCHAR" property="importItemType"/>
        <result column="item_Name" jdbcType="VARCHAR" property="itemName"/>
        <result column="IMPORT_ITEM_NAME" jdbcType="VARCHAR" property="boqItemName"/>
        <result column="BOQ_ITEM_NAME_ENG" jdbcType="VARCHAR" property="boqItemNameEng"/>
        <result column="quantity_Total" jdbcType="VARCHAR" property="quantityTotal"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="PTO_Code" jdbcType="VARCHAR" property="ptoCode"/>
        <result column="PTO_Name" jdbcType="VARCHAR" property="ptoName"/>
        <result column="PTO_Name_ENG" jdbcType="VARCHAR" property="ptoNameEng"/>
        <result column="site_Address" jdbcType="VARCHAR" property="siteAddress"/>
        <result column="assist_Flag" jdbcType="VARCHAR" property="assistFlag"/>
        <result column="Unit" jdbcType="VARCHAR" property="unit"/>
        <result column="mfg_Site_ID" jdbcType="VARCHAR" property="mfgSiteID"/>
    </resultMap>

    <resultMap id="CheckBindingRelationshipMap" type="com.zte.interfaces.dto.CheckBindingRelationshipDTO">
        <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName"/>
        <result column="IS_PDVM_FLAG" jdbcType="VARCHAR" property="isPDVMFlag"/>
        <result column="ENTITY_ID" jdbcType="VARCHAR" property="entityId"/>
    </resultMap>

    <select id="paramGetDate" statementType="CALLABLE" parameterType="map">
        {call APP_MES.CPM_CONFIG_ITEM_ASSEMBLE_PKG.GET_CONFIG_ITEM_ASSEMBLENEWS(
        #{map.entityName,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.itemBarcode,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.mfgSiteId,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=paramResultMap}
        )}
    </select>

    <select id="paramGetDatePlus" statementType="CALLABLE" parameterType="map">
        {call APP_MES.CPM_CONFIG_ITEM_ASSEMBLE_PKG.GET_CONFIG_ITEM_ASSEMBLEPLUS(
        #{map.entityName,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.itemBarcode,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.mfgSiteId,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=paramResultMap}
        )}
    </select>

    <select id="getEntityId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT ENTITY_ID FROM APP_MES.CPM_CONTRACT_ENTITIES
        WHERE ENTITY_NAME=#{param}
    </select>

    <select id="checkSiteTally" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT COUNT(0) AS RES_COUNT
        FROM APP_MES.CPM_CONTRACT_SITES A,
        APP_MES.CPM_CONTRACT_MFG_SITES B
        WHERE A.SITE_ID = B.SITE_ID
        AND A.ENABLED_FLAG = 'Y'
        AND B.ENABLED_FLAG = 'Y'
        AND B.ENTITY_ID = #{entityId}
    </select>

    <select id="getLeverInfo" parameterType="java.lang.Integer" resultMap="getLeverInfoSelect">
        SELECT
        PARENT_CONFIG_DETAIL_ID,
        CCD.CONFIG_DETAIL_ID,
        CCD.RELATION_IDENTIFIER,
        B.SEGMENT1,
        B.DESCRIPTION
        FROM APP_MES.MTL_SYSTEM_ITEMS B,
        APP_MES.MTL_SYSTEM_ITEMS PTO,
        APP_MES.BOM_STANDARD_OPERATIONS BSO,
        APP_MES.CPM_CONFIG_DETAILS CCD,
        APP_MES.CPM_CONTRACT_SITES SITE,
        APP_MES.CPM_CONTRACT_MFG_SITES MFG,
        APP_MES.CPM_PO_HEADERS cph,
        APP_MES.CPM_PO_LINES cpl
        WHERE CCD.ORGANIZATION_ID = B.ORGANIZATION_ID
        AND CCD.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
        AND CCD.STANDARD_OPERATION_ID=BSO.STANDARD_OPERATION_ID(+)
        AND CCD.ENABLED_FLAG = 'Y'
        AND SITE.ENABLED_FLAG = 'Y'
        AND MFG.ENABLED_FLAG = 'Y'
        AND MFG.PTO_INVENTORY_ITEM_ID = PTO.INVENTORY_ITEM_ID(+)
        AND MFG.ORGANIZATION_ID = PTO.ORGANIZATION_ID(+)
        AND CCD.MFG_SITE_ID = MFG.MFG_SITE_ID
        AND MFG.SITE_ID = SITE.SITE_ID
        AND ccd.po_header_id = cph.po_header_id(+)
        AND ccd.po_line_id = cpl.po_line_id(+)
        AND MFG.MFG_SITE_ID =#{siteID}
    </select>


    <select id="getMarkInfo" parameterType="Map" resultMap="getMarkInfoSelect">
        SELECT
        CONFIG_DETAIL_ID,
        '已完成' MARK_STATUS
        FROM APP_MES.CPM_BOXUP_BILL_MARK MARK
        WHERE ENABLED_FLAG='Y'
        <if test="entityId != null">AND ENTITY_ID =#{entityId,jdbcType=DECIMAL}</if>
        <if test="siteId != null">AND MFG_SITE_ID =#{siteId,jdbcType=DECIMAL}</if>
        <if test="siteId == null and entityId == null">AND 1 = 2</if>
    </select>

    <select id="getDetailByMfgSiteIDList" resultMap="DetailMap">
        SELECT CCD.CONFIG_DETAIL_ID,
        CCD.PARENT_CONFIG_DETAIL_ID,
        CCD.RELATION_IDENTIFIER,
        BSO.OPERATION_DESCRIPTION,
        B.SEGMENT1 as item_code,
        B.DESCRIPTION as item_Name,
        CCD.IMPORT_ITEM_TYPE,
        CCD.IMPORT_ITEM_NAME,
        CCD.BOQ_ITEM_NAME_ENG,
        to_char(ROUND(APP_MES.GETCONFIGQTYBYMFGSITE(CCD.MFG_SITE_ID, CCD.CONFIG_DETAIL_ID),2)) as quantity_Total,
        to_char(round(CCD.QUANTITY, 3)) as quantity,
        PTO.SEGMENT1 as PTO_Code,
        PTO.DESCRIPTION as PTO_Name,
        APP_MES.FIND_ITEM_ENGLISH_NAME(PTO.INVENTORY_ITEM_ID,PTO.ORGANIZATION_ID) as PTO_Name_ENG,
        SITE.SITE_ADDRESS,
        DECODE(CCD.ASSIST_FLAG, 'Y', '是', '否') as assist_Flag,
        DECODE(CCD.CN_UNIT,NULL,APP_MES.FIND_ITEM_UNIT_REPAIR_TL(B.SEGMENT1,B.PRIMARY_UNIT_OF_MEASURE,
        'ZHS'),CCD.CN_UNIT) as Unit,
        MFG.MFG_SITE_ID,
        CCD.config_type,
        CCD.config_type_id
        FROM APP_MES.MTL_SYSTEM_ITEMS B,
        APP_MES.MTL_SYSTEM_ITEMS PTO,
        APP_MES.BOM_STANDARD_OPERATIONS BSO,
        APP_MES.CPM_CONFIG_DETAILS CCD,
        APP_MES.CPM_CONTRACT_SITES SITE,
        APP_MES.CPM_CONTRACT_MFG_SITES MFG
        WHERE CCD.ORGANIZATION_ID = B.ORGANIZATION_ID
        AND CCD.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
        AND CCD.STANDARD_OPERATION_ID = BSO.STANDARD_OPERATION_ID(+)
        AND CCD.ENABLED_FLAG = 'Y'
        AND SITE.ENABLED_FLAG = 'Y'
        AND MFG.ENABLED_FLAG = 'Y'
        AND MFG.PTO_INVENTORY_ITEM_ID = PTO.INVENTORY_ITEM_ID(+)
        AND MFG.ORGANIZATION_ID = PTO.ORGANIZATION_ID(+)
        AND CCD.MFG_SITE_ID = MFG.MFG_SITE_ID
        AND MFG.SITE_ID = SITE.SITE_ID
        and MFG.MFG_SITE_ID in
        <foreach collection="mfgSiteIDList" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ORDER BY CCD.RELATION_IDENTIFIER
    </select>

    <select id="selectWholeBarcodeInfoOfTask" resultMap="CheckBindingRelationshipMap">
        select t.ENTITY_ID,t.ENTITY_NAME, T.IS_PDVM_FLAG
        from app_mes.cpm_contract_entities t
        WHERE T.ENABLED_FLAG = 'Y'
        AND t.ENTITY_NAME IN
        <foreach collection="parameterList" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="filterNotBoundEntityIds" resultType="java.lang.String">
        SELECT DISTINCT t.ENTITY_ID
        FROM APP_MES.CPM_CONTRACT_MFG_SITES t
        WHERE t.ENABLED_FLAG = 'Y'
        AND t.IS_PDVM_FLAG = 'Y' AND (t.IS_TRUE_MATATERIAL = 'N' OR t.IS_TRUE_MATATERIAL IS NULL)
        AND t.ENTITY_ID IN
        <foreach collection="entityIdList" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="filterNotBoundMfgSiteIds" resultType="java.lang.String">
        SELECT DISTINCT t.MFG_SITE_ID
        FROM APP_MES.CPM_CONTRACT_MFG_SITES t
        WHERE t.ENABLED_FLAG = 'Y'
        AND t.IS_PDVM_FLAG = 'Y' AND (t.IS_TRUE_MATATERIAL = 'N' OR t.IS_TRUE_MATATERIAL IS NULL)
        AND t.MFG_SITE_ID IN
        <foreach collection="mfgSiteIdList" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>