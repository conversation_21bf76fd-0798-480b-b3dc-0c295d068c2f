<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewGetConfigItemRepository">

    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="querySubBarCodeByMainBar">
        <result property="subBarcode" jdbcType="VARCHAR" column="SUB_BARCODE" />
        <result property="itemCode"  jdbcType="VARCHAR" column="ITEM_CODE" />
    </resultMap>



<resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="querySubConfigItemAssemble">
    <result property="itemBarcode" jdbcType="VARCHAR" column="ITEM_BARCODE" />
    <result property="itemCode"  jdbcType="VARCHAR" column="ITEM_CODE" />
</resultMap>

<select id="getSubConfigItemAssemble"  parameterType="Map"  resultMap="querySubConfigItemAssemble">
    SELECT C.RECORD_ID,
    C.ENTITY_ID,
    C.CONFIG_DETAIL_ID,
    C.ITEM_BARCODE,
    C.ITEM_CODE,
    C.BARCODE_TYPE,
    C.BARCODE_QTY,
    C.PARENT_RECORD_ID,
    C.ORGANIZATION_ID,
    C.LAST_UPDATE_DATE,
    C.ITEM_CODE,
    C.ITEM_ID,
    C.ITEM_NAME,
    C.SEQUENCE_NAME
    FROM APP_MES.CPM_CONFIG_ITEM_ASSEMBLE C
    WHERE C.ENABLED_FLAG = 'Y'
    AND C.LAST_UPDATE_DATE > ADD_MONTHS(sysdate, -6)

    <if test="recordId != null">
        AND C.RECORD_ID != #{recordId,jdbcType=BIGINT}
    </if>

    <if test="configDetailId != null">
        AND C.CONFIG_DETAIL_ID = #{configDetailId,jdbcType=INTEGER}
    </if>

    <if test="entityId != null">
        AND C.ENTITY_ID = #{entityId,jdbcType=INTEGER}
    </if>
    <if test="recordId != null">
    START WITH C.RECORD_ID = #{recordId,jdbcType=BIGINT}
    </if>


    CONNECT BY PRIOR C.RECORD_ID = C.PARENT_RECORD_ID

</select>


    <select id="getSubBarCodeByMainBar"  parameterType="Map"  resultMap="querySubBarCodeByMainBar">

        SELECT L.ITEM_BARCODE SUB_BARCODE,
        L.ITEM_ID,
        L.ITEM_CODE,
        L.ITEM_QTY,
        L.CONFIG_DETAIL_ID,
        L.SCAN_TYPE
        FROM APP_MES.CPM_MFG_BARCODE_HEADERS H,
        APP_MES.CPM_MFG_BARCODE_LINES   L
        WHERE H.CONFIG_HEADERS_ID = L.CONFIG_HEADERS_ID
        AND H.ORG_ID = L.ORG_ID
        AND H.ENABLED_FLAG = 'Y'
        AND L.ENABLED_FLAG = 'Y'

        <if test="strItemBarcode != null">
            AND H.ITEM_BARCODE = #{strItemBarcode,jdbcType=VARCHAR}
        </if>

        <if test="siteId != null">
            AND H.MFG_SITE_ID = #{siteId,jdbcType=INTEGER}
        </if>

    </select>


</mapper>