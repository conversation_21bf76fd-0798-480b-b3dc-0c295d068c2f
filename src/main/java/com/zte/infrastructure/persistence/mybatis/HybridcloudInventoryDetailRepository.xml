<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.HybridcloudInventoryDetailRepository">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.HybridCloudInventoryDetailDTO" id="hybridcloudInventoryDetailMap">
        <result property="id" column="id"/>
        <result property="inventoryDate" column="inventory_date"/>
        <result property="configModel" column="config_model"/>
        <result property="mpn" column="mpn"/>
        <result property="inventoryQty" column="inventory_qty"/>
        <result property="deliveredQuantity" column="delivered_quantity"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="enabledFlag" column="enabled_flag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, inventory_date, config_model, mpn, inventory_qty, delivered_quantity, remark, create_by, create_date,
        last_updated_by, last_updated_date, enabled_flag
    </sql>

    <insert id="insert">
        insert into hybridcloud_inventory_detail
        (id, inventory_date, config_model, mpn, inventory_qty, delivered_quantity, remark, create_by, create_date,
        last_updated_by, last_updated_date, enabled_flag )
        values
        (#{id, jdbcType=VARCHAR}, #{inventoryDate, jdbcType=TIMESTAMP}, #{configModel, jdbcType=VARCHAR},
        #{mpn, jdbcType=VARCHAR}, #{inventoryQty, jdbcType=DECIMAL}, #{deliveredQuantity, jdbcType=DECIMAL},
        #{remark, jdbcType=VARCHAR}, #{createBy, jdbcType=VARCHAR}, #{createDate, jdbcType=TIMESTAMP},
        #{lastUpdatedBy, jdbcType=VARCHAR}, #{lastUpdatedDate, jdbcType=TIMESTAMP}, #{enabledFlag, jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsert">
        insert into hybridcloud_inventory_detail
        (id, inventory_date, config_model, mpn, inventory_qty, delivered_quantity, remark, create_by,
        last_updated_by )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id, jdbcType=VARCHAR}, #{item.inventoryDate, jdbcType=TIMESTAMP},
            #{item.configModel, jdbcType=VARCHAR}, #{item.mpn, jdbcType=VARCHAR},
            #{item.inventoryQty, jdbcType=DECIMAL}, #{item.deliveredQuantity, jdbcType=DECIMAL},
            #{item.remark, jdbcType=VARCHAR}, #{item.createBy, jdbcType=VARCHAR},
            #{item.lastUpdatedBy, jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="deleteByInventoryDate">
        update hybridcloud_inventory_detail
        set enabled_flag = 'N'
        where inventory_date = #{inventoryDate}
    </update>

    <select id="selectCount" parameterType="com.zte.interfaces.dto.HybridCloudInventoryDetailPageQueryDTO"
            resultType="java.lang.Integer">
        select count(1) from hybridcloud_inventory_detail
        <where>
            enabled_flag= 'Y'
            <if test="inventoryDate != null">
                and inventory_date = #{params.inventoryDate}
            </if>
            <if test="mpn != null and mpn != ''">
                and mpn = #{mpn, jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectPage" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="hybridcloudInventoryDetailMap">
        select
        <include refid="Base_Column_List"/>
        from hybridcloud_inventory_detail
        <where>
            enabled_flag= 'Y'
            <if test="params != null and params.inventoryDate != null">
                and inventory_date = #{params.inventoryDate}
            </if>
            <if test="params != null and params.mpn != null and params.mpn != ''">
                and mpn = #{params.mpn, jdbcType=VARCHAR}
            </if>
        </where>
        order by last_updated_date desc
    </select>
    <select id="selectByInventoryDate" parameterType="com.zte.springbootframe.common.model.Page"
            resultMap="hybridcloudInventoryDetailMap">
        select
        <include refid="Base_Column_List"/>
        from hybridcloud_inventory_detail
        <where>
            enabled_flag= 'Y'
            and inventory_date = #{inventoryDate, jdbcType=TIMESTAMP}
        </where>
        order by last_updated_date desc
    </select>

</mapper>