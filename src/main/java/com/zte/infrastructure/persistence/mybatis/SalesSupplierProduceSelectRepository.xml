<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.SalesSupplierProduceSelectRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.SalesSupplierProduceSelectDTO">
        <id column="ITEM_BARCODE" jdbcType="VARCHAR" property="itemBarcode"/>
        <result column="PRODUCT_NO" jdbcType="VARCHAR" property="productNo"/>
        <result column="EXTERNALORDERKEY2" jdbcType="VARCHAR" property="externalorderkey2"/>
        <result column="ACTUALSHIPDATE" jdbcType="VARCHAR" property="actualshipdate"/>
    </resultMap>

    <select id="getStItemBarcodeList" parameterType="com.zte.interfaces.dto.SalesSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select ITEM_BARCODE,PRODUCT_NO
        from kxstepiii.ST_ITEM_BARCODE t
        where t.ENABLED_FLAG=1
        <if test="itemBarcodes != null and itemBarcodes.size > 0">
            AND t.ITEM_BARCODE IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR t.ITEM_BARCODE IN()">    <!-- 表示删除最后一个条件 -->
                <foreach collection="itemBarcodes" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR t.ITEM_BARCODE IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="itemBarcodes == null or itemBarcodes.size == 0">
            and 1=2
        </if>
    </select>

    <select id="selectItemBarcodeList" parameterType="com.zte.interfaces.dto.SalesSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select ITEM_BARCODE
        from kxstepiii.ST_ITEM_BARCODE t
        where t.ENABLED_FLAG=1
        <if test="productNo != null and productNo != ''">and t.PRODUCT_NO = #{productNo}</if>
        <if test="productNo == null or productNo == ''">
            and rownum <![CDATA[ <= 500 ]]>
        </if>
    </select>

    <select id="getReelIdInfoList" parameterType="com.zte.interfaces.dto.SalesSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select EXTERNALORDERKEY2,to_char(ACTUALSHIPDATE,'yyyy-MM-dd HH:mi:ss') ACTUALSHIPDATE
        from v_reelid_info t
        where 1=1
        <if test="externalorderkey2s != null and externalorderkey2s.size > 0">
            AND t.EXTERNALORDERKEY2 IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR t.EXTERNALORDERKEY2 IN()">    <!-- 表示删除最后一个条件 -->
                <foreach collection="externalorderkey2s" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR t.EXTERNALORDERKEY2 IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="externalorderkey2s == null or externalorderkey2s.size() == 0">
            and rownum <![CDATA[ <= 500 ]]>
        </if>
    </select>
    <select id="selectReelIdInfoList" parameterType="com.zte.interfaces.dto.SalesSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select EXTERNALORDERKEY2
        from v_reelid_info t
        where 1=1
        <if test="startDate != null and startDate != ''">
            and t.ACTUALSHIPDATE <![CDATA[ >= ]]>  TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate != ''">
            and t.ACTUALSHIPDATE <![CDATA[ <= ]]>  TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">
            and rownum <![CDATA[ <= 500 ]]>
        </if>
    </select>
</mapper>
