<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.InfoInventoryQueryInfoRepository">

    <resultMap id="InfoInventoryResultMap" type="com.zte.domain.model.datawb.InfoInventoryQueryInfo">
        <result column="SUBINVEBTORY" jdbcType="VARCHAR" property="subInventory" />
        <result column="MATERIALCODE" jdbcType="VARCHAR" property="materialCode" />
        <result column="MATERIALNAME" jdbcType="VARCHAR" property="materialName" />

        <result column="NUM" jdbcType="INTEGER" property="number" />
        <result column="UNLEADNUM" jdbcType="INTEGER" property="unleadNumber" />

        <result column="BRANDNAME" jdbcType="VARCHAR" property="brandName" />
        <result column="ISLEAD" jdbcType="INTEGER" property="isLead" />
    </resultMap>

    <select id="selectInventoryInfo"  resultMap="InfoInventoryResultMap">
                  select SA.SUBINV_CODE SUBINVEBTORY,
                       sb.item_no MATERIALCODE,
                       sb.item_name MATERIALNAME,
                       sum(SA.QUANTITY) NUM,
                       sum(SA.NOLEAD_QTY) UNLEADNUM,
                       bb.brand_name BRANDNAME,
                       SB.IS_LEAD ISLEAD
                  FROM lms.stockssummary_nolead SA, kxstepiii.ba_item SB, kxstepiii.ba_item_brandstyle bib, kxstepiii.ba_brand bb
                 WHERE sa.BOM_NO = sb.item_no
                   and bib.brand_no = bb.brand_no(+)
                   and sb.Item_No = bib.item_no
                   and SA.QUANTITY > 0
                   AND SA.SUBINV_CODE in ('24_BCP', 'HUB配套库(西丽工勘)')
                 group by SA.SUBINV_CODE, sb.item_no, sb.item_name, bb.brand_name,SB.IS_LEAD
            union all
                select SA.SUBINV_CODE SUBINVEBTORY,
                       sh.new_bom_no MATERIALCODE,
                       sh.bom_name MATERIALNAME,
                       sum(SA.QUANTITY) NUM,
                       sum(SA.NOLEAD_QTY) UNLEADNUM,
                       bb.brand_name BRANDNAME,
                       sh.is_lead ISLEAD
                  FROM lms.stockssummary_nolead SA, kxstepiii.ba_bom_head sh, kxstepiii.ba_item_brandstyle bib, kxstepiii.ba_brand bb
                 WHERE SA.bomitem_no = sh.new_bom_no
                   and bib.brand_no = bb.brand_no(+)
                   and sh.new_bom_no = bib.item_no
                   AND SA.QUANTITY > 0
                   AND SA.SUBINV_CODE in ('24_BCP', 'HUB配套库(西丽工勘)')
                 group by SA.SUBINV_CODE, sh.new_bom_no, sh.bom_name, bb.brand_name,sh.is_lead
    </select>


</mapper>
