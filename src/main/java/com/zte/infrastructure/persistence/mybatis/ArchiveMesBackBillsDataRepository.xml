<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.ArchiveMesBackBillsDataRepository">

    <resultMap id="ArchiveMesBackBillsMap" type="com.zte.interfaces.dto.ArchiveMesBackBillsDTO">
        <result column="BACK_BILL_ID" jdbcType="VARCHAR" property="backBillId" />
        <result column="BACK_NUMBER" jdbcType="VARCHAR" property="backNumber" />
        <result column="POST_BY" jdbcType="VARCHAR" property="postBy" />
        <result column="POST_DATE" jdbcType="VARCHAR" property="postDate" />
        <result column="CONTRACT_NUMBER" jdbcType="VARCHAR" property="contractNumber" />
        <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName" />
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
        <result column="PalletNo" jdbcType="VARCHAR" property="palletNo" />
        <result column="SiteAddress" jdbcType="VARCHAR" property="siteAddress" />
        <result column="PalletVolume" jdbcType="VARCHAR" property="palletVolume" />
        <result column="TotalWeight" jdbcType="VARCHAR" property="totalWeight" />
        <result column="BillNumber" jdbcType="VARCHAR" property="billNumber" />
        <result column="BoxesNumbers" jdbcType="VARCHAR" property="boxesNumbers" />
        <result column="EquipmentDesc" jdbcType="VARCHAR" property="equipmentDesc" />
        <result column="MfgSiteName" jdbcType="VARCHAR" property="mfgSiteName" />
        <result column="Cubage" jdbcType="VARCHAR" property="cubage" />
        <result column="GrossWeight" jdbcType="VARCHAR" property="grossWeight" />
    </resultMap>

    <select id="getByBackNumber" resultMap="ArchiveMesBackBillsMap"
            parameterType="java.lang.String">
            SELECT
                MBB.BACK_BILL_ID,
                MBB.BACK_NUMBER,
                MBB.POST_BY,
                MBB.POST_DATE,
                MBB.CREATED_BY,
                CCEB.ENTITY_NAME,
                CCEB.CONTRACT_NUMBER
            FROM
                APP_MES.MES_BACK_BILLS mbb,
                APP_MES.CPM_CONTRACT_ENTITIES_BOXUP_V cceb
            WHERE MBB.ENABLED_FLAG = 'Y'
            AND MBB.ENTITY_ID = CCEB.ENTITY_ID
            AND MBB.BACK_NUMBER = #{backNumber}
    </select>
    <select id="getListByBackBillid" resultMap="ArchiveMesBackBillsMap"
            parameterType="java.lang.String">
        SELECT
        f.PALLET_NO PalletNo,
        (g.LENGTH / 1000 * g.WIDTH / 1000 * f.CODE_HIGHT / 1000) PalletVolume,
        f.total_weight TotalWeight,
        B.BILL_NUMBER BillNumber,
        B.TOTAL_BOXES AS TotalPieces,
        B.BOX_NUMBER || '/' || B.TOTAL_BOXES AS BoxesNumbers,
        B.EQUIPMENT_DESC EquipmentDesc,
        D.MFG_SITE_NAME MfgSiteName,
        NVL(B.CUBAGE, 0) Cubage,
        NVL(B.GROSS_WEIGHT, 0) GrossWeight,
        SITE_ADDRESS SiteAddress,
        C.ENTITY_NAME
        FROM
        APP_MES.MES_BACK_BILL_LINES A
        INNER JOIN app_mes.CPM_BOXUP_BILLS B ON A.BILL_ID = B.BILL_ID AND A.ENABLED_FLAG = 'Y' AND B.ENABLED_FLAG = 'Y'
        INNER JOIN app_mes.CPM_CONTRACT_ENTITIES_BOXUP_V C ON B.ENTITY_ID = C.ENTITY_ID
        INNER JOIN app_mes.CPM_CONTRACT_MFG_SITES D ON B.MFG_SITE_ID = D.MFG_SITE_ID
        INNER JOIN app_mes.CPM_CONTRACT_SITES E ON D.SITE_ID = E.SITE_ID
        LEFT JOIN APP_MES.PALLET_SCAN_PACKING P ON P.BILL_NUMBER = B.Bill_Number AND P.Enabled_Flag = 'Y'
        LEFT JOIN app_mes.pallet_info_manage f ON P.PALLET_ID = F.PALLET_ID AND f.enabled_flag = 'Y'
        LEFT JOIN app_mes.mes_topbox_define g ON f.Box_Type_Code = g.Box_Type_Code AND g.enabled_flag = 'Y'
        WHERE A.BACK_BILL_ID = #{backBillId}
        ORDER BY
        B.BOX_NUMBER
    </select>
    <select id="getPageByDateRange" resultMap="ArchiveMesBackBillsMap"
            parameterType="com.zte.springbootframe.common.model.Page">
        SELECT
           DISTINCT MBB.BACK_BILL_ID,
            MBB.BACK_NUMBER
        FROM
            APP_MES.MES_BACK_BILLS mbb,
            APP_MES.CPM_CONTRACT_ENTITIES_BOXUP_V cceb
        WHERE
            MBB.ENABLED_FLAG = 'Y'
            AND MBB.BACK_STATUS = 'POSTED'
            AND MBB.ENTITY_ID = CCEB.ENTITY_ID
        AND MBB.LAST_UPDATE_DATE &gt;= #{params.startDate}
        AND MBB.LAST_UPDATE_DATE &lt;= #{params.endDate}
    </select>
</mapper>