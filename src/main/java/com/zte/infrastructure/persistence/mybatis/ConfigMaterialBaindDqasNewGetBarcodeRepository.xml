<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewGetBarcodeRepository">

    <resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="querySubBarCodeBoard">
        <result property="subBarcode" jdbcType="VARCHAR" column="SUB_BARCODE"/>
        <result property="itemCode" jdbcType="VARCHAR" column="ITEM_CODE"/>
    </resultMap>

    <resultMap id="getSubBarCodeAssemblyResultMap" type="com.zte.interfaces.dto.ConfigMaterialDTO">
        <result property="configBarcode1" jdbcType="VARCHAR" column="物料条码1"/>
        <result property="configBarcode2" jdbcType="VARCHAR" column="物料条码2"/>
        <result property="configBarcode3" jdbcType="VARCHAR" column="物料条码3"/>
        <result property="itemCode2" jdbcType="VARCHAR" column="物料代码2"/>
        <result property="itemCode3" jdbcType="VARCHAR" column="物料代码3"/>
    </resultMap>

    <select id="getSubBarCodeBoard" parameterType="Map" resultMap="querySubBarCodeBoard">
        SELECT to_char(t.board_planid) || trim(to_char(t.board_sn, '00000')) SUB_BARCODE, B.BOM_NO ITEM_CODE
        FROM KXBARIII.Scan_PartsToBoard t
        LEFT JOIN KXBARIII.PROD_PLAN P ON t.PARTS_PLANID = P.PRODPLAN_ID
        LEFT JOIN KXBARIII.BOARD B ON P.BOM_ID = B.BOM_ID
        WHERE
        <if test="partsPlanId != null">
            t.parts_planid = #{partsPlanId,jdbcType=INTEGER}
        </if>
        <if test="partsSn != null">
            AND t.parts_sn = #{partsSn,jdbcType=INTEGER}
        </if>
        <if test="(partsPlanId == null) and (partsSn == null)">
            and 1=2
        </if>
    </select>

    <select id="getSubBarCodeAssembly" statementType="CALLABLE" parameterType="map">
        {call kxbariii.PKG_ASSEMBLE_SCAN.PRO_QRY_ASSEMBLE_SCAN_INFO(
        #{map.PRODID,mode=IN,jdbcType=DECIMAL,javaType=java.math.BigDecimal},
        #{map.MAINBARCODE,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.SCANMAN,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.SCANSTARTDATE,mode=IN,jdbcType=DATE,javaType=java.sql.Date},
        #{map.SCANENDDATE,mode=IN,jdbcType=DATE,javaType=java.sql.Date},
        #{map.FLAG,mode=IN,jdbcType=INTEGER,javaType=java.lang.Long},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=getSubBarCodeAssemblyResultMap}
        )}
    </select>

</mapper>