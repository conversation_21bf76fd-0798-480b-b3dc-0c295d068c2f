<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ZmsStationInfoUploadRepository">

    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.ZmsStationInfoDTO">
        <result column="SERVER_SN" jdbcType="VARCHAR" property="serverSn"/>
        <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName"/>
        <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId"/>
        <result column="END_USER" jdbcType="VARCHAR" property="endUser"/>
        <result column="STATION_ID" jdbcType="VARCHAR" property="stationId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="ATTRIBUTE" jdbcType="VARCHAR" property="attribute"/>
        <result column="STATE" jdbcType="VARCHAR" property="state"/>
        <result column="START_TIME" jdbcType="INTEGER" property="startTime"/>
        <result column="END_TIME" jdbcType="INTEGER" property="endTime"/>
    </resultMap>

    <select id="getEntitySnList" resultMap="BaseResultMap">
        SELECT DISTINCT H.SERVER_SN, H.ORDER_ID, H.FACTORY_ORDER_ID AS ENTITY_NAME, H.CUSTOMER_NAME AS END_USER
        FROM SFC.ZMS_COMPLETE_MACHINE_HEAD H,sfc.cpm_contract_entities cce
        WHERE  cce.entity_name = h.factory_order_id
        <if test='autoFlag == "Y" ' >
            and cce.entity_status not in
            <foreach collection="entityStatus" item="sn" index="index" open="(" close=")" separator=",">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='autoFlag == "N" ' >
            and h.server_sn in
            <foreach collection="snList" item="sn" index="index" open="(" close=")" separator=",">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getZmsStationInfoList" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT (T.NAME || T.START_TIME) AS STATION_ID , T.* FROM (
        SELECT CASE WHEN WTH.SUB_STATUS = '装配' THEN '组装'
               WHEN WTH.SUB_STATUS = '检验' THEN '包装'
               ELSE WTH.SUB_STATUS END AS NAME,
               CASE WHEN WTH.SUB_STATUS = '装配' THEN '组装'
               WHEN WTH.SUB_STATUS = '检验' THEN '包装'
               ELSE WTH.SUB_STATUS END AS ATTRIBUTE,
        'PASS' AS STATE,
        TO_CHAR((MIN(WTH.LAST_UPDATE_DATE) - to_date('1970-01-01 08:00:00','yyyy-mm-dd hh24:mi:ss'))*86400) AS START_TIME,
        TO_CHAR((MAX(WTH.LAST_UPDATE_DATE) - to_date('1970-01-01 08:00:00','yyyy-mm-dd hh24:mi:ss'))*86400) AS END_TIME,
        CCE.ENTITY_NAME
        FROM WMES.WMES_ENTITY_TRACE_MODIFY_HIS WTH
        INNER JOIN WMES.CPM_CONTRACT_ENTITIES CCE ON WTH.ENTITY_ID = CCE.ENTITY_ID
        WHERE WTH.ENABLED_FLAG = 'Y'
        AND CCE.ENTITY_NAME IN
        <foreach collection="list" separator="," close=")" open="(" index="index" item="item">
            #{item.entityName, jdbcType=VARCHAR}
        </foreach>
        AND WTH.SUB_STATUS IN ('装配','检验')
        GROUP BY WTH.SUB_STATUS,cce.entity_name
        ) T
    </select>

    <insert id="insertOrUpdateZmsStationInfo" parameterType="java.util.List">
        MERGE INTO SFC.ZMS_STATION_INFO Z
        USING
        (
        <foreach collection ="list" item="item" index= "index" separator="union all">
            SELECT #{item.serverSn,jdbcType=VARCHAR} AS SERVER_SN,
                #{item.entityName,jdbcType=VARCHAR} AS ENTITY_NAME,
                #{item.orderId,jdbcType=VARCHAR} AS ORDER_ID,
                #{item.endUser,jdbcType=VARCHAR} AS END_USER,
                #{item.stationId,jdbcType=VARCHAR} AS STATION_ID,
                #{item.name,jdbcType=VARCHAR} AS NAME,
                #{item.attribute,jdbcType=VARCHAR} AS ATTRIBUTE,
                #{item.state,jdbcType=VARCHAR} AS STATE,
                #{item.startTime,jdbcType=INTEGER} AS START_TIME,
                #{item.endTime,jdbcType=INTEGER} AS END_TIME,
                #{item.problemId,jdbcType=VARCHAR} AS PROBLEM_ID,
                #{item.problemType,jdbcType=VARCHAR} AS PROBLEM_TYPE,
                #{item.manufacturer,jdbcType=VARCHAR} AS MANUFACTURER,
                #{item.componentSn,jdbcType=VARCHAR} AS COMPONENT_SN,
                #{item.originComponentPn,jdbcType=VARCHAR} AS ORIGIN_COMPONENT_PN,
                #{item.phenomenonType,jdbcType=VARCHAR} AS PHENOMENON_TYPE,
                #{item.phenomenonDetail,jdbcType=VARCHAR} AS PHENOMENON_DETAIL,
                #{item.reasonType,jdbcType=VARCHAR} AS REASON_TYPE,
                #{item.reasonDetail,jdbcType=VARCHAR} AS REASON_DETAIL,
                sysdate AS CREATE_DATE,
                #{item.createBy,jdbcType=VARCHAR} AS CREATE_BY,
                sysdate AS LAST_UPDATE_DATE,
                #{item.lastUpdateBy,jdbcType=VARCHAR} AS LAST_UPDATE_BY
                from dual
        </foreach>
        ) T
        ON (Z.SERVER_SN = t.SERVER_SN AND Z.ATTRIBUTE = T.ATTRIBUTE)
        WHEN MATCHED THEN
        UPDATE SET Z.ENTITY_NAME=T.ENTITY_NAME,
                Z.ORDER_ID=T.ORDER_ID,
                Z.END_USER=T.END_USER,
                Z.STATION_ID=T.STATION_ID,
                Z.NAME=T.NAME,
                Z.STATE=T.STATE,
                Z.START_TIME = T.START_TIME,
                Z.END_TIME = T.END_TIME,
                Z.PROBLEM_ID = T.PROBLEM_ID,
                Z.PROBLEM_TYPE = T.PROBLEM_TYPE,
                Z.MANUFACTURER = T.MANUFACTURER,
                Z.COMPONENT_SN = T.COMPONENT_SN,
                Z.ORIGIN_COMPONENT_PN = T.ORIGIN_COMPONENT_PN,
                Z.PHENOMENON_TYPE = T.PHENOMENON_TYPE,
                Z.PHENOMENON_DETAIL = T.PHENOMENON_DETAIL,
                Z.REASON_TYPE = T.REASON_TYPE,
                Z.REASON_DETAIL = T.REASON_DETAIL,
                Z.LAST_UPDATE_DATE = T.LAST_UPDATE_DATE,
                Z.LAST_UPDATE_BY = T.LAST_UPDATE_BY
        WHEN NOT MATCHED THEN
        INSERT (ID,SERVER_SN,ENTITY_NAME,ORDER_ID,END_USER,STATION_ID,NAME,ATTRIBUTE,STATE,START_TIME,END_TIME,PROBLEM_ID,PROBLEM_TYPE,
                MANUFACTURER,COMPONENT_SN,ORIGIN_COMPONENT_PN,PHENOMENON_TYPE,PHENOMENON_DETAIL,REASON_TYPE,REASON_DETAIL,CREATE_DATE,CREATE_BY)
                VALUES (SFC.SEQ_ZMS_STATION_INFO.NEXTVAL,T.SERVER_SN,T.ENTITY_NAME,T.ORDER_ID,T.END_USER,T.STATION_ID,T.NAME,T.ATTRIBUTE,T.STATE,T.START_TIME,T.END_TIME,T.PROBLEM_ID,T.PROBLEM_TYPE,
                T.MANUFACTURER,T.COMPONENT_SN,T.ORIGIN_COMPONENT_PN,T.PHENOMENON_TYPE,T.PHENOMENON_DETAIL,T.REASON_TYPE,T.REASON_DETAIL,T.CREATE_DATE,T.CREATE_BY)
    </insert>

</mapper>