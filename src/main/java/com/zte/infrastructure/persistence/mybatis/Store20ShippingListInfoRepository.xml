<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.Store20ShippingListInfoRepository">

    <resultMap id="FinalSelectResult" type="com.zte.domain.model.datawb.Store20ShippingListInfo">
        <result column="externOrderNum" jdbcType="VARCHAR" property="externOrderNum" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="itemName" jdbcType="VARCHAR" property="itemName" />
        <result column="itemCode" jdbcType="VARCHAR" property="itemCode" />
        <result column="batchNum" jdbcType="VARCHAR" property="batchNum" />
        <result column="partCode" jdbcType="VARCHAR" property="partCode" />
        <result column="useAge" jdbcType="VARCHAR" property="useAge" />
        <result column="project" jdbcType="VARCHAR" property="project" />
        <result column="userJobNumber" jdbcType="VARCHAR" property="userJobNumber" />
        <result column="userName" jdbcType="VARCHAR" property="userName" />
        <result column="targetSubStore" jdbcType="VARCHAR" property="targetSubStore" />
        <result column="outAccountDate" jdbcType="DATE" property="outAccountDate" />

    </resultMap>

    <select id="selectFinalResult"  resultMap="FinalSelectResult" parameterType="java.util.List">
        select s.externalorderkey2 externOrderNum,
        OP.Description      status,
        SK.DESCR            itemName,
        S.SKU               itemCode,
        s.lottable02        batchNum,
        PO.SERIALNUMBER     partCode,
        S.HREF04            useAge,
        S.HREF25            project,
        S.HREF33            userJobNumber,
        S.HREF34            userName,
        S.href08            targetSubStore,
        S.ADDDATE           outAccountDate
        from plugin.edi_so_s S
        JOIN WMWHSE6.ORDERS O
        ON S.Externalorderkey2 = O.Externalorderkey2
        and s.orderkey = o.orderkey
        JOIN WMWHSE6.ORDERDETAIL OD
        ON O.ORDERKEY = OD.ORDERKEY
        AND s.ORDERLINENUMBER = OD.ORDERLINENUMBER
        JOIN WMWHSE6.ORDERSTATUSSETUP OP
        ON OD.STATUS = OP.CODE
        JOIN WMWHSE6.SKU SK
        ON s.SKU = SK.SKU
        AND SK.STORERKEY = 'ZTE'
        JOIN PLUGIN.EDI_PCBSERIAL_OUT PO
        on po.externalorderkey2 = s.externalorderkey2
        AND PO.EXTERNLINENO = S.EXTERNLINENO
        and po.lottable02 = s.lottable02
        where S.href02 = '20_BCP'
        and S.href11 not in ('610', '611', '620', '621') and PO.Serialnumber in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>

    </select>

</mapper>
