<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.ArchiveBoqBoxMakeDataRepository">

    <resultMap id="resultMap" type="com.zte.interfaces.dto.ArchiveBoqBoxMakeDTO">
        <result column="ENTITY_ID" property="entityId" jdbcType="VARCHAR" />
        <result column="ENTITY_NAME" property="entityName" jdbcType="VARCHAR" />
        <result column="BILL_ID" property="billId" jdbcType="VARCHAR" />
        <result column="Organization_Id" property="organizationId" jdbcType="VARCHAR" />
        <result column="CONTRACT_NUMBER" property="contractNumber" jdbcType="VARCHAR" />
        <result column="CREATION_DATE" property="creationDate" jdbcType="VARCHAR" />
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
        <result column="BILL_NUMBER" property="billNumber" jdbcType="VARCHAR" />
        <result column="MFG_SITE_ID" property="mfgSiteId" jdbcType="VARCHAR" />
    </resultMap>
    
    <!--根据任务号查询boq装箱拟制-->
    <select id="getByEntityName" resultMap="resultMap"
            parameterType="java.lang.String">
         SELECT
            ENTITY.ENTITY_ID,
            ENTITY.ENTITY_NAME,
            CBB.CREATED_BY,
            CBB.CREATION_DATE,
            CBB.ORGANIZATION_ID,
            CBB.BILL_ID,
            CBB.BILL_NUMBER,
            CBB.MFG_SITE_ID,
            CCH.CONTRACT_NUMBER
        FROM
            app_mes.CPM_CONTRACT_ENTITIES ENTITY,
            app_mes.cdm_contract_lines CCL,
            app_mes.CDM_CONTRACT_HEADERS CCH,
            APP_MES.Cpm_Boxup_Bills CBB
        WHERE
            CBB.ENABLED_FLAG = 'Y'
            AND ENTITY.ENABLED_FLAG = 'Y'
            AND CCL.ENABLED_FLAG = 'Y'
            AND CCH.ENABLED_FLAG = 'Y'
            AND ENTITY.ENTITY_ID = CBB.ENTITY_ID
            AND ENTITY.contract_line_id = ccl.contract_line_id
            AND CCH.contract_header_id = CCL.contract_header_id
            AND CBB.BILL_ID = #{billId}
    </select>

    <!--根据时间范围查询归档数据-->
    <select id="getPageByDateRange" resultMap="resultMap"
            parameterType="com.zte.springbootframe.common.model.Page">
        SELECT
        DISTINCT CBB.BILL_ID,
        CBB.BILL_NUMBER
        FROM
        APP_MES.CPM_CONTRACT_ENTITIES ENTITY,
        APP_MES.CPM_BOXUP_BILLS CBB,
        app_mes.cpm_contract_mfg_sites ccms,
        app_mes.cpm_contract_sites ccs
        WHERE
        1 = 1
        AND entity.ENABLED_FLAG = 'Y'
        AND CBB.ENABLED_FLAG = 'Y'
        AND ccms.enabled_flag = 'Y'
        AND ccs.enabled_flag = 'Y'
        AND entity.ENTITY_ID = CBB.ENTITY_ID
        AND ccms.mfg_site_id = cbb.mfg_site_id
        AND ccms.site_id = ccs.site_id
        AND entity.ENTITY_STATUS = '40'
        AND CBB.BILL_NUMBER LIKE #{params.boxType}||'%'
        AND CBB.LAST_UPDATE_DATE &gt;= #{params.startDate}
        AND CBB.LAST_UPDATE_DATE &lt; #{params.endDate}
    </select>

    <!--单元箱列表-->
    <resultMap id="ArchiveBoqUnitBoxMakeResult" type="com.zte.interfaces.dto.ArchiveBoqUnitBoxMakeDTO">
        <result property="assembly" column="BOX_NUMBER" jdbcType="VARCHAR" />
        <result property="assemblyQty" column="ASSEMBLY_QTY" jdbcType="VARCHAR" />
        <result property="billNumber" column="BILL_NUMBER" jdbcType="VARCHAR" />
        <result property="billNumber" column="BILL_NUMBER" jdbcType="VARCHAR" />
        <result property="equipmentDesc" column="EQUIPMENT_DESC" jdbcType="VARCHAR" />
        <result property="mergeBillFlag" column="MERGE_BILL_FLAG" jdbcType="VARCHAR" />
        <result property="boxTypeCode" column="BOX_TYPE_CODE" jdbcType="VARCHAR" />
        <result property="length" column="LENGTH" jdbcType="VARCHAR" />
        <result property="width" column="WIDTH" jdbcType="VARCHAR" />
        <result property="height" column="HEIGHT" jdbcType="VARCHAR" />
        <result property="cubage" column="CUBAGE" jdbcType="VARCHAR" />
        <result property="memo" column="MEMO" jdbcType="VARCHAR" />
        <result property="attribute1" column="ATTRIBUTE1" jdbcType="VARCHAR" />
        <result property="boxTypeId" column="BOX_TYPE_ID" jdbcType="VARCHAR" />
    </resultMap>
    <!--根据组织id和生产站点id查询单元箱列表-->
    <select id="getArchiveUnitBoxMakeByBillId"
            resultMap="ArchiveBoqUnitBoxMakeResult">
        SELECT
         CV.BOX_NUMBER,
        (SELECT SUM(ENTITY1.ASSEMBLY_QTY) FROM
            app_mes.CPM_CONTRACT_ENTITIES entity1,
            app_mes.CPM_BOXUP_BILLS   CBBT1
         where ENTITY1.ENTITY_ID = CBBT1.ENTITY_ID
           AND CBBT1.ENABLED_FLAG = 'Y'
           AND ENTITY1.ENABLED_FLAG = 'Y'
           and CBBT1.MFG_SITE_ID=CV.MFG_SITE_ID) AS ASSEMBLY_QTY,
          CV.BILL_NUMBER,
          CV.EQUIPMENT_DESC,
          CV.MERGE_BILL_FLAG,
          CV.BOX_TYPE_CODE,
          CV.MEMO,
          CV.LENGTH,
          CV.WIDTH,
          CV.HEIGHT,
          CV.CUBAGE,
          CV.BOX_TYPE_ID,
          CV.ATTRIBUTE1
      FROM
      APP_MES.CPM_BOXUP_BILLS_BOQ_V CV
      WHERE
      CV.BILL_ID = #{billId}
      ORDER BY  CV.BOX_NUMBER
    </select>
    <!--根据组织id和单据id查询箱物料列表-->
    <resultMap id="ArchiveBoqBoxMakeItemResult" type="com.zte.interfaces.dto.ArchiveBoqBoxMakeItemDTO">
        <result property="levels" column="levels" jdbcType="VARCHAR" />
        <result property="itemCode" column="物料代码" jdbcType="VARCHAR" />
        <result property="itemName" column="物料名称" jdbcType="VARCHAR" />
        <result property="configQty" column="配置数量" jdbcType="VARCHAR" />
        <result property="boxQty" column="BOX_QTY" jdbcType="VARCHAR" />
        <result property="secondItemCode" column="二级物料代码" jdbcType="VARCHAR" />
        <result property="secondItemName" column="二级物料名称" jdbcType="VARCHAR" />
        <result property="memo" column="配置物料备注" jdbcType="VARCHAR" />
        <result property="unit" column="单位" jdbcType="VARCHAR" />
        <result property="importItemName" column="BOQ物料名称" jdbcType="VARCHAR" />
        <result property="boqItemNameEng" column="BOQ物料英文名称" jdbcType="VARCHAR" />
        <result property="cmsCode" column="CMS_CODE" jdbcType="VARCHAR" />
        <result property="englishItemName" column="物料英文名称" jdbcType="VARCHAR" />
        <result property="cnUnit" column="CN_UNIT" jdbcType="VARCHAR" />
        <result property="enUnit" column="EN_UNIT" jdbcType="VARCHAR" />
        <result property="beforeSallSiteId" column="BEFORESALLSITEID" jdbcType="VARCHAR" />
        <result property="isDangergoods" column="IS_DANGERGOODS" jdbcType="VARCHAR" />
        <result property="dangergoodsType" column="DANGERGOODSTYPE" jdbcType="VARCHAR" />
        <result property="batteryLabelType" column="BATTERY_LABELTYPE" jdbcType="VARCHAR" />
        <result property="productLabelName" column="PRODUCT_LABEL_NAME" jdbcType="VARCHAR" />
        <result property="configDetailId" column="CONFIG_DETAIL_ID" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getArchiveBoqBoxMakeItemByOrganizationIdAndBillId"
            resultMap="ArchiveBoqBoxMakeItemResult">
        SELECT 0 ROWCOUNT,
               (select decode(substr(BILL_NUMBER,1,1),'C',3,4) from APP_MES.CPM_BOXUP_BILLS where bill_id=CV.BILL_ID) levels,
               null RECORD_ID,
               0 Select_Flag,
               CV.BILL_ITEM_ID,
               CV.BILL_ID,
               (select decode(substr(BILL_NUMBER,1,1),'E',CCD.PARENT_CONFIG_DETAIL_ID,CV.CONFIG_DETAIL_ID) from APP_MES.CPM_BOXUP_BILLS where bill_id=CV.BILL_ID) as CONFIG_DETAIL_ID,
               CV.REISSUE_HEADER_ID,
               CV.REISSUE_LINE_ID,
               CV.INVENTORY_ITEM_ID ITEM_ID,
               CV.ORGANIZATION_ID,
               CV.ITEM_CODE 物料代码,
               CV.ITEM_NAME 物料名称,
               CV.BOX_NO,
               TO_NUMBER(DECODE(TO_CHAR(CV.SEQ), '0', '', TO_CHAR(CV.SEQ))) SEQ,
               ROUND(APP_MES.GETCONFIGQTYBYMFGSITE(CCD.MFG_SITE_ID,CCD.CONFIG_DETAIL_ID),2) 配置数量,
               CV.BOXUP_QTY BOX_QTY,
               CV.ENGLISH_ITEM_NAME 物料英文名称,
               CV.UNIT 单位,
               CV.ENABLED_FLAG,
               CV.MEMO 配置物料备注,
               CV.BARCODE,
               CCD.RELATION_IDENTIFIER,
               CCD.CODE CMS_CODE,
               CCD.IMPORT_ITEM_NAME BOQ物料名称,
               CCD.BOQ_ITEM_NAME_ENG BOQ物料英文名称,
               CCD.CN_UNIT,
               CCD.EN_UNIT,
               CCD.ENABLED_FLAG CCD_ENABLED_FLAG,
               DECODE( CV.IS_DANGERGOODS, 1, '是', '否') IS_DANGERGOODS,
               CV.DANGERGOODSTYPE,
               CV.BATTERY_LABELTYPE,
               CV.PRODUCT_LABEL_NAME
          FROM APP_MES.CPM_BOXUP_BILL_ITEMS_BOQ_V CV
          LEFT JOIN APP_MES.CPM_CONFIG_DETAILS CCD ON CV.ORGANIZATION_ID =
                                                      CCD.ORGANIZATION_ID
                                                  AND CV.CONFIG_DETAIL_ID =
                                                      CCD.CONFIG_DETAIL_ID
         WHERE CV.ORGANIZATION_ID = #{organizationId}
            AND CV.BILL_ID = #{billId}
         ORDER BY CCD.RELATION_IDENTIFIER
    </select>
    <select id="getFourMakeItem"
            resultMap="ArchiveBoqBoxMakeItemResult">
        SELECT
            0 ROWCOUNT,
            4 levels,
            0 Select_Flag,
            CBBID.BILL_ITEM_ID,
            CBBID.RECORD_ID,
            ROUND(APP_MES.GETCONFIGQTYBYMFGSITE(CCD.MFG_SITE_ID, CCD.CONFIG_DETAIL_ID), 2) 配置数量,
            CBBI.BOXUP_QTY,
            CBBID.BOX_NO,
            CBBID.ORGANIZATION_ID,
            CCD.CONFIG_DETAIL_ID,
            CCD.PARENT_CONFIG_DETAIL_ID,
            CBBID.BOXUP_QTY BOX_QTY,
            CBBID.INVENTORY_ITEM_ID ITEM_ID,
            B.SEGMENT1 物料代码,
            CBBID.ITEM_NAME 物料名称,
            CBBID.UNIT 单位,
            CBBID.ENGLISH_ITEM_NAME 物料英文名称,
            CCD.IMPORT_ITEM_NAME BOQ物料名称,
            CCD.BOQ_ITEM_NAME_ENG BOQ物料英文名称,
            CCD.CODE CMS_CODE,
            CBBID.MEMO 配置物料备注,
            CCD.ENABLED_FLAG,
            CCD.RELATION_IDENTIFIER,
            CCD.CN_UNIT,
            CCD.EN_UNIT,
            DECODE(CBBID.IS_DANGERGOODS, 1, '是', '否') IS_DANGERGOODS,
            CBBID.DANGERGOODSTYPE,
            CBBID.BATTERY_LABELTYPE,
            CBBID.PRODUCT_LABEL_NAME
        FROM
            APP_MES.MTL_SYSTEM_ITEMS B,
            APP_MES.CPM_CONFIG_DETAILS CCD,
            APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
            APP_MES.CPM_BOXUP_BILL_ITEMS CBBI
        WHERE
            CBBID.ORGANIZATION_ID = B.ORGANIZATION_ID
            AND CBBID.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
            AND CBBID.ENABLED_FLAG = 'Y'
            AND CBBI.ENABLED_FLAG = 'Y'
            AND CBBID.ORGANIZATION_ID = CBBI.ORGANIZATION_ID
            AND CBBID.BILL_ITEM_ID = CBBI.BILL_ITEM_ID
            AND CBBID.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID(+)
            AND CBBID.BILL_ITEM_ID = #{billItemId}
            AND CBBID.ORGANIZATION_ID = #{organizationId}
        ORDER BY
            RELATION_IDENTIFIER
    </select>
    <!--根据组织id和单据id查询箱物料列表-->
    <resultMap id="ArchiveBoxMakeItemResult"
               type="com.zte.interfaces.dto.ArchiveBoxMakeItemDTO">
        <result property="seq" column="SEQ" jdbcType="VARCHAR" />
        <result property="boxNo" column="BOX_NO" jdbcType="VARCHAR" />
        <result property="itemCode" column="ITEM_CODE" jdbcType="VARCHAR" />
        <result property="itemName" column="ITEM_NAME" jdbcType="VARCHAR" />
        <result property="boxupQty" column="BOXUP_QTY" jdbcType="VARCHAR" />
        <result property="unit" column="UNIT" jdbcType="VARCHAR" />
        <result property="englishItemName" column="ENGLISH_ITEM_NAME" jdbcType="VARCHAR" />
        <result property="memo" column="配置物料备注" jdbcType="VARCHAR" />
        <result property="siteAddr" column="SITE_ADDR" jdbcType="VARCHAR" />
        <result property="isDangergoods" column="IS_DANGERGOODS" jdbcType="VARCHAR" />
        <result property="dangergoodsType" column="DANGERGOODSTYPE" jdbcType="VARCHAR" />
        <result property="batteryLabelType" column="BATTERY_LABELTYPE" jdbcType="VARCHAR" />
        <result property="productLabelName" column="PRODUCT_LABEL_NAME" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getArchiveBoxMakeItemByOrganizationIdAndBillId"
            resultMap="ArchiveBoxMakeItemResult">
            SELECT
                   MSI.SEGMENT1 ITEM_CODE,
                   CBBIT.ITEM_NAME,
                   CBBIT.BOX_NO,
                   CBBIT.SEQ,
                   round(CBBIT.BOXUP_QTY,4) BOXUP_QTY,
                   CBBIT.ENGLISH_ITEM_NAME,
                   CBBIT.UNIT,
                   CBBIT.ENABLED_FLAG,
                   CBBIT.MEMO,
                   CBBIT.BARCODE,
                   CCD.PCONFIG_DETAIL_ID,
                   (SELECT CCS.SITE_ADDRESS || ',' || CCS.DISTRICT || ',' || CCS.CITY
                    FROM APP_MES.CPM_SALE_CONFIG_DETAILS CSD,
                         APP_MES.CPM_SITE_PTO            CSP,
                         APP_MES.CPM_CONTRACT_SITES      CCS
                    WHERE CSD.DEVICE_SITE_ID = CSP.DEVICE_SITE_ID(+)
                      AND CCS.SITE_ID = CSP.SITE_ID
                      AND CSD.ENABLED_FLAG = 'Y'
                      AND CSP.ENABLED_FLAG = 'Y'
                      AND CCS.ENABLED_FLAG = 'Y'
                      AND CSD.PCONFIG_DETAIL_ID = CCD.PCONFIG_DETAIL_ID
                      AND ROWNUM = 1) SITE_ADDR,
                   DECODE(CBBIT.Is_Dangergoods, 1, '是', '否') Is_Dangergoods,
                   CBBIT.Dangergoodstype,
                   CBBIT.Battery_Labeltype,
                   CBBIT.PRODUCT_LABEL_NAME
            FROM APP_MES.CPM_BOXUP_BILLS      CBBT,
                 APP_MES.CPM_BOXUP_BILL_ITEMS CBBIT,
                 APP_MES.MTL_SYSTEM_ITEMS          MSI,
                 APP_MES.CPM_CONFIG_DETAILS        CCD
            WHERE CBBIT.INVENTORY_ITEM_ID = MSI.INVENTORY_ITEM_ID
              AND CBBIT.ORGANIZATION_ID = MSI.ORGANIZATION_ID
              AND CBBIT.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID(+)
              AND CBBT.BILL_ID = CBBIT.BILL_ID
              AND CBBIT.ENABLED_FLAG = 'Y'
              AND CBBIT.ORGANIZATION_ID = #{organizationId}
              AND CBBIT.BILL_ID = #{billId}
           ORDER BY  SEQ,BOX_NO
    </select>

    <resultMap id="ArchiveEuUnitBoxMakeResult"
               type="com.zte.interfaces.dto.ArchiveEuUnitBoxMakeDTO">
        <result property="assembly" column="BOX_NUMBER" jdbcType="VARCHAR" />
        <result property="assemblyQty" column="ASSEMBLY_QTY" jdbcType="VARCHAR" />
        <result property="billNumber" column="BILL_NUMBER" jdbcType="VARCHAR" />
        <result property="equipmentDesc" column="EQUIPMENT_DESC" jdbcType="VARCHAR" />
        <result property="equipmentDescEn" column="EQUIPMENT_DESC_EN" jdbcType="VARCHAR" />
        <result property="euNo" column="EU_NO" jdbcType="VARCHAR" />
        <result property="euDesc" column="EU_DESC" jdbcType="VARCHAR" />
        <result property="euDescEn" column="EU_DESC_EN" jdbcType="VARCHAR" />
        <result property="euUnit" column="EU_UNIT" jdbcType="VARCHAR" />
        <result property="euUnitEn" column="EU_UNIT_EN" jdbcType="VARCHAR" />
        <result property="singleEuboxNum" column="SINGLE_EUBOX_NUM" jdbcType="VARCHAR" />
        <result property="euPosition" column="EU_POSITION" jdbcType="VARCHAR" />
        <result property="euQty" column="EU_QTY" jdbcType="VARCHAR" />
        <result property="mergeBillFlag" column="MERGE_BILL_FLAG" jdbcType="VARCHAR" />
        <result property="boxTypeCode" column="BOX_TYPE_CODE" jdbcType="VARCHAR" />
        <result property="length" column="LENGTH" jdbcType="VARCHAR" />
        <result property="width" column="WIDTH" jdbcType="VARCHAR" />
        <result property="height" column="HEIGHT" jdbcType="VARCHAR" />
        <result property="cubage" column="CUBAGE" jdbcType="VARCHAR" />
        <result property="memo" column="MEMO" jdbcType="VARCHAR" />
        <result property="attribute1" column="ATTRIBUTE1" jdbcType="VARCHAR" />
        <result property="boxTypeId" column="BOX_TYPE_ID" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getArchiveEuUnitBoxMakeByBillid"
            resultMap="ArchiveEuUnitBoxMakeResult">
        SELECT
            (SELECT SUM(ENTITY1.ASSEMBLY_QTY) FROM
                app_mes.CPM_CONTRACT_ENTITIES entity1,
                app_mes.CPM_BOXUP_BILLS   CBBT1
                where ENTITY1.ENTITY_ID = CBBT1.ENTITY_ID
                  AND CBBT1.ENABLED_FLAG = 'Y'
                  AND ENTITY1.ENABLED_FLAG = 'Y'
                  and CBBT1.MFG_SITE_ID=CBBT.MFG_SITE_ID) AS ASSEMBLY_QTY,
               CBBT.BOX_NUMBER,
               CBBT.BILL_NUMBER,
               CBBT.EQUIPMENT_DESC,
               CBBT.EQUIPMENT_DESC_EN,
               CBBT.EU_NO,
               CBBT.EU_DESC,
               CBBT.EU_DESC_EN,
               CBBT.EU_UNIT,
               CBBT.EU_UNIT_EN,
               CBBT.SINGLE_EUBOX_NUM,
               CBBT.EU_POSITION,
               CBBT.EU_QTY,
               CBBT.MERGE_BILL_FLAG,
               DECODE(CBBT.BILL_STATUS, '10', CBR.BOX_TYPE_CODE, CBT.BOX_TYPE_CODE) BOX_TYPE_CODE,
               CBBT.LENGTH,
               CBBT.WIDTH,
               CBBT.HEIGHT,
               CBBT.CUBAGE,
               CBBT.MEMO,
               CBBT.ATTRIBUTE1,
               CBBT.BOX_TYPE_ID
          FROM
               app_mes.CPM_CONTRACT_ENTITIES entity,
               app_mes.CPM_BOXUP_BILLS   CBBT,
               app_mes.MES_BOXUP_RULES        CBR,
               app_mes.MES_TOPBOX_DEFINE CBT,
               app_mes.CPM_CONTRACT_MFG_SITES CCMS
         WHERE
           ENTITY.ENTITY_ID = CBBT.ENTITY_ID
           AND CBBT.MFG_SITE_ID = CCMS.MFG_SITE_ID(+)
           AND CBBT.BOX_TYPE_ID = CBR.BOXUP_RULE_ID(+)
           AND CBBT.BOX_TYPE_ID = CBT.BOX_TYPE_ID(+)
           AND CBBT.ENABLED_FLAG = 'Y'
           AND ENTITY .ENABLED_FLAG = 'Y'
           AND CBBT.BILL_id = #{billId}
           ORDER BY CBBT.BOX_NUMBER
    </select>

    <select id="getArchiveEuBoxMakeItemByOrganizationIdAndBillId"
            resultMap="ArchiveBoqBoxMakeItemResult">
        SELECT
            CV.ITEM_CODE 物料代码,
            CV.ITEM_NAME 物料名称,
            ROUND(APP_MES.GETCONFIGQTYBYMFGSITE(CCD.MFG_SITE_ID, CCD.CONFIG_DETAIL_ID), 2) 配置数量,
            CV.BOXUP_QTY BOX_QTY,
            CV1.ITEM_CODE 二级物料代码,
            CV1.ITEM_NAME 二级物料名称,
            CV.MEMO 配置物料备注,
            CV.UNIT 单位,
            CCD.IMPORT_ITEM_NAME BOQ物料名称,
            CCD.BOQ_ITEM_NAME_ENG BOQ物料英文名称,
            CCD.CODE CMS_CODE,
            CV.ENGLISH_ITEM_NAME 物料英文名称,
            CCD.CN_UNIT,
            CCD.EN_UNIT,
            CV.IS_DANGERGOODS,
            CV.DANGERGOODSTYPE,
            CV.BATTERY_LABELTYPE,
            CV.BEFORESALLSITEID,
            CV.PRODUCT_LABEL_NAME
        FROM
            APP_MES.CPM_BOXUP_BILL_ITEMS_BOQ_V CV
        LEFT JOIN APP_MES.CPM_CONFIG_DETAILS CCD ON
            CV.ORGANIZATION_ID = CCD.ORGANIZATION_ID
            AND CV.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID
        LEFT JOIN APP_MES.CPM_CONFIG_DETAILS PCCD ON
            PCCD.CONFIG_DETAIL_ID = CCD.PARENT_CONFIG_DETAIL_ID
        LEFT JOIN APP_MES.CPM_BOXUP_BILL_ITEMS_BOQ_V CV1 ON
            CV1.ORGANIZATION_ID = PCCD.ORGANIZATION_ID
            AND CV1.CONFIG_DETAIL_ID = PCCD.CONFIG_DETAIL_ID
        WHERE
            CV.ORGANIZATION_ID = #{organizationId}
            AND CV.BILL_ID = #{billId}
        ORDER BY
            CCD.RELATION_IDENTIFIER
    </select>
    <select id="getSecondItem" resultMap="ArchiveBoqBoxMakeItemResult"
            parameterType="java.lang.String">
        SELECT msi.segment1 物料代码, msi.description 物料名称
          FROM app_mes.cpm_config_details ccd, app_mes.mtl_system_items msi
          where ccd.inventory_item_id = msi.inventory_item_id
          and ccd.organization_id = msi.organization_id
          and ccd.config_detail_id =
          (SELECT t.parent_config_detail_id
          FROM app_mes.cpm_config_details t
          where t.config_detail_id = #{configDetailId})
    </select>
</mapper>