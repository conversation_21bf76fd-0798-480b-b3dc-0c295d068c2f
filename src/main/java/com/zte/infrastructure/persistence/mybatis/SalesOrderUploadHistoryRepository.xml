<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.SalesOrderUploadHistoryRepository">

    <insert id="newSalesOrderUploadHistory">
        insert into APP_MES.SALES_ORDER_UPLOAD_HISTORY
        (CONTRACT_NUMBER, DATA_TRANSFER_BATCH_NO, STATUS, SALES_ORDER_TYPE_DESC)
        (<foreach collection ="contractNos" item="item" separator="union all">
            select
            #{item.salesOrderNo},
            #{transferBatchNo, jdbcType=VARCHAR},
            '0',
            #{item.salesOrderTypeDesc}
            from dual
        </foreach>)
    </insert>

    <update id="updateSalesOrderUploadStatus" parameterType="java.lang.String">
        update APP_MES.SALES_ORDER_UPLOAD_HISTORY
        set FINISH_DATE = sysdate,
            status = '1'
        where DATA_TRANSFER_BATCH_NO = #{transferBatchNo, jdbcType=VARCHAR}
    </update>
</mapper>