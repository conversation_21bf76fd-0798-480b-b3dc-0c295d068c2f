<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ConfigMaterialBaindDqasNewGetProductRepository">


<resultMap type="com.zte.interfaces.dto.ConfigMaterialDTO" id="queryProductPlaceNameByEntityId">
    <result property="productPlaceName" jdbcType="VARCHAR" column="PRODUCT_PLACE_NAME" />
</resultMap>

<select id="getProductPlaceNameByEntityId"  parameterType="java.lang.String"  resultMap="queryProductPlaceNameByEntityId">
    select
    PlaceHead.PRODUCT_PLACE_NAME PRODUCT_PLACE_NAME
    from wmes.CPM_CONTRACT_ENTITIES A,WMES.WMES_PRODUCT_PLACE_HEAD PlaceHead
    where A.ENABLED_FLAG = 'Y'
    AND PlaceHead.ENABLED_FLAG = 'Y'
    AND A.PRODUCT_PLACE_NO = PlaceHead.PRODUCT_PLACE_NO(+)
    <if test="entityName != null">
        AND A.ENTITY_NAME= #{entityName,jdbcType=VARCHAR}
    </if>

</select>
</mapper>