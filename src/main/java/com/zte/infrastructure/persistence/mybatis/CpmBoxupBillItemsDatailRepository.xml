<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.CpmBoxupBillItemsDatailRepository">

    <sql id="Base_Column_List" >
        CBBID.BILL_ITEM_ID as billItemId
        ,CBB.Bill_Id as billId
        ,CBB.BILL_NUMBER as billNo
        ,CBB.BILL_NUMBER as billNumber
        ,CBBID.INVENTORY_ITEM_ID as inventoryItemId
        ,MSI.SEGMENT1 as inventoryItem
        ,CBBID.item_name as itemName
        ,CBBID.BOXUP_QTY as boxupQty
        ,CBBID.ENGLISH_ITEM_NAME as englishItemName
        ,CBBI.UNIT as unit
        ,CBBID.CONFIG_DETAIL_ID as configDetailId
        ,CBBID.IS_DANGERGOODS as isDangergoods
        ,CBBID.DANGERGOODSTYPE as dangergoodstype
        ,CBBID.BATTERY_LABELTYPE as batteryLabeltype
        ,CCD.CN_UNIT as cnUnit
        ,CCD.EN_UNIT as enUnit
        <!--,CCD.BOQ_ITEM_NAME_ENG as enBoqItemName
          ,CCD.code as boqItemCode-->
        ,A.UNIT_OF_MEASURE_TL as boqUnit
        ,ccd.mfg_site_id as mfgSiteId
    </sql>

    <select id="getOtherInfoForBom" parameterType="java.lang.String" resultType="com.zte.domain.model.datawb.CpmBoxupBillItemsDatail">
        SELECT
        B.SEGMENT1  as inventoryItem,
        CCD.BOQ_ITEM_NAME_ENG as enBoqItemName,
        CCD.CODE   as boqItemCode
        FROM APP_MES.MTL_SYSTEM_ITEMS        B,
        APP_MES.MTL_SYSTEM_ITEMS        PTO,
        APP_MES.CPM_CONFIG_DETAILS      CCD,
        APP_MES.CPM_CONTRACT_SITES      SITE,
        APP_MES.CPM_CONTRACT_MFG_SITES  MFG,
        APP_MES.bom_standard_operations SEQ,
        APP_MES.CPM_PO_LINES            CPL
        WHERE CCD.ORGANIZATION_ID = B.ORGANIZATION_ID
        AND CCD.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
        AND CCD.ENABLED_FLAG = 'Y'
        AND SITE.ENABLED_FLAG = 'Y'
        AND MFG.ENABLED_FLAG = 'Y'
        AND MFG.PTO_INVENTORY_ITEM_ID = PTO.INVENTORY_ITEM_ID(+)
        AND MFG.ORGANIZATION_ID = PTO.ORGANIZATION_ID(+)
        AND CCD.MFG_SITE_ID = MFG.MFG_SITE_ID
        AND SEQ.STANDARD_OPERATION_ID(+) = CCD.standard_operation_id
        AND MFG.SITE_ID = SITE.SITE_ID
        AND CCD.PO_LINE_ID = CPL.PO_LINE_ID(+)
        AND MFG.MFG_SITE_ID = #{mfgSiteId,jdbcType=VARCHAR}
        ORDER BY CCD.RELATION_IDENTIFIER
    </select>

    <select id="getCusItemCodeForBom" parameterType="com.zte.interfaces.dto.MesSysInfoDTO" resultType="com.zte.domain.model.datawb.CpmBoxupBillItemsDatail">
        select
        cbb.bill_number billNumber,
        MSI.SEGMENT1 inventoryItem,
        case
        when CCD.PCONFIG_DETAIL_ID is null then
        app_mes.find_customer_item_code(CCH.CONTRACT_HEADER_ID,MSI.segment1)
        else
        cpl.ITEM_NUMBER
        end customerItemCode
        from APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS CBBI,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
        app_mes.cpm_contract_mfg_sites ccms,
        app_mes.cpm_contract_sites ccs,
        app_mes.CDM_CONTRACT_HEADERS CCH,
        APP_MES.CPM_CONFIG_DETAILS CCD,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.CPM_PO_LINES cpl
        where cbb.enabled_flag = 'Y'
        AND ccms.enabled_flag = 'Y'
        AND ccs.enabled_flag = 'Y'
        AND CCH.enabled_flag = 'Y'
        AND ccms.mfg_site_id = cbb.mfg_site_id
        AND ccms.site_id = ccs.site_id
        AND ccs.contract_header_id =  CCH.CONTRACT_HEADER_ID
        and CBBI.BILL_ID = CBB.BILL_ID(+)
        and CBBI.BILL_ITEM_ID = CBBID.BILL_ITEM_ID(+)
        and CBBI.ORGANIZATION_ID = CCD.ORGANIZATION_ID(+)
        and CBBI.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID(+)
        and CBBID.inventory_item_id = MSI.inventory_item_id(+)
        AND CCD.PO_LINE_ID = CPL.PO_LINE_ID(+)
        and cbb.bill_number in
        <foreach collection="packNo"  item="item"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="findGbomInfoByBillIds" parameterType="com.zte.interfaces.dto.MesSysInfoDTO" resultType="com.zte.domain.model.datawb.CpmBoxupBillItemsDatail">
        SELECT
        <include refid="Base_Column_List"/>
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS CBBI,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
        APP_MES.CPM_CONFIG_DETAILS CCD,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.MTL_UNITS_OF_MEASURE_TL A
        where 1=1
        and CBBI.BILL_ID = CBB.BILL_ID(+)
        and CBBI.BILL_ITEM_ID = CBBID.BILL_ITEM_ID(+)
        and CBBI.ORGANIZATION_ID = CCD.ORGANIZATION_ID(+)
        and CBBI.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID(+)
        and CBBID.inventory_item_id = MSI.inventory_item_id(+)
        and CBBID.enabled_flag = 'Y'
        and MSI.enabled_flag(+) = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and A.LANGUAGE = 'ZHS'
        and A.UNIT_OF_MEASURE = MSI.PRIMARY_UNIT_OF_MEASURE
        AND CBB.BILL_NUMBER IN
        <foreach collection="packNo"  item="item"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getCount" parameterType="com.zte.interfaces.dto.MesSysInfoDTO" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS CBBI,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
        APP_MES.CPM_CONFIG_DETAILS CCD,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.MTL_UNITS_OF_MEASURE_TL A
        where 1=1
        and CBBI.BILL_ID = CBB.BILL_ID(+)
        and CBBI.BILL_ITEM_ID = CBBID.BILL_ITEM_ID(+)
        and CBBI.ORGANIZATION_ID = CCD.ORGANIZATION_ID(+)
        and CBBI.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID(+)
        and CBBID.inventory_item_id = MSI.inventory_item_id(+)
        and CBBID.enabled_flag = 'Y'
        and MSI.enabled_flag(+) = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and A.LANGUAGE = 'ZHS'
        and A.UNIT_OF_MEASURE = MSI.PRIMARY_UNIT_OF_MEASURE
        AND CBB.BILL_NUMBER IN
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
            <foreach collection="packNo" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
    </select>

    <select id="getList" parameterType="com.zte.interfaces.dto.MesSysInfoDTO" resultType="com.zte.domain.model.datawb.CpmBoxupBillItemsDatail">
        <if test="startRow != null and endRow != null">
            select * from (select u.*, rownum r from (
        </if>
        SELECT
        <include refid="Base_Column_List"/>
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS CBBI,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
        APP_MES.CPM_CONFIG_DETAILS CCD,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.MTL_UNITS_OF_MEASURE_TL A
        where 1=1
        and CBBI.BILL_ID = CBB.BILL_ID(+)
        and CBBI.BILL_ITEM_ID = CBBID.BILL_ITEM_ID(+)
        and CBBI.ORGANIZATION_ID = CCD.ORGANIZATION_ID(+)
        and CBBI.CONFIG_DETAIL_ID = CCD.CONFIG_DETAIL_ID(+)
        and CBBID.inventory_item_id = MSI.inventory_item_id(+)
        and CBBID.enabled_flag = 'Y'
        and MSI.enabled_flag(+) = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and A.LANGUAGE = 'ZHS'
        and A.UNIT_OF_MEASURE = MSI.PRIMARY_UNIT_OF_MEASURE
        AND CBB.BILL_NUMBER IN
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
            <foreach collection="packNo" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>

        <if test="startRow != null and endRow != null">
            <![CDATA[
			    ) u where rownum <= #{endRow}) where r >= #{startRow}
			]]>
        </if>
    </select>



    <select id="getTransCount" parameterType="com.zte.interfaces.dto.MesSysInfoDTO"  resultType="java.lang.Integer">
        select count(*) from(
        select
        '' as dgItemCode,
        CBBID.BOXUP_QTY as boxupQty,
        msi.segment1 itemNo,
        msi.description itemName,
        PIM.Pallet_No palletNo,
        CBBID.IS_DANGERGOODS as isDangergoods,
        CBBID.DANGERGOODSTYPE as  dangergoodstype,
        CBBID.BATTERY_LABELTYPE as batteryLabeltype,
        CBB.Bill_Number packNo
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS CBBI,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.PALLET_SCAN_PACKING PSP,
        APP_MES.PALLET_INFO_MANAGE PIM
        where
        CBBI.BILL_ID = CBB.BILL_ID
        and CBBI.BILL_ITEM_ID = CBBID.BILL_ITEM_ID
        and CBBID.inventory_item_id = MSI.inventory_item_id
        and CBBID.Organization_Id=MSI.Organization_Id
        and cbb.bill_number=psp.bill_number(+)
        AND PSP.PALLET_ID = PIM.PALLET_ID(+)
        and psp.enabled_flag(+)='Y'
        and pim.enabled_flag(+)='Y'
        and CBBID.enabled_flag = 'Y'
        and MSI.enabled_flag = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and CBB.BILL_NUMBER like 'C%'
        AND (CBB.BILL_NUMBER IN
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
            <foreach collection="packNo" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
        )
        union all
        select
        '' as dgItemCode,
        CBBI.Boxup_Qty as boxupQty ,
        msi.segment1  itemNo,
        msi.description itemName,
        PIM.Pallet_No palletNo,
        CBBI.IS_DANGERGOODS  as isDangergoods ,
        CBBI.DANGERGOODSTYPE as dangergoodstype,
        CBBI.BATTERY_LABELTYPE as batteryLabeltype,
        CBB.Bill_Number packNo
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS   CBBI,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.PALLET_SCAN_PACKING PSP,
        APP_MES.PALLET_INFO_MANAGE PIM
        where
        CBBI.BILL_ID = CBB.BILL_ID
        and cbbi.inventory_item_id = MSI.inventory_item_id
        and cbbi.organization_id=MSI.Organization_Id
        and cbb.bill_number=psp.bill_number(+)
        AND PSP.PALLET_ID = PIM.PALLET_ID(+)
        and psp.enabled_flag(+)='Y'
        and pim.enabled_flag(+)='Y'
        and MSI.enabled_flag = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and (CBB.BILL_NUMBER like 'B%' or CBB.Bill_Number like 'E%')
        AND (CBB.BILL_NUMBER IN
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
            <foreach collection="packNo" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
        )
    )
    </select>

    <select id="getTransList" parameterType="com.zte.interfaces.dto.MesSysInfoDTO" resultType="com.zte.domain.model.datawb.CpmBoxupBillItemsDatailTrans">
        <if test="startRow != null and endRow != null">
            select * from (select u.*, rownum r from (
        </if>
        select * from (SELECT
        decode(substr(MSI.SEGMENT1,0,1),'1',
        (select to_char(wm_concat(
        distinct
        ctv.item_no))
        from
        APP_MES.PART_DANGER_ITEM ctv
        where ctv.part_no=MSI.segment1
        )
        ,MSI.segment1
        ) as dgItemCode,
        CBBID.BOXUP_QTY as boxupQty,
        msi.segment1 itemNo,
        msi.description itemName,
        PIM.Pallet_No palletNo,
        CBBID.IS_DANGERGOODS as isDangergoods,
        CBBID.DANGERGOODSTYPE as  dangergoodstype,
        CBBID.BATTERY_LABELTYPE as batteryLabeltype,
        cBB.Bill_Number packNo,
        CBBID.Record_Id as BILL_ITEM_ID
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS CBBI,
        APP_MES.CPM_BOXUP_BILL_ITEMS_DATAIL CBBID,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.PALLET_SCAN_PACKING PSP,
        APP_MES.PALLET_INFO_MANAGE PIM
        where
        CBBI.BILL_ID = CBB.BILL_ID
        and CBBI.BILL_ITEM_ID = CBBID.BILL_ITEM_ID
        and CBBID.inventory_item_id = MSI.inventory_item_id
        and CBBID.Organization_Id=MSI.Organization_Id
        and cbb.bill_number=psp.bill_number(+)
        AND PSP.PALLET_ID = PIM.PALLET_ID(+)
        and psp.enabled_flag(+)='Y'
        and pim.enabled_flag(+)='Y'
        and CBBID.enabled_flag = 'Y'
        and MSI.enabled_flag = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and CBB.BILL_NUMBER like 'C%'
        AND (CBB.BILL_NUMBER IN
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">  <!-- 表示删除最后一个条件 -->
            <foreach collection="packNo" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
        )
        union all
        SELECT
        decode(substr(MSI.SEGMENT1,0,1),'1',
        (select to_char(wm_concat(
        distinct
        ctv.item_no))
        from
        APP_MES.PART_DANGER_ITEM ctv
        where ctv.part_no=MSI.segment1
        )
        ,MSI.segment1
        ) as dgItemCode,
        CBBI.Boxup_Qty as boxupQty ,
        msi.segment1  itemNo,
        msi.description itemName,
        PIM.Pallet_No palletNo,
        CBBI.IS_DANGERGOODS  as isDangergoods ,
        CBBI.DANGERGOODSTYPE as dangergoodstype,
        CBBI.BATTERY_LABELTYPE as batteryLabeltype,
        CBB.Bill_Number packNo,
        CBBI.BILL_ITEM_ID
        from
        APP_MES.CPM_BOXUP_BILLS CBB,
        APP_MES.CPM_BOXUP_BILL_ITEMS   CBBI,
        APP_MES.MTL_SYSTEM_ITEMS MSI,
        APP_MES.PALLET_SCAN_PACKING PSP,
        APP_MES.PALLET_INFO_MANAGE PIM
        where
        CBBI.BILL_ID = CBB.BILL_ID
        and cbbi.inventory_item_id = MSI.inventory_item_id
        and cbbi.organization_id=MSI.Organization_Id
        and cbb.bill_number=psp.bill_number(+)
        AND PSP.PALLET_ID = PIM.PALLET_ID(+)
        and psp.enabled_flag(+)='Y'
        and pim.enabled_flag(+)='Y'
        and MSI.enabled_flag = 'Y'
        and CBBI.ENABLED_FLAG = 'Y'
        and CBB.ENABLED_FLAG = 'Y'
        and (CBB.BILL_NUMBER like 'B%' or CBB.Bill_Number like 'E%')
        AND (CBB.BILL_NUMBER IN
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">  <!-- 表示删除最后一个条件 -->
            <foreach collection="packNo" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
        )
        ) order by BILL_ITEM_ID desc nulls last
        <if test="startRow != null and endRow != null">
        <![CDATA[
			    ) u where rownum <= #{endRow}) where r >= #{startRow}
			]]>
        </if>
    </select>
</mapper>
