<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.JobsSupplierProduceDataRepository">
  
  <resultMap id="RelResultMap" type="com.zte.domain.model.datawb.OemProduceSkipGoodsQuery">
    <result property="contractNumber"   jdbcType="VARCHAR" column="CONTRACT_NUMBER" />
    <result property="customerAddress"  jdbcType="VARCHAR" column="CUSTOMER_ADDRESS" />
    
    <result property="frInsID"   		jdbcType="VARCHAR"   column="FR_INS_ID" />
    <result property="frInsNO"          jdbcType="VARCHAR" column="FR_INS_NO" />
    <result property="issueDate"  		jdbcType="TIMESTAMP" column="ISSUE_DATE" />
    
    <result property="billNumber" 		jdbcType="VARCHAR"   column="BILL_NUMBER" />
    <result property="itemName"  		jdbcType="VARCHAR" column="ITEM_NAME" />
    <result property="itemNo"  			jdbcType="VARCHAR" column="ITEM_NO" />
    
    <result property="barCode"  		jdbcType="VARCHAR" column="ITEM_BARCODE" />
  
  
    <result column="ITEM_REVISION" jdbcType="VARCHAR" property="itemRevision" />
    <result column="DSN_NUMBER" jdbcType="VARCHAR" property="dsnNumber" />
    <result column="WIRELESS_NAME1" jdbcType="VARCHAR" property="wirelessName1" />
    <result column="WIRELESS_PASSWORD1" jdbcType="VARCHAR" property="wirelessPassword1" />
    <result column="WIRELESS_NAME2" jdbcType="VARCHAR" property="wirelessName2" />
    <result column="WIRELESS_PASSWORD2" jdbcType="VARCHAR" property="wirelessPassword2" />
    <result column="ACCESS_ADDRESS" jdbcType="VARCHAR" property="accessAddress" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="TERMINAL_PASSWORD" jdbcType="VARCHAR" property="terminalPassword" />
    <result column="EQUIPMENT_NAME" jdbcType="VARCHAR" property="equipmentName" />
    <result column="MAC_ADDRESS1" jdbcType="VARCHAR" property="macAddress1" />
    <result column="MAC_ADDRESS2" jdbcType="VARCHAR" property="macAddress2" />
    <result column="MAC_ADDRESS3" jdbcType="VARCHAR" property="macAddress3" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />    
    <result column="PRODUCE_DATE" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="IMPORT_DATE" jdbcType="TIMESTAMP" property="importDate" />
    <result column="SOFT_VERSION" jdbcType="VARCHAR" property="softVersion" />
    <result column="HARD_VERSION" jdbcType="VARCHAR" property="hardVersion" />
    <result column="RATED_VOLTAGE" jdbcType="VARCHAR" property="ratedVoltage" />
    <result column="RATED_CURRENT" jdbcType="VARCHAR" property="ratedCurrent" />
    <result column="BUY_BATCH_NO" jdbcType="VARCHAR" property="buyBatchNo" />
    <result column="CRAFTS_NO" jdbcType="VARCHAR" property="craftsNo" />
    <result column="PRESERVED1" jdbcType="VARCHAR" property="preserved1" />
    <result column="PRESERVED2" jdbcType="VARCHAR" property="preserved2" />
    <result column="PRESERVED3" jdbcType="VARCHAR" property="preserved3" />
    <result column="PRESERVED4" jdbcType="VARCHAR" property="preserved4" />
    <result column="PRESERVED5" jdbcType="VARCHAR" property="preserved5" />
    <result column="NET_ACCESS_SIGN_NUM" jdbcType="VARCHAR" property="netAccessSignNum" />
    <result column="SCRAMBLE_CODE" jdbcType="VARCHAR" property="scrambleCode" />
    <result column="NASN" jdbcType="VARCHAR" property="nasn" />
    <result column="MADEIN" jdbcType="VARCHAR" property="madeIn" />
  </resultMap>
  <sql id="Table_Name">
    JOBS_SUPPLIER_PRODUCE_DATA
  </sql>
  <sql id="Columns">
    DSN_NUMBER, WIRELESS_NAME1, ITEM_REVISION,ITEM_BARCODE,
    WIRELESS_PASSWORD1, WIRELESS_NAME2, WIRELESS_PASSWORD2, ACCESS_ADDRESS, USER_NAME, 
    TERMINAL_PASSWORD, EQUIPMENT_NAME, MAC_ADDRESS1, MAC_ADDRESS2, MAC_ADDRESS3,  
    REMARK, PRODUCE_DATE,  IMPORT_DATE, 
    SOFT_VERSION, HARD_VERSION, 
    RATED_VOLTAGE, RATED_CURRENT, BUY_BATCH_NO, CRAFTS_NO, PRESERVED1, PRESERVED2, PRESERVED3, 
    PRESERVED4, PRESERVED5, NET_ACCESS_SIGN_NUM, SCRAMBLE_CODE, NASN, MADEIN
  </sql>
   <sql id="Condtions">    
    <include refid="Table_Name" />.ENABLED_FLAG = 'Y'  
  </sql>

  <!-- 
         根据条码查询外协单据信息
       注意： 该模块在使用的时候，条码已经分页
   @auto 曹亮6055000032 
 

 <select id="getOemQueryList" parameterType="java.util.List" resultMap="RelResultMap">   
   <foreach item="item" collection="list" separator=" union  all "  index="index">
    SELECT <include refid="Columns" />
            ,'${index}' FR_INS_ID
         	
    FROM   <include refid="Table_Name" />
    WHERE  <include refid="Condtions" /> 
           AND <include refid="Table_Name" />.ITEM_BARCODE = '${item.barCode}'
    </foreach> 
     
  </select>  -->   
 <select id="getOemQueryList" parameterType="java.lang.String" resultMap="RelResultMap">   
    SELECT <include refid="Columns" />          
    FROM   <include refid="Table_Name" />
    WHERE  <include refid="Condtions" /> 
        AND <include refid="Table_Name" />.ITEM_BARCODE in (${barCode})
  </select>

  <select id="getColumnList" resultType="java.lang.String">
    SELECT column_name
    FROM all_tab_columns
    WHERE table_name = 'JOBS_SUPPLIER_PRODUCE_DATA'
  </select>
  <select id="getProductSumList" resultType="com.zte.interfaces.dto.sfc.PilotProductParamVO">
    select
    CONTRACT_NUMBER entityName ,
    WORK_UNIT workUnit,
    #{paramName} as paramName,
    ${paramName} as paramValue
    from SFC.JOBS_SUPPLIER_PRODUCE_DATA  t
    where
    t.ENABLED_FLAG = 'Y'
    <if test='paramType =="2"'>
      and ${paramName}  in
      <foreach collection="paramList" item="item" separator=','
               open='(' close=')' index="index">
        #{item}
      </foreach>
    </if>
    <if test='paramType =="1"'>
      and  ${paramName}  between #{paramStart} and  #{paramEnd}
    </if>

  </select>

  <select id="getProductSumMacList" resultType="com.zte.interfaces.dto.sfc.PilotProductParamVO">
    select
    t.CONTRACT_NUMBER entityName,
    t.WORK_UNIT workUnit,
    #{paramName} as paramName,
    tm.${paramName} as paramValue
    from SFC.JOBS_SUPPLIER_PRODUCE_DATA_MAC  tm
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA t  on tm. RECORD_ID  = t.RECORD_ID
    where
    t.ENABLED_FLAG = 'Y'
    <if test='paramType =="2"'>
      and tm.${paramName}  in
      <foreach collection="paramList" item="item" separator=','
               open='(' close=')' index="index">
        #{item}
      </foreach>
    </if>
    <if test='paramType =="1"'>
      and  tm.${paramName}  between #{paramStart} and  #{paramEnd}
    </if>
  </select>

  <!-- 发货数量查询-->
  <select id="getCpeSnCount" parameterType="java.util.List" resultType="com.zte.domain.model.datawb.StbProductSnCount">
    SELECT
    T4.snStart,
    COUNT(1) AS count
    FROM (
    <foreach item="snStart" collection="snStartList" separator=" UNION ALL ">
      SELECT #{snStart,jdbcType=VARCHAR} snStart from dual
    </foreach>
    ) T4
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA_TAC T1 ON T1.SN LIKE T4.snStart || '%'
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA T2 ON T1.RECORD_ID = T2.RECORD_ID
    JOIN SFC.PKG_SUBMAC_SCAN_INFO_V T3 ON T2.item_barcode = T3.item_barcode
    WHERE T1.ENABLED_FLAG = 'Y'
    AND T2.ENABLED_FLAG = 'Y'
    AND T3.ACTION_DATE >= TRUNC(SYSDATE - #{daysAgoStart,jdbcType=INTEGER})
    AND T3.ACTION_DATE &lt;  TRUNC(SYSDATE - #{daysAgoEnd,jdbcType=INTEGER})
    GROUP BY T4.snStart
  </select>

  <!-- 发货数量查询 -->
  <select id="getCpeSnCountByItemBarcodes" parameterType="java.util.Map" resultType="com.zte.domain.model.datawb.StbProductSnCount">
    SELECT
    T4.snStart,
    COUNT(1) AS count
    FROM (
    <foreach item="snStart" collection="snStartList" separator=" UNION ALL ">
      SELECT #{snStart,jdbcType=VARCHAR} snStart from dual
    </foreach>
    ) T4
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA_TAC T1 ON T1.SN LIKE T4.snStart || '%'
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA T2 ON T1.RECORD_ID = T2.RECORD_ID
    WHERE T1.ENABLED_FLAG = 'Y'
    AND T2.ENABLED_FLAG = 'Y'
    AND T2.item_barcode IN
    <foreach collection="itemBarcodes" item="item" separator=',' open='(' close=')'>
      #{item,jdbcType=VARCHAR}
    </foreach>
    GROUP BY T4.snStart
  </select>

  <select id="getCpeSnListBySnStart" resultType="java.lang.String">
    SELECT t1.SN
    FROM SFC.JOBS_SUPPLIER_PRODUCE_DATA_TAC T1
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA T2 ON T1.RECORD_ID = T2.RECORD_ID
    JOIN sfc.pkg_submac_scan_info scan_info ON T2.item_barcode = scan_info.item_barcode
    JOIN sfc.wsm_contract_machine_info con_mac ON con_mac.submachine_id = scan_info.submachine_id
    left JOIN sfc.cpm_boxup_bills_scan box_bill ON scan_info.bill_id = box_bill.bill_id AND box_bill.scan_flag = 105
    WHERE
    T1.ENABLED_FLAG = 'Y'
    AND T2.ENABLED_FLAG = 'Y'
    AND scan_info.enabled_flag = 'Y'
    AND T1.SN LIKE #{snStart,jdbcType=VARCHAR} || '%'
    AND con_mac.ACTION_DATE >= TRUNC(SYSDATE - #{daysAgoStart,jdbcType=INTEGER})
    AND con_mac.ACTION_DATE &lt; TRUNC(SYSDATE - #{daysAgoEnd,jdbcType=INTEGER})
    UNION ALL
    SELECT t1.SN
    FROM SFC.JOBS_SUPPLIER_PRODUCE_DATA_TAC T1
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA T2 ON T1.RECORD_ID = T2.RECORD_ID
    JOIN
    ( SELECT
    DECODE (a.item_series, NULL, a.item_barcode, a.item_series ) item_barcode , a.wip_entity_name, a.item_id
    FROM sfc.wsm_complement_scan_info a where a.enabled_flag = 'Y') wcsi
    ON T2.item_barcode = wcsi.item_barcode
    JOIN sfc.wsm_contract_machine_info_v con_mac ON wcsi.wip_entity_name = con_mac.submachine_number
    JOIN sfc.bas_items_info e ON wcsi.item_id = e.item_id
    WHERE
    T1.ENABLED_FLAG = 'Y'
    AND T2.ENABLED_FLAG = 'Y'
    AND e.enabled_flag = 'Y'
    AND e.organization_id = 1
    AND T1.SN LIKE  #{snStart,jdbcType=VARCHAR} || '%'
    AND con_mac.ACTION_DATE >= TRUNC(SYSDATE - #{daysAgoStart,jdbcType=INTEGER})
    AND con_mac.ACTION_DATE &lt; TRUNC(SYSDATE - #{daysAgoEnd,jdbcType=INTEGER})
  </select>

  <select id="getCpeSnListByItemBarcodes" resultType="java.lang.String">
    SELECT T1.SN
    from SFC.JOBS_SUPPLIER_PRODUCE_DATA_TAC T1
    JOIN SFC.JOBS_SUPPLIER_PRODUCE_DATA T2 ON T1.RECORD_ID = T2.RECORD_ID
    WHERE T1.ENABLED_FLAG = 'Y'
    AND T2.ENABLED_FLAG = 'Y'
    AND T1.SN LIKE #{snStart,jdbcType=VARCHAR}  || '%'
    AND T2.item_barcode IN
    <foreach collection="itemBarcodes" item="item" separator=',' open='(' close=')'>
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
</mapper>
