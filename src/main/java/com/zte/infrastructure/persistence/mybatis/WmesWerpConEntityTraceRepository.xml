<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.WmesWerpConEntityTraceRepository">
  <resultMap id="wmesWerpConEntityTraceMap" type="com.zte.domain.model.datawb.WmesWerpConEntityTrace">
      <result column="生产计划周期" jdbcType="DOUBLE" property="planningCycle" />
      <result column="生产计调周期" jdbcType="DOUBLE" property="meteringCycle" />
      <result column="生产周期" jdbcType="DOUBLE" property="productionCycle" />
      <result column="任务号ID" jdbcType="DECIMAL" property="entityId" />
      <result column="任务号" jdbcType="VARCHAR" property="entityName" />
      <result column="产品大类" jdbcType="VARCHAR" property="productType" />
      <result column="设备类型" jdbcType="VARCHAR" property="mfgSiteType" />
      <result column="任务数量" jdbcType="DECIMAL" property="taskNumber" />
      <result column="当日日期" jdbcType="TIMESTAMP" property="dateOfDay" />
  </resultMap>
  <resultMap id="contractCharacterizationInfoMap" type="com.zte.interfaces.dto.ContractCharacterizationInfoDTO">
      <result property="id" column="ID" />
      <result property="deliverSetNum" column="DELIVER_SET_NUM" />
      <result property="innerOrderNumber" column="INNER_ORDER_NUMBER" />
      <result property="entityName" column="ENTITY_NAME" />
      <result property="overallUnitName" column="OVERALL_UNIT_NAME" />
      <result property="overallCount" column="OVERALL_COUNT" />
      <result property="statusName" column="STATUS_NAME" />
      <result property="orderLevel" column="ORDER_LEVEL" />
      <result property="contractNumber" column="CONTRACT_NUMBER" />
      <result property="customerAddr" column="CUSTOMER_ADDR" />
      <result property="contractAbDeliveryDate" column="CONTRACT_AB_DELIVERY_DATE" />
      <result property="boqFlag" column="BOQ_FLAG" />
      <result property="completionDate" column="COMPLETION_DATE" />
      <result property="remark01" column="REMARK_01" />
      <result property="remark02" column="REMARK_02" />
      <result property="remark03" column="REMARK_03" />
      <result property="planningGroupDesc" column="PLANNING_GROUP_DESC" />
      <result property="productType" column="PRODUCT_TYPE" />
      <result property="productStype" column="PRODUCT_STYPE" />
      <result property="joinLine" column="JOIN_LINE" />
      <result property="equipMethod" column="EQUIP_METHOD" />
      <result property="entityStatus" column="ENTITY_STATUS" />
      <result property="equipNowdesc" column="EQUIP_NOWDESC" />
      <result property="s20ByName" column="S20_BY_NAME" />
      <result property="productDispatcher" column="PRODUCT_DISPATCHER" />
      <result property="assembleMan" column="ASSEMBLE_MAN" />
      <result property="checkMan" column="CHECK_MAN" />
      <result property="qccheckMan" column="QCCHECK_MAN" />
      <result property="packlistMan" column="PACKLIST_MAN" />
      <result property="willProductDate" column="WILL_PRODUCT_DATE" />
      <result property="taskStartdate" column="TASK_STARTDATE" />
      <result property="scheduledStartDate" column="SCHEDULED_START_DATE" />
      <result property="receiveDate" column="RECEIVE_DATE" />
      <result property="aimDate01" column="AIM_DATE01" />
      <result property="assembleEnddate" column="ASSEMBLE_ENDDATE" />
      <result property="aimDate02" column="AIM_DATE02" />
      <result property="checkEnddate" column="CHECK_ENDDATE" />
      <result property="qccheckEnddate" column="QCCHECK_ENDDATE" />
      <result property="aimDate03" column="AIM_DATE03" />
      <result property="packStartdate" column="PACK_STARTDATE" />
      <result property="indate" column="INDATE" />
      <result property="workQty" column="WORK_QTY" />
      <result property="packlistDate" column="PACKLIST_DATE" />
      <result property="expectedWarehousingDate" column="EXPECTED_WAREHOUSING_DATE" />
      <result property="s10Date" column="S10_DATE" />
      <result property="s20Date" column="S20_DATE" />
      <result property="s30Date" column="S30_DATE" />
      <result property="s40Date" column="S40_DATE" />
      <result property="planDate" column="PLAN_DATE" />
      <result property="contractDemandDate" column="CONTRACT_DEMAND_DATE" />
      <result property="contractIssueDate" column="CONTRACT_ISSUE_DATE" />
      <result property="contractType" column="CONTRACT_TYPE" />
      <result property="contractAttribute" column="CONTRACT_ATTRIBUTE" />
      <result property="checkInfo" column="CHECK_INFO" />
      <result property="qccheckInfo" column="QCCHECK_INFO" />
      <result property="qccheckPassflag" column="QCCHECK_PASSFLAG" />
      <result property="qccheckRemainflag" column="QCCHECK_REMAINFLAG" />
      <result property="assembleStartdate" column="ASSEMBLE_STARTDATE" />
      <result property="entityId" column="ENTITY_ID" />
      <result property="organizationId" column="ORGANIZATION_ID" />
      <result property="creationDate" column="CREATION_DATE" />
      <result property="priorityLevel" column="PRIORITY_LEVEL" />
      <result property="deliverShipmentMethod" column="DELIVER_SHIPMENT_METHOD" />
      <result property="directSendFlag" column="DIRECT_SEND_FLAG" />
      <result property="aimDate05" column="AIM_DATE05" />
      <result property="assemblyDescInfo" column="ASSEMBLY_DESC_INFO" />
      <result property="assemblyRealBeginDate" column="ASSEMBLY_REAL_BEGIN_DATE" />
      <result property="debugRealBeginDate" column="DEBUG_REAL_BEGIN_DATE" />
      <result property="checkRealBeginDate" column="CHECK_REAL_BEGIN_DATE" />
      <result property="packRealBeginDate" column="PACK_REAL_BEGIN_DATE" />
      <result property="memo" column="MEMO" />
      <result property="specialRequest" column="SPECIAL_REQUEST" />
      <result property="demandPackDate" column="DEMAND_PACK_DATE" />
      <result property="productPlaceName" column="PRODUCT_PLACE_NAME" />
      <result property="isTerminal" column="IS_TERMINAL" />
      <result property="kitDate" column="KIT_DATE" />
      <result property="packPlanMemo" column="PACK_PLAN_MEMO" />
      <result property="completeSetDate" column="COMPLETE_SET_DATE" />
      <result property="dispatchMemo" column="DISPATCH_MEMO" />
      <result property="mfgSiteType" column="MFG_SITE_TYPE" />
      <result property="attribute1" column="ATTRIBUTE1" />
      <result property="attribute2" column="ATTRIBUTE2" />
      <result property="attribute3" column="ATTRIBUTE3" />
      <result property="createBy" column="CREATE_BY" />
      <result property="createDate" column="CREATE_DATE" />
      <result property="lastUpdatedBy" column="LAST_UPDATED_BY" />
      <result property="lastUpdatedDate" column="LAST_UPDATED_DATE" />
      <result property="enabledFlag" column="ENABLED_FLAG" />
      <result property="orgId" column="ORG_ID" />
      <result property="factoryId" column="FACTORY_ID" />
      <result property="mfgSiteBigType" column="MFG_SITE_BIG_TYPE" />
  </resultMap>
  
  <!-- 计算计调周期，生产周期，计划周期    TO_CHAR(SYSDATE,'YYYY/MM') -->
  <select id="selectWmesOutputCycleByM" parameterType="com.zte.domain.model.datawb.WmesWerpConEntityTrace" resultMap="wmesWerpConEntityTraceMap">
    SELECT
	ROUND(SUM(DECODE(SIGN(F.WILL_PRODUCT_DATE-D.S20_DATE),1,F.WILL_PRODUCT_DATE-D.S20_DATE,0))/COUNT(T.ENTITY_NUMBER),2) 生产计划周期,
	ROUND(SUM(DECODE(SIGN(F.TASK_STARTDATE-F.WILL_PRODUCT_DATE),1,F.TASK_STARTDATE-F.WILL_PRODUCT_DATE,0))/COUNT(T.ENTITY_NUMBER),2) 生产计调周期,
	ROUND(SUM(DECODE(SIGN(D.S30_DATE-F.TASK_STARTDATE),1,D.S30_DATE-F.TASK_STARTDATE,0))/COUNT(T.ENTITY_NUMBER),2) 生产周期,
	COUNT(T.ENTITY_NUMBER) 任务数量
	FROM CMS_DELIVER_ENTITY_INFO T
	LEFT JOIN
	WERP_CON_ENTITY_TRACE F
	ON 
	T.ENTITY_NUMBER = F.ENTITY_NAME
	LEFT JOIN
	WMES_CON_ACTIONS_SUB D
	ON
	F.ENTITY_ID=D.SUBMACHINE_ID 
	WHERE
	T.MFG_SITE_TYPE LIKE CONCAT(CONCAT('%',#{mfgSiteType}),'%')
	AND
	F.PRODUCT_TYPE = #{productType}
	AND 
	TO_CHAR(D.S30_DATE,'YYYY/MM') = '2017/10'   
  </select>
  <!-- 计算按日的生产周期，  SELECT TO_DATE('2017/10/31','YYYY/MM/DD')-ROWNUM+1 allday FROM DUAL CONNECT BY LEVEL  <= TO_CHAR(TO_DATE('2017/10/31','YYYY/MM/DD'),'DD') -->
  <!-- SELECT (last_day(SYSDATE)-ROWNUM+1) AS allday FROM DUAL CONNECT BY LEVEL <![CDATA[ <= ]]> TO_CHAR(last_day(SYSDATE),'DD') -->
  <select id="selectWmesOutputCycleByD" parameterType="com.zte.domain.model.datawb.WmesWerpConEntityTrace" resultMap="wmesWerpConEntityTraceMap">
  	SELECT 
  	CALDAY.allday 当日日期,
  	CASE 
          WHEN COUNT(RECORD.ENTITY_NAME) <![CDATA[ <> ]]> 0 THEN
    ROUND(SUM(DECODE(SIGN(RECORD.S30_DATE-RECORD.TASK_STARTDATE),1,RECORD.S30_DATE-RECORD.TASK_STARTDATE,0))/COUNT(RECORD.ENTITY_NAME),2)
          ELSE
    ROUND(SUM(DECODE(SIGN(RECORD.S30_DATE-RECORD.TASK_STARTDATE),1,RECORD.S30_DATE-RECORD.TASK_STARTDATE,0)),2)
          END 生产周期,
  	COUNT(RECORD.ENTITY_NAME) 任务数量 
  	FROM 
  	(
  		 SELECT TO_DATE('2017/10/31','YYYY/MM/DD')-ROWNUM+1 allday 
  		 FROM DUAL CONNECT BY LEVEL <![CDATA[ <= ]]> TO_CHAR(TO_DATE('2017/10/31','YYYY/MM/DD'),'DD')
  	) CALDAY 
  	LEFT JOIN 
  	(
  		SELECT 
  		D.S20_DATE,F.WILL_PRODUCT_DATE,F.TASK_STARTDATE,F.ENTITY_NAME,D.S30_DATE 
  		FROM 
  		WMES_CON_ACTIONS_SUB D 
  		LEFT JOIN 
  		WERP_CON_ENTITY_TRACE F 
  		ON 
  		F.ENTITY_ID=D.SUBMACHINE_ID 
  		LEFT JOIN 
  		CMS_DELIVER_ENTITY_INFO T 
  		ON 
  		T.ENTITY_NUMBER = F.ENTITY_NAME 
  		WHERE 
  		T.MFG_SITE_TYPE LIKE CONCAT(CONCAT('%',#{mfgSiteType}),'%')  
  		AND 
  		F.PRODUCT_TYPE = #{productType}
  	) RECORD 
  	ON 
  	TO_CHAR(CALDAY.allday,'YYYY/MM/DD') = TO_CHAR(RECORD.S30_DATE,'YYYY/MM/DD') 
  	GROUP BY CALDAY.allday 
  	ORDER BY CALDAY.allday
  </select>
  
  <!-- 查询合同任务表征数据条数 -->
  <select id="getContractCharaInfoCount" parameterType="com.zte.interfaces.dto.ContractCharaInfoQueryDTO" resultType="java.lang.Integer">
  	SELECT count(*) FROM
      wmes.WERP_CON_ENTITY_TRACE S,
      wmes.CPM_CONTRACT_ENTITIES A,
      WMES.CDM_CONTRACT_LINES ccl,
      WMES.CMS_DELIVER_ENTITY_INFO CDEI,
      WMES.CDM_CONTRACT_HEADERS CCH,
      wmes.WMES_CON_ACTIONS_SUB wcas,
      WMES.WMES_PRODUCT_PLACE_HEAD PlaceHead,
      WMES.WMES_INNERORDER_TRACK  orderTrack,
      WMES.CPM_INNER_ORDER_HEADERS orderHead,
      WMES.CDM_DELIVER_PLANS deliverPlan,
      WMES.CDM_DELIVER_SETS  deliverSet ,
      WMES.WERP_ENTITY_PKG_PLAN_APD x,
      wmes.wip_discrete_jobs wdj,
      wmes.Task_Class_Terminal tas,
      WMES.EXE_ORDER_PRIORITY EOP,
      WMES.BAS_LOOKUP_VALUES_VL BLV,
      messys.sys_lookup_values SLV
      WHERE 1=1 
      <if test="startTime != null">
      	and wcas.S30_DATE >= #{startTime}
      </if>
      <if test="endTime != null">
      	and wcas.S30_DATE &lt; #{endTime}
      </if>
      <if test="inStatusNames != null">
      	and S.STATUS_NAME in (${inStatusNames})
      </if>
      	and wcas.SUBMACHINE_ID=a.ENTITY_ID(+)
        and A.ENTITY_ID = S.ENTITY_ID(+)
        and A.ENTITY_NAME=tas.ENTITY_NUMBER(+)
        and A.contract_line_id=ccl.contract_line_id(+)
        and A.ENTITY_NAME = CDEI.ENTITY_NUMBER(+)
        <!-- WMES删除后也需删除imes数据使用 -->
        <!-- and a.ENABLED_FLAG = 'Y' -->
        and CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID(+)
        and A.PRODUCT_PLACE_NO = PlaceHead.PRODUCT_PLACE_NO(+)
        and orderHead.INNER_ORDER_HEADER_ID = orderTrack.INNER_ORDER_HEADER_ID(+)
        and a.INNER_ORDER_HEADER_ID = orderHead.INNER_ORDER_HEADER_ID(+)
        and deliverSet.DELIVER_SET_ID  = deliverPlan.DELIVER_SET_ID(+)
        and orderHead.DELIVER_SET_ID = deliverSet.DELIVER_SET_ID(+)
        and deliverPlan.ENABLED_FLAG(+) = 'Y'
        and orderTrack.ENABLED_FLAG(+) = 'Y'
        and orderHead.ENABLED_FLAG(+) = 'Y'
        and deliverSet.ENABLED_FLAG(+) = 'Y'
        and a.WIP_ENTITY_ID = wdj.WIP_ENTITY_ID(+)
        and A.ORGANIZATION_ID =wdj.ORGANIZATION_ID(+)
        AND x.ENTITY_ID(+) = s.ENTITY_ID
        AND deliverSet.DELIVER_SET_NUM=EOP.DELIVER_SET_NUM(+)
        AND BLV.enabled_flag(+) = 'Y'
        AND BLV.LOOKUP_TYPE(+) = 'CDM_EQUIPMENT_TYPE'
        AND A.Mfg_Site_Type=BLV.lookup_code(+)
        and SLV.Enabled_Flag(+) = 'Y'
        and slv.LOOKUP_TYPE(+) = 7090001
        and a.organization_id=slv.lookup_meaning(+)
      
  </select>
  
  <!-- 查询合同任务表征数据 -->
  <select id="getContractCharaInfo" parameterType="com.zte.interfaces.dto.ContractCharaInfoQueryDTO" resultMap="contractCharacterizationInfoMap">
  	<if test="page != null and page > 0 and rows != null and rows > 0">
  		select * from (select u.*, rownum r from (
  	</if>
  	SELECT  
      	deliverSet.DELIVER_SET_NUM,
	    orderHead.INNER_ORDER_NUMBER,
	    A.ENTITY_NAME,
      	S.STATUS_NAME,
	    EOP.ORDER_LEVEL,
	    CCH.CONTRACT_NUMBER,
	    wmes.Find_Customer_Name(cch.CONTRACT_HEADER_ID) as CUSTOMER_ADDR,
	    ccl.BOQ_FLAG,
	    A.COMPLETION_DATE,
	    S.REMARK_01,
      	S.REMARK_02,
      	S.REMARK_03,
    	S.PLANNING_GROUP_DESC,
      	S.PRODUCT_TYPE,
      	S.PRODUCT_STYPE,
      	S.JOIN_LINE,
      	S.EQUIP_METHOD,
      	DECODE(A.ENTITY_STATUS,10,'生成',15,'配置完成',20,'排产完成',30,'入库完成',40,'发货完成',50,'开通完成',60,'初验完成',70,'终验完成') AS ENTITY_STATUS,
	    S.EQUIP_NOWDESC,
      	WCAS.S20_BY_NAME,
      	DIS.PRODUCT_DISPATCHER,
      	S.ASSEMBLE_MAN,
      	S.CHECK_MAN,
      	S.QCCHECK_MAN,
      	S.PACKLIST_MAN,
      	S.WILL_PRODUCT_DATE,
      	S.TASK_STARTDATE,
    	wdj.scheduled_start_date,
      	S.RECEIVE_DATE,
      	S.AIM_DATE01 as AIM_DATE01,
      	S.ASSEMBLE_ENDDATE,
      	S.AIM_DATE02 as AIM_DATE02,
      	S.CHECK_ENDDATE,
      	S.QCCHECK_ENDDATE,
      	S.AIM_DATE03 as AIM_DATE03,
		S.PACK_STARTDATE,
      	S.INDATE,
      	S.WORK_QTY,
      	S.PACKLIST_DATE as PACKLIST_DATE,
      	wdj.SCHEDULED_COMPLETION_DATE as EXPECTED_WAREHOUSING_DATE,
    	wcas.S10_DATE,
      	wcas.S20_DATE,
      	wcas.S30_DATE,
      	wcas.S40_DATE,
    	x.plan_date,
      	wmes.Find_Demand_Date(ccl.CONTRACT_HEADER_ID) as CONTRACT_DEMAND_DATE,
      	WMES.FIND_CONTRACT_ISSUE_DATE(CCH.CONTRACT_HEADER_ID,orderHead.DELIVER_SET_ID) as CONTRACT_ISSUE_DATE,
      	wmes.Find_Contract_Type_Name(ccl.CONTRACT_HEADER_ID) as CONTRACT_TYPE,
      	wmes.Find_Contract_Attribute_Name(ccl.CONTRACT_HEADER_ID) as CONTRACT_ATTRIBUTE,
    	S.CHECK_INFO,
      	S.QCCHECK_INFO,
      	S.QCCHECK_PASSFLAG,
      	S.QCCHECK_REMAINFLAG,
      	S.ASSEMBLE_STARTDATE,
    	A.ENTITY_ID,
      	S.ORGANIZATION_ID,
      	X.CREATION_DATE,
      	wmes.Find_Entity_Priority_Level(A.PRIORITY_LEVEL) as PRIORITY_LEVEL,  
      	decode(nvl(wmes.Find_deliver_Shipment_Method(deliverSet.DELIVER_SET_ID),'0'),'0',wmes.Find_Contract_Shipment_Method(ccl.Contract_Header_id),wmes.Find_deliver_Shipment_Method(deliverSet.DELIVER_SET_ID)) as DELIVER_SHIPMENT_METHOD,
    	DECODE(X.DIRECT_SEND_FLAG,'是','Y','否','N',null,'N') as DIRECT_SEND_FLAG,
    	S.AIM_DATE05,
    	S.ASSEMBLY_DESC_INFO,
      	S.ASSEMBLY_REAL_BEGIN_DATE,
      	S.DEBUG_REAL_BEGIN_DATE,
      	S.CHECK_REAL_BEGIN_DATE,
      	S.PACK_REAL_BEGIN_DATE,
    	A.MEMO,
      	deliverPlan.BEMAND_PACK_DATE,
    	PlaceHead.PRODUCT_PLACE_NAME,
    	tas.IS_TERMINAL,
    	S.KIT_DATE,
    	S.PACK_PLAN_MEMO,
    	S.COMPLETE_SET_DATE as COMPLETE_SET_DATE,
      	S.DISPATCH_MEMO,
      	a.ENABLED_FLAG as ENABLED_FLAG,
    	BLV.meaning as MFG_SITE_TYPE
      FROM
	      wmes.WERP_CON_ENTITY_TRACE S,
	      wmes.CPM_CONTRACT_ENTITIES A,
	      WMES.CDM_CONTRACT_LINES ccl,
	      WMES.CMS_DELIVER_ENTITY_INFO CDEI,
	      WMES.CDM_CONTRACT_HEADERS CCH,
	      wmes.WMES_CON_ACTIONS_SUB wcas,
	      WMES.WMES_PRODUCT_PLACE_HEAD PlaceHead,
	      WMES.WMES_INNERORDER_TRACK  orderTrack,
	      WMES.CPM_INNER_ORDER_HEADERS orderHead,
	      WMES.CDM_DELIVER_PLANS deliverPlan,
	      WMES.CDM_DELIVER_SETS  deliverSet ,
	      WMES.WERP_ENTITY_PKG_PLAN_APD x,
	      wmes.wip_discrete_jobs wdj,
	      wmes.Task_Class_Terminal tas,
	      WMES.EXE_ORDER_PRIORITY EOP,
	      (SELECT
	          b.enabled_flag,
	          b.lookup_type, b.lookup_code, b.meaning, b.description,b.LANGUAGE
	     	FROM BAS_LOOKUP_VALUES b
	     	WHERE b.LANGUAGE = 'ZHS') BLV,
	      messys.sys_lookup_values SLV,
	      wmes.WMES_PRODUCT_DISPATCHER DIS
      WHERE 2=2
      	  <if test="minEntityId != null">
	      	and A.ENTITY_ID >= #{minEntityId}
	      </if>
	      <if test="maxEntityId != null">
	      	and A.ENTITY_ID &lt;= #{maxEntityId}
	      </if>
	      <if test="inEntityId != null">
	      	and A.ENTITY_ID in (${inEntityId})
	      </if>
          <if test="s30DateIsNull != null and s30DateIsNull == true">
          	<!-- 排产日期为两年前任务不再统计 -->
          	and WCAS.S20_DATE > SYSDATE - 2 * 365
   			and wcas.S30_DATE is null
	      </if>
	      <if test="s30DateIsNull == null or s30DateIsNull == false">
	   		<if test="startTime != null">
	      		and wcas.S30_DATE >= #{startTime}
	      	</if>
	      	<if test="endTime != null">
	      		and wcas.S30_DATE &lt; #{endTime}
	      	</if>
	      </if>
          
	      <if test="inStatusNames != null and inStatusNames != ''">
	      	and S.STATUS_NAME in (${inStatusNames})
	      </if>
	      and wcas.SUBMACHINE_ID=a.ENTITY_ID(+)
          and A.ENTITY_ID = S.ENTITY_ID(+)
        and A.ENTITY_NAME=tas.ENTITY_NUMBER(+)
        and A.contract_line_id=ccl.contract_line_id(+)
        and A.ENTITY_NAME = CDEI.ENTITY_NUMBER(+)
        <!-- WMES删除后也需删除imes数据使用 -->
        <!-- and a.ENABLED_FLAG = 'Y' -->
        and CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID(+)
        and A.PRODUCT_PLACE_NO = PlaceHead.PRODUCT_PLACE_NO(+)
        and orderHead.INNER_ORDER_HEADER_ID = orderTrack.INNER_ORDER_HEADER_ID(+)
        and a.INNER_ORDER_HEADER_ID = orderHead.INNER_ORDER_HEADER_ID(+)
        and deliverSet.DELIVER_SET_ID  = deliverPlan.DELIVER_SET_ID(+)
        and orderHead.DELIVER_SET_ID = deliverSet.DELIVER_SET_ID(+)
        and deliverPlan.ENABLED_FLAG(+) = 'Y'
        and orderTrack.ENABLED_FLAG(+) = 'Y'
        and orderHead.ENABLED_FLAG(+) = 'Y'
        and deliverSet.ENABLED_FLAG(+) = 'Y'
        and a.WIP_ENTITY_ID = wdj.WIP_ENTITY_ID(+)
        and A.ORGANIZATION_ID =wdj.ORGANIZATION_ID(+)
        AND x.ENTITY_ID(+) = s.ENTITY_ID
        AND deliverSet.DELIVER_SET_NUM=EOP.DELIVER_SET_NUM(+)
         AND BLV.enabled_flag(+) = 'Y'
         AND BLV.LOOKUP_TYPE(+) = 'CDM_EQUIPMENT_TYPE'
        AND A.Mfg_Site_Type=BLV.lookup_code(+)
        and SLV.Enabled_Flag(+) = 'Y'
        and slv.LOOKUP_TYPE(+) = 7090001
        and S.PRODUCT_TYPE = DIS.PRODUCT_TYPE(+)
        and a.organization_id=slv.lookup_meaning(+)
	      
	      <!-- 排序会影响查询速度,查询过慢时可考虑删除 -->
	      <!-- order by A.ENTITY_NAME -->
	      <if test="page != null and page > 0 and rows != null and rows > 0">
  			) u where rownum &lt;= (${page} * ${rows})) where r > ((${page} - 1) * ${rows})
	  	  </if>
  </select>
  
  <!-- 合同任务表征数据根据rownum分页获取每页entity_id区间（用于优化后续查询数据效率） -->
  <select id="getMinAndMaxEntityIdWithPage" parameterType="com.zte.interfaces.dto.ContractCharaInfoQueryDTO" resultType="java.util.Map">
  	SELECT PAGE as page, min(Entity_Id) as minId, max(Entity_Id) as maxId
	  from (select FLOOR(rownum / ${rows}) PAGE, ENTITY_ID
	          FROM (
               SELECT A.Entity_Id
                 FROM wmes.WERP_CON_ENTITY_TRACE S,
                       wmes.CPM_CONTRACT_ENTITIES A,
                       wmes.WMES_CON_ACTIONS_SUB  wcas
                WHERE 1 = 1
                <!-- erp入库日为空(查询未入库任务) -->
                <if test="s30DateIsNull != null and s30DateIsNull == true">
                	<!-- 排产日期为两年前任务不再统计 -->
                	and WCAS.S20_DATE > SYSDATE - 2 * 365
			   		and wcas.S30_DATE is null
			    </if>
			    <if test="s30DateIsNull == null or s30DateIsNull == false">
			   		and wcas.S30_DATE >= #{startTime}
		      		and wcas.S30_DATE &lt; #{endTime}
			    </if>
			    <if test="inStatusNames != null and inStatusNames != ''">
			   		and S.STATUS_NAME in (${inStatusNames})
			    </if>
			    and wcas.SUBMACHINE_ID = a.ENTITY_ID(+)
			    <!-- WMES删除后也需删除imes数据使用 -->
			    <!-- and a.ENABLED_FLAG = 'Y' -->
              	and A.ENTITY_ID = S.ENTITY_ID(+)
              	order by A.Entity_Id)) S
	 GROUP BY S.PAGE
	 ORDER BY PAGE
  </select>
  
  <!-- 查询合同任务表征数据ID列表 -->
  <select id="getEntityIdList" parameterType="com.zte.interfaces.dto.ContractCharaInfoQueryDTO" resultType="java.lang.String">
  	SELECT  
        A.ENTITY_ID
      FROM
        wmes.WERP_CON_ENTITY_TRACE S,
        wmes.CPM_CONTRACT_ENTITIES A,
        WMES.CMS_DELIVER_ENTITY_INFO CDEI,
        wmes.WMES_CON_ACTIONS_SUB wcas,
        WMES.WMES_INNERORDER_TRACK  orderTrack,
        WMES.CPM_INNER_ORDER_HEADERS orderHead,
        WMES.CDM_DELIVER_PLANS deliverPlan,
        WMES.CDM_DELIVER_SETS  deliverSet ,
        wmes.Task_Class_Terminal tas,
        WMES.EXE_ORDER_PRIORITY EOP,
        messys.sys_lookup_values SLV
      WHERE 2=2
         
   		and A.ENTITY_ID >= #{minEntityId}
   		and A.ENTITY_ID &lt;= #{maxEntityId}
        <if test="s30DateIsNull != null and s30DateIsNull == true">
         	<!-- 排产日期为两年前任务不再统计 -->
         	and WCAS.S20_DATE > SYSDATE - 2 * 365
  			and wcas.S30_DATE is null
      	</if>
      	<if test="s30DateIsNull == null or s30DateIsNull == false">
	   		<if test="startTime != null">
	      		and wcas.S30_DATE >= #{startTime}
	      	</if>
	      	<if test="endTime != null">
	      		and wcas.S30_DATE &lt; #{endTime}
	      	</if>
      	</if>
         
      	<if test="inStatusNames != null and inStatusNames != ''">
      		and S.STATUS_NAME in (${inStatusNames})
      	</if>
         
        and wcas.SUBMACHINE_ID=a.ENTITY_ID(+)
        and A.ENTITY_ID = S.ENTITY_ID(+)
        and A.ENTITY_NAME=tas.ENTITY_NUMBER(+)
        and A.ENTITY_NAME = CDEI.ENTITY_NUMBER(+)
        <!-- WMES删除后也需删除imes数据使用 -->
        <!-- and a.ENABLED_FLAG = 'Y' -->
        and orderHead.INNER_ORDER_HEADER_ID = orderTrack.INNER_ORDER_HEADER_ID(+)
        and a.INNER_ORDER_HEADER_ID = orderHead.INNER_ORDER_HEADER_ID(+)
        and deliverSet.DELIVER_SET_ID  = deliverPlan.DELIVER_SET_ID(+)
        and orderHead.DELIVER_SET_ID = deliverSet.DELIVER_SET_ID(+)
        and deliverPlan.ENABLED_FLAG(+) = 'Y'
        and orderTrack.ENABLED_FLAG(+) = 'Y'
        and orderHead.ENABLED_FLAG(+) = 'Y'
        and deliverSet.ENABLED_FLAG(+) = 'Y'
        AND deliverSet.DELIVER_SET_NUM=EOP.DELIVER_SET_NUM(+)
        and SLV.Enabled_Flag(+) = 'Y'
        and slv.LOOKUP_TYPE(+) = 7090001
        and a.organization_id=slv.lookup_meaning(+)
        <!-- ORDER BY A.ENTITY_ID -->
  </select>

    <!--判断是不是类终端任务-->
    <select id="getTaskisTerminal" parameterType="com.zte.interfaces.dto.EntryBoxAppliedDTO" resultType="java.lang.String">
        SELECT T.Is_Terminal FROM WMES.Task_Class_Terminal T WHERE T.ENABLED_FLAG = 'Y'
        AND  rownum=1
        AND T.ENTITY_NUMBER=#{entityName, jdbcType=VARCHAR}
    </select>

    <!--获取子状态-->
    <select id="getEntityTraceStatus" parameterType="com.zte.interfaces.dto.EntryBoxAppliedDTO" resultType="java.lang.String">
        SELECT S.STATUS_NAME
        FROM wmes.WERP_CON_ENTITY_TRACE S,
        wmes.CPM_CONTRACT_ENTITIES A,
        WMES.CDM_CONTRACT_LINES    ccl,
        WMES.CDM_CONTRACT_HEADERS  CCH,
        wmes.WMES_CON_ACTIONS_SUB  wcas
        WHERE A.ENTITY_ID = S.ENTITY_ID
        and A.contract_line_id = ccl.contract_line_id
        and a.ENTITY_ID = wcas.SUBMACHINE_ID
        and a.ENABLED_FLAG = 'Y'
        and CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID
        and A.ENTITY_NAME = #{entityName, jdbcType=VARCHAR}
        and A.ORGANIZATION_ID =#{organizationID, jdbcType=VARCHAR}
        AND rownum=1
    </select>

    <select id="checkOrgRepeat"  resultType="java.lang.Integer">
        select ORGANIZATION_ID
        from messys.SYS_LOOKUP_VALUES d
        WHERE  D.LOOKUP_TYPE= '8000220' and d.enabled_flag='Y'
        group by ORGANIZATION_ID having count(1)>=2
    </select>

    <select id="getTaskContractInformation"  resultType="com.zte.interfaces.dto.CompleteMachineDataLogEntityDTO">
        select trace.REMARK_01 COMBO,DECODE(trace.status_name, '入库', 100, 0)  orderState ,
        A.ENTITY_ID ,A.ENTITY_NAME factoryOrderId,nvl(PlaceHead.PRODUCT_PLACE_NAME,A.PRODUCT_PLACE_NO) factoryLocation,
        wcas.S40_DATE MANUFACTURER_TIME, cch.CONTRACT_NUMBER orderId ,cch.ACTUAL_EFFECT_DATE orderTime
        from WMES.CDM_CONTRACT_HEADERS cch
        join WMES.CDM_CONTRACT_LINES ccl on CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID
        join wmes.CPM_CONTRACT_ENTITIES A on A.contract_line_id=ccl.contract_line_id
        join wmes.WMES_CON_ACTIONS_SUB wcas on A.ENTITY_ID=wcas.SUBMACHINE_ID
        left join WMES.WERP_CON_ENTITY_TRACE trace on trace.ENTITY_ID = A.ENTITY_ID
        left join WMES.WMES_PRODUCT_PLACE_HEAD PlaceHead on A.PRODUCT_PLACE_NO = PlaceHead.PRODUCT_PLACE_NO
        where A.ENTITY_ID = #{entityId, jdbcType=DECIMAL}
    </select>

    <select id="getEarliestTimeForAssemblyPackaging"  resultType="com.zte.interfaces.dto.TaskHistorySubStateDTO">
        select subStatus,creationDate from (
            SELECT ROW_NUMBER() OVER(PARTITION BY t.ENTITY_ID,t.SUB_STATUS ORDER BY t.last_update_date ASC)
            rn,ENTITY_ID as EntityId,SUB_STATUS as SubStatus,
            CREATION_DATE as creationDate
            FROM WMES.WMES_ENTITY_TRACE_MODIFY_HIS t
            WHERE ENABLED_FLAG = 'Y'
            AND ENTITY_ID = #{entityId, jdbcType=DECIMAL} )
        where rn =1
    </select>

    <select id="getSessionBoqNoScanTask" parameterType="com.zte.interfaces.dto.AutoBatchMarkInDTO" resultType="com.zte.interfaces.dto.AutoBatchMarkTaskDTO">
        select Entity_Name entityName,
               Entity_Id entityId,
               CONTRACT_NUMBER contractNumber from (
                select  A.Entity_Name,
                        A.Entity_Id,
                        CCH.CONTRACT_NUMBER,
                        AA.Record_Id,
                case when (select count(*) from WMES.cpm_contract_mfg_sites CCMS
                          where CCMS.ENTITY_ID = A.ENTITY_ID and CCMS.ENABLED_FLAG='Y'  and CCMS.eu_code is not null) != 0 then 'EU'
                     when dicDtValue.Description is not null  then dicDtValue.Description when upper(A.MFG_SITE_TYPE) = 'C' then '软件'
                     when upper(A.MFG_SITE_TYPE) = 'ZC' then '否'  when upper(ccl.BOQ_FLAG) = 'Y' then '是' else '否' end BOQ_FLAG
                from
                wmes.CPM_CONTRACT_ENTITIES A,
                WMES.CDM_CONTRACT_LINES ccl,
                WMES.CDM_CONTRACT_HEADERS CCH,
                WMES.BAS_LOOKUP_VALUES_VL BLV,
                apps.SYS_LOOKUP_VALUES dicDtValue,
                wmes.entitytrace_import_temp temp,
                SFC.pkg_submac_scan_info_line AA
                where  A.contract_line_id=ccl.contract_line_id
                AND AA.enabled_flag(+) ='Y'
                AND AA.submachine_id(+) = A.Entity_Id
                AND BLV.enabled_flag(+) = 'Y'
                AND A.Mfg_Site_Type=BLV.lookup_code(+)
                AND BLV.LOOKUP_TYPE(+) = 'CDM_EQUIPMENT_TYPE'
                and dicDtValue.Lookup_Type(+)=8240022
                and BLV.meaning =dicDtValue.Lookup_Meaning(+)
                and CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID
                AND A.Entity_Name = temp.entity_name
                AND temp.session_id=#{sessionId, jdbcType=VARCHAR}
        ) where  Record_Id is null  and BOQ_FLAG ='是'
    </select>

    <select id="batchQueryStatusName" resultType="com.zte.domain.model.datawb.WmesWerpConEntityTrace">
        SELECT STATUS_NAME,ENTITY_ID
        FROM WMES.WERP_CON_ENTITY_TRACE
        WHERE 1 = 1
        <choose>
            <when test="entityIdList !=null and entityIdList.size()>0">
                and ENTITY_ID IN
                <foreach collection="entityIdList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1 = 2
            </otherwise>
        </choose>
    </select>
</mapper>