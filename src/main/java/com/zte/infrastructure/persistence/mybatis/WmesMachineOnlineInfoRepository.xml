<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.WmesMachineOnlineInfoRepository">
    <delete id="deleteByFactoryId" parameterType="java.lang.String">
        delete from WMES.WMES_MACHINE_ONLINE_INFO
        where FACTORY_ID = #{factoryId,jdbcType=VARCHAR}
    </delete>

    <insert id="insertBatch">
        <foreach collection="dataList" index="index" item="item" open="begin" separator=";" close="; end ;">
            insert into WMES.WMES_MACHINE_ONLINE_INFO (ITEM_CODE, PLAN_GROUP, NUM, ENABLED_FLAG, CREATION_DATE,
            LAST_UPDATE_DATE, FACTORY_ID)
            values
            (#{item.itemCode, jdbcType = VARCHAR},
            #{item.planGroup, jdbcType = VARCHAR},
            #{item.num, jdbcType = NUMERIC},
            #{item.enabledFlag, jdbcType = VARCHAR},
            sysdate,
            sysdate,
            #{item.factoryId, jdbcType = VARCHAR})
        </foreach>
    </insert>
</mapper>