<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.CompletebindSupplierProduceSelectRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.CompletebindSupplierProduceSelectDTO">
        <id column="ITEM_BARCODE" jdbcType="VARCHAR" property="itemBarcode"/>
        <result column="PRODUCT_NO" jdbcType="VARCHAR" property="productNo"/>
        <result column="CONTRACT_NUMBER" jdbcType="VARCHAR" property="contractNumber"/>
        <result column="SUBMACHINE_NUMBER" jdbcType="VARCHAR" property="submachineNumber"/>
        <result column="BILL_NUMBER" jdbcType="VARCHAR" property="billNumber"/>
        <result column="USER_ADDRESS" jdbcType="VARCHAR" property="userAddress"/>
        <result column="ACTION_DATE" jdbcType="VARCHAR" property="actionDate"/>
    </resultMap>

    <select id="getStItemBarcodeList" parameterType="com.zte.interfaces.dto.CompletebindSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select ITEM_BARCODE,PRODUCT_NO
        from kxstepiii.ST_ITEM_BARCODE t
        where t.ENABLED_FLAG=1
        <if test="itemBarcodes != null and itemBarcodes.size > 0">
            AND t.ITEM_BARCODE IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR t.ITEM_BARCODE IN()">    <!-- 表示删除最后一个条件 -->
                <foreach collection="itemBarcodes" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR t.ITEM_BARCODE IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="itemBarcodes == null or itemBarcodes.size == 0">
            and 1=2
        </if>
    </select>

    <select id="getSubmacScanInfoList" parameterType="com.zte.interfaces.dto.CompletebindSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select item_barcode,contract_number,submachine_number,user_address,to_char(action_date,'yyyy-MM-dd HH:mi:ss')
        action_date,bill_number
        from PKG_SUBMAC_SCAN_INFO_V t
        where 1=1
        <if test="itemBarcodes != null and itemBarcodes.size > 0">
            AND t.ITEM_BARCODE IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR t.ITEM_BARCODE IN()">    <!-- 表示删除最后一个条件 -->
                <foreach collection="itemBarcodes" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR t.ITEM_BARCODE IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="itemBarcodes == null or itemBarcodes.size == 0">
            and 1=2
        </if>
    </select>

    <select id="selectSubmacScanInfoList" parameterType="com.zte.interfaces.dto.CompletebindSupplierProduceSelectDTO"
            resultMap="BaseResultMap">
        select item_barcode
        from PKG_SUBMAC_SCAN_INFO_V t
        where 1=1
        <if test="startDate != null and startDate != ''">
            and t.action_date <![CDATA[ >= ]]>  TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate != ''">
            and t.action_date <![CDATA[ <= ]]>  TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="contractNumber != null and contractNumber != ''">and t.contract_number = #{contractNumber}</if>
        <if test="submachineNumber != null and submachineNumber != ''">and t.submachine_number = #{submachineNumber}</if>
        <if test="billNumber != null and billNumber != ''">and t.bill_number = #{billNumber}</if>
        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '') and (contractNumber == null or contractNumber == '') and
                (submachineNumber == null or submachineNumber == '') and (billNumber == null or billNumber == '')">
            and 1=2
        </if>
    </select>

</mapper>
