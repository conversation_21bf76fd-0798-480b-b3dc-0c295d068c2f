<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.RecalculationConvergenceRepository">
    <resultMap id="MesAssemblySpecificationsInfoMap" type="com.zte.interfaces.dto.MesAssemblySpecificationsOutDTO">
        <result property="entityName" jdbcType="VARCHAR" column="entityName"/>
        <result property="itemBarcode" jdbcType="VARCHAR" column="itemBarcode"/>
        <result property="itemNo" jdbcType="VARCHAR" column="itemNo"/>
        <result property="brand" jdbcType="VARCHAR" column="brand"/>
        <result property="style" jdbcType="VARCHAR" column="style"/>
        <result property="subBarcode" jdbcType="VARCHAR" column="subBarcode"/>
        <result property="scanQty" jdbcType="INTEGER" column="scanQty"/>
        <result property="itemName" jdbcType="VARCHAR" column="itemName"/>
        <result property="customerAddr" jdbcType="VARCHAR" column="customerAddr"/>
        <result property="itemType" jdbcType="VARCHAR" column="itemType"/>
    </resultMap>

    <select id="callPkgCollectDetailsCollectBoxdetails" statementType="CALLABLE" parameterType="com.zte.interfaces.dto.RecalculationConvergenceParamterDTO">
        {call APP_MES.PKG_COLLECT_DETAILS.COLLECT_BOXDETAILS_JAVA(
        #{strBillNumbers,jdbcType=ARRAY,typeHandler=com.zte.domain.handler.OracleStringArrayTypeHandler}
        )}
    </select>

    <select id="callPkgAutoMarkInsertAutoMarkRecord" statementType="CALLABLE" parameterType="com.zte.interfaces.dto.AutoBatchMarkTaskDTO">
        {call APP_MES.PKG_AUTO_MARK.INSERT_AUTO_MARK_RECORD_JAVA(
        #{entityIdOrgMark,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{strMark,jdbcType=ARRAY,typeHandler=com.zte.domain.handler.OracleStringArrayTypeHandler}
        )}
    </select>

    <select id="callPkgBoxListDetailsBoxlistDetailsUUID" statementType="CALLABLE" parameterType="com.zte.interfaces.dto.RecalculationConvergenceParamterDTO">
        {call APP_MES.PKG_BOXLIST_DETAILS.BOXLIST_DETAILS_UUID_JAVA(
        #{createBy,mode=IN,jdbcType=INTEGER,javaType=java.lang.Integer},
        #{strBillNumbers,jdbcType=ARRAY,typeHandler=com.zte.domain.handler.OracleStringArrayTypeHandler}
        )}
    </select>

    <select id="getMesAssemblySpecificationsInfo" statementType="CALLABLE" parameterType="map">
        {call APP_MES.PKG_JD_SENDCONFIG_EXPORT.MesAssemblySpecificationsInfo(
        #{p_strbarcode,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{p_pagesize,mode=IN,jdbcType=INTEGER,javaType=java.lang.Integer},
        #{p_currentpage,mode=IN,jdbcType=INTEGER,javaType=java.lang.Integer},
        #{p_count,mode=OUT,jdbcType=INTEGER,javaType=java.lang.Integer},
        #{p_cursor,mode=OUT,jdbcType=CURSOR,resultMap=MesAssemblySpecificationsInfoMap}
        )}
    </select>

    <select id = "getOriginCountryNA" resultType="java.lang.String">
        select  ITEM_BARCODE itemBarcode
        from APP_MES.BOXLIST_DETAILS bd
        where bd.enabled_flag='Y'  and bd.z_mat_ori_land = 'NOTAVAL'
    </select>

    <update id ="updatePurchasedOriginCountry" parameterType="com.zte.interfaces.dto.EntryBoxAppliedOriginDTO">
        UPDATE APP_MES.BOXLIST_DETAILS SET
        last_update_date=sysdate,
        Z_MAT_ORI_LAND= #{prodAddress,jdbcType=VARCHAR}
        WHERE ENABLED_FLAG='Y'
        and ITEM_BARCODE= #{barcode,jdbcType=VARCHAR}
    </update>

    <select id="getAllBoxupCount" parameterType="com.zte.interfaces.dto.RecalculationConvergenceParamterDTO"
            resultType="int">
        select count(1) from APP_MES.CPM_BOXUP_BILLS CBB
        where CBB.enabled_flag='Y' AND CBB.BILL_NUMBER in
        <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
        <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">    <!-- 表示删除最后一个条件 -->
            <foreach collection="billNumbers" item="item" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
    </select>
    <insert id="inBoxlistDetailsCkLog" parameterType="com.zte.interfaces.dto.RecalculationConvergenceParamterDTO">
        INSERT INTO APP_MES.IN_BOXLIST_DETAILS_CK_LOG
        (BILL_NUMBER,CREATION_DATE,CREATED_BY,COMMENTS)
        VALUES
        (
        #{billNumberList,jdbcType=VARCHAR}
        ,SYSDATE
        ,#{empNo,jdbcType=VARCHAR}
        ,#{token,jdbcType=VARCHAR}
        )
    </insert>
    <select id="getMesEntryBillsByPsApplyBillNo" parameterType="java.lang.String"
            resultType="com.zte.interfaces.dto.EntryBoxAppliedEntryBillDTO">
        SELECT ENTRY_STATUS entryStatus,
        SUBINVENTORY subinventory,
        b.ENTRY_BILL_ID entryBillId,
        b.ENTRY_LINE_ID entryLineId,
        c.BILL_NUMBER billNumber,
        IS_EMPTY isEmpty
        FROM APP_MES.MES_ENTRY_BILLS a,
        APP_MES.MES_ENTRY_BILL_LINES b,
        APP_MES.cpm_boxup_bills c
        WHERE a.entry_bill_id = b.entry_bill_id
        and b.bill_id = c.bill_id
        and a.ENTRY_NUMBER = #{entryNumber,jdbcType=VARCHAR}
    </select>

    <update id="updateMesEntryBillLInesByEntryBillId" parameterType="com.zte.interfaces.dto.EntryBoxAppliedEntryBillDTO">
        UPDATE APP_MES.MES_ENTRY_BILL_LINES
        SET SCAN_BY = #{scanBy,jdbcType=VARCHAR},
        SCAN_DATE = SYSDATE,
        POSTION_ID = #{postionId,jdbcType=VARCHAR}
        WHERE ENTRY_BILL_ID = #{entryBillId,jdbcType=VARCHAR}
    </update>

    <update id="updateMesEntryBillsByEntryBillId" parameterType="com.zte.interfaces.dto.EntryBoxAppliedEntryBillDTO">
        UPDATE APP_MES.MES_ENTRY_BILLS
        SET ENTRY_STATUS = 'POSTED',
        RECEIVE_BY =#{receiveBy,jdbcType=VARCHAR},
        RECEIVE_DATE = SYSDATE,
        POST_BY = #{postBy,jdbcType=VARCHAR},
        POST_DATE = SYSDATE
        WHERE ENTRY_BILL_ID = #{entryBillId,jdbcType=VARCHAR}
    </update>

    <select id="callPkgEntityConfigAssemble" statementType="CALLABLE" parameterType="com.zte.interfaces.dto.RecalculationConvergenceParamterDTO">
        {call APP_MES.PKG_ENTITY_CONFIG_ASSEMBLE.ENTITY_CONFIG_ASSEMBLE(
        #{entityNoList,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{entryNumber,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{createBy,mode=IN,jdbcType=INTEGER,javaType=java.lang.Integer}
        )}
    </select>

</mapper>