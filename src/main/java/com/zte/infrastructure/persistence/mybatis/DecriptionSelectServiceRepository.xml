<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.DecriptionSelectServiceRepository">

    <resultMap id="TechnologyDTOMapDes" type="com.zte.interfaces.dto.TechnologyModifyResultDTO">
        <result column="description" jdbcType="VARCHAR" property="descriptionCode"/>
    </resultMap>

    <select id="getDictionrayList" resultType="java.util.HashMap" resultMap="TechnologyDTOMapDes">
        select d.description
        from wmes.SYS_LOOKUP_TYPES h
        join wmes.SYS_LOOKUP_VALUES d on d.lookup_type = h.lookup_type
        and h.LOOKUP_TYPE = #{keyType}
        <if test="_parameter.containsKey('forName')">
            and d.lookup_meaning =#{forName}
        </if>
        <if test="_parameter.containsKey('orgDtList')">
            and d.ORGANIZATION_ID=#{orgDtList}
        </if>
    </select>
</mapper>
