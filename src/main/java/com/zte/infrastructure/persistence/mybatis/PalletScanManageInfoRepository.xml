<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.PalletScanManageInfoRepository">
    <resultMap id="backLogQueryCol" type="com.zte.interfaces.dto.PalletScanManageInfoResultDTO">
        <result property="boxWeight" jdbcType="DECIMAL" column="Box_Weight"/>
        <result property="isScan" jdbcType="VARCHAR" column="IS_SCAN"/>
        <result property="totalWeight" jdbcType="DECIMAL" column="TOTAL_WEIGHT"/>
        <result property="height" jdbcType="DECIMAL" column="HEIGHT"/>
    </resultMap>
    <select id="getMessagePallet" parameterType="com.zte.interfaces.dto.PalletScanManageInfoParamterDTO" resultMap="backLogQueryCol"  >
    SELECT
      distinct
      MTD.Box_Weight,
      DECODE(PIM.IS_SCAN,'Y','Y','N') IS_SCAN ,
      PIM.TOTAL_WEIGHT,
      MTD.HEIGHT
      FROM APP_MES.PALLET_INFO_MANAGE PIM
      LEFT JOIN APP_MES.MES_TOPBOX_DEFINE  MTD
      ON MTD.BOX_TYPE_CODE = PIM.BOX_TYPE_CODE
      AND MTD.ENABLED_FLAG = 'Y'
      LEFT JOIN APP_MES.CPM_CONTRACT_ENTITIES CCE
      ON PIM.ENTITY_NAME = CCE.ENTITY_NAME
      AND CCE.ENABLED_FLAG = 'Y'
      left join APP_MES.CPM_INNER_ORDER_HEADERS cion
      on cce.inner_order_header_id = cion.inner_order_header_id
      LEFT JOIN APP_MES.CDM_CONTRACT_LINES CCL
      ON CCE.CONTRACT_LINE_ID = CCL.CONTRACT_LINE_ID
      AND CCL.ENABLED_FLAG = 'Y'
      LEFT JOIN APP_MES.CDM_CONTRACT_HEADERS CCH
      ON CCH.CONTRACT_HEADER_ID = CCL.CONTRACT_HEADER_ID
      LEFT JOIN APP_MES.CDM_CONTRACT_SHIPMENTS CCSH
      ON CCE.SHIPMENT_ID = CCSH.SHIPMENT_ID
      AND CCSH.ENABLED_FLAG =  'Y'
      LEFT JOIN APP_MES.CDM_BOXUP_ITEM_TYPE CBIT
      ON CCE.ENTITY_ID = CBIT.ENTITY_ID
      LEFT JOIN APP_MES.CPM_CONTRACT_MFG_SITES CCMS
      ON PIM.MFG_SITE_ID = CCMS.MFG_SITE_ID
      AND CCMS.ENABLED_FLAG = 'Y'
      AND CCE.ENTITY_ID = CCMS.ENTITY_ID
      LEFT JOIN APP_MES.CPM_CONTRACT_SITES CCS
      ON CCS.SITE_ID = CCMS.SITE_ID
      AND CCS.ENABLED_FLAG = 'Y'
      LEFT JOIN APP_MES.CDM_DELIVER_SETS CDS
      on cion.deliver_set_id = cds.deliver_set_id
      AND CDS.ENABLED_FLAG = 'Y'
      LEFT JOIN APP_MES.CDM_CONTRACT_PROJECT_SHIPMENTS CCPS
      ON CCPS.PROJECT_SHIPMENT_ID = CDS.PROJECT_SHIPMENT_ID
      AND CCPS.ENABLED_FLAG = 'Y'
      LEFT JOIN APP_MES.CDM_ENGINEER_DELIVER_SETS CEDS
      ON CEDS.ENGINEER_DELIVER_SET_ID = CCPS.ENGINEER_DELIVER_SET_ID
      LEFT JOIN APP_MES.CDM_TEMPLATE_LANGUAGE_INFO CTLI
      ON CCH.CONTRACT_HEADER_ID = CTLI.CONTRACT_HEADER_ID
      and pim.organization_id = ctli.organization_id
      AND CTLI.ENABLED_FLAG = 'Y'
      AND CEDS.ENABLED_FLAG = 'Y'
      WHERE PIM.ENABLED_FLAG = 'Y'
      AND PIM.PALLET_NO =#{palletNo,jdbcType=VARCHAR}
    </select>

  <select id="getMessageSumbox" parameterType="com.zte.interfaces.dto.PalletScanManageInfoParamterDTO" resultType="java.lang.Long"  >
       SELECT SUM( CBB.GROSS_WEIGHT)  SUMBOX_WHIGHT
      FROM APP_MES.PALLET_SCAN_PACKING PSP,
      APP_MES.PALLET_INFO_MANAGE PIM,
      APP_MES.CPM_BOXUP_BILLS CBB
      WHERE  PSP.PALLET_ID=PIM.PALLET_ID AND PSP.BILL_NUMBER=CBB.BILL_NUMBER
      AND PSP.ENABLED_FLAG='Y'
      AND PIM.ENABLED_FLAG='Y'
      AND CBB.ENABLED_FLAG='Y'
      AND PSP.PALLET_NO=#{palletNo,jdbcType=VARCHAR}
      ORDER BY PSP.BILL_NUMBER
    </select>

    <update id="updatePalletScan" parameterType="com.zte.interfaces.dto.PalletScanManageInfoParamterDTO"  >
      begin
           UPDATE APP_MES.PALLET_INFO_MANAGE
         SET
           LAST_UPDATE_DATE = SYSDATE
           <if test="userId != null and userId != ''">
              ,  LAST_UPDATED_BY <![CDATA[=]]> #{userId}
           </if>
           <if test="height != null and height != ''">
             , CODE_HIGHT<![CDATA[=]]> #{height}
            </if>
            <if test="height != null and height != ''">
              , TOTAL_WEIGHT<![CDATA[=]]> #{weight}
            </if>
           ,IS_SCAN_END='Y'
           where
          ENABLED_FLAG = 'Y'
          AND PALLET_NO=#{palletNo,jdbcType=VARCHAR} ;
          commit;

      end;
    </update>

    <select id="selectUserId" resultType="java.lang.Long"  >
      SELECT  su.user_id FROM APP_MES.SYS_USERS su
       where
       su.user_name = #{userId}
        and rownum=1
    </select>

</mapper>