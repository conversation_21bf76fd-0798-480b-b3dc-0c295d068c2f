<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.InsertModifyStatusAndDateLogRepository">


    <insert id="insertModifyStatusAndDateLogMethod" parameterType="com.zte.domain.model.datawb.InsertModifyStatusAndDateLog">
        <selectKey  keyProperty="recordId" order="BEFORE" resultType="java.math.BigDecimal">
            SELECT WMES_ENTITY_TRACE_MODIFY_HIS_S.nextval recordId from dual
        </selectKey>
        insert into WMES.WMES_ENTITY_TRACE_MODIFY_HIS(RECORD_ID,ENTITY_ID,SUB_STATUS,UPDATED_MAN,CREATION_DATE,CREATED_BY,LAST_UPDATE_DATE,LAST_UPDATED_BY,MODIFY_HIS,AIM_DATE03_HIS,AIM_DATE03)
        values (#{recordId},(select a.ENTITY_ID from WMES.CPM_CONTRACT_ENTITIES A where a.ENTITY_NAME = #{entityNum,jdbcType=VARCHAR}) ,#{status,jdbcType=VARCHAR},#{updateMan,jdbcType=VARCHAR},SYSDATE,#{updateMan,jdbcType=VARCHAR},SYSDATE,#{updateMan,jdbcType=VARCHAR},'导入修改',
        to_date(SUBSTR(#{aimDate03His,jdbcType=VARCHAR},1,10),'yyyy-MM-dd'),to_date(SUBSTR(#{aimDate03,jdbcType=VARCHAR},1,10),'yyyy-MM-dd'))
    </insert>

</mapper>
