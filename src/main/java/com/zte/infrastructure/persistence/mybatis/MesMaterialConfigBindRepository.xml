<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.MesMaterialConfigBindRepository">

    <resultMap id="MESSTEPSTMAP" type="com.zte.interfaces.dto.MesConfigMaterialDTO">
        <result property="isSn" jdbcType="INTEGER" column="IS_SN"/>
    </resultMap>
    <select id="qryBarcodeType"  parameterType="java.lang.String"  resultType="java.lang.String">
        SELECT
        REGEXP_SUBSTR (sfc.Discern_CodeType(#{barCode,jdbcType=VARCHAR}), '[^,]+',1)   AS BarcodeType
        FROM DUAL

    </select>

    <select id="getStepItemBar" statementType="CALLABLE" parameterType="map">
        {call SFC.WSM_BOXUP_SCAN_PKG.GET_STEP_ITEM_BARCODE_SSP(
        #{map.itemBarcode,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=MESSTEPSTMAP}
        )}
    </select>

    <select id="getCenterItemID"  parameterType="java.lang.String"  resultType="com.zte.interfaces.dto.BasBarcodeInfo">
        select
        B.Item_Id  as  itemId ,
        B.Item_Code as itemCode ,
        B.Item_Name as itemName
        from sfc.BAS_ITEMS_INFO B where B.Item_Code=#{itemCode,jdbcType=VARCHAR}
        and B.Enabled_Flag='Y'
    </select>

    <select id ="repleacedInformation" parameterType="java.util.Map" resultType="com.zte.interfaces.dto.BindLevelDto">
        select
        t.inventory_item_id as inventoryItemId,
        i.item_id as itemId,
        i.item_code as itemCode,
        i.item_name as itemName
        from sfc.mtl_related_items t,sfc.bas_items_info i
        WHERE t.related_item_id = i.item_id
        and t.RELATIONSHIP_TYPE_ID = 2
        and i.enabled_flag = 'Y'
        and i.organization_id = #{OrgId,jdbcType=VARCHAR}
        and t.inventory_item_id = #{ItemId,jdbcType=VARCHAR}
    </select>

    <select id ="getEmpNoUserID" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT  su.user_id FROM sfc.SYS_USERS su
        where
        su.user_name = #{userName,jdbcType=VARCHAR}
        and rownum=1
    </select>

    <insert id="saveLog" parameterType="com.zte.interfaces.dto.MesConfigMaterialDTO">
        INSERT INTO SFC.SSP_MES_LOG (RECORD_ID,ERROR_MESSAGE,CREATED_BY)VALUES
        (#{uuid,jdbcType=VARCHAR},#{info,jdbcType=VARCHAR},#{createby,jdbcType=VARCHAR})
    </insert>



</mapper>