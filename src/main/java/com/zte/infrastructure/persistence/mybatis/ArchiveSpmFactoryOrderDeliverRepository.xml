<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.ArchiveSpmFactoryOrderDeliverRepository">
    <select id="getPageByDateRange" resultType="com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO"
            parameterType="com.zte.springbootframe.common.model.Page">
            select
            distinct
            r.RECORD_ID as recordId,
            r.EXPENSEITEM as expenseitem
            from
            (select r.PLANID,r.RECORD_ID,r.EXPENSEITEM,r.last_update_date
            from SFC.SPM_FACTORY_ORDERDELIVER r,
            SFC.SPM_SYS_LOOKUP_TYPES     ta,
            SFC.SPM_SYS_LOOKUP_VALUES    vb
            where r.billstatus = vb.Lookup_Code
            and ta.lookup_type = vb.lookup_type
            and ta.lookup_type = '2020021'
            and ta.enabled_flag = 'Y'
            and vb.enabled_flag = 'Y'
            and r.enabled_flag = 'Y'
            and vb.description <![CDATA[<>]]> '已取消'
            and (r.CANCELREASON = '' OR r.CANCELREASON IS NULL)) r,
            SFC.SPM_PROD_PLAN                              p,
            SFC.SPM_BOARD                                  bd,
            SFC.SPM_FACTORY_BOM_DEFINE                     d,
            SFC.SPM_SYS_LOOKUP_TYPES                       a,
            SFC.SPM_SYS_LOOKUP_VALUES                      b,
            SFC.SPM_FACTORY_PRODUCT_DEFINE                 c,
            KXSTEPIII.op_addr_orga_relation@step o
            where r.planid=p.prodplan_id
            and p.bom_id = bd.bom_id
            and d.item_id = bd.bom_id
            and o.address_code=p.prod_address
            and d.product_model_id = c.record_Id
            and d.organization_id = c.organization_id
            and d.factory_type_no = b.lookup_meaning
            and a.lookup_type = b.lookup_type
            and a.lookup_type = '2020020'
            and d.enabled_flag = 'Y'
            and A.enabled_flag = 'Y'
            and b.enabled_flag = 'Y'
            and c.enabled_flag = 'Y'
            AND r.LAST_UPDATE_DATE &gt;= #{params.startDate}
            AND r.LAST_UPDATE_DATE &lt; #{params.endDate}
    </select>
    <select id="getByRecordId" resultType="com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO"
            parameterType="java.lang.String">
        SELECT
            DISTINCT
            SFO.RECORD_ID as recordId,
            SFO.EXPENSEITEM as expenseitem,
            to_char(SPP.PLAN_DATE, 'yyyy-MM-dd hh24:mi:ss') as planDate,
            SFO.LAST_UPDATED_BY as lastUpdatedBy,
            SFO.FACTORY_SUPPLIER_NAME as factorySupplierName,
            SPP.PRODPLAN_ID as prodplanId,
            SPP.PRODPLAN_NO as prodplanNo,
            SFPD.PRODUCT_MODEL_NAME as productModelName,
            SSLV.DESCRIPTION as factoryType,
            SFO.ORGANIZATION_ID as organizationId
        FROM
            SFC.SPM_FACTORY_ORDERDELIVER sfo,
            SFC.SPM_PROD_PLAN spp,
            SFC.SPM_FACTORY_BOM_DEFINE sfbd,
            SFC.SPM_FACTORY_PRODUCT_DEFINE sfpd,
            SFC.SPM_SYS_LOOKUP_TYPES sslt,
            SFC.SPM_SYS_LOOKUP_VALUES sslv
        WHERE
            (
            sfo.CANCELREASON = ''
            OR SFO.CANCELREASON IS NULL
            )
            AND SFO.PLANID = SPP.PRODPLAN_ID
            AND SFBD.ITEM_ID = SPP.BOM_ID
            AND SFPD.RECORD_ID = SFBD.PRODUCT_MODEL_ID
            AND SFBD.FACTORY_TYPE_NO = SSLV.LOOKUP_MEANING
            AND SSLV.LOOKUP_TYPE = SSLT.LOOKUP_TYPE
            AND SFO.ENABLED_FLAG = 'Y'
            AND SFBD.ENABLED_FLAG = 'Y'
            AND SFPD.ENABLED_FLAG = 'Y'
            AND SSLT.ENABLED_FLAG = 'Y'
            AND SSLV.ENABLED_FLAG = 'Y'
            AND SSLV.DESCRIPTION in('系统板','手机板')
            and rownum = 1
            AND SFO.RECORD_ID = #{recordId}
    </select>
    <select id="getSpmFactoryReturnByRecordId" resultType="com.zte.interfaces.dto.ArchiveSpmFactoryReturnDTO"
            parameterType="java.lang.String">
        SELECT
            to_char(n.returndate, 'yyyy-MM-dd hh24:mi:ss') returnDate,
            n.returnnumber returnNumber
        FROM
            SFC.SPM_FACTORY_RETURN n
        WHERE
             n.orderdeliverid = #{recordId}
    </select>
    <select id="getSpmProdPlanByRecordId" resultType="com.zte.interfaces.dto.ArchiveSpmFactoryOrderDeliverDTO"
            parameterType="java.lang.String">
            select  to_char(p.plan_date, 'yyyy-MM-dd hh24:mi:ss') as planDate,
            d.factory_type_no as factoryTypeNo,
            d.product_model_id,
            b.description as factoryType,
            c.product_model_name as productModelName,
            p.prodplan_no as prodplanNo,
            p.prodplan_id as prodplanId,
            p.qty,
            bd.bom_no as bomNo,
            bd.bom_name as bomName,
            decode(k.issue_date, NULL, '否', '是') isQitao,
            bd.version,
            r.factory_supplier_name as factorySupplierName,
            SFC.SPM_GETORDERPROCESSNAME(r.record_id) AS process_name,
            r.record_id,
            r.expenseitem,
            nvl2(r.PLANID,'已排产','未排产')  as planStatus,
            DECODE(r.IS_BUY_SELL, 1, '是', 0, '否', '') isBuySell
            from  (select r.PLANID,r.RECORD_ID,r.EXPENSEITEM,r.IS_BUY_SELL,r.factory_supplier_name,r.last_update_date
            from SFC.SPM_FACTORY_ORDERDELIVER r,
            SFC.SPM_SYS_LOOKUP_TYPES     ta,
            SFC.SPM_SYS_LOOKUP_VALUES    vb
            where r.billstatus = vb.Lookup_Code
            and ta.lookup_type = vb.lookup_type
            and ta.lookup_type = '2020021'
            and ta.enabled_flag = 'Y'
            and vb.enabled_flag = 'Y'
            and r.enabled_flag = 'Y'
            and vb.description <![CDATA[<>]]> '已取消'
            and (r.CANCELREASON = '' OR r.CANCELREASON IS NULL)
            AND r.record_id = #{recordId}
            AND r.organization_id = #{organizationId})  r,
            SFC.SPM_PROD_PLAN                              p,
            SFC.SPM_BOARD                                  bd,
            SFC.SPM_FACTORY_BOM_DEFINE                     d,
            SFC.SPM_SYS_LOOKUP_TYPES                       a,
            SFC.SPM_SYS_LOOKUP_VALUES                      b,
            SFC.SPM_FACTORY_PRODUCT_DEFINE                 c,
            kxstepiii.st_prepare_dictatestatus@step k,
            KXSTEPIII.op_addr_orga_relation@step o
            where r.planid=p.prodplan_id
            and p.bom_id = bd.bom_id
            and d.item_id = bd.bom_id
            and o.address_code=p.prod_address
            AND p.prodplan_no = k.PRODPLAN_NO(+)
            and d.product_model_id = c.record_Id
            and d.organization_id = c.organization_id
            and d.factory_type_no = b.lookup_meaning
            and a.lookup_type = b.lookup_type
            and a.lookup_type = '2020020'
            and d.enabled_flag = 'Y'
            and A.enabled_flag = 'Y'
            and b.enabled_flag = 'Y'
            and c.enabled_flag = 'Y'
            and d.organization_id =#{organizationId}
            <if test="organizationId!=null and organizationId=='31'">
            and o.organization_id in (31,3417,3577,395)
            </if>
            <if test="organizationId!=null and organizationId=='635'">
            and  o.organization_id = 395
            </if>
            <if test="organizationId!=null and organizationId=='855'">
            and  o.organization_id = 855
            </if>
    </select>
    <select id="getSpmFactoryProcessDefineByOrderId" resultType="com.zte.interfaces.dto.ArchiveSpmFactoryReturnDTO"
            parameterType="java.lang.String">
        SELECT
            SFPD.PROCESS_NANME as processNanme
        FROM
            SFC.SPM_FACTORY_ORDERDELIVER_PRO sfop,
            SFC.SPM_FACTORY_PROCESS_DEFINE sfpd
        WHERE
            SFOP.PROCESS_ID = SFPD.RECORD_ID
            AND SFOP.ORGANIZATION_ID = SFPD.ORGANIZATION_ID
            AND SFOP.ENABLED_FLAG = 'Y'
            AND SFPD.ENABLED_FLAG = 'Y'
            AND SFOP.ORDERDELIVERID = #{recordId}
    </select>
</mapper>