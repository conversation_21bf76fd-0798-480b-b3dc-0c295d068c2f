<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ModelCodeQueryOutputRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.ModelCodeQueryOutput">
    <result column="DEVICES_SITE_CODE" jdbcType="VARCHAR" property="devicesSiteCode" />
    <result column="PTO_ITEM_NAME" jdbcType="VARCHAR" property="ptoItemName" />
    <result column="BILL_NUMBER" jdbcType="VARCHAR" property="billNumber" />
  </resultMap>

  <select id="modelCodeQuery" parameterType="com.zte.interfaces.dto.ModelCodeQueryInputDTO" resultMap="BaseResultMap">
     SELECT substr(csp.devices_site_code,0,instr(csp.devices_site_code,'-')-1) DEVICES_SITE_CODE,csp.PTO_ITEM_NAME,
     boxbill.BILL_NUMBER
    FROM  APP_MES.cpm_site_pto   csp,
          APP_MES.cpm_contract_mfg_sites  ccm,
          app_mes.cpm_boxup_bills boxbill,
          APP_MES.cpm_config_details ccd,
          APP_MES.cpm_sale_config_details cscd,
          APP_MES.CPM_CONTRACT_ENTITIES      CCE,
          app_mes.cdm_contract_headers    cch,
          app_mes.cdm_contract_lines      ccl 

   WHERE csp.SITE_ID=ccm.SITE_ID
   and csp.device_site_id = cscd.device_site_id
   and ccd.pconfig_detail_id = cscd.pconfig_detail_id
   and  ccd.mfg_site_id = ccm.mfg_site_id   
   and boxbill.MFG_SITE_ID=ccm.MFG_SITE_ID
   and ccl.CONTRACT_HEADER_ID=cch.CONTRACT_HEADER_ID
   and cce.CONTRACT_LINE_ID=ccl.CONTRACT_LINE_ID
   and CCE.ENTITY_ID=boxbill.ENTITY_ID
   <if test="contractNumber != null and contractNumber !=''">
        and cch.CONTRACT_NUMBER=#{contractNumber,jdbcType=VARCHAR}
   </if>
   <if test="entityName != null and entityName !=''">
        and cce.ENTITY_NAME=#{entityName,jdbcType=VARCHAR}
   </if>
   <if test="billNumberList != null and billNumberList.size()>0">
      and boxbill.BILL_NUMBER in
    <foreach collection="billNumberList" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
    </foreach>
   </if>
   group by  csp.devices_site_code,csp.PTO_ITEM_NAME,boxbill.BILL_NUMBER
  </select>

  <insert id="insertModelCodeQueryOutput" parameterType="com.zte.domain.model.datawb.ModelCodeQueryOutput">
    insert into MODEL_CODE_QUERY_OUTPUT (DEVICES_SITE_CODE, PTO_ITEM_NAME)
    values (#{devicesSiteCode,jdbcType=VARCHAR}, #{ptoItemName,jdbcType=VARCHAR})
  </insert>

  <insert id="insertModelCodeQueryOutputSelective" parameterType="com.zte.domain.model.datawb.ModelCodeQueryOutput">
    insert into MODEL_CODE_QUERY_OUTPUT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="devicesSiteCode != null">
        DEVICES_SITE_CODE,
      </if>

      <if test="ptoItemName != null">
        PTO_ITEM_NAME,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="devicesSiteCode != null">
        #{devicesSiteCode,jdbcType=VARCHAR},
      </if>

      <if test="ptoItemName != null">
        #{ptoItemName,jdbcType=VARCHAR},
      </if>

    </trim>

  </insert>

</mapper>
