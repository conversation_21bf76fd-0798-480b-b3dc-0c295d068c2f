<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.sfc.JobsSupplierProduceDataTacRepository">

    <select id="getProductSumList" resultType="com.zte.interfaces.dto.sfc.PilotProductParamVO">
        select
        CONTRACT_NUMBER entityName ,
        WORK_UNIT workUnit,
        #{paramName} as paramName,
        ${paramName} as paramValue
        from SFC.JOBS_SUPPLIER_PRODUCE_DATA_TAC  t
        where t.ENABLED_FLAG = 'Y'
        <if test='paramType =="2"'>
            and ${paramName}  in
            <foreach collection="paramList" item="item" separator=','
                     open='(' close=')' index="index">
                #{item}
            </foreach>
        </if>
        <if test='paramType =="1"'>
            and  ${paramName}  between #{paramStart} and  #{paramEnd}
        </if>
    </select>

</mapper>