<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.CustomerItemsRepository">
    <insert id="newCustomerItems" parameterType="com.zte.interfaces.dto.CustomerItemsDTO">
        insert into customer_items
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="customerName != null and customerName != ''">
                customer_name,
            </if>
            <if test="projType != null and projType != ''">
                proj_type,
            </if>
            <if test="customerId != null and customerId != ''">
                customer_id,
            </if>
            <if test="projectName != null and projectName != ''">
                project_name,
            </if>
            <if test="projectPhase != null and projectPhase != ''">
                project_phase,
            </if>
            <if test="projectType != null and projectType != ''">
                project_type,
            </if>
            <if test="cooperationMode != null and cooperationMode != ''">
                cooperation_mode,
            </if>
            <if test="zteCodeName != null and zteCodeName != ''">
                zte_code_name,
            </if>
            <if test="zteCode != null and zteCode != ''">
                zte_code,
            </if>
            <if test="customerMaterialType != null and customerMaterialType != ''">
                customer_material_type,
            </if>
            <if test="customerCode != null and customerCode != ''">
                customer_code,
            </if>
            <if test="customerNumber != null and customerNumber != ''">
                customer_number,
            </if>
            <if test="customerItemName != null and customerItemName != ''">
                customer_item_name,
            </if>
            <if test="zteSupplier != null and zteSupplier != ''">
                zte_supplier,
            </if>
            <if test="customerSupplier != null and customerSupplier != ''">
                customer_supplier,
            </if>
            <if test="zteBrandStyle != null and zteBrandStyle != ''">
                zte_Brand_Style,
            </if>
            <if test="customerSpecification != null and customerSpecification != ''">
                customer_specification,
            </if>
            <if test="customerModel != null and customerModel != ''">
                customer_model,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="customerComponentType != null and customerComponentType != ''">
                customer_component_type,
            </if>
            <if test="boardType != null and boardType != ''">
                board_type,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                last_updated_by,
            </if>
            <if test="pnCode != null and pnCode != ''">
                pn_code,
            </if>
            <if test="additionalInfoId != null and additionalInfoId != ''">
                additional_info_id,
            </if>
            <if test="originalManufacturerName != null and originalManufacturerName != ''">
                original_manufacturer_name,
            </if>
            <if test="powerSpecification != null and powerSpecification != ''">
                power_specification,
            </if>
            create_date,
            last_updated_date,
            enabled_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="customerName != null and customerName != ''">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="projType != null and projType != ''">
                #{projType,jdbcType=VARCHAR},
            </if>
            <if test="customerId != null and customerId != ''">
                #{customerId,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null and projectName != ''">
                #{projectName,jdbcType=VARCHAR},
            </if>
            <if test="projectPhase != null and projectPhase != ''">
                #{projectPhase,jdbcType=VARCHAR},
            </if>
            <if test="projectType != null and projectType != ''">
                #{projectType,jdbcType=VARCHAR},
            </if>
            <if test="cooperationMode != null and cooperationMode != ''">
                #{cooperationMode,jdbcType=VARCHAR},
            </if>
            <if test="zteCodeName != null and zteCodeName != ''">
                #{zteCodeName,jdbcType=VARCHAR},
            </if>
            <if test="zteCode != null and zteCode != ''">
                #{zteCode,jdbcType=VARCHAR},
            </if>
            <if test="customerMaterialType != null and customerMaterialType != ''">
                #{customerMaterialType,jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null and customerCode != ''">
                #{customerCode,jdbcType=VARCHAR},
            </if>
            <if test="customerNumber != null and customerNumber != ''">
                #{customerNumber,jdbcType=VARCHAR},
            </if>
            <if test="customerItemName != null and customerItemName != ''">
                #{customerItemName,jdbcType=VARCHAR},
            </if>
            <if test="zteSupplier != null and zteSupplier != ''">
                #{zteSupplier,jdbcType=VARCHAR},
            </if>
            <if test="customerSupplier != null and customerSupplier != ''">
                #{customerSupplier,jdbcType=VARCHAR},
            </if>
            <if test="zteBrandStyle != null and zteBrandStyle != ''">
                #{zteBrandStyle,jdbcType=VARCHAR},
            </if>
            <if test="customerSpecification != null and customerSpecification != ''">
                #{customerSpecification,jdbcType=VARCHAR},
            </if>
            <if test="customerModel != null and customerModel != ''">
                #{customerModel,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="customerComponentType != null and customerComponentType != ''">
                #{customerComponentType,jdbcType=VARCHAR},
            </if>
            <if test="boardType != null and boardType != ''">
                #{boardType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
                #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="pnCode != null and pnCode != ''">
                #{pnCode,jdbcType=VARCHAR},
            </if>
            <if test="additionalInfoId != null and additionalInfoId != ''">
                #{additionalInfoId,jdbcType=VARCHAR},
            </if>
            <if test="originalManufacturerName != null and originalManufacturerName != ''">
                #{originalManufacturerName,jdbcType=VARCHAR},
            </if>
            <if test="powerSpecification != null and powerSpecification != ''">
                #{powerSpecification,jdbcType=VARCHAR},
            </if>
            sysdate,
            sysdate,
            'Y'
        </trim>
    </insert>

    <select id="checkItemsExist" parameterType="com.zte.interfaces.dto.CustomerItemsDTO" resultType="java.lang.Integer">
        select 1 from
        customer_items
        where enabled_flag = 'Y'
        <if test="projectType != null and projectType == '4'.toString()">
            and project_type = '4'
            and customer_name = #{customerName,jdbcType=VARCHAR}
            and zte_supplier = #{zteSupplier,jdbcType=VARCHAR}
            and zte_code = #{zteCode,jdbcType=VARCHAR}
            and zte_Brand_Style = #{zteBrandStyle,jdbcType=VARCHAR}
        </if>
        <if test="projectType != null and projectType == '5'.toString()">
            and project_type = #{projectType,jdbcType=VARCHAR}
            and customer_name = #{customerName,jdbcType=VARCHAR}
            and zte_code = #{zteCode,jdbcType=VARCHAR}
            and zte_Brand_Style = #{zteBrandStyle,jdbcType=VARCHAR}
        </if>
        <if test="projectType != null and projectType == '3'.toString()">
            and project_type = #{projectType,jdbcType=VARCHAR}
            and customer_name = #{customerName,jdbcType=VARCHAR}
            and project_name = #{projectName,jdbcType=VARCHAR}
            and zte_code = #{zteCode,jdbcType=VARCHAR}
        </if>
        <if test="projectType != null and projectType != '4'.toString() and projectType != '3'.toString() and projectType != '5'.toString()">
            and project_type = #{projectType,jdbcType=VARCHAR}
            and zte_code = #{zteCode,jdbcType=VARCHAR}
            and customer_name = #{customerName,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>

    <select id="pageCustomerItemsInfo" parameterType="com.zte.springbootframe.common.model.Page" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select id, customer_name, customer_id, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code, customer_item_name,customer_number,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model,proj_type,
        status, remark, create_by, create_date, last_updated_by, last_updated_date, enabled_flag,
        customer_component_type, board_type,pn_code,original_manufacturer_name,power_specification,additional_info_id
        from customer_items
        where enabled_flag = 'Y'
        <if test="params.customerName != null and params.customerName != ''">
            and customer_name = #{params.customerName,jdbcType=VARCHAR}
        </if>
        <if test="params.projectName != null and params.projectName != ''">
            and project_name = #{params.projectName,jdbcType=VARCHAR}
        </if>
        <if test="params.zteCode != null and params.zteCode != ''">
            and zte_code = #{params.zteCode,jdbcType=VARCHAR}
        </if>
        <if test="params.customerCode != null and params.customerCode != ''">
            and customer_code = #{params.customerCode,jdbcType=VARCHAR}
        </if>
        <if test="params.createBy != null and params.createBy != ''">
            and create_by = #{params.createBy,jdbcType=VARCHAR}
        </if>
        <if test="params.lastUpdatedBy != null and params.lastUpdatedBy != ''">
            and last_updated_by = #{params.lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        <if test="params.projectTypeList != null and params.projectTypeList.size() >0">
            and project_type in
            <foreach collection="params.projectTypeList" open="(" index="index" separator="," close=")">
                #{params.projectTypeList[${index}]}
            </foreach>
        </if>
        <if test="params.customerNameList != null and params.customerNameList.size() >0">
            and CUSTOMER_NAME in
            <foreach collection="params.customerNameList" open="(" index="index" separator="," close=")">
                #{params.customerNameList[${index}]}
            </foreach>
        </if>
        <if test="params.startCreateDate != null and params.endCreateDate != null">
            and create_date > #{params.startCreateDate,jdbcType=TIMESTAMP}
            and create_date <![CDATA[ < ]]> #{params.endCreateDate,jdbcType=TIMESTAMP}
        </if>
        <if test="params.startUpdateDate != null and params.endUpdateDate != null">
            and last_updated_date > #{params.startUpdateDate,jdbcType=TIMESTAMP}
            and last_updated_date <![CDATA[ < ]]> #{params.endUpdateDate,jdbcType=TIMESTAMP}
        </if>
        order by last_updated_date desc
    </select>

    <update id="deleteCustomerItems">
        update
        customer_items
        set
        last_updated_by = #{empNo},
        last_updated_date = sysdate,
        enabled_flag = 'N'
        where id = #{id}
        and enabled_flag = 'Y'
    </update>

    <update id="updateCustomerItems" parameterType="com.zte.interfaces.dto.CustomerItemsDTO">
        update
        customer_items
        set
            customer_id = #{customerId,jdbcType=VARCHAR},
            project_Phase = #{projectPhase,jdbcType=VARCHAR},
            proj_type = #{projType,jdbcType=VARCHAR},
            project_name = #{projectName,jdbcType=VARCHAR},
            project_type = #{projectType,jdbcType=VARCHAR},
            cooperation_Mode = #{cooperationMode,jdbcType=VARCHAR},
            zte_Code_Name = #{zteCodeName,jdbcType=VARCHAR},
            zte_Code = #{zteCode,jdbcType=VARCHAR},
            customer_Material_Type = #{customerMaterialType,jdbcType=VARCHAR},
            customer_Code = #{customerCode,jdbcType=VARCHAR},
            customer_item_name = #{customerItemName,jdbcType=VARCHAR},
            zte_Supplier = #{zteSupplier,jdbcType=VARCHAR},
            customer_Supplier = #{customerSupplier,jdbcType=VARCHAR},
            zte_Brand_Style = #{zteBrandStyle,jdbcType=VARCHAR},
            customer_specification = #{customerSpecification,jdbcType=VARCHAR},
            customer_model = #{customerModel,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            customer_component_type = #{customerComponentType,jdbcType=VARCHAR},
            original_manufacturer_name = #{originalManufacturerName,jdbcType=VARCHAR},
            power_specification = #{powerSpecification,jdbcType=VARCHAR},
            board_type = #{boardType,jdbcType=VARCHAR},
            pn_code = #{pnCode,jdbcType=VARCHAR},
            additional_info_id = #{additionalInfoId,jdbcType=VARCHAR},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = sysdate
        where enabled_flag = 'Y'
        and id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getSubCustomerItemsInfo" parameterType="com.zte.interfaces.dto.CustomerItemsDTO"
            resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        customer_name, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code, customer_item_name,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model, customer_component_type, board_type
        ,pn_code,additional_info_id,original_manufacturer_name,power_specification
        from customer_items
        where enabled_flag = 'Y'
        <if test="customerSupplier!=null and customerSupplier !=''">
            and CUSTOMER_SUPPLIER = #{customerSupplier,jdbcType=VARCHAR}
        </if>
        <if test="customerName!=null and customerName !=''">
            and customer_name = #{customerName,jdbcType=VARCHAR}
        </if>
        <if test="originalManufacturerName!=null and originalManufacturerName !=''">
            and original_manufacturer_name = #{originalManufacturerName,jdbcType=VARCHAR}
        </if>
        and zte_code in
        <foreach item="item" collection="itemNoList" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <select id="getCustomerItemsByCustomerAndMode" parameterType="com.zte.interfaces.dto.CustomerItemsDTO"
            resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        customer_name, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code, customer_item_name,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model, customer_component_type, board_type
        ,pn_code,additional_info_id,original_manufacturer_name,power_specification,customer_number,proj_type,
        status, remark, create_by, create_date, last_updated_by, last_updated_date
        from customer_items
        where enabled_flag = 'Y'
        and customer_name = #{customerName,jdbcType=VARCHAR}
        and cooperation_mode = #{cooperationMode,jdbcType=VARCHAR}
        <if test="itemNoList != null and itemNoList.size() > 0">
            and zte_code in
            <foreach item="item" collection="itemNoList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        order by last_updated_date desc
    </select>

    <select id="getCustomerItemsInfoSelfDevelopedByItemNoList" parameterType="com.zte.interfaces.dto.CustomerItemsDTO"
            resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        customer_name, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code,customer_item_name,proj_type,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model, customer_component_type, board_type
        ,pn_code,additional_info_id,power_specification
        from customer_items
        where enabled_flag = 'Y' and project_type in ('0','1','2') and status ='Y'
        <if test="customerName !=null and customerName !=''">
            and  customer_name = #{customerName,jdbcType=VARCHAR}
        </if>
        <if test="customerSupplier !=null and customerSupplier !=''">
            and  CUSTOMER_SUPPLIER = #{customerSupplier,jdbcType=VARCHAR}
        </if>
        <if test="itemNoList != null and itemNoList.size()>0">
            and zte_code in
            <foreach item="item" collection="itemNoList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if
                test="(customerName == null or customerName == '')
                and (customerSupplier == null or customerSupplier == '')
                and (itemNoList == null or itemNoList.size()==0)">
            and 1 = 2
        </if>

    </select>

    <select id="queryCustomerItemsById" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        customer_component_type,additional_info_id
        from customer_items
        where enabled_flag = 'Y'
        and id =#{id}
    </select>

    <select id="queryListByCustomerList"
            resultType="com.zte.interfaces.dto.CustomerItemsDTO" parameterType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        customer_name, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code,customer_item_name,proj_type,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model, customer_component_type, board_type
        ,pn_code,additional_info_id,customer_number
        from customer_items
        where enabled_flag = 'Y'
        <if test="customerNameList != null and customerNameList.size()>0">
            and customer_name in
            <foreach collection="customerNameList" open="(" separator=","  item="item" close=")">
                #{item}
            </foreach>
        </if>
        <if test="itemNoList != null and itemNoList.size()>0">
            and zte_code in
            <foreach item="item" collection="itemNoList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="(customerNameList == null or customerNameList.size()==0) and (itemNoList == null or itemNoList.size()==0)">
            and 1 = 2
        </if>
    </select>

    <select id="checkItemsExistBatch" parameterType="java.util.List" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
            zte_code as zteCode,
            project_type as projectType,
            customer_name as customerName,
            zte_supplier as zteSupplier,
            zte_Brand_Style as zteBrandStyle,
            project_name as projectName
        from customer_items
        where enabled_flag = 'Y'
        <if test="list != null and list.size > 0">
            <trim prefix="and (" suffix=")" suffixOverrides="or">
                <foreach collection="list" item="item" separator="or">
                    <trim prefix="(" suffix=")" suffixOverrides="and">
                        <if test="item.projectType != null and item.projectType != ''">
                            project_type = #{item.projectType,jdbcType=VARCHAR} and
                        </if>
                        <if test="item.zteCode != null and item.zteCode != ''">
                            zte_code = #{item.zteCode,jdbcType=VARCHAR} and
                        </if>
                        <if test="item.projectType != null and item.projectType == '4'.toString()">
                            customer_name = #{item.customerName,jdbcType=VARCHAR} and
                            zte_supplier = #{item.zteSupplier,jdbcType=VARCHAR} and
                            zte_Brand_Style = #{item.zteBrandStyle,jdbcType=VARCHAR} and
                        </if>
                        <if test="item.projectType != null and item.projectType == '5'.toString()">
                            customer_name = #{item.customerName,jdbcType=VARCHAR} and
                            zte_Brand_Style = #{item.zteBrandStyle,jdbcType=VARCHAR} and
                        </if>
                        <if test="item.projectType != null and item.projectType == '3'.toString()">
                            customer_name = #{item.customerName,jdbcType=VARCHAR} and
                            project_name = #{item.projectName,jdbcType=VARCHAR} and
                        </if>
                    </trim>
                </foreach>
            </trim>
        </if>
        <if test="list == null or list.size == 0">
            and 1 = 0
        </if>
        group by
            zte_code,
            project_type,
            customer_name,
            zte_supplier,
            zte_Brand_Style,
            project_name
    </select>



    <insert id="batchInsertCustomerItems" parameterType="java.util.List">
        INSERT INTO customer_items (
        id, customer_name, proj_type, customer_id, project_name, project_phase,
        project_type, cooperation_mode, zte_code_name, zte_code, customer_material_type,
        customer_code, customer_number, customer_item_name, zte_supplier, customer_supplier, zte_Brand_Style,
        customer_specification, customer_model, status, customer_component_type, board_type,
        remark, create_by, last_updated_by, pn_code, additional_info_id, original_manufacturer_name,
        create_date, last_updated_date, enabled_flag
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.customerName,jdbcType=VARCHAR},
            #{item.projType,jdbcType=VARCHAR},
            #{item.customerId,jdbcType=VARCHAR},
            #{item.projectName,jdbcType=VARCHAR},
            #{item.projectPhase,jdbcType=VARCHAR},
            #{item.projectType,jdbcType=VARCHAR},
            #{item.cooperationMode,jdbcType=VARCHAR},
            #{item.zteCodeName,jdbcType=VARCHAR},
            #{item.zteCode,jdbcType=VARCHAR},
            #{item.customerMaterialType,jdbcType=VARCHAR},
            #{item.customerCode,jdbcType=VARCHAR},
            #{item.customerNumber,jdbcType=VARCHAR},
            #{item.customerItemName,jdbcType=VARCHAR},
            #{item.zteSupplier,jdbcType=VARCHAR},
            #{item.customerSupplier,jdbcType=VARCHAR},
            #{item.zteBrandStyle,jdbcType=VARCHAR},
            #{item.customerSpecification,jdbcType=VARCHAR},
            #{item.customerModel,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.customerComponentType,jdbcType=VARCHAR},
            #{item.boardType,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.pnCode,jdbcType=VARCHAR},
            #{item.additionalInfoId,jdbcType=VARCHAR},
            #{item.originalManufacturerName,jdbcType=VARCHAR},
            SYSDATE, -- create_date
            SYSDATE, -- last_updated_date
            'Y'      -- enabled_flag
            )
        </foreach>
    </insert>

    <update id="deleteCustomerItemsByZteCodes">
        update
        customer_items
        set
        last_updated_date = sysdate,
        enabled_flag = 'N'
        where zte_code in
        <foreach collection="zteCodes" open="(" separator=","  item="item" close=")">
            #{item}
        </foreach>
        and enabled_flag = 'Y'
    </update>

    <select id="queryListByZteCodes" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        id, customer_name, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code, customer_number,customer_item_name,proj_type,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model, customer_component_type, board_type
        ,pn_code,additional_info_id, create_by, last_updated_by, create_date, last_updated_date, enabled_flag
        from customer_items
        where enabled_flag = 'Y'
        and zte_code in
        <foreach collection="zteCodes" open="(" separator=","  item="item" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getZteCodeByCustomerName" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select distinct zte_code, customer_name
        from customer_items
        where enabled_flag = 'Y'
        and customer_name in
        <foreach collection="customerNameList" open="(" separator=","  item="item" close=")">
            #{item}
        </foreach>
        order by zte_code
        limit #{rows}::numeric offset ((#{page}::numeric - 1) * #{rows}::numeric)
    </select>


    <update id="batchUpdateByZteCode" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE customer_items
            SET
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="item.customerId != null and item.customerId != ''">
                    customer_id = #{item.customerId,jdbcType=VARCHAR},
                </if>
                <if test="item.projectPhase != null and item.projectPhase != ''">
                    project_Phase = #{item.projectPhase,jdbcType=VARCHAR},
                </if>
                <if test="item.projType != null and item.projType != ''">
                    proj_type = #{item.projType,jdbcType=VARCHAR},
                </if>
                <if test="item.cooperationMode != null and item.cooperationMode != ''">
                    cooperation_Mode = #{item.cooperationMode,jdbcType=VARCHAR},
                </if>
                <if test="item.zteCodeName != null and item.zteCodeName != ''">
                    zte_Code_Name = #{item.zteCodeName,jdbcType=VARCHAR},
                </if>
                <if test="item.zteCode != null and item.zteCode != ''">
                    zte_Code = #{item.zteCode,jdbcType=VARCHAR},
                </if>
                <if test="item.customerMaterialType != null and item.customerMaterialType != ''">
                    customer_Material_Type = #{item.customerMaterialType,jdbcType=VARCHAR},
                </if>
                <if test="item.customerCode != null and item.customerCode != ''">
                    customer_Code = #{item.customerCode,jdbcType=VARCHAR},
                </if>
                <if test="item.customerNumber != null and item.customerNumber != ''">
                    customer_number = #{item.customerNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.customerName != null and item.customerName != ''">
                    customer_name = #{item.customerName,jdbcType=VARCHAR},
                </if>
                <if test="item.customerItemName != null and item.customerItemName != ''">
                    customer_item_name = #{item.customerItemName,jdbcType=VARCHAR},
                </if>
                <if test="item.zteSupplier != null and item.zteSupplier != ''">
                    zte_Supplier = #{item.zteSupplier,jdbcType=VARCHAR},
                </if>
                <if test="item.customerSupplier != null and item.customerSupplier != ''">
                    customer_Supplier = #{item.customerSupplier,jdbcType=VARCHAR},
                </if>
                <if test="item.zteBrandStyle != null and item.zteBrandStyle != ''">
                    zte_Brand_Style = #{item.zteBrandStyle,jdbcType=VARCHAR},
                </if>
                <if test="item.customerSpecification != null and item.customerSpecification != ''">
                    customer_specification = #{item.customerSpecification,jdbcType=VARCHAR},
                </if>
                <if test="item.customerModel != null and item.customerModel != ''">
                    customer_model = #{item.customerModel,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.customerComponentType != null and item.customerComponentType != ''">
                    customer_component_type = #{item.customerComponentType,jdbcType=VARCHAR},
                </if>
                <if test="item.originalManufacturerName != null and item.originalManufacturerName != ''">
                    original_manufacturer_name = #{item.originalManufacturerName,jdbcType=VARCHAR},
                </if>
                <if test="item.powerSpecification != null and item.powerSpecification != ''">
                    power_specification = #{item.powerSpecification,jdbcType=VARCHAR},
                </if>
                <if test="item.boardType != null and item.boardType != ''">
                    board_type = #{item.boardType,jdbcType=VARCHAR},
                </if>
                <if test="item.pnCode != null and item.pnCode != ''">
                    pn_code = #{item.pnCode,jdbcType=VARCHAR},
                </if>
                <if test="item.additionalInfoId != null and item.additionalInfoId != ''">
                    additional_info_id = #{item.additionalInfoId,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdatedBy != null and item.lastUpdatedBy != ''">
                    last_updated_by = #{item.lastUpdatedBy,jdbcType=VARCHAR},
                </if>
                <!-- 始终更新时间 -->
                last_updated_date = SYSDATE
            </trim>
            WHERE enabled_flag = 'Y'
            AND zte_code = #{item.zteCode,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getByCustomerCode" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select t.zte_code, t.customer_code from customer_items t
        where t.enabled_flag = 'Y'
        and t.customer_code in
        <foreach collection="customerCodeList" open="(" separator="," item="customerCode" close=")">
            #{customerCode}
        </foreach>
    </select>

    <select id="getSameItemOfZteCode" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select a.zte_code, a.customer_code, b.customer_code as original_customer_code from customer_items a,
        (
            select distinct t.zte_code, t.customer_code from customer_items t
            where t.enabled_flag = 'Y'
            and t.customer_code != ''
            and t.customer_code is not null
            and t.zte_code in
            <foreach collection="zteCodeList" open="(" separator="," item="zteCode" close=")">
                #{zteCode}
            </foreach>
        ) b
        where a.enabled_flag = 'Y'
        and a.customer_code like '%' || b.customer_code || '%'
        <if test="customerNumberList != null and customerNumberList.size() > 0">
            and a.customer_number in
            <foreach collection="customerNumberList" open="(" separator="," item="customerNumber" close=")">
                #{customerNumber}
            </foreach>
        </if>
    </select>

    <select id="queryListByZteCodAndCustomerNumber" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        select
        customer_name, project_name, project_phase, project_type,
        cooperation_mode, zte_code_name, zte_code, customer_material_type, customer_code, customer_number,customer_item_name,proj_type,
        zte_supplier, customer_supplier, zte_Brand_Style, customer_specification, customer_model, customer_component_type, board_type
        ,pn_code,additional_info_id
        from customer_items
        where enabled_flag = 'Y'
        and zte_code in
        <foreach collection="zteCodes" open="(" separator=","  item="item" close=")">
            #{item}
        </foreach>
        and customer_number = #{customerNumber,jdbcType=VARCHAR}
    </select>

    <select id="getZteCodeOfSameItem" resultType="com.zte.interfaces.dto.CustomerItemsDTO">
        with t1 as (
            select t.zte_code, unnest(string_to_array(t.customer_code, '//')) as original_customer_code, t.customer_code
            from customer_items t,
            (
                <foreach collection="itemSupplierNoList" item="item" index="index" separator="UNION">
                    select #{item} as customer_code
                </foreach>
            )t0
            where t.enabled_flag = 'Y'
            and t.customer_number in
            <foreach collection="customerNumberList" open="(" separator="," item="customerNumber" close=")">
                #{customerNumber}
            </foreach>
            and t.customer_code like '%' || t0.customer_code || '%'
        ), t2 as (
            select distinct customer_code
            from t1 where original_customer_code in
            <foreach collection="itemSupplierNoList" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
        )
        select distinct t1.zte_code, t1.original_customer_code, t1.customer_code from t1 join t2 on t1.customer_code = t2.customer_code
    </select>

</mapper>
