<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.OtherSysProdBarcodeListRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.OtherSysProdBarcodeList">
        <id column="RECORD_ID" jdbcType="DECIMAL" property="recordId"/>
        <result column="PRODPLAN_ID" jdbcType="DECIMAL" property="prodplanId"/>
        <result column="BARCODE" jdbcType="VARCHAR" property="barcode"/>
        <result column="LIST_TYPE" jdbcType="VARCHAR" property="listType"/>
        <result column="LAST_UPDATED_DATE" jdbcType="TIMESTAMP" property="lastUpdatedDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy"/>
        <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy"/>
        <result column="ORGANIZATION_ID" jdbcType="DECIMAL" property="organizationId"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="IS_SUBMIT" jdbcType="VARCHAR" property="isSubmit"/>
        <result column="WORK_ORDER_NO" jdbcType="VARCHAR" property="workOrderNo"/>
    </resultMap>

    <resultMap id="BaseResultDTOMap" type="com.zte.interfaces.dto.EnetySupplierProduceDateWBDTO">
        <result column="PROD_CLASS" jdbcType="VARCHAR" property="prodClass"/>
        <result column="PROD_TYPE" jdbcType="VARCHAR" property="prodType"/>
        <result column="PROD_NAME" jdbcType="VARCHAR" property="prodName"/>
        <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        RECORD_ID,PRODPLAN_ID, BARCODE, LIST_TYPE, LAST_UPDATED_DATE, LAST_UPDATED_BY, CREATION_DATE,
        CREATED_BY, ORGANIZATION_ID, ENABLED_FLAG, IS_SUBMIT, WORK_ORDER_NO
    </sql>

    <select id="selectOtherSysProdBarcodeListById" parameterType="com.zte.domain.model.datawb.OtherSysProdBarcodeList"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from OTHER_SYS_PROD_BARCODE_LIST
        where RECORD_ID = #{recordId,jdbcType=DECIMAL}
    </select>

    <delete id="deleteOtherSysProdBarcodeListById" parameterType="com.zte.domain.model.datawb.OtherSysProdBarcodeList">
        delete from OTHER_SYS_PROD_BARCODE_LIST
        where RECORD_ID = #{recordId,jdbcType=DECIMAL}
    </delete>

    <insert id="insertOtherSysProdBarcodeList" parameterType="com.zte.domain.model.datawb.OtherSysProdBarcodeList">
        <selectKey keyProperty="recordId" order="BEFORE" resultType="java.math.BigDecimal">
            SELECT OTHER_SYS_PROD_BARCODE_LIST_S.nextval recordId from dual
        </selectKey>
        insert into OTHER_SYS_PROD_BARCODE_LIST (RECORD_ID,PRODPLAN_ID, BARCODE, LIST_TYPE,
        LAST_UPDATED_DATE, LAST_UPDATED_BY, CREATION_DATE,
        CREATED_BY, ORGANIZATION_ID, ENABLED_FLAG,
        IS_SUBMIT, WORK_ORDER_NO)
        values (#{recordId},#{prodplanId,jdbcType=DECIMAL}, #{barcode,jdbcType=VARCHAR}, #{listType,jdbcType=VARCHAR},
        SYSDATE, #{lastUpdatedBy,jdbcType=DECIMAL}, SYSDATE,
        #{createdBy,jdbcType=DECIMAL}, #{organizationId,jdbcType=DECIMAL}, #{enabledFlag,jdbcType=VARCHAR},
        #{isSubmit,jdbcType=VARCHAR}, #{workOrderNo,jdbcType=VARCHAR})
    </insert>

    <insert id="insertOtherSysProdBarcodeListSelective"
            parameterType="com.zte.domain.model.datawb.OtherSysProdBarcodeList">
        <selectKey keyProperty="recordId" order="BEFORE" resultType="java.math.BigDecimal">
            SELECT OTHER_SYS_PROD_BARCODE_LIST_S.nextval recordId from dual
        </selectKey>
        insert into OTHER_SYS_PROD_BARCODE_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RECORD_ID,
            <if test="prodplanId != null">
                PRODPLAN_ID,
            </if>

            <if test="barcode != null">
                BARCODE,
            </if>

            <if test="listType != null">
                LIST_TYPE,
            </if>

            LAST_UPDATED_DATE,

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY,
            </if>

            SYSDATE,

            <if test="createdBy != null">
                CREATED_BY,
            </if>

            <if test="organizationId != null">
                ORGANIZATION_ID,
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG,
            </if>

            <if test="isSubmit != null">
                IS_SUBMIT,
            </if>

            <if test="workOrderNo != null">
                WORK_ORDER_NO,
            </if>

        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{recordId},
            <if test="prodplanId != null">
                #{prodplanId,jdbcType=DECIMAL},
            </if>

            <if test="barcode != null">
                #{barcode,jdbcType=VARCHAR},
            </if>

            <if test="listType != null">
                #{listType,jdbcType=VARCHAR},
            </if>

            SYSDATE,

            <if test="lastUpdatedBy != null">
                #{lastUpdatedBy,jdbcType=DECIMAL},
            </if>

            SYSDATE,

            <if test="createdBy != null">
                #{createdBy,jdbcType=DECIMAL},
            </if>

            <if test="organizationId != null">
                #{organizationId,jdbcType=DECIMAL},
            </if>

            <if test="enabledFlag != null">
                #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="isSubmit != null">
                #{isSubmit,jdbcType=VARCHAR},
            </if>

            <if test="workOrderNo != null">
                #{workOrderNo,jdbcType=VARCHAR},
            </if>

        </trim>

    </insert>

    <update id="updateOtherSysProdBarcodeListByIdSelective"
            parameterType="com.zte.domain.model.datawb.OtherSysProdBarcodeList">
        update OTHER_SYS_PROD_BARCODE_LIST
        <set>
            <if test="prodplanId != null">
                PRODPLAN_ID = #{prodplanId,jdbcType=DECIMAL},
            </if>
            <if test="barcode != null">
                BARCODE = #{barcode,jdbcType=VARCHAR},
            </if>

            <if test="listType != null">
                LIST_TYPE = #{listType,jdbcType=VARCHAR},
            </if>

            LAST_UPDATED_DATE = SYSDATE,

            <if test="lastUpdatedBy != null">
                LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=DECIMAL},
            </if>

            <if test="organizationId != null">
                ORGANIZATION_ID = #{organizationId,jdbcType=DECIMAL},
            </if>

            <if test="enabledFlag != null">
                ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
            </if>

            <if test="isSubmit != null">
                IS_SUBMIT = #{isSubmit,jdbcType=VARCHAR},
            </if>

            <if test="workOrderNo != null">
                WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR},
            </if>

        </set>
        where RECORD_ID = #{recordId,jdbcType=DECIMAL}
    </update>

    <update id="updateOtherSysProdBarcodeListById" parameterType="com.zte.domain.model.datawb.OtherSysProdBarcodeList">
        update OTHER_SYS_PROD_BARCODE_LIST
        set PRODPLAN_ID = #{prodplanId,jdbcType=DECIMAL},
        BARCODE = #{barcode,jdbcType=VARCHAR},
        LIST_TYPE = #{listType,jdbcType=VARCHAR},
        LAST_UPDATED_DATE = SYSDATE,
        LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=DECIMAL},
        ORGANIZATION_ID = #{organizationId,jdbcType=DECIMAL},
        ENABLED_FLAG = #{enabledFlag,jdbcType=VARCHAR},
        IS_SUBMIT = #{isSubmit,jdbcType=VARCHAR},
        WORK_ORDER_NO = #{workOrderNo,jdbcType=VARCHAR}
        where RECORD_ID = #{recordId,jdbcType=DECIMAL}
    </update>

    <update id="updateIMUList">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            update kxbariii.board_online
            <set>imu_idold=imu_id,IMU_ID = #{imuId},scan_date=sysdate,CARD_NO=#{item.cardNo,jdbcType=VARCHAR}</set>
            WHERE PRODPLAN_ID = #{item.prodplanId,jdbcType=DECIMAL}
            and BOARD_SN = #{item.boardSn,jdbcType=DECIMAL}
        </foreach>
    </update>

    <update id="updateImuBatch">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            update kxbariii.board_online
            <set>imu_idold=#{imuIdOld},IMU_ID = #{imuId},scan_date=sysdate,CARD_NO=#{item.cardNo,jdbcType=VARCHAR}</set>
            WHERE PRODPLAN_ID = #{item.prodplanId,jdbcType=DECIMAL}
            and BOARD_SN = #{item.boardSn,jdbcType=DECIMAL}
        </foreach>
    </update>

    <select id="getItemNoList" parameterType="com.zte.interfaces.dto.EnetySupplierProduceDateWBDTO"
            resultMap="BaseResultDTOMap">
        SELECT DISTINCT BI.ITEM_NO
        FROM kxstepiii.BA_ITEM_CLASS BIC, BA_ITEM BI
        WHERE (BIC.MASTER_NO || BIC.SMALL_NO || BIC.SUB_SMALL_NO) = BI.CLASS_NO
        <if test="prodClass != null and prodClass!='' ">and BIC.NAME = #{prodClass,jdbcType=VARCHAR}</if>
        <if test="prodType != null and  prodType!=''">and BI.STYLE = #{prodType,jdbcType=VARCHAR}</if>
        <if test="prodName != null and prodName!=''">and BI.ITEM_NAME = #{prodName,jdbcType=VARCHAR}</if>
    </select>

    <select id="getListByItemNo" parameterType="com.zte.interfaces.dto.EnetySupplierProduceDateWBDTO"
            resultMap="BaseResultDTOMap">
        SELECT BIC.NAME PROD_CLASS,
        BI.STYLE PROD_TYPE,
        BI.ITEM_NAME PROD_NAME,
        BI.ITEM_NO
        FROM kxstepiii.BA_ITEM_CLASS BIC
        JOIN kxstepiii.BA_ITEM BI
        ON (BIC.MASTER_NO || BIC.SMALL_NO || BIC.SUB_SMALL_NO) = BI.CLASS_NO 　
        WHERE 1=1
        <if test="itemNo != null and itemNo != ''">
            and BI.ITEM_NO= #{itemNo}
        </if>
        <if test="itemNos != null and itemNos.size >  0">
            AND BI.ITEM_NO IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR BI.ITEM_NO IN()">    <!-- 表示删除最后一个条件 -->
                <foreach collection="itemNos" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR BI.ITEM_NO IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
        </if>
        <if test="(itemNo == null or itemNo == '') and (itemNos == null or itemNos.size() == 0)">
            and 1=2
        </if>
    </select>

    <select id="selectBoardOnline" resultType="java.lang.String">
        SELECT BOARD_SN FROM kxbariii.board_online
        WHERE
        PRODPLAN_ID = #{prodplanId}
        and BOARD_SN IN
        <foreach collection="list" open="(" separator="," item="item" close=")">
            #{item.boardSn}
        </foreach>
    </select>

</mapper>
