<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.datawb.BoardBarcodeTestResultRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.BoardBarcodeTestResultEntityDTO" id="boardBarcodeTestResultMap">
        <result property="recordId" column="RECORD_ID" jdbcType="VARCHAR" />
        <result property="bomBarcode" column="BOM_BARCODE" jdbcType="VARCHAR" />
        <result property="isableFlag" column="ISABLE_FLAG" jdbcType="VARCHAR" />
        <result property="reason" column="REASON" jdbcType="VARCHAR" />
        <result property="inFlag" column="IN_FLAG" jdbcType="VARCHAR" />
        <result property="enableFlag" column="ENABLE_FLAG" jdbcType="VARCHAR" />
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"  />
        <result property="createDate" column="CREATE_DATE" jdbcType="DATE" />
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR" />
        <result property="updateDate" column="UPDATE_DATE" jdbcType="DATE" />
        <result property="attribute1" column="ATTRIBUTE1" jdbcType="VARCHAR" />
        <result property="attribute2" column="ATTRIBUTE2" jdbcType="VARCHAR" />
        <result property="attribute3" column="ATTRIBUTE3" jdbcType="DATE" />
    </resultMap>

    <sql id="Base_Column_List">
      RECORD_ID,
      BOM_BARCODE,
      ISABLE_FLAG,
      REASON,
      IN_FLAG,
      ENABLE_FLAG,
      CREATE_BY,
      CREATE_DATE,
      UPDATE_BY,
      UPDATE_DATE,
      ATTRIBUTE1,
      ATTRIBUTE2,
      ATTRIBUTE3
    </sql>

    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        merge into KXBARIII.BOARD_BARCODE_TEST_RESULT T
        using (
        <foreach collection="list" item="item" index="index" separator ="UNION ALL">
            select #{item.bomBarcode,jdbcType=VARCHAR} BOM_BARCODE, #{item.isableFlag,jdbcType=VARCHAR} ISABLE_FLAG,
            #{item.reason,jdbcType=VARCHAR} REASON, #{item.inFlag,jdbcType=VARCHAR} IN_FLAG,
            #{item.createBy,jdbcType=VARCHAR} CREATE_BY, #{item.createDate,jdbcType=TIMESTAMP} CREATE_DATE, #{item.updateBy,jdbcType=VARCHAR} UPDATE_BY,
            #{item.updateDate,jdbcType=TIMESTAMP} UPDATE_DATE,#{item.sourceSys,jdbcType=VARCHAR} SOURCE_SYS
          from dual
        </foreach>
        ) T1
        ON (
        T1.BOM_BARCODE = T.BOM_BARCODE
        )
        WHEN MATCHED THEN
        update
        set
        T.ISABLE_FLAG=T1.ISABLE_FLAG,
        T.REASON=T1.REASON,
        T.IN_FLAG=T1.IN_FLAG,
        T.UPDATE_BY=T1.UPDATE_BY,
        T.UPDATE_DATE= sysdate,
        T.SOURCE_SYS = T1.SOURCE_SYS
        WHEN NOT MATCHED THEN
        insert (RECORD_ID, BOM_BARCODE, ISABLE_FLAG,
        REASON, IN_FLAG, ENABLE_FLAG,
        CREATE_BY,CREATE_DATE, UPDATE_BY, UPDATE_DATE,SOURCE_SYS
        ) VALUES(kxbariii.board_barcode_test_resule_s.nextval, T1.BOM_BARCODE, T1.ISABLE_FLAG,
        T1.REASON, T1.IN_FLAG, 'Y',
        T1.CREATE_BY, sysdate,T1.UPDATE_BY,sysdate,T1.SOURCE_SYS)
    </insert>

    <select id="getList" parameterType="com.zte.interfaces.dto.BoardBarcodeTestResultEntityDTO" resultMap="boardBarcodeTestResultMap">
        select
        <include refid="Base_Column_List" />
        from KXBARIII.BOARD_BARCODE_TEST_RESULT where 1=1
        <if test="recordId != null and recordId != ''">and RECORD_ID = #{recordId}</if>
        <if test="bomBarcode != null and bomBarcode != ''">and BOM_BARCODE = #{bomBarcode}</if>
        <if test="bomBarcodeList != null and bomBarcodeList.size() >0">
            and BOM_BARCODE in
            <foreach collection="bomBarcodeList" item="item" index="index" open="("
                     separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="(recordId == null or recordId == '') and  (bomBarcode == null or bomBarcode == '') and (bomBarcodeList == null or bomBarcodeList.size() == 0 ) ">
            and 1=2
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
        insert into KXBARIII.BOARD_BARCODE_TEST_RESULT (
          RECORD_ID,
          BOM_BARCODE,
          ISABLE_FLAG,
          REASON,
          IN_FLAG,
          ENABLE_FLAG,
          CREATE_BY,
          CREATE_DATE,
          UPDATE_BY,
          UPDATE_DATE,
          ATTRIBUTE1,
          ATTRIBUTE2,
          ATTRIBUTE3)
        values (
          #{item.recordId,jdbcType=VARCHAR},
          #{item.bomBarcode,jdbcType=VARCHAR},
          #{item.isableFlag,jdbcType=VARCHAR},
          #{item.reason,jdbcType=VARCHAR},
          #{item.inFlag,jdbcType=VARCHAR},
          'Y',
          #{item.createBy,jdbcType=VARCHAR},
          sysdate,
          #{item.updateBy,jdbcType=VARCHAR},
          sysdate,
          #{item.attribute1,jdbcType=VARCHAR},
          #{item.attribute2,jdbcType=VARCHAR},
          sysdate
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item" open="begin" close="; end ;">
            update KXBARIII.BOARD_BARCODE_TEST_RESULT t set
              t.RECORD_ID=#{item.recordId,jdbcType=VARCHAR},
              t.BOM_BARCODE=#{item.bomBarcode,jdbcType=VARCHAR},
              t.ISABLE_FLAG=#{item.isableFlag,jdbcType=VARCHAR},
              t.REASON=#{item.reason,jdbcType=VARCHAR},
              t.IN_FLAG=#{item.inFlag,jdbcType=VARCHAR},
              t.UPDATE_BY=#{item.updateBy,jdbcType=VARCHAR},
              t.UPDATE_DATE=sysdate,
              t.ATTRIBUTE1=#{item.attribute1,jdbcType=VARCHAR},
              t.ATTRIBUTE2=#{item.attribute2,jdbcType=VARCHAR}
              where enable_flag = 'Y'
              and RECORD_ID = #{item.recordId}
        </foreach>
    </update>
</mapper>