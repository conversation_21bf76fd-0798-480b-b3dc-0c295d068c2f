<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ContractMaterialServiceRepository">
    <resultMap id="BarcodeMaterialResultMap" type="com.zte.interfaces.dto.ContractMaterialResultDTO">
        <result property="entityId" jdbcType="VARCHAR" column="ENTITY_ID"/>
        <result property="entityName" jdbcType="VARCHAR" column="ENTITY_NAME"/>
        <result property="mfgSiteName" jdbcType="VARCHAR" column="MFG_SITE_NAME"/>
        <result property="siteAdress" jdbcType="VARCHAR" column="SITE_ADDRESS"/>
        <result property="contactNumber" jdbcType="VARCHAR" column="CONTRACT_NUMBER"/>
        <result property="boxNo" jdbcType="VARCHAR" column="BOX_NO"/>
        <result property="itemCode" jdbcType="VARCHAR" column="ITEM_CODE"/>
        <result property="itemName" jdbcType="VARCHAR" column="ITEM_NAME"/>
        <result property="configDetailId" jdbcType="VARCHAR" column="CONFIG_DETAIL_ID"/>
        <result property="smallClassName" jdbcType="VARCHAR" column="SMALL_CLASS_NAME"/>
        <result property="englishItemName" jdbcType="VARCHAR" column="ENGLISH_ITEM_NAME"/>
        <result property="itemBarcode" jdbcType="VARCHAR" column="ITEM_BARCODE"/>
        <result property="qty" jdbcType="VARCHAR" column="QTY"/>
        <result property="seriesNo" jdbcType="VARCHAR" column="SERIES_NO"/>
        <result property="truckNo" jdbcType="VARCHAR" column="TRUCK_NO"/>
        <result property="mfgSiteEquipMent" jdbcType="VARCHAR" column="MFG_SITE_EQUIPMENT"/>
        <result property="productTypeName" jdbcType="VARCHAR" column="PRODUCT_TYPE_NAME"/>
        <result property="headerNo" jdbcType="VARCHAR" column="HEADER_NO"/>
        <result property="poNumber" jdbcType="VARCHAR" column="PO_NUMBER"/>
        <result property="length" jdbcType="VARCHAR" column="LENGTH"/>
        <result property="width" jdbcType="VARCHAR" column="WIDTH"/>
        <result property="height" jdbcType="VARCHAR" column="HEIGHT"/>
        <result property="cubage" jdbcType="VARCHAR" column="CUBAGE"/>
        <result property="netWeight" jdbcType="VARCHAR" column="NET_WEIGHT"/>
        <result property="grossWeight" jdbcType="VARCHAR" column="GROSS_WEIGHT"/>
        <result property="equipmentDESC" jdbcType="VARCHAR" column="EQUIPMENT_DESC"/>
        <result property="palletNO" jdbcType="VARCHAR" column="PALLET_NO"/>
        <result property="freightPlanNo" jdbcType="VARCHAR" column="FREIGHT_PLAN_NO"/>
        <result property="codeDesc" jdbcType="VARCHAR" column="CODE_DESC"/>
        <result property="frInsNo" jdbcType="VARCHAR" column="FR_INS_NO"/>
    </resultMap>

    <resultMap id="BarcodeCountResultMap" type="java.lang.Long">
        <result property="resultCursor" jdbcType="VARCHAR" column="NUMBER_COUNT"/>
    </resultMap>

    <select id="callBarcodeMaterialPage" statementType="CALLABLE" parameterType="map">
        {call APP_WMS.P_QUERY_CODE_CONTACT.QUERY_CODE_CONTACT_FOR_ID(
        #{map.boxNo,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.startRow,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.endRow,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=BarcodeMaterialResultMap}
        )}
    </select>

    <select id="callBarcodeMaterialCount" statementType="CALLABLE" parameterType="map">
        {call APP_WMS.P_QUERY_CODE_CONTACT.QUERY_CODE_CONTACT_FOR_COUNT(
        #{map.boxNo,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
        #{map.resultCursor,mode=OUT,jdbcType=CURSOR,resultMap=BarcodeCountResultMap}
        )}
    </select>

    <select id="getThirdItemCode"  parameterType="java.util.List" resultMap="BarcodeMaterialResultMap">
        SELECT
        distinct
        CCE.entity_id    ENTITY_ID, --任务id
        CCE.entity_name      ENTITY_NAME, --任务号
        b.mfg_site_name    MFG_SITE_NAME, --生产布点
        APP_MES.DEL_FIRSTCHAR(decode(s.CITY,null,null,',' || s.CITY) || decode(s.DISTRICT,null,null,',' || s.DISTRICT) || decode(s.SITE_ADDRESS,null,null,',' || s.SITE_ADDRESS))    SITE_ADDRESS, --站点
        CCH.CONTRACT_NUMBER  CONTRACT_NUMBER, --合同号
        a.bill_number      BOX_NO, --箱号
        msi.segment1       ITEM_CODE, --物料代码
        msi.description    ITEM_NAME, --物料名称
        CBI.CONFIG_DETAIL_ID,
        t.item_name_en   ENGLISH_ITEM_NAME ,--物料英文名称
        '' ITEM_BARCODE,
        round(CBI.Boxup_Qty,2) QTY,---数量
        '' SERIES_NO, ---序列号
        b.MFG_SITE_EQUIPMENT, --设备参数
        G.PRODUCT_TYPE_NAME, --产品大类
        NVL(B.USER_CON_NUMBER, SC.CUSTOMER_PO) AS PO_NUMBER ,--客户PO
        a.LENGTH LENGTH ,  --长
        a.WIDTH WIDTH ,   --宽
        a.HEIGHT  HEIGHT ,--高
        a.CUBAGE CUBAGE ,--体积
        a.NET_WEIGHT NET_WEIGHT,--净重
        a.GROSS_WEIGHT GROSS_WEIGHT,--毛重
        a.EQUIPMENT_DESC  EQUIPMENT_DESC,--设备描述
        PIM.PALLET_NO PALLET_NO  --托盘号
        FROM app_mes.cpm_boxup_bills a
        JOIN APP_MES.CPM_CONTRACT_ENTITIES CCE on CCE.Entity_Id=a.entity_id and CCE.Enabled_Flag='Y'
        JOIN APP_MES.CDM_CONTRACT_LINES   CCL  on CCE.CONTRACT_LINE_ID = CCL.CONTRACT_LINE_ID
        JOIN  APP_MES.CDM_CONTRACT_HEADERS  CCH  on CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID
        left join  APP_MES.CDM_PRODUCT_TYPE_DETAILS F  on CCL.INVENTORY_ITEM_ID=F.INVENTORY_ITEM_ID
        and CCL.ORGANIZATION_ID = F.ORGANIZATION_ID and F.ENABLED_FLAG= 'Y'
        left join  APP_MES.CDM_PRODUCT_TYPES  G  on F.PRODUCT_TYPE_ID = G.PRODUCT_TYPE_ID and F.ORGANIZATION_ID = G.ORGANIZATION_ID
        and  G.ENABLED_FLAG= 'Y'
        JOIN app_mes.cpm_contract_mfg_sites b ON a.mfg_site_id = b.mfg_site_id
        left JOIN APP_MES.CPM_BOXUP_BILL_ITEMS CBI on CBI.Bill_Id = a.bill_id
        and CBI.Enabled_Flag = 'Y'
        left JOIN app_mes.bas_material t on t.item_id = CBI.Inventory_Item_Id
        join APP_MES.MTL_SYSTEM_ITEMS MSI on CBI.Inventory_Item_Id =
        MSI.Inventory_Item_Id
        and CBI.Organization_Id =
        MSI.Organization_Id
        LEFT JOIN app_mes.cpm_contract_sites s ON s.site_id = b.site_id
        left JOIN app_mes.cdm_contract_headers sc ON SC.CONTRACT_HEADER_ID =
        s.CONTRACT_HEADER_ID
        left join  APP_MES.PALLET_SCAN_PACKING PSP on  a.BILL_NUMBER =PSP.BILL_NUMBER and psp.enabled_flag='Y'
        left join  APP_MES.PALLET_INFO_MANAGE PIM on PSP.PALLET_ID = PIM.PALLET_ID and PIM.Enabled_Flag='Y'
        where a.enabled_flag = 'Y'
        <if test="boxBillNumber != null and boxBillNumber.size()>0">
            AND (a.BILL_NUMBER IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR a.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
                <foreach collection="boxBillNumber" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR a.BILL_NUMBER IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
            )
        </if>

        <if test="configDetailIdList != null and configDetailIdList.size()>0">
            AND (CBI.CONFIG_DETAIL_ID IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR CBI.CONFIG_DETAIL_ID IN()">	<!-- 表示删除最后一个条件 -->
                <foreach collection="configDetailIdList" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR CBI.CONFIG_DETAIL_ID IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
            )
        </if>
        <if test="(boxBillNumber == null or boxBillNumber.size() == 0) and (configDetailIdList == null or configDetailIdList.size() == 0)">
            and 1=2
        </if>
    </select>

    <select id="getBarcoderByBillnumber"  parameterType="java.util.List" resultMap="BarcodeMaterialResultMap">
        <if test="startRow != null and endRow != null">
            select * from (select u.*, rownum r
            from (
        </if>
        select
        CBB.BILL_NUMBER BOX_NO,
        NVL(SCAN_INFO.SERIES_NO, SCAN_INFO.ITEM_BARCODE) AS SERIES_NO,
        NVL(SCAN_INFO.SERIES_NO, SCAN_INFO.ITEM_BARCODE) AS ITEM_BARCODE,
        SCAN_INFO.ITEM_CODE,
        round(NVL(SCAN_INFO.Scan_Qty,0),2) Qty,
        SCAN_INFO.CONFIG_DETAIL_ID
        from SFC.cpm_boxup_bills      CBB,
        SFC.CPM_BOXUP_BILL_ITEMS CBBI,
        SFC.PKG_SUBMAC_SCAN_INFO     SCAN_INFO
        where
        CBBI.Bill_Id = CBB.bill_id
        and CBBI.Enabled_Flag = 'Y'
        and CBB.Enabled_Flag = 'Y'
        AND SCAN_INFO.ORGANIZATION_ID = CBB.ORGANIZATION_ID
        AND SCAN_INFO.BILL_ID = CBB.BILL_ID
        AND SCAN_INFO.ENABLED_FLAG = 'Y'
        AND SCAN_INFO.CONFIG_DETAIL_ID = CBBI.CONFIG_DETAIL_ID
        <if test="boxBillNumber != null and boxBillNumber.size()>0">
            AND (CBB.BILL_NUMBER IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
                <foreach collection="boxBillNumber" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
            )
        </if>
        <if test="startRow != null and endRow != null">
            <![CDATA[
			    ) u ) where r <= #{endRow} and r >= #{startRow}
			]]>
        </if>
    </select>

    <select id="getBarcoderCount"  parameterType="java.util.List" resultType="java.lang.Long">
        select
        count(1)
        from SFC.cpm_boxup_bills     CBB,
        SFC.CPM_BOXUP_BILL_ITEMS     CBBI,
        SFC.PKG_SUBMAC_SCAN_INFO     SCAN_INFO
        where
        CBBI.Bill_Id = CBB.bill_id
        and CBBI.Enabled_Flag = 'Y'
        and CBB.Enabled_Flag = 'Y'
        AND SCAN_INFO.ORGANIZATION_ID = CBB.ORGANIZATION_ID
        AND SCAN_INFO.BILL_ID = CBB.BILL_ID
        AND SCAN_INFO.ENABLED_FLAG = 'Y'
        AND SCAN_INFO.CONFIG_DETAIL_ID = CBBI.CONFIG_DETAIL_ID
        <if test="boxBillNumber != null and boxBillNumber.size()>0">
            AND (CBB.BILL_NUMBER IN
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <trim suffixOverrides=" OR CBB.BILL_NUMBER IN()">	<!-- 表示删除最后一个条件 -->
                <foreach collection="boxBillNumber" item="item" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR CBB.BILL_NUMBER IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{item}
                </foreach>
            </trim>
            )
        </if>
    </select>

</mapper>