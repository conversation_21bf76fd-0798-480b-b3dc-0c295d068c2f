<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.TechnicalChangeBarcodeRepository">
  <resultMap id="BaseResultDTOMap" type="com.zte.interfaces.dto.TechnicalChangeBarcodeDTO">
    <id column="RECORD_ID" jdbcType="DECIMAL" property="recordId" />
    <result column="BILL_NO" jdbcType="VARCHAR" property="billNo" />
    <result column="BILL_TYPE" jdbcType="DECIMAL" property="billType" />
    <result column="PRODPLAN_ID" jdbcType="VARCHAR" property="prodplanId" />
    <result column="ITEM_BARCODE" jdbcType="VARCHAR" property="itemBarcode" />
    <result column="ITEM_OUT_INV" jdbcType="VARCHAR" property="itemOutInv" />
    <result column="HOT" jdbcType="VARCHAR" property="hot" />
    <result column="ASSEMBLE" jdbcType="VARCHAR" property="assemble" />
    <result column="TEST" jdbcType="VARCHAR" property="test" />
    <result column="REPARI" jdbcType="VARCHAR" property="repari" />
    <result column="ORGANIZATION_ID" jdbcType="DECIMAL" property="organizationId" />
  </resultMap>

  <sql id="Base_Column_List">
    RECORD_ID, BILL_NO, BILL_TYPE, PRODPLAN_ID, ITEM_BARCODE, ITEM_OUT_INV, HOT, ASSEMBLE, TEST, REPARI, ORGANIZATION_ID
  </sql>

  <select id="selectTechnicalChangeList" parameterType="com.zte.interfaces.dto.TechnicalChangeBarcodeDTO" resultMap="BaseResultDTOMap">
    SELECT tcb.bill_type,tcb.item_barcode
    FROM sfc.technical_change_barcode tcb
    WHERE tcb.bill_type = 1
    <if test="inSns != null and inSns != ''">
    AND tcb.item_barcode in (${inSns})   
    </if>
    UNION
    SELECT tcb.bill_type,tcb.item_barcode
    FROM sfc.technical_change_barcode tcb
    WHERE tcb.bill_type = 0 
     <if test="craftAttr4 != null and craftAttr4 != ''">
    and  ${craftAttr4} = '已锁定'
    </if>
    <if test="inSns != null and inSns != ''">
    AND tcb.item_barcode in (${inSns})   
    </if>
  </select>

  <select id="selectBarAttr" statementType="CALLABLE" parameterType="java.util.Map">
    {call SFC.CHECK_ENVIRONATTRIBUTE.CHECK_ENVIRONATTRIBUTE_IMES(
            #{entityNo, jdbcType=VARCHAR, javaType=java.lang.String, mode=IN},
            #{barcode, jdbcType=VARCHAR, javaType=java.lang.String, mode=IN},
            #{organizationId, jdbcType=DECIMAL, javaType=java.math.BigDecimal, mode=IN},
            #{result, jdbcType=VARCHAR, javaType=java.lang.String, mode=OUT}
      )}
  </select>

  <select id="selectBarAttr1" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT T1.SPECIAL_NAME
    FROM SFC.BAS_BARCODE_INFO T1
    WHERE T1.ITEM_BARCODE = #{barcode, jdbcType=VARCHAR}
      AND T1.SPECIAL_NAME IS NOT NULL
  </select>

  <select id="selectSn16BarAttr" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT DECODE(NO_PB_FLAG, 'Y', '无铅', 'HSF')
    FROM SFC.BAS_BARCODE_SERVICE_APPLY_V
    WHERE #{barcode, jdbcType=VARCHAR} BETWEEN APPLY_BARCODE_FROM AND APPLY_BARCODE_TO
  </select>

  <select id="selectBarAttr2" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT T2.SPECIAL_NAME
    FROM SSYN.STEP_ITEM_BARCODE T2
    WHERE T2.ITEM_BARCODE = #{barcode, jdbcType=VARCHAR}
      AND T2.SPECIAL_NAME IS NOT NULL
      AND ROWNUM = 1
  </select>

  <select id="selectBarAttr3" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT T3.SPECIAL_NAME
    FROM SFC.WSM_AGGREGATE_HEADERS T3
    WHERE T3.AGGREGATE_CODE = #{barcode, jdbcType=VARCHAR}
      AND T3.SPECIAL_NAME IS NOT NULL
      AND ROWNUM = 1
  </select>

  <select id="selectBarAttr4" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT T4.SPECIAL_NAME
    FROM SFC.INV_CONTAINER_ITEM_INFO T4
    WHERE T4.CON_BARCODE = #{barcode, jdbcType=VARCHAR}
      AND T4.SPECIAL_NAME IS NOT NULL
      AND ROWNUM = 1
  </select>

  <select id="selectItemBarcode5" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT K.ITEM_BARCODE
    FROM KXSCMIII.V_PU_SERIES@STEP K
    WHERE K.SERIES_NO = #{barcode, jdbcType=VARCHAR}
  </select>

  <select id="selectBarAttr5" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT S.SPECIAL_NAME
    FROM SSYN.STEP_ITEM_BARCODE S
    WHERE S.ITEM_BARCODE = #{itemBarcode, jdbcType=VARCHAR}
      AND S.SPECIAL_NAME IS NOT NULL
      AND ROWNUM = 1
  </select>

  <select id="selectItemNo6" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT DISTINCT ITEM_NO
    FROM SFC.BAS_BARCODE_SERVICE_APPLY_V
    WHERE #{barcode, jdbcType=VARCHAR} BETWEEN APPLY_BARCODE_FROM AND APPLY_BARCODE_TO
  </select>

  <select id="selectBarAttr6" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT DECODE(IS_LEAD, 0, '有铅', 1, '无铅', 2, 'ROHS', 3, 'HSF', 4, 'HSF-S', '')
    FROM KXSTEPIII.BA_ITEM@STEPORA
    WHERE ITEM_NO = #{itemNo, jdbcType=VARCHAR}
      AND ROWNUM = 1
  </select>

  <select id="selectCode7" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT A.ITEM_NO
    FROM (SELECT ITEM_NO
          FROM SFC.STEP_SERIES_S S
          WHERE S.SERIES_CODE = #{barcode, jdbcType=VARCHAR}
          UNION ALL
          SELECT ITEM_NO
          FROM SFC.STEP_SERIES_S S
          WHERE S.SERIES_NO = #{barcode, jdbcType=VARCHAR}) A
  </select>

  <select id="selectBarAttr7" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT DECODE(IS_LEAD, 0, '有铅', 1, '无铅', 2, 'ROHS', 3, 'HSF', 4, 'HSF-S', '')
    FROM KXSTEPIII.BA_ITEM@STEPORA
    WHERE ITEM_NO = #{code, jdbcType=VARCHAR}
      AND ROWNUM = 1
  </select>

  <select id="selectBarAttr8" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT DECODE(IS_LEAD, 0, '有铅', 1, '无铅', 2, 'ROHS', 3, 'HSF', 4, 'HSF-S', '')
    FROM KXSTEPIII.BA_ITEM@STEPORA
    WHERE ITEM_NO = (SELECT A.ITEM_NO
                     FROM SFC.SP_ITEMBARCODE A, SFC.V_SFC_SUPPLIER B
                     WHERE A.SUPPLIER_NO = B.SUPPLIER_NO(+)
                       AND A.PRODUCT_NO = #{barcode, jdbcType=VARCHAR})
      AND ROWNUM = 1
  </select>

  <select id="checkEntityAndSnBarAttr" parameterType="com.zte.interfaces.dto.TechnicalChangeBarcodeDTO" resultType="java.lang.Long">
    SELECT COUNT(1)
    FROM MESSYS.SYS_LOOKUP_TYPES H
           JOIN MESSYS.SYS_LOOKUP_VALUES D ON D.LOOKUP_TYPE = H.LOOKUP_TYPE
    WHERE H.LOOKUP_TYPE = '3030111'
      AND D.ENABLED_FLAG = 'Y'
      AND D.LOOKUP_MEANING = #{entityBarAttr, jdbcType=VARCHAR}
      AND D.DESCRIPTION = #{snBarAttr, jdbcType=VARCHAR}
    ORDER BY D.SORT_SEQ
  </select>


  <select id="syncInfo" statementType="CALLABLE" parameterType="com.zte.interfaces.dto.TechnicalSummaryInfoDTO">
    {call SFC.TECHNICAL_CHANGE_NEWBARCODE.TECHNICAL_NEW_BARCODE(
    #{dto.organizationId,mode=IN,jdbcType=NUMERIC,javaType=java.lang.Integer},
    #{dto.chgReqNo,mode=IN,jdbcType=VARCHAR,javaType=java.lang.String},
    #{dto.prodplanId,mode=IN,jdbcType=VARCHAR, javaType=java.lang.String},
    #{dto.taskQty,mode=IN,jdbcType=NUMERIC,javaType=java.lang.Integer},
    #{dto.isSuccess,mode=OUT,jdbcType=DECIMAL},
    #{dto.message,mode=OUT,jdbcType=VARCHAR}
    )}
  </select>

  <delete id="deleteTechnicalInfo" parameterType="com.zte.interfaces.dto.TechnicalSummaryInfoDTO">
    delete from SFC.TECHNICAL_CHANGE_BARCODE
    where PRODPLAN_ID = #{dto.prodplanId,jdbcType=VARCHAR}
    and  BILL_NO = #{dto.chgReqNo,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteTechnicalInfoWithSpm" parameterType="com.zte.interfaces.dto.TechnicalSummaryInfoDTO">
    delete from SFC.TECHNICAL_CHANGE_BARCODE
    where BILL_NO in
    (
    select TO_CHAR(record_id) from SFC.ACT_TECHNICAL_CHANGE_DETAIL
    where PRODPLAN_ID = #{dto.prodplanId,jdbcType=VARCHAR}
    and chg_reg_no = #{dto.chgReqNo,jdbcType=VARCHAR}
    )
  </delete>

  <delete id="deleteTechnicalInfoBySn" >
    delete from SFC.TECHNICAL_CHANGE_BARCODE
    where BILL_NO in
    <foreach collection="chgReqNoList" item="chgReqNo" index="index" open="(" separator="," close=")">
      #{chgReqNo}
    </foreach>
    and ITEM_BARCODE in
    <foreach collection="snList" item="sn" index="index" open="(" separator="," close=")">
      #{sn}
    </foreach>
    and (bill_no || item_barcode) in
    <foreach collection="mixList" item="mix" index="index" open="(" separator="," close=")">
      #{mix}
    </foreach>
  </delete>

  <!-- 因MES库TECHNICAL_CHANGE_BARCODE表bill_no字段存储的旧数据实际为SPM对应表的record_id,增加补丁-->
  <delete id="deleteTechnicalInfoBySnWithSpm" >
    delete from SFC.TECHNICAL_CHANGE_BARCODE
    where BILL_NO in
    (select TO_CHAR(record_id) from SFC.ACT_TECHNICAL_CHANGE_DETAIL where
    chg_reg_no in
    <foreach collection="chgReqNoList" item="chgReqNo" index="index" open="(" separator="," close=")">
      #{chgReqNo}
    </foreach>
    and ITEM_BARCODE in
    <foreach collection="snList" item="sn" index="index" open="(" separator="," close=")">
      #{sn}
    </foreach>
    and (CHG_REG_NO || item_barcode) in
    <foreach collection="mixList" item="mix" index="index" open="(" separator="," close=")">
      #{mix}
    </foreach>
    )
  </delete>
</mapper>
