<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.BoardOnlineStockDetailRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.BoardOnlineStockDetail">
        <result column="prodplanId" jdbcType="DECIMAL" property="prodplanId"/>
        <result column="boardSn" jdbcType="DECIMAL" property="boardSn"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="scanDate" jdbcType="TIMESTAMP" property="scanDate"/>
    </resultMap>

    <resultMap id="SimpleResultMap" type="com.zte.interfaces.dto.BoardInstructionCycleDataCreateDTO">
        <result column="PRODPLAN_ID" jdbcType="DECIMAL" property="prodplanId"/>
        <result column="FIRST_DATE" jdbcType="TIMESTAMP" property="firstDate"/>
    </resultMap>

    <resultMap id="BaseResultMapDTO" type="com.zte.domain.model.datawb.BoardOnlineStockDetail">
        <result column="PRODPLAN_ID" jdbcType="DECIMAL" property="prodplanId"/>
        <result column="BOARD_SN" jdbcType="DECIMAL" property="boardSn"/>
        <result column="SCAN_DATE" jdbcType="TIMESTAMP" property="scanDate"/>
        <result column="IMU_ID" jdbcType="DECIMAL" property="imuId"/>
        <result column="CARD_NO" jdbcType="VARCHAR" property="cardNo"/>
        <result column="IMU_IDOLD" jdbcType="DECIMAL" property="imuIdold"/>
    </resultMap>

    <resultMap type="com.zte.interfaces.dto.BaImuDTO" id="baImuMap">
        <result property="imuId" column="IMU_ID" jdbcType="INTEGER"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="workshopId" column="WORKSHOP_ID" jdbcType="INTEGER"/>
        <result property="bimuId" column="BIMU_ID" jdbcType="INTEGER"/>
        <result property="colName" column="COL_NAME" jdbcType="VARCHAR"/>
        <result property="lineName" column="LINE_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getBaImuPageList" parameterType="com.zte.springbootframe.common.model.Page" resultMap="baImuMap">
        SELECT
            IMU_ID,
            NAME,
            WORKSHOP_ID,
            BIMU_ID,
            COL_NAME,
            LINE_NAME
        FROM kxbariii.BA_IMU order by imu_id
    </select>


    <!-- 查询所有信息 -->
    <select id="selectBoardOnlineStockDetailByProdPlanIds" parameterType="java.util.ArrayList"
            resultMap="BaseResultMap">
        SELECT a.prodPlan_ID as prodplanId,
        a.Board_SN as boardSn,
        b.NAME as name,
        scan_date as scanDate
        FROM kxbariii.Board_Online a
        JOIN kxbariii.BA_IMU b
        ON a.IMU_ID = B.IMU_ID
        WHERE a.prodPlan_ID in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getFirstDateOfBimuAndProdplanId" resultMap="SimpleResultMap">
        select min(min_scan_date) as FIRST_DATE,PRODPLAN_ID
        from (select min(t.scan_date) as min_scan_date,prodplan_id
        from kxbarhisiii.board_onlineold_back t
        where t.prodplan_id in (${inProdplanId})
        and t.imu_id in
        (select distinct i.imu_id
        from ba_imu i
        where i.bimu_id in (${inBimuId}))
        group by prodplan_id
        union all
        select min(t.scan_date) as min_scan_date,prodplan_id
        from kxbariii.board_onlineold t
        where t.prodplan_id in (${inProdplanId})
        and t.imu_id in
        (select distinct i.imu_id
        from ba_imu i
        where i.bimu_id in (${inBimuId}))
        group by prodplan_id)
        group by prodplan_id
    </select>

    <delete id="deleteBatch" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";" open="begin" close=";end;">
            delete from kxbariii.Board_Online
            where PRODPLAN_ID = #{item.prodplanId,jdbcType=DECIMAL}
            AND BOARD_SN = #{item.boardSn,jdbcType = DECIMAL}
        </foreach>
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" index="index" item="item"
                 separator="">
            INTO kxbariii.Board_Online
            (PRODPLAN_ID,BOARD_SN,SCAN_DATE,IMU_ID,CARD_NO,IMU_IDOLD) VALUES
            (
            #{item.prodplanId,jdbcType=DECIMAL},#{item.boardSn,jdbcType=DECIMAL},
            sysdate,#{item.imuId,jdbcType=DECIMAL}
            ,#{item.cardNo,jdbcType=VARCHAR},#{item.imuIdold,jdbcType=DECIMAL}
            )
        </foreach>
        SELECT * FROM DUAL

    </insert>

</mapper>
