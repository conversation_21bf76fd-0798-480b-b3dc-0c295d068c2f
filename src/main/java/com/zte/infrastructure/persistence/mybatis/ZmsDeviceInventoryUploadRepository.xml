<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ZmsDeviceInventoryUploadRepository">

    <!-- Started by AICoder, pid:vdb9319815l8efb148500aa6b0d4f02b18159fb0 -->
    <select id="queryMtlMaterialTransactionPage"
            resultType="com.zte.interfaces.dto.bytedance.MtlMaterialTransactionsDTO"
            parameterType="com.zte.interfaces.dto.bytedance.MtlMaterialTransactionsDTO">
        SELECT T.TRANSACTION_REFERENCE, T.TRANSACTION_ID,
        T.TRANSACTION_QUANTITY, T.TRANSACTION_DATE,
        T.SUBINVENTORY_CODE, T.TRANSFER_SUBINVENTORY,
        b.SEGMENT1 ,b.DESCRIPTION
        FROM apps.MTL_MATERIAL_TRANSACTIONS@erp T
        join apps.MTL_SYSTEM_ITEMS@erp B ON t.INVENTORY_ITEM_ID = b.INVENTORY_ITEM_ID and b.ORGANIZATION_ID = 635
        WHERE 1 = 1
        <if test="params.subinventoryCodeList != null and params.subinventoryCodeList.size() > 0">
            AND T.SUBINVENTORY_CODE IN
            <foreach collection="params.subinventoryCodeList" index="index" open="(" separator=","
                     close=")">
                #{params.subinventoryCodeList[${index}]}
            </foreach>
        </if>
        <if test="params.lastUpdatedDateStart != null and params.lastUpdatedDateEnd != null">
            AND T.LAST_UPDATE_DATE BETWEEN #{params.lastUpdatedDateStart, jdbcType=TIMESTAMP} AND #{params.lastUpdatedDateEnd, jdbcType=TIMESTAMP}
        </if>
        <if test="params.transactionIdList != null and params.transactionIdList.size() > 0">
            AND T.TRANSACTION_ID IN
            <foreach collection="params.transactionIdList" index="index" open="(" separator="," close=")">
                #{params.transactionIdList[${index}]}
            </foreach>
        </if>
        <if test="params.itemNoList != null and params.itemNoList.size()>0 ">
            AND B.SEGMENT1 IN
            <foreach collection="params.itemNoList" open="(" separator="," index="index" close=")">
                #{params.itemNoList[${index}]}
            </foreach>
        </if>
        ORDER BY T.TRANSACTION_ID, T.LAST_UPDATE_DATE
    </select>

    <!-- Ended by AICoder, pid:vdb9319815l8efb148500aa6b0d4f02b18159fb0 -->

    <!-- Started by AICoder, pid:a9c4buca92d4674146e00a2bb0a46f4fe998fc33 -->
    <select id="getDeviceInventoryList" parameterType="com.zte.interfaces.dto.StationInputDTO" resultType="com.zte.interfaces.dto.ZmsDeviceInventoryDTO">
        SELECT
        to_char(sysdate, 'YYYYMMDD') dataDate,
        'BU01' buCode,
        (SELECT
            CASE WHEN INSTR(CCMS.MFG_SITE_EQUIPMENT, '_') > 0
            THEN SUBSTR(CCMS.MFG_SITE_EQUIPMENT, 1, INSTR(CCMS.MFG_SITE_EQUIPMENT, '_') - 1)
            ELSE CCMS.MFG_SITE_EQUIPMENT
            END
        FROM SFC.CPM_CONTRACT_MFG_SITES CCMS
        WHERE CCMS.ENTITY_ID = S.ENTITY_ID AND ROWNUM = 1) lobCode,
        'ZTE1' odmPlantCode,
        CASE WHEN A.ENTITY_STATUS = 30 THEN 'N003' ELSE 'N002' END odmStorageLoc,
        CASE WHEN A.ENTITY_STATUS = 30 THEN '成品良品仓' ELSE '成品在途仓' END odmStorageLocDesc,
        A.ENTITY_NAME prodOrderNo,
        1 stockQuantity,
        '个' unit,
        CASE WHEN A.ENTITY_STATUS = 30 THEN '00' ELSE '13' END stockStatus,
        S.ENTITY_ID entityId,
        A.USER_ADDRESS userAddress
        FROM wmes.WERP_CON_ENTITY_TRACE S,
        WMES.CPM_CONTRACT_ENTITIES A
        WHERE S.ENTITY_ID = A.ENTITY_ID
        AND A.ENABLED_FLAG = 'Y'
        AND (A.ENTITY_STATUS = 30 OR
        (A.ENTITY_STATUS = 20 AND S.ASSEMBLE_ENDDATE IS NOT NULL))
        AND A.ENTITY_NAME LIKE '%${entityLike}%'
        <if test="userAddressList !=null and userAddressList.size()>0">
            and A.USER_ADDRESS in
            <foreach collection="userAddressList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <!--部件库存查询-->
    <!-- Started by AICoder, pid:9a1adv6021y71b114c30094c60989520a46626e8 -->
    <select id="getPartsInventoryList" resultType="com.zte.interfaces.dto.ZmsDeviceInventoryUploadDTO">
        select
            'ZTE1' as odmPlantCode,
            'BU02' as buCode,
            to_char(sysdate, 'YYYYMMDD') as dataDate,
            bi.item_no as itemNo,
            ci.customer_code as materialCode,
            ci.customer_item_name as materialDesc,
            'N001' as odmStorageLoc,
            st.balance_qty as stockQuantity,
            'EA' as unit,
            case
                when instr(ses.stock_name, '在途') > 0 then '20'
                when instr(ses.stock_name, '待处理') > 0 then '11'
                else '00'
            end as stockStatus
        from kxstepiii.st_summary@stepora st
        join kxstepiii.ba_item@stepora bi on bi.item_id = st.item_id
        join kxstepiii.st_stock@stepora ses on ses.stock_no = st.stock_no
        join kxstepiii.customer_items@stepora ci on ci.zte_code = bi.item_no
        where st.balance_qty > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        and ci.COOPERATION_MODE in ('B&amp;S','AVAP')
    </select>

    <!-- Ended by AICoder, pid:9a1adv6021y71b114c30094c60989520a46626e8 -->

    <!-- Started by AICoder, pid:a46a273759e44691494a0b1660b8fe309b02c5f4 -->
    <!-- 获取整机车间库存 -->
    <select id="getWholeInventoryList" resultType="com.zte.interfaces.dto.ZmsDeviceInventoryUploadDTO">
        SELECT
               A.QTY as stockQuantity,
               'ZTE1' as odmPlantCode,
               'N002' as odmStorageLoc,
               'BU02' as buCode,
               'EA' as unit,
                ci.customer_code as materialCode,
                ci.customer_item_name as materialDesc,
                to_char(sysdate, 'YYYYMMDD') dataDate,
                CASE WHEN A.SUBINVENTORY_CODE IN ('ZK42ZXZJ', '39-FX-BLNJ', '39-FXZX-WX', '39-FX-BLSX') THEN '11'
                ELSE '13' END AS stockStatus
        FROM (
            SELECT aa.ORGANIZATION_ID,
                   aa.SUBINVENTORY_CODE,
                   aa.LOCATOR_ID,
                   aa.INVENTORY_ITEM_ID,
                   SUM(aa.TRANSACTION_QUANTITY) AS QTY
            FROM APPS.MTL_ONHAND_QUANTITIES@erp aa
            WHERE aa.TRANSACTION_QUANTITY != 0
            GROUP BY aa.ORGANIZATION_ID,
                     aa.SUBINVENTORY_CODE,
                     aa.LOCATOR_ID,
                     aa.INVENTORY_ITEM_ID
        ) A
        JOIN apps.MTL_SYSTEM_ITEMS@erp B ON A.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
                                      AND A.ORGANIZATION_ID = B.ORGANIZATION_ID
        JOIN wmes.BAS_ORGANIZAITONS G ON A.ORGANIZATION_ID = G.ORGANIZATION_ID
        join kxstepiii.customer_items@stepora ci on ci.zte_code = B.SEGMENT1
        WHERE ci.customer_name = 'ByteDance'
        and ci.COOPERATION_MODE in ('B&amp;S','AVAP')
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        <choose>
            <when test="subinventoryCodeList != null and subinventoryCodeList.size()>0">
                AND A.SUBINVENTORY_CODE IN
                <foreach collection="subinventoryCodeList" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 2
            </otherwise>
        </choose>
    </select>

    <!-- Ended by AICoder, pid:a46a273759e44691494a0b1660b8fe309b02c5f4 -->

    <!-- Ended by AICoder, pid:a9c4buca92d4674146e00a2bb0a46f4fe998fc33 -->
    <select id="getConfigDesc" resultType="com.zte.interfaces.dto.ZmsCbomInfoDTO">
        SELECT DISTINCT
            CASE WHEN INSTR(B.DESCRIPTION, '|') > 0
            THEN(
                CASE WHEN INSTR(SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1), #{intercept}) > 0
                THEN SUBSTR(SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1), 1, INSTR(SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1), #{intercept}, -1) - 1)
                ELSE SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1)
                END)
            ELSE B.DESCRIPTION
            END cbomNameCn,
            CASE WHEN INSTR(B.DESCRIPTION, '|') > 0
            THEN SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1)
            ELSE B.DESCRIPTION
            END quotationMaterialNameCn,
            CASE WHEN INSTR(B.DESCRIPTION, '|') > 0
            THEN(
                CASE WHEN INSTR(SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1), #{intercept}) > 0
                THEN SUBSTR(SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1), INSTR(SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, '|') - 1), #{intercept}, -1) + 1, INSTR(B.DESCRIPTION, '|') - 1)
                ELSE ''
            END)
            ELSE ''
            END cbomVerison,
            CCE.ENTITY_NAME entityName
        FROM APP_MES.MTL_SYSTEM_ITEMS  B,
        APP_MES.CPM_CONFIG_DETAILS     CCD,
        APP_MES.CPM_CONTRACT_MFG_SITES MFG,
        app_mes.cpm_contract_entities  cce
        WHERE CCD.ORGANIZATION_ID = B.ORGANIZATION_ID
        AND CCD.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
        AND CCD.ENABLED_FLAG = 'Y'
        AND MFG.ENABLED_FLAG = 'Y'
        AND CCD.MFG_SITE_ID = MFG.MFG_SITE_ID
        and cce.entity_id = mfg.entity_id
        and cce.entity_name in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
        and B.DESCRIPTION like '%${descLike}%'
    </select>
    <!-- Started by AICoder, pid:x84dcx3bddh128c14e390b4c40401a317ee98706 -->
    <select id="getDeviceInventoryMoveList" parameterType="com.zte.interfaces.dto.StationInputDTO" resultType="com.zte.interfaces.dto.ZmsDeviceInventoryMoveUploadDTO">
        select to_char(sysdate, 'YYYYMMDD') dataDate,
        'ZTE1' odmPlantCode,
        'ZJ-NANJING' odmLocCode,
        'FG01' bdLocCode,
        t.entity_name materialDocNo,
        '000001' materialDocLine,
        t.entity_name sourceOrder,
        CASE WHEN t.ENTITY_STATUS = 30 THEN 'BD56' ELSE 'BD59' END movementType,
        CASE WHEN t.ENTITY_STATUS = 30 THEN 'Receipt' ELSE 'Issue' END odmMovementType,
        1 receivingQuantity,
        'GE' unit,
        t.last_update_date operateTime,
        'MES' dataSource
        from wmes.cpm_contract_entities_upload t
        where t.upload_status = 0
        and t.entity_status in (30, 40)
        and t.enabled_flag='Y'
        and t.entity_name LIKE '%${entityLike}%'
        <if test="userAddressList !=null and userAddressList.size()>0">
            and t.user_address in
            <foreach collection="userAddressList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="entityNameList !=null and entityNameList.size()>0">
            and t.entity_name in
            <foreach collection="entityNameList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <!-- Ended by AICoder, pid:x84dcx3bddh128c14e390b4c40401a317ee98706 -->
    <update id="updateCpmContractEntitiesUpload" parameterType="java.util.List">
        update wmes.cpm_contract_entities_upload
        set upload_status = 1
        where 1=1
        <if test="list !=null and list.size()>0">
            and (entity_name,entity_status) in
            <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
                (#{item.sourceOrder, jdbcType=VARCHAR},
                <if test="item.odmMovementType == 'Receipt' ">
                    30
                </if>
                <if test="item.odmMovementType== 'Issue' ">
                    40
                </if>
                )
            </foreach>
        </if>
        and enabled_flag='Y'
        and upload_status = 0
    </update>

</mapper>