<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.WerpMrpInventoryQuantityRepository">
  <resultMap id="completeResultMap" type="com.zte.domain.model.datawb.WerpMrpInventoryQuantity">
    <result column="SECONDARYINVENTORYNAME" jdbcType="VARCHAR" property="secondaryInventoryName" />
    <result column="ITEMCODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="ITEMNAME" jdbcType="VARCHAR" property="itemName" />
    <result column="CABLELENGTH" jdbcType="DECIMAL" property="cableLength" />
    <result column="CABLEQTY" jdbcType="DECIMAL" property="cableQty" />
    <result column="TOTALLENGTH" jdbcType="DECIMAL" property="totalLength" />
  </resultMap>

  <select id="selectWerpMrpInventoryQuantitybyItemcode" parameterType="com.zte.domain.model.datawb.WerpMrpPlanResult" resultMap="completeResultMap">
SELECT  MSI.SECONDARY_INVENTORY_NAME SECONDARYINVENTORYNAME, VEC.SKU ITEMCODE, VEC.DESCR ITEMNAME,
        NVL(VEC.CABLELENGTH, 0) CABLELENGTH, NVL(VEC.CABLEQTY, 0) CABLEQTY, NVL(VEC.TOTALLENGTH, 0) TOTALLENGTH
FROM     KXSTEPIII.V_EDI_CABLESTOCK@STEP VEC, (SELECT DISTINCT WHSEID,SECONDARY_INVENTORY_NAME FROM WMES.MTL_SECONDARY_INVENTORIES) MSI
WHERE   VEC.WHSEID = MSI.WHSEID AND VEC.SKU = #{itemCode,jdbcType=VARCHAR} AND VEC.cableqty > 0 AND MSI.SECONDARY_INVENTORY_NAME = #{secondaryInventoryName,jdbcType=VARCHAR}
  </select>
</mapper>
