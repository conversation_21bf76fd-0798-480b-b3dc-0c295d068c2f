<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.domain.model.ArchiveBomLockingBillDataRepository">
    <select id="getPageByDateRange" resultType="com.zte.interfaces.dto.ArchiveBomLockingBillDTO"
            parameterType="com.zte.springbootframe.common.model.Page">
        SELECT
            DISTINCT BLB.BILL_ID,
            BLB.BILL_NO
        FROM
            SFC.BOM_LOCKING_BILL blb,
            SFC.BOM_LOCKING_PLAN blp
        WHERE
            BLP.ENABLED_FLAG = 'Y'
            AND BLB.ENABLED_FLAG = 'Y'
            AND BLB.BILL_ID = BLP.BILL_ID
            AND BLP.PLAN_STATUS = '已解锁'
            AND BLB.LAST_UPDATE_DATE &gt;= #{params.startDate}
            AND BLB.LAST_UPDATE_DATE &lt; #{params.endDate}
    </select>
    <select id="getByBillNo" resultType="com.zte.interfaces.dto.ArchiveBomLockingBillDTO"
            parameterType="java.lang.String">
        SELECT
            s.*
        FROM
            (
            SELECT
                BLP.UNLOCKING_DATE,
                BLB.BILL_ID,
                BLB.BILL_NO,
                BLB.CREATED_BY,
                ROW_NUMBER() OVER (PARTITION BY BLB.BILL_ID ORDER BY BLP.UNLOCKING_DATE DESC) AS group_idx
            FROM
                SFC.BOM_LOCKING_BILL blb,
                SFC.BOM_LOCKING_PLAN blp
            WHERE
                BLP.ENABLED_FLAG = 'Y'
                AND BLB.ENABLED_FLAG = 'Y'
                AND BLP.BILL_ID = BLB.BILL_ID
                AND BLP.PLAN_STATUS = '已解锁'
                AND BLB.BILL_NO = #{billNo}) s
        WHERE
            s.group_idx = 1
    </select>
    <resultMap id="ArchiveBomLockingBarcodeMap" type="com.zte.interfaces.dto.ArchiveBomLockingBarcodeDTO">
        <result column="ITEM_BARCODE" jdbcType="VARCHAR" property="itemBarcode" />
        <result column="ITEM_OUT_INV" jdbcType="VARCHAR" property="itemOutInv" />
        <result column="HOT" jdbcType="VARCHAR" property="hot" />
        <result column="ASSEMBLE" jdbcType="VARCHAR" property="assemble" />
        <result column="TEST" jdbcType="VARCHAR" property="test" />
        <result column="PACK" jdbcType="VARCHAR" property="pack" />
        <result column="REPARI" jdbcType="VARCHAR" property="repari" />
        <result column="UNLOCKING_NAME" jdbcType="VARCHAR" property="unlockingName" />
        <result column="UNLOCKING_DATE" jdbcType="VARCHAR" property="unlockingDate" />
    </resultMap>
    <select id="getBarcodeByBillId" resultMap="ArchiveBomLockingBarcodeMap"
            parameterType="java.lang.String">
        SELECT
            BLB.ITEM_BARCODE,
            BLB.ITEM_OUT_INV,
            BLB.ASSEMBLE,
            BLB.HOT,
            BLB.TEST,
            BLB."PACKAGE" PACK,
            BLB.REPARI,
            BLB.UNLOCKING_NAME,
            BLB.UNLOCKING_DATE
        FROM
            SFC.BOM_LOCKING_BARCODE blb
        WHERE
            BLB.ENABLED_FLAG = 'Y'
            AND  BLB.BILL_ID = #{billId}
        ORDER BY BLB.ITEM_BARCODE
    </select>
    <select id="getPlanByBillId" resultType="com.zte.interfaces.dto.ArchiveBomLockingPlanDTO"
            parameterType="java.lang.String">
        SELECT
            BLB.BILL_NO,
            BLP.PRODPLAN_ID,
            BLP.PLAN_STATUS,
            BLP.PLAN_NUM,
            BLP.ITEM_CODE,
            BLP.ITEM_NAME,
            BLB.FAULT_MEMO,
            BLB.SEND_PERSON,
            BLP.CREATED_NAME,
            BLP.CREATION_DATE,
            BLP.LOCKING_NAME,
            BLP.LOCKING_DATE,
            BLP.UNLOCKING_NAME,
            BLP.UNLOCKING_DATE
        FROM
            SFC.BOM_LOCKING_BILL blb,
            SFC.BOM_LOCKING_PLAN blp
        WHERE
            BLP.ENABLED_FLAG = 'Y'
            AND BLB.ENABLED_FLAG = 'Y'
            AND BLB.BILL_ID = BLP.BILL_ID
            AND BLB.BILL_ID = #{billId}
    </select>
</mapper>