<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.PallectNoEveryBoxNoServiceRepository">
    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.PalletScanInCollectionDTO">
        <result column="BILL_NUMBER" jdbcType="VARCHAR" property="boxNo"/>
        <result column="Pallet_No" jdbcType="VARCHAR" property="pallet"/>
    </resultMap>

    <select id="pallectGetCollection" parameterType="com.zte.interfaces.dto.PalletScanInCollectionDTO"
            resultMap="BaseResultMap">
        SELECT T.Bill_Number,T.Pallet_No FROM APP_MES.PALLET_SCAN_PACKING T
        where T.Enabled_Flag='Y'
        <choose>
            <when test="pallets  !=null">
                and T.Pallet_No in
                <foreach collection="pallets" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1=2
            </otherwise>
        </choose>
    </select>

    <select id="getEntityMax" parameterType="java.util.List" resultType="com.zte.interfaces.dto.TranspotDTO">
        SELECT A.ENTITY_NAME as entityName,
        APP_MES.CPM_INLIB_QUERY_PKG.Get_InLib_POSTED_date(A.entity_id,A.ORGANIZATION_ID) as postDate
        FROM
        APP_MES.cpm_contract_entities A
        WHERE
        A.enabled_flag = 'Y'
        AND A.ENTITY_NAME in
        <foreach collection="entityList" item="item" index="index" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
