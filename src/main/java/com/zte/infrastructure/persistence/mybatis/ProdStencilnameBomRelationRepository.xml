<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ProdStencilnameBomRelationRepository">
    <resultMap id="BaseResultMap" type="com.zte.domain.model.datawb.ProdStencilnameBomRelation">
        <result column="RELATION_ID" jdbcType="DECIMAL" property="relationId" />
        <result column="STENCIL_NAME" jdbcType="VARCHAR" property="stencilName" />
        <result column="BOM_ID" jdbcType="DECIMAL" property="bomId" />
        <result column="CREATION_BY" jdbcType="VARCHAR" property="creationBy" />
        <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
        <result column="LAST_UPDATE_BY" jdbcType="VARCHAR" property="lastUpdateBy" />
        <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
        <result column="BOM_NO" jdbcType="VARCHAR" property="bomNo" />
        <result column="BOM_NAME" jdbcType="VARCHAR" property="bomName" />
        <result column="VERSION" jdbcType="VARCHAR" property="version" />
    </resultMap>

    <sql id="Base_Column_List">
    RELATION_ID, STENCIL_NAME, BOM_ID,CREATION_BY,CREATION_DATE,LAST_UPDATE_BY,LAST_UPDATE_DATE
  </sql>

  <select id="getStencilNameBomRelationList"  parameterType="java.lang.String" resultMap="BaseResultMap">
   select stencil_name,
          bom_name,
          version,
          CREATION_BY,
          LAST_UPDATE_BY
     FROM (

           select a.relation_id ,
                   a.stencil_name ,
                   b.bom_name ,
                   b.version ,
                   a.CREATION_BY ,
                   a.CREATION_DATE ,
                   a.LAST_UPDATE_BY ,
                   a.LAST_UPDATE_DATE,
                   ROW_NUMBER() OVER(PARTITION BY a.stencil_name, b.bom_name, b.version ORDER BY a.CREATION_DATE DESC) as FLAG
             FROM prod_stencilname_bom_relation a, board b
            where a.BOM_ID = b.BOM_ID
              and a.stencil_name in (${stencilNameS})) TEMP
    WHERE TEMP.FLAG = 1
  </select>
  <select id="pageSelectStencilOfSPMData" parameterType="com.zte.interfaces.dto.SynchronizeStencilOfSpmDataDTO" resultType="com.zte.interfaces.dto.SynchronizeStencilOfSpmDataDTO">
      SELECT * FROM(
      SELECT TT.*, ROWNUM AS RN FROM(
      SELECT  t1.STENCIL_NAME,t1.BOM_ID, t1.CREATION_BY ,t1.CREATION_DATE ,t1.LAST_UPDATE_BY ,t1.LAST_UPDATE_DATE
      FROM PROD_STENCILNAME_BOM_RELATION t1
      WHERE 1 = 1
      <if test="startTime != null and endTime != null">
          AND t1.LAST_UPDATE_DATE &lt;=  #{endTime, jdbcType = TIMESTAMP}
          AND t1.LAST_UPDATE_DATE >=  #{startTime, jdbcType = TIMESTAMP}
      </if>
      <if test="bomId != null">
        And t1.BOM_ID not in
          <!-- 防止因时间相同导致数据重复查询-->
          (select BOM_ID from PROD_STENCILNAME_BOM_RELATION where LAST_UPDATE_DATE = #{startTime, jdbcType = TIMESTAMP} and BOM_ID &lt;= #{bomId, jdbcType = DECIMAL})
      </if>
      <if test="startTime == null or endTime == null">
          and 1=2
      </if>
      <if test="stencilNameList != null and stencilNameList.size() != 0">
          AND t1.STENCIL_NAME in
          <foreach collection="stencilNameList" separator="," index="index" open="(" item="stencilName" close=")">
              #{stencilName, jdbcType = VARCHAR}
          </foreach>
      </if>
      ORDER BY t1.LAST_UPDATE_DATE, t1.BOM_ID )TT
      WHERE ROWNUM &lt;= #{page, jdbcType = INTEGER} * #{row, jdbcType = INTEGER}
      )
      WHERE RN > (#{page, jdbcType = INTEGER} - 1) * #{row, jdbcType = INTEGER}
  </select>
  <select id="selectEffectiveStencilOfSPMData" resultType="com.zte.interfaces.dto.SynchronizeStencilOfSpmDataDTO">
      SELECT DISTINCT t.STENCIL_NAME FROM kxbariii.PROD_STENCIL_BASIC_INFO t
      WHERE t.STENCIL_NAME  IN
      <foreach collection="stencilNameList" index="index" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
  </select>
</mapper>
