<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.ZmsCompleteMachineLineRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.CompleteMachineDataLogLineEntityDTO" id="completeMachineDataLogLineMap">
        <result property="id" column="ID" />
        <result property="serverSn" column="SERVER_SN" />
        <result property="componentId" column="COMPONENT_ID" />
        <result property="componentType" column="COMPONENT_TYPE" />
        <result property="manufacturer" column="MANUFACTURER" />
        <result property="specifications" column="SPECIFICATIONS" />
        <result property="version" column="VERSION" />
        <result property="modelNumber" column="MODEL_NUMBER" />
        <result property="originComponentSn" column="ORIGIN_COMPONENT_SN" />
        <result property="createDate" column="CREATE_DATE" />
        <result property="createBy" column="CREATE_BY" />
    </resultMap>

    <sql id="Base_Column_List">
      ID,
      SERVER_SN,
      COMPONENT_ID,
      COMPONENT_TYPE,
      MANUFACTURER,
      SPECIFICATIONS,
      VERSION,
      MODEL_NUMBER,
      ORIGIN_COMPONENT_SN,
      CREATE_DATE,
      CREATE_BY
    </sql>



    <insert id="batchInsert" parameterType="java.util.List">

        insert into SFC.ZMS_COMPLETE_MACHINE_LINE (
        ID,
        SERVER_SN,
        COMPONENT_ID,
        COMPONENT_TYPE,
        MANUFACTURER,
        SPECIFICATIONS,
        VERSION,
        MODEL_NUMBER,
        ORIGIN_COMPONENT_SN,
        CREATE_DATE,
        CREATE_BY,
        MPN,
        MAC_VERSION,
        BIOS_VERSION,
        BMC_VERSION,
        CPLD_VERSION,
        DATE_CODE,
        SLOT,
        ITEM_CODE
        )
        (
        <foreach collection ="list" item="item" index= "" separator="union all">
            select
            #{item.id, jdbcType=VARCHAR},
            #{item.serverSn, jdbcType=VARCHAR},
            #{item.componentId, jdbcType=DECIMAL},
            #{item.componentType, jdbcType=VARCHAR},
            #{item.manufacturer, jdbcType=VARCHAR},
            #{item.specifications, jdbcType=VARCHAR},
            #{item.version, jdbcType=VARCHAR},
            #{item.modelNumber, jdbcType=VARCHAR},
            #{item.originComponentSn, jdbcType=VARCHAR},
            sysdate,
            #{item.createBy, jdbcType=DECIMAL},
            #{item.mpn, jdbcType=VARCHAR},
            #{item.macVersion, jdbcType=VARCHAR},
            #{item.biosVersion, jdbcType=VARCHAR},
            #{item.bmcVersion, jdbcType=VARCHAR},
            #{item.cpldVersion, jdbcType=VARCHAR},
            #{item.dateCode, jdbcType=VARCHAR},
            #{item.slot, jdbcType=VARCHAR},
            #{item.itemCode, jdbcType=VARCHAR}
            from dual
        </foreach>
        )
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from SFC.ZMS_COMPLETE_MACHINE_LINE t where t.SERVER_SN in
        <foreach collection="list" item="item" separator=','
                 open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>