<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.ZmsCompleteMachineHeadRepository">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zte.interfaces.dto.CompleteMachineDataLogEntityDTO" id="completeMachineDataLogMap">
        <result property="id" column="ID" />
        <result property="serverSn" column="SERVER_SN" />
        <result property="mainBoardSn" column="MAIN_BOARD_SN" />
        <result property="serverType" column="SERVER_TYPE" />
        <result property="combo" column="COMBO" />
        <result property="orderId" column="ORDER_ID" />
        <result property="orderTime" column="ORDER_TIME" />
        <result property="idc" column="IDC" />
        <result property="manufacturerTime" column="MANUFACTURER_TIME" />
        <result property="factoryOrderId" column="FACTORY_ORDER_ID" />
        <result property="factoryOrderTime" column="FACTORY_ORDER_TIME" />
        <result property="factoryLocation" column="FACTORY_LOCATION" />
        <result property="biosVersion" column="BIOS_VERSION" />
        <result property="bmcVersion" column="BMC_VERSION" />
        <result property="bqcVersion" column="BQC_VERSION" />
        <result property="packageTime" column="PACKAGE_TIME" />
        <result property="acceptanceTime" column="ACCEPTANCE_TIME" />
        <result property="orderState" column="ORDER_STATE" />
        <result property="createDate" column="CREATE_DATE" />
        <result property="createBy" column="CREATE_BY" />
    </resultMap>

    <sql id="Base_Column_List">
      ID,
      SERVER_SN,
      MAIN_BOARD_SN,
      SERVER_TYPE,
      COMBO,
      ORDER_ID,
      ORDER_TIME,
      IDC,
      MANUFACTURER_TIME,
      FACTORY_ORDER_ID,
      FACTORY_ORDER_TIME,
      FACTORY_LOCATION,
      BIOS_VERSION,
      BMC_VERSION,
      BQC_VERSION,
      PACKAGE_TIME,
      ACCEPTANCE_TIME,
      ORDER_STATE,
      CREATE_DATE,
      CREATE_BY
    </sql>



    <insert id="batchInsert" parameterType="java.util.List">
            insert into SFC.ZMS_COMPLETE_MACHINE_HEAD (
            ID,
            SERVER_SN,
            MAIN_BOARD_SN,
            SERVER_TYPE,
            COMBO,
            ORDER_ID,
            ORDER_TIME,
            IDC,
            MANUFACTURER_TIME,
            FACTORY_ORDER_ID,
            FACTORY_ORDER_TIME,
            FACTORY_LOCATION,
            BIOS_VERSION,
            BMC_VERSION,
            BQC_VERSION,
            PACKAGE_TIME,
            ACCEPTANCE_TIME,
            ORDER_STATE,
            CREATE_DATE,
            CREATE_BY,
            CUSTOMER_NAME,
            GRAY_PLAN_NO,
            ASSET_NO,
            VASSET_NO
            )
            (
            <foreach collection ="list" item="item" index= "" separator="union all">
                select
                #{item.id, jdbcType=VARCHAR},
                #{item.serverSn, jdbcType=VARCHAR},
                #{item.mainBoardSn, jdbcType=VARCHAR},
                #{item.serverType, jdbcType=VARCHAR},
                #{item.combo, jdbcType=VARCHAR},
                #{item.orderId, jdbcType=VARCHAR},
                #{item.orderTime, jdbcType=DATE},
                #{item.idc, jdbcType=VARCHAR},
                #{item.manufacturerTime, jdbcType=DATE},
                #{item.factoryOrderId, jdbcType=VARCHAR},
                #{item.factoryOrderTime, jdbcType=DATE},
                #{item.factoryLocation, jdbcType=VARCHAR},
                #{item.biosVersion, jdbcType=VARCHAR},
                #{item.bmcVersion, jdbcType=VARCHAR},
                #{item.bqcVersion, jdbcType=VARCHAR},
                #{item.packageTime, jdbcType=DATE},
                #{item.acceptanceTime, jdbcType=DATE},
                #{item.orderState, jdbcType=VARCHAR},
                sysdate,
                #{item.createBy, jdbcType=VARCHAR},
                #{item.customerName, jdbcType=VARCHAR},
                #{item.grayPlanNo, jdbcType=VARCHAR},
                #{item.assetNo, jdbcType=VARCHAR},
                #{item.vAssetNo, jdbcType=VARCHAR}
                from dual
            </foreach>
            )
    </insert>

    <delete id="batchDelete" parameterType="java.util.List">
        delete from SFC.ZMS_COMPLETE_MACHINE_HEAD t where t.SERVER_SN in
        <foreach collection="list" item="item" separator=','
                 open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getMacByResourceNumber" parameterType="java.util.List" resultType="com.zte.interfaces.dto.SpSpecialityNalDTO">
        select MAC1_ADDR mac,outsource_factory,NET_ACCESS_SIGN_NUM,CREATED_BY binder from SFC.BARCODE_NET_SIGN  where NET_ACCESS_SIGN_NUM in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="batchInsertResourceInfo" parameterType="java.util.List" >
        MERGE INTO SFC.SP_SPECIALITY_NAL T1
        USING (
            <foreach collection ="list" item="item" index= "" separator="union all">
                select
                #{item.paramHeaderId, jdbcType=DECIMAL} paramHeaderId,
                #{item.netPermit, jdbcType=VARCHAR} netPermit,
                #{item.netAccessSignNum, jdbcType=VARCHAR} netAccessSignNum,
                #{item.scrambleCode, jdbcType=VARCHAR} scrambleCode,
                #{item.eqpModel, jdbcType=VARCHAR} eqpModel,
                #{item.modelNumber, jdbcType=VARCHAR} ||#{item.scrambleCode, jdbcType=VARCHAR}  nasn,
                sysdate createdDate,
                #{item.createdBy, jdbcType=VARCHAR} createdBy
                from dual
            </foreach>
        ) T2
        ON (
        T1.NET_ACCESS_SIGN_NUM = T2.netAccessSignNum and T1.NET_PERMIT = T2.netPermit
        )
        WHEN MATCHED THEN
        UPDATE SET  T1.PARAM_HEADER_ID = T2.paramHeaderId
        WHEN NOT MATCHED THEN
        INSERT
        (
            RECORD_ID,
            PARAM_HEADER_ID,
            NET_PERMIT,
            NET_ACCESS_SIGN_NUM,
            SCRAMBLE_CODE,
            EQP_MODEL,
            NASN,
            CREATION_DATE,
            CREATED_BY
        )
        VALUES
        (SFC.SP_SPECIALITY_NAL_S.nextval,T2.paramHeaderId,T2.netPermit,T2.netAccessSignNum,T2.scrambleCode,T2.eqpModel,T2.nasn,T2.createdDate,T2.createdBy)
    </insert>

    <select id="selectBarAccSignForSchTask" parameterType="com.zte.interfaces.dto.BarcodeNetSignDTO" resultType="com.zte.interfaces.dto.BarcodeNetSignDTO">
        select * from (
        select rownum, t.net_access_sign_num, t.mac1_addr, t.CREATED_BY,t.lastupdatetime as last_updated_time
        from sfc.barcode_net_sign t
        where 1=1
        <if test="lastUpdatedTime != null">
            AND t.lastupdatetime >=  #{lastUpdatedTime, jdbcType = TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND t.lastupdatetime &lt;=  #{endTime, jdbcType = TIMESTAMP}
        </if>
        <if test="lastUpdatedTime != null and netAccessSignNum != null and netAccessSignNum != ''">
            and not exists (
            select 1 from sfc.barcode_net_sign temp
            where temp.net_access_sign_num = t.net_access_sign_num
            and temp.lastupdatetime = #{lastUpdatedTime, jdbcType = TIMESTAMP}
            and temp.net_access_sign_num &lt;= #{netAccessSignNum, jdbcType = VARCHAR}
            )
        </if>
        <if test="lastUpdatedTime == null">
            and 1=2
        </if>
        order by t.lastupdatetime,t.net_access_sign_num asc)
        where rownum &lt;= #{row, jdbcType = INTEGER}
    </select>

</mapper>