<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.domain.model.ZmsBoardSnTencentRepository">
    <resultMap id="entityMap" type="com.zte.interfaces.dto.ZmsBoardSnTencentDTO">
        <result column="record_id" jdbcType="NUMERIC" property="recordId" />
        <result column="entity_id" jdbcType="VARCHAR" property="entityId" />
        <result column="entity_name" jdbcType="VARCHAR" property="entityName" />
        <result column="board_sn" jdbcType="VARCHAR" property="boardSn" />
        <result column="b2b_status" jdbcType="VARCHAR" property="b2bStatus" />
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
        <result column="created_by" jdbcType="NUMERIC" property="createdBy" />
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
        <result column="last_updated_by" jdbcType="NUMERIC" property="lastUpdatedBy" />
        <result column="enabled_flag" jdbcType="VARCHAR" property="enabledFlag" />
    </resultMap>

    <select id="pageSelectBoardSnTencent" resultMap="entityMap">
        SELECT * from (
        select a.*, ROWNUM rn from (
            select
            record_id,entity_id,entity_name,board_sn,b2b_status,error_msg,creation_date,created_by,last_update_date,
            last_updated_by, enabled_flag
            from
            WMES.ZMS_BOARD_SN_TENCENT
            where enabled_flag = 'Y'
            <if test="jobStartTime == null or jobEndTime == null">
                and 1=2
            </if>
            <if test="jobStartTime != null and jobEndTime != null">
                and creation_date >  #{jobStartTime, jdbcType = TIMESTAMP}
                and creation_date &lt;=  #{jobEndTime, jdbcType = TIMESTAMP}
            </if>
            <if test="boardSnList != null and boardSnList.size() > 0">
                and board_sn in
                <foreach item="item" index="index" collection="boardSnList" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            order by creation_date asc
        ) a WHERE ROWNUM &lt;= #{page, jdbcType = NUMERIC} * #{rows, jdbcType = NUMERIC}
        ) where rn > (#{page, jdbcType = NUMERIC} - 1) * #{rows, jdbcType = NUMERIC}
    </select>


</mapper>