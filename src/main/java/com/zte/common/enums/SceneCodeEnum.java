package com.zte.common.enums;

/**
 * 任务分类枚举
 * <AUTHOR>
 */
public enum SceneCodeEnum {
    /**
     * formal_scedule
     */
    FORMAL_SCEDULE("formal_scedule"),
    /**
     * rework_without_md
     */
    REWORK_WITHOUT_MD("rework_without_md"),
    /**
     * buffer_scedule_without_md
     */
    BUFFER_SCEDULE_WITHOUT_MD("buffer_scedule_without_md"),
    /**
     * 场景五 buffer_totally_bind
     */
    BUFFER_TOTALLY_BIND("buffer_totally_bind"),
    /**
     * 场景六 buffer_partially_bind
     */
    BUFFER_PARTIALLY_BIND("buffer_partially_bind");

    private String code;

    SceneCodeEnum(String code){
        this.code = code ;
    }

    public String getCode() {
        return code;
    }

    public static SceneCodeEnum getEnum(String code) {
        for (SceneCodeEnum tempEnum : SceneCodeEnum.values()) {
            if (tempEnum.getCode().equals(code)) {
                return tempEnum;
            }
        }
        return null;
    }
}


