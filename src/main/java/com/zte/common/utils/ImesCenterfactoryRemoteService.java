package com.zte.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.interfaces.step.dto.BulkTaskDetailDTO;
import com.zte.interfaces.step.dto.CustomerDataLogDTO;
import com.zte.interfaces.step.dto.ECCustomerItemsDTO;
import com.zte.interfaces.step.dto.ECCustomerStockInfoRequestDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.resourcewarehouse.common.utils.AesUtils;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.JsonUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.zte.common.model.MessageId.PUSH_B2B_FAILED;
import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;

@Component
public class ImesCenterfactoryRemoteService {

    @Value("${imes.url}")
    private String imesUrl;
    @Value("${imes.access.key}")
    private String accessKey;
    @Value("${imes.access.secret}")
    private String accessSecret;
    @Value("${imes.secret}")
    private String secret;

    @Value("${in.one.url}")
    private String inoneUrl;
    @Value("${in.one.wms.app.code}")
    private String inoneAppcode;

    public void pushDataToB2B(List<CustomerDataLogDTO> dataList, String empNo) {
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(X_TENANT_ID, STR_10001);
        headers.put(X_EMP_NO, empNo);
        headers.put(X_AUTH_VALUE, STR_1);
        headers.put(X_FACTORY_ID, STR_51);
        headers.put(X_SOURCE_SYS, STR_R_WMS);
        headers.put(STR_APP_CODE, accessKey);
        headers.put(STR_SECRET_KEY, AesUtils.aesGcmEncrypt(accessSecret+SPLIT_22 + new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(new Date()), secret));

        // 请求url
        String url = imesUrl+ZTE_MES_MANUFACTURESHARE_CENTERFACTORY+PUSH_DATA_TO_B2B;

        // 发送请求
        String res = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(dataList),headers);

        BusiAssertException.isEmpty(res, PUSH_B2B_FAILED);
        ServiceData<JSONObject> result = JsonUtil.parseObject(res, new TypeReference<ServiceData<JSONObject>>() {});

        BusiAssertException.isEmpty(result, PUSH_B2B_FAILED);

        BusiAssertException.notEquals(SUSSESS, result.getCode().getCode(), result.getCode().getMsg());
    }

    public void pushDataToB2BKafKa(List<CustomerDataLogDTO> dataList, String empNo) {
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(INONE_APPCODE, inoneAppcode);
        headers.put(X_EMP_NO, empNo);
        headers.put(X_FACTORY_ID, STR_51);

        // 请求url
        String url = inoneUrl+ZTE_MES_MANUFACTURESHARE_CENTERFACTORY+PUSH_DATA_TO_B2B_KAFKA;

        // 发送请求
        String res = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(dataList),headers);

        BusiAssertException.isEmpty(res, PUSH_B2B_FAILED);
        ServiceData<JSONObject> result = JsonUtil.parseObject(res, new TypeReference<ServiceData<JSONObject>>() {});

        BusiAssertException.isEmpty(result, PUSH_B2B_FAILED);

        BusiAssertException.notEquals(SUSSESS, result.getCode().getCode(), result.getCode().getMsg());
    }

    public List<BulkTaskDetailDTO> getCategory(List<String> taskNo, String empNo) throws Exception{
        List<BulkTaskDetailDTO> bulkTaskDetailDTOList = new ArrayList<>();
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(INONE_APPCODE, inoneAppcode);
        headers.put(X_EMP_NO, empNo);
        headers.put(X_MES_BFF,IMES_BFF_AUTHORIZATION_DEFAULT);

        // 请求url
        String url = inoneUrl+ZTE_MES_MANUFACTURESHARE_CENTERFACTORY+BULK_QUERIES_INONE_URL;

        // 发送请求
        String result = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(taskNo),headers);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result).get(JSON_BO);
        bulkTaskDetailDTOList = JacksonJsonConverUtil.jsonToListBean(String.valueOf(json), new TypeReference<List<BulkTaskDetailDTO>>() {});
        return bulkTaskDetailDTOList;
    }

    /**
     * 按客户名称和合作模式查询客户物料信息
     * @param customerName 客户名称
     * @param cooperationMode 合作模式
     * @param itemNoList ZTE代码列表
     * @param empNo 员工编号
     * @return 客户物料信息列表
     */
    public List<ECCustomerItemsDTO> getCustomerItemsByCustomerAndMode(String customerName, String cooperationMode, List<String> itemNoList, String empNo) throws Exception {
        List<ECCustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        // 请求头
        Map<String, String> headers = Tools.newHashMap();
        headers.put(INONE_APPCODE, inoneAppcode);
        headers.put(X_EMP_NO, empNo);
        headers.put(X_MES_BFF, IMES_BFF_AUTHORIZATION_DEFAULT);

        ECCustomerStockInfoRequestDTO params = new ECCustomerStockInfoRequestDTO();
        params.setCustomerName(customerName);
        params.setCooperationMode(cooperationMode);
        params.setItemNoList(itemNoList);

        // 请求url
        String url = inoneUrl + ZTE_MES_MANUFACTURESHARE_CENTERFACTORY + GET_CUSTOMER_ITEMS_INFO_URL;
        // 发送请求
        String result = HttpClientUtil.httpPostWithJSON(url, JacksonJsonConverUtil.beanToJson(params), headers);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(result).get(JSON_BO);
        customerItemsDTOList = JacksonJsonConverUtil.jsonToListBean(String.valueOf(json), new TypeReference<List<ECCustomerItemsDTO>>() {});
        return customerItemsDTOList;
    }
}
