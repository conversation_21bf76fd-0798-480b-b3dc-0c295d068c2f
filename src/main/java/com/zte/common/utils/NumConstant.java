package com.zte.common.utils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class NumConstant {
    // String
    public static final String STR_0 = "0";
    public static final String STR_1 = "1";
    public static final String STR_2 = "2";
    public static final String STR_3 = "3";
    public static final String STR_4 = "4";
    public static final String STR_5 = "5";
    public static final String STR_6 = "6";
    public static final String STR_01 = "01";
    public static final String STR_02 = "02";
    public static final String STR_03 = "03";
    public static final String STR_04 = "04";
    public static final String STR_05 = "05";
    public static final String STR_06 = "06";
    public static final String STR_07 = "07";
    public static final String STR_10 = "10";
    public static final String STR_11 = "11";
    public static final String STR_12 = "12";
    public static final String STR_13 = "13";
    public static final String STR_14 = "14";
    public static final String STR_51 = "51";
    public static final String STR_101 = "101";
    public static final String STR_395 = "395";
    public static final String STR_350 = "350";
    public static final String STR_461 = "461";
    public static final String STR_471 = "471";
    public static final String TYPE_472 = "472";
    public static final String STR_10001 = "10001";

    // int
    public static final int INT_0 = 0;
    public static final int INT_1 = 1;
    public static final int INT_MINUS_1 = -1;
    public static final int INT_2 = 2;
    public static final int INT_3 = 3;
    public static final int INT_4 = 4;
    public static final int INT_5 = 5;
    public static final int INT_8 = 8;
    public static final int INT_9 = 9;
    public static final int INT_10 = 10;
    public static final int INT_12 = 12;
    public static final int INT_11 = 11;

    public static final int INT_13 = 13;
    public static final int INT_15 = 15;
    public static final int INT_16 = 16;
    public static final int INT_28 = 28;
    public static final int INT_30 = 30;
    public static final int INT_40 = 40;
    public static final int INT_50 = 50;
    public static final int INT_51 = 51;
    public static final int INT_60 = 60;
    public static final int INT_128 = 128;
    public static final int BATCH_SIZE = 100;
    public static final int NUM_TWO_HUNDRED = 200;
    public static final int NUM_TWO_HUNDRED_ONE = 201;
    public static final int INT_250 = 250;
    public static final int INT_500 = 500;
    public static final int INT_601 = 601;
    public static final int LIST_INCREMENT = 1000;
    public static final int INT_1999 = 1999;
    public static final int INT_2000 = 2000;
    public static final int INT_4000 = 4000;
    public static final int INT_740 = 740;
    public static final int INT_100000000 = 100000000;
    public static final int INT_1000000000 = 1000000000;


    // long
    public static final long LONG_1000 = 1000L;
    public static final long LONG_3600 = 3600L;
    public static final long LONG_36000 = 36000L;

    // BigDecimal
    public static final BigDecimal BIG_001 = BigDecimal.valueOf(0.01);
    public static final BigDecimal BIG_0 = new BigDecimal(0);
    public static final BigDecimal BIG_1 = new BigDecimal(1);
    public static final BigDecimal BIG_2 = new BigDecimal(2);
    public static final BigDecimal BIG_3 = new BigDecimal(3);
    public static final BigDecimal BIG_4 = new BigDecimal(4);
    public static final BigDecimal BIG_9 = new BigDecimal(9);



    public static final int EMP_NO_PNK = 10297758;
    public static final String NULLSTR = "";
    public static final String ZERO_D = "%0";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String D = "d";
    //  数据字典
    // 异常关闭退货申请单，不重新生成退货申请单的处理意见
    public static final String STR_1000034 = "1000034";
    // INFOR仓库与标准仓对应关系
    public static final String STR_1000037 = "1000037";
    public static final String STH = "STH";
    public static final String YYMMDD = "yyMMdd";

    public static final List<String> PENDING_TYPE_STR_LIST = Arrays.asList(STR_2,STR_4);
    public static final List<String> APPLY_TYPE_STR_LIST = Arrays.asList(STR_1,STR_3);
    public static final List<String> APPROVAL_TYPE_STR_LIST = Arrays.asList(STR_3,STR_4);
    public static final List<String> BILL_TYPE_STR_LIST = Arrays.asList(STR_1,STR_2);


    public static final String STR_MINUS_1 = "-1";
}
