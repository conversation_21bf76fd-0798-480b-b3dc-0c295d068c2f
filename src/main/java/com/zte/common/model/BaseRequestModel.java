package com.zte.common.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 网关请求对象
 * <AUTHOR>
 */
public class BaseRequestModel implements Serializable {

    private static final long serialVersionUID = 7235324831227496950L;

    private String name;

    private Object data;

    private String formatType;

    private String requestType;

    private Map<String, String> headerMap = new HashMap<>();

    private Map<String, BaseRequestModel> routeMap = new HashMap<>();

    public String getName() {

        return name;
    }

    public void setName(String name) {

        this.name = name;
    }

    public Object getData() {

        return data;
    }

    public void setData(Object data) {

        this.data = data;
    }

    public String getFormatType() {

        return formatType;
    }

    public void setFormatType(String formatType) {

        this.formatType = formatType;
    }

    public String getRequestType() {

        return requestType;
    }

    public void setRequestType(String requestType) {

        this.requestType = requestType;
    }

    public Map<String, String> getHeaderMap() {

        return headerMap;
    }

    public void setHeaderMap(Map<String, String> headerMap) {

        this.headerMap = headerMap;
    }

    public Map<String, BaseRequestModel> getRouteMap() {

        return routeMap;
    }

    public void setRouteMap(Map<String, BaseRequestModel> routeMap) {

        this.routeMap = routeMap;
    }

}

