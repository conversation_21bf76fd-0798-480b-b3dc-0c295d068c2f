package com.zte.common.model;

/**
 * 消息ID
 *
 * <AUTHOR>
 */
public class MessageId {
    public static final String CA_API_ERROR_CODE = "0010";
    public static final String CUSTOMIZE_MSG = "customize.msg";
    public static final String BOARD_ASSEMBLY_QUERY_ROW_TOO_LARGE = "board.assembly.query.row.too.large";
    public static final String BOARD_ASSEMBLY_QUERY_PARAMS_NOT_NULL = "board.assembly.query.params.not.null";
    public static final String BOARD_ASSEMBLY_QUERY_TIME_NOT_PAIRED = "board.assembly.query.time.not.paired";
    public static final String BOARD_ASSEMBLY_QUERY_PARAMS_NOT_ONE_CONDITION = "board.assembly.query.params.not.one.condition";
    public static final String BOARD_ASSEMBLY_QUERY_PAGE_AND_ROW_NOT_NULL = "board.assembly.query.page.and.row.not.null";
    public static final String BOARD_ASSEMBLY_QUERY_TIME_INTERVAL_EXCEEDS_ONE_MONTH = "board.assembly.query.time.interval.exceeds.one.month";
    public static final String DAY_OVER_ONE_YEAR = "day.over.one.year";

    public static final String DUPLICATE_MATERIAL_STORAGE_ATTRIBUTES = "duplicate_material_storage_attributes";

    public static final String YEAR_MONTH_IS_OVER_SYNCY_EAR = "year.month.is.over.syncYear";
    public static final String YEAR_OR_MONTH_IS_NULL = "year.or.month.is.null";

    public static final String THE_BIT_NUMBER_DATA_IS_ABNORMAL = "the.bit.number.data.is.abnormal";

    public static final String TIME_FORMAT_ERROR = "time.format.error";
    public static final String FACTORY_ID_IS_NULL = "factory.id.is.null";
    public static final String EMP_NO_IS_NULL = "emp.no.is.null";
    public static final String PAGE_SIZE_LAGER_THAN_1000 = "The.page.size.should.not.be.larger.than.1000";
    public static final String DO_NOT_PERFORM_DELETION_OPERATION_WITHOUT_INCOMING_DATA = "Do.not.perform.deletion.operation.without.incoming.data";
    public static final String FACTORY_ID_MUST_GREATER_THAN_ZERO = "factory.id.must.greater.than.zero";

    public static final String FACTORY_ID_OR_EMP_NO_IS_NULL = "factory.id.or.emp.no.is.null";

    // 数据不存在或已删除
    public static final String DATA_NOT_EXIST_OR_HAS_DELETED = "data.not.exist.or.has.deleted";

    public static final String SOFT_VER_OF_BOMS_INFO_NOT_SAME = "soft.ver.of.boms.info.not.same";

    public static final String SOFT_VERSION_NOT_MATCH = "soft.version.not.match";

    public static final String DOWNLOAD_IS_FAILED = "download.is.failed";

    public static final String SPLIT_TASK_NO_DATA = "split.task.no.data";

    public static final String MATCH_TASK_NO_DATA = "match.task.no.data";

    public static final String TASK_NOT_EXIST_OR_NOT_ALLOW_TO_MODIFY = "task.not.exist.or.not.allow.to.modify";

    // 上传文件到制品库失败
    public static final String UPLOAD_ARTIFACTORY_FAILED = "upload.artifactory.failed";
    // 删除制品库文件失败
    public static final String DELETE_ARTIFACTORY_FAILED = "delete.artifactory.failed";
    // 下载制品库文件失败
    public static final String DOWNLOAD_ARTIFACTORY_FAILED = "download.artifactory.failed";
    // 数据或参数异常
    public static final String DATA_OR_PARAMS_ERROR = "data.or.params.error";
    // 料单数据重复,请检查。
    public static final String BOM_DATA_REPEAT = "bom.data.repeat";

    public static final String APPROVAL_LOG_NAME = "approval_log_name";

    public static final String BOM_PROD_PARAMS_ERRRO = "bom_prod_params_errro";


    // 料单代码不能为空
    public static final String BOM_CODE_CAN_NOT_BE_EMPTY = "bom.code.can.not.be.empty";
    /**
     * 当前位号和已上传记录位号不完全匹配
     */
    public static final String POSITION_DO_NOT_MATCH_THE_UPLOADED_RECORD = "position.do.not.match.the.uploaded.record";
    // 您有待审批物料单,请及时处理。单据号 {0}
    public static final String YOU_HAVE_BILL_TO_APPROVE = "you.have.bill.to.approve";
    // 版本号最大为Z
    public static final String MAX_VERSION_IS_Z = "max.version.is.z";

    // 当前软件版本不是最新版本，不能删除
    public static final String SOFT_VERSION_OF_DELETE_DATA_IS_NOT_THE_LATEST = "soft.version.of.delete.data.is.not.the.latest";

    /**
     * lfid不能为空
     */
    public static final String LFID_IS_NULL = "lfid.is.null";

    /**
     * lfid不能为空
     */
    public static final String BATCH_LFID_ALL_NULL = "batch.lfid.all.null";

    public static final String PREMANU_TYPE_EXISTS = "premanu.type.exists";

    public static final String USER_IS_NOT_EXISTS = "user.is.not.exists";

    public static final String PASSWORD_IS_ERROR = "password.is.error";

    public static final String TAG_NUM_CANNOT_BE_NULL = "tagNum.cannot.be.null";

    public static final String SEND_CENTER_PSTASK_TO_SYS_FAILURE = "send.center.psTask.to.sys.failure";

    public static final String TAG_NULL_ONLY_ONE = "tag.null.only.one";

    public static final String TAG_NUM_CANNOT_BE_SAME = "tagNum.cannot.be.same";

    public static final String ITEM_NO_IS_NULL = "item.no.is.null";

    public static final String BOM_CODE_IS_NULL = "bom.code.is.null";
    public static final String EXCEL_RESOLUTION_FAILURE = "excel.resolution.failure";

    public static final String NETWORK_LICENSE_BINDING_ISNULL = "network.license.binding.isNUll";
    public static final String NETWORK_LICENSE_BINDING_ALLOCATED = "network.license.binding.allocated";
    public static final String NETWORK_LICENSE_BINDING_REPEATED = "network.license.binding.repeated";

    public static final String EXCEL_IMPORT_FAILURE = "excel.import.failure";

    public static final String EXCEL_IMPORT_SUCCESS = "excel.import.success";

    public static final String BARCODE_PARAM_IS_NULL_OR_TOO_MANY = "barcode.param.is.null.or.too.many";

    public static final String CLASSIFY_IS_TOO_MANY = "classify.is.too.many";

    public static final String LABEL_GENERATE_RULE_IS_NULL = "label.generate.rule.is.null";

    public static final String MATCH_MULTIPLE_LABEL_GENERATE_RULE = "match.multiple.label.generate.rule";

    public static final String GENERATE_BARCODE_FAILURE = "generate.barcode.failure";

    public static final String GENERATE_MAX_REEL_ID_COUNT_2000 = "generate.max.reel.id.count";

    public static final String FACTORY_ID_NOT_FIND = "orgid_is_null_or_factoryid_not_find";

    // 打印数量不能小于0且不能大于2000
    public static final String MAX_PRINT_COUNT_IS_2000 = "max.print.count.is.2000";
    // 调用条码中心生成条码异常
    public static final String BARCODE_GENERATE_ERROR = "barcode.generate.error";

    public static final String PLEASE_UPDATE_PRINTCS = "please.update.printcs";

    public static final String TASKSTATUS_NOT_ALLOW_TO_MODIFY = "taskStatus.not.allow.to.modify";

    public static final String SN_IS_NULL = "sn.is.null";
    public static final String PRODPLAN_ID_IS_NULL = "prodplan.id.is.null";
    public static final String QUERY_FLAG_ERROR = "query.flag.error";
    public static final String SN_NUM_MORE_THAN_HUNDRED = "sn.num.is.more.than.100";

    public static final String RESOURCE_USE_INFO_EXPORT_MAX = "resource.use.info.export.max";
    public static final String CA_API_CALL_ERROR = "ca.api.call.error";
    public static final String NO_NEED_BIND = "item.no.need.bind.ca";
    public static final String TASK_DATA_ERROR = "task.data.is.error";
    public static final String NO_TASK_NEED_GENE = "no.task.need.to.gene";
    public static final String SOCKET_IO_EXCEPTION = "socket.io.exception";
    public static final String XML_TO_OBJ_EXCEPTION = "xml.to.obj.error";
    public static final String BARCODE_NOT_EXIST = "barcode.not.exist";
    public static final String INIT_BARCODE_NOT_EXIST = "init.barcode.not.exist";
    public static final String NEW_BARCODE_EXIST = "new.barcode.existed";
    public static final String INCOMPLETE_CONDITIONS = "incomplete.conditions";

    public static final String PARAM_IS_NULL = "param.is.null";
    public static final String A_MAXIMUM_OF_TEN_DATA_CAN_BE_QUERIED = "a.maximum.of.ten.data.can.be.queried";
    public static final String CHECK_THAT_THE_PRODUCT_CODE_IS_EMPTY = "check_that_the_product_code_is_empty";
    public static final String MAIN_CRAFT_SECTION_IS_NULL = "mainCraftSection.is.null";
    public static final String PARAMS_ERROR = "params.error";
    public static final String EXPORT_ITEMLISTNO_EMPTY = "export.itemlistno.empty";
    public static final String REEL_ID_NOT_REGISTER = "reel.id.not.register";

    public static final String WORK_TIME_ERROR = "work.time.error";
    public static final String REMAIN_TIME_ERROR = "remain.time.error";
    public static final String THE_LAST_PROCESS_SHOULD_BE_STOCK = "last.process.should.be.stock";
    /**
     * 资源申请
     */
    public static final String USERTYPE_NOT_CORRENT = "userType.not.corrent";
    public static final String RESOURCE_APPLICATION_IS_APPLYING = "resource.application.is.applying";
    public static final String RESOURCE_INFO_NOT_EXIST = "resource.info.not.exist";
    public static final String RESOURCE_APPLICATION_NOT_EXIST = "resource.application.not.exist";
    public static final String RESOURCE_APPLICATION_APPLY_AMOUNT_IS_NULL = "resource.application.apply.amount.is.null";
    public static final String RESOURCE_APPLICATION_NOT_NEWEST = "resource.application.not.newest";
    public static final String APPLY_QTY_EXCEED_CAN_USE_QTY = "apply.qty.exceed.can.use.qty";
    public static final String AVAILABLE_QUANTITY_IS_ZERO = "available.quantity.is.zero";
    public static final String ORDER_OR_TASK_HAS_BEEN_USED = "orderOrTask.has.already.applied.for.resources";
    /**
     * 签名数据没有传
     */
    public static final String RFID_SIGN_INFO_DATA_IS_NULL = "rfid.sign.info.data.is.null";

    /**
     * 没有设置电子签名信息
     */
    public static final String RFID_SIGN_INFO_NOT_SETTING = "rfid.sign.info.not.setting";

    /**
     * 没有设置电子签名加密盐值
     */
    public static final String RFID_SIGN_INFO_NOT_SALT = "rfid.sign.info.not.salt";

    /**
     * 没有设置电子签名证书id
     */
    public static final String RFID_SIGN_INFO_NOT_CERT_ID = "rfid.sign.info.not.cert.id";

    /**
     * 没有设置电子签名证书id获取地址
     */
    public static final String RFID_SIGN_INFO_NOT_CERT_ID_GET_ADDR = "rfid.sign.info.not.cert.id.get.addr";

    /**
     * 证书id获取失败
     */
    public static final String RFID_SIGN_INFO_CERT_ID_GET_FAIL = "rfid.sign.info.cert.id.get.fail";

    /**
     * 证书id获取发生异常
     */
    public static final String RFID_SIGN_INFO_CERT_ID_GET_ERROR = "rfid.sign.info.cert.id.get.error";

    /**
     * 证书id获取和更新成功
     */
    public static final String RFID_SIGN_INFO_CERT_ID_OPER_SUCCESS = "rfid.sign.info.cert.id.oper.success";

    public static final String BOARD_FIRST_HOUSE_SERVICE_ERROR = "board.first.house.service.error";

    public static final String PRODPLAN_SERVICE_ERROR = "prodplan.service.error";
    public static final String RE_WOREK_ERROR = "re.work.error";

    public static final String DATE_ERROR = "date.error";
    public static final String AT_LEAST_CHOOSE_ONE = "at.least.choose.one";
    public static final String DATATYPE_ERROR = "datatype.error";
    public static final String DATETYPE_ERROR = "datetype.error";
    public static final String CYCLE_TYPE_ERROR = "cycle.type.error";
    public static final String RESULT_TYPE_ERROR = "result.type.error";

    public static final String DELIVER_SETS_SERVICE_ERROR = "deliver.sets.service.error";
    public static final String DELIVER_ENTITY_SERVICE_ERROR = "deliver.entity.service.error";

    public static final String CYCLE_PRODUCTCLASS_AND_PLANGROUP_ERROR = "cycle.productclass.and.plangroup.error";
    public static final String STR_FORMAT_TIME_NOT_NULL = "str.format.time.not.null";

    public static final String NO_DATA_TO_EXPORT = "no.data.to.export";
    public static final String BOM_SERVER_ERROR = "bom.server.error";
    //传入数据超出最大值
    public static final String AFFERENT_DATA_EXCEEDS_MAXIMUM = "afferent.data.exceeds.maximum";
    //没有找到设置的最大值
    public static final String NO_MAXIMUM_VALUE_FOUND = "no.maximum.value.found";
    //数据为空
    public static final String DATA_IS_EMPTY = "data.is.empty";
    public static final String CHECK_THE_FILE_AND_THE_SELECTED_CUSTOMER_PART_TYPE = "check.the.file.and.the.selected.customer.part.type";
    //接收到的数据不符合要求，请重新确认后提交
    public static final String RECEIVED_DATA_NOT_MEET_REQUIREMENTS = "received.data.not.meet.requirements";
    public static final String RECEIVED_DATA_NOT_MEET_REQUIREMENTS_NEW = "received.data.not.meet.requirements.new";
    //文件格式错误，请使用excel导入！
    public static final String FILE_FORMAT_ERROR = "file_format_error";
    //Workbook初始化失败
    public static final String WORKBOOK_INIT_FAIL = "workbook_init_fail";
    //导入失败，请重试！
    public static final String IMPORT_FAILED_PLEASE_TRY_AGAIN = "import.failed.please.try.again";
    //导入成功！行数：
    public static final String IMPORT_SUCCESSFUL_ROWS = "import.successful.rows";
    //导入成功！
    public static final String IMPORT_SUCCESSFUL = "import.successful";
    //一次最多只能导入5000条！
    public static final String MAX_IMPORT_IS_ONCE = "max.import.is.once";
    //外协厂ID不允许为空，第：row.getRowNum() + 1  行。
    public static final String SUBCONTRACTOR_ID_CAN_NOT_EMPTY = "subcontractor.id.can.not.empty";
    public static final String SUBCONTRACTOR_ID_CAN_NOT_EMPTY_NEW = "subcontractor.id.can.not.empty.new";
    //接入方编码为空！
    public static final String ACCESS_PARTY_CODE_IS_EMPTY = "access.party.code.is.empty";
    //外协厂名称为空！
    public static final String OUTSOURCING_FACTORY_NAME_IS_EMPTY = "outsourcing.factory.name.is.empty";
    //中兴po为空！
    public static final String ZTE_PO_IS_EMPTY = "zte.po.is.empty";
    //外协工单号为空！
    public static final String OUTSOURCING_WORK_ORDER_NUMBER_IS_EMPTY = "outsourcing.work.order.number.is.empty";
    //整机sn/en/产品/sn为空！
    public static final String DEVICE_SN_EN_PRODUCT_EMPTY = "device.sn.en.product.empty";

    /**
     * 时间格式报错
     */
    public static final String HARD_CODE_TIME_FORMAT = "Hard.Cord.Time.Format";
    //单板sn为空！
    public static final String SN_IS_EMPTY = "sn.is.empty";
    //mac为空
    public static final String MAC_IS_EMPTY = "mac.is.empty";
    //产品代码为空！
    public static final String PRODUCT_CODE_IS_EMPTY = "product.code.is.empty";
    // 物料代码不存在
    public static final String PRODUCT_CODE_NOT_EXIST = "product.code.not.exist";
    //主键为空
    public static final String PRIMARY_KEY_IS_EMPTY = "primary.key.is.empty";
    //系统暂不支持此种上送类型数据
    public static final String SYSTEM_NOT_SUPPORT_THE_TYPE = "system.not.support.the.type";
    //找不到接入方注册信息(tk)。
    public static final String TK_REGISTRATION_INFO_NOT_FOUND = "tk.registration.info.not.found";
    //找不到接入方注册信息(pk)。
    public static final String PK_REGISTRATION_INFO_NOT_FOUND = "pk.registration.info.not.found";
    //上送数据无效，验签失败。
    public static final String SUBMITED_DATA_IS_INVALID = "submited.data.is.invalid";
    //上送数据格式错误，没有传递值：
    public static final String SUBMITED_DATA_FORMAT_ERROR = "submited.data.format.error";
    //导入失败，请检查表中数据后重试！
    public static final String IMPORT_FAILED_PLEASE_CHECK_DATA = "import.failed.please.check.data";
    //产品库包结存不允许为空，第：
    public static final String PRODUCT_LIBRARY_CANNOT_BE_EMPTY = "product.library.cannot.be.empty";
    //计划数量不允许为空
    public static final String PLAN_QUANTITY_CANNOT_BE_EMPTY = "plan.quantity.cannot.be.empty";
    //产品大类不允许为空
    public static final String PRODUCT_CATEGORIES_CANNOT_BE_EMPTY = "product.categories.cannot.be.empty";
    //产品分类不允许为空
    public static final String PRODUCT_CLASSIFICATION_CANNOT_BE_EMPTY = "product.classification.cannot.be.empty";
    //产品型号不允许为空
    public static final String PRODUCT_MODEL_CANNOT_BE_EMPTY = "product.model.cannot.be.empty";
    //产品单位不能为空
    public static final String PRODUCT_UNIT_CANNOT_BE_EMPTY = "product.unit.cannot.be.empty";
    //产品名称不允许为空
    public static final String PRODUCT_NAME_CANNOT_BE_EMPTY = "product.name.cannot.be.empty";
    //已发货数不允许为空
    public static final String SHIPPED_NUMBER_CANNOT_BE_EMPTY = "shipped.number.cannot.be.empty";
    //截止时间不允许为空
    public static final String DEADLINE_CANNOT_BE_EMPTY = "deadline.cannot.be.empty";
    //单板结存不允许为空
    public static final String BOARD_BALANCE_CANNOT_BE_EMPTY = "board.balance.cannot.be.empty";
    //测试包结存不允许为空
    public static final String TEST_PACKAGE_BALANCE_CANNOT_BE_EMPTY = "test.package.balance.cannot.be.empty";
    //供应商不能为空
    public static final String VENDOR_CANNOT_BE_EMPTY = "vendor.cannot.be.empty";
    //物料名称不能为空
    public static final String ITEM_NAME_CANNOT_BE_EMPTY = "item.name.cannot.be.empty";
    //物料条码不能为空
    public static final String ITEM_CODE_CANNOT_BE_EMPTY = "item.code.cannot.be.empty";
    //物料代码不能为空
    public static final String ITEM_NO_CANNOT_BE_EMPTY = "item.no.cannot.be.empty";
    //物料型号不能为空
    public static final String ITEM_MODEL_CANNOT_BE_EMPTY = "item.model.cannot.be.empty";
    //物料PO号不能为空
    public static final String MATERIAL_PO_NUMBER_CANNOT_BE_EMPTY = "material.po.number.cannot.be.empty";
    //本批数量不能为空
    public static final String THIS_BATCH_QUANTITY_CANNOT_BE_EMPTY = "this.batch.quantity.cannot.be.empty";
    //抽检数量不能为空
    public static final String SAMPLING_QUANTITY_CANNOT_BE_EMPTY = "sampling.quantity.cannot.be.empty";
    //来料日期不能为空
    public static final String INCOMING_DATE_CANNOT_BE_EMPTY = "incoming.date.cannot.be.empty";
    //检测日期不能为空
    public static final String DETECTION_DATE_CANNOT_BE_EMPTY = "detection.date.cannot.be.empty";
    //检测单号不能为空
    public static final String DETECTION_NO_CANNOT_BE_EMPTY = "detection.no.cannot.be.empty";
    //抽样方式不能为空
    public static final String SAMPLING_METHOD_CANNOT_BE_EMPTY = "sampling.method.cannot.be.empty";
    //检测结果为空
    public static final String DETECTION_RESULT_IS_EMPTY = "detection.result.is.empty";
    //外协 REEL ID为空
    public static final String OUTSOURCING_REELID_IS_EMPTY = "outsourcing.reelid.is.empty";
    ///REEL ID数量为空
    public static final String REELID_QUANTITY_IS_EMPTY = "reelid.quantity.is.empty";
    //关联时间为空
    public static final String CORRELATION_TIME_IS_EMPTY = "correlation.time.is.empty";
    //库存数量为空！
    public static final String STOCK_QUANTITY_IS_EMPTY = "stock.quantity.is.empty";
    //库存截止时间为空！
    public static final String STOCK_CUTOFF_TIME_IS_EMPTY = "stock.cutoff.time.is.empty";
    //线体不允许为空
    public static final String LINE_CAN_NOT_BE_EMPTY = "line_can_not_be_empty";
    //SN扫描时间不允许为空，或者格式错误
    public static final String SN_SCAN_TIME_CANNOT_BE_EMPTY = "sn.scan.time.cannot.be.empty";
    //上料数量不允许为空
    public static final String FEED_QUANTITY_CANNOT_BE_EMPTY = "feed.quantity.cannot.be.empty";
    //FEED_TIME不允许为空，或者格式错误
    public static final String FEED_TIME_CANNOT_BE_EMPTY = "feed_time_cannot_be_empty";
    //文件名为空
    public static final String FILE_NAME_IS_EMPTY = "file_name_is_empty";
    //数据表中数据重复！
    public static final String DATA_TABLE_DUPLICATED = "data.table.duplicated";
    //车牌号为空！
    public static final String LICENSE_PLATE_NUMBER_IS_EMPTY = "license.plate.number.is.empty";
    //班车ERROR
    public static final String SHUTTLE_ERROR = "shuttle.error";
    //手机号码为空！
    public static final String PHONE_NUMBER_IS_EMPTY = "phone.number.is.empty";
    //路线为空！
    public static final String ROUTE_IS_EMPTY = "route.is.empty";
    //该标签不存在序列
    public static final String TAG_SEQUENCE_NOT_EXISTS = "tag.sequence.not.exists";
    //无BOM明细数据
    public static final String NO_BOM_DETAILS_DATA = "no.bom.details.data";
    //未查询到物料基础属性
    public static final String MATERIAL_BASE_ATTRIBUTES_NOT_FOUND = "material.base.attributes.not.found";
    //位号分析失败
    public static final String TAG_NUMBER_ANALYSIS_FAILED = "tag.number.analysis.failed";
    //位号拆分失败
    public static final String TAG_NUMBER_SPLIT_FAILED = "tag.number.split.failed";
    //不满足独立号规则,
    public static final String NOT_MEET_RULE = "not.meet.rule";
    //存在中文逗号，请确认!
    public static final String CHINESE_COMMA_EXISTED = "chinese.comma.existed";
    //存在多个连接符,
    public static final String EXIST_MULTIPLE_CONNECTORS = "exist.multiple.connectors";
    //只存在一个括号
    public static final String EXIST_ONLY_ONE_BRACKET = "exist.only.one.bracket";
    //不满足连续号规则,
    public static final String NOT_MEET_CONSECUTIVE_NUMBER_RULE = "not.meet.consecutive.number.rule";
    //两个位号数字前字符不相等,
    public static final String TWO_TAG_NUMBERS_CHARACTERS_NOT_EQUAL = "two.tag.numbers.characters.not.equal";
    //开始位号大于或等于结束位号,
    public static final String START_NUM_GREATER_OR_EQUAL_END_NUM = "start.num.greater.or.equal.end.num";
    //没有需拆分的BOM数据
    public static final String NO_BOM_DATA_NEED_SPLIT = "no.bom.data.need.split";
    //文件解析错误
    public static final String FILE_PARSING_ERROR = "file.parsing.error";
    //文件解析成功
    public static final String FILE_PARSING_SUCCESS = "file.parsing.success";
    //条码规则参数异常
    public static final String SN_RULE_PARAMS_ERROR = "sn.rule.params.error";
    //在其他大类中存在
    public static final String EXIST_IN_OTHER_CATEGORIES = "exist.in.other.categories";
    public static final String EXIST_IN_OTHER_CATEGORIES_NEW = "exist.in.other.categories.new";
    //已存在
    public static final String EXISTED = "existed";
    //描述：
    public static final String DESCRIPTION = "description";
    public static final String DESCRIPTION_NEW = "description.new";
    //名称：
    public static final String NAME = "name";
    public static final String NAME_NEW = "name.new";
    //传入机型为空
    public static final String INCOMING_MODEL_IS_EMPTY = "incoming.model.is.empty";
    //当前分类已有入库目标值，无法删除或修改
    public static final String CLASSIFICATION_EXIST_STORAGE_VALUE = "classification.exist.storage.value";
    //机型已存在
    public static final String MODEL_ALREADY_EXISTS = "model.already.exists";
    //点击下面的下载地址（下载导出文件）
    public static final String CLICK_THE_DOWNLOAD_ADDRESS_BELOW = "click.the.download.address.below";
    //"'>下载地址("
    public static final String DOWNLOAD_ADDRESS = "download.address";
    //<p style='color:red'>此地址将在
    public static final String ADDRESS_WILL_BE_AT = "address.will.be.at";
    //后自动删除,请尽快下载</p>
    public static final String DELETE_AUTOMATICALLY_AFTER = "delete.automatically.after";
    //条码追溯信息查询，生成查询结果excel成功
    public static final String BARCODE_CREATE_EXCEL_SUCCESS = "barcode.create.excel.success";
    //查询结果为空
    public static final String QUERY_RESULT_IS_EMPTY = "query.result.is.empty";
    //生成查询结果excel失败
    public static final String CREATE_QUERY_RESULT_EXCEL_FAILED = "create.query.result.excel.failed";
    //生成查询结果excel成功
    public static final String CREATE_QUERY_RESULT_EXCEL_SUCCESS = "create.query.result.excel.success";
    //生成查询结果excel失败：请重新生成
    public static final String GENERATE_QUERY_RESULT_EXCEL_FAILED = "generate.query.result.excel.failed";
    //物料代码前缀不能为空
    public static final String ITEM_CODE_PREFIX_CANNOT_BE_EMPTY = "item.code.prefix.cannot.be.empty";
    //,物料代码前缀长度必须为
    public static final String MATERIAL_CODE_PREFIX_LENGTH_BE = "material.code.prefix.length.be";
    //产品类型长度不能超过
    public static final String PRODUCT_TYPE_LENGTH_CANNOTE_EXCEED = "product.type.length.cannot.exceed";
    //MAC1地址："+model.getMacAddress1()+"未在MAC地址区间维护里,不能导入
    public static final String MAC_ADDRESS_NOT_MAINTAINED = "mac.address.not.maintained";
    //数据库中的表已经存在：barcodeList子部件条码
    public static final String DATABASE_TABLE_EXIST_SN = "database.table.exist.sn";
    //生产日期日期不能为空且格式 必须为：yyyy-MM-dd或yyyy/MM/dd!行：
    public static final String DATE_FORMAT_MUST_BE = "date.format.must.be";
    //内部合同号、子部件代码、子部件条码、生产日期、软件版本、硬件版本、MAC1、外箱箱号、接入地址、用户名、终端配置密码、制造工艺单号不能为空！行：
    public static final String SOME_PARMS_CANNOT_BE_EMPTY = "some.parms.cannot.be.empty";
    //数据异常,请重新导入:
    public static final String DATA_IS_ERROR = "data.is.error";
    //请输入有效的MAC地址
    public static final String MAC_ADDRESS_INVALID = "mac.address.invalid";
    //模板内部子部件条码重复
    public static final String DUPLICATE_BAR_CODE_INSIDE_TEMPLATE = "duplicate.bar.code.inside.template";
    //模板内部GPON-SN重复
    public static final String DUPLICATE_GPON_SN_INSIDE_THE_TEMPLATE = "duplicate.gpon.sn.inside.the.template";
    //模板内部设备标识重复
    public static final String DUPLICATE_DEVICE_ID_INSIDE_TEMPLATE = "duplicate.device.id.inside.template";
    //记录数不允许超过
    public static final String RECORDS_QTY_CANNOT_EXCEED = "records.qty.cannot.exceed";
    //生产单位不存在
    public static final String PRODUCTION_UNIT_DOES_NOT_EXIST = "production.unit.does.not.exist";
    //MAC起始地址应小于结束地址,且地址区间不能大于200万，不能新增
    public static final String MAC_START_ADDRESS_LESS_END = "mac.start.address.less.end";
    //MAC地址不能重复
    public static final String MAC_ADDRESS_CANNOT_BE_DUPLICATED = "mac.address.cannot.be.duplicated";
    //获取用户email失败
    public static final String FAILED_TO_GET_USER_EMAIL = "failed.to.get.user.email";
    //email格式不正确，请确认
    public static final String EMAIL_FORMAT_INCORRECT = "email.format.incorrect";
    public static final String EMAIL_TO_IS_EMPTY = "email.to.is.empty";
    public static final String EMAIL_SEND_ERR = "email.send.err";
    //获取redis锁资源失败，请重试
    public static final String FAILED_TO_GET_REDIS_LOCK = "failed.to.get.redis.lock";
    //待注册信息获取失败
    public static final String FAILED_TO_GET_REGISTRATION_INFO = "failed.to.get.registration.info";
    //导入数据为空
    public static final String IMPORT_DATA_IS_EMPTY = "import.data.is.empty";
    //SN重复
    public static final String DUPLICATE_SN = "duplicate.sn";
    public static final String DUPLICATE_SN_NEW = "duplicate.sn.new";
    //Brand/品牌不能为空
    public static final String BRAND_CANNOT_BE_EMPTY = "brand.cannot.be.empty";
    //Type/设备种类不能为空
    public static final String DEVICE_TYPE_CANNOT_BE_EMPTY = "device.type.cannot.be.empty";
    //Model/型号不能为空
    public static final String MODEL_CANNOT_BE_EMPTY = "model.cannot.be.empty";
    //生产任务号(外协)不能为空
    public static final String PROD_TASK_NO_CANNOT_BE_EMPTY = "prod.task.no.cannot.be.empty";
    //Product BOM/制造工艺单号不能为空
    public static final String MANUFACTURING_PROCESS_NO_CANNOT_BE_EMPTY = "manufacturing.process.no.cannot.be.empty";
    //STB MAC1/ MAC1* 必填项不能为空
    public static final String MAC1_CANNOT_BE_EMPTY = "mac1.cannot.be.empty";
    //PCBA SN1/单板SN1* 必填项不能为空
    public static final String SN_CANNOT_BE_EMPTY = "sn.cannot.be.empty";
    //STB SN/产品SN* 必填项不能为空
    public static final String PRODUCT_SN_CANNOT_BE_EMPTY = "product.sn.cannot.be.empty";
    //CARTOON SN/纸箱SN* 必填项不能为空
    public static final String CARTON_SN_CANNOT_BE_EMPTY = "carton.sn.cannot.be.empty";
    //栈板SN/生产日期/电源SN/遥控器SN/FT测试结果/FT测试时间/Burn-In Result老化测试结果/Burn-In Time老化测试时长/MAC配置结果/MAC配置时间* 必填项不能为空
    public static final String REQUIRED_PARAMS_CANNOT_BE_EMPTY = "params.cannot.be.empty";
    //开关机测试结果/开关机测试时间/整机测试结果/整机测试时间/整机校验结果/整机校验时间* 必填项不能为空
    public static final String PART_TWO_PARAMS_CANNOT_BE_EMPTY = "part.two.params.cannot.be.empty";
    //出厂配置结果/出厂配置时间/软件版本号/Logo版本号/出厂配置文件名* 必填项不能为空
    public static final String PART_THREE_PARAMS_CANNOT_BE_EMPTY = "part.three.params.cannot.be.empty";
    //产品SN已经存在
    public static final String PRODUCT_SN_ALREADY_EXISTS = "product.sn.already.exists";
    //传输校验失败(SHA256)
    public static final String TRANSMISSION_CHECK_FAILED = "transmission.check.failed";
    //没有需要绑定CA证书的条码
    public static final String NO_BARCODE_REQUIRED_BIND_CA_CERTIFICATE = "no.barcode.required.bind.ca.certificate";
    //文件上传失败
    public static final String FILE_UPLOAD_FAILED = "file.upload.failed";
    //文件上传成功
    public static final String FILE_UPLOAD_SUCCEED = "file.upload.succeed";
    //生成秘钥异常
    public static final String GENERATE_KEY_EXCEPTION = "generate.key.exception";
    //加密异常
    public static final String ENCRYPTION_EXCEPTION = "encryption.exception";
    //解密异常
    public static final String DECRYPTION_EXCEPTION = "decryption.exception";
    //签名异常
    public static final String SIGNATURE_EXCEPTION = "signature.exception";
    //私钥为空
    public static final String PRIVATE_KEY_IS_EMPTY = "private.key.is.empty";
    //验签异常
    public static final String SIGNING_EXCEPTION = "signing.exception";
    //文件初始化失败
    public static final String FILE_INITIALIZATION_FAILED = "file.initialization.failed";
    //文件为空
    public static final String FILE_IS_NULL = "file_is_null";
    //数据有误
    public static final String DATA_ERROR = "data.error";
    //设备已绑定，请重新选择
    public static final String DEVICE_IS_BOUND = "device.is.bound";
    //插入数据操作失败！
    public static final String INSERT_DATA_OPERATION_FAILED = "insert.data.operation.failed";
    //删除数据操作失败！
    public static final String DEL_DATA_OPERATION_FAILED = "del.data.operation.failed";
    //修改数据操作失败！
    public static final String UPDATE_DATA_OPERATION_FAILED = "update.data.operation.failed";
    //该卡机信息已存在，请检查
    public static final String CARD_MACHINE_INFO_IS_EXISTED = "card.machine.info.is.existed";
    //部门名称已存在，请检查"
    public static final String DEPARTMENET_IS_EXISTED = "departmenet.is.existed";
    //该工号已存在，请检查
    public static final String EMPLOYEE_NO_IS_EXISTED = "employee.no.is.existed";
    //该卡号已存在，请检查
    public static final String CARD_NO_IS_EXISTED = "card.no.is.existed";
    //定时更新记录执行失败
    public static final String SCHEDULED_UPDATE_RECORD_EXECUTION_FAILED = "scheduled.update.record.execution.failed=";
    //定时更新记录接口调用成功
    public static final String INTERFACE_CALL_SUCCEEDED = "interface.call.succeeded";
    // 接口调用异常
    public static final String INTERFACE_CALL_ERROR = "interface.call.error";
    //定时更新记录接口调用成功
    public static final String DUPLICATE_ROUTE_NAME = "duplicate.route.name";
    //"第" + (i+1) + "条缺失调度日期！请检查"
    public static final String MISS_SCHEDULED_DATE = "miss.scheduled.date";
    //站点名称重复，请重新输入站点名称！
    public static final String DUPLICATE_SITE_NAME = "duplicate.site.name";
    //经停站已存在，请重新选择！
    public static final String STOP_ALREADY_EXISTED = "stop.already.existed";
    //序号已存在，请重新输入！
    public static final String SERIAL_NUMBER_IS_EXISTS = "serial.number.is.exists";
    //条码生成失败
    public static final String SN_GENERATION_FAILED = "sn.genseration.failed";
    //条码生成成功
    public static final String SN_GENERATION_SUCCESS = "sn.generation.success";
    //条码绑定CA失败
    public static final String SN_BIND_CA_FAILED = "sn.bind.ca.failed";
    //接入方编号已存在，请重新输入
    public static final String ACCESS_NO_IS_EXISTED = "access.no.is.existed";
    //接入方名称已存在，请重新输入
    public static final String ACCESS_NAME_IS_EXISTED = "access.name.is.existed";
    //绑定成功
    public static final String BIND_SUCCESS = "bind_success";
    //操作成功
    public static final String OPERATION_SUCCESS = "operation.success";
    //处理失败
    public static final String PROCESSING_FAILED = "processing.failed";
    //前加工去向代码已存在，请确认
    public static final String PREPROCESS_DESTINATION_CODE_EXISTED = "preProcess.destination.code.existed";
    //正在生成excel，请留意邮件
    public static final String GENERATING_EXCEL = "generating.excel";
    public static final String GENERATING_OFFLINE_EXPORT_EXCEL = "generating.offline.export.excel";
    //操作人不能为空
    public static final String OPERATOR_CANNOT_BE_EMPTY = "operator.cannot.be.empty";
    //没有可删除数据，只能删除自己导入的数据！
    public static final String NO_DATA_TO_DEL = "no.data.to.del";
    //MAC格式不正确！
    public static final String MAC_FORMAT_INCORRECT = "mac.format.incorrect";
    //导出数据失败，用户empNo为空
    public static final String FAILED_EXPORT_EMP_NO_IS_EMPTY = "failed.export.emp.no.is.empty";
    //Reel ID校验通过
    public static final String REEL_ID_VERIFY_PASSED = "reel.id.verify.passed";
    //Reel ID有误
    public static final String REEL_ID_IS_WRONG = "reel.id.is.wrong";
    //操作异常
    public static final String ABNORMAL_OPERATION = "abnormal.operation";
    //ReelId已注册
    public static final String REELID_IS_REGISTERED = "reelid.is.registered";
    //记录数不允许超过5000行
    public static final String NUMBER_CANNOT_EXCEED = "number.cannot.exceed";
    //请输入目录代码
    public static final String CATALOG_CODE_CANNOT_BE_EMPTY = "catalog.code.cannot.be.empty";
    //请输入子项代码
    public static final String SUB_ITEM_CODE_CAN_NOT_BE_EMPTY = "sub.item.code.can.not.be.empty";
    //{"message":"统一权限鉴权不通过"}
    public static final String AUTHENTICATION_FAILED = "authentication.failed";

    public static final String BARCODE_CALL_SYSTEM_NULL = "barcode.call.system";

    public static final String BARCODE_IS_NULL = "barcode.is.null";

    public static final String CHOREOGRAPHER_URL_TYPE_ERROR = "choreographer.url.type.error";

    public static final String CHOREOGRAPHER_CALL_ERROR = "choreographer.call.error";

    public static final String TASK_NO_IS_NULL = "taskNo.is.null";

    // todo
    //根据批次查询料单代码异常
    public static final String GET_ITEMLISTNO_ERROR = "get.itemlistno.error";

    //回写查询型号报错
    public static final String QUERY_STYLE_AND_BRAND_ERROR = "query.style.and.brand.error";

    //资源池维护导出异常
    public static final String QUERY_EXPORT_ERROR = "query.export.error";

    //资源池维护导出异常
    public static final String EXPORT_LIMIT_1W = "export.limit.1w";
    //AVL查询失败
    public static final String AVL_QUERY_ERROR = "avl.query.error";

    // 调用条码中心生成空条码失败
    public static final String BLANK_GENERATE_ERROR = "blank.generate.error";

    //reelid为空
    public static final String REELID_NULL = "reelid.null";

    //查询pkCode信息为空
    public static final String PKCODE_INFO_IS_EMPTY = "pkcode.info.is.empty";

    //物料代码为空
    public static final String ITEM_NO_NULL = "item.no.null";

    //生产批次为空
    public static final String PRODUCT_TASK_IS_NULL = "product.task.is.null";

    //220条码为空
    public static final String REELID_SN_IS_NULL = "reelid.sn.is.null";

    //输入数量大于待注册数量
    public static final String INPUT_MORE_THAN_REGITER = "input.more.than.regiter";
    public static final String NOT_LFID_MATER = "not.lfid.meter";

    public static final String REEL_ID_REGISTERED = "reel.id.registered";
    // 合同任务表征数据正在生成中。请稍候重试
    public static final String IS_CREATING_PLEASE_WAIT = "is.creating.please.wait";

    // 料单代码{0}正在保存绑定设置。请稍候重试
    public static final String PROD_BIND_SETTING_IS_SAVING = "prod.bind.setting.is.saving";

    // 获取装配最后子工序失败
    public static final String GET_LAST_PACKAGE_PROCESS_FAIL = "get.last.package.process.fail";

    // 物料代码 {0} 已绑定到其它工序。请检查
    public static final String ITEM_CODE_HAS_BIND_ON_OTHER_PROCESS = "item.code.has.bind.on.other.process";

    // 正在上传请稍后
    public static final String IS_UPLOADING_PLEASE_WAIT = "is.uploading.please.wait";

    // 单板指令周期信息数据正在生成中。请稍候重试
    public static final String BOARD_INSTRUCTION_CYCLE_INFO_IS_CREATING_PLEASE_WAIT = "board.instruction.cycle.info.is.creating.please.wait";

    public static final String NETWORK_LICENSE_SIGN_LOCK_ERROR = "network.license.sign.lock";

    public static final String NETWORK_LICENSE_RESOURCE_NOT_FOUNT = "network.license.resource.not.fount";

    public static final String NETWORK_LICENSE_PRINT_NOT_FOUNT = "network.license.print.not.fount";
    public static final String NETWORK_LICENSE_RESOURCE_PRINT_NOT_FOUNT = "network.license.resource.print.not.fount";
    /**
     * 打印模板信息
     */
    public static final String PRINTTYPE_OR_PRINTSCENE_IS_NULL = "printType.or.printScene.is.null";

    public static final String BOM_DATA_OF_BOM_NO_NOT_EXIST = "bom.data.of.bom.no.not.exist";

    public static final String ITEM_NO_CONTAINS_ONE_POSITION_BUT_NOT_ALL_POSITION = "item.no.contains.one.position.but.not.all.position";

    public static final String NO_FIND = "not.find";
    public static final String MATERIAL_FILE_UPLOAD_ERROR = "material.file.upload.error";
    public static final String MATERIAL_FILE_UPLOAD_EMPTY = "material.file.upload.empty";
    public static final String MATERIAL_FILE_UPLOAD_MAX_BEYOND = "material.file.upload.max.beyond";
    public static final String MATERIAL_FILE_UPLOAD_MESSAGE = "material.file.upload.message";
    public static final String TAGNUM_NULL_IS_CREATE = "tagNum.null.is.create";
    public static final String TAGNUM_NULL_CAN_NOT_CREATE = "tagNum.null.can.not.create";
    public static final String REELID_NOT_EXIST_IN_IMES = "reelid.not.exist.in.mes";
    public static final String TAGNUM_EXIST_DISABE_CREATE = "tagNum.exist.disabe.create";
    public static final String TAGNUM_IS_EXIT = "tagnum.is.exit";
    public static final String TAGNUM_SOME_IS_EXIT = "tagnum.some.is.exit";
    public static final String DISTRIBUTION_OPERATION_IS_EMPTY = "distribution.operation.is.empty";
    public static final String TAGNUM_EXIST_PRE_PROCESSING_TYPE_AND_DESTINATION_IS_NULL = "tagNum.exist.pre.processing.type.and.destination.is.null";

    public static final String GENERATE_APPLY_NO_FAILED = "generate.apply.no.failed";

    public static final String GET_PROGRAM_BILL_INFO_FAILED = "get.program.bill.info.failed";

    public static final String GET_PROGRAM_SAMPLE_INFO_FAILED = "get.program.sample.info.failed";

    public static final String GET_PROGRAM_SMALL_INFO_FAILED = "get.program.small.info.failed";

    public static final String CURRENTHANDLER_IS_NULL = "currenthandler.is.null";

    public static final String UPDATE_BARCODE_FAIL = "update.barcode.fail";
    public static final String SN_LOST_BOARD_CENTER = "sn.lost.board.center";

    public static final String MATERIAL_CODE_IS_MAINTAINED_PRE_PROCESSING_DATA = "material.code.has.maintained.the.pre.processing.data";

    public static final String MATERIAL_CODE_IS_MAINTAINED_DISTRIBUTION_OPERATION = "material.code.has.maintained.only.distribution.operation";

    public static final String TAGNUM_DISTRIBUTION_OPERATION_INCONSISTENT = "tagnum.distribution.operation.inconsistent";

    public static final String ERROR_MSG_SET = "error_msg_set";

    public static final String CONFIRM_MSG_SET = "confirm_msg_set";

    public static final String ADD_PRE_MANU_INFO_BATCH_FAILED = "add.pre.manu.info.batch.failed";

    public static final String NO_LOCATION_DATA = "no.location.data";
    //供应商编码错误
    public static final String SUPPLIER_CODE_ERROR = "supplier.coding.error";
    //物料接收库存为空
    public static final String ITEM_INVENTORY_IS_NULL = "item.inventory.is.null";
    //物料未核对，不允许结束
    public static final String ITEM_NOT_CHECK = "item.not.check";
    // 任务号不是本工程
    public static final String TASK_IS_NOT_THIS_FATORY = "task.is.not.this.factory";
    //数据字典 {0} 未配置
    public static final String SYS_LOOK_NOT_CONFIG = "sys.look.not.config";

    // 您有informatica定时器数据同步失败,请及时处理
    public static final String YOU_HAVE_TIMER_SYNCHRONIZE_FAIL = "you.have.timer.synchronize.fail";

    public static final String RESOURCE_WARNING_WATERLEVEL_TITLE = "resource.warning.waterLevel.title";
    public static final String RESOURCE_WARNING_WATERLEVEL_TIP = "resource.warning.waterLevel.tip";
    public static final String RESOURCE_WARNING_EXPIRYDATE_TITLE = "resource.warning.expiryDate.title";
    public static final String RESOURCE_WARNING_EXPIRYDATE_TIP = "resource.warning.expiryDate.tip";
    public static final String RESOURCE_WARNING_RESOURCENO = "resource.warning.resourceNo";
    public static final String RESOURCE_WARNING_DEVICETYPE = "resource.warning.deviceType";
    public static final String RESOURCE_WARNING_LOWWATERLEVEL = "resource.warning.lowWaterLevel";
    public static final String RESOURCE_WARNING_AVAILABLEQUANTITY = "resource.warning.availableQuantity";
    public static final String RESOURCE_WARNING_EXPIRYDATE = "resource.warning.expiryDate";
    public static final String RESOURCE_WARNING_EXPIRYDATEBEFORE = "resource.warning.expiryDateBefore";

    //informatica定时器数据同步失败
    public static final String TIMER_SYNCHRONIZE_FAIL = "timer.synchronize.fail";
    // 批次正在排产
    public static final String PRODPLANID_IS_PRODUCTION = "prodPlanId.is.production";
    public static final String UNBINDING_SETTING_EXIST = "unbinding.setting.exist";
    // 当前产品代码正在位号拆分中，请稍后
    public static final String PRODUCTCODE_IS_DEALING = "productcode.is.dealing";
    public static final String CAD_POINT_LOST_ERROR = "cad.point.lost.error";
    public static final String CAD_POINT_LOST_LAST_INFOR = "cad.point.lost.last.infor";
    public static final String CAD_POINT_LOST_ERROR_SURE = "cad.point.lost.error.sure";
    public static final String CAD_POINT_EMPTY = "cad.point.empty";

    public static final String TYPE_CODE_NAME_TRACE_CODE_NAME_CAN_NOT_BE_EMPTY_ALONE = "type_code_name_trace_code_name_can_not_be_empty_alone";
    public static final String EXPORT_TIMEOUT = "export.timeout";
    public static final String RESOURCE_DATA_ERROR = "resource.data.error";
    //以下资源号不匹配
    public static final String RESOURCE_NO_DOES_NOT_MATCH = "resource.no.does.not.match";
    public static final String IMPORT_DATA_ERROR = "import.data.error";
    public static final String IMPORT_TIMEOUT = "import.timeout";
    public static final String IMPORT_SUCCESS = "import.success";

    public static final String RESOURCE_MATCH_FAILED = "resource.match.failed";
    public static final String RESOURCE_TYPE_NOT_EXISTS = "resource.type.not.exists";
    //输入资源号进行查询时资源类型必填
    public static final String RESOURCE_TYPE_SHOULD_NOT_BE_NULL = "resource.type.should.not.be.null";
    //查询时资源编号、资源号和创建时间必有一个
    public static final String RESOURCE_NO_STR_TIME_SHOULD_NOT_BE_NULL = "resource.no.str.time.should.not.be.null";
    //查询时时间类型不能超过2年
    public static final String SEARCH_TIME_LIMIT = "search.time.limit";
    //导出时资源类型必选
    public static final String RESOURCE_TYPE_CAN_NOT_BE_NULL = "resource.type.can.not.be.null";
    //资源维护导出时间不能超过半年
    public static final String EXPORT_TIME_INTERVAL_ERROR = "export.time.interval.more.than.180.days";
    //资源维护详细信息导出时间不能超过3个月
    public static final String EXPORT_DRTAIL_TIME_INTERVAL_ERROR = "export.time.interval.more.than.90.days";
    //资源编号不能为空
    public static final String EXPORT_DRTAIL_RESOURCE_NO_ERROR = "export.resourceNo.can.not.empty";
    //导出数量超过10w，请缩小选择时间范围
    public static final String EXPORT_MORE_THAN_10W = "export.total.more.than.100000";
    public static final String EXPORT_TIME_CAN_NOT_NULL = "export.time.can.not.null";

    public static final String START_SEGMENT_MUST_LESS_END_SEGMENT = "start.segment.must.less.end.segment";

    public static final String SEGMENT_MUST_LESS_END_SEGMENT = "start.segment.must.less.end.segment";

    public static final String DUPLICATE_RESOURCE_NUMBER_SEGMENT = "duplicate.resource.number.segment";

    public static final String HARD_CODE_FILE_TEMPLATE = "Hard.Cord.File.Template";
    public static final String FILE_FORMAT = "file.format.error";
    public static final String RESOURCE_FILE_FORMAT = "resource.file.format.error";
    public static final String RESOURCE_FILE_HEADER_TAGPARAM_ERROR = "resource.file.header.tagparam.error";

    public static final String ITEMCODE_SELECTED_CANNOT_MORE_THAN_100 = "itemCode.selected.cannot.more.than.100";

    public static final String LOG_ID = "log.id";
    public static final String UNKNOWN_ERROR_HANDLER = "unknown.error.handler";

    public static final String TASK_SOURCE_CANNOT_BE_EMPTY = "task_source_cannot_be_empty";

    public static final String TASK_HAS_BEEN_ISSUED_TO_THE_LOCAL_FACTORY = "task_has_been_issued_to_the_local_factory";

    public static final String FACTORY_NOT_CONFIGURED_IN_1245_DATA_DICTIONARY = "factory_not_configured_in_1245_data_dictionary";

    public static final String THE_BATCH_HAS_GENERATED_A_NESTING_LIST = "the_batch_has_generated_a_nesting_list";

    public static final String GET_TASK_INFO_ERROR = "get.task.info.error";

    public static final String THE_CURRENT_BATCH_IS_ISSUED = "the_current_batch_is_issued";

    public static final String GET_SPM_TLD_ERROR = "get.spm.tld.error";
    public static final String SEQUENCE_BEYOND_MAXVALUE = "sequence.beyond.maxvalue";
    public static final String SEQUENCE_NO_VALUE = "sequence.no.value";

    public static final String OPERATER_TIME_CAN_NOT_BE_EMPTY = "operater_time_can_not_be_empty";

    public static final String OPERATER_TIME_CAN_NOT_GREATER_ONE_YEAR = "operater_time_can_not_greater_one_year";

    public static final String FAILED_TO_GET_ASSEMBLY_RELATION_PUSH = "failed.to.get.assembly.relation.push";

    public static final String NO_PUSH_RECORD_FOR_THE_MATERIAL_CODE = "no.push.record.for.the.material.code";

    public static final String BLANK_MSG = "blank.msg";

    public static final String FAILED_TO_UPDATE_RESULT = "failed.to.update.result";

    public static final String ITEM_VERSION_NULL = "item.version.null";

    public static final String UPDATE_SUCCESSED = "update.successed";

    public static final String ITEM_CODE_LENGTH_NOT_TWELVE = "item.code.length.not.twelve";

    public static final String SELECTED_FACTORY_NOT_THE_FACTORY_CORRESPONDING_OF_TASK = "selected.factory.not.the.factory.corresponding.of.task";

    public static final String TASK_QTY_IS_NULL = "taskQty.is.error";

    public static final String BARCODE_GET_TEMPLATE_ERROR = "barcode.get.template.error";

    public static final String BARCODE_GET_TEMPLATE_ERROR_MSG = "barcode.get.template.error.msg";

    public static final String GET_LOOKUP_VALUE_ERROR = "get.lookup.value.error";

    public static final String FAILED_TO_GET_BARCODE_CENTER_URL = "failed_to_get_barcode_center_url";

    public static final String FAILED_TO_GET_BARCODE_CENTER_DOWNLOAD_URL = "failed.to.get.barcode.center.download.url";

    public static final String FAILED_TO_ADJUST_BARCODE_CENTER_PRINT_INTERFACE = "failed_to_adjust_barcode_center_print_interface";

    public static final String CALL_BARCODE_CENTER_TO_PRINT_FALIED = "call.barCode.center.to.print.falied";

    public static final String DOZEN_SN_NOT_EXIST_PRINT_DATA = "dozen.sn.not.exist.print.data";

    public static final String BAR_CODE_CENTER_IS_NULL = "bar.code.center.is.null";

    public static final String IP_IS_NULL = "ip.is.null";

    //物料代码为空
    public static final String ITEM_NOT_EXIST = "item.no.not.exist";

    public static final String PRINT_COUNT_IS_NULL = "print.count.is.null";
    public static final String PRINT_COUNT_CAN_NOT_MORE_TEN = "print.count.can.not.more.ten";
    public static final String PRINT_NUM_IS_NULL = "print.num.is.null";
    public static final String PRINT_NUM_CAN_NOT_MORE_ONE_THOUSAND = "print.num.can.not.more.one.thousand";
    public static final String LEAD_FLAG_IS_NULL = "lead.flag.is.null";
    public static final String PRINT_TEMPLATE_IS_NULL = "print.template.is.null";
    public static final String GET_PRINTER_FAILED = "get.printer.failed";

    public static final String FAILED_TO_OBTAIN_HR_INFO = "failed.to.obtain.hr.info";

    // 任务号批次号不能同时为空
    public static final String TASKNO_PRODPLAN_IS_NULL = "taskNo.prodplan.is.null";

    public static final String NO_BOX_CODE_DATA_OBTAINED = "no_box_code_data_obtained";
    /**
     * 物料代码类型不一致
     */
    public static final String ITEM_TYPE_DIFF = "item.type.diff";
    public static final String ITEM_HAS_EXISTS = "item.has.exists";
    /**
     * 物料代码、料单代码在bom中不存在
     */
    public static final String ITEM_NOT_EXISTS_BOM = "item.not.exists.bom";
    public static final String ITEM_INFLUENCE_BOM_MSG1 = "item.influence.bom.msg1";
    public static final String ITEM_INFLUENCE_BOM_MSG2 = "item.influence.bom.msg2";
    public static final String ITEM_INFLUENCE_BOM_MSG3 = "item.influence.bom.msg3";
    public static final String ROUTE_DETAIL_EMPTY = "route.detail.empty";

    public static final String UAC_TOKEN_CANNOT_BE_EMPTY = "uac_token_cannot_be_empty";

    public static final String FAILED_TO_TUNE_ICENTER_INTERFACE = "failed_to_tune_icenter_interface";

    public static final String FAILED_TO_CALL_ICENTER_INTERFACE = "failed_to_call_icenter_interface";
    public static final String FAILED_TO_CALL_ICENTER_TO_WITHDRAW_DOCUMENTS = "failed_to_call_icenter_to_withdraw_documents";

    public static final String THE_BARCODE_LENGTH_IS_LESS_THAN_7_DIGITS = "the_barcode_length_is_less_than_7_digits";
    public static final String FAILED_TO_GET_BARCODE_FACTORY_ID = "failed_to_get_barcode_factory_id";
    public static final String FAILED_TO_WRITE_TEST_RECORD = "failed_to_write_test_record";

    public static final String SPECIALITY_RESOURCE_NOT_EXIST = "speciality.resource.not.exist";
    public static final String SPECIALITY_PARAM_TEMPLATE_EXIST = "speciality.param.template.exist";
    public static final String SPECIALITY_PARAM_TEMPLATE_NOT_EXIST = "speciality.param.template.not.exist";
    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_NULL = "speciality.param.template.item.null";
    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_NAME_REPEAT = "speciality.param.template.item.name.repeat";
    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_BRACKETS = "speciality.param.template.item.brackets";
    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_TWO_OPERATOR = "speciality.param.template.item.two.operator";
    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_FUNCTION = "speciality.param.template.item.function";

    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR = "speciality.param.template.item.rule.err";
    public static final String SPECIALITY_PARAM_TEMPLATE_ITEM_ERROR = "speciality.param.template.item.error";

    public static final String SPECIALITY_PARAM_APPLYTASK_GENERATE = "speciality.param.applyTask.generate";
    public static final String SPECIALITY_PARAM_APPLYTASK_NOT_RESOURCE = "speciality.param.applyTask.not.resource";
    public static final String SPECIALITY_PARAM_APPLYTASK_RESOURCE_NOT_MATCH = "speciality.param.applyTask.resource.not.match";

    public static final String EXCEL_READ_FAILED = "excel.read.failed";
    public static final String TABLE_COLUME_ERROR = "table.column.error";
    //数据长度超限
    public static final String DATA_LENGTH_EXCEEDS_500 = "length.of.data.exceeds.500";
    public static final String STATUS_IS_RELEASED = "status.of.taskNo.is.released";
    public static final String TASKNO_OR_PRODPLANID_IS_EMPTY = "taskNo.or.prodplanId.fields.cannot.be.empty";
    public static final String FIELDS_CAN_NOT_BE_EMPTY = "fields.such.as.assembly.remarks.must.not.be.empty";
    public static final String DUPLICATE_DATE_IN_THE_TABLE = "duplicate.data.in.the.table";
    public static final String TASKNO_DO_NOT_MATCH_PRODPLANID = "taskNo.and.prodplanId.do.not.match";
    public static final String FAILED_TO_PROCESS_THE_DOCUMENT_IN_THE_FACTORY = "failed_to_process_the_document_in_the_factory";
    public static final String FAILED_TO_PROCESS_APPROVAL_CENTER_KAFKA_MESSAGE = "failed_to_process_approval_center_kafka_message";
    public static final String TASK_NO_IS_NOT_EXISTS = "task.no.not.exists";
    public static final String PRODPLAN_ID_NOT_EXISTS = "prodplan.id.not.exists";
    //前加工类型不能为空
    public static final String TYPE_NAME_SHOULD_NOT_BE_NULL = "type.name.should.not.be.null";
    public static final String PROCESS_CODE_ID_EXISTS = "process.code.id.exists";
    public static final String PROCESS_NAME_EXISTS = "process.name.exists";
    public static final String WORKSTTION_IS_EXISTS = "station.is.existed";
    public static final String INSERT_WORKSTATON_IS_SUCCESS = "insert.workstaion.is.success";
    /**
     * 参数为空
     */
    public static final String PARAMS_IS_NULL = "params.is.null";
    /**
     * 根据craftId未查询到状态为已提交CtBasic信息
     */
    public static final String QUERY_NOCTBASIC_BY_CRAFTID = "query.noCtBasic.by.craftId";
    public static final String EXISTING_UNCOMMIT_CRAFT = "existing.uncommit.craft";
    public static final String SAVE_SUCESS = "save.sucess";
    public static final String NOT_FIND_CRAFT_INFO = "not.find.craft.info";
    public static final String BOARD_ASSEMBLY = "must.be.board.assembly";
    public static final String BOARD_TEST = "must.be.board.test";
    public static final String THIS_VERSION_CRAFT_EXISTS = "this.version.craft.exists";
    public static final String THE_CURRENT_VERSION_IS_MALFORMED = "the_current_version_is_malformed";
    public static final String THE_VERSION_NUMBER_IS_TEMPORARILY_ONLY_SUPPORTED = "the_version_number_is_temporarily_only_supported";

    public static final String NOT_FIND_ROUTE_HEAD_INFO = "not.find.route.head.info";
    public static final String NOT_FIND_ROUTE_DETAIL_INFO = "not.find.route.detail.info";
    public static final String CRAFT_VERSION_EXIST = "craft_version_exist";
    public static final String CRAFT_VERSION_EXIST_SUBMITED = "craft_version_exist_submited";

    public static final String TAGS_PARAM_IS_NULL = "tags.param.is.null";
    public static final String TASK_NO_PULLING = "task.no.pulling";
    public static final String TASK_NO_NOT_EXIST_APS = "task.no.not.exist.aps";
    public static final String TASK_NO_EXIST_IMES = "task.no.exist.imes";
    public static final String PROD_ADDRESS_EMPTY = "prod.address.empty";
    public static final String BOM_ID_MISS = "bom.id.miss";
    public static final String SN_CARTON_RELATION_ERR = "sn_carton_relation_err";
    public static final String SN_CENTER_GREATER_THAN_INFOR_ERR = "sn_center_greater_than_infor_err";
    public static final String SN_CENTER_LESS_THAN_INFOR_ERR = "sn_center_less_than_infor_err";
    public static final String SKU_IS_NOT_ALI_CONTROL_OR_ALI_CODE = "sku_is_not_ali_control_or_ali_code";
    public static final String INFOR_STATUS_IS_NOT_EQUAL_TO_9 = "inFor_status_is_not_equal_to_9";
    public static final String BOX_HAS_NOT_BEEN_FULLY_BOUND = "box_has_not_been_fully_bound";
    public static final String BARCODES_IN_BOX_IS_GREATER_THAN_INFOR_CODES = "barcodes_in_box_is_greater_than_infor_codes";

    public static final String RECOVERY_FINISHED = "recovery.finished";
    public static final String NO_RESOURCE_RECOVERY = "no.resource.recovery";
    public static final String EXCEL_CONTENT_FORMAT_NOT_MATCH = "excel.content.format.not.match";
    public static final String PROD_PLAN_SEQUENCE_OVER = "prod.plan.sequence.over";
    public static final String KAFKA_MSG_SAVE_DATABASE = "kafka.msg.save.database";
    public static final String KAFKA_MSG_SAVE_DATABASE_TASK_UPDATE = "kafka.msg.save.database.task.update";
    public static final String PROD_PLAN_EXIST_CENTER_FACTORY = "prod.plan.exist.center.factory";
    public static final String THE_CURRENT_STATUS_OF_THE_APPROVER_IS_FINISHED = "the_current_status_of_the_approver_is_finished";
    public static final String CURRENT_DOCUMENT_INFORMATION_NOT_FOUND = "current_document_information_not_found";
    public static final String PARMS_ERR = "params.err";
    public static final String APS_PROPLANID_MSG_ERROR = "aps.proplanId.msg.error";

    public static final String FAILED_TO_DEAL_TECHNICAL_BILL = "failed_to_deal_technical_bill";
    public static final String MORE_THAN_50_BATCHES = "more_than_50_batches";
    public static final String REELID_ALREADY_EXISTS = "reelid_already_exists";


    public static final String FACTORY_ID_OF_CENTER_PSTASK_IS_NULL = "factory.id.of.center.pstask.is.null";
    public static final String FACTORY_ID_OF_CENTER_PSTASK_IS_ILLEGALITY = "factory.id.of.center.pstask.is.illegality";
    public static final String CENTER_FACTORY_NOT_EXIST_THE_TASK_NO = "center.factory.not.exist.the.task.no";
    public static final String PRODPLANNO_NOT_NULL = "prodplanno.not.null";
    public static final String PRODPLANMODIFYNO_NOT_NULL = "prodplanmodifyno.not.null";
    public static final String TECHNICAL_INFO_LOST = "technical_info_lost";

    public static final String GET_BOARD_ONLINE_INFO_FAILED = "get.boardonline.info.failed";
    public static final String GET_BAR_SUBMIT_INFO_FAILED = "get.barsubmit.info.failed";
    //更新数据字典值失败
    public static final String FAILED_TO_UPDATE_SYS_LOOK_UP_MEANING = "failed.to.update.sys.look.up.meaning";
    public static final String QUERY_PARAMS_LOST = "query.params.lost";
    public static final String TECHNICAL_INFO_OUTNUMBER = "technical_info_outnumber";
    public static final String APS_TASK_STATUS_ERROR = "aps.task.status.error";

    public static final String THE_PROCESS_PATH_OF_THE_MATERIAL_LIST_IS_NOT_FOUND = "the_process_path_of_the_material_list_is_not_found";
    public static final String PRODUCT_CODE_INCLUDE_PCB = "product.code.include.pcb";

    public static final String MATERAIL_LIST_MUST_BE_CONTAIN_15_CHARACTERS = "the.material.list.code.should.contain.15.characters";
    public static final String NO_BOM_INFORMATION_IS_FOUND = "no.bom.information.is.found";
    public static final String NO_BOM_INFORMATION_OR_NO_ROUTE = "no.bom.information.or.no.route";

    //料单代码数量超过三百
    public static final String THE_ITEMNOS_IS_MORE_THAN_THREE_HUNDRED = "the_itemnos_is_more_than_three_hundred";
    public static final String SYNCHRONIZE_SPM_DATA_WARNING = "synchronize.spm.data.warning";
    public static final String SYNCHRONIZE_SPM_DATA_ERROR = "synchronize.spm.data.error";
    public static final String GET_LAST_SYNC_TIME_ERROR = "get.last.sync.time.error";
    public static final String SPM_DATA_NOT_HAVE_CHG_REG_NO = "spm.data.not.have.chg.reg.no";
    public static final String SPM_DATA_NOT_HAVE_PROD_ID = "spm.data.not.have.prod.id";
    public static final String ITEM_NOS_MORE_THAN_ONE_THOUSAND = "the_itemnos_is_more_than_one_thousand";
    public static final String PARAM_CAN_NOT_BE_NULL = "params.can.not.be.null";
    public static final String ITEM_NOS_MORE_THAN_TEN = "the_itemnos_is_more_than_ten";
    public static final String QUERY_BOM_NO_NULL = "query.bom.no.null";
    public static final String TASK_NO_CANNOT_BE_NULL = "task.no.cannot.be.null";

    public static final String PARAM_SIZE_BETWEEN_1_AND_1000 = "param.size.between.1.and.1000";
    public static final String SEND_SEMI_WIP_EXT_BY_SPM_LOCK = "send.semi.wip.ext.by.spm.lock";
    public static final String SEND_MATERIAL_WIP_EXT_BY_SPM_LOCK = "send.material.wip.ext.by.spm.lock";
    public static final String NOT_FIND_WIP_EXT_PROD_PLAN = "no.find.wip.ext.prod.plan";
    public static final String PUSH_WIP_EXT_TO_FACTORY_FAILED = "push.wip.ext.to.factory.failed";

    public static final String FAILED_TO_OBTAIN_PROD_PLAN_IMES_BATCH_INFORMATION = "failed_to_obtain_prod_plan_imes_batch_information";

    public static final String PARAM_MISSING = "param.missing";
    public static final String QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY = "query.params.empty.except.create.by";
    public static final String IS_UPDATING_PLEASE_WAIT = "is.updating.please.wait";
    public static final String BOM_TEMPERATURE_IS_EXITED = "bom.temperature.is.exited";
    public static final String FAILED_TO_GET_SPM_LOCK_INFORMATION = "failed_to_get_spm_lock_information";
    public static final String FAILED_TO_WRITE_LOCAL_FACTORY_LOCK_INFORMATION = "failed_to_write_local_factory_lock_information";
    public static final String PRODUCTION_TYPE_ALREADY_EXISTS = "production_type_already_exists";
    public static final String THE_PROCESS_PATH_OF_THE_MAINTAINED_PROCESS_TYPE = "the_process_path_of_the_maintained_process_type";
    public static final String THE_CURRENT_PROCESS_PATH_IS_BEING_PROCESSED = "the_current_process_path_is_being_processed";
    public static final String CURRENTLY_EXPORTING = "currently_exporting";
    public static final String LOOKUP_6001_EMPTY = "lookup.6001.empty";
    public static final String DATA_VOLUME_EXCEEDS_5_W = "data_volume_exceeds_5_w";
    public static final String CENTER_TASK_QUERY_PARAM_IS_NULL = "center.task.query.param.is.null";
    public static final String CENTER_TASK_QUERY_PAGE_OR_ROW_ILLEGAL = "center.task.query.page.or.row.illegal";
    public static final String CENTER_TASK_QUERY_FIVE_PARAMS_NOT_ALL_NULL = "center.task.query.five.params.not.all.null";
    public static final String CENTER_TASK_QUERY_TIME_LARGER_2_YEARS_WHEN_NOT_PROD_AND_TASK = "center.task.query.time.larger.2.years.when.not.prod.and.task";
    public static final String CUSTOMER_ITEMS_PARAMS_NULL = "customer.items.params.null";
    public static final String CUSTOMER_TYPE_NULL = "customer.type.null";
    public static final String CUSTOMER_PARAMS_NULL_FOUR = "customer.params.null.four";
    public static final String CUSTOMER_PARAMS_NULL_OTHER = "customer.params.null.other";
    public static final String CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO = "customer.params.null.one.two.zero";

    public static final String CUSTOMER_ITEMS_ADD_OR_UPDATE = "customer.items.add.or.update";
    public static final String CHECK_ERROR_PLEASE_RESELECT_FILE_PARSING = "check.error.please.reselect.file.parsing";
    public static final String CUSTOMER_ITEMS_EXIST = "customer.items.exist";
    public static final String CUSTOMER_QRY_PARAMS_NULL = "customer.qry.params.null";
    public static final String QRY_TIME_CAN_NOT_GREATER_ONE_YEAR = "qry.time.can.not.greater.one.year";
    public static final String NO_ID_CANNOT_DELETE = "no.id.cannot.delete";
    public static final String SUB_CUSTOMER_NULL = "sub.customer.null";
    public static final String ITEM_NO_LIST_NULL = "item.no.list.null";
    public static final String MAX_ITEM_NO_IS_ONCE = "max.item.no.is.once";
    public static final String SUB_CUSTOMER_NOT_EXIST = "sub.customer.not.exist";
    public static final String SIZE_OF_DATA_TOO_LARGE = "size.of.data.too.large";

    public static final String PUSH_TIME_WITHIN_180_DAYS = "push.time.within.180.days";
    public static final String LOG_DATA_QUERY_PARAM_NULL = "log.data.query.param.null";
    public static final String LOG_DATA_QUERY_PARAM_NOT_WITH_TIME = "log.data.query.param.not,with.time";
    public static final String LOG_DATA_LOOKUP_TYPE_ERROR = "log.data.lookup.type.error";
    public static final String LOG_DATA_NOT_MATCH_LOOKUP_TYPE = "log.data.not.match.lookup.type";
    public static final String SIGN_ILLEGAL = "sign.illegal";

    public static final String WORKSHOP_CAPACITY_ALREADY_EXISTS = "workshop_capacity_already_exists";
    public static final String URL_EMPTY = "url.empty";
    public static final String ZJ_CUSTOMER_MODEL_NULL = "zj.customer.model.null";
    public static final String SPARE_PART_DETAIL_ERROR = "spare.part.detail.error";
    public static final String SPARE_PART_APPROVE_ERROR = "spare.part.approve.error";
    public static final String SPARE_PART_BILL_LOST = "spare.part.bill.lost";
    public static final String SPARE_PART_STATUS_ERROR = "spare.part.status.error";
    public static final String SPARE_PART_STATUS_ERROR_MSG = "spare.part.status.error.msg";
    public static final String SPARE_PART_PARTNAME_ERROR = "spare.part.partName.error";
    public static final String SPARE_PART_QUANTITY_ERROR = "spare.part.quantity.error";
    public static final String SPARE_PART_PARTNAME_REPETITION = "spare.part.partName.repetition";
    public static final String SPARE_PART_FACTORY_ERROR = "spare.part.factory.error";
    public static final String SPARE_PART_APPROVE_LOST = "spare.part.approve.lost";
    public static final String SPARE_PART_BILL_NO_NOT_NULL = "spare.part.bill.no.not.null";
    public static final String GET_FIXTURE_MODEL_HEAD_ID_NULL = "get.fixture.model.head.id.null";
    public static final String ITEM_DETAIL_CAN_NOT_BE_NUL = "item.detail.can.not.be.empty";
    public static final String NOT_IN_THE_DOCUMENT_DETAILS = "not.in.the.document.details";
    public static final String CLOSE_BILL_DETAIL_ALLOCATION_STATUS_WRONG = "close.bill.detail.allocation.status.wrong";
    public static final String SPARE_PART_ALLOCATION_QUERY_PARAM_NULL = "spare.part.allocation.query.param.null";
    public static final String SN_LIST_OF_CA_CERTIFICATE_NULL = "sn.list.of.ca.certificate.null";
    public static final String SN_LIST_OF_CA_CERTIFICATE_MORE_200 = "sn.list.of.ca.certificate.more.200";
    public static final String SN_CA_BIND_HAVE_SN_INFO = "sn.ca.bind.have.sn.info";
    public static final String SN_CA_CHECK_ERROR = "sn.ca.check.error";
    public static final String SN_CA_CHECK_ERROR_HEAD = "sn.ca.check.error.head";
    public static final String PAGE_PARAMS_OF_CA_QUERY_NULL = "page.params.of.ca.query.null";
    public static final String SN_LIST_OF_CA_QUERY_NULL = "sn.list.of.ca.query.null";
    public static final String BARCODE_NOT_REGISTERED = "barcode.not.registered";
    public static final String DATE_RANGE_REPEAT = "date.range.repeat";

    public static final String DIMENSION_NULL = "dimension_null";
    public static final String PRODUCTION_UNIT_NULL = "production_unit_null";
    public static final String WORK_ORDER_CATEGORY_NULL = "work_order_category_null";
    public static final String PLAN_GROUP_NULL = "plan_group_null";
    public static final String MODEL_NAME_NULL = "model_name_null";
    public static final String UTILIZATION_RATE_NULL = "utilization_rate_null";
    public static final String CAPACITY_DAY_NULL = "capacity_day_null";
    public static final String MODEL_OR_PLAN_GROUP_MORE_THEN_TEN = "model_or_plan_group_more_then_ten";

    public static final String HOLIDAY_PARAMS_ERROR = "holiday.params.error";
    public static final String HOLIDAY_REPEAT = "holiday.repeat";
    public static final String HOLIDAY_PARAMS_DATE_NULL = "holiday.date.null";
    public static final String HOLIDAY_YEAR_ERROR = "holiday.year.error";
    public static final String PSW_IS_WRONG = "pws.is.wrong";
    public static final String FACTORY_ID_IS_SAME = "factory.id.is.same";
    public static final String BATCH_HAS_A_LOCK_ORDER = "batch_has_a_lock_order";

    // 资源预警
    public static final String RESOURCE_WARNING_REQUIRED_ONE = "resource.warning.required.one";
    public static final String RESOURCE_WARNING_RESOURCE_NO_IS_NOT_EMPTY = "resource.warning.resourceNo.notEmpty";
    public static final String RESOURCE_WARNING_DEVICE_TYPE_IS_NOT_EMPTY = "resource.warning.deviceType.notEmpty";

    public static final String RESOURCE_WARNING_RESOURCE_EXIST = "resource.warning.resource.exist";

    public static final String RESOURCE_WARNING_DEVICE_EXIST = "resource.warning.device.exist";
    public static final String RESOURCE_WARNING_RESOURCE_DEVICE_NO_EXIST = "resource.warning.resource.device.noExist";
    public static final String RESOURCE_WARNING_IMPORT_MAX_1000 = "resource.warning.import.max";
    public static final String RESOURCE_WARNING_IMPORT_CHECK_FAIL = "resource.warning.import.check.fail";

    public static final String RESOURCE_WARNING_TYPE_NOT_EMPTY = "resource.warning.type.notEmpty";

    public static final String RESOURCE_WARNING_TYPE_NOT_SUPPORT = "resource.warning.type.notSupport";

    public static final String RESOURCE_WARNING_DEVICE_ISNULL = "resource.warning.device.isNUll";

    public static final String RESOURCE_WARNING_RESOURCE_ISNULL = "resource.warning.resource.isNUll";

    public static final String RESOURCE_WARNING_LOW_WATER_LEVEL_IS_NUMERIC = "resource.warning.water.level.isNumeric";

    public static final String RESOURCE_WARNING_EXPIRY_DATE_IS_NUMERIC = "resource.warning.expiry.date.isNumeric";


    public static final String RESOURCE_WARNING_EXCEL_RESOURCE_IS_SAME = "resource.warning.excel.resource.isSame";


    public static final String RESOURCE_WARNING_EXCEL_DEVICE_IS_SAME = "resource.warning.excel.device.isSame";


    public static final String RESOURCE_WARNING_DATABASE_RESOURCE_TYPE_DEVICE_IS_SAME = "resource.warning.database.resourceType.device.isSame";

    public static final String RESOURCE_WARNING_DATABASE_DEVICE_IS_MULTIPLE = "resource.warning.database.device.isMultiple";
    public static final String RESOURCE_WARNING_DATABASE_DEVICE_TYPE_IS_SAME = "resource.warning.database.device.type.isSame";


    public static final String RESOURCE_WARNING_DATABASE_RESOURCE_SAME_DEVICE_DIFFERENT = "resource.warning.database.resource.same.device.different";
    public static final String RESOURCE_WARNING_DATABASE_RESOURCE_IS_MULTIPLE = "resource.warning.database.resource.isMultiple";

    public static final String RESOURCE_WARNING_DATABASE_RESOURCE_IS_EMPTY = "resource.warning.database.resource.isEmpty";

    public static final String RESOURCE_WARNING_DATABASE_RESOURCE_DEVICE_IS_EMPTY = "resource.warning.database.resource.device.isEmpty";

    public static final String RESOURCE_WARNING_DATABASE_RESOURCE_DEVICE_MISMATCH = "resource.warning.database.resource.device.mismatch";


    public static final String NETWORK_LICENSE_CERT_NAME = "network.license.cert.name";

    public static final String NETWORK_LICENSE_RESOURCE_TYPE = "network.license.resource.type";

    public static final String NETWORK_LICENSE_FILE_NAME_ERROR = "network.license.file.name.error";

    public static final String NETWORK_LICENSE_FILE_MAX = "network.license.file.max";

    public static final String NETWORK_LICENSE_FILE_IMPORT = "network.license.file.import";

    public static final String NETWORK_LICENSE_FILE_ERROR = "network.license.file.error";

    public static final String NETWORK_LICENSE_SIGN_EXITS = "network.license.sign.exits";

    public static final String NETWORK_LICENSE_SIGN_FILE_EXITS = "network.license.sign.file.exits";
    public static final String NETWORK_LICENSE_PRINT_SHORTAGE = "network.license.print.shortage";
    public static final String NETWORK_LICENSE_PRINT_NUM_MAX = "network.license.print.num.max";
    public static final String NETWORK_LICENSE_PRINT_SN_MAX = "network.license.print.sn.max";
    public static final String NETWORK_LICENSE_PRINT_SAME = "network.license.print.same";
    public static final String GET_APS_PLAN_GROUP_AND_MODEL_ERROR = "get.aps.plan.group.and.model.error";
    public static final String GET_APS_PLAN_GROUP_AND_MODEL_ERRORS = "get.aps.plan.group.and.model.errors";
    public static final String MORE_THAN_MAX_NUM = "more.than.max.num";

    public static final String RESOURCE_NO_AVAILABLE_QUANTITY_NOT_ENOUGH = "resource.no.available.quantity.not.enough";

    public static final String CURRENT_RESOURCE_NUMBER_IS_OPERATION = "current_resource_number_is_operation";
    public static final String OPERATION_TIME_CAN_NOT_GREATER_THREE_MONTHS = "operation_time_can_not_greater_three_months";
    public static final String NO_PRODUCT_HAS_PROCESS_CODE = "no.product.has.process.code";
    public static final String PLS_SELECT_PROCESS_CODE = "pls.select.process.code";
    public static final String PROCESS_CODE_CANNOT_BE_N = "process.code.cannot.be.n";
    public static final String DATE_RANGE_MORE_THAN_90_DAYS = "date.range.more.than.90.days";

    public static final String NO_CURRENT_BARCODE_BINDING_RECORD_FOUND = "no_current_barcode_binding_record_found";
    public static final String RESOURCE_USE_NUM_IS_EMPTY = "resource_use_num_is_empty";
    public static final String FAILED_TO_GET_PREVIEW_LINK = "failed_to_get_preview_link";
    public static final String FAILED_TO_GENERATE_PREVIEW_HEADER = "failed_to_generate_preview_header";
    public static final String APPROVAL_COMMENTS_CANNOT_BE_EMPTY_WHEN_REJECTING = "approval_comments_cannot_be_empty_when_rejecting";
    public static final String QUERY_TIME_AND_BILL_NO_IS_NULL = "query.time.and.billNo.is.null";
    public static final String TIME_INTERVAL_MORE_THAN_HALF_YEAR = "time_interval_more_than_half_year";
    public static final String LAST_TIME_INTERVAL_MORE_THAN_HALF_YEAR = "last_time_interval_more_than_half_year";
    public static final String THE_PERSON_TO_BE_HANDED_OVER_TO_CAN_NOT_BE_NULL = "the_person_to_be_handed_over_to_can_not_be_null";
    public static final String THE_APPROVAL_TYPE_IS_INCORRECT = "the_approval_type_is_incorrect";
    public static final String FILE_TYPE_ILLEGAL = "file.type.illegal";
    public static final String FAIL_TO_UPLOAD_FILE = "fail_to_upload_file";
    public static final String BILL_INFO_INPUT_PARAM_EMPTY = "bill_info_input_param_empty";
    public static final String PLEASE_MAINTAIN_AT_LEAST_ONE = "please_maintain_at_least_one";
    public static final String INPUT_SN_IS_NULL_OR_DUPLICATE = "input_sn_is_null_or_duplicate";
    public static final String TYPE_OR_DEC_OF_SN_IS_NULL = "type_or_dec_of_sn_is_null";
    public static final String QTY_OF_BARCODE_CAN_NOT_BE_NULL = "qty.of.barcode.can.not.be.null";
    public static final String NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER = "no.corresponding.documents.obtained.in.the.approval.center";
    public static final String EXTERNAL_TYPE_IS_NULL = "external.type.is.null";
    public static final String STYLE_IS_NULL = "style.is.null";
    public static final String SINGLE_PAGE_QUERY_CANNOT_EXCEED_500_ENTRIES = "single.page.query.cannot.exceed.500.entries";
    public static final String QUERY_LAST_TIME_AND_BILL_NO_IS_NULL = "query.last.time.and.billNo.is.null";
    public static final String RESOURCE_USE_NO_IS_DIFF = "resource_use_no_is_diff";
    public static final String RESOURCE_USE_STATUS_NOT_ALLOW_IMPORT = "resource_use_status_not_allow_import";
    public static final String RESOURCE_USE_NUM_EXIST_SAME = "resource_use_num_exist_same";
    public static final String RESOURCE_USE_BARCODE_NO_SAME = "resource_use_barcode_no_same";
    public static final String RESOURCE_USE_BARCODE_TYPE_IS_DIFF = "resource_use_barcode_type_is_diff";
    public static final String RESOURCE_USE_DATA_OPTION_ERROR = "resource_use_data_option_error";
    public static final String RESOURCE_USE_IMPORT_ERROR = "resource_use_import_error";

    public static final String MAX_EXPORT_IS_ONCE = "max.export.is.once";

    public static final String EXCEED_MAX_EXPORT_COUNT = "exceed.max.export.count";

    public static final String OPERATION_TIME_CAN_NOT_GREATER_ONE_MONTHS = "operation_time_can_not_greater_ont_months";

    public static final String OPERATION_TIME_CAN_NOT = "operation_time_can_not";


    public static final String RESOURCE_USE_BARCODE_TYPE_NOT_CORRECT = "resource_use_barcode_type_not_correct";

    public static final String RESOURCE_USE_NO_NOT_CORRECT = "resource_use_no_is_empty_or_status_error";

    public static final String RESOURCE_USE_IMPORT_FILE_IS_EMPTY = "resource_use_import_file_is_empty";
    public static final String SCRAP_NUM_BIGGER_THAN_DATA_NUM = "scrap.num.bigger.than.data.num";
    public static final String SCRAP_NUM_OVER_LIMIT = "scrap.num.over.limit";
    public static final String QUERY_RESOURCE_SCRAP_PARAMS_ERROR = "query.resource.scrap.params.error";
    public static final String QUERY_RESOURCE_SCRAP_DATE_RANGE_OVER_YEAR = "query.resource.scrap.date.range.over.year";
    public static final String CF_USER_MENU_MAX_COUNT = "cf.user.menu.max.count";

    public static final String OPERATION_IN_PROGRESS_PLEASE_WAIT = "operation_in_progress_please_wait";

    public static final String FAILED_TO_CALL_B2B_INTERFACE = "failed_to_call_b2b_interface";

    public static final String AI_QUESTION_NOT_FOUND = "ai_question_not_found";

    public static final String PRODUCT_CODE_NOT_SAME_ERROR = "product.code.not.same.error";
    public static final String PRODUCT_CODE_LENGTH_ERROR = "product.code.length.error";
    public static final String MDS_IN_PROGRAM_ERROR = "mds.in.program.error";
    public static final String PRODUCT_CODE_NUM_OVER_TEN = "product.code.num.over.ten";
    public static final String CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED = "call.barCode.center.expandQuery.barCode.falied";
    public static final String CLOUD_DISK_INPUT_ERR = "cloud.disk.input.err";
    public static final String PRODUCT_CODE_IS_NOT_IMPORT_CAD_FILE = "productCode.is.not.import.cdaFile";

    public static final String DATA_DELETE_REFRESH_PAGE = "data.delete.refresh.page";
    public static final String CUSTOMER_PARAM_TABLE_ERROR = "customer.param.table.error";
    // 中试接口异常
    public static final String DQAS_ERROR = "dqas.error";
    // MDS触发写片信息同步失败：料单:{0}
    public static final String XP_AUTO_SYNC_FAILED_TITLE_ONE = "xp.auto.sync.failed.title.one";
    // 写片前加工自动维护失败：料单:{0},单板名称:{1},PCB版本:{2},请确认并进行人工维护
    public static final String XP_AUTO_SYNC_FAILED_TITLE_TWO = "xp.auto.sync.failed.title.two";
    // 写片前加工自动维护失败：料单:{0},单板名称:{1},PCB版本:{2},请确认并进行人工维护,并执行BOM分阶
    public static final String XP_AUTO_SYNC_FAILED_TITLE_THREE = "xp.auto.sync.failed.title.three";
    // {0}料单前加工写片数据在UTS或PCB升级但研发未启用
    public static final String XP_AUTO_SYNC_FAILED_MSG_ONE = "xp.auto.sync.failed.msg.one";
    // {0}料单前加工写片未启用或数据在UTS
    public static final String XP_AUTO_SYNC_FAILED_MSG_TWO = "xp.auto.sync.failed.msg.two";
    // {0}料单在MDS无写片信息
    public static final String XP_AUTO_SYNC_FAILED_MSG_THREE = "xp.auto.sync.failed.msg.three";
    // {0}料单未查到位号信息,请确认是否执行BOM位号解析后重新操作
    public static final String XP_AUTO_SYNC_FAILED_MSG_FOUR = "xp.auto.sync.failed.msg.four";

    public static final String XP_AUTO_SYNC_FAILED_MSG = "xp.auto.sync.failed.msg";
    public static final String PCBVERSION_NOT_EXIST = "pcbversion.not.exist";
    public static final String PARAMS_LACKING = "params.lacking";
    public static final String RESOURCE_NUM_MAX_ONE_HUNDRED = "resource.num.max.one.hundred";

    public static final String RELATIONSHIP_VERIFY_NOT_PASS = "relationship.verify.not.pass";
    public static final String CHANGE_ORDER_NO_DATA_NULL = "change.order.no.data.null";
    public static final String NOT_FOUND_ITEM_INFO = "not.found.item.info";

    public static final String THE_MODEL_CODE_OF_THE_RESOURCE_NUMBER_IS_EMPTY = "the_model_code_of_the_resource_number_is_empty";

    public static final String PLEASE_ENTER_THE_QUESTION_CONTENT = "Please_enter_the_question_content";
    public static final String BOM_ITEM_USE_ACCOUNT = "bom.item.use.account";
    public static final String BOM_EXCEPTION_MESSAGE = "bom.exception.message";
    public static final String PLEASE_INPUT_ONE_CONDITION = "please.input.one.condition";

    public static final String THE_MODEL_CODE_IS_EMPTY = "the_model_code_is_empty";

    public static final String RESOURCE_NO_RULE_INVALID = "resource.no.rule.invalid";

    public static final String RESOURCE_STEP_IS_DIFFERENT = "resource.step.is.different";

    public static final String PARAMS_RULE_ERROR = "params.rule.error" ;

    public static final String NO_MAC_AVAILABLE_RESOURCE = "no.mac.available.resource" ;

    public static final String NO_GPON_SN_AVAILABLE_RESOURCE = "no.gpon.sn.available.resource" ;

    public static final String NO_AVAILABLE_RESOURCE = "no.available.resource" ;

    public static final String RANDOM_NUMBERS_MUST_GREATER_THAN_OR_EQUAL_3 = "random.numbers.must.greater.than.or.equal.3" ;

    public static final String ITEM_NO_IS_INVALID = "item.no.is.invalid" ;

    public static final String TASK_IS_GENERATING = "task.is.generating" ;

    public static final String APPLY_QTY_IS_EXCEED_MAX = "apply.qty.is.exceed.max" ;

    public static final String EMP_NO_HAS_UNDONE_TASK = "emp.no.has.undone.task" ;

    // 料单对应AOI图片不存在
    public static final String AOI_IMG_NOT_EXIST = "aoi.img.not.exist" ;

    public static final String EIGEN_VALUE_EXCEED_MAX = "eigen.value.exceed.max" ;
    public static final String UCS_LOGIN_ERROR = "ucs.login.error";
    public static final String SUB_CUSTOMER_CONFIG_LOST = "sub.customer.config.lost" ;
    public static final String APS_TASK_NO_IS_NULL = "aps.task.no.is.null";
    public static final String TASK_NO_INFO_IS_NULL = "task.no.info.is.null";
    public static final String APS_TASK_NO_INFO_FACTORY_ID_IS_NULL = "aps.task.no.info.factory.id.is.null";
    public static final String APS_DERIVATIVECODE_NO_INFO_FACTORY_ID_IS_NULL = "kafka.msg.save.database.derivativeCode.update";
    public static final String TASK_NO_INFO_NOT_EXIST = "task.no.info.not.exist";
    public static final String TASK_NO_TRANSFERRING = "task.no.transferring";
    public static final String NO_DERIVATIVE_CODE_CHANGE_INFORMATION_FOUND = "no_derivative_code_change_information_found";

    public static final String ORIGINAL_BOM_INFORMATION_NOT_QUERIED = "original_bom_information_not_queried";

    public static final String THIS_TASK_VERSION_HAS_ALREADY_BEEN_PROCESSED = "this_task_version_has_already_been_processed";


    public static final String WRITE_BACK_ERP_ERROR = "write.back.erp.error";
    public static final String CONSENT_AVAILABLE_QUANTITY_IS_INSUFFICIENT = "consent.available.quantity.is.insufficient";

    public static final String NET_WORK_PARAMS_MUST_EXISTING_SIMULTANEOUSLY  = "net.work.params.must.existing.simultaneously";
    public static final String USER_NO_PERMISSIONS   = "user.no.permissions";

    public static final String FAILED_TO_OBTAIN_UCS_PUBLIC_KEY   = "failed_to_obtain_ucs_public_key";
    public static final String PARAM_ZTE_CODE_NOT_BLANK   = "param_zte_code_not_blank";

    public static final String PACK_LIST_IS_EXISTED  = "pack_list_already_existed";
    public static final String CPQD_INSTANCE_NO_LOST   = "cpqd.instance.no.lost";
    public static final String ICC_MBOM_LOST   = "icc.mbom.lost";
    public static final String PDM_MBOM_LOST   = "pdm.mbom.lost";
    public static final String PDM_MBOM_ITEM_ICC_MBOM_LOST = "pdm.mbom.item.icc.mbom.lost";
    public static final String TASK_FIX_BOM_ERROR   = "task.fix.bom.error";
    public static final String TASK_FIX_BOM_SUBJECT   = "task.fix.bom.subject";
    public static final String TASK_LIST_NOT_BLANK = "task.list.not.blank";
    public static final String FIX_BOM_DETAIL_LOST = "fix.bom.detail.lost";
    public static final String FIX_BOM_INCOMPLETE = "fix.bom.incomplete";
    public static final String SPLIT_NOT_ALLOWED = "split.not.allowed";
    public static final String CUSTOMER_LOOKUPTYPE_CONFIG_LOST = "customer.to.lookupType.config.lost";

    public static final String VARIABLE_DOES_NOT_EXIST  = "variable.does.not.exist";
    /**
     * 必须选择反馈时间
     */
    public static final String DELIVERY_FEEDBACK_IS_MUST = "delivery.feedback.is.must";
    /**
     * 最大查询100个任务号
     */
    public static final String MAX_SEARCH_TASKS = "max.search.tasks";

    /**
     * 数据量不能超过1000条
     */
    public static final String SN_DATA_SIZE_OVER = "sn.data.size.over";

    /**
     * 任务号重复
     */
    public static final String TASK_NO_REPEAT = "task.no.repeat";
    /**
     * 延期编号不能重复
     */
    public static final String ABNORMAL_NO_REPEAT = "abnormal.no.repeat";
    /**
     * 一级原因分类不正确
     */
    public static final String ABNORMAL_CATEGORY_FIRST_ERROR = "abnormal.category.first.error";
    /**
     * 二级原因分类不正确
     */
    public static final String ABNORMAL_CATEGORY_SECOND_ERROR = "abnormal.category.second.error";
    /**
     * 三级原因分类不正确
     */
    public static final String ABNORMAL_CATEGORY_THIRD_ERROR = "abnormal.category.third.error";
    /**
     * 实际投产日期需晚于全部物料实际齐套日期
     */
    public static final String TIMEPRODUCTION_NEED_LATER_THAN_DATEALLMATERIALPREPARED = "timeproduction.need.later.than.dateallmaterialprepared";

    /**
     * 实际投产日期需晚于厂商自供料实际齐套日期
     */
    public static final String TIMEPRODUCTION_NEED_LATER_THAN_TIMEMATERIALPREPARED = "timeproduction.need.later.than.timematerialprepared";

    /**
     * 实际投产日期需晚于首次领料日期
     */
    public static final String TIMEPRODUCTION_NEED_LATER_THAN_FIRSTPICKINGDATE = "timeproduction.need.later.than.firstpickingdate";

    /**
     * 提交失败加原因
     */
    public static final String SUBMIT_FAILED = "submit.failed";
    /**
     * 任务号不能为空
     */
    public static final String TASK_NO_NOT_EMPTY = "task.no.not.empty";
    /**
     * 交期相关日期不能为空
     */
    public static final String DELIVERY_FEEDBACK_DATE_NOT_EMPTY = "delivery.feedback.date.not.empty";
    /**
     * 任务号状态必须为已开工、已排产和已发放
     */
    public static final String TASK_NO_STATUS = "task.no.status";
    /**
     * 任务号延期，需要必填延期原因等
     */
    public static final String TASK_DELAYED_REASON_NOT_EMPTY = "task.delayed.reason.not.empty";
    /**
     * 必须是阿里任务
     */
    public static final String CUSTOM_NO_NOT_ALIBABA = "custom.no.not.alibaba";

    /**
     * 生产交期反馈邮件配置目录代码：{0}，没有维护接收人
     */
    public static final String DELIVERY_FEEDBACK_NO_RECEIVER = "delivery.feedback.no.receiver";
    /**
     * 没有维护客户编码与名称的对应关系
     */
    public static final String CUSTOMER_CODE_NAME_NOT_MATCH = "customer.code.name.not.match";
    /**
     * 任务号:{0}找不到对应的生产交期反馈记录
     */
    public static final String TASK_NO_NOT_MATCH_RECORD = "task.no.not.match.record";
    public static final String GET_URL_NULL = "get.url.null";
    public static final String ERPSTOCK_NULL = "erpstock.null";
    public static final String ZTE_CODE_FIXBOMID_IS_NULL = "zte.code.fixbomid.is.null";

    public static final String FAILED_TO_CALL_MDS_INTERFACE = "failed_to_call_mds_interface";
    public static final String REQUIRED_TASK_NO_OR_FIX_BOM_ID = "required.task.no.or.fix.bom.id";
    public static final String SN_PUSH_STD_MODEL_LOST = "sn.push.std.model.lost";

    public static final String STBIDTYPE_NUM_OVERONE = "stbidtype.num.overone";
    public static final String STBIDTYPE_NOAUTO = "stbidtype.noauto";
    public static final String STBIDTYPE_NOSTBIDPREFIX = "stbidtype.nostbidprefix";
    public static final String STBIDTYPE_STBIDCFG_ERROR = "stbidtype.stbidcfg.error";
    public static final String STBIDTYPE_STBIDTEMPLATE_ERROR = "stbidtype.stbidtemplate.error";

    public static final String GENERATE_TASK_NO_REDIS_KEY_ERROR = "generate.task.no.redis.key.error";
    public static final String SYNCMDS_FMTYPE_ERROR = "syncmds.fmtype.error";
    public static final String SYNCMDS_FMTYPE_REPEATOPERATION_ERROR = "syncmds.fmtype.repeatoperation.error";
    public static final String SYNCMDS_SPECIALITYPARAMID_EMPTY_ERROR = "syncmds.specialityparamid.empty.error";
    public static final String SYNCMDS_SPECIALITYPARAMID_REPEAT_ERROR = "syncmds.specialityparamid.repeat.error";
    public static final String SPECIALITY_RESOURCE_ABNORMALITY = "speciality.resource.abnormality";

    public static final String NO_MATCH_SN = "no.match.sn";
    public static final String CF_API_RESOURCE_EMPTY = "cf.api.resource.empty";
    public static final String BAGGAGE_PARAM_LOST = "baggage.param.lost";
    public static final String BAGGAGE_PARAM_TIME_OVER = "baggage.param.time.over";
    public static final String ROW_COL_READ_ERROR = "row.col.read.error";

    // 客户物料查询相关错误信息
    public static final String CUSTOMER_NAME_NULL = "CUSTOMER_NAME_NULL";
    public static final String COOPERATION_MODE_NULL = "COOPERATION_MODE_NULL";
}

