package com.zte.springbootframe.config;

import com.zte.itp.msa.datasource.DynamicDataSource;
import com.zte.itp.msa.datasource.MsaDataSourceFactory;
import com.zte.itp.msa.datasource.config.MsaDatasourceConfig;
import com.zte.itp.msa.datasource.config.MsaDatasourceProperties;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
@PropertySource("classpath:datasource.properties")
@MapperScan(basePackages={"com.zte.domain"})
public class MesDatasourceConfig extends MsaDatasourceConfig {

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource dataSource1(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc1.url"));
        props.put("username", env.getProperty("jdbc1.username"));
        props.put("password", env.getProperty("jdbc1.password"));
        props.put("driverClassName", env.getProperty("jdbc1.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource dataSource2(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc2.url"));
        props.put("username", env.getProperty("jdbc2.username"));
        props.put("password", env.getProperty("jdbc2.password"));
        props.put("driverClassName", env.getProperty("jdbc2.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource SFCDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc3.url"));
        props.put("username", env.getProperty("jdbc3.username"));
        props.put("password", env.getProperty("jdbc3.password"));
        props.put("driverClassName", env.getProperty("jdbc3.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource MESDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc4.url"));
        props.put("username", env.getProperty("jdbc4.username"));
        props.put("password", env.getProperty("jdbc4.password"));
        props.put("driverClassName", env.getProperty("jdbc4.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource MES_EAIDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc5.url"));
        props.put("username", env.getProperty("jdbc5.username"));
        props.put("password", env.getProperty("jdbc5.password"));
        props.put("driverClassName", env.getProperty("jdbc5.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource WMESDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbcWmes.url"));
        props.put("username", env.getProperty("jdbcWmes.username"));
        props.put("password", env.getProperty("jdbcWmes.password"));
        props.put("driverClassName", env.getProperty("jdbcWmes.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource WMSPRODLMSDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc.applms.url"));
        props.put("username", env.getProperty("jdbc.applms.username"));
        props.put("password", env.getProperty("jdbc.applms.password"));
        props.put("driverClassName", env.getProperty("jdbc.applms.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource inforDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc.infor.url"));
        props.put("username", env.getProperty("jdbc.infor.username"));
        props.put("password", env.getProperty("jdbc.infor.password"));
        props.put("driverClassName", env.getProperty("jdbc.infor.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource sfcNewDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc.sfc.url"));
        props.put("username", env.getProperty("jdbc.sfc.username"));
        props.put("password", env.getProperty("jdbc.sfc.password"));
        props.put("driverClassName", env.getProperty("jdbc.sfc.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource erpDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbcerp.url"));
        props.put("username", env.getProperty("jdbcerp.username"));
        props.put("password", env.getProperty("jdbcerp.password"));
        props.put("driverClassName", env.getProperty("jdbcerp.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource erpDataSource1(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbcerp1.url"));
        props.put("username", env.getProperty("jdbcerp1.username"));
        props.put("password", env.getProperty("jdbcerp1.password"));
        props.put("driverClassName", env.getProperty("jdbcerp1.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    /**
     * 创建数据源(数据源的名称：方法名可以取为XXXDataSource(),XXX为数据库名称,该名称也就是数据源的名称)
     */
    @Bean
    public DataSource barcodeDataSource(MsaDatasourceProperties msaDatasourceProperties) throws Exception {
        Properties props = new Properties();
        this.setCommonJDBCProperties(props, msaDatasourceProperties);
        props.put("url", env.getProperty("jdbc.barcode.url"));
        props.put("username", env.getProperty("jdbc.barcode.username"));
        props.put("password", env.getProperty("jdbc.barcode.password"));
        props.put("driverClassName", env.getProperty("jdbc.barcode.driverClassName"));
        DataSource dataSource = MsaDataSourceFactory.createDataSource(props);
        setProxyFilters(dataSource);
        return dataSource;
    }

    @Bean("msaDataSource")
    @Primary
    public DynamicDataSource msaDataSource(@Qualifier("dataSource1") DataSource dataSource1,
                                           @Qualifier("dataSource2") DataSource dataSource2,
                                           @Qualifier("SFCDataSource") DataSource SFCDataSource,
                                           @Qualifier("MESDataSource") DataSource MESDataSource,
                                           @Qualifier("WMESDataSource") DataSource WMESDataSource,
                                           @Qualifier("MES_EAIDataSource") DataSource MES_EAIDataSource,
                                           @Qualifier("WMSPRODLMSDataSource") DataSource WMSPRODLMSDDataSource,
                                           @Qualifier("inforDataSource") DataSource inforDataSource,
                                           @Qualifier("sfcNewDataSource") DataSource sfcNewDataSource,
                                           @Qualifier("erpDataSource") DataSource erpDataSource,
                                           @Qualifier("erpDataSource1") DataSource erpDataSource1,
                                           @Qualifier("barcodeDataSource") DataSource barcodeDataSource) {

        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DatabaseType.DB1, dataSource1);
        targetDataSources.put(DatabaseType.DB2, dataSource2);
        targetDataSources.put(DatabaseType.SFC, SFCDataSource);
        targetDataSources.put(DatabaseType.MES, MESDataSource);
        targetDataSources.put(DatabaseType.WMES, WMESDataSource);
        targetDataSources.put(DatabaseType.MES_EAI, MES_EAIDataSource);
        targetDataSources.put(DatabaseType.WMSPRODLMS, WMSPRODLMSDDataSource);
        targetDataSources.put(DatabaseType.INFOR, inforDataSource);
        targetDataSources.put(DatabaseType.SFCNEW, sfcNewDataSource);
        targetDataSources.put(DatabaseType.ERP, erpDataSource);
        targetDataSources.put(DatabaseType.ERP1, erpDataSource1);
        targetDataSources.put(DatabaseType.BARCODE, barcodeDataSource);
        DynamicDataSource dataSource = new DynamicDataSource();
        dataSource.setTargetDataSources(targetDataSources);
        dataSource.setDefaultTargetDataSource(dataSource1);
        return dataSource;
    }


    /**
     * 配置事务管理器
     */
    @Bean
    public DataSourceTransactionManager transactionManager(DynamicDataSource dataSource) throws Exception {
        return new DataSourceTransactionManager(dataSource);
    }


}
