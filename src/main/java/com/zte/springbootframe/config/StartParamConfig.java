package com.zte.springbootframe.config;

import com.zte.aiop.dtems.entity.ConfigObject;
import com.zte.aiop.dtems.utils.InitUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "dtems")
@Component
public class StartParamConfig implements CommandLineRunner {

    private ConfigObject agent;

    public ConfigObject getAgent() {
        return agent;
    }

    public void setAgent(ConfigObject agent) {
        this.agent = agent;
    }

    @Override
    public void run(String... strings){
        InitUtils.getInstance(agent);
    }

}
