package com.zte.springbootframe.transaction;

import org.springframework.stereotype.Component;

@Component
public interface SpringBeanFactory {
	/**
	 * 根据类名获取bean实例,会优先根据类型去查找查找失败后再根据类的名称进行查找
	 * @param clazz
	 * @return
	 */
	public <T> T getBean(Class<T> clazz);
	
	/**
	 * 根据beanId和类名获取bean实例
	 * @param beanId
	 * @param clazz
	 * @return
	 */
	public <T> T getBean(String beanId, Class<T> clazz);
}
