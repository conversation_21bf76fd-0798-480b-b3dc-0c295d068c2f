package com.zte.springbootframe.transaction;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 10266925
 */
@Component
public class SpringBeanFactoryImpl implements SpringBeanFactory, BeanFactoryAware
{
    private BeanFactory beanFactory;

    @Override
    public <T> T getBean(Class<T> clazz) {
        T result = beanFactory.getBean(clazz);
        if(result == null) {
            String beanId = this.getBeanName(clazz, null);
            result = beanFactory.getBean(beanId, clazz);
        }
        return result;
    }

    @Override
    public <T> T getBean(String beanId, Class<T> clazz) {
        beanId = this.getBeanName(clazz, beanId);
        return beanFactory.getBean(beanId, clazz);
    }

    private <T> String getBeanName(final Class<T> clazz, final String serviceName) {
        String beanName = serviceName;
        if (StringUtils.isBlank(beanName)) {
            beanName = clazz.getSimpleName();
            // 首字母小写
            beanName = beanName.substring(0, 1).toLowerCase() + beanName.substring(1);
        }
        return beanName;
    }

    
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException
    {
        this.beanFactory = beanFactory;
    }
}
