package com.zte.springbootframe.transaction;

import javax.sql.DataSource;

import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.zte.common.utils.Constant;
import com.zte.itp.msa.datasource.DatabaseContextHolder;

/**
 * 用于辅助多数据源事务时，手动控制各数据源事务
 * <AUTHOR>
 */
public class TransactionManager{
    private DataSourceTransactionManager transactionManager;
    private TransactionStatus status;
    private DataSource dataSource;
    
    public TransactionManager(String dataSourceType, SpringBeanFactory springBeanFactory) {
        if(dataSourceType != null) {
            DatabaseContextHolder.setDatabaseType(dataSourceType);
        }
        dataSource = springBeanFactory.getBean(Constant.MSA_DATA_SOURCE, DataSource.class);
        transactionManager = new DataSourceTransactionManager(dataSource);
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
        status = transactionManager.getTransaction(definition);
    }

    public DataSourceTransactionManager getTransactionManager()
    {
        return transactionManager;
    }

    public void setTransactionManager(DataSourceTransactionManager transactionManager)
    {
        this.transactionManager = transactionManager;
    }

    public TransactionStatus getStatus()
    {
        return status;
    }

    public void setStatus(TransactionStatus status)
    {
        this.status = status;
    }
    
    public void commit() {
        transactionManager.commit(status);
    }
    
    public void rollback() {
        transactionManager.rollback(status);
    }
}
