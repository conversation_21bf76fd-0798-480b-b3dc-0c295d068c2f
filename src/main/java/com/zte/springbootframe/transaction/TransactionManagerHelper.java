package com.zte.springbootframe.transaction;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TransactionManagerHelper
{
    private List<TransactionManager> managerList = new ArrayList<TransactionManager>();

    /**
     * 创建新事务，数据源切换前应开始新的事务
     * @param dataSourceType
     * @param springBeanFactory
     * @return
     */
    public TransactionManager createTransactionManager(String dataSourceType, SpringBeanFactory springBeanFactory) {
        TransactionManager manager = new TransactionManager(dataSourceType, springBeanFactory);
        managerList.add(manager);
        return manager;
    }


    public List<TransactionManager> getManagerList()
    {
        return managerList;
    }


    public void setManagerList(List<TransactionManager> managerList)
    {
        this.managerList = managerList;
    }
    
    /**
     * 统一提交
     */
    public void commit() {
        if(managerList.size() > 0) {
            for(int i = managerList.size() - 1; i >= 0;  i--) {
                TransactionManager manager = managerList.get(i);
                manager.commit();
            }
        }
    }
    
    /**
     * 统一回滚
     */
    public void rollback() {
        if(managerList.size() > 0) {
            for(int i = managerList.size() - 1; i >= 0;  i--) {
                TransactionManager manager = managerList.get(i);
                manager.rollback();
            }
        }
    }
}
