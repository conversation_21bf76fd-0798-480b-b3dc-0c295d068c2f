package com.zte.springbootframe.listener;

import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

/**
 * Session监听器,实现原理:采取redis pub/sub功能来实现的。
 * 见RedisOperationsSessionRepository源码onMessage
 * 注意点:
 * sessionDestroyed采取的是Redis订阅发布功能,只接受当前连接上去的redis主节点发布,故使用redisCluster的情况下
 * 有可能会收不到发布消息功能。解决方式(3选1):
 * 1:主主之间数据同步，需要自己实现
 * 2:自己实现轮询所有节点的session key,自实现pub/sub捕捉session销毁功能
 * 3:干脆使用单节点redis,就别使用redisCluster了。
 * 
 * 特别注意：
 * 1:如果本类的类名或包名发生变化，则需要在application.yml或配置中心增加配置 spring.session.listener 指向 新的类名。
 * 目前该配置的默认值为：com.zte.springbootframe.listener.SessionListener
 * <AUTHOR>
 */
public class SessionListener implements HttpSessionListener
{
    
    public void sessionCreated(HttpSessionEvent sessionEvent)
    {
        // TODO Auto-generated method stub

    }
    
    
    public void sessionDestroyed(HttpSessionEvent sessionEvent)
    {
        // TODO Auto-generated method stub
        //@TODO,判断是当前服务如果是"用户服务"就记录离线日志以及相关业务处理,注意需要防止重复进入
        //当一个session过期时，那么我们的listener在所有微服务实例都会收到事件通知。会导致重复，故需要注意这点。

    }
}