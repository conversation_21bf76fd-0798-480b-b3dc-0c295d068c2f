package com.zte.springbootframe.util.json;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.text.Normalizer;

/**
 * 提供json工具类,对象和String互转
 * <AUTHOR>
 *
 */
public class JacksonJsonConverUtil {
	private static final Logger logger = LoggerFactory.getLogger(JacksonJsonConverUtil.class);
	private static ObjectMapper mapper;

	public static synchronized ObjectMapper getMapperInstance() {
		if (mapper == null) {
			mapper = new ObjectMapper();
		}
		return mapper;
	}

	public static String beanToJson(Object obj) throws RouteException {
		String json = null;
		try {
			ObjectMapper objectMapper = getMapperInstance();
			json = objectMapper.writeValueAsString(obj);
		} catch (Exception e) {
			String errorMsg = "Class beanToJson faild";
			throwsRouteException(errorMsg, e, "BEAN_TO_JSON_FAILD");
		}
		return json;
	}

	public static Object jsonToBean(String json, Class<?> cls) throws RouteException {
		Object vo = null;
		try {
			ObjectMapper objectMapper = getMapperInstance();

			vo = objectMapper.readValue(Normalizer.normalize(json, Normalizer.Form.NFKC), cls);
		} catch (Exception e) {
			String errorMsg = cls + " JsonTobean faild:" + e.getMessage();
			throwsRouteException(errorMsg, e, "JSON_TO_BEAN_FAILD");
		}
		return vo;
	}

	public static <T> T jsonToListBean(String json, TypeReference<T> valueTypeRef) {
		try {
			ObjectMapper objectMapper = getMapperInstance();

			return objectMapper.readValue(Normalizer.normalize(json, Normalizer.Form.NFKC), valueTypeRef);
		} catch (Exception e) {
			String errorMsg = " JsonTobean faild:" + e.getMessage();
			logger.error(errorMsg);
		}
		return null;
	}

	private static void throwsRouteException(String errorMsg, Exception e, String errorCode) throws RouteException {
		String msg = errorMsg + ".errorMsg:" + e.getMessage();
		logger.error(msg);
		throw new RouteException(errorMsg, errorCode);
	}

}
