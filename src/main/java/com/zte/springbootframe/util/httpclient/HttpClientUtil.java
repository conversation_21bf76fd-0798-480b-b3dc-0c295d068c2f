package com.zte.springbootframe.util.httpclient;

import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * HttpClient工具类，含get,post,put,delete方法
 *
 * <AUTHOR>
 *
 */
public class HttpClientUtil
{
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    public static final String HTTP_POST_CONSTRUCT_ERR = "HTTP_POST_CONSTRUCT_ERR";
    public static final String HTTP_PUT_CONSTRUCT_ERR = "HTTP_PUT_CONSTRUCT_ERR";
    public static final String HTTP_GET_CONSTRUCT_ERR = "HTTP_GET_CONSTRUCT_ERR";
    public static final String DATA_FORMAT_ERROR = "DATA_FORMAT_ERROR";
    public static final String SERVICE_POST_ERR = "SERVICE_POST_ERR";
    public static final String POST_CONNECT_FAILD = "POST_CONNECT_FAILD";
    public static final String CLOSE_CONNECT_FAILD = "CLOSE_CONNECT_FAILD";
    public static final String SERVICE_DELETE_ERR = "SERVICE_DELETE_ERR";
    public static final String SERVICE_GET_ERR = "SERVICE_GET_ERR";
    public static final String DELETE_CONNECT_FAILD = "DELETE_CONNECT_FAILD";
    public static final String GET_CONNECT_FAILD = "GET_CONNECT_FAILD";
    public static final String SERVICE_PUT_ERR = "SERVICE_PUT_ERR";

    private static final String urlErrMsg = "url can't be empty";

    /**
     * post方式请求 ，参数放RequestBody里面
     *
     * @param url
     *            访问url地址
     * @param params
     *            post参数
     * @param headerParamsMap
     *            传入header参数
     * @return
     * @throws RouteException
     * <AUTHOR>
     */
    public static String httpPostWithJSON(String url, String params, Map<String, String> headerParamsMap)
            throws RouteException
    {
        // 数据必填项校验
        if (StringUtils.isBlank(url))
        {
            throw new RouteException(urlErrMsg, DATA_FORMAT_ERROR);
        }
        String result = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = null;
        try{
            httpPost = new HttpPost(new URIBuilder(Normalizer.normalize(url, Normalizer.Form.NFKC)).toString());
        }catch(Exception e ){}
        if(null == httpPost) {
            String msg = "construct HttpPost failed!";
            throw new RouteException(msg, HTTP_POST_CONSTRUCT_ERR);
        }
        httpPost.addHeader("Content-type", "application/json; charset=utf-8");
        httpPost.setHeader("Accept", "application/json; charset=utf-8");
        // 传入的header参数
        if (null != headerParamsMap)
        {
            for (Map.Entry<String, String> entry : headerParamsMap.entrySet())
            {
                String key = entry.getKey();
                String value = entry.getValue();
                httpPost.setHeader(key, value);
            }
        }

        httpPost.setEntity(new StringEntity(params, Charset.forName("UTF-8")));
        try
        {
            CloseableHttpResponse res = httpClient.execute(httpPost);

            result = EntityUtils.toString(res.getEntity());
            if (res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED && res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED_ONE)
            {
                throw new RouteException(result, SERVICE_POST_ERR);
            }
            res.close();
        }
        catch (IOException e)
        {
            String errorMsg = url + ":httpPostWithJSON connect faild";
            throwsRouteException(errorMsg, e, POST_CONNECT_FAILD);
        }
        finally
        {
            try
            {
                httpClient.close();
            }
            catch (IOException e)
            {
                String errorMsg = url + ":close  httpClient faild";
                throwsRouteException(errorMsg, e, CLOSE_CONNECT_FAILD);
            }
        }

        return result;
    }

    /**
     * delete方式请求,以?参数带过去，不是放RequestBody里面
     *
     * @param url
     * @param paramsMap
     * @param headerParamsMap
     *            传入的header参数
     * @return
     * @throws RouteException
     * <AUTHOR>
     */
    public static String httpDelete(String url, Map<String, String> paramsMap,
            Map<String, String> headerParamsMap) throws RouteException
    {
        // 数据必填项校验
        if (StringUtils.isBlank(url))
        {
            throw new RouteException(urlErrMsg, DATA_FORMAT_ERROR);
        }
        String result = null;
        String baseUrl;
        if (paramsMap != null)
        {
            List params = new ArrayList();
            for (Map.Entry<String, String> entry : paramsMap.entrySet())
            {
                String key = entry.getKey();
                String value = entry.getValue();
                params.add(new BasicNameValuePair(key, value));
            }
            baseUrl = url + "?" + URLEncodedUtils.format(params, "UTF-8");
        }
        else
        {
            baseUrl = url;
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();
        try
        {
            HttpDelete httpDelete = new HttpDelete(Normalizer.normalize(baseUrl, Normalizer.Form.NFKC));
            httpDelete.addHeader("Content-type", "application/json; charset=utf-8");
            httpDelete.setHeader("Accept", "application/json");
            // 传入的header参数
            if (headerParamsMap != null)
            {
                for (Map.Entry<String, String> entry : headerParamsMap.entrySet())
                {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    httpDelete.setHeader(key, value);
                }
            }

            CloseableHttpResponse res = httpClient.execute(httpDelete);
            if (null != res.getEntity())
            {
                result = EntityUtils.toString(res.getEntity());
            }
            if (res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED && res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED_FOUR)
            {
                throw new RouteException(EntityUtils.toString(res.getEntity()), SERVICE_DELETE_ERR);
            }

            res.close();
        }
        catch (IOException e)
        {
            String errorMsg = baseUrl + ":delete connect faild";
            throwsRouteException(errorMsg, e, DELETE_CONNECT_FAILD);
        }
        finally
        {
            try
            {
                httpClient.close();
            }
            catch (IOException e)
            {
                String errorMsg = baseUrl + ":close  httpClient faild";
                throwsRouteException(errorMsg, e, CLOSE_CONNECT_FAILD);
            }
        }
        return result;
    }

    /**
     * delete方式请求， 参数以?形式拼接在url后面，不是放RequestBody里面
     *
     * @param url
     * @param params
     * @param headerParamsMap
     *            传入的header参数
     * @return
     * @throws RouteException
     * <AUTHOR>
     */
    public static String httpDeleteWithJson(String url, String params, Map<String, String> headerParamsMap)
            throws RouteException
    {
        // 数据必填项校验
        if (StringUtils.isBlank(url))
        {
            throw new RouteException(urlErrMsg, DATA_FORMAT_ERROR);
        }
        String result = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        try
        {
            HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(url);
            httpDelete.addHeader("Content-type", "application/json; charset=utf-8");
            httpDelete.setHeader("Accept", "application/json");

            // 传入的header参数
            if (headerParamsMap != null)
            {
                for (Map.Entry<String, String> entry : headerParamsMap.entrySet())
                {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    httpDelete.setHeader(key, value);
                }
            }

            httpDelete.setEntity(new StringEntity(params, Charset.forName("UTF-8")));
            CloseableHttpResponse res = httpClient.execute(httpDelete);
            if (null != res.getEntity())
            {
                result = EntityUtils.toString(res.getEntity());
            }

            if (res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED && res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED_FOUR)
            {
                throw new RouteException(EntityUtils.toString(res.getEntity()), SERVICE_DELETE_ERR);
            }

            res.close();
        }
        catch (IOException e)
        {
            String errorMsg = url + ":delete connect faild";
            throwsRouteException(errorMsg, e, DELETE_CONNECT_FAILD);
        }
        finally
        {
            try
            {
                httpClient.close();
            }
            catch (IOException e)
            {
                String errorMsg = url + ":close  httpClient faild";
                throwsRouteException(errorMsg, e, CLOSE_CONNECT_FAILD);
            }
        }
        return result;
    }

    /**
     * get方式请求， 参数以?形式拼接在url后面，不是放RequestBody里面
     *
     * @param url
     * @param paramsMap
     * @param headerParamsMap
     *            传入的header参数
     * @return
     * @throws RouteException
     * <AUTHOR>
     */

    public static String httpGet(String url, Map<String, String> paramsMap,
            Map<String, String> headerParamsMap) throws RouteException
    {
        // 数据必填项校验
        if (StringUtils.isBlank(url))
        {
            throw new RouteException(urlErrMsg, DATA_FORMAT_ERROR);
        }
        String result = null;
        String baseUrl;
        if (paramsMap != null)
        {
            List params = new ArrayList();
            for (Map.Entry<String, String> entry : paramsMap.entrySet())
            {
                String key = entry.getKey();
                String value = entry.getValue();
                params.add(new BasicNameValuePair(key, value));
            }
            baseUrl = url + "?" + URLEncodedUtils.format(params, "UTF-8");
        }
        else
        {
            baseUrl = url;
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = null;
        try{
            httpGet = new HttpGet(new URIBuilder(Normalizer.normalize(baseUrl, Normalizer.Form.NFKC)).toString());
        }catch(Exception e ){}
        if(null == httpGet) {
            String errorMsg = "construct HttpGet failed!";
            throw new RouteException(errorMsg, HTTP_GET_CONSTRUCT_ERR);
        }
        httpGet.addHeader("Content-type", "application/json; charset=utf-8");
        httpGet.setHeader("Accept", "application/json");
        // 传入的header参数
        if (headerParamsMap != null)
        {
            for (Map.Entry<String, String> entry : headerParamsMap.entrySet())
            {
                String key = entry.getKey();
                String value = entry.getValue();
                httpGet.setHeader(key, value);
            }
        }

        try
        {
            CloseableHttpResponse res = httpClient.execute(httpGet);
            result = EntityUtils.toString(res.getEntity());
            if (res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED)
            {
                throw new RouteException(result, SERVICE_GET_ERR);
            }
            res.close();
        }
        catch (ClientProtocolException e)
        {
            String errorMsg = url + ":httpGetWithJSON connect faild";
            throwsRouteException(errorMsg, e, GET_CONNECT_FAILD);
        }
        catch (IOException e)
        {
            String errorMsg = url + ":httpGetWithJSON connect faild";
            throwsRouteException(errorMsg, e, GET_CONNECT_FAILD);
        }
        finally
        {
            try
            {
                httpClient.close();
            }
            catch (IOException e)
            {
                String errorMsg = url + ":close  httpClient faild";
                throwsRouteException(errorMsg, e, CLOSE_CONNECT_FAILD);
            }
        }

        return result;
    }

    /**
     * put方式请求，参数放RequestBody里面
     *
     * @param url
     * @param params
     * @param headerParamsMap
     *            传入的header参数
     * @return
     * @throws RouteException
     * <AUTHOR>
     */
    public static String httpPutWithJSON(String url, String params, Map<String, String> headerParamsMap)
            throws RouteException
    {
        // 数据必填项校验
        if (StringUtils.isBlank(url))
        {
            throw new RouteException(urlErrMsg, DATA_FORMAT_ERROR);
        }
        String result = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPut httpPut = null;
        try{
            httpPut = new HttpPut(new URIBuilder(Normalizer.normalize(url, Normalizer.Form.NFKC)).toString());
        }catch(Exception e ){}
        if(null == httpPut) {
            String errorMsg = "construct HttpPut failed!";
            throw new RouteException(errorMsg, HTTP_PUT_CONSTRUCT_ERR);
        }
        httpPut.addHeader("Content-type", "application/json; charset=utf-8");
        httpPut.setHeader("Accept", "application/json");

        // 传入的header参数
        if (headerParamsMap != null)
        {
            for (Map.Entry<String, String> entry : headerParamsMap.entrySet())
            {
                String key = entry.getKey();
                String value = entry.getValue();
                httpPut.setHeader(key, value);
            }
        }

        httpPut.setEntity(new StringEntity(params, Charset.forName("UTF-8")));
        try
        {
            CloseableHttpResponse res = httpClient.execute(httpPut);
            result = EntityUtils.toString(res.getEntity());
            if (res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED && res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED_ONE)
            {
                throw new RouteException(result, SERVICE_PUT_ERR);
            }
            res.close();
        }
        catch (IOException e)
        {
            String errorMsg = url + ":httpPostWithJSON connect faild";
            throwsRouteException(errorMsg, e, POST_CONNECT_FAILD);
        }
        finally
        {
            try
            {
                httpClient.close();
            }
            catch (IOException e)
            {
                String errorMsg = url + ":close  httpClient faild";
                throwsRouteException(errorMsg, e, CLOSE_CONNECT_FAILD);
            }
        }

        return result;
    }


    public static String httpPostForWebService(String url, String params, Map<String, String> headerParamsMap)
            throws RouteException
    {
        // 数据必填项校验
        if (StringUtils.isBlank(url))
        {
            throw new RouteException(urlErrMsg, DATA_FORMAT_ERROR);
        }
        String result = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = null;
        try{
            httpPost = new HttpPost(new URIBuilder(Normalizer.normalize(url, Normalizer.Form.NFKC)).toString());
        }catch(Exception e ){}
        if(null == httpPost) {
            String msg = "construct HttpPost failed!";
            throw new RouteException(msg, HTTP_POST_CONSTRUCT_ERR);
        }
        httpPost.addHeader("Content-type", Constant.WEB_CHART_ADD);
        httpPost.setHeader("Accept", Constant.WEB_CHART_ADD);
        // 传入的header参数
        if (null != headerParamsMap)
        {
            for (Map.Entry<String, String> entry : headerParamsMap.entrySet())
            {
                String key = entry.getKey();
                String value = entry.getValue();
                httpPost.setHeader(key, value);
            }
        }

        httpPost.setEntity(new StringEntity(params, ContentType.parse(Constant.WEB_CHART_ADD)));
        try
        {
            CloseableHttpResponse res = httpClient.execute(httpPost);

            result = EntityUtils.toString(res.getEntity());
            if (res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED && res.getStatusLine().getStatusCode() != NumConstant.NUM_TWO_HUNDRED_ONE)
            {
                throw new RouteException(result, SERVICE_POST_ERR);
            }
            res.close();
        }
        catch (IOException e)
        {
            String errorMsg = url + ":httpPostWithJSON connect faild";
            throwsRouteException(errorMsg, e, POST_CONNECT_FAILD);
        }
        finally
        {
            try
            {
                httpClient.close();
            }
            catch (IOException e)
            {
                String errorMsg = url + ":close  httpClient faild";
                throwsRouteException(errorMsg, e, CLOSE_CONNECT_FAILD);
            }
        }

        return result;
    }

    private static void throwsRouteException(String errorMsg, Exception e, String errorCode)
            throws RouteException
    {
        String msg = errorMsg + ".errorMsg:" + e.getMessage();
        logger.error(msg);
        throw new RouteException(errorMsg, errorCode);
    }

}
