package com.zte.springbootframe.aop;

import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeaderUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * 针对数据源的切面操作,统一在此切换多租户的数据源操作。
 * 根据用户的信息获取对应数据源，并切换.
 *
 * <AUTHOR>
 */

@Aspect
@Component
@Order(1)
public class IMESDataSourceAspect {

    @Autowired
    private FactoryConfig factoryConfig;

    /**
     * 对业务层方法做aop拦截,便于在执行业务层方法前切换数据源
     *
     * @param point 切点
     * <AUTHOR>
     */
    @Before("execution(* com.zte..application..impl..*.*(..))")
    public void setDataSourceKey(JoinPoint point) {

        // 1.根据request切换数据源
        changeDataSourceByReq();

        // 2.根据注解切换数据源
        changeDataSourceByAnnotation(point);

        // 注意：第2点优先级高于第1点，所以1和2的执行顺序不能调换

    }

    /**
     * 对业务层方法做aop拦截,在业务层方法之后执行
     *
     * @param point 切点
     * <AUTHOR>
     */
    @After("execution(* com.zte..interfaces..*Controller.*(..))")
    public void after(JoinPoint point) {

        DatabaseContextHolder.clear();
    }

    /**
     * 根据注解切换数据源
     *
     * @param point
     */
    private void changeDataSourceByAnnotation(JoinPoint point) {

        Object target = point.getTarget();
        String method = point.getSignature().getName();
        Class<?> classz = target.getClass();
        Class<?>[] parameterTypes = ((MethodSignature) point.getSignature()).getMethod().getParameterTypes();
        try {

            // 检查类是否用到datasorce注解
            if (classz.isAnnotationPresent(DataSource.class)) {
                DataSource data = classz.getAnnotation(DataSource.class);
                DatabaseContextHolder.setDatabaseType(data.value());
            }

            // 检查当前方法是否用到datasorce注解
            Method m = classz.getMethod(method, parameterTypes);
            if (m != null && m.isAnnotationPresent(DataSource.class)) {
                DataSource data = m.getAnnotation(DataSource.class);
                DatabaseContextHolder.setDatabaseType(data.value());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据request信息切换数据源
     */
    private void changeDataSourceByReq() {
        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
        String factoryId = header.get(SysConst.HTTP_HEADER_X_FACTORY_ID.toLowerCase());
        ServletRequestAttributes servletReqAttr =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletReqAttr == null && StringUtils.isBlank(factoryId)) {
            return;
        }
        if (servletReqAttr != null) {
            HttpServletRequest request = servletReqAttr.getRequest();
            String dataBaseParam = request.getParameter("Data-Base-Param");
            if (StringUtils.isNotBlank(dataBaseParam)) {
                DatabaseContextHolder.setDatabaseType(dataBaseParam);
                return;
            }
            if (StringUtils.isBlank(factoryId)) {
                factoryId = request.getParameter(SysConst.HTTP_HEADER_X_FACTORY_ID);
                String parameterFactoryId = request.getParameter("factoryId");
                factoryId = StringUtils.isNotBlank(factoryId) ? factoryId : parameterFactoryId;
                RequestHeaderUtil.setHeaderFactoryId(request, factoryId);
            }
        }
        if (StringUtils.isNotBlank(factoryId)) {
            String dataBaseType = factoryConfig.getFactoryDbById(factoryId);
            if (StringUtils.isNotEmpty(dataBaseType)) {
                DatabaseContextHolder.setDatabaseType(dataBaseType);
            }
        }
    }

}
