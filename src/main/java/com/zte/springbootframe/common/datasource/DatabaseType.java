package com.zte.springbootframe.common.datasource;

/**
 * 列出所有的数据源key（常用数据库名称来命名）
 * 注意：
 * 1）这里数据源与数据库是一对一的
 * 2）DatabaseType中的变量名称就是数据库的名称
 *  <AUTHOR>
 */
public class DatabaseType {

    public static final String DB1 = "DB1";

    public static final String DB2 = "DB2";

    public static final String SFC = "SFC";

    public static final String MES = "MES";

    public static final String WMES = "WMES";

    public static final String MES_EAI = "MES_EAI";

    public static final String INFOR = "INFOR";

    public static final String EAI = "EAI";

    public static final String WMSPRODLMS = "WMSPRODLMS";
    
    public static final String ERP ="ERP";

    public static final String SFCNEW = "SFCNEW";

    public static final String ERP1 ="ERP1";

    public static final String BARCODE = "BARCODE";
}
