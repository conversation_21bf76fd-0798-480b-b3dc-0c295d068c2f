package com.zte.springbootframe.common.model;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class MsgData<T> {

    /**
     * 工厂ID
     */
    private String factoryId;

    /**
     * 操作模式：1.新增；2.修改；3.删除
     */
    private Integer operationMode;

    private T data;

    public MsgData(){

    }

    public MsgData(String factoryId, Integer operationMode) {
        this.factoryId = factoryId;
        this.operationMode = operationMode;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public Integer getOperationMode() {
        return operationMode;
    }

    public void setOperationMode(Integer operationMode) {
        this.operationMode = operationMode;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return JSONObject.toJSON(this).toString();
    }

    public enum OperationEnum {
        insert(1, "新增"),
        update(2, "修改"),
        delete(3, "删除"),
        other(9,"其他");

        private final Integer type;

        private final String name;

        private OperationEnum(Integer type, String name) {
            this.type = type;
            this.name = name;
        }

        public Integer getType() {
            return type;
        }

        public String getName() {
            return name;
        }


    }

}
