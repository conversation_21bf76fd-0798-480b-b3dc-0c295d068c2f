package com.zte.springbootframe.common.model;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class FactoryConfig {

//    @Value("${common.center.factory.id}")
    private String commonCenterFactoryId;

//    @Value("${common.center.factory.db}")
    private String commonCenterFactoryDb;

//    @Value("${common.site.factory.id}")
    private String commonSiteFactoryId;

//    @Value("${common.site.factory.db}")
    private String commonSiteFactoryDb;

//    @Value("${common.site.xa.factory.id}")
    private String commonSiteXaFactoryId;

//    @Value("${common.site.xa.factory.db}")
    private String commonSiteXaFactoryDb;

//    @Value("${common.entity.id}")
    private String commonEntityId;

    private Map<String,String> siteMap = new HashMap<>();

    @PostConstruct
    public void init(){
        if(StringUtils.isNotBlank(commonSiteFactoryId) && StringUtils.isNotBlank(commonSiteFactoryDb)){
            siteMap.put(commonSiteFactoryId,commonSiteFactoryDb);
        }
        if(StringUtils.isNotBlank(commonSiteXaFactoryId) && StringUtils.isNotBlank(commonSiteXaFactoryDb)){
            siteMap.put(commonSiteXaFactoryId,commonSiteXaFactoryDb);
        }
    }



    public String getCommonSiteFactoryId() {
        return commonSiteFactoryId;
    }

    public String getCommonSiteFactoryDb() {
        return commonSiteFactoryDb;
    }

    public String getCommonCenterFactoryId() {
        return commonCenterFactoryId;
    }

    public String getCommonCenterFactoryDb() {
        return commonCenterFactoryDb;
    }

    public String getCommonSiteXaFactoryId() {
        return commonSiteXaFactoryId;
    }

    public String getCommonSiteXaFactoryDb() {
        return commonSiteXaFactoryDb;
    }

    public String getCommonEntityId() {
        return commonEntityId;
    }

    public String getFactoryDbById(String factoryId){
        if(StringUtils.isNotBlank(factoryId)){
            if(factoryId.equals(commonCenterFactoryId)){
                return commonCenterFactoryDb;
            }
        }
        return siteMap.get(factoryId);
    }

    public Map<String, String> getSiteMap() {
        return siteMap;
    }

}
