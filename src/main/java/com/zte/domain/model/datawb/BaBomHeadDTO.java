package com.zte.domain.model.datawb;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BaBomHeadDTO implements Serializable {

	private String bomNo;

	private String codeType;

	private String codeDesc;

	private String isLead;

	private String bomName;

	private String itemId;

	//in入参
	private String inItemId;

	private List<String> itemIdList;

	private String bomHeaderId;

	//技术参数
	private String param;
	//封装
	private String encapsulation;
	//是否有AVL指定,不为空则是
	private String isAvl;

	private Long total;

	//价格
	private String lastPrice;

	public String getLastPrice() {
		return lastPrice;
	}

	private String standardCost;

	private String createdBy;
	/**
	 * 归档人
	 */
	private String archiver;

	private String remark;

	private String style;
	/**
	 * 料单首行
	 */
	private String firstrow;
	/**
	 * 料单次行
	 */
	private String secondrow;

	public String getFirstrow() {
		return firstrow;
	}

	public void setFirstrow(String firstrow) {
		this.firstrow = firstrow;
	}

	public String getSecondrow() {
		return secondrow;
	}

	public void setSecondrow(String secondrow) {
		this.secondrow = secondrow;
	}

	public String getStyle() {
		return style;
	}

	public void setStyle(String style) {
		this.style = style;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * 归档日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date archiveDate;

	public Date getArchiveDate() {
		return archiveDate;
	}

	public void setArchiveDate(Date archiveDate) {
		this.archiveDate = archiveDate;
	}

	public String getArchiver() {
		return archiver;
	}

	public void setArchiver(String archiver) {
		this.archiver = archiver;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getStandardCost() {
		return standardCost;
	}

	public void setStandardCost(String standardCost) {
		this.standardCost = standardCost;
	}

	public void setLastPrice(String lastPrice) {
		this.lastPrice = lastPrice;
	}

	public Long getTotal() {
		return total;
	}

	public void setTotal(Long total) {
		this.total = total;
	}

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	private String bomId;

	public String getBomId() {
		return bomId;
	}

	public void setBomId(String bomId) {
		this.bomId = bomId;
	}

	public List<String> getItemIdList() {
		return itemIdList;
	}

	public void setItemIdList(List<String> itemIdList) {
		this.itemIdList = itemIdList;
	}

	public String getParam() {
		return param;
	}

	public void setParam(String param) {
		this.param = param;
	}

	public String getEncapsulation() {
		return encapsulation;
	}

	public void setEncapsulation(String encapsulation) {
		this.encapsulation = encapsulation;
	}

	public String getIsAvl() {
		return isAvl;
	}

	public void setIsAvl(String isAvl) {
		this.isAvl = isAvl;
	}

	public String getBomHeaderId() {
		return bomHeaderId;
	}

	public void setBomHeaderId(String bomHeaderId) {
		this.bomHeaderId = bomHeaderId;
	}

	public String getInItemId() {
		return inItemId;
	}

	public void setInItemId(String inItemId) {
		this.inItemId = inItemId;
	}

	private static final long serialVersionUID = 1L;

	public String getBomNo() {
		return bomNo;
	}

	public void setBomNo(String bomNo) {
		this.bomNo = bomNo;
	}

	public String getCodeType() {
		return codeType;
	}

	public void setCodeType(String codeType) {
		this.codeType = codeType;
	}

	public String getCodeDesc() {
		return codeDesc;
	}

	public void setCodeDesc(String codeDesc) {
		this.codeDesc = codeDesc;
	}

	public String getIsLead() {
		return isLead;
	}

	public void setIsLead(String isLead) {
		this.isLead = isLead;
	}

	public String getBomName() {
		return bomName;
	}

	public void setBomName(String bomName) {
		this.bomName = bomName;
	}
}
