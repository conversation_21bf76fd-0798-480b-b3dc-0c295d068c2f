package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Ztebarcode implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物料条码")
    private String itemBarcode;

    @ApiModelProperty(value = "物料代码")
    private Long sku;

    @ApiModelProperty(value = "物料Id")
    private Long itemId;

    @ApiModelProperty(value = "物料UUID")
    private Long itemUuid;

    @ApiModelProperty(value = "供应商代码")
    private String supplierNo;

    @ApiModelProperty(value = "生产编码")
    private String productNo;

    @ApiModelProperty(value = "本地价格")
    private Long localPrice;

    @ApiModelProperty(value = "货币类型")
    private String currencyType;

    @ApiModelProperty(value = "不含税价格")
    private Long priceWithoutTax;

    @ApiModelProperty(value = "小批量编号")
    private String litbatchNo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "最新更新人")
    private String lastUpdatedBy;


    @ApiModelProperty(value = "生效标识（Y有效，N无效）")
    private Long enabledFlag;


    @ApiModelProperty(value = "是否隔离")
    private Long isSeparate;

    @ApiModelProperty(value = "环保属性")
    private Long isLead;

    @ApiModelProperty(value = "hub类型")
    private Long hubType;

    @ApiModelProperty(value = "是否Xp")
    private Long isXp;

    @ApiModelProperty(value = "是否Gz")
    private Long isGz;

    @ApiModelProperty(value = "qc类型")
    private String qctype;

    @ApiModelProperty(value = "qc标识")
    private String qcflag;

    @ApiModelProperty(value = "有效期")
    private Long shelflife;

    @ApiModelProperty(value = "qc次数")
    private Long qctimes;

    @ApiModelProperty(value = "成本")
    private Long pocost;

    @ApiModelProperty(value = "asn")
    private String asn;

    @ApiModelProperty(value = "含铅等级")
    private String leadlevel;

    @ApiModelProperty(value = "防潮等级")
    private String wetlevel;

    @ApiModelProperty(value = "供应模式")
    private String supplierModel;

    public String getItemBarcode() {

        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {

        this.itemBarcode = itemBarcode;
    }

    public Long getSku() {

        return sku;
    }

    public void setSku(Long sku) {

        this.sku = sku;
    }

    public Long getItemId() {

        return itemId;
    }

    public void setItemId(Long itemId) {

        this.itemId = itemId;
    }

    public Long getItemUuid() {

        return itemUuid;
    }

    public void setItemUuid(Long itemUuid) {

        this.itemUuid = itemUuid;
    }

    public String getSupplierNo() {

        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {

        this.supplierNo = supplierNo;
    }

    public String getProductNo() {

        return productNo;
    }

    public void setProductNo(String productNo) {

        this.productNo = productNo;
    }


    public Long getLocalPrice() {

        return localPrice;
    }

    public void setLocalPrice(Long localPrice) {

        this.localPrice = localPrice;
    }

    public String getCurrencyType() {

        return currencyType;
    }

    public void setCurrencyType(String currencyType) {

        this.currencyType = currencyType;
    }

    public Long getPriceWithoutTax() {

        return priceWithoutTax;
    }

    public void setPriceWithoutTax(Long priceWithoutTax) {

        this.priceWithoutTax = priceWithoutTax;
    }

    public String getLitbatchNo() {

        return litbatchNo;
    }

    public void setLitbatchNo(String litbatchNo) {

        this.litbatchNo = litbatchNo;
    }

    public String getCreatedBy() {

        return createdBy;
    }

    public void setCreatedBy(String createdBy) {

        this.createdBy = createdBy;
    }

    public String getLastUpdatedBy() {

        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {

        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Long getEnabledFlag() {

        return enabledFlag;
    }

    public void setEnabledFlag(Long enabledFlag) {

        this.enabledFlag = enabledFlag;
    }

    public Long getIsSeparate() {

        return isSeparate;
    }

    public void setIsSeparate(Long isSeparate) {

        this.isSeparate = isSeparate;
    }

    public Long getIsLead() {

        return isLead;
    }

    public void setIsLead(Long isLead) {

        this.isLead = isLead;
    }

    public Long getHubType() {

        return hubType;
    }

    public void setHubType(Long hubType) {

        this.hubType = hubType;
    }

    public Long getIsXp() {

        return isXp;
    }

    public void setIsXp(Long isXp) {

        this.isXp = isXp;
    }

    public Long getIsGz() {

        return isGz;
    }

    public void setIsGz(Long isGz) {

        this.isGz = isGz;
    }

    public String getQctype() {

        return qctype;
    }

    public void setQctype(String qctype) {

        this.qctype = qctype;
    }

    public String getQcflag() {

        return qcflag;
    }

    public void setQcflag(String qcflag) {

        this.qcflag = qcflag;
    }

    public Long getShelflife() {

        return shelflife;
    }

    public void setShelflife(Long shelflife) {

        this.shelflife = shelflife;
    }

    public Long getQctimes() {

        return qctimes;
    }

    public void setQctimes(Long qctimes) {

        this.qctimes = qctimes;
    }

    public Long getPocost() {

        return pocost;
    }

    public void setPocost(Long pocost) {

        this.pocost = pocost;
    }

    public String getAsn() {

        return asn;
    }

    public void setAsn(String asn) {

        this.asn = asn;
    }

    public String getLeadlevel() {

        return leadlevel;
    }

    public void setLeadlevel(String leadlevel) {

        this.leadlevel = leadlevel;
    }

    public String getWetlevel() {

        return wetlevel;
    }

    public void setWetlevel(String wetlevel) {

        this.wetlevel = wetlevel;
    }

    public String getSupplierModel() {

        return supplierModel;
    }

    public void setSupplierModel(String supplierModel) {

        this.supplierModel = supplierModel;
    }
}

