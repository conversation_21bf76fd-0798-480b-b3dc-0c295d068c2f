package com.zte.domain.model.material;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zte.interfaces.onlinefallback.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail;
import com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader;

/**
 * [描述] <br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020年5月27日 <br>
 * @see com.zte.domain.model.material <br>
 */
@Mapper
public interface OnlineFallBackApplyBillRepository {

    List<StFallbackApplybillHead> queryFallbackHead(String fallbackNo);

    List<StFallbackApplybillDetail> queryFallbackDetail(String fallbackNo);

    List<FallbackNoInfo> queryWaitFallback(FallbackNoInfo fallbackNoInfo);

    void insertStFallbackApplybillHead(StFallbackApplybillHead stFallbackApplybillHead);

    void insertStFallbackApplybillDetail(@Param("listDetail") List<StFallbackApplybillDetail> listDetail);

    List<ApplyBillDTO> queryFallbackApplybillHead(String applybillNo);

    List<ApplyBillItemDTO> queryFallbackApplybillDetail(String applybillNo);

    void updateStFallbackApplybillHead(StFallbackApplybillHead stFallbackApplybillHead);

    String queryStFallbackApplybillHeadHandlingOptions(@Param("applybillNo") String applybillNo);

    List<ApplyBillSoResultDTO> queryApplyBillSoResultHead(String applybillNo);

    List<ApplyBillSoResultItemDTO> queryApplyBillSoResultDetail(String applybillNo);

    List<Map<String, String>> queryApplyBillSoResultReelSn(String applybillNo);

    void updateFallbackStatus(FallbackNoInfo info);

    void updateBatchFallbackStatus(@Param("list") List<FallbackNoInfo> list);

    String getWsdlUrl();

    List<SoHeader> getSoHeader(String applybillNo);

    List<SoDetail> getSoDetail(String applybillNo);

    List<ApplyBillDTO> queryWaitFeedApplyNo(@Param("applybillNo") String applybillNo);

    List<ApplyBillDTO> queryStFallbackDetailBarcode(@Param("splitList") List<String> splitList);

    List<StSysLookupValuesDTO> getSysLookupValues(@Param("lookupType")String lookupType);

    List<StSysLookupValuesDTO> getEccnSysLookupValues(@Param("lookupType")String lookupType,@Param("lookupCode")String lookupCode);

    /**
     * 按sort_seq查询
     * @param lookupType
     * @return
     */
    List<StSysLookupValuesDTO> getSysLookupValuesByOrder(@Param("lookupType")String lookupType);

	void insertApplyBillNoLog(String applybillNo);

	List<String> queryWaitPushList();

	void updateApplyBillNoLog(@Param("applyBillNo") String applyBillNo, @Param("flag") int flag);

	void updateStFallbackProductByNonconformingProductNo(StFallbackApplybillHead stFallbackApplybillHead);

    void syncRealOutNumber(@Param("rowNo") int rowNo, @Param("applyBillNo") String applyBillNo , @Param("realOutNumber") double realOutNumber);

	void updateLastUpdatedTime(@Param("maxLastUpdatedTime") String maxLastUpdatedTime, @Param("lookupCode")String lookupCode);

    long selectApplybillNoAndStatus(@Param("billNo")String billNo,@Param("strExcclosed") String strExcclosed);
}
