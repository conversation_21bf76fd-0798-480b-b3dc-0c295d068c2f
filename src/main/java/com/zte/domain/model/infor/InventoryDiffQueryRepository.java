package com.zte.domain.model.infor;

import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.B2BCallBackInventoryResultDTO;
import com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import com.zte.interfaces.infor.vo.UnBindPkgSnJobVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/* Started by AICoder, pid:m97156114drce53147ca09df5035748da6631384 */

public interface InventoryDiffQueryRepository {
    /**
     * 获取差异数据
     * @param dto
     * @return
     */
    List<InventoryDiffDTO> getInventoryDiffData(InventoryDiffDTO dto);

    /**
     * 获取差异数据总量
     * @param dto
     * @return
     */
    int getInventoryDiffDataCount(InventoryDiffDTO dto);
    /**
     * 获取临时表差异数据
     * @param dto
     * @return
     */
    List<InventoryDiffDTO> getInventoryDiffTempData(InventoryDiffDTO dto);

    /**
     * 获取临时表差异数据总量
     * @param dto
     * @return
     */
    int getInventoryDiffDataTempCount(InventoryDiffDTO dto);

    /**
     * 清空阿里库存临时表
     */
    void deleteInventoryAlibabaTemp();
    /**
     * 清空ZTE库存临时表
     */
    void deleteInventoryTemp();
    /**
     * 清空库存差异临时表
     */
    void deleteInventoryDiffTemp();

    /**
     * 获取良品库
     * @return
     */
    List<ZteWarehouseInfoDTO> getInforWarehouseList();

    /**
     * 获取阿里infor良品库数据
     * @param inventoryList
     * @return
     */
    List<CustomerInventoryPickUpDTO> getALiStockInfoList(@Param("inventoryList") List<ZteWarehouseInfoDTO>   inventoryList);

    /**
     * 保存zte库存临时表数据
     * @param list
     * @return
     */
    void addInventoryTempData(List<CustomerInventoryPickUpDTO> list);

    /**
     * 获取ZTE库存临时表数据
     * @return
     */
    List<CustomerInventoryPickUpDTO> getInventoryTempData();

    /**
     * 保存差异库存临时表
     * @param list
     */
    void insertInventoryDiffTempData(List<InventoryDiffTempDTO> list);

    /**
     * 保存阿里库存临时表
     * @param list
     */
    void insertAliInventoryTempData(List<B2BCallBackInventoryResultDTO> list);


    /**
     * 获取未及时绑定的箱与sn关系单据。
     * @return
     */
    List<UnBindPkgSnJobVo> getUnBindSnRelationData();



    /**
     * 查询阿里一码通库存的箱类别和库区
     * @return
     */
    List<OriginMixedBoxInventoryDTO> getOriginAndMixedBoxInventory(@Param("inventoryWarehouseList") List<ZteWarehouseInfoDTO> inventoryWarehouseList);

    /**
     * 查询阿里一码通混箱库存和SN库存
     * @return
     */
    List<MixedBoxInventoryDTO> getMixedBoxAndSnInventory(@Param("inventoryWarehouseList") List<ZteWarehouseInfoDTO> inventoryWarehouseList);

    /**
     * 查询ZTE和阿里库存差异
     * @return
     */
    List<ZteAliInventoryDTO> getZteAndAliInventoryDiff();
    /**
     * 将成品库存汇总信息写入库存统计表
     */
    int saveProductInventoryStaticsData(List<CustomerInventoryPickUpDTO> list);

    /**
     * 根据仓库、物料代码库存数量并返回仓库id
     */
    List<ECLotxlocxid> getStockQtyWithWisId(ECLotxlocxidDTO lotxlocxidDTO);

}

/* Ended by AICoder, pid:m97156114drce53147ca09df5035748da6631384 */