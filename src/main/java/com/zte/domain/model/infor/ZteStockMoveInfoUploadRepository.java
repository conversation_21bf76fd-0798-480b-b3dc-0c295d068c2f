package com.zte.domain.model.infor;

import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.ZteStockInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ZteStockMoveInfoUploadRepository {

    /**
     * 查询库存移动信息
     */
    List<ZteStockMoveInfoUploadDTO> getStockMoveInfo(ZteStockInfoDTO dto);

    /**
     * 查询采购订单收货数据
     */
    List<ZtePurchaseOrderDTO> getPurchaseOrder(ZteStockInfoDTO dto);

    int updateEdiPoSBySerialKey(List<String> list);
    int updateEdiSoSBySerialKey(List<String> list);
    List<IscpEdiLog> getTransferNo();
    List<InforIsrmTransferDTO> getEdiPoSList(List<String> list);
    List<InforIsrmTransferDTO> getEdiSoSList(List<String> list);
    int updateIscpEdiLog(@Param("list") List<String> list, @Param("sourceTable") String sourceTable,
                          @Param("issend") int issend, @Param("responseParam") String responseParam);

    List<AliCreateMoveBillDTO> getCreateInfo(IscpEdiLog iscpEdiLog);

    List<AliOrderDeductionBillDTO> getDistributeMaterialInfo(IscpEdiLog iscpEdiLog);

    List<AliOrderSubmitDetailDTO> getSubmitMaterialInfo(IscpEdiLog iscpEdiLog);
}