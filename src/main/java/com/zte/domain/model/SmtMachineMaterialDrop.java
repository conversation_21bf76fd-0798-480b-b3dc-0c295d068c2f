/**
 * 项目名称 : ${project_name}
 * 创建日期 : ${date}
 * 修改历史 :
 *   1. [${date}] 创建文件 by ${user}
 **/
package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * // TODO 添加类/接口功能描述
 * 
 * <AUTHOR>
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class SmtMachineMaterialDrop implements Serializable {



    private static final long serialVersionUID = 4508187872035388804L;
    /**
     * // TODO id
     **/
    private String materialDropId;

    //线体
    private String lineCode;

    //位号
    private String locationNo;

    //机台编号
    private String machineNo;

    //组织id
    private BigDecimal orgId;

    //数量
    private BigDecimal qty;

    //工单/指令
    private String workOrder;

    //零件编号
    private String itemCode;

    //轨道
    private String trackNo;

    //feeder
    private String feederNo;

    //料盘
    private String reelId;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * // TODO remarks
     **/
    private String createUser;

    /**
     * // TODO remarks
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;

    /**
     * // TODO remarks
     **/
    private String lastUpdatedBy;

    /**
     * // TODO remarks
     **/
    private String enabledFlag;

    /**
     * // TODO remarks
     **/
    private BigDecimal entityId;

    /**
     * // TODO remarks
     **/
    private BigDecimal factoryId;

    private String remark;

    private BigDecimal itemQty;

    public String getMaterialDropId() {
        return materialDropId;
    }

    public void setMaterialDropId(String materialDropId) {
        this.materialDropId = materialDropId;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLocationNo() {
        return locationNo;
    }

    public void setLocationNo(String locationNo) {
        this.locationNo = locationNo;
    }

    public String getMachineNo() {
        return machineNo;
    }

    public void setMachineNo(String machineNo) {
        this.machineNo = machineNo;
    }

    public BigDecimal getOrgId() {
        return orgId;
    }

    public void setOrgId(BigDecimal orgId) {
        this.orgId = orgId;
    }

    public String getWorkOrder() {
        return workOrder;
    }

    public void setWorkOrder(String workOrder) {
        this.workOrder = workOrder;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getTrackNo() {
        return trackNo;
    }

    public void setTrackNo(String trackNo) {
        this.trackNo = trackNo;
    }

    public String getFeederNo() {
        return feederNo;
    }

    public void setFeederNo(String feederNo) {
        this.feederNo = feederNo;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public BigDecimal getEntityId() {
        return entityId;
    }

    public void setEntityId(BigDecimal entityId) {
        this.entityId = entityId;
    }

    public BigDecimal getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(BigDecimal factoryId) {
        this.factoryId = factoryId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReelId() {
        return reelId;
    }

    public void setReelId(String reelId) {
        this.reelId = reelId;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getItemQty() {
        return itemQty;
    }

    public void setItemQty(BigDecimal itemQty) {
        this.itemQty = itemQty;
    }
}