package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;

/**
 * 条码规则生成
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BarcodeGenerateRule implements Serializable {

    private String forumId;


    private String barcodeName;


    private String barcodeForum;


    private String barcodeCatagory;


    private String barcodeType;


    private String codeType;


    private String classifyFactor;


    private String barcodeSample;


    private Integer barcodeLength;


    private Integer factoryId;


    private Integer entityId;


    private String enabledFlag;


    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedDate;


    private static final long serialVersionUID = 1L;

    public String getForumId() {
        return forumId;
    }

    public void setForumId(String forumId) {
        this.forumId = forumId;
    }

    public String getBarcodeName() {
        return barcodeName;
    }

    public void setBarcodeName(String barcodeName) {
        this.barcodeName = barcodeName;
    }

    public String getBarcodeForum() {
        return barcodeForum;
    }

    public void setBarcodeForum(String barcodeForum) {
        this.barcodeForum = barcodeForum;
    }

    public String getBarcodeCatagory() {
        return barcodeCatagory;
    }

    public void setBarcodeCatagory(String barcodeCatagory) {
        this.barcodeCatagory = barcodeCatagory;
    }

    public String getBarcodeType() {
        return barcodeType;
    }

    public void setBarcodeType(String barcodeType) {
        this.barcodeType = barcodeType;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getClassifyFactor() {
        return classifyFactor;
    }

    public void setClassifyFactor(String classifyFactor) {
        this.classifyFactor = classifyFactor;
    }

    public String getBarcodeSample() {
        return barcodeSample;
    }

    public void setBarcodeSample(String barcodeSample) {
        this.barcodeSample = barcodeSample;
    }

    public Integer getBarcodeLength() {
        return barcodeLength;
    }

    public void setBarcodeLength(Integer barcodeLength) {
        this.barcodeLength = barcodeLength;
    }

    public Integer getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Integer factoryId) {
        this.factoryId = factoryId;
    }

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
}