package com.zte.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 子母卡绑定反向设置日志实体
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProdUnbindingSettingLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String logId;
    /**
    * 反向设置ID
    */
    private String settingId;
    /**
     * 料单代码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 物料代码
     */
    private String itemCode;
    /**
     * 物料名称
     */
    private String itemName;
    /**
     * 物料类型
     */
    private String itemType;
    /**
     * 不能绑定原因
     */
    private String cannotReason;
    /**
     * 用量
     */
    private BigDecimal usageCount;
    /**
     * 批次
     */
    private String prodPlanId;
    /**
     * 子工序
     */
    private String processCode;
    /**
     * 工艺段
     */
    private String craftSection;
    /**
     * 来源系统
     */
    private String sourceSystem;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 最后更新人
     */
    private Date createDate;
    /**
     * 最后更新人
     */
    private String lastUpdatedBy;
    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;
    /**
     * 可用标识
     */
    private String enabledFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 组织ID
     */
    private BigDecimal orgId;
    /**
     * 工厂ID
     */
    private BigDecimal factoryId;
    /**
     * 实体ID
     */
    private BigDecimal entityId;
    private String attribute1;
    private String attribute2;
    private String attribute3;
    private String attribute4;
    private String attribute5;
}
