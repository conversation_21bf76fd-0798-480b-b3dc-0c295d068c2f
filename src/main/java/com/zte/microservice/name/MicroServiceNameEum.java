package com.zte.microservice.name;

/**
 * 列出所有的服务名
 *  <AUTHOR>
 */
public class MicroServiceNameEum {

    public static final String SYSTEM = "zte-mes-manufactureshare-systemsys";

    public static final String BASICSETTING = "zte-mes-manufactureshare-basicsettingsys";

    public static final String CENTERFACTORY = "zte-mes-manufactureshare-centerfactory";

    public static final String CRAFTTECH = "zte-mes-manufactureshare-crafttechsys";

    public static final String PLANSCHEDULE = "zte-mes-manufactureshare-planschedulesys";

    public static final String PRODUCTIONMGMT = "zte-mes-manufactureshare-productionmgmtsys";

    public final static String EQPMGMT = "zte-mes-manufactureshare-eqpmgmtsys";

    public static final String MDS = "zte-mes-mdsservice";

    public static final String ISCPMGMT="zte-scm-iscp-delicoordination-service";

    public static final String ISCPORDERFEEDBACK="zte-scm-iscp-qareceivemanage-service";

    public static final String ISCPSERVICE="zte-scm-iscp-bff-service";

    public static final String DATAWB="zte-mes-manufactureshare-datawb";

    public static final String VERSION = "v1";

    public static final String SENDTYPEGET = "GET";

    public static final String SENDTYPEDELETE = "DELETE";

    public static final String DELETEJSON_STRING = "DELETEJSON";

    public static final String SENDTYPEPOST = "POST";

    public static final String SENDTYPEPUT = "PUT";
}
