package com.zte.application.kxbariii;

import com.zte.interfaces.dto.kxbariii.TechCpmMeasureHeadDTO;
import com.zte.interfaces.dto.kxbariii.TechSupcpHeadDTO;

/**
 * <AUTHOR>
 * @date 2024-06-18 15:11
 */
public interface TechCpmMeasureHeadService {

    /**
     * 预防纠正措施单 新增
     *
     * @param headDTO 头表信息
     */
    void insertTechCpmMeasureHead(TechCpmMeasureHeadDTO headDTO);

    /**
     * 供方预防纠正措施单 新增
     *
     * @param headDTO 头表信息
     */
    void insertTechSupcpHead(TechSupcpHeadDTO headDTO);


}
