package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.step.ECZteMeiTuanService;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.ECLotxlocxid;
import com.zte.interfaces.infor.dto.ECLotxlocxidDTO;
import com.zte.interfaces.step.dto.*;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.utils.CommonUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_13;
import static com.zte.common.utils.NumConstant.INT_51;
import static com.zte.common.utils.NumConstant.STR_2;

/**
 * 美团信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-14 10:14:00
 */
@Service
public class ECZteMeiTuanServiceImpl implements ECZteMeiTuanService {

    private static final Logger log = LoggerFactory.getLogger(ECZteMeiTuanServiceImpl.class);

    @Autowired
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;

    @Autowired
    private InforBarcodeCenterRepository barcodeCenterRepository;

    @Autowired
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;

    @Autowired
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    /**
     * 上传库存信息
     */
    @Override
    public void uploadStockInfo(List<String> itemNoList, String empNo) throws Exception {
        // 查询美团客户物料信息
        List<ECCustomerItemsDTO> customerItemsDTOList = imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(
                CUSTOMER_NAME_MEITUAN, COOPERATION_MODE_AVAP, itemNoList, empNo);
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        // 查询可用库存仓库信息
        List<String> inventoryHoldWmwhids = barcodeCenterRepository.getInventoryHoldWmwhids();
        // 查询不良品库存仓库信息
        List<String> badInventoryHoldWmwhids = barcodeCenterRepository.getBadInventoryHoldWmwhids();
        // 查询可用库存
        ECLotxlocxidDTO inventoryHoldLotxlocxidDTO = new ECLotxlocxidDTO();
        inventoryHoldLotxlocxidDTO.setWisIdList(inventoryHoldWmwhids);
        // 物料代码列表
        inventoryHoldLotxlocxidDTO.setSkuList(customerItemsDTOList.stream().map(
                ECCustomerItemsDTO::getZteCode).distinct().collect(Collectors.toList()));
        List<ECLotxlocxid> inventoryHoldList = inventoryDiffQueryRepository.getStockQtyWithWisId(inventoryHoldLotxlocxidDTO);
        // 查询不良品库存
        ECLotxlocxidDTO badInventoryHoldLotxlocxidDTO = new ECLotxlocxidDTO();
        badInventoryHoldLotxlocxidDTO.setWisIdList(badInventoryHoldWmwhids);
        badInventoryHoldLotxlocxidDTO.setSkuList(inventoryHoldLotxlocxidDTO.getSkuList());
        List<ECLotxlocxid> badInventoryHoldList = inventoryDiffQueryRepository.getStockQtyWithWisId(badInventoryHoldLotxlocxidDTO);
        // 保存库存快照（可用库存 + 不良品库存）
        saveStockSnapshot(inventoryHoldList);
        saveStockSnapshot(badInventoryHoldList);

        // 组装上传数据
        List<ECCustomerStockInfoDataDTO> uploadDataList = getCustomerStockInfo(customerItemsDTOList, inventoryHoldList, badInventoryHoldList);

        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        List<ZteStockInfoUploadLogDTO> zteStockInfoUploadLogDTOList = new ArrayList<>();
        for (List<ECCustomerStockInfoDataDTO> tempList : CommonUtils.splitList(uploadDataList, NumConstant.INT_500)) {
            // 组装B2B推送对象
            CustomerDataLogDTO customerDataLogDTO = getCustomerDataLogDTO(empNo, tempList);
            customerDataLogDTOList.add(customerDataLogDTO);
            // 组装日志对象
            ZteStockInfoUploadLogDTO zteStockInfoUploadLogDTO = new ZteStockInfoUploadLogDTO();
            BeanUtils.copyProperties(customerDataLogDTO, zteStockInfoUploadLogDTO);
            zteStockInfoUploadLogDTOList.add(zteStockInfoUploadLogDTO);
        }
        //  记录上传日志
        for (List<ZteStockInfoUploadLogDTO> tempZteStockInfoUploadLogDTOList : CommonUtils.splitList(zteStockInfoUploadLogDTOList, NumConstant.INT_500)) {
            zteStockInfoUploadRepository.insertStockUploadLog(tempZteStockInfoUploadLogDTOList);
        }
        //上传B2B
        for (List<CustomerDataLogDTO> tempCustomerDataLogDTOList : CommonUtils.splitList(customerDataLogDTOList, NumConstant.INT_500)) {
            imesCenterfactoryRemoteService.pushDataToB2B(tempCustomerDataLogDTOList, empNo);
        }
    }

    /**
     * 组装B2B推送对象
     *
     * @param empNo
     * @param tempList
     * @return
     */
    @NotNull
    private CustomerDataLogDTO getCustomerDataLogDTO(String empNo, List<ECCustomerStockInfoDataDTO> tempList) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(UUID.randomUUID().toString());
        customerDataLogDTO.setOrigin(INFOR_WMS);
        customerDataLogDTO.setCustomerName(CUSTOMER_NAME_MEITUAN);
        customerDataLogDTO.setProjectName(STOCK_INFO_ZH);
        customerDataLogDTO.setProjectPhase(STRING_EMPTY);
        customerDataLogDTO.setCooperationMode(STRING_EMPTY);
        customerDataLogDTO.setMessageType(ZTE_IMES_MEITUAN_INVENTORY);
        customerDataLogDTO.setContractNo(STRING_EMPTY);
        customerDataLogDTO.setTaskNo(getDataTransferBatchNo(ZTE1));
        customerDataLogDTO.setItemNo(STRING_EMPTY);
        customerDataLogDTO.setSn(STRING_EMPTY);
        Map<String, Object> map = new HashMap<>();
        map.put(DATA, tempList);
        customerDataLogDTO.setJsonData(JSON.toJSONString(map));
        customerDataLogDTO.setFactoryId(INT_51);
        customerDataLogDTO.setCreateBy(empNo);
        customerDataLogDTO.setLastUpdatedBy(empNo);
        return customerDataLogDTO;
    }

    /**
     * 组装上传数据
     *
     * @param customerItemsDTOList
     * @param inventoryHoldList
     * @param badInventoryHoldList
     * @return
     */
    private List<ECCustomerStockInfoDataDTO> getCustomerStockInfo(List<ECCustomerItemsDTO> customerItemsDTOList,
                                                                  List<ECLotxlocxid> inventoryHoldList, List<ECLotxlocxid> badInventoryHoldList) {

        // 1. 按物料代码分组汇总可用库存数量
        Map<String, Integer> availableStockMap = CollectionUtils.isEmpty(inventoryHoldList) ?
                new HashMap<>() :
                inventoryHoldList.stream()
                        .filter(item -> item.getSku() != null && item.getQty() != null)
                        .collect(Collectors.groupingBy(
                                ECLotxlocxid::getSku,
                                Collectors.summingInt(ECLotxlocxid::getQty)
                        ));

        // 2. 按物料代码分组汇总不良品库存数量
        Map<String, Integer> badStockMap = CollectionUtils.isEmpty(badInventoryHoldList) ?
                new HashMap<>() :
                badInventoryHoldList.stream()
                        .filter(item -> item.getSku() != null && item.getQty() != null)
                        .collect(Collectors.groupingBy(
                                ECLotxlocxid::getSku,
                                Collectors.summingInt(ECLotxlocxid::getQty)
                        ));

        // 3. 获取当前日期
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMATER_YYYYMMDD));

        // 4. 遍历客户物料信息，组装结果对象
        List<ECCustomerStockInfoDataDTO> resultList = new ArrayList<>();

        for (ECCustomerItemsDTO customerItem : customerItemsDTOList) {
            if (customerItem.getZteCode() == null) {
                log.warn("客户物料信息中ZTE代码为空，跳过处理: {}", customerItem);
                continue;
            }
            ECCustomerStockInfoDataDTO stockInfoData = new ECCustomerStockInfoDataDTO();
            // 设置日期
            stockInfoData.setDate(currentDate);
            // 设置客户物料信息
            stockInfoData.setManufacturerPartNo(customerItem.getCustomerMaterialType());
            stockInfoData.setManufacturer(customerItem.getCustomerSupplier());
            stockInfoData.setDescription(customerItem.getCustomerItemName());

            // 设置库存数量（从库存映射中获取，如果不存在则默认为0）
            String zteCode = customerItem.getZteCode();
            Integer availableQty = availableStockMap.getOrDefault(zteCode, 0);
            Integer badQty = badStockMap.getOrDefault(zteCode, 0);

            stockInfoData.setQuantity(availableQty);
            stockInfoData.setBadQuantity(badQty);
            resultList.add(stockInfoData);
        }
        log.info("库存信息组装完成，共处理 {} 条客户物料信息，生成 {} 条库存数据",
                customerItemsDTOList.size(), resultList.size());
        return resultList;
    }

    /**
     * 保存库存快照
     */
    private void saveStockSnapshot(List<ECLotxlocxid> ecLotxlocxids) {
        if (CollectionUtils.isEmpty(ecLotxlocxids)) {
            return;
        }
        List<ZteStockInfoDTO> snapshotList = new ArrayList<>();
        for (ECLotxlocxid ecLotxlocxid : ecLotxlocxids) {
            ZteStockInfoDTO zteStockInfoDTO = new ZteStockInfoDTO();
            zteStockInfoDTO.setItemNo(ecLotxlocxid.getSku());
            zteStockInfoDTO.setStockNo(ecLotxlocxid.getWisId());
            zteStockInfoDTO.setStockQuantity(ecLotxlocxid.getQty());
            snapshotList.add(zteStockInfoDTO);
        }
        // 分批保存库存快照
        for (List<ZteStockInfoDTO> tempZteStockInfoDTOList : CommonUtils.splitList(snapshotList, NumConstant.INT_500)) {
            zteStockInfoUploadRepository.insertStockUploadSnapshot(tempZteStockInfoDTOList);
        }
    }

    /**
     * 获取数据传输批次号
     */
    public String getDataTransferBatchNo(String batchNoType) {
        // 获取递增的序列号，并将其转换为字符串
        String dataTransferBatchNo = RedisSerialNoUtil.getDateIncreaseId(batchNoType, INT_6);
        // 对序列号进行格式化，将前12位和后两位用分隔符连接起来
        return dataTransferBatchNo.substring(INT_0, INT_12) + STR_2 + dataTransferBatchNo.substring(INT_13);
    }
}
