package com.zte.application.step;

import com.zte.interfaces.step.dto.*;
import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.interfaces.step.dto.B2BCallBackDTO;
import com.zte.interfaces.step.dto.ZteApproveResultDTO;
import com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface ZteAlibabaService {

    /**
     * 部件出⼊库计划申请&执⾏&核销
     */
    void deductionPlan(ZteDeductionPlanParamDTO dto);

    /**
     * 部件出入库执行数据定时推送
     */
    void excutedUploadJob(ZteDeductionPlanParamDTO dto);

    /**
     * 判断单据来源
     */
    void getBillType(ZteDeductionPlanParamDTO dto);

    /**
     * 通知出⼊库审批结果
     */
    void noticeApproveResult(List<ZteApproveResultDTO> list);

    /**
     * 接收阿里的返回消息
     */
    void zteB2BReturnInfo(B2BCallBackDTO dto);

    /**
     * 创建调拨单接口
     */
    void doExecutedCreateMoveBill(List<IscpEdiLog> iscpEdiLogs);

    /**
     * 阿里库存现有量
     */
    void executeQueryInventoryJob(ZteAlibabaInventoryParamDTO dto);
    /**
     * 部件箱包&SN库存同步定时推送
     */
    void executeSnInventoryUploadJob(ZteDeductionPlanParamDTO requestDto);

    /**
     * 部件箱包&SN库存同步定时推送(补偿+最后一批次)
     */
    void executeSnInventoryUploadLastBatchJob(ZteDeductionPlanParamDTO requestDto);

    /**
     * 工单扣料信息接口
     */
    void doExcutedDistributeMaterial(List<IscpEdiLog> iscp);

    /**
     * 领料单查询公共方法
     */
    List<ZmsPicklistMainDTO> selectPicklistMain(ZmsPicklistMainInDTO zmsPicklistMainInDTO);


    /**
     * 阿里推送B2B
     * @param json     阿里交互DTO转成的Json字符串
     * @param dto      通用dto，记录日志信息
     * @param projName 接口名称常量，自己定义：例如：部件出入库计划申请&执行&核销、创建调拨单等等
     */
    Boolean pushDataToB2B(String json, ZteDeductionPlanParamDTO dto, String projName);

    /**
     * 供应商库存Gap责任归属信息反馈
     */
    void executeInventoryDiffFeedback(List<List<CheckDifferenceIReqDTOS>> listList, List<Long> keyList, String empNo);

    void executedInventoryDiffFeedbackJob(InventoryDiffFeedbackJobDTO jobDTO);

    /**
     * 定时将INFO完成领料数据推送标准仓与WMS
     */
    void executeTransferInfoToWmsJob(String xEmpNo, String xAuthValue) throws Exception;

    List<ZteSnInventoryQueryDTO> queryInventoryFromStorageCenter(String requestDto);
}
