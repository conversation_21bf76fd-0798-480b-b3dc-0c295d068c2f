package com.zte.application.datawb;

import com.zte.domain.model.infordt.Lotxlocxid;
import com.zte.interfaces.infordt.dto.LotxlocxidDTO;

import java.util.List;

public interface LotxlocxidService {
	/**
	 * 根据仓库、物料代码查询统计物料代码的可用库存和隔离库存
	 * @param lotxlocxidDTO
	 * @return
	 */
	List<Lotxlocxid> getStockQty(LotxlocxidDTO lotxlocxidDTO);

	List<Lotxlocxid> getStockQtyByBrand(LotxlocxidDTO lotxlocxidDTO);

	/**
	 * 根据仓库、物料代码、状态查询库存数量并返回仓库id
	 * @param lotxlocxidDTO
	 * @return
	 */
	List<Lotxlocxid> getStockQtyWithWisId(LotxlocxidDTO lotxlocxidDTO);
}
