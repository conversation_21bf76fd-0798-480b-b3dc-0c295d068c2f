package com.zte.application.datawb.impl;

import com.zte.application.datawb.BoqBomBoxPrintInfoService;
import com.zte.application.datawb.BoqBomPrintInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.datawb.BoqBomBoxPrintInfoRepository;
import com.zte.interfaces.dto.BoxUpBillInfoDTO;
import com.zte.interfaces.dto.ErrDTO;
import com.zte.interfaces.dto.EuItemDTO;
import com.zte.interfaces.dto.ItemDTO;
import com.zte.interfaces.dto.PrintInfoInDTO;
import com.zte.interfaces.dto.PrintInfoOutDTO;
import com.zte.itp.msa.datasource.DatabaseContextHolder;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AU-BBU-普通BOQ打印內容微服務傳送+ eu 打印内容微服务传送 信息
 */
@Service
@DataSource(DatabaseType.WMSPRODLMS)
public class BoqBomBoxPrintInfoServiceImpl implements BoqBomBoxPrintInfoService {
    @Autowired
    private BoqBomBoxPrintInfoRepository boqBomBoxPrintInfoRepository;

    @Autowired
    private BoqBomPrintInfoService boqBomPrintInfoService;

    /**
     * <AUTHOR>
     * 函数总入口
     * @params dto 入参实体类
     */
    public PrintInfoOutDTO boqBomBoxPrintInfo(PrintInfoInDTO params) {
        PrintInfoOutDTO result = new PrintInfoOutDTO();
        ErrDTO err = new ErrDTO();

        //0.入参校验
        PrintInfoOutDTO res = paramInCheck(params);
        if (res != null && Constant.KEY_E.equals(res.getErrDTO().getProcessStatus())) {
            res.setEuItemDTOList(null);
            res.setItemDtoList(null);
            return res;//不满足校验提前终止
        }

        //1、按箱号查询装箱信息，2个打印数据的公共开始
        List<BoxUpBillInfoDTO> boxUpBillInfoDTOList = getBoxUpBillInfo(params);
        if (CollectionUtils.isEmpty(boxUpBillInfoDTOList)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.NO_FIND_BOX_INFO)); //未查询到装箱信息，请确认
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            result.setEuItemDTOList(null);
            result.setItemDtoList(null);
            return result;
        }

        //2、要打印的Bom数据
        Map<String, Object> outMap = new HashMap<>();
        outMap.put(Constant.STRING_XN, Constant.FLAG_N);//不是虚拟箱
        outMap.put(Constant.STRING_ZC, Constant.FLAG_Y);//正常结束
        outMap.put(Constant.STRING_YY, Constant.FLAG_Y);//模板语言
        outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_Y);//是否找到箱对应的BOQ信息
        outMap.put("isStop", Constant.FLAG_N);//再次封装用
        Map<String, Object> outMap3 = getBoxInfo(boxUpBillInfoDTOList, outMap);
        //打印是否提前终止判断
        PrintInfoOutDTO res2 = getBoqPrintInfo(outMap3, result);//res2保证了一定不为空，所以防止空指针异常。
        if (res2 != null && Constant.KEY_E.equals(res2.getErrDTO().getProcessStatus())) {//防止空指针异常
            res2.setEuItemDTOList(null);
            res2.setItemDtoList(null);
            return res2;//提前终止，所以都为null
        }
        PrintInfoOutDTO printInfoOutDTO = (PrintInfoOutDTO) outMap3.get("printInfoOutDTO");
        if (printInfoOutDTO == null) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.NO_FIND_BOX_INFO)); //未查询到装箱信息，请确认
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            result.setEuItemDTOList(null);
            result.setItemDtoList(null);
            return result;
        }
        //如果普通箱，不用进后面的eu箱逻辑了。//看bill_number 开头 E，B，C 开头可知
        if (!printInfoOutDTO.getBillNumber().startsWith(Constant.KEY_E)) {
            err.setProcessStatus(Constant.KEY_S);
            printInfoOutDTO.setErrDTO(err);
            printInfoOutDTO.setEuItemDTOList(null);
            return printInfoOutDTO;
        }

        //3 、eu打印数据
        Map<String, Object> outMap5 = new HashMap<>();
        outMap5.put(Constant.STRING_IS_INFO, Constant.FLAG_Y);//是否找到箱对应的BOQ信息
        outMap5.put(Constant.STRING_IS_INFOC, Constant.FLAG_Y);//是否找到箱对应的CBOQ信息
        Map<String, Object> outMap4 = getBoxBOQInfoForEU(boxUpBillInfoDTOList, outMap5);
        PrintInfoOutDTO res3 = getBoqPrintInfoEu(outMap4, result);//入参判空处理
        if (res3 != null && Constant.KEY_E.equals(res3.getErrDTO().getProcessStatus())) {
            res3.setEuItemDTOList(null);
            return res3;//不满足提前终止
        }
        PrintInfoOutDTO printInfoOutDTO2 = (PrintInfoOutDTO) outMap4.get("printInfoOutDTO");
        if (printInfoOutDTO2 != null) {
            printInfoOutDTO.setEuItemDTOList(printInfoOutDTO2.getEuItemDTOList());
        }

        //如果上面过程不满足，中间就会提前return终止。否则都走完了，这里就返回true
        err.setProcessStatus(Constant.KEY_S);
        err.setCode(Constant.SUCCESS_CODE);//0000
        printInfoOutDTO.setErrDTO(err);
        return printInfoOutDTO;
    }

    public PrintInfoOutDTO getBoqPrintInfo(Map<String, Object> outMap, PrintInfoOutDTO result) {
        ErrDTO err = new ErrDTO();
        if (MapUtils.isEmpty(outMap) || outMap.get(Constant.STRING_IS_INFO).equals(Constant.FLAG_N)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.NO_BOM_INFORMATION_FROM_BOXINFO));//未找到箱对应的BOQ信息，无法打印
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        } else if (outMap.get(Constant.STRING_YY).equals(Constant.FLAG_N)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.TEMPLATE_INFO_IS_NULL));//模板语言不能为空
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        } else if (!outMap.get(Constant.STRING_ZC).equals(Constant.FLAG_Y)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.BOM_NOT_LOADED));//该箱未完成装箱扫描，不允许打印
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        } else if (outMap.get(Constant.STRING_XN).equals(Constant.FLAG_Y)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.BOX_IS_VIRTURE));//此箱为虚拟箱，无法打印，请确认
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        } else {
            err.setProcessStatus(Constant.KEY_S);
            err.setCode(Constant.SUCCESS_CODE);//0000
            result.setErrDTO(err);
            return result;

        }
    }

    public PrintInfoOutDTO getBoqPrintInfoEu(Map<String, Object> outMap, PrintInfoOutDTO result) {
        ErrDTO err = new ErrDTO();
        if (MapUtils.isEmpty(outMap) || outMap.get(Constant.STRING_IS_INFO).equals(Constant.FLAG_N)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.NO_BOM_INFORMATION_FROM_BOXINFO));//未找到箱对应的BOQ信息，无法打印
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        } else if (outMap.get(Constant.STRING_IS_INFOC).equals(Constant.FLAG_N)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.NO_CBOM_INFORMATION_FROM_BOXINFO));//未找到箱对应的CBOQ信息，无法打印
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        } else {
            err.setProcessStatus(Constant.KEY_S);
            err.setCode(Constant.SUCCESS_CODE);//0000
            result.setErrDTO(err);
            return result;
        }
    }

    /**
     * <AUTHOR>
     * 0 入参校验
     * @params dto 入参实体类
     */
    public PrintInfoOutDTO paramInCheck(PrintInfoInDTO params) {
        PrintInfoOutDTO result = new PrintInfoOutDTO();
        ErrDTO err = new ErrDTO();

        if (params == null) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg((Constant.NO_DATA));
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        }

        String billNo = params.getBillNo();
        String orgId = params.getOrgId();

        if (StringUtils.isBlank(billNo)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.BILL_BOX_IS_NULL));
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        }
        if (StringUtils.isBlank(orgId)) {
            err.setCode(Constant.BUSINESS_ERROR_CODE);
            err.setMsg(CommonUtils.getLmbMessage(MessageId.ORGNIIZATON_IS_EMPTY));
            err.setProcessStatus(Constant.KEY_E);
            result.setErrDTO(err);
            return result;
        }

        //上面如果过程不满足就终止。如果走到这里，就是OK
        err.setCode(Constant.SUCCESS_CODE);//0000
        err.setProcessStatus(Constant.KEY_S);
        result.setErrDTO(err);
        return result;
    }

    /**
     * <AUTHOR>
     * 1、按箱号查询装箱信息
     * @params dto 入参实体类
     */
    public List<BoxUpBillInfoDTO> getBoxUpBillInfo(PrintInfoInDTO params) {
        return boqBomBoxPrintInfoRepository.boxBarcodePrint(params);
    }

    /**
     * <AUTHOR>
     * 2循环列表，获取要打印的数据
     * @params dto 入参实体类
     */
    public Map<String, Object> getBoxInfo(List<BoxUpBillInfoDTO> boxUpBillInfoDTOList, Map<String, Object> outMap) {

        int rowCount = 0;

        for (BoxUpBillInfoDTO drBoxUp : boxUpBillInfoDTOList) {
            rowCount++;
            //语言等校验开始
            String strTempLan = drBoxUp.getTemplateLanguage();//1 ch,3 en,2 ch&en
            Boolean language = strTempLan.equals(Constant.STRING_CN) || strTempLan.equals(Constant.STRING_US);

            if (!language) {
                outMap.put(Constant.STRING_YY, Constant.FLAG_N);//模板语言不能为空
                return outMap;
            }

            // 判断是否虚拟箱，虚拟箱就不操作
            if (drBoxUp.getMergeBillFlag().equals(Constant.STRING_VR)) {
                outMap.put(Constant.STRING_XN, Constant.FLAG_Y);//此箱为虚拟箱，无法打印，请确认
                return outMap;
            }

            DatabaseContextHolder.setDatabaseType(DatabaseType.SFC);
            Map<String, Object> checkScanFlag = this.boqBomPrintInfoService.checkScanFlag(drBoxUp.getBillID());
            DatabaseContextHolder.setDatabaseType(DatabaseType.WMSPRODLMS);

            if (CommonUtils.isEmpty(checkScanFlag) ||
                    !Constant.STRING_NORMAL_END.equals(checkScanFlag.get(Constant.SCAN_FLAG))) {
                outMap.put(Constant.STRING_ZC, Constant.FLAG_N);//此箱未完成装箱扫描，无法打印，请确认
                return outMap;
            }

            //查询箱物料信息
            Map<String, Object> map = new HashMap<>();
            map.put("billNo", drBoxUp.getBillNumber());
            List<BoxUpBillInfoDTO> ds = boqBomBoxPrintInfoRepository.getBoxUpBOQ(map);//龙哥给的XML不对，debug+测试就可以验证了。返回结果先用 ItemDTO，如果不行，才用公共对象BoxUpBillInfoDTO
            if (CollectionUtils.isEmpty(ds)) {
                outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_N);//未找到箱对应的BOQ信息，无法打印，请确认
                return outMap;
            }

            //封装---》最终打印函数封装
            PrintInfoOutDTO printInfoOutDTO = boqTableInfo(ds, drBoxUp, rowCount, strTempLan);
            //顺利取到数据，进if
            if (!CollectionUtils.isEmpty(printInfoOutDTO.getItemDtoList())) {
                //只有正常才会到这里
                outMap.put("printInfoOutDTO", printInfoOutDTO);
                return outMap;
            }
            //只有异常了才会走到这里，此处应该跟上面的if配对，为了省复杂度，在if里面加了return等效处理。
            outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_N);//未找到箱对应的BOQ信息，无法打印，请确认
        }
        return outMap;
    }

    //最终打印函数封装,drBoxUp 2 个SQL，大boss，  ds 第3个SQL
    public PrintInfoOutDTO boqTableInfo(List<BoxUpBillInfoDTO> ds, BoxUpBillInfoDTO drBoxUp, int rowCount, String strTempLan) {
        PrintInfoOutDTO printInfoOutDTO = new PrintInfoOutDTO();
        ArrayList<ItemDTO> itemDTOList = new ArrayList<>();//保存接口数据

        // PTO层，新箱BOQ水晶报表需要获取PTO代码对应的BOQ物料名称，在第一层中设置
        String ptoBoqNameCn = "";

        for (int i = 0; i < ds.size(); i++) {
            ItemDTO drTemp = new ItemDTO();//保存返回的接口数据
            BoxUpBillInfoDTO newRow = ds.get(i);//每行数据,第3个查询SQL
            if (newRow == null) {
                continue;
            }

            // 如果是装箱单的BOQ信息，则层次为一个标志值，则清空此标志值
            Map<String, Object> map = setPtoBoqNameCn(i, ptoBoqNameCn, ds, newRow, strTempLan);

            if (map.get(Constant.STRING_STOP).equals(Constant.FLAG_Y)) {
                ptoBoqNameCn = (String) map.get("ptoBoqNameCn");
                continue;
            }

            String strLayer = newRow.getLevel();//每行数据

            // 装箱单数据
            if (StringUtils.isBlank(strLayer) || !strLayer.contains(Constant.DOT_T)) {
                drTemp.setLevel(Constant.STR_NUMBER_ONE);
                drTemp.setItemCodeBoq(newRow.getItemCode());

            } else {
                //2,3,4，实际打印也只有这里的数据。1级不管的
                setItemCodeBoq(strLayer, drTemp, newRow);
            }

            printInfoOutDTO.setBillId(String.valueOf(rowCount));

            // PTO层，新箱BOQ水晶报表需要获取PTO代码对应的BOQ物料名称，在第一层中设置
            printInfoOutDTO.setPtoBoqNameCn(ptoBoqNameCn);
            printInfoOutDTO.setBillNumber(drBoxUp.getBillNumber());
            //c# drTemp水晶报表的象征，表示了我接口的 itemDtoList 和printInfoOutDTO,这里字段问题时候

            printInfoOutDTO.setMemo(drBoxUp.getMemo());
            printInfoOutDTO.setMfgSiteNameEquipment(drBoxUp.getMfgSiteNameEquipment());
            printInfoOutDTO.setContractNumber(drBoxUp.getContractNumber());

            setItemMemoBoq(drTemp, newRow);

            printInfoOutDTO.setEntityName(drBoxUp.getEntityName());

            printInfoOutDTO.setCusProjectName(newRow.getCusProjectName());
            printInfoOutDTO.setMfgSiteCode(newRow.getMfgSiteCode());

            drTemp.setModuleCodeBoq(newRow.getModuleZyCode());

            setParams(strTempLan, drBoxUp, printInfoOutDTO, drTemp, newRow);

            // 打印条码
            drTemp.setItemBarcodeBoq(newRow.getItemBarcode());

            // 客户名称 箱单客户名称字段
            printInfoOutDTO.setEndUser(drBoxUp.getEndUser());
            // 工程交付集
            printInfoOutDTO.setEngineeringDelivery(drBoxUp.getEngineeringDeliverySet());
            drTemp.setSalItemBoq(newRow.getCmsCode());

            setBomProItemBoq(strTempLan, drTemp, newRow);

            itemDTOList.add(drTemp);

        }
        printInfoOutDTO.setStrTempLan(strTempLan);//中英文模板区分
        printInfoOutDTO.setItemDtoList(itemDTOList);
        return printInfoOutDTO;
    }

    public Map<String, Object> setPtoBoqNameCn(int i, String ptoBoqNameCn, List<BoxUpBillInfoDTO> ds, BoxUpBillInfoDTO newRow, String strTempLan) {
        // 获取层次
        String level = (ds.get(i).getLevel()).trim();

        Map<String, Object> map = new HashMap<>();
        map.put(Constant.STRING_STOP, Constant.FLAG_N);
        if (Constant.STRING_BOXUP_FLAG.equals(level)) {
            ds.get(i).setLevel("");
        } else if (StringUtils.isBlank(level) || !level.contains(Constant.DOT_T)) {
            // PTO层，新箱BOQ水晶报表需要获取PTO代码对应的BOQ物料名称，在第一层中设置
            ptoBoqNameCn = newRow.getPtoBoqName();
            if (StringUtils.isBlank(ptoBoqNameCn)) {
                ptoBoqNameCn = newRow.getItemName();
            }

            //上面默认中文，如果是英文再取英文
            if (!strTempLan.equals(Constant.STRING_CN)) {
                ptoBoqNameCn = newRow.getPtoBoqEnglishName();
                if (StringUtils.isBlank(ptoBoqNameCn)) {
                    ptoBoqNameCn = newRow.getItemEnglishName();
                }
            }
            map.put(Constant.STRING_STOP, Constant.FLAG_Y);
        }
        map.put("ptoBoqNameCn", ptoBoqNameCn);
        return map;
    }

    public void setItemCodeBoq(String strLayer, ItemDTO drTemp, BoxUpBillInfoDTO newRow) {
        int intFirstLevel = strLayer.indexOf(Constant.DOT_T);
        int intLastLevel = strLayer.lastIndexOf(Constant.DOT_T);
        // 如果不相等，则是3or4层关系
        if (intFirstLevel != intLastLevel) {
            String strThirdSeq = strLayer.substring(intFirstLevel + 1, intLastLevel);
            // 第四层
            if (strThirdSeq.contains(Constant.DOT_T)) {
                drTemp.setLevel("4");
                drTemp.setItemCodeBoq(newRow.getItemCode());

            } else {
                // 第三层
                drTemp.setLevel("3");
                drTemp.setItemCodeBoq(newRow.getItemCode());
            }
        } else {
            // 第二层
            drTemp.setLevel("2");
            // 物料备注
            String itemMemo = newRow.getMemo();
            // 物料装在其他箱中的所有箱（用逗号隔开）
            String boxNoAllItem = newRow.getBxoNoAllItem();

            // 如果物料有装在其他箱中，则封装物料备注
            if (!StringUtils.isBlank(boxNoAllItem)) {
                newRow.setMemo(itemMemo + (Constant.STR_IN_OTHER));
            }

            drTemp.setItemCodeBoq(newRow.getItemCode());
        }
    }

    public void setParams(String strTempLan, BoxUpBillInfoDTO drBoxUp, PrintInfoOutDTO printInfoOutDTO, ItemDTO drTemp, BoxUpBillInfoDTO newRow) {
        if (strTempLan.equals(Constant.STRING_CN)) {
            printInfoOutDTO.setBillCreator(drBoxUp.getUserName());
        } else {
            printInfoOutDTO.setBillCreator(drBoxUp.getEmpNo());
        }

        printInfoOutDTO.setBoxTypeName(drBoxUp.getBoxTypeName());

        Map<String, Object> paramMap = getBoqProductItemData(newRow);
        printInfoOutDTO.setSiteAddressThree(String.valueOf(paramMap.get("site")));
        printInfoOutDTO.setClientPo(drBoxUp.getClientPo());

        if (strTempLan.equals(Constant.STRING_CN)) {
            // 销售物料名称
            drTemp.setSalItemBoq((String) paramMap.get("boqSalItemChn"));
            // 单位
            drTemp.setBomUnitBoq(String.valueOf(paramMap.get("unit")));
            // 生产物料名称
            drTemp.setBomProItemBoq(String.valueOf(paramMap.get("bomProItemChn")));
            // 数量
            drTemp.setBomQtyBoq(String.valueOf(paramMap.get("bomBoqQty")));
        } else {
            // 销售物料名称
            drTemp.setSalItemBoq((String) paramMap.get("boqSalItemEn"));
            // 生产物料名称
            drTemp.setBomProItemBoq(String.valueOf(paramMap.get("bomProItemEn")));
            // 数量
            drTemp.setBomQtyBoq(String.valueOf(paramMap.get("bomBoqQty")));
            // 单位
            drTemp.setBomUnitBoq(String.valueOf(paramMap.get("unitEn")));
        }


        // 针对辅箱，装箱物料的数量为零，为改为"－"
        boolean bool = (Constant.STR_NUMBER_ZERO.equals(drTemp.getBomQtyBoq().trim()) ||
                StringUtils.isBlank(drTemp.getBomQtyBoq()) || Constant.NULL.equals(drTemp.getBomQtyBoq()));
        if (bool) {
            drTemp.setBomQtyBoq("－");
        }

        // PTO代码
        if (!StringUtils.isBlank(newRow.getDevicesSiteCode())) {
            printInfoOutDTO.setPtoCode(newRow.getDevicesSiteCode());
        }

        // 判断是否为空或者是System.DBNull
        if (StringUtils.isBlank(drBoxUp.getMfgSiteCode())) {
            printInfoOutDTO.setMfgSiteCode("");
        } else {
            printInfoOutDTO.setMfgSiteCode(drBoxUp.getMfgSiteCode());
        }
    }

    public void setBomProItemBoq(String strTempLan, ItemDTO drTemp, BoxUpBillInfoDTO newRow) {
        if (strTempLan.equals(Constant.STRING_CN)) {
            setBomProItemBoqCn(drTemp, newRow);
        } else {
            //英文
            if (!drTemp.getLevel().equals(Constant.STR_NUMBER_FOUR)) {
                if (StringUtils.isBlank(newRow.getPtoBoqEnglishName())) {
                    drTemp.setBomProItemBoq(newRow.getItemEnglishName());
                } else {
                    drTemp.setBomProItemBoq(newRow.getPtoBoqEnglishName());
                }
            } else {
                drTemp.setBomProItemBoq(newRow.getItemEnglishName());
            }
        }
    }

    public void setBomProItemBoqCn(ItemDTO drTemp, BoxUpBillInfoDTO newRow) {
        if (!drTemp.getLevel().equals(Constant.STR_NUMBER_FOUR)) {
            if (StringUtils.isBlank(newRow.getPtoBoqName())) {
                drTemp.setBomProItemBoq(newRow.getItemName());
            } else {
                drTemp.setBomProItemBoq(newRow.getPtoBoqName());
            }
        } else {
            drTemp.setBomProItemBoq(newRow.getItemName());//这个字段要检查，是否set，get对的上，前面写错的
        }
    }

    public void setItemMemoBoq(ItemDTO drTemp, BoxUpBillInfoDTO newRow) {
        String eccBrand = "";
        if (!StringUtils.isBlank(newRow.getEccBrand())) {
            eccBrand = newRow.getEccBrand();
        }

        String memo = "";
        if (!StringUtils.isBlank(newRow.getMemo())) {
            memo = newRow.getMemo();
        }

        String remark = eccBrand + "," + memo;
        if (StringUtils.isBlank(eccBrand) && StringUtils.isBlank(memo)) {
            remark = "";
        }

        //头有
        if (remark.startsWith(Constant.COMMA)) {
            String remark2 = remark.substring(1);
            //尾巴有
            if (remark.endsWith(Constant.COMMA)) {
                String remark3 = remark2.substring(0, remark2.length() - 1);
                drTemp.setItemMemoBoq(remark3);
            } else {
                //尾巴没有
                drTemp.setItemMemoBoq(remark2);
            }
        }
        //头没有，尾有
        if (!remark.startsWith(Constant.COMMA) && remark.endsWith(Constant.COMMA)) {
            String remark3 = remark.substring(0, remark.length() - 1);
            drTemp.setItemMemoBoq(remark3);
        }
        //头没有，尾没有
        if (!remark.startsWith(Constant.COMMA) && !remark.endsWith(Constant.COMMA)) {
            drTemp.setItemMemoBoq(remark);
        }
    }

    public Map<String, Object> getBoqProductItemData(BoxUpBillInfoDTO newRow) {
        Map<String, Object> map = new HashMap<>();

        // 获取识别生产/销售/生产销售物料的字段
        String importItemType = newRow.getImportItemType();
        if (StringUtils.isBlank(importItemType)) {
            importItemType = "";
        }
        String siteCityAddr = newRow.getSiteCityAddress();

        // 如果在SQL中拼捧的站点不为空，则进行处理
        siteCityAddr(siteCityAddr, map);

        // 分类设置物料
        Boolean bool = (Constant.STRING_Z.equals(importItemType) || Constant.DOT_N.equals(importItemType) ||
                Constant.STRING_EMPTY.equals(importItemType));
        if (Constant.REASON_ID.equals(importItemType)) {
            // 销售物料名称
            map.put("boqSalItemChn", "-");
            map.put("boqSalItemEn", "-");
            // 生产物料名称
            map.put("bomProItemChn", "-");
            map.put("bomProItemEn", "-");
            // 数量
            map.put("bomBoqQty", "-");
            // 单位
            map.put("unit", "-");
            map.put("unitEn", "-");
        } else if (Constant.FLAG_Y.equals(importItemType) || Constant.STR_TWENTY.equals(importItemType)) {
            // 销售物料名称
            map.put("boqSalItemChn", newRow.getPtoBoqName());
            map.put("boqSalItemEn", newRow.getPtoBoqEnglishName());
            // 生产物料名称
            map.put("bomProItemChn", "-");
            map.put("bomProItemEn", "-");
            // 数量
            map.put("bomBoqQty", newRow.getBoxUpQty());
            // 单位
            map.put("unit", newRow.getUnit());
            map.put("unitEn", newRow.getUnitEn());
        } else if (bool) {
            // 销售物料名称
            map.put("boqSalItemChn", "-");//这里已有，小心是否有问题
            map.put("boqSalItemEn", "-");
            // 生产物料名称
            map.put("bomProItemChn", newRow.getItemName());//这里copy上面的，小心是否有问题
            map.put("bomProItemEn", newRow.getItemEnglishName());
            // 数量
            map.put("bomBoqQty", newRow.getBoxUpQty());
            // 单位
            map.put("unit", newRow.getUnit());
            map.put("unitEn", newRow.getUnitEn());
        } else if (Constant.STRING_B.equals(importItemType)) {
            // 销售物料名称
            map.put("boqSalItemChn", newRow.getPtoBoqName());//这里已有，小心是否有问题
            map.put("boqSalItemEn", newRow.getPtoBoqEnglishName());
            // 生产物料名称
            map.put("bomProItemChn", newRow.getItemName());
            map.put("bomProItemEn", newRow.getItemEnglishName());
            // 数量
            map.put("bomBoqQty", newRow.getBoxUpQty());
            // 单位
            map.put("unit", newRow.getUnit());
            map.put("unitEn", newRow.getUnitEn());
        } else {
            //不处理，防止后面去这些字段有空指针异常
            map.put("boqSalItemChn", Constant.STRING_EMPTY);
            map.put("boqSalItemEn", Constant.STRING_EMPTY);
            // 生产物料名称
            map.put("bomProItemChn", Constant.STRING_EMPTY);
            map.put("bomProItemEn", Constant.STRING_EMPTY);
            // 数量
            map.put("bomBoqQty", Constant.STRING_EMPTY);
            // 单位
            map.put("unit", Constant.STRING_EMPTY);
            map.put("unitEn", Constant.STRING_EMPTY);
        }

        return map;
    }

    public void siteCityAddr(String siteCityAddr, Map<String, Object> map) {
        if (!StringUtils.isBlank(siteCityAddr.trim())) {
            // 两个逗号时替换为一个
            siteCityAddr = siteCityAddr.replace(",,", Constant.COMMA);

            // 首字符为逗号，则去掉=[1,length-1]
            if (siteCityAddr.startsWith(Constant.COMMA)) {
                siteCityAddr = siteCityAddr.substring(1);
            }

            // 最后一个字符为逗号，则去掉=[0,length-2]
            if (siteCityAddr.endsWith(Constant.COMMA)) {
                siteCityAddr = siteCityAddr.substring(0, siteCityAddr.length() - 1);
            }
        }

        // 三级站点地址，对应的水晶报表数据集字段为：SITE_ADDRESS_THREE
        map.put("site", siteCityAddr);
    }

    /**
     * <AUTHOR>
     * 3获取要打印的eu数据
     * @params dto 入参实体类
     */
    public Map<String, Object> getBoxBOQInfoForEU(List<BoxUpBillInfoDTO> boxUpBillInfoDTOList,
                                                  Map<String, Object> outMap) {
        PrintInfoOutDTO printInfoOutDTO = new PrintInfoOutDTO();
        ArrayList<EuItemDTO> euItemDTOList = new ArrayList<>();
        for (BoxUpBillInfoDTO mDrBoxup : boxUpBillInfoDTOList) {
            //查询箱物料信息，list源头
            Map<String, Object> map = new HashMap<>();
            map.put("billNo", mDrBoxup.getBillNumber());
            List<BoxUpBillInfoDTO> mDsItem = boqBomBoxPrintInfoRepository.getBoxBOQInfoForEU(map);
            if (CollectionUtils.isEmpty(mDsItem)) {
                outMap.put(Constant.STRING_IS_INFO, Constant.FLAG_N);//未找到箱对应的BOQ信息，无法打印，请确认
                return outMap;
            }

            mergeCodeItem(mDsItem);

            ArrayList<String> listCBom = new ArrayList<>();
            for (int j = 0; j < mDsItem.size(); j++) {
                String moduleZyCode = mDsItem.get(j).getModuleZyCode();
                String level1 = mDsItem.get(j).getLevel();//四级物料，且找不到
                String[] configIds = level1.split(Constant.POINT_P);

                Boolean b = (level1.split(Constant.POINT_P).length == Constant.INT_4 && listCBom.indexOf(moduleZyCode + "#" + configIds[2]) == Constant.INT_B1 && !StringUtils.isBlank(moduleZyCode));
                if (b) {
                    listCBom.add(moduleZyCode + "#" + configIds[2]);//把數量改為层次
                }
            }
            if (listCBom.size() == Constant.INT_0) {
                outMap.put(Constant.STRING_IS_INFOC, Constant.FLAG_N);
                return outMap;//未找到箱对应的CBOQ信息，无法打印

            }

            //eu真正封装2个数据的地方
            euItemDTOListAdd(listCBom, mDsItem, mDrBoxup, euItemDTOList);
        }
        printInfoOutDTO.setEuItemDTOList(euItemDTOList);
        outMap.put("printInfoOutDTO", printInfoOutDTO);
        return outMap;
    }

    public void mergeCodeItem(List<BoxUpBillInfoDTO> dt) {
        for (int i = 0; i < dt.size(); i++) {
            for (int j = i + 1; j < dt.size(); j++) {
                //上面判空处理封装函数，否则报错
                Boolean validateBool = validateParam(dt, i, j);
                if (validateBool) {
                    dt.get(i).setItemBarcode(dt.get(i).getItemBarcode() + " " + dt.get(j).getItemBarcode());
                    setNumber(dt, i, j);
                    dt.remove(j);
                    j = j - 1;
                }
            }
        }
    }

    public void setNumber(List<BoxUpBillInfoDTO> dt, int i, int j) {

        float num1 = 0;
        float num2 = 0;
        if (dt.get(i).getBoxUpQty() != null) {
            num1 = dt.get(i).getBoxUpQty();
        }
        if (dt.get(j).getBoxUpQty() != null) {
            num2 = dt.get(j).getBoxUpQty();
        }
        dt.get(i).setBoxUpQty(num1 + num2);

        int number1 = 0;
        int number2 = 0;
        //不为null,就取值，否则默认为0，integer这样判断，不能转string再判string为空无效的
        if (dt.get(i).getZyLineNumber() != null) {
            number1 = dt.get(i).getZyLineNumber();
        }
        if (dt.get(j).getZyLineNumber() != null) {
            number2 = dt.get(j).getZyLineNumber();
        }
        dt.get(i).setZyLineNumber(number1 + number2);


    }

    public Boolean validateParam(List<BoxUpBillInfoDTO> dt, int i, int j) {
        String itemId1 = "";
        String itemId2 = "";
        //不为null,就取值，否则默认为“”,注意integer=null不能下面转string判空，测试过无效的
        if (dt.get(i).getItemId() != null) {
            itemId1 = String.valueOf(dt.get(i).getItemId());
        }
        if (dt.get(j).getItemId() != null) {
            itemId2 = String.valueOf(dt.get(j).getItemId());
        }


        String suCode1 = "";
        String suCode2 = "";
        if (!StringUtils.isBlank(dt.get(i).getSuCode())) {
            suCode1 = dt.get(i).getSuCode();
        }
        if (!StringUtils.isBlank(dt.get(j).getSuCode())) {
            suCode2 = dt.get(j).getSuCode();
        }

        String cBomCode1 = "";
        String cBomCode2 = "";
        if (!StringUtils.isBlank(dt.get(i).getcBomCode())) {
            cBomCode1 = dt.get(i).getcBomCode();
        }
        if (!StringUtils.isBlank(dt.get(j).getcBomCode())) {
            cBomCode2 = dt.get(j).getcBomCode();
        }

        //return suCode1.equals(suCode2) && cBomCode1.equals(cBomCode2);//验证过，为空也OK的
        return itemId1.equals(itemId2) && suCode1.equals(suCode2) && cBomCode1.equals(cBomCode2);//string=null 用equal也是OK的
    }

    public void euItemDTOListAdd(List<String> listCBom, List<BoxUpBillInfoDTO> mDsItem, BoxUpBillInfoDTO mDrBoxup, List<EuItemDTO> euItemDTOList) {
        for (int k = 0; k < listCBom.size(); k++) {
            List<BoxUpBillInfoDTO> drCBomList = new ArrayList<>();

            //su物料代码
            String moduleZyCode = listCBom.get(k).split("#")[0];
            //层次
            String configDetailId = listCBom.get(k).split("#")[1];

            addParam(mDsItem, moduleZyCode, configDetailId, drCBomList);

            // 创建新行
            EuItemDTO drTemp = new EuItemDTO();

            if (!Constant.STRING_BOXUP_FLAG.equals(drCBomList.get(0).getBoxUpFlag().trim())) {
                continue;
            }

            //获取语言状态
            String strTempLan = mDrBoxup.getTemplateLanguage();

            //eu 1
            drTemp.setModuleCodeBoqEu(drCBomList.get(0).getModuleZyCode());
            //eu2
            int zyLineNumber = 0;
            for (int n = 0; n < drCBomList.size(); n++) {
                zyLineNumber = zyLineNumber + drCBomList.get(n).getZyLineNumber();
            }
            drTemp.setBomQtyBoqEu(String.valueOf(zyLineNumber));

            // 中文
            if (strTempLan.equals(Constant.STRING_CN)) {
                drTemp.setBomUnitBoqEu(drCBomList.get(0).getCmsUnitCn());
                drTemp.setBomProItemBoqEu(drCBomList.get(0).getCmsCode());
            }
            //英文
            else {
                drTemp.setBomUnitBoqEu(drCBomList.get(0).getCmsUnitEn());
                drTemp.setBomProItemBoqEu(drCBomList.get(0).getCmsCodeEn());
            }

            // 物料代码
            drTemp.setItemCodeBoqEu("");
            // 序列号（条码）
            drTemp.setItemBarcodeBoqEu("");

            setItemMemoBoqEu(drCBomList, strTempLan, drTemp);

            //物料代号 eu8
            drTemp.setSalItemBoqEu(drCBomList.get(0).getSuNumber());
            drTemp.setLevel(Constant.STR_NUMBER_THREE);
            euItemDTOList.add(drTemp);

            euItemDTOListAdd2(drCBomList, strTempLan, euItemDTOList);
        }
    }

    public void addParam(List<BoxUpBillInfoDTO> mDsItem, String moduleZyCode, String num, List<BoxUpBillInfoDTO> drCBomList) {
        for (int m = 0; m < mDsItem.size(); m++) {

            String moduleZyCode1 = "";
            //不为null，就用原来的，否则用默认的“”
            if (!StringUtils.isBlank(mDsItem.get(m).getModuleZyCode())) {
                moduleZyCode1 = mDsItem.get(m).getModuleZyCode();
            }
            String moduleZyCode2 = "";
            if (!StringUtils.isBlank(moduleZyCode)) {
                moduleZyCode2 = moduleZyCode;
            }

            String num1 = "";
            if (!StringUtils.isBlank(num)) {
                num1 = num;
            }
            num1 = Constant.POINT + num1 + Constant.POINT;

            String num2 = "";
            //层次代替原来的数量
            if (null != mDsItem.get(m).getLevel()) {
                num2 = String.valueOf(mDsItem.get(m).getLevel());
            }

            if (moduleZyCode1.equals(moduleZyCode2) && num2.contains(num1)) {
                drCBomList.add(mDsItem.get(m));
            }
        }
    }

    public void setItemMemoBoqEu(List<BoxUpBillInfoDTO> drCBomList, String strTempLan, EuItemDTO drTemp) {
        String memo = "";
        if (!StringUtils.isBlank(drCBomList.get(0).getMemo())) {
            memo = drCBomList.get(0).getMemo();
        }

        if (!StringUtils.isBlank(drCBomList.get(0).getIsMask())) {
            // 中文
            if (strTempLan.equals(Constant.STRING_CN)) {//eu7
                drTemp.setItemMemoBoqEu(Constant.STR_MAIN_MATERIAL + memo);
            }
            //英文
            else {
                drTemp.setItemMemoBoqEu("Master Material" + memo);
            }
        } else {
            drTemp.setItemMemoBoqEu(memo);
        }
    }

    public void euItemDTOListAdd2(List<BoxUpBillInfoDTO> drCBomList, String strTempLan, List<EuItemDTO> euItemDTOList) {
        for (BoxUpBillInfoDTO dr : drCBomList) {
            EuItemDTO drTemp1 = new EuItemDTO();
            if (!Constant.STRING_BOXUP_FLAG.equals(dr.getBoxUpFlag().trim())) {
                continue;
            }
            // 获取新箱BOQ清单使用的各个参数
            Map<String, Object> htData1 = getBoqProductItemData(dr);
            drTemp1.setModuleCodeBoqEu("");
            drTemp1.setItemCodeBoqEu(dr.getItemCode());
            drTemp1.setItemBarcodeBoqEu(dr.getItemBarcode());

            setItemMemoBoqEu2(dr, strTempLan, drTemp1);

            if (strTempLan.equals(Constant.STRING_CN)) {
                drTemp1.setSalItemBoqEu(String.valueOf(htData1.get("boqSalItemChn")));
                drTemp1.setBomUnitBoqEu(String.valueOf(htData1.get("unit")));
                drTemp1.setBomProItemBoqEu(String.valueOf(htData1.get("bomProItemChn")));
                drTemp1.setBomQtyBoqEu(String.valueOf(htData1.get("bomBoqQty")));
            } else {
                drTemp1.setSalItemBoqEu(String.valueOf(htData1.get("boqSalItemEn")));
                drTemp1.setBomUnitBoqEu(String.valueOf(htData1.get("unitEn")));
                drTemp1.setBomProItemBoqEu(String.valueOf(htData1.get("bomProItemEn")));
                drTemp1.setBomQtyBoqEu(String.valueOf(htData1.get("bomBoqQty")));
            }

            drTemp1.setLevel(Constant.STR_NUMBER_FOUR);
            euItemDTOList.add(drTemp1);
        }
    }

    public void setItemMemoBoqEu2(BoxUpBillInfoDTO dr, String strTempLan, EuItemDTO drTemp1) {
        String memo = "";
        if (!StringUtils.isBlank(dr.getMemo())) {
            memo = dr.getMemo();
        }
        if (!StringUtils.isBlank(dr.getIsMask())) {
            // 中文 eu7
            if (strTempLan.equals(Constant.STRING_CN)) {
                drTemp1.setItemMemoBoqEu(Constant.STR_MAIN_MATERIAL + memo);
            }
            //英文
            else {
                drTemp1.setItemMemoBoqEu("Master Material" + memo);
            }
        } else {
            drTemp1.setItemMemoBoqEu(memo);
        }
    }

}
