package com.zte.application.impl;

import com.zte.application.ArchiveBomLockingBillDataService;
import com.zte.domain.model.ArchiveBomLockingBillDataRepository;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.annotation.DataSource;
import com.zte.springbootframe.common.datasource.DatabaseType;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date : Created in 2023/8/17
 * @description : 单板锁定归档服务接口实现
 */
@Service
@DataSource(DatabaseType.SFC)
public class ArchiveBomLockingBillDataServiceImpl implements ArchiveBomLockingBillDataService {

    @Autowired
    ArchiveBomLockingBillDataRepository repository;

    @Override
    public Page<ArchiveBomLockingBillDTO> getPageByDateRange(ArchiveQueryParamDTO archiveQueryParamDTO) {
        if(null == archiveQueryParamDTO || null == archiveQueryParamDTO.getStartDate() || null == archiveQueryParamDTO.getEndDate()){
            return new Page<>();
        }
        Page<ArchiveBomLockingBillDTO> page = new Page<>(archiveQueryParamDTO.getPage(),archiveQueryParamDTO.getRows());
        page.setParams(archiveQueryParamDTO);
        page.setRows(repository.getPageByDateRange(page));
        return page;
    }

    @Override
    public ArchiveBomLockingBillDTO getByBillNo(String billNo) {
        if(StringUtils.isBlank(billNo)){
            return null;
        }
        return repository.getByBillNo(billNo);
    }

    @Override
    public List<ArchiveBomLockingBarcodeDTO> getBarcodeByBillId(String billId) {
        if(StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getBarcodeByBillId(billId);
    }

    @Override
    public List<ArchiveBomLockingPlanDTO> getPlanByBillId(String billId) {
        if(StringUtils.isBlank(billId)){
            return Collections.emptyList();
        }
        return repository.getPlanByBillId(billId);
    }
}
