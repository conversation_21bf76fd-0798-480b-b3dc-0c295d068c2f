package com.zte.application.impl;

import cn.hutool.core.collection.CollUtil;
import com.zte.application.BaggageBindingBillboardService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.InoneRemoteService;
import com.zte.interfaces.VO.BaggageBindingBillboardVO;
import com.zte.interfaces.dto.BarcodeExpandResponse;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.datawb.BaggageBindingBillboardDTO;
import com.zte.interfaces.dto.datawb.BaggageBindingBillboardQueryDTO;
import com.zte.interfaces.dto.datawb.PickListQueryDTO;
import com.zte.interfaces.dto.datawb.ProcPicklistDetail;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Service
public class BaggageBindingBillboardServiceImpl implements BaggageBindingBillboardService {

    @Resource
    private InoneRemoteService inoneRemoteService;
    @Resource
    private PsTaskExtendedService psTaskExtendedService;
    @Resource
    private SysLookupValuesService sysLookupValuesService;
    @Resource
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Value("${baggage.binding.query.time.range:1}")
    private Integer baggageBindingQueryTimeRange;


    /**
     * 查询箱包绑定看板当天数据
     * *             当日待绑定代码总数：获取当天MES所提的领料单号，根据领料单号所属任务号判断是否为阿里标模任务（取数据字典配置的阿里客户编码），
     * *             若为阿里任务则通过领料单号获取物料代码个数（不同的领料单号可能会领相同的代码，查完后需要对物料代码进行去重）
     * *             代码发料未完成数量：根据MES领料单号+物料代码获取状态不等于95的物料代码个数，
     * *             当日待绑定SN总数：根据领料单号查询原箱发料数量
     * *             未绑定SN总数：当日待绑定SN总数-已绑定数量，已绑定数量：根据领料单号查询条码中心已绑定数量相加
     * *             代码发料完成率：（当日待绑定代码总数-代码发料未完成数量）/当日待绑定代码总数
     * *             SN绑定完成率：（当日待绑定SN总数-未绑定SN总数）/当日待绑定SN总数
     *
     * @return BaggageBindingBillboardVO
     */
    @Override
    @TransmittableHeader
    public BaggageBindingBillboardVO queryCurrentDayBoard(BaggageBindingBillboardQueryDTO param) throws Exception {
        if (Objects.isNull(param.getStartTime()) || Objects.isNull(param.getEndTime())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BAGGAGE_PARAM_LOST);
        }
        // 1.获取领料单信息 // 正常领料信息 // 维修领料单
        List<ProdPickListMainDTO> prodPickList = this.getProdPickListMainList(param);
        // 2. 移除不是阿里任务的领料单数据
        this.removeNotCustomerBill(prodPickList);

        // 3. 当日待绑定代码总数 通过领料单号获取物料代码个数（不同的领料单号可能会领相同的代码，查完后需要对物料代码进行去重）
        List<String> billNumberList = prodPickList.stream().map(ProdPickListMainDTO::getBillNumber)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billNumberList)) {
            return new BaggageBindingBillboardVO();
        }
        List<ProcPicklistDetail> list = DatawbRemoteService.queryProcPickDetailBatch(billNumberList);
        List<String> itemCodeList = list.stream().map(ProcPicklistDetail::getItemCode)
                .distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        BaggageBindingBillboardVO baggageBindingBillboardVO = new BaggageBindingBillboardVO();
        // 3.1 待绑定代码总数
        baggageBindingBillboardVO.setTotalNumber(new BigDecimal(itemCodeList.size()));

        // 获取查询参数
        EdiSoSDTO ediSo = this.getEdiSosQueryParam(billNumberList, null);
        // 4. 代码发料未完成数量：根据MES领料单号+物料代码获取状态不等于95的物料代码个数,
        List<EdiSoSDTO> ediSoList = DatawbRemoteService.queryTotalNotComplete(ediSo);
        List<String> skuList = ediSoList.stream().map(EdiSoSDTO::getSku).distinct().collect(Collectors.toList());
        baggageBindingBillboardVO.setUnfinishedNumber(new BigDecimal(skuList.size()));

        // 5. 当日待绑定SN总数：根据领料单号查询原箱发料数量
        List<EdiSoSDTO> sendMaterials = DatawbRemoteService.getSendMaterials(ediSo);
        BigDecimal big = BigDecimal.ZERO;
        for (EdiSoSDTO sendMaterial : sendMaterials) {
            BigDecimal qty = sendMaterial.getQty();
            big = big.add(qty);
        }
        baggageBindingBillboardVO.setBoundNumber(big);

        //  未绑定SN总数： 当日待绑定SN总数-已绑定数量， 已绑定数量： 根据领料单号查询条码中心已绑定数量相加
        long boundLong = this.queryBoundedNumber(sendMaterials);
        // 已绑定数量
        baggageBindingBillboardVO.setBoundedNumber(new BigDecimal(boundLong));
        // 未绑定SN总数： 当日待绑定SN总数-已绑定数量
        baggageBindingBillboardVO.setUnboundNumber(baggageBindingBillboardVO.getBoundNumber().subtract(baggageBindingBillboardVO.getBoundedNumber()));
        // 代码发料完成率：（当日待绑定代码总数-代码发料未完成数量）/当日待绑定代码总数
        baggageBindingBillboardVO.setDeliveryCompletionRate(baggageBindingBillboardVO.getTotalNumber()
                .subtract(baggageBindingBillboardVO.getUnfinishedNumber())
                .divide(this.getDivBigDecimal(baggageBindingBillboardVO.getTotalNumber()), 4, RoundingMode.HALF_DOWN)
                .multiply(new BigDecimal(Constant.NUMBER_100)).toString());
        // SN绑定完成率：（当日待绑定SN总数-未绑定SN总数）/当日待绑定SN总数
        baggageBindingBillboardVO.setBindingCompletionRate(
                baggageBindingBillboardVO.getBoundedNumber()
                        .divide(this.getDivBigDecimal(baggageBindingBillboardVO.getBoundNumber()), 4, BigDecimal.ROUND_DOWN)
                        .multiply(new BigDecimal(Constant.NUMBER_100))
                        .toString());
        return baggageBindingBillboardVO;
    }

    private BigDecimal getDivBigDecimal(BigDecimal bigDecimal) {
        if (BigDecimal.ZERO.compareTo(bigDecimal) == 0) {
            return BigDecimal.ONE;
        }
        return bigDecimal;
    }

    /**
     * 未绑定SN总数： 当日待绑定SN总数-已绑定数量， 已绑定数量： 根据领料单号查询条码中心已绑定数量相加
     *
     * @param sendMaterials sendMaterials
     */
    private long queryBoundedNumber(List<EdiSoSDTO> sendMaterials) {
        // 已绑定数量
        long boundLong = 0L;
        for (EdiSoSDTO sendMaterial : sendMaterials) {
            BarcodeExpandResponse response = barcodeCenterRemoteService.queryRegisterNumber(sendMaterial.getFromId(), sendMaterial.getOrderNo());
            boundLong += Optional.ofNullable(response.getTotal()).orElse(0L);
        }
        return boundLong;
    }

    private EdiSoSDTO getEdiSosQueryParam(List<String> billNumberList, String sku) {
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_VALUE_8886002);
        if (null == sysLookupValues || null == sysLookupValues.getLookupMeaning()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_VALUE_1004113003});
        }
        EdiSoSDTO query = new EdiSoSDTO();
        query.setExternalorderkey2List(billNumberList);
        query.setWarehouseIds(sysLookupValues.getLookupMeaning());
        query.setSku(sku);
        return query;
    }

    /**
     * 移除不是阿里任务的领料单数据
     *
     * @param prodPickList prodPickList
     */
    private void removeNotCustomerBill(List<ProdPickListMainDTO> prodPickList) {
        List<String> taskNos = prodPickList.stream().map(ProdPickListMainDTO::getTaskNo).collect(Collectors.toList());
        List<List<String>> splitList = CommonUtils.splitList(taskNos, Constant.BATCH_SIZE_500);
        List<PsTaskExtendedDTO> psTaskExtendedList = new LinkedList<>();
        splitList.forEach(item -> {
            List<PsTaskExtendedDTO> tempList = psTaskExtendedService.queryByTaskNos(taskNos);
            psTaskExtendedList.addAll(tempList);
        });
        //3.查询阿里配置客户编码
        List<SysLookupValues> typesDTOList = sysLookupValuesService.selectValuesByType(Constant.LOOKUP_VALUE_1004115);
        Set<String> customerNoSet = typesDTOList.stream().map(SysLookupValues::getLookupMeaning).collect(Collectors.toSet());
        //4 是阿里任务数据
        List<String> taskNoList = psTaskExtendedList.stream()
                .filter(item -> customerNoSet.contains(item.getCustomerNo()))
                .map(PsTaskExtendedDTO::getTaskNo).collect(Collectors.toList());
        // 5 移除不是阿里任务的领料单数据
        prodPickList.removeIf(item -> !taskNoList.contains(item.getTaskNo()));
    }

    /**
     * 获取当天领料单信息
     *
     * @return List<ProdPickListMainDTO>
     */
    private List<ProdPickListMainDTO> getProdPickListMainList(BaggageBindingBillboardQueryDTO param) {
        PickListQueryDTO queryDTO = new PickListQueryDTO();
        // 获取当天领料单号信息
        String startTime = DateUtil.convertDateToString(param.getStartTime(), DateUtil.DATE_FORMATE_DAY);
        String endTime = DateUtil.convertDateToString(DateUtil.addDay(param.getEndTime(), Constant.INT_1).getTime(), DateUtil.DATE_FORMATE_DAY);
        queryDTO.setStartDate(startTime);
        queryDTO.setEndDate(endTime);
        List<ProdPickListMainDTO> prodPickList = new ArrayList<>();
        // 正常领料信息
        prodPickList.addAll(DatawbRemoteService.queryPickListByTaskNos(queryDTO));
        // 维修领料信息
        prodPickList.addAll(inoneRemoteService.getPicklistMain(queryDTO));
        return prodPickList;
    }

    /**
     * 分页查询数据
     * 领料时间范围默认选择当天，领料单号、领料时间必填一个，点击查询按钮可展示待绑定明细信息：若未输入领料单号则根据领料时间查询领料单号，有输入领料单号则直接取界面上所填领料单号，根据领料单号调用infor接口获取物料代码、物料条码、原箱码、箱ID数量、发料数量、发料时间、发料人。在根据领料单号+箱码调用条码中心获取关联的SN并统计数量作为已绑定数量，若已绑定数量=发料数量则视为绑定完成。若已绑定数量<发料数量则视为未绑定完成，仅需展示未绑定完成的数据。若条码中心未查到绑定记录则状态为待绑定，否则为绑定中。
     * 领料单号：MES领料单号
     * 物料代码：领料单号对应的物料代码
     * 物料条码：箱ID对应的220条码
     * PKG ID：原箱码
     * 状态：箱+领料单不存在绑定记录则视为待绑定、否则为绑定中
     * 箱类别：原箱
     * 箱ID数量：原箱数量
     * 发料完成数量：原箱在当前领料单的发料数量
     * 发料完成时间：原箱在当前领料单的发料时间
     * 发料人：原箱在当前领料单的发料人
     * 已绑定数量：原箱在当前领料单号关联条码的数量
     * 绑定人：原箱在当前领料单的绑定人，条码中心接口最后更新人
     *
     * @param page page
     * @return Page<BaggageBindingBillboardDTO>
     * @throws Exception Exception
     */
    @Override
    public Page<BaggageBindingBillboardDTO> queryBoardPage(BaggageBindingBillboardQueryDTO page) throws Exception {
        this.checkParams(page);

        Page<BaggageBindingBillboardDTO> pageResult = new Page<>();
        pageResult.setRows(new LinkedList<>());
        // 1. 获取领料单信息
        PickListQueryDTO queryDTO = this.buildQueryParams(page);
        List<ProdPickListMainDTO> pickResultList = new LinkedList<>();
        pickResultList.addAll(DatawbRemoteService.queryPickListCondition(queryDTO));
        pickResultList.addAll(inoneRemoteService.getPicklistMain(queryDTO));
        if (CollectionUtils.isEmpty(pickResultList)) {
            return pageResult;
        }

        // 2.移除不是阿里任务
        this.removeNotCustomerBill(pickResultList);
        List<String> billNumberList = pickResultList.stream().map(ProdPickListMainDTO::getBillNumber)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(billNumberList)) {
            return pageResult;
        }
        // 获取info 发料数量 及 条码中心注册数量
        List<EdiSoSDTO> sendMaterials = this.getSendMaterial(page, billNumberList);

        List<BaggageBindingBillboardDTO> rows = new LinkedList<>();
        for (EdiSoSDTO sendMaterial : sendMaterials) {
            BaggageBindingBillboardDTO temp = new BaggageBindingBillboardDTO();
            // 领料单号
            temp.setOrderNo(sendMaterial.getOrderNo());
            temp.setSku(sendMaterial.getSku());
            temp.setIssueTime(sendMaterial.getEditDate());
            temp.setItemBarcode(sendMaterial.getLottable02());
            // 箱+领料单不存在绑定记录则视为待绑定、否则为绑定中
            temp.setStatus(Constant.NO_SHIPED);
           if (sendMaterial.getRegisterQty().compareTo(sendMaterial.getQty()) >= 0) {
                // 发料完成
                continue;
            }else  if (sendMaterial.getRegisterQty().compareTo(BigDecimal.ZERO) > 0) {
               temp.setStatus(Constant.BINDING);
           }
            temp.setIssuer(sendMaterial.getEditwho());
            temp.setBinder(sendMaterial.getBinder());
            temp.setPkgId(sendMaterial.getFromId());
            temp.setBoxType(Constant.ORIGINAL_LPN);
            temp.setIssueQuantity(sendMaterial.getQty());
            temp.setBoundedQuantity(sendMaterial.getRegisterQty());
            rows.add(temp);
        }
        rows.sort(Comparator.comparing(BaggageBindingBillboardDTO::getOrderNo)
                .thenComparing(BaggageBindingBillboardDTO::getSku));
        int start = (page.getPageNum() - Constant.INT_1) * page.getPageSize();
        int endCount = start + page.getPageSize();
        pageResult.setRows(CollUtil.sub(rows, start, endCount));
        pageResult.setTotal(rows.size());
        pageResult.setTotalPage(new BigDecimal(pageResult.getTotal())
                .divide(new BigDecimal(page.getPageSize()), 0,
                        RoundingMode.FLOOR).intValue());
        return pageResult;
    }

    /**
     * @param page page
     */
    private void checkParams(BaggageBindingBillboardQueryDTO page) {
        if (StringUtils.isBlank(page.getOrderNo())
                && (Objects.isNull(page.getStartTime()) || Objects.isNull(page.getEndTime()))) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BAGGAGE_PARAM_LOST);
        }
        Integer pageNum = page.getPageNum();
        if (pageNum == null || pageNum <= Constant.INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BAGGAGE_PARAM_LOST);
        }
        Integer pageSize = page.getPageSize();
        if (pageSize == null || pageSize <= Constant.INT_0) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BAGGAGE_PARAM_LOST);
        }
        if (StringUtils.isBlank(page.getOrderNo())) {
            long startLong = page.getStartTime().getTime();
            long endLong = page.getEndTime().getTime();
            if (endLong - startLong > baggageBindingQueryTimeRange * Constant.DAY_OF_SECOND) {
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.BAGGAGE_PARAM_TIME_OVER,
                        new Object[]{baggageBindingQueryTimeRange});
            }
        }
    }

    /**
     * 获取发料数量级注册数量
     *
     * @param page           page
     * @param billNumberList billNumberList
     * @return List<EdiSoSDTO>
     * @throws InterruptedException InterruptedException
     * @throws ExecutionException   ExecutionException
     */
    private List<EdiSoSDTO> getSendMaterial(BaggageBindingBillboardQueryDTO page, List<String> billNumberList) throws InterruptedException, ExecutionException {
        EdiSoSDTO ediSosQueryParam = this.getEdiSosQueryParam(billNumberList, page.getSku());
        List<EdiSoSDTO> sendMaterials = DatawbRemoteService.getSendMaterials(ediSosQueryParam);
        for (EdiSoSDTO so : sendMaterials) {
            BarcodeExpandResponse response = barcodeCenterRemoteService.queryRegisterNumber(so.getFromId(), so.getOrderNo());
            Long l = Optional.ofNullable(response.getTotal()).orElse(0L);
            so.setRegisterQty(BigDecimal.ZERO);
            List<BarcodeExpandVO> rows = response.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                so.setRegisterQty(new BigDecimal(l));
                so.setBinder(rows.get(0).getLastUpdatedBy());
            }
        }
        return sendMaterials;
    }

    /**
     * 参数组装
     *
     * @param page page
     * @return PickListQueryDTO
     */
    private PickListQueryDTO buildQueryParams(BaggageBindingBillboardQueryDTO page) {
        PickListQueryDTO queryDTO = new PickListQueryDTO();
        if (Objects.nonNull(page.getStartTime())) {
            // 获取当天领料单号信息
            String startTime = DateUtil.convertDateToString(page.getStartTime(), DateUtil.DATE_FORMATE_DAY);
            String endTime = DateUtil.convertDateToString(DateUtil.addDay(page.getEndTime(), Constant.INT_1).getTime(), DateUtil.DATE_FORMATE_DAY);
            queryDTO.setStartDate(startTime);
            queryDTO.setEndDate(endTime);
        }
        queryDTO.setSku(page.getSku());
        queryDTO.setOrderNo(page.getOrderNo());
        queryDTO.setCreator(page.getCreator());
        return queryDTO;
    }
}
