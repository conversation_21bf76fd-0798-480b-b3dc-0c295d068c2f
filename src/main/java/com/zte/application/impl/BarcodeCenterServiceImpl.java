package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.BarcodeCenterService;
import com.zte.application.PrintRecordService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.interfaces.dto.BarcodeCenterDTO;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class BarcodeCenterServiceImpl implements BarcodeCenterService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Autowired
    private PrintRecordService printRecordService;

    @Autowired
    private SysLookupValuesService lookupValuesService;

    /**
     * 获取模板 根据目录
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getTemplateNameList(String templateContnet) throws Exception {
        return barcodeCenterRemoteService.getTemplateNameList(templateContnet);
    }

    //获取配置url
    private String getUrl(String lookUpValue) {
        // 获取url
        SysLookupValues sysLookupTypesDTO = lookupValuesService.findByLookupCode(Integer.valueOf(lookUpValue));
        //若没有配置数据字典则启用条码中心
        if (sysLookupTypesDTO == null) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.GET_LOOKUP_VALUE_ERROR);
        }
        String url = sysLookupTypesDTO.getLookupMeaning();
        if (StringUtils.isEmpty(url)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_CENTER_URL);
        }
        return url;
    }

    /**
     * 条码中心适配程序下载地址获取接口
     *
     * @return
     * @throws Exception
     */
    @Override
    public BarcodeCenterDTO getDownloadUrl() throws Exception {
        String url = getUrl(MpConstant.LOOKUP_6679002);
        Map<String, String> headerParamsMap = barcodeCenterRemoteService.getHeaderForBarcodeCenter();
        String getResult = HttpRemoteUtil.remoteExe("", headerParamsMap, url, MicroServiceNameEum.SENDTYPEGET);
        JsonNode json = JacksonJsonConverUtil.getMapperInstance().readTree(getResult);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_CENTER_DOWNLOAD_URL);
        }
        String retCode = json.get(Constant.CODE).get(Constant.CODE).asText();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_CENTER_DOWNLOAD_URL);
        }
        String bo = json.get(Constant.BO).toString();
        BarcodeCenterDTO barcodeCenterDTO = JacksonJsonConverUtil.jsonToBean(bo, BarcodeCenterDTO.class);
        if (null == barcodeCenterDTO || StringUtils.isEmpty(barcodeCenterDTO.getDownloadUrl())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_BARCODE_CENTER_DOWNLOAD_URL);
        }
        return barcodeCenterDTO;
    }

    /**
     * 调条码中心打印
     *
     * @return
     * @throws Exception
     */
    @Override
    public void serverTemplatePrint(BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO) throws Exception {
        //打印
        barcodeCenterRemoteService.serverTemplatePrint(barcodeCenterTemplatePrintDTO);
    }

    /**
     * 条码中心查询
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<BarcodeExpandVO> barcodeQuery(BarcodeExpandQueryDTO barcodeExpandQueryDTO) throws Exception {
        if(barcodeExpandQueryDTO == null || CollectionUtils.isEmpty(barcodeExpandQueryDTO.getBarcodeList())){
            return new ArrayList<>();
        }
        String url = getUrl(MpConstant.LOOKUP_6679005);
        Map<String, String> headerParamsMap = barcodeCenterRemoteService.getHeaderForBarcodeCenter();
        List<BarcodeExpandVO> barcodeExpandDTOList = new ArrayList<>();
        for (List<String> barcodeTempList : CommonUtils.splitList(barcodeExpandQueryDTO.getBarcodeList(), Constant.BATCH_SIZE_499)) {
            BarcodeExpandQueryDTO param = new BarcodeExpandQueryDTO();
            param.setParentCategoryCode(barcodeExpandQueryDTO.getParentCategoryCode());
            param.setBarcodeList(barcodeTempList);
            List<BarcodeExpandVO> tempList = getBarcodeExpandDTOList(param, url, headerParamsMap);
            if(CollectionUtils.isNotEmpty(tempList)){
                barcodeExpandDTOList.addAll(tempList);
            }
        }
        return barcodeExpandDTOList;
    }

    private List<BarcodeExpandVO> getBarcodeExpandDTOList(BarcodeExpandQueryDTO barcodeExpandQueryDTO, String url, Map<String, String> headerParamsMap) throws MesBusinessException {
        String getResult = HttpRemoteUtil.remoteExe(JacksonJsonConverUtil.beanToJson(barcodeExpandQueryDTO), headerParamsMap, url, MicroServiceNameEum.SENDTYPEPOST);
        JSONObject json= JSON.parseObject(getResult);
        if (null == json) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED);
        }
        String retCode = json.getJSONObject(Constant.CODE).get(Constant.CODE).toString();
        if (!RetCode.SUCCESS_CODE.equalsIgnoreCase(retCode)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED,new Object[]{json.getJSONObject(Constant.CODE).get(Constant.MSG).toString()});
        }
        String bo = json.get(Constant.BO).toString();
        return  JSON.parseArray(bo,BarcodeExpandVO.class);
    }

}
