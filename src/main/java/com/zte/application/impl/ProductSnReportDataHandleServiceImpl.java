package com.zte.application.impl;

import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.application.CustomerItemsService;
import com.zte.application.PushStdModelConfirmationService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.enums.PushStatusEnum;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.ComponentInfoDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.ItemSplitInfoDTO;
import com.zte.interfaces.dto.MdsSspMainBoardDTO;
import com.zte.interfaces.dto.MdsSspSnMainBoardDTO;
import com.zte.interfaces.dto.ProductSnReportDTO;
import com.zte.interfaces.dto.ProductSnReportItemDTO;
import com.zte.interfaces.dto.PushStdModelConfirmationDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.SspTaskInfoDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.common.utils.Constant.COMMA;
import static com.zte.common.utils.Constant.NUM_ZERO;
import static com.zte.common.utils.Constant.STRING_EMPTY;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2025/5/13 15:53
 */
@Slf4j
@Service
public class ProductSnReportDataHandleServiceImpl extends BasePushStdModelSnDataHandleService<ProductSnReportDTO> {

    @Resource
    MdsRemoteService mdsRemoteService;
    @Resource
    private CustomerItemsService customerItemsService;
    @Resource
    private SysLookupValuesService sysLookupValuesService;
    @Resource
    private PushStdModelConfirmationService pushStdModelConfirmationService;

    // 中试字段和客户部件类型对应关系
    @Value("#{${zs.colume.customer.component.type.map:{'ssdList':'server.ssd','nicList':'server.nic','memoryList':'server.mem','hddList':'server.hdd','gpuList':'server.gpu','fpgaList':'server.fpga','cpuList':'server.cpu'}}}")
    private Map<String, String> columeToCustCompType;

    /**
     * 特定物料类型
     */
    @Value("${imes.std.task.specific.itemType:成品料}")
    private List<String> specificItemTypeList;

    /**
     * 特定客户部件类型
     * server.configmodel.moc
     */
    @Value("${imes.std.task.specific.CustomerComponentType:server.configmodel.moc}")
    private List<String> specificCustomerComponentType;

    /**
     * 是否调用中试接口获取9大件组装绑定关系
     */
    @Value("${call.mds.for.bind.info.switch:false}")
    private boolean callMdsForBindInfo;

    // 是否强制使用fixbom中mpn进行上传
    @Value("${sn.report.force.fixbom.mpn:false}")
    private boolean forceFixbomMpn;

    @Override
    public boolean match(String currProcess) {
        return productSnReportCurrProcess.equals(currProcess);
    }
    
    @Override
    @RedisDistributedLockAnnotation(redisKey = Constant.MESSAGE_TYPE_PRODUCT_SN_REPORT, redisLockTime = 10 * 60)
    public boolean handlePushStdModelSnDatas(List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles) {
        if (CollectionUtils.isEmpty(pushStdModelSnDataHandles)) {
            return false;
        }
        pushStdModelSnDataHandles.forEach(this::handlePushStdModelSnData);
        return true;
    }

    @Override
    protected ProductSnReportDTO wrapPushMessageData(
            PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications,
            List<FixBomDetailDTO> fixBomDetails) throws JsonProcessingException {
        String sn = pushStdModelSnDataExt.getSn();
        // 以SN维度获取中试数据
        MdsSspSnMainBoardDTO sspSnMainBoard = mdsRemoteService.getSspMainBoardBySn(sn);

        // 处理装配数据
        Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap = Maps.newHashMap();
        Map<String, WipExtendIdentificationDTO> snWipExtendIdentificationMap =
                this.getSnWipExtendIdentificationMapAndWrapItemNoSnMap(wipExtendIdentifications, itemNoSnMap);
        // 不存在多个mpn的物料代码
        Set<String> singleMpnItemNoSet = new HashSet<>();
        // 一个条码可能对应多个zteCode。需根据zteCode找到对应MPN,再根据MPN模糊匹配得到对应的多个zteCode
        Map<String, List<WipExtendIdentificationDTO>> fullItemNoSnMap = this.getFullItemNoSnMap(itemNoSnMap, singleMpnItemNoSet);
        // 对应多MPN条码根据采购中心数据设置规格型号
        this.setCustomerItemStyle(wipExtendIdentifications, singleMpnItemNoSet);

        // 处理fixBom数据
        FixBomDetailDTO topSnFixBomDetail = this.getTopSnFixBomDetail(fixBomDetails, pushStdModelSnDataExt);
        if (topSnFixBomDetail == null) {
            return null;
        }

        // 组装数据
        ProductSnReportItemDTO topSnItem = new ProductSnReportItemDTO();

        topSnItem.setSnNo(sn);
        topSnItem.setMaterialCategory(topSnFixBomDetail.getCustomerComponentType());
        topSnItem.setMaterialName(topSnFixBomDetail.getItemSupplierNo());
        topSnItem.setMaterialBom(pushStdModelSnDataExt.getTaskFixBomId());
        topSnItem.setMaterialQuantity(BigDecimal.ONE);
        topSnItem.setFactoryMpn(topSnFixBomDetail.getZteCode());
        topSnItem.setOobMac(this.getBmcMacFromSspSnMainBord(sspSnMainBoard));
        // 资产编码从中试数据获取， 如果没有值则设置为整机条码sn
        topSnItem.setTdId(this.getAssetNumFromSspSnMainBord(sspSnMainBoard));
        topSnItem.setProductSnList(Lists.newArrayList());

        if (CollectionUtils.isNotEmpty(topSnFixBomDetail.getChildNodes())) {
            topSnFixBomDetail.setItemNumber(StringUtils.defaultString(topSnFixBomDetail.getItemNumber(), NumConstant.STRING_ONE));
            this.wrapChildNodes(topSnFixBomDetail, fullItemNoSnMap, topSnItem, snWipExtendIdentificationMap, singleMpnItemNoSet);
        }

        // 遍历树,找到所有moc条码
        List<String> mocSnList = new ArrayList<>();
        this.getMocSnList(topSnItem, mocSnList);
        if (!CollectionUtils.isEmpty(mocSnList)) {
            // 取最小moc条码拼接-001后作为主条码
            topSnItem.setSnNo(Collections.min(mocSnList) + Constant.MOC_SUFFIX);
        }

        ProductSnReportDTO manufactureFeedBack = new ProductSnReportDTO();
        // 如果是buffer任务且(未转正或转正后还未推送成功)传0，其它传客户指令号
        this.setManufactureDirectiveNo(manufactureFeedBack, pushStdModelSnDataExt);
        manufactureFeedBack.setManufactureOrderNo(pushStdModelSnDataExt.getTaskNo());
        manufactureFeedBack.setCategory(pushStdModelSnDataExt.getTaskCustomerPartType());
        manufactureFeedBack.setProductSn(topSnItem);

        return manufactureFeedBack;
    }

    private void setCustomerItemStyle(List<WipExtendIdentificationDTO> wipExtendIdentifications, Set<String> singleMpnItemNoSet) throws JsonProcessingException {
        // 筛选物料代码对应多个mpn的条码
        List<WipExtendIdentificationDTO> multiMpnSnList = wipExtendIdentifications.stream().filter(curr -> StringUtils.isNotBlank(curr.getSn()) && !singleMpnItemNoSet.contains(curr.getItemNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(multiMpnSnList)) {
            return;
        }
        List<String> snList = multiMpnSnList.stream().map(WipExtendIdentificationDTO::getSn).distinct().collect(Collectors.toList());
        // 根据条码查询条码中心数据
        List<BarcodeExpandVO> barcodeExpandList = barcodeCenterRemoteService.barcodeQueryBatch(snList);
        // 条码和uuid对应关系map
        Map<String, String> snUuidMap = barcodeExpandList.stream()
                .filter(curr -> StringUtils.isNotBlank(curr.getBarcode()) && StringUtils.isNotBlank(curr.getSysLotCode()))
                .collect(Collectors.toMap(
                        BarcodeExpandVO::getBarcode,
                        BarcodeExpandVO::getSysLotCode,
                        (p1, p2) -> p1
                ));
        // 再根据sysLotCode(对应采购uuid)查询采购接口获取规格型号
        List<String> uuidList = barcodeExpandList.stream()
                .filter(curr -> StringUtils.isNotBlank(curr.getSysLotCode()))
                .map(BarcodeExpandVO::getSysLotCode).distinct().collect(Collectors.toList());
        List<ItemSplitInfoDTO> itemSplitInfoList = iscpRemoteService.getItemSplitInfoByUuid(uuidList);
        Map<String, String> uuidBrandStyleMap = itemSplitInfoList.stream()
                .filter(curr -> StringUtils.isNotBlank(curr.getItemUuid()) && StringUtils.isNotBlank(curr.getCustomerItemStyle()))
                .collect(Collectors.toMap(
                        ItemSplitInfoDTO::getItemUuid,
                        ItemSplitInfoDTO::getCustomerItemStyle,
                        (p1, p2) -> p1
                ));
        multiMpnSnList.forEach(p -> {
            String uuid = snUuidMap.get(p.getSn());
            if (StringUtils.isNotBlank(uuid)) {
                // 设置条码对应规格型号
                p.setCustomerItemStyle(uuidBrandStyleMap.get(uuid));
            }
        });
    }

    /**
     * 遍历树结构获取所有moc条码
     * @param parentSnItem
     * @param mocSnList
     */
    private void getMocSnList(ProductSnReportItemDTO parentSnItem, List<String> mocSnList) {
        if (CollectionUtils.isEmpty(parentSnItem.getProductSnList())) {
            return;
        }
        for (ProductSnReportItemDTO snItem : parentSnItem.getProductSnList()) {
            if (specificCustomerComponentType.contains(snItem.getMaterialCategory())) {
                mocSnList.add(snItem.getSnNo());
            }
            this.getMocSnList(snItem, mocSnList);
        }
    }

    private Map<String, List<String>> getNodeSlotCustCompTypeAndSnListMap(String sn) {
        Map<String, List<String>> nodeSlotCustCompTypeAndSnListMap = new HashMap<>();
        if (!callMdsForBindInfo) {
            return nodeSlotCustCompTypeAndSnListMap;
        }
        List<SspTaskInfoDTO> sspTaskInfoList = mdsRemoteService.querySspTaskInfoForAli(Arrays.asList(sn));
        // zsColumeToCustCompType
        for (SspTaskInfoDTO sspTaskInfoDTO : sspTaskInfoList) {
            if (!Constant.PARTCODE_TYPE_ZB.equals(sspTaskInfoDTO.getPartcodeType())) {
                continue;
            }
            for (Map.Entry<String, String> entry : columeToCustCompType.entrySet()) {
                // 拼接key,示例:1,server.ssd
                String key = sspTaskInfoDTO.getNodeSlot() + COMMA + entry.getValue();
                List<ComponentInfoDTO> componentList = (List<ComponentInfoDTO>) ReflectUtil.getFieldValue(sspTaskInfoDTO.getSubMaterialInfo(), entry.getKey());
                if (CollectionUtils.isEmpty(componentList)) {
                    continue;
                }
                Map<String, List<ComponentInfoDTO>> map = componentList.stream().collect(Collectors.groupingBy(ComponentInfoDTO::getItemcode));
                for (Map.Entry<String, List<ComponentInfoDTO>> ent : map.entrySet()) {
                    // 拼接key,示例:1,server.ssd,0060401B0071
                    nodeSlotCustCompTypeAndSnListMap.put(key + COMMA + ent.getKey(), ent.getValue().stream().map(ComponentInfoDTO::getSn).distinct().collect(Collectors.toList()));
                }
            }
        }
        return nodeSlotCustCompTypeAndSnListMap;
    }

    /**
     * 如果是buffer任务且未转正传0，其它传客户指令号
     * @param manufactureFeedBack
     * @param pushStdModelSnDataExt
     */
    private void setManufactureDirectiveNo(ProductSnReportDTO manufactureFeedBack, PushStdModelSnDataExtDTO pushStdModelSnDataExt) {
        manufactureFeedBack.setManufactureDirectiveNo(pushStdModelSnDataExt.getTaskBillNo());
        if (BusinessSceneEnum.BUFFER.getCode().equals(pushStdModelSnDataExt.getBusinessScene())) {
            // 判断任务是否已转正且已推送
            PushStdModelConfirmationDTO pushStdModelConfirmationDTO = pushStdModelConfirmationService.getByTaskNo(pushStdModelSnDataExt.getTaskNo());
            if (pushStdModelConfirmationDTO == null || (!Arrays.asList(PushStatusEnum.PUSHED_AND_CALLBACK.getCode(), PushStatusEnum.PUSHED_NOT_CALLBACK.getCode()).contains(pushStdModelConfirmationDTO.getPushStatus()))) {
                // 未转正且未推送则传0
                manufactureFeedBack.setManufactureDirectiveNo(NumConstant.STRING_ZERO);
            }
        }
    }

    /**
     * 补全物料代码和条码关系
     * 一个条码实际可能对应多个zteCode
     * 需根据zteCode找到对应MPN
     * 再根据MPN模糊匹配得到对应的多个zteCode
     * @param itemNoSnMap
     * @return
     */
    private Map<String, List<WipExtendIdentificationDTO>> getFullItemNoSnMap(Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap, Set<String> singleMpnItemNoSet) {
        Map<String, List<WipExtendIdentificationDTO>> fullItemNoSnMap = new HashMap<>();
        if (itemNoSnMap.isEmpty()) {
            return fullItemNoSnMap;
        }
        // 阿里客户编码
        List<SysLookupValues> lookupValues = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115);
        List<String> specificCustomerNoList = lookupValues.stream().filter(curr -> StringUtils.isNotBlank(curr.getLookupMeaning()))
                .map(SysLookupValues::getLookupMeaning).distinct().collect(Collectors.toList());
        List<CustomerItemsDTO> customerItemsList = customerItemsService.getSameItemOfZteCode(new ArrayList<>(itemNoSnMap.keySet()), specificCustomerNoList);
        // 按zteCode分组
        Map<String, String> zteCodeAndCusCodeMap = new HashMap<>();
        // 按originalCustomerCode分组
        Map<String, List<String>> cusCodeAndZteCodeMap = new HashMap<>();
        for (CustomerItemsDTO customerItemsDTO : customerItemsList) {
            zteCodeAndCusCodeMap.put(customerItemsDTO.getZteCode(), customerItemsDTO.getOriginalCustomerCode());
            List<String> zteCodeList = cusCodeAndZteCodeMap.get(customerItemsDTO.getOriginalCustomerCode());
            if (zteCodeList == null) {
                zteCodeList = new ArrayList<>();
                cusCodeAndZteCodeMap.put(customerItemsDTO.getOriginalCustomerCode(), zteCodeList);
            }
            zteCodeList.add(customerItemsDTO.getZteCode());
        }
        for (Map.Entry<String, List<WipExtendIdentificationDTO>> entry : itemNoSnMap.entrySet()) {
            String customerCode = zteCodeAndCusCodeMap.get(entry.getKey());
            if (StringUtils.isEmpty(customerCode)) {
                // 客户代码不存在，丢弃
                continue;
            }
            List<String> zteCodeList = cusCodeAndZteCodeMap.get(customerCode);
            if (CollectionUtils.isEmpty(zteCodeList)) {
                continue;
            }
            this.putSingleMpnItemNo(singleMpnItemNoSet, zteCodeList);

            for (String zteCode : zteCodeList) {
                List<WipExtendIdentificationDTO> wipExtendIdentificationList = fullItemNoSnMap.get(zteCode);
                if (wipExtendIdentificationList == null) {
                    // 为空赋值
                    fullItemNoSnMap.put(zteCode, entry.getValue());
                    continue;
                }
                // 不为空合并并去重(修复绑定条码物料代码不一致导致漏挂载问题(实际通过mpn转换后为相同物料))
                List<WipExtendIdentificationDTO> temp = Stream.concat(wipExtendIdentificationList.stream(), entry.getValue().stream()).distinct().collect(Collectors.toList());
                wipExtendIdentificationList.clear();
                wipExtendIdentificationList.addAll(temp);
            }
        }
        return fullItemNoSnMap;
    }

    private void putSingleMpnItemNo(Set<String> singleMpnItemNoSet, List<String> zteCodeList) {
        if (zteCodeList.size() == NumConstant.NUM_ONE) {
            singleMpnItemNoSet.addAll(zteCodeList);
        }
    }

    private String getAssetNumFromSspSnMainBord(MdsSspSnMainBoardDTO mdsSspSnMainBoard) {
        if (mdsSspSnMainBoard == null) {
            return STRING_EMPTY;
        }
        String assetNum = mdsSspSnMainBoard.getAssetNum();
        return StringUtils.isNotBlank(assetNum) ? assetNum : mdsSspSnMainBoard.getSn();
    }

    private String getBmcMacFromSspSnMainBord(MdsSspSnMainBoardDTO mdsSspSnMainBoard) {
        if (mdsSspSnMainBoard == null) {
            return STRING_EMPTY;
        }
        List<MdsSspMainBoardDTO> mainBoards = mdsSspSnMainBoard.getMainBoard();
        if (CollectionUtils.isEmpty(mainBoards)) {
            return STRING_EMPTY;
        }
        return mainBoards.stream()
                .map(MdsSspMainBoardDTO::getBmcMac)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(COMMA));
    }

    @Override
    public <T> boolean validatePushMessageData(String currProcess, T pushMessageData) {
        if (!(pushMessageData instanceof ProductSnReportDTO)) {
            return false;
        }
        ProductSnReportDTO manufactureFeedBack = (ProductSnReportDTO) pushMessageData;
        if (StringUtils.isBlank(manufactureFeedBack.getCategory())) {
            return false;
        }
        if (StringUtils.isBlank(manufactureFeedBack.getManufactureOrderNo())) {
            return false;
        }
        if (StringUtils.isBlank(manufactureFeedBack.getManufactureDirectiveNo())) {
            return false;
        }
        ProductSnReportItemDTO productSn = manufactureFeedBack.getProductSn();
        if (productSn == null) {
            return false;
        }
        return validatePushMessageDataItem(productSn);
    }

    private boolean validatePushMessageDataItem(ProductSnReportItemDTO productSnReportItem) {
        if (StringUtils.isBlank(productSnReportItem.getMaterialCategory())) {
            return false;
        }
        if (productSnReportItem.getMaterialQuantity() == null) {
            return false;
        }
        List<ProductSnReportItemDTO> productSnList = productSnReportItem.getProductSnList();
        if (CollectionUtils.isEmpty(productSnList)) {
            return true;
        }
        for (ProductSnReportItemDTO item : productSnList) {
            if (!validatePushMessageDataItem(item)) {
                return false;
            }
        }
        return true;
    }

    @Override
    protected String getPushStdModelSnDataMessageType() {
        return Constant.MESSAGE_TYPE_PRODUCT_SN_REPORT;
    }

    private FixBomDetailDTO getTopSnFixBomDetail(List<FixBomDetailDTO> inputList, PushStdModelSnDataExtDTO pushStdModelSnDataExt) {
        if (CollectionUtils.isEmpty(inputList)) {
            return null;
        }
        FixBomDetailDTO inputItem = inputList.get(0);
        FixBomDetailDTO tmpItem = new FixBomDetailDTO();

        BeanUtils.copyProperties(inputItem, tmpItem);
        tmpItem.setChildNodes(Lists.newArrayList());

        // 需要上传的bomDetail的id与其父级的映射关系
        Map<String, FixBomDetailDTO> idParentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(inputItem.getChildNodes())) {
            mountInputListToResult(idParentMap, inputItem.getChildNodes(), tmpItem, pushStdModelSnDataExt);
        }
        return tmpItem;
    }

    private void mountInputListToResult(
            Map<String, FixBomDetailDTO> idParentMap, List<FixBomDetailDTO> inputList, FixBomDetailDTO parent, PushStdModelSnDataExtDTO pushStdModelSnDataExt) {
        inputList.forEach(inputItem -> {
            // 复制一个临时对象
            FixBomDetailDTO tmpItem = new FixBomDetailDTO();
            BeanUtils.copyProperties(inputItem, tmpItem);
            tmpItem.setChildNodes(Lists.newArrayList());

            idParentMap.put(inputItem.getId(), parent);
            // 云属性为混合云，则仅上传fixbom需要物料，非混合云上传boxBom需要物料
            if (Constant.FLAG_Y.equals(inputItem.getBoxBomRequired())
                    && (!Constant.CLOUD_TYPE_HC.equalsIgnoreCase(pushStdModelSnDataExt.getCloudType()) || Constant.FLAG_Y.equals(inputItem.getFixBomRequired()))) {
                // L6包 的 成品料 不需要
                if (!Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(inputItem.getItemType())
                        || !Constant.ITEM_TYPE_L6_PKG.equals(inputItem.getItemMaterialType())) {
                    // 当前元素boxBomRequired为Y(混合云还需满足fixBomRequired为Y)且不是L6包成品料， 则将输入元素与父级建立关联
                    mountInputToResult(idParentMap, tmpItem, parent, pushStdModelSnDataExt);
                }
            }

            List<FixBomDetailDTO> inputItemChildNodes = inputItem.getChildNodes();
            if (CollectionUtils.isNotEmpty(inputItemChildNodes)) {
                mountInputListToResult(idParentMap, inputItemChildNodes, tmpItem, pushStdModelSnDataExt);
            }
        });
    }

    private void mountInputToResult(Map<String, FixBomDetailDTO> idParentMap, FixBomDetailDTO inputItem, FixBomDetailDTO parent, PushStdModelSnDataExtDTO pushStdModelSnDataExt) {
        String parentId = parent.getId();
        FixBomDetailDTO parentParent = idParentMap.get(parentId);
        if (parentParent != null) {
            boolean isParentL6PkgFinishedMaterial = Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(parent.getItemType())
                    && Constant.ITEM_TYPE_L6_PKG.equals(parent.getItemMaterialType());
            if (!Constant.FLAG_Y.equals(parent.getBoxBomRequired()) || isParentL6PkgFinishedMaterial
                    || specificCustomerComponentType.contains(inputItem.getCustomerComponentType())
                    || (Constant.CLOUD_TYPE_HC.equalsIgnoreCase(pushStdModelSnDataExt.getCloudType()) && !Constant.FLAG_Y.equals(parent.getFixBomRequired()))) {
                // 父节点是L6包成品料或boxBomRequired不为Y或(是混合云但fixBomRequired不为Y)则继续寻找上层节点直至满足条件作为当前节点挂载点
                // 当前节点是moc需挂载到最上层节点下
                mountInputToResult(idParentMap, inputItem, parentParent, pushStdModelSnDataExt);
                return;
            }
        }
        parent.getChildNodes().add(inputItem);
    }

    private void wrapChildNodes(
            FixBomDetailDTO parentFixBom, Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap, ProductSnReportItemDTO parent,
            Map<String, WipExtendIdentificationDTO> snWipExtendIdentificationMap, Set<String> singleMpnItemNoSet) {
        List<FixBomDetailDTO> childNodes = parentFixBom.getChildNodes();
        // 使用list存储index,避免值无法有效++
        List<Integer> index = new ArrayList<>();
        index.add(1);
        for (int i = 0; i < childNodes.size(); i++) {
            FixBomDetailDTO fixBomDetail = childNodes.get(i);
            // 用量为空时默认为1
            fixBomDetail.setItemNumber(StringUtils.defaultString(fixBomDetail.getItemNumber(), NumConstant.STRING_ONE));
            List<String> snList = this.getSnList(fixBomDetail, parent, index, parentFixBom.getParentVirtualSn(), itemNoSnMap);
            // 设置剩余需要数量
            fixBomDetail.setNeedQty(Integer.parseInt(fixBomDetail.getItemNumber()));
            while (fixBomDetail.getNeedQty() > 0) {
                if (CollectionUtils.isEmpty(snList)) {
                    break;
                }
                Iterator<String> iterator = snList.iterator();
                // 取第一个可用条码
                String itemSn = iterator.next();
                // 删除第一个元素,避免重复绑定
                iterator.remove();
                // 下层元素对应的上层虚拟码
                fixBomDetail.setParentVirtualSn(parentFixBom.getParentVirtualSn());
                if (itemSn.contains(Constant.LINE)) {
                    fixBomDetail.setParentVirtualSn(itemSn);
                }
                WipExtendIdentificationDTO wipExtendIdentification = snWipExtendIdentificationMap.get(itemSn);
                int formQty = this.getFormQty(wipExtendIdentification);
                List<ProductSnReportItemDTO> snItemList = this.getManufactureFeedBackItem(itemSn, fixBomDetail, parent, wipExtendIdentification, singleMpnItemNoSet);
                fixBomDetail.setNeedQty(fixBomDetail.getNeedQty() - formQty);
                this.setFormQty(formQty, fixBomDetail, wipExtendIdentification);
                parent.getProductSnList().addAll(snItemList);
                if (CollectionUtils.isNotEmpty(fixBomDetail.getChildNodes())) {
                    // 当前snItem作为下层父条码继续遍历
                    wrapChildNodes(fixBomDetail, itemNoSnMap, snItemList.get(0), snWipExtendIdentificationMap, singleMpnItemNoSet);
                }
            }
        }
    }

    private int getFormQty(WipExtendIdentificationDTO wipExtendIdentification) {
        return  (wipExtendIdentification == null || wipExtendIdentification.getFormQty() == null) ? NumConstant.NUM_ONE : wipExtendIdentification.getFormQty().intValue();
    }

    private void setFormQty(int formQty, FixBomDetailDTO fixBomDetail, WipExtendIdentificationDTO wipExtendIdentification) {
        if (formQty > fixBomDetail.getNeedQty() && wipExtendIdentification != null) {
            // 若可用数量大于需要数量更新可用数量为剩余数量
            wipExtendIdentification.setFormQty(new BigDecimal(formQty - fixBomDetail.getNeedQty()));
        }
    }

    /**
     * 获取条码列表
     * 成品料根据用量生成虚拟码 示例:219000000000-01
     * 父条码为虚拟码时需查询中试接口获取绑定条码(9大类)
     * 中试没有找到根据绑定记录中的SN进行
     * 且物料在多节点下，则将绑定记录中的SN按照MBOM的用量均分到节点下
     *
     * @param fixBomDetail
     * @param parent
     * @param index
     * @param lastVirtualSn 上层成品料对应的虚拟条码
     * @param itemNoSnMap
     * @return
     */
    private List<String> getSnList(
            FixBomDetailDTO fixBomDetail, ProductSnReportItemDTO parent, List<Integer> index,
            String lastVirtualSn, Map<String, List<WipExtendIdentificationDTO>> itemNoSnMap) {
        String itemNo = fixBomDetail.getZteCode();
        int needQty = Integer.parseInt(fixBomDetail.getItemNumber());
        List<String> snList = new ArrayList<>();
        if (specificItemTypeList.contains(fixBomDetail.getItemType())) {
            // 成品料根据用量生成虚拟码
            if (StringUtils.equals(NumConstant.STRING_ONE, fixBomDetail.getItemNumber())) {
                // 数量为1则是单节点，直接用219整机条码(只有单节点moc机型存在这种结构,且fixbom上挂载moc的成品料不会挂载其它非成品料(BA确认),否则挂载的条码可能会重复)
                snList.add(parent.getSnNo());
                return snList;
            }
            for (int j = 0; j < needQty; j++) {
                // 根据用量生成虚拟条码
                snList.add(parent.getSnNo() + Constant.LINE + StringUtils.leftPad(index.get(NUM_ZERO) + "", NumConstant.NUM_TWO, NumConstant.STRING_ZERO));
                index.set(NUM_ZERO, index.get(NUM_ZERO) + 1);
            }
            return snList;
        }

        // 非成品料根据装配关系和物料代码获取条码
        List<WipExtendIdentificationDTO> wipExtSnList = itemNoSnMap.get(itemNo);
        if (CollectionUtils.isEmpty(wipExtSnList)) {
            return snList;
        }
        Iterator<WipExtendIdentificationDTO> it = wipExtSnList.iterator();
        while (it.hasNext()) {
            if (needQty <= NUM_ZERO) {
                break;
            }
            WipExtendIdentificationDTO wipExtend = it.next();
            if (!StringUtils.equals(StringUtils.defaultString(lastVirtualSn), StringUtils.defaultString(wipExtend.getVirtualSn()))) {
                continue;
            }
            snList.add(wipExtend.getSn());
            int formQty = wipExtend.getFormQty() == null ? NumConstant.NUM_ONE : wipExtend.getFormQty().intValue();
            if (needQty >= formQty) {
                // 全部取用后从列表移除，避免重复取用
                // 没用完不移除,后续取用后需扣减formQty(需保证按顺序取用)
                it.remove();
            }
            needQty = needQty - formQty;
        }
        return snList;
    }

    private List<ProductSnReportItemDTO> getManufactureFeedBackItem(String snNo, FixBomDetailDTO fixBomDetail,
                                                                    ProductSnReportItemDTO parent, WipExtendIdentificationDTO wipExtendIdentification, Set<String> singleMpnItemNoSet) {
        List<ProductSnReportItemDTO> snItemList = new ArrayList<>();

        ProductSnReportItemDTO snItem = new ProductSnReportItemDTO();
        int formQty = this.getFormQty(wipExtendIdentification);

        // 所有情况数量都传1,数量大于1时拆分为多条数据
        snItem.setMaterialQuantity(BigDecimal.ONE);
        if (!Constant.FLAG_N.equals(fixBomDetail.getUploadBySn())) {
            // 按条码上传不为N时，需传数量
            snItem.setSnNo(snNo);
        }
        if (StringUtils.contains(snNo, Constant.LINE)) {
            // 成品料虚拟码
            snItem.setMaterialName(fixBomDetail.getItemSupplierNo());
        } else {
            // 非成品料虚拟码(是主物料代码且存在多MPN使用条码中心规格型号作为mpn,其它使用fixbom中mpn)
            snItem.setMaterialMpn(fixBomDetail.getItemSupplierNo());
            if (!forceFixbomMpn && wipExtendIdentification != null && !singleMpnItemNoSet.contains(wipExtendIdentification.getItemNo()) && StringUtils.isNotBlank(wipExtendIdentification.getCustomerItemStyle())) {
                snItem.setMaterialMpn(wipExtendIdentification.getCustomerItemStyle());
            }
        }
        snItem.setMaterialCategory(fixBomDetail.getCustomerComponentType());
        // materialBom只有第一层才传
//        snItem.setMaterialBom(parent.getMaterialBom());
        snItem.setFactoryMpn(fixBomDetail.getZteCode());
        snItem.setOobMac(parent.getOobMac());
        snItem.setTdId(parent.getTdId());
        snItem.setProductSnList(Lists.newArrayList());
        snItemList.add(snItem);

        int minQty = fixBomDetail.getNeedQty() > formQty ? formQty : fixBomDetail.getNeedQty();
        for (int i = 1; i < minQty; i++) {
            ProductSnReportItemDTO snItemCopy = new ProductSnReportItemDTO();
            BeanUtils.copyProperties(snItem, snItemCopy);
            snItemList.add(snItemCopy);
        }

        return snItemList;
    }
}
