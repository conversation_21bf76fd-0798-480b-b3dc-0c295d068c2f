package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.application.CustomerItemsService;
import com.zte.application.PushModelSnTestRecordService;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.application.PushStdModelSnDataService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.PushModelSnTestRecord;
import com.zte.domain.model.PushStdModelSnData;
import com.zte.domain.model.PushStdModelSnDataSub;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.TradeDataLogRepository;
import com.zte.gei.processor.handler.exporter.ExportTaskHandler;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportParamDTO;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationFileDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationTestingInfoDTO;
import com.zte.interfaces.dto.MdsProblemsDTO;
import com.zte.interfaces.dto.MdsRepairInfoDTO;
import com.zte.interfaces.dto.MdsRepairInformationDTO;
import com.zte.interfaces.dto.MdsStationsDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushCompleteMachineTestDataToAlibabaDTO;
import com.zte.interfaces.dto.PushModelSnTestRecordDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.PushStdModelSnDataPageQueryDTO;
import com.zte.interfaces.dto.PushStdModelSnDataQueryDTO;
import com.zte.interfaces.dto.SspTaskInfoDTO;
import com.zte.interfaces.dto.WholeMachineDataUploadDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackDataDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackResultDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackResultDataDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.zte.common.model.NumConstant.NUM_ONE;
import static com.zte.common.utils.NumConstant.NUM_ZERO;

/**
 * 标模任务条码推送信息表服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-14 09:58:57
 */
@Slf4j
@Service("pushStdModelSnDataService")
public class PushStdModelSnDataServiceImpl extends BasePushStdModelSnDataService implements PushStdModelSnDataService, ExportTaskHandler<PushStdModelSnDataPageQueryDTO, PushStdModelSnData> {

    @Autowired
    private MdsRemoteService mdsRemoteService;

    @Autowired
    private OpenApiRemoteService openApiRemoteService;
    @Autowired
    private CustomerItemsService customerItemsService;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;

    @Autowired
    private PushModelSnTestRecordService pushModelSnTestRecordService;
    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    private EmailUtils emailUtils;

    @Value("${push.std.model.sn.data.manufacturer.name:ZTE101}")
    private String manufacturerName;
    @Value("${push.std.model.sn.data.pageSize:5000}")
    private Integer pageSize;
    @Value("${push.std.model.sn.data.preDays:180}")
    private Integer preDays;

    @Value("${push.std.model.sn.data.limit:500}")
    private Integer limit;

    @Value("${push.std.model.sn.data.requestIdPrefixSwitch:false}")
    private boolean requestIdPrefixSwitch;

    @Value("${push.std.model.sn.data.emailer:}")
    private String sendEmailer;

    @Value("${push.std.model.sn.data.customerSubName:阿里巴巴（中国）有限公司}")
    private String customerSubName;
    @Value("#{${request.default.head.map:{'x-mes-bff':'iMes-bff-authorization'}}}")
    private Map<String,String> requestDefaultHeadMap;
    @Value("#{${same.message.type.call.back.map:{'#snReplace':'/zte-mes-manufactureshare-productionmgmtsys/CustomerCommonJobController/callBackSnReplacementJob'}}}")
    private Map<String, String> sameMessageTypeCallBackMap;
    @Value("${center.interface.address}")
    private String centerInterfaceAddress;
    @Resource
    private TradeDataLogRepository tradeDataLogRepository;
    @Resource
    List<PushStdModelSnDataHandleService<?>> pushStdModelSnDataHandleServices;
    @Resource
    private FixBomCommonService fixBomCommonService;

    @Value("${finished.product.inventory.customer.component.type:server.configmodel.moc}")
    private String customerComponentType;

    @Value("${finished.product.inventory.moc.customer.component.type:server.configmodel}")
    private String mocCustomerComponentType;

    @Value("${push.std.model.sn.data.virtualBarcodeSwitch:false}")
    private boolean virtualBarcodeSwitch;

    @Override
    public PageRows<PushStdModelSnData> queryPage(PushStdModelSnDataPageQueryDTO query) {
        Page<PushStdModelSnDataPageQueryDTO> page = new Page<>(query.getPage(), query.getRows());
        page.setParams(query);

        PageRows<PushStdModelSnData> pageRows = new PageRows<>();
        pageRows.setRows(pushStdModelSnDataRepository.selectPage(page));
        pageRows.setCurrent(query.getPage());
        pageRows.setTotal(page.getCurrent());
        return pageRows;
    }

    @Override
    public PushStdModelSnData getById(String id) {
        return pushStdModelSnDataRepository.selectById(id);
    }

    @Override
    public void add(PushStdModelSnData pushStdModelSnData) {
        pushStdModelSnData.setId(UUID.randomUUID().toString());

        pushStdModelSnData.setCreateBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        pushStdModelSnDataRepository.insert(pushStdModelSnData);
    }

    @Override
    public void batchInsertOrUpdate(List<PushStdModelSnData> pushStdModelSnDataList) {
        if (CollectionUtils.isEmpty(pushStdModelSnDataList)) {
            return;
        }
        for (List<PushStdModelSnData> pushStdModelSnData : CommonUtils.splitList(pushStdModelSnDataList, NumConstant.NUM_500)) {
            pushStdModelSnDataRepository.batchInsertOrUpdate(pushStdModelSnData);
        }
    }

    @Override
    public void updateById(PushStdModelSnData pushStdModelSnData) {
        pushStdModelSnData.setLastUpdatedBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
        pushStdModelSnDataRepository.updateById(pushStdModelSnData);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        pushStdModelSnDataRepository.deleteByIds(ids);
    }

    @Override
    public Integer countExportTotal(PushStdModelSnDataPageQueryDTO query) {
        return pushStdModelSnDataRepository.selectCount(query);
    }

    @Override
    public List<PushStdModelSnData> queryExportData(PushStdModelSnDataPageQueryDTO query, int pageNo, int pageSize) {
        Page<PushStdModelSnDataPageQueryDTO> page = new Page<>(pageNo, pageSize);
        page.setParams(query);
        page.setSearchCount(false);
        return pushStdModelSnDataRepository.selectPage(page);
    }

    /**
     * 整机测试数据上传-阿里
     *
     * @param list
     * @throws Exception
     */
    @Override
    @RedisDistributedLockAnnotation(redisKey = "pushCompleteMachineTestData", redisLockTime = 7200)
    public void pushCompleteMachineTestData(List<String> list) throws Exception {

        Map<String, String> sysMap = pushModelSnTestRecordService.getSysMap(Constant.LOOKUP_VALUE_6746);
        Map<String, String> sysMapForReasonType = pushModelSnTestRecordService.getSysMap(Constant.LOOKUP_VALUE_6747);
        PushStdModelSnDataDTO pushStdModelSnDataDTO = new PushStdModelSnDataDTO();
        pushStdModelSnDataDTO.setPreDays(preDays);
        pushStdModelSnDataDTO.setSnList(list);
        pushStdModelSnDataDTO.setLimit(limit);
        StringBuilder errorSb = new StringBuilder();
        while (true) {
            List<PushStdModelSnDataDTO> pushStdModelSnDataList = pushStdModelSnDataRepository.getNeedPushDataPage(pushStdModelSnDataDTO);
            if (CollectionUtils.isEmpty(pushStdModelSnDataList)) {
                break;
            }
            pushStdModelSnDataDTO.setLastId(pushStdModelSnDataList.get(pushStdModelSnDataList.size()- NUM_ONE).getId());
            pushStdModelSnDataDTO.setLastUpdatedDate(pushStdModelSnDataList.get(pushStdModelSnDataList.size()- NUM_ONE).getLastUpdatedDate());
            this.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMapForReasonType, errorSb);
        }
        this.sendEmail(errorSb.toString());
    }

    private void pushCompleteMachineTestData(List<PushStdModelSnDataDTO> pushStdModelSnDataList, Map<String, String> sysMap, Map<String, String> sysMapForReasonType, StringBuilder errorSb) {
        try {
            this.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMapForReasonType);
        }catch (Exception e){
            errorSb.append(e.getMessage());
        }
    }


    public void sendEmail(String errorStr) {
        if(StringUtils.isEmpty(errorStr)){
            return;
        }
        if(StringUtils.isEmpty(sendEmailer)){
            return;
        }
        emailUtils.sendMail(sendEmailer,Constant.ABNORMAL_UPLOADING_OF_TEST_DATA,"",errorStr,"");
    }

    public void pushCompleteMachineTestData(List<PushStdModelSnDataDTO> pushStdModelSnDataList, Map<String, String> sysMap, Map<String, String> sysMapForReasonType) throws Exception {
        List<String> snList = pushStdModelSnDataList.stream().map(e -> e.getSn()).distinct().collect(Collectors.toList());
        List<MdsFeedbackProductionStationTestingInfoDTO> mdsFeedbackProductionStationTestingInfoDTOList = mdsRemoteService.feedbackOfProductionStationTestingInformation(snList);
        if (CollectionUtils.isEmpty(mdsFeedbackProductionStationTestingInfoDTOList)) {
            return;
        }
        //获取条码中试节点信息
        Map<String, List<SspTaskInfoDTO>> sspMap = this.getSspMap(snList);
        List<CustomerItemsDTO> customerItemsDTOList = this.getCustomerItemsDTOList(pushStdModelSnDataList);
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return;
        }
        //整机-主板
        Map<String, PushStdModelSnDataDTO> itemMap = pushStdModelSnDataList.stream().collect(Collectors.toMap(k -> k.getSn(), v -> v, (k1, k2) -> k1));
        //维修信息
        List<String> repairSnList = this.getRepairSnList(mdsFeedbackProductionStationTestingInfoDTOList);
        List<MdsRepairInformationDTO> mdsRepairInformationDTOList = mdsRemoteService.getRepairInformation(repairSnList);
        Map<String, MdsRepairInformationDTO> repairMap = mdsRepairInformationDTOList.stream().collect(Collectors.toMap(k -> k.getServerSn(), v -> v, (oldValue, newValue) -> newValue));
        List<PushModelSnTestRecordDTO> insertList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        for (MdsFeedbackProductionStationTestingInfoDTO mdsDTO : mdsFeedbackProductionStationTestingInfoDTOList) {
            WholeMachineDataUploadDTO wholeMachineDataUploadDTO = new WholeMachineDataUploadDTO();
            wholeMachineDataUploadDTO.setSysMap(sysMap);
            wholeMachineDataUploadDTO.setSysMapForReasonType(sysMapForReasonType);
            wholeMachineDataUploadDTO.setMdsDTO(mdsDTO);
            wholeMachineDataUploadDTO.setItemMap(itemMap);
            wholeMachineDataUploadDTO.setCustomerItemsDTOList(customerItemsDTOList);
            wholeMachineDataUploadDTO.setRepairMap(repairMap);
            List<SspTaskInfoDTO> sspTaskInfoDTOList = sspMap.get(mdsDTO.getServerSn());
            if(CollectionUtils.isEmpty(sspTaskInfoDTOList)){
                continue;
            }
            List<SspTaskInfoDTO> cnList = sspTaskInfoDTOList.stream().filter(e->StringUtils.startsWith(e.getNodeNumber(),Constant.CN)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(cnList)){
                continue;
            }
            for (SspTaskInfoDTO sspTaskInfoDTO : cnList) {
                String nodeNumber = sspTaskInfoDTO.getNodeNumber().substring(Constant.CN.length());
                String nodeSn = this.generateNodeSn(mdsDTO.getServerSn(), nodeNumber);
                sspTaskInfoDTO.setNodeSn(nodeSn);
                wholeMachineDataUploadDTO.setSspTaskInfoDTO(sspTaskInfoDTO);
                this.assemblyData(wholeMachineDataUploadDTO, insertList, dataList);
            }
        }
        PushStdModelSnDataService pushStdModelSnDataService = SpringContextUtil.getBean("pushStdModelSnDataService", PushStdModelSnDataService.class);
        //新增测试记录
        pushStdModelSnDataService.insertAndSendToB2B(insertList, dataList);
    }

    private Map<String, List<SspTaskInfoDTO>> getSspMap(List<String> snList) {
        List<SspTaskInfoDTO> sspTaskInfoDTOList = mdsRemoteService.querySspTaskInfoForAli(snList);
        if (CollectionUtils.isEmpty(sspTaskInfoDTOList)) {
            return new HashMap<>();
        }
        Map<String, List<SspTaskInfoDTO>> sspMap = sspTaskInfoDTOList.stream().filter(i -> i.getSn() != null).collect(Collectors.groupingBy(SspTaskInfoDTO::getSn));
        return sspMap;
    }

    private List<String> getRepairSnList(List<MdsFeedbackProductionStationTestingInfoDTO> mdsFeedbackProductionStationTestingInfoDTOList) {
        List<String> repairSnList = new ArrayList<>();
        for (MdsFeedbackProductionStationTestingInfoDTO mdsFeedbackProductionStationTestingInfoDTO : mdsFeedbackProductionStationTestingInfoDTOList) {
            List<MdsStationsDTO> stations = mdsFeedbackProductionStationTestingInfoDTO.getStations();
            if(CollectionUtils.isEmpty(stations)){
                continue;
            }
            MdsStationsDTO mdsStationsDTO = stations.stream().filter(e->!StringUtils.equals(e.getState(), Constant.PASS_STR)).findFirst().orElse(null);
            if(mdsStationsDTO != null){
                repairSnList.add(mdsFeedbackProductionStationTestingInfoDTO.getServerSn());
            }
        }
        return repairSnList;
    }

    private void assemblyData(WholeMachineDataUploadDTO wholeMachineDataUploadDTO, List<PushModelSnTestRecordDTO> insertList,List<CustomerDataLogDTO> dataList) throws InterruptedException {
        Map<String, String> sysMap = wholeMachineDataUploadDTO.getSysMap();
        Map<String, String> sysMapForReasonType = wholeMachineDataUploadDTO.getSysMapForReasonType();
        MdsFeedbackProductionStationTestingInfoDTO mdsDTO = wholeMachineDataUploadDTO.getMdsDTO();
        Map<String, PushStdModelSnDataDTO> itemMap = wholeMachineDataUploadDTO.getItemMap();
        List<CustomerItemsDTO> customerItemsDTOList = wholeMachineDataUploadDTO.getCustomerItemsDTOList();
        SspTaskInfoDTO sspTaskInfoDTO = wholeMachineDataUploadDTO.getSspTaskInfoDTO();
        Map<String, MdsRepairInformationDTO> repairMap = wholeMachineDataUploadDTO.getRepairMap();
        List<MdsStationsDTO> stationsList = mdsDTO.getStations();
        if (CollectionUtils.isEmpty(stationsList)) {
            return;
        }
        //过滤已上传过的
        List<MdsStationsDTO> stations = this.getMdsStationsDTOList(stationsList,sspTaskInfoDTO.getNodeSn());
        if (CollectionUtils.isEmpty(stations)) {
            return;
        }
        PushStdModelSnDataDTO pushStdModelSnDataDTO = itemMap.get(mdsDTO.getServerSn());
        if (pushStdModelSnDataDTO == null) {
            return;
        }
        //整机客户物料信息
        CustomerItemsDTO customerItemsDTO = this.getCustomerItemsDTO(customerItemsDTOList, pushStdModelSnDataDTO);
        Map<String, MdsRepairInfoDTO> repairInfoDTOMap = this.getRepairInfoDTOMap(mdsDTO, repairMap);
        Map<String, List<MdsFeedbackProductionStationFileDTO>> stationMap = this.getStationFileDTOMap(mdsDTO, stations);
        for (MdsStationsDTO station : stations) {
            //在配置的工序才需要推送
            String stationName = sysMap.get(station.getName());
            if (StringUtils.isEmpty(stationName)) {
                continue;
            }
            PushModelSnTestRecordDTO pushModelSnTestRecordDTO = new PushModelSnTestRecordDTO();
            pushModelSnTestRecordDTO.setStationId(station.getStationId());
            pushModelSnTestRecordDTO.setType(Constant.ZTEIMES_ALIBABA_TYPE);
            this.setType(customerItemsDTO, pushModelSnTestRecordDTO);
            pushModelSnTestRecordDTO.setDirectiveNumber(this.getDirectiveNumber(pushStdModelSnDataDTO));
            pushModelSnTestRecordDTO.setWorkorderId(pushStdModelSnDataDTO.getTaskNo());
            pushModelSnTestRecordDTO.setSn(pushStdModelSnDataDTO.getSn());
            pushModelSnTestRecordDTO.setNodeSn(sspTaskInfoDTO.getNodeSn());
            String requestId = this.getRequestId(station) + sspTaskInfoDTO.getNodeSn();
            pushModelSnTestRecordDTO.setRequestId(requestId);
            pushModelSnTestRecordDTO.setBrand(Constant.ZTE);
            pushModelSnTestRecordDTO.setModel(pushStdModelSnDataDTO.getCustomerItemName());
            pushModelSnTestRecordDTO.setBoardSn(sspTaskInfoDTO.getPartcode());
            pushModelSnTestRecordDTO.setStationName(stationName);
            this.setTestTime(station, pushModelSnTestRecordDTO);
            this.setResult(station, pushModelSnTestRecordDTO);
            this.setPhenomenonDetail(station, pushModelSnTestRecordDTO);
            this.setActionInfo(station, pushModelSnTestRecordDTO, sysMapForReasonType);
            this.setOssFileKey(station, stationMap, pushModelSnTestRecordDTO);
            this.setMdsRepairInfoDTO(station, repairInfoDTOMap, pushModelSnTestRecordDTO);
            pushModelSnTestRecordDTO.setManufacturerName(manufacturerName);
            pushModelSnTestRecordDTO.setUploadStatus(Constant.STR_0);
            pushModelSnTestRecordDTO.setFileUploadStatus(Constant.STR_0);
            pushModelSnTestRecordDTO.setStationUploadStatus(Constant.STR_0);
            pushModelSnTestRecordDTO.setCreateBy(Constant.SYSTEM);
            pushModelSnTestRecordDTO.setLastUpdatedBy(Constant.SYSTEM);
            pushModelSnTestRecordDTO.setFactoryId(pushStdModelSnDataDTO.getFactoryId());
            insertList.add(pushModelSnTestRecordDTO);

            PushCompleteMachineTestDataToAlibabaDTO pushDTO = new PushCompleteMachineTestDataToAlibabaDTO();
            BeanUtils.copyProperties(pushModelSnTestRecordDTO, pushDTO);
            pushDTO.setChassisSn(pushModelSnTestRecordDTO.getSn());
            CustomerDataLogDTO customerDataLogDTO = this.generateCustomerDataLogDTO(pushStdModelSnDataDTO, pushDTO);
            dataList.add(customerDataLogDTO);
        }
    }

    private String generateNodeSn(String sn, String nodeNumber) {
        if(StringUtils.isNotEmpty(nodeNumber)){
            return sn+Constant.HORIZON+Constant.STR_0+ nodeNumber;
        }
        return sn;
    }

    private String getDirectiveNumber(PushStdModelSnDataDTO pushStdModelSnDataDTO) {
        return StringUtils.isEmpty(pushStdModelSnDataDTO.getBillNo()) ? Constant.NULL : pushStdModelSnDataDTO.getBillNo();
    }

    private CustomerItemsDTO getCustomerItemsDTO(List<CustomerItemsDTO> customerItemsDTOList, PushStdModelSnDataDTO pushStdModelSnDataDTO) {
        CustomerItemsDTO customerItemsDTO = customerItemsDTOList.stream().filter(f -> StringUtils.equals(pushStdModelSnDataDTO.getItemNo(), f.getZteCode()) && StringUtils.equals(NumConstant.STRING_THREE, f.getProjectType())).findFirst().orElse(null);
        return customerItemsDTO;
    }


    private List<MdsStationsDTO> getMdsStationsDTOList(List<MdsStationsDTO> stationsList,String nodeSn) {
        List<String> stationIdList = stationsList.stream().map(e -> e.getStationId()).distinct().collect(Collectors.toList());
        List<PushModelSnTestRecord> pushModelSnTestRecordList = pushModelSnTestRecordService.getByIdList(stationIdList);
        List<String> existStationIdList = pushModelSnTestRecordList.stream().map(e -> e.getStationId()+e.getNodeSn()).distinct().collect(Collectors.toList());
        List<MdsStationsDTO> stations = stationsList.stream().filter(e -> !existStationIdList.contains(e.getStationId()+nodeSn)).collect(Collectors.toList());
        return stations;
    }

    private void setMdsRepairInfoDTO(MdsStationsDTO station, Map<String, MdsRepairInfoDTO> repairInfoDTOMap, PushModelSnTestRecordDTO pushModelSnTestRecordDTO) {
        MdsRepairInfoDTO mdsRepairInfoDTO = repairInfoDTOMap.get(station.getStationId());
        if (mdsRepairInfoDTO != null) {
            pushModelSnTestRecordDTO.setFinishReworkTime(this.getConvertDateToString(mdsRepairInfoDTO.getFinishReworkTime()));
            pushModelSnTestRecordDTO.setReworkTime(this.getConvertDateToString(mdsRepairInfoDTO.getReworkTime()));
            pushModelSnTestRecordDTO.setActionMsg(mdsRepairInfoDTO.getActionMsg());
        }
    }

    private void setType(CustomerItemsDTO customerItemsDTO, PushModelSnTestRecordDTO pushModelSnTestRecordDTO) {
        if (customerItemsDTO != null) {
            pushModelSnTestRecordDTO.setType(customerItemsDTO.getCustomerComponentType());
        }
    }

    private String getRequestId(MdsStationsDTO station) {
        String requestId = Constant.ZTE + station.getStationId();
        if (requestIdPrefixSwitch) {
            requestId += System.currentTimeMillis();
        }
        return requestId;
    }

    private List<CustomerItemsDTO> getCustomerItemsDTOList(List<PushStdModelSnDataDTO> pushStdModelSnDataList) {
        List<String> itemNoList = new ArrayList<>();
        itemNoList.addAll(pushStdModelSnDataList.stream().map(e -> e.getItemNo()).distinct().collect(Collectors.toList()));
        CustomerItemsDTO paramCustomerItemsDTO = new CustomerItemsDTO();
        paramCustomerItemsDTO.setCustomerSubName(customerSubName);
        paramCustomerItemsDTO.setItemNoList(itemNoList);

        List<CustomerItemsDTO> customerItemsDTOList = customerItemsService.getCustomerItemsInfo(paramCustomerItemsDTO);
        return customerItemsDTOList;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAndSendToB2B(List<PushModelSnTestRecordDTO> insertList, List<CustomerDataLogDTO> dataList) throws Exception {
        pushModelSnTestRecordService.batchInsertOrUpdate(insertList);
        if (CollectionUtils.isNotEmpty(dataList)) {
            tradeDataLogService.pushDataOfExceptionRollback(dataList);
        }
    }

    private Map<String, MdsRepairInfoDTO> getRepairInfoDTOMap(MdsFeedbackProductionStationTestingInfoDTO mdsDTO, Map<String, MdsRepairInformationDTO> repairMap) {
        MdsRepairInformationDTO mdsRepairInformationDTO = repairMap.get(mdsDTO.getServerSn());
        Map<String, MdsRepairInfoDTO> repairInfoDTOMap = new HashMap<>();
        if (mdsRepairInformationDTO == null) {
            return repairInfoDTOMap;
        }
        List<MdsRepairInfoDTO> mdsRepairInfoDTOList = mdsRepairInformationDTO.getRepairInfoList();
        if (mdsRepairInfoDTOList == null) {
            return repairInfoDTOMap;
        }
        return mdsRepairInfoDTOList.stream().collect(Collectors.toMap(k -> k.getProblemRequestId(), v -> v, (oldValue, newValue) -> newValue));
    }

    private String getConvertDateToString(Long date) {
        return date == null ? "" : DateUtil.convertDateToString(new Date(date * NumConstant.NUM_1000), DateUtil.DATE_FORMATE_FULL);
    }

    private CustomerDataLogDTO generateCustomerDataLogDTO(PushStdModelSnDataDTO pushStdModelSnDataDTO,
                                                          PushCompleteMachineTestDataToAlibabaDTO pushCompleteMachineTestDataToAlibabaDTO) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(idGenerator.snowFlakeIdStr());
        customerDataLogDTO.setKeywords(pushCompleteMachineTestDataToAlibabaDTO.getRequestId());
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setCustomerName(Constant.ALIBABA);
        customerDataLogDTO.setProjectName(Constant.ALIBABA);
        customerDataLogDTO.setProjectPhase(Constant.UPLOAD_COMPLETE_MACHINE_TESTING_DATA);
        customerDataLogDTO.setPushType(Constant.PUSH_TYPE.KAFKA);
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_TEST_DATA_OF_THE_WHOLE_MACHINE);
        customerDataLogDTO.setTaskNo(pushStdModelSnDataDTO.getTaskNo());
        customerDataLogDTO.setFactoryId(pushStdModelSnDataDTO.getFactoryId());
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setSn(pushStdModelSnDataDTO.getSn());
        customerDataLogDTO.setItemNo(pushStdModelSnDataDTO.getItemNo());
        customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(pushCompleteMachineTestDataToAlibabaDTO));
        return customerDataLogDTO;
    }

    private List<WipExtendIdentificationDTO> getWipExtendIdentificationDTOS(List<WipExtendIdentificationDTO> tempWipTendList, List<String> itemNoForMotherboard) {
        List<WipExtendIdentificationDTO> tempWipTendItemList = CollectionUtils.isEmpty(tempWipTendList) ? new ArrayList<>() : tempWipTendList.stream().filter(e -> itemNoForMotherboard.contains(e.getItemNo())).distinct().collect(Collectors.toList());
        return tempWipTendItemList;
    }

    private Map<String, List<MdsFeedbackProductionStationFileDTO>> getStationFileDTOMap(MdsFeedbackProductionStationTestingInfoDTO mdsDTO, List<MdsStationsDTO> stations) {
        List<String> stationIdList = stations.stream().map(e -> e.getStationId()).distinct().collect(Collectors.toList());
        List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(mdsDTO.getServerSn(), stationIdList, false);
        return stationFileDTOList.stream().collect(Collectors.groupingBy(MdsFeedbackProductionStationFileDTO::getStationId));
    }

    private void setOssFileKey(MdsStationsDTO station, Map<String, List<MdsFeedbackProductionStationFileDTO>> stationMap, PushModelSnTestRecordDTO pushDTO) {
        List<MdsFeedbackProductionStationFileDTO> list = stationMap.get(station.getStationId());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<MdsFeedbackProductionStationFileDTO> tempList = list.stream().filter(e->StringUtils.contains(e.getLogName(),pushDTO.getNodeSn())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tempList)) {
            return;
        }
        pushDTO.setOssFileKey(tempList.get(NUM_ZERO).getLogName());
    }

    private void setPhenomenonDetail(MdsStationsDTO station, PushModelSnTestRecordDTO pushDTO) {
        List<MdsProblemsDTO> problems = station.getProblems();
        if (CollectionUtils.isNotEmpty(problems)) {
            pushDTO.setMessage(problems.get(NumConstant.NUM_ZERO).getPhenomenonDetail());
        }
    }

    private void setActionInfo(MdsStationsDTO station, PushModelSnTestRecordDTO pushDTO, Map<String, String> sysMapForReasonType) {
        List<MdsProblemsDTO> problems = station.getProblems();
        if (CollectionUtils.isNotEmpty(problems)) {
            String reasonType = problems.get(NumConstant.NUM_ZERO).getReasonType();
            String mappingReasonType = sysMapForReasonType.get(reasonType);
            pushDTO.setActionCode(mappingReasonType);
        }
    }

    private void setResult(MdsStationsDTO station, PushModelSnTestRecordDTO pushModelSnTestRecordDTO) {
        pushModelSnTestRecordDTO.setResult(StringUtils.equals(station.getState(), Constant.PASS_STR) ? Constant.ALi.PASS : Constant.ALi.FAIL);
    }

    private void setTestTime(MdsStationsDTO stationsDTO, PushModelSnTestRecordDTO pushModelSnTestRecordDTO) throws InterruptedException {
        if (requestIdPrefixSwitch) {
            pushModelSnTestRecordDTO.setStartedTime(DateUtil.convertDateToString(DateUtil.addDay(new Date(), -1).getTime(), DateUtil.DATE_FORMATE_FULL));
            Thread.sleep(100);
            pushModelSnTestRecordDTO.setFinishedTime(DateUtil.convertDateToString(new Date(), DateUtil.DATE_FORMATE_FULL));
            Thread.sleep(100);
            return;
        }
        if (stationsDTO.getStartTime() != null) {
            pushModelSnTestRecordDTO.setStartedTime(DateUtil.convertDateToString(new Date(stationsDTO.getStartTime() * NumConstant.NUM_1000), DateUtil.DATE_FORMATE_FULL));
        }
        if (stationsDTO.getEndTime() != null) {
            pushModelSnTestRecordDTO.setFinishedTime(DateUtil.convertDateToString(new Date(stationsDTO.getEndTime() * NumConstant.NUM_1000), DateUtil.DATE_FORMATE_FULL));
        }
    }

    @Override
    public boolean batchSave(List<PushStdModelSnDataExtDTO> pushStdModelSnDataList) {
        if (CollectionUtils.isEmpty(pushStdModelSnDataList)) {
            return false;
        }

        Set<String> pendingAddTaskNoSnSet = Sets.newHashSet();
        int factoryId = Integer.parseInt(RequestHeadValidationUtil.validaFactoryId());
        List<PushStdModelSnDataExtDTO> pendingSaveList = pushStdModelSnDataList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getSn()))
                .filter(item -> StringUtils.isNotBlank(item.getTaskNo()))
                .filter(item -> pendingAddTaskNoSnSet.add(item.getTaskNo() + Constant.UNDER_LINE + item.getSn()))
                .peek(item -> {
                    item.setFactoryId(factoryId);
                    item.setId(idGenerator.snowFlakeIdStr());
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pendingSaveList)) {
            return false;
        }

        List<String> taskNos = pendingSaveList.stream().map(PushStdModelSnDataExtDTO::getTaskNo).distinct().collect(Collectors.toList());
        // 根据任务号查询任务信息
        List<PsTaskExtendedDTO> taskExtendedList = psTaskExtendedService.queryByTaskNos(taskNos);
        // 筛选物料控制模式为整机模式的任务
        List<String> materialControl2TaskNos = taskExtendedList.stream().filter(p -> NumConstant.STRING_TWO.equals(p.getMaterialControl()))
                .map(PsTaskExtendedDTO::getTaskNo).distinct().collect(Collectors.toList());

        Map<String, PushStdModelSnDataDTO> existsMap = Maps.newHashMap();
        List<PushStdModelSnDataDTO> pendingAddList = Lists.newArrayList();
        List<PushStdModelSnDataDTO> pendingUpdateList = Lists.newArrayList();
        // 获取传入的需要新增的数据中已经存在的数据
        List<PushStdModelSnDataDTO> existsList = pushStdModelSnDataRepository.selectExists(pendingSaveList, factoryId);
        if (CollectionUtils.isNotEmpty(existsList)) {
            existsMap.putAll(existsList.stream().collect(Collectors.toMap(item -> item.getTaskNo() + Constant.UNDER_LINE + item.getSn(), k -> k, (k1, k2) -> k1)));
        }

        pendingSaveList.forEach(item -> {
            // 设置状态为待推送
            item.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);
            PushStdModelSnDataDTO existsPushStdModelSnData = existsMap.get(item.getTaskNo() + Constant.UNDER_LINE + item.getSn());
            if (existsPushStdModelSnData == null) {
                if (Constant.FG_DISAS_2.equals(item.getTaskEntityClass())) {
                    // 如果任务列别为拆解任务 且数据尚未入库 则设置为40添加到新增队列
                    item.setCurrProcess(finishedProductStorageCurrProcess);
                    pendingAddList.add(item);
                }else if (Objects.nonNull(item.getWhiteTaskNoList()) && item.getWhiteTaskNoList().contains(item.getTaskNo())) {
                    this.setCurrProcess(item, materialControl2TaskNos);
                    pendingAddList.add(item);
                }
            } else {
                // 数据已入库, 封装到待更新列表
                this.wrapItemForUpdate(item, existsPushStdModelSnData, pendingUpdateList, materialControl2TaskNos);
            }
        });

        if (!CollectionUtils.isEmpty(pendingAddList)) {
            pushStdModelSnDataRepository.batchInsert(pendingAddList);
        }
        if (!CollectionUtils.isEmpty(pendingUpdateList)) {
            pushStdModelSnDataRepository.batchUpdate(pendingUpdateList);
        }
        return true;
    }

    private void setCurrProcess(PushStdModelSnDataExtDTO item, List<String> materialControl2TaskNos) {
        if (materialControl2TaskNos.contains(item.getTaskNo())) {
            // 质量码白名单且物料控制模式为整机模式，自动条码推送到 40
            item.setCurrProcess(finishedProductStorageCurrProcess);
        } else {
            // 质量码白名单且物料控制模式不为整机模式，自动条码推送到 30
            item.setCurrProcess(productSnReportCurrProcess);
        }
    }

    private void wrapItemForUpdate(PushStdModelSnDataExtDTO item, PushStdModelSnDataDTO existsPushStdModelSnData,
                                   List<PushStdModelSnDataDTO> pendingUpdateList, List<String> materialControl2TaskNos) {
        item.setId(existsPushStdModelSnData.getId());
        String existsCurrProcess = existsPushStdModelSnData.getCurrProcess();
        // 根据预设工序 获取当前进程的 前后进程
        int index = currProcessSortedList.indexOf(existsCurrProcess);
        String nextProcessItem = index >= currProcessSortedList.size() - 1 ? null : currProcessSortedList.get(index + 1);
        if (Objects.equals(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK, existsPushStdModelSnData.getPushStatus())
                && nextProcessItem != null) {
            if (index == NumConstant.NUM_ZERO && materialControl2TaskNos.contains(item.getTaskNo())) {
                // 物料管控类型为整机模式不进行产品SN上传,跳过30直接推送到40成品入库上传
                index = index + 1;
                nextProcessItem = index >= currProcessSortedList.size() - 1 ? finishedProductStorageCurrProcess : currProcessSortedList.get(index + 1);
            }
            item.setCurrProcess(nextProcessItem);
            pendingUpdateList.add(item);
        }
        // 当前工序 等待状态3 向下推进 或者 没有下工序直接完成
        if (Objects.equals(item.getPushNextProcess(), existsPushStdModelSnData.getCurrProcess())
                && Objects.equals(Constant.PUSH_STATUS.PUSHED_AND_NEXT, existsPushStdModelSnData.getPushStatus())){
            if (nextProcessItem != null) {
                // 推进到下工序
                item.setCurrProcess(nextProcessItem);
            } else {
                // 当前工序完工
                item.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
            }
            pendingUpdateList.add(item);
        }
    }

    @Override
    public PageRows<PushStdModelSnDataDTO> queryPushStdModelSnData(PushStdModelSnDataQueryDTO query) {
        String snFrom = query.getSnFrom();
        Date lastUpdatedDateStart = query.getLastUpdatedDateStart();

        int pageNum = query.getPageNum() == null || query.getPageNum() < NumConstant.NUM_ONE ? NumConstant.NUM_ONE : query.getPageNum();
        int localPageSize = query.getPageSize() == null || query.getPageSize() > NumConstant.NUM_500 ? NumConstant.NUM_500 : query.getPageSize();

        // 分页逻辑是否需要计算总数
        boolean countFlag = !org.springframework.util.StringUtils.hasText(snFrom) || lastUpdatedDateStart == null;
        PageMethod.startPage(pageNum, localPageSize, countFlag);
        com.github.pagehelper.Page<PushStdModelSnDataDTO> pushStdModelSnDataExtPage =
                (com.github.pagehelper.Page<PushStdModelSnDataDTO>) pushStdModelSnDataRepository.selectByQuery(query);

        PageRows<PushStdModelSnDataDTO> pushStdModelSnDataExtPageRows = new PageRows<>();

        pushStdModelSnDataExtPageRows.setCurrent(pushStdModelSnDataExtPage.getPageNum());
        pushStdModelSnDataExtPageRows.setTotal(pushStdModelSnDataExtPage.getTotal());
        pushStdModelSnDataExtPageRows.setRows(pushStdModelSnDataExtPage.getResult());

        return pushStdModelSnDataExtPageRows;
    }

    @Override
    public boolean handleB2bCallback(B2bCallBackNewDTO b2bCallBackNewDTO) {
        log.info("PushStdModelSnDataServiceImpl.handleB2bCallback, data:{}", JSON.toJSONString(b2bCallBackNewDTO));
        String keywords = b2bCallBackNewDTO.getKeywords();
        if (StringUtils.isBlank(keywords)) {
            return false;
        }

        if (keywords.endsWith(Constant.CALL_BACK_SN_REPLACEMENT)) {
            String url = sameMessageTypeCallBackMap.get(Constant.CALL_BACK_SN_REPLACEMENT);
            Map<String, String> httpRequestHeader = MESHttpHelper.getHttpRequestHeader();
            httpRequestHeader.putAll(requestDefaultHeadMap);
            // 获取工厂id
            CustomerDataLogDTO tradeDataLogById = tradeDataLogRepository.getTradeDataLogById(b2bCallBackNewDTO.getMessageId());
            if (Objects.nonNull(tradeDataLogById)) {
                Integer factoryId = tradeDataLogById.getFactoryId();
                if (Objects.nonNull(factoryId)) {
                    httpRequestHeader.put(Constant.X_FACTORY_ID, factoryId.toString());
                }
            }
            String msg = HttpClientUtil.httpPostWithJSON(centerInterfaceAddress + url,
                    JacksonJsonConverUtil.beanToJson(b2bCallBackNewDTO), httpRequestHeader);
            ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(msg);
            return true;
        } else if (keywords.endsWith(Constant.CALL_BACK_DATA_SUB)) {
            return updateSnReportSub(b2bCallBackNewDTO, keywords);
        }

        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Lists.newArrayList();
        if (Constant.MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE.equals(b2bCallBackNewDTO.getMessageType())) {
            // 如果是成品入库上传 需要根据上传报文获取推送的数据并更新
            pushStdModelSnDataList = this.getAndWrapPushStdModelSnDataList(b2bCallBackNewDTO);
        } else {
            boolean success = this.checkSuccess(b2bCallBackNewDTO);
            pushStdModelSnDataList.add(this.getAndWrapPushStdModelSnData(b2bCallBackNewDTO.getKeywords(), success, b2bCallBackNewDTO.getData()));
        }
        if (CollectionUtils.isNotEmpty(pushStdModelSnDataList)) {
            pushStdModelSnDataRepository.batchUpdate(pushStdModelSnDataList);
        }
        return true;
    }

    private boolean updateSnReportSub(B2bCallBackNewDTO b2bCallBackNewDTO, String keywords) {
        String id = keywords.substring(0, keywords.indexOf(Constant.CALL_BACK_DATA_SUB));
        // 查询数据
        PushStdModelSnDataSub pushStdModelSnData = pushStdModelSnDataSubRepository.selectById(id);
        if(Objects.isNull(pushStdModelSnData)){
            return false;
        }
        if (this.checkSuccess(b2bCallBackNewDTO)) {
            pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
        } else {
            // 失败
            pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
            pushStdModelSnData.setPushErrorMsg(StringUtils.substring(b2bCallBackNewDTO.getData(), 0, Constant.INT_1800));
        }
        pushStdModelSnDataSubRepository.updateDataList(Collections.singletonList(pushStdModelSnData));
        return true;
    }

    private List<PushStdModelSnDataDTO> getAndWrapPushStdModelSnDataList(B2bCallBackNewDTO b2bCallBackNewDTO) {
        CustomerDataLogDTO tradeDataLog = tradeDataLogRepository.getTradeDataLogById(b2bCallBackNewDTO.getMessageId());
        if (tradeDataLog == null) {
            return Lists.newArrayList();
        }
        boolean success = this.checkSuccess(b2bCallBackNewDTO);
        String pushData = tradeDataLogRepository.getPushData(b2bCallBackNewDTO.getMessageId());
        FinishedProductStorageDTO finishedProductStorage = JacksonJsonConverUtil.jsonToBean(pushData, FinishedProductStorageDTO.class);
        return pushStdModelSnDataRepository.selectExists(
                        finishedProductStorage.getMaterialBillList().stream()
                                .map(item -> {
                                    PushStdModelSnDataDTO data = new PushStdModelSnDataDTO();
                                    data.setSn(item.getOriginalBarcode());
                                    data.setTaskNo(finishedProductStorage.getManufactureOrderNo());
                                    return data;
                                })
                                .collect(Collectors.toList()), tradeDataLog.getFactoryId()
                ).stream()
                .map(item -> this.getAndWrapPushStdModelSnData(item.getId(), success, b2bCallBackNewDTO.getData()))
                .collect(Collectors.toList());
    }

    private PushStdModelSnDataDTO getAndWrapPushStdModelSnData(String id, boolean success, String errorMsg) {
        PushStdModelSnDataDTO pushStdModelSnData = new PushStdModelSnDataDTO();
        pushStdModelSnData.setId(id);

        if (success) {
            // 修改状态为回调成功
            pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
            pushStdModelSnData.setPushFailCount(NUM_ZERO);
            pushStdModelSnData.setErrorMsg("");
        } else {
            pushStdModelSnData.setPushStatus(Constant.PUSH_STATUS.CALLBACK_ERROR);
            pushStdModelSnData.setErrorMsg(StringUtils.substring(errorMsg, NUM_ZERO, NumConstant.NUM_1500));
        }
        return pushStdModelSnData;
    }

    private boolean checkSuccess(B2bCallBackNewDTO b2bCallBackNew) {
        if (!b2bCallBackNew.isSuccess()) {
            return false;
        }
        WorkOrderWriteCallBackDTO orderWriteCallBack = JSON.parseObject(b2bCallBackNew.getData(), WorkOrderWriteCallBackDTO.class);
        if (orderWriteCallBack == null) {
            return false;
        }
        WorkOrderWriteCallBackDataDTO orderWriteCallBackData = JSON.parseObject(orderWriteCallBack.getData(), WorkOrderWriteCallBackDataDTO.class);
        if (orderWriteCallBackData == null || !orderWriteCallBackData.getSuccess()) {
            return false;
        }
        WorkOrderWriteCallBackResultDTO orderWriteCallBackResult = JSON.parseObject(orderWriteCallBackData.getResult(), WorkOrderWriteCallBackResultDTO.class);
        if (orderWriteCallBackResult == null || !orderWriteCallBackResult.getSuccess()) {
            return false;
        }
        WorkOrderWriteCallBackResultDataDTO orderWriteCallBackResultData = JSON.parseObject(orderWriteCallBackResult.getData(), WorkOrderWriteCallBackResultDataDTO.class);
        if (orderWriteCallBackResultData == null) {
            return true;
        }
        return StringUtils.isBlank(orderWriteCallBackResultData.getCheckErrorMsg());
    }

    @Override
    public List<PushStdModelSnDataDTO> reportRecords(PushStdModelSnDataDTO b2bCallBackNew) {
        return pushStdModelSnDataRepository.reportRecords(b2bCallBackNew);
    }

    /**
     * @param pushStdModelSnDataHandle 参数
     * @return
     */
    @Override
    public Object buildPushSnData(PushStdModelSnDataHandleDTO pushStdModelSnDataHandle) throws JsonProcessingException {
        String factoryId = RequestHeadValidationUtil.validaFactoryId();
        pushStdModelSnDataHandle.setFactoryId(Integer.valueOf(factoryId));
        PushStdModelSnDataHandleService<?> handleService = getHandleService(pushStdModelSnDataHandle);
        // 获取推送条码信息
        PushStdModelSnDataDTO snDataDTO = pushStdModelSnDataRepository.queryPushStdModelSn(pushStdModelSnDataHandle.getSn(),
                pushStdModelSnDataHandle.getTaskNo(), pushStdModelSnDataHandle.getFactoryId());
        if (Objects.isNull(snDataDTO)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_PUSH_STD_MODEL_LOST,
                    new Object[]{pushStdModelSnDataHandle.getSn()});
        }

        pushStdModelSnDataHandle.setId(snDataDTO.getId());
        // 任务条码
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = pushStdModelSnDataRepository.selectExtByPrimaryKey(pushStdModelSnDataHandle.getId());
        pushStdModelSnDataExt.setStockName(pushStdModelSnDataHandle.getStockName());
        List<WipExtendIdentificationDTO> wipExtendIdentifications = CollectionUtils.isEmpty(pushStdModelSnDataHandle.getWipExtendIdentifications())
                ? Lists.newArrayList()
                : pushStdModelSnDataHandle.getWipExtendIdentifications();

        List<Object> pushDataList = new ArrayList<>();
        // 产品条码上报
        if(Constant.STRING_30.equals(pushStdModelSnDataHandle.getCurrProcess())){
            getReportSnMsgList(pushStdModelSnDataHandle, pushStdModelSnDataExt, wipExtendIdentifications, handleService, pushDataList);
        }else{
            Object messageData = handleService.getPushMessageData(pushStdModelSnDataHandle.getCurrProcess(),
                    pushStdModelSnDataExt, wipExtendIdentifications);
            pushDataList.add(messageData);
        }
        return getObjects(pushStdModelSnDataHandle, pushDataList, handleService);
    }

    private void getReportSnMsgList(PushStdModelSnDataHandleDTO pushStdModelSnDataHandle, PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications, PushStdModelSnDataHandleService<?> handleService, List<Object> pushDataList) throws JsonProcessingException {
        List<FixBomDetailDTO> list = fixBomCommonService.queryFixBomDetailByHeadId(pushStdModelSnDataExt.getFixBomHeadId());
        if (CollectionUtils.isEmpty(list)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FIX_BOM_DETAIL_LOST,
                    new Object[]{pushStdModelSnDataExt.getFixBomHeadId()});
        }
        Object messageData = handleService.getPushMessageData(pushStdModelSnDataHandle.getCurrProcess(),
                pushStdModelSnDataExt, wipExtendIdentifications);
        pushDataList.add(messageData);
    }

    private PushStdModelSnDataHandleService<?> getHandleService(PushStdModelSnDataHandleDTO pushStdModelSnDataHandle) {
        PushStdModelSnDataHandleService<?> handleService = null;
        for (PushStdModelSnDataHandleService<?> pushStdModelSnDataHandleService : pushStdModelSnDataHandleServices) {
            if(pushStdModelSnDataHandleService.match(pushStdModelSnDataHandle.getCurrProcess())){
                handleService = pushStdModelSnDataHandleService;
            }
        }
        if (Objects.isNull(handleService)) {
            // 报错
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAM_MISSING);
        }
        return handleService;
    }

    private Object getObjects(PushStdModelSnDataHandleDTO pushStdModelSnDataHandle, List<Object> pushDataList,
                         PushStdModelSnDataHandleService<?> handleService) {
        for (Object messageData : pushDataList) {
            if(!handleService.validatePushMessageData(pushStdModelSnDataHandle.getCurrProcess(), messageData)){
                // 校验不通过， 返回失败标识
                throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.PARAMS_RULE_ERROR);
            }
        }
        for (Object t : pushDataList) {
            //库存上报只有一个
            if (t instanceof FinishedProductStorageDTO) {
                FinishedProductStorageDTO messageData1 = (FinishedProductStorageDTO) t;
                return messageData1.getMaterialBillList();
            }
        }
        return pushDataList;
    }

    /**
     * 相互替换条码信息
     *
     * @param pushStdModelSnData 条码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void replaceSnStdModelData(PushStdModelSnData pushStdModelSnData) {
        Pair<String, String> pair = RequestHeadValidationUtil.validaFactoryIdAndEmpno();
        pushStdModelSnData.setFactoryId(Integer.parseInt(pair.getFirst()));
        // 1.查询替换条码
        PushStdModelSnDataDTO pushStdModelSn = pushStdModelSnDataRepository.queryPushStdModelSn(pushStdModelSnData.getReplaceSn(),
                        pushStdModelSnData.getTaskNo(), pushStdModelSnData.getFactoryId());
        if (Objects.isNull(pushStdModelSn)) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SN_PUSH_STD_MODEL_LOST,
                    new Object[]{pushStdModelSnData.getReplaceSn()});
        }
        // 删除替换条码信息
        pushStdModelSnDataRepository.deleteByIds(Collections.singletonList(pushStdModelSn.getId()));
        // 更新原始sn 记录为 替换后的Sn
        pushStdModelSnDataRepository.replaceSnByCondition(pushStdModelSnData);

        PushStdModelSnData insertData = new PushStdModelSnData();
        // 替换原始条码
        insertData.setId(idGenerator.snowFlakeIdStr());
        insertData.setSn(pushStdModelSnData.getOriginalSn());
        insertData.setTaskNo(pushStdModelSn.getTaskNo());
        insertData.setItemNo(pushStdModelSn.getItemNo());
        // 跳过当前进程等上工序修改状态
        insertData.setPushStatus(Constant.INT_3);
        // 条码上报完成
        insertData.setCurrProcess(Constant.STRING_30);
        insertData.setLastUpdatedDate(new Date());
        insertData.setCreateDate(new Date());
        insertData.setPushFailCount(0);
        insertData.setFactoryId(pushStdModelSnData.getFactoryId());
        insertData.setEnabledFlag(Constant.FLAG_Y);
        insertData.setCreateBy(pair.getSecond());
        insertData.setLastUpdatedBy(pair.getSecond());
        insertData.setVirtualSn(pushStdModelSn.getVirtualSn());
        insertData.setOriginalSn(pushStdModelSn.getOriginalSn());
        pushStdModelSnDataRepository.insert(insertData);
    }

    /**
     * 条码加任务查询条码信息
     *
     * @param queryList queryList
     * @return List<PushStdModelSnDataDTO>
     */
    @Override
    public List<PushStdModelSnDataDTO> queryPushStdModelSnList(List<PushStdModelSnData> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        // 响应结果集
        List<PushStdModelSnDataDTO> resultList = new LinkedList<>();
        List<List<PushStdModelSnData>> splitList = CommonUtils.splitList(queryList, Constant.INT_100);
        for (List<PushStdModelSnData> items : splitList) {
            List<PushStdModelSnDataDTO> list = pushStdModelSnDataRepository.queryPushStdModelSnList(items);
            if (CollectionUtils.isNotEmpty(list)) {
                resultList.addAll(list);
            }
        }
        return resultList;
    }

    @Override
    public List<FinishedProductStorageCargoTransportDTO> queryInventoryDetails(List<FinishedProductStorageCargoTransportParamDTO> paramDTOList) {
        //按任务分组
        Map<String, List<FinishedProductStorageCargoTransportParamDTO>> transportParamMap = paramDTOList.stream()
                .collect(Collectors.groupingBy(FinishedProductStorageCargoTransportParamDTO::getTaskNo));
        List<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.queryByTaskNos(Lists.newArrayList(transportParamMap.keySet()));
        Map<String, PsTaskExtendedDTO> psTaskExtendedMap = CollectionUtils.isEmpty(psTaskExtendedDTOList) ? new HashMap<>() : psTaskExtendedDTOList.stream().collect
                (Collectors.toMap(k -> k.getTaskNo(), v -> v, (oldValue, newValue) -> newValue));
        //组装数据
        List<FinishedProductStorageCargoTransportDTO> topFinishList = Lists.newArrayList();
        for (Map.Entry<String, List<FinishedProductStorageCargoTransportParamDTO>> entry : transportParamMap.entrySet()) {
            PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedMap.get(entry.getKey());
            if (psTaskExtendedDTO == null) {
                continue;
            }
            List<FixBomDetailDTO> list = fixBomCommonService.queryTreeNodeByHeadId(psTaskExtendedDTO.getFixBomHeadId());
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            List<FinishedProductStorageCargoTransportParamDTO> transportParamDTOList = entry.getValue();
            for (FinishedProductStorageCargoTransportParamDTO dto : transportParamDTOList) {
                List<WipExtendIdentificationDTO> wipExtendIdentificationDTOList = dto.getWipExtendIdentificationList();
                if(CollectionUtils.isEmpty(wipExtendIdentificationDTOList)){
                    continue;
                }
                FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO = this.getFinishedProductStorageCargoTransportDTO(
                        wipExtendIdentificationDTOList,
                        list,true, psTaskExtendedDTO.getCloudType());
                topFinishList.add(finishedProductStorageCargoTransportDTO);
            }

        }
        return topFinishList;
    }

    @Override
    public String getVirtualSn(String sn) {
        return pushStdModelSnDataRepository.getVirtualSn(sn);
    }
    public FinishedProductStorageCargoTransportDTO getFinishedProductStorageCargoTransportDTO(List<WipExtendIdentificationDTO> wipExtendIdentificationDTOList,
                                                                                               List<FixBomDetailDTO> list,Boolean fromCargoTransport, String cloudType) {

        //获取子条码物料代码对应全部客户物料信息
        this.setMocFixBomDetail(wipExtendIdentificationDTOList, openDetailList(list), cloudType);
        String formSn = wipExtendIdentificationDTOList.get(NUM_ZERO).getFormSn();
        FixBomDetailDTO topFixBomDetailDTO = list.stream().filter(e->StringUtils.equals(Constant.STR_0,e.getItemLevel())).findFirst().orElse(null);
        FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO = new FinishedProductStorageCargoTransportDTO();
        finishedProductStorageCargoTransportDTO.setOriginalBarcode(formSn);
        finishedProductStorageCargoTransportDTO.setBarcode(formSn);
        finishedProductStorageCargoTransportDTO.setBarcodeType(Constant.STR_0);
        if(topFixBomDetailDTO == null){
            return finishedProductStorageCargoTransportDTO;
        }
        finishedProductStorageCargoTransportDTO.setMaterialName(topFixBomDetailDTO.getItemSupplierNo());
        finishedProductStorageCargoTransportDTO.setCustomerComponentType(topFixBomDetailDTO.getCustomerComponentType());
        //存在moc条码，需要取最小moc条码加-001替换顶层条码
        List<FinishedProductStorageCargoTransportDTO> childFinishList = Lists.newArrayList();
        this.dealMocBarcode(wipExtendIdentificationDTOList, childFinishList,finishedProductStorageCargoTransportDTO,fromCargoTransport);
        //虚拟表的条码
        this.dealXnBarcode(wipExtendIdentificationDTOList, childFinishList);
        finishedProductStorageCargoTransportDTO.setMaterialBillList(childFinishList);
        return finishedProductStorageCargoTransportDTO;
    }

    private List<FixBomDetailDTO> openDetailList(List<FixBomDetailDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<FixBomDetailDTO> resultList = new LinkedList<>();
        for (FixBomDetailDTO fixBomDetailDTO : list) {
            resultList.add(fixBomDetailDTO);
            resultList.addAll(openDetailList(fixBomDetailDTO.getChildNodes()));
        }
        return resultList;
    }


    private void setMocFixBomDetail(List<WipExtendIdentificationDTO> wipExtendIdentificationDTOList, List<FixBomDetailDTO> list, String cloudType) {
        List<CustomerItemsDTO> customerItemsList = this.getCustomerItemsList(wipExtendIdentificationDTOList.stream().map(e->e.getItemNo()).distinct().collect(Collectors.toList()));
        Map<String, CustomerItemsDTO> zteCodeMap = customerItemsList.stream().collect(Collectors.toMap(k -> k.getZteCode(), v -> v, (oldValue, newValue) -> newValue));
        Map<String, List<CustomerItemsDTO>> originalCustomerCodeMap = customerItemsList.stream().collect(Collectors.groupingBy(CustomerItemsDTO::getOriginalCustomerCode));
        this.setMocFixBomDetail(wipExtendIdentificationDTOList, zteCodeMap, originalCustomerCodeMap, list, cloudType);
    }

    private List<CustomerItemsDTO> getCustomerItemsList(List<String> itemNoList) {
        // 阿里客户编码
        List<SysLookupValues> lookupValues = sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115);
        List<String> specificCustomerNoList = lookupValues.stream().filter(curr -> StringUtils.isNotBlank(curr.getLookupMeaning()))
                .map(SysLookupValues::getLookupMeaning).distinct().collect(Collectors.toList());
        //客户物料代码信息
        List<CustomerItemsDTO> customerItemsList = customerItemsService.getSameItemOfZteCode(itemNoList, specificCustomerNoList);
        return customerItemsList;
    }

    private void setMocFixBomDetail(List<WipExtendIdentificationDTO> wipExtendIdentificationDTOList, Map<String, CustomerItemsDTO> zteCodeMap,
                                    Map<String, List<CustomerItemsDTO>> originalCustomerCodeMap, List<FixBomDetailDTO> fixBomDetails, String cloudType) {
        Map<String, FixBomDetailDTO> idMap = fixBomDetails.stream().collect(Collectors.toMap(k -> k.getId(), v -> v, (oldValue, newValue) -> newValue));
        for (WipExtendIdentificationDTO wipExtendIdentificationDTO : wipExtendIdentificationDTOList) {
            String itemNo = wipExtendIdentificationDTO.getItemNo();
            //根据条码zteCode取对应多个客户物料信息
            List<String> zteCodeList = this.getCustomerItemsDTOList(zteCodeMap, itemNo, originalCustomerCodeMap);

            FixBomDetailDTO mocFixBomDetailDTO = fixBomDetails.stream().filter(e -> zteCodeList.contains(e.getZteCode())
                    && Constant.FLAG_Y.equals(e.getBoxBomRequired()) && StringUtils.equals(e.getCustomerComponentType(), customerComponentType)).findFirst().orElse(null);
            if(mocFixBomDetailDTO == null){
                continue;
            }
            //上层成品料
            FixBomDetailDTO finishedMaterialsFixBomDetailDTO = this.getPreviousLayerOfFinishedMaterials(idMap, mocFixBomDetailDTO, cloudType);
            if(finishedMaterialsFixBomDetailDTO != null ){
                mocFixBomDetailDTO.setParentItemSupplierNo(finishedMaterialsFixBomDetailDTO.getItemSupplierNo());
            }
            //moc条码
            wipExtendIdentificationDTO.setFixBomDetailDTO(mocFixBomDetailDTO);
        }
    }

    /**
     * 获取上层成品料
     * @param idMap
     * @param fixBomDetailDTO
     */
    private FixBomDetailDTO getPreviousLayerOfFinishedMaterials(Map<String, FixBomDetailDTO> idMap, FixBomDetailDTO fixBomDetailDTO, String cloudType) {
        if (fixBomDetailDTO == null) {
            return null;
        }
        FixBomDetailDTO parentFixBomDetailDTO = idMap.get(fixBomDetailDTO.getParentId());
        if (parentFixBomDetailDTO == null) {
            return null;
        }
        if (Constant.COMPONENTS_OF_FINISHED_MATERIAL.equals(parentFixBomDetailDTO.getItemType())
                && Constant.FLAG_Y.equals(fixBomDetailDTO.getBoxBomRequired())
                && (!Constant.CLOUD_TYPE_HC.equalsIgnoreCase(cloudType) || Constant.FLAG_Y.equals(fixBomDetailDTO.getFixBomRequired()))) {
            return parentFixBomDetailDTO;
        } else {
            return this.getPreviousLayerOfFinishedMaterials(idMap, parentFixBomDetailDTO, cloudType);
        }
    }


    private List<String> getCustomerItemsDTOList(Map<String, CustomerItemsDTO> zteCodeMap, String itemNo, Map<String, List<CustomerItemsDTO>> originalCustomerCodeMap) {
        CustomerItemsDTO customerItemsDTO = zteCodeMap.get(itemNo);
        if (customerItemsDTO == null) {
            return Lists.newArrayList();
        }
        String originalCustomerCode = customerItemsDTO.getOriginalCustomerCode();
        List<CustomerItemsDTO> customerItemsDTOList = originalCustomerCodeMap.get(originalCustomerCode);
        if (CollectionUtils.isEmpty(customerItemsDTOList)) {
            return Lists.newArrayList();
        }
        return customerItemsDTOList.stream().map(e -> e.getZteCode()).distinct().collect(Collectors.toList());
    }

    /**
     * 复制树结构并且过滤moc物料
     *
     * @param childNodes
     * @return
     */
    private List<FixBomDetailDTO> copyTreeList(List<FixBomDetailDTO> childNodes) {
        List<FixBomDetailDTO> copyList = Lists.newArrayList();
        for (FixBomDetailDTO childNode : childNodes) {
            //过滤掉moc条码的物料代码
            if (StringUtils.equals(childNode.getCustomerComponentType(),customerComponentType)) {
                continue;
            }
            FixBomDetailDTO tempFixBomDetailDTO = new FixBomDetailDTO();
            BeanUtils.copyProperties(childNode, tempFixBomDetailDTO);
            List<FixBomDetailDTO> nodes = childNode.getChildNodes();
            if (CollectionUtils.isNotEmpty(nodes)) {
                List<FixBomDetailDTO> tempList = this.copyTreeList(nodes);
                tempFixBomDetailDTO.setChildNodes(tempList);
            }
            copyList.add(tempFixBomDetailDTO);
        }
        return copyList;
    }

    private void dealMocBarcode(List<WipExtendIdentificationDTO> wipExtendIdentificationDTOList,
                                List<FinishedProductStorageCargoTransportDTO> childFinishList,
                                FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO,boolean fromCargoTransport) {
        //moc条码
        List<WipExtendIdentificationDTO> mocList = wipExtendIdentificationDTOList.stream().filter(e -> e.getFixBomDetailDTO() != null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mocList)) {
            mocList = mocList.stream().sorted(Comparator.comparing(WipExtendIdentificationDTO::getSn)).collect(Collectors.toList());
            finishedProductStorageCargoTransportDTO.setBarcode(mocList.get(NUM_ZERO).getSn()+Constant.MOC_SUFFIX);
            for (WipExtendIdentificationDTO wipExtendIdentificationDTO : mocList) {
                FinishedProductStorageCargoTransportDTO childDTO = new FinishedProductStorageCargoTransportDTO();
                FixBomDetailDTO fixBomDetailDTO = wipExtendIdentificationDTO.getFixBomDetailDTO();
                childDTO.setBarcode(wipExtendIdentificationDTO.getSn());
                childDTO.setBarcodeType(Constant.STR_TWO);
                //货运的接口需要返回上层成品料料号，imes的返回当前节点的料号
                this.setMaterialName(fromCargoTransport, childDTO, fixBomDetailDTO);
                childDTO.setCustomerComponentType(mocCustomerComponentType);
                childFinishList.add(childDTO);
            }
        }
    }

    private void setMaterialName(boolean fromCargoTransport, FinishedProductStorageCargoTransportDTO childDTO, FixBomDetailDTO fixBomDetailDTO) {
        if(fromCargoTransport){
            childDTO.setMaterialName(fixBomDetailDTO.getParentItemSupplierNo());
        }else{
            childDTO.setMaterialName(fixBomDetailDTO.getItemSupplierNo());
        }
    }

    /**
     * 更新条码推送表，虚拟moc条码信息
     *
     * @param list list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVirtualSnBatch(List<PushStdModelSnData> list) {
        List<List<PushStdModelSnData>> splitList = CommonUtils.splitList(list, Constant.INT_50);
        splitList.forEach(items-> pushStdModelSnDataRepository.updateVirtualSnBatch(items));
    }



    private void dealXnBarcode(List<WipExtendIdentificationDTO> list, List<FinishedProductStorageCargoTransportDTO> childFinishList) {
        List<WipExtendIdentificationDTO> xnList = list.stream().filter(e->StringUtils.isNotEmpty(e.getVirtualSn())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(xnList)){
            return;
        }
        Map<String, WipExtendIdentificationDTO> map = xnList.stream().collect(Collectors.toMap(k -> k.getVirtualSn(), v -> v, (oldValue, newValue) -> newValue));
        for (WipExtendIdentificationDTO wipExtendIdentificationDTO : map.values()) {
            FinishedProductStorageCargoTransportDTO childFinishDTO = new FinishedProductStorageCargoTransportDTO();
            childFinishDTO.setBarcode(wipExtendIdentificationDTO.getVirtualSn());
            childFinishDTO.setBarcodeType(Constant.STR_ONE);
            childFinishDTO.setMaterialName(wipExtendIdentificationDTO.getItemSupplierNo());
            childFinishDTO.setCustomerComponentType(wipExtendIdentificationDTO.getCustomerComponentType());
            childFinishList.add(childFinishDTO);
        }
    }
}



