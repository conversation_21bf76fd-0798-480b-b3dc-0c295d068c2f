package com.zte.application.impl;

import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.entity.AlarmSeverityEnum;
import com.zte.application.CustomerItemsService;
import com.zte.application.ModelRcvBillService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.ModelRcvBillRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.InoneRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.CartonRelationCategory;
import com.zte.interfaces.dto.CartonSnRelationCategory;
import com.zte.interfaces.dto.ChangeCartonNoDTO;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.interfaces.dto.ModelChangedCartonDTO;
import com.zte.interfaces.dto.ModelRcvBillDTO;
import com.zte.interfaces.dto.ModelRcvBillDetailDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.ReplaceCartonRelationCategory;
import com.zte.interfaces.dto.ReplaceCartonRelationDTO;
import com.zte.interfaces.dto.SyncCartonSnRelationOperateDTO;
import com.zte.interfaces.dto.SyncCartonSnRelationOperateDetailDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackDataDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackResultDTO;
import com.zte.interfaces.dto.datawb.PickListQueryDTO;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.annotation.RecordLogAnnotation;
import com.zte.springbootframe.common.annotation.RedisDistributedLockAnnotation;
import com.zte.springbootframe.common.annotation.TransmittableHeader;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.NoticeCenterUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.common.utils.NumConstant.*;

@Service("modelRcvBillService")
public class ModelRcvBillServiceImpl implements ModelRcvBillService {

    @Autowired
    private ModelRcvBillRepository modelRcvBillRepository;
    @Autowired
    private SysLookupValuesService sysLookupValuesService;
    @Autowired
    private PsTaskExtendedService psTaskExtendedService;
    @Autowired
    private CustomerItemsService customerItemsService;
    @Autowired
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Autowired
    private TradeDataLogService tradeDataLogService;
    @Autowired
    private IscpRemoteService iscpRemoteService;
    @Autowired
    private InoneRemoteService inoneRemoteService;
    @Autowired
    private ModelRcvBillService modelRcvBillService;
    @Autowired
    private NoticeCenterUtils noticeCenterUtils;

    @Value("${sleep.time:10000}")
    private int pushSleepTime ;

    @Override
    public Integer queryMesModelRcvBill() {
        int count = Constant.INT_0;
        //1.获取查询时间范围条件
        PickListQueryDTO pickListQueryDTO = getTimeRange();
        List<ProdPickListMainDTO> prodPickList = new ArrayList<>();
        //正常领料信息
        prodPickList.addAll(DatawbRemoteService.queryPickListByTaskNos(pickListQueryDTO));
        //维修领料信息
        prodPickList.addAll(inoneRemoteService.getPicklistMain(pickListQueryDTO));
        if (CollectionUtils.isEmpty(prodPickList)) {
            return count;
        }
        //1.2查询凭证单号与领料单号关系，留下凭证单号不为空且状态为9的
        List<String> billNos = prodPickList.stream()
                .filter(curr -> StringUtils.isNotBlank(curr.getBillNumber()))
                .map(ProdPickListMainDTO::getBillNumber).collect(Collectors.toList());
        List<EdiSoSDTO> ediSosDtoList = this.getSendMaterialsList(billNos)
                .stream()
                .filter(dto -> dto.getExternalorderkey2() != null && Constant.STR_9.equals(dto.getStatus()))
                .collect(Collectors.toList());
        //1.3赋值taskNo任务号
        Map<String, String> billNumberToTaskNoMap = prodPickList.stream()
                .filter(curr -> StringUtils.isNotBlank(curr.getBillNumber()) && StringUtils.isNotBlank(curr.getTaskNo()))
                .collect(Collectors.toMap(
                        ProdPickListMainDTO::getBillNumber,
                        ProdPickListMainDTO::getTaskNo,
                        (existing, replacement) -> existing
                ));
        ediSosDtoList.forEach(ediSo -> {
            String orderNo = ediSo.getOrderNo();
            if (billNumberToTaskNoMap.containsKey(orderNo)) {
                ediSo.setTaskNo(billNumberToTaskNoMap.get(orderNo));
            }
        });
        //2.根据任务号查询客户编码
        List<String> taskNos = ediSosDtoList.stream().map(EdiSoSDTO::getTaskNo).distinct().collect(Collectors.toList());
        List<PsTaskExtendedDTO> psTaskExtendedDTOS = psTaskExtendedService.queryByTaskNos(taskNos);
        //3.查询阿里配置客户编码
        List<SysLookupValues> typesDTOList = sysLookupValuesService.selectValuesByType(Constant.LOOKUP_VALUE_1004115);
        Set<String> customerNoSet = typesDTOList.stream().map(SysLookupValues::getLookupMeaning).collect(Collectors.toSet());
        // 4. 构建 TaskNo -> CustomerNo 的 Map，并过滤出 customerNoSet 中存在的客户编码
        Map<String, String> customerTaskNoMap = psTaskExtendedDTOS.stream()
                .filter(dto -> null != dto.getCustomerNo()
                        && customerNoSet.contains(dto.getCustomerNo()))
                .collect(Collectors.toMap(
                        PsTaskExtendedDTO::getTaskNo,
                        PsTaskExtendedDTO::getCustomerNo,
                        (existing, replacement) -> existing
                ));
        //5.构建参数
        List<ModelRcvBillDTO> modelRcvBillDTOList = new ArrayList<>();
        for (EdiSoSDTO ediSosDTO : ediSosDtoList) {
            if (customerTaskNoMap.containsKey(ediSosDTO.getTaskNo())) {
                ModelRcvBillDTO modelRcvBillDTO = new ModelRcvBillDTO();
                modelRcvBillDTO.setBillNo(ediSosDTO.getOrderNo());
                modelRcvBillDTO.setTaskNo(ediSosDTO.getTaskNo());
                modelRcvBillDTO.setCustomerName(customerTaskNoMap.get(ediSosDTO.getTaskNo()));
                modelRcvBillDTO.setPushStatus(Constant.INT_0); //推送状态 0:待推送
                modelRcvBillDTO.setRcvNo(ediSosDTO.getExternalorderkey2());
                modelRcvBillDTO.setCreateBy(Constant.SYSTEM);
                modelRcvBillDTO.setLastUpdatedBy(Constant.SYSTEM);
                modelRcvBillDTOList.add(modelRcvBillDTO);
            }
        }
        if (modelRcvBillDTOList.isEmpty()) {
            return count;
        }
        for (List<ModelRcvBillDTO> tempList : CommonUtils.splitList(modelRcvBillDTOList, Constant.INT_50)) {
            count += modelRcvBillRepository.batchInsertIgnoreExisted(tempList);
        }
        return count;
    }

    @Override
    public void b2bCallBackUpdateStatus(B2bCallBackNewDTO b2bCallBackNewDTO) {
        if (StringUtils.isBlank(b2bCallBackNewDTO.getKeywords())) {
            return;
        }
        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        boolean success = this.checkSuccess(b2bCallBackNewDTO);
        pushStdModelDataDTO.setPushStatus(success ? NUM_TWO : NUM_NINE);
        if (!success) {
            AlarmHelper.alarm("imes_dependent_service_error", "1001", AlarmSeverityEnum.CRITICAL, Constant.B2B_ALARM_TITLE, JSON.toJSONString(b2bCallBackNewDTO));
            pushStdModelDataDTO.setErrorMsg(StringUtils.substring(b2bCallBackNewDTO.getData(), NUM_ZERO, NumConstant.NUM_1500));
        }else {
            pushStdModelDataDTO.setErrorMsg(Constant.STRING_EMPTY);
        }
        if (Constant.MESSAGE_TYPE_CREATE_CARTO_RELATION.equals(b2bCallBackNewDTO.getMessageType())) {
            pushStdModelDataDTO.setRcvNo(b2bCallBackNewDTO.getKeywords());//凭证单号
            modelRcvBillRepository.updateStatus(Collections.singletonList(pushStdModelDataDTO));
        } else if (Constant.MESSAGE_TYPE_UPDATE_CARTO_RELATION.equals(b2bCallBackNewDTO.getMessageType())) {
            pushStdModelDataDTO.setCartonNo(b2bCallBackNewDTO.getKeywords());//箱号
            modelRcvBillRepository.updateStatusByCartonNo(Collections.singletonList(pushStdModelDataDTO));
        }
    }

    @Override
    @RecordLogAnnotation("箱包修改消费kafka消息")
    @TransmittableHeader
    public void consumeChangeCartonNo(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        ChangeCartonNoDTO changeCartonNoDTO = JacksonJsonConverUtil.jsonToBean(msg, new TypeReference<ChangeCartonNoDTO>() {
        });
        if (null == changeCartonNoDTO || CollectionUtils.isEmpty(changeCartonNoDTO.getChangedContainerBarcodeList())) {
            return;
        }
        List<ModelChangedCartonDTO> modelChangedCartonList = new ArrayList<>();
        for (String cartonNo : changeCartonNoDTO.getChangedContainerBarcodeList()) {
            ModelChangedCartonDTO modelChangedCartonDTO = new ModelChangedCartonDTO();
            modelChangedCartonDTO.setId(UUID.randomUUID().toString().trim().replaceAll(Constant.LINE, Constant.STRING_EMPTY));
            modelChangedCartonDTO.setCartonNo(cartonNo);
            modelChangedCartonDTO.setPushStatus(Constant.INT_0);
            modelChangedCartonDTO.setCreateBy(Constant.IMES);
            modelChangedCartonDTO.setLastUpdatedBy(Constant.IMES);
            modelChangedCartonList.add(modelChangedCartonDTO);
        }
        for (List<ModelChangedCartonDTO> tempList : CommonUtils.splitList(modelChangedCartonList, Constant.INT_50)) {
            modelRcvBillRepository.batchInsertChangeCartonNo(tempList);
        }
    }

    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "pushPackListAndSnToKafka", factoryId = true, redisLockTime = 600,
            lockFailMsgZh = "正在进行箱包同步操作请稍后再试", lockFailMsgEn = "The synchronization operation is in progress. Please try again later", redisLockParam = {})
    public Integer pushPackListAndSnToKafka() throws Exception {
        LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES);
        //1.获取近x天待推送的领料单
        ModelRcvBillDTO modelRcvBillDTO = getQueryCondition(Constant.LOOKUP_VALUE_1004113004);
        List<ModelRcvBillDTO> modelRcvBills = modelRcvBillRepository.getByCondition(modelRcvBillDTO);
        if (CollectionUtils.isEmpty(modelRcvBills)) {
            return NUM_ZERO;
        }
        // 待推送凭证单号
        List<String> needPushRcvNos = modelRcvBills.stream().map(ModelRcvBillDTO::getRcvNo).distinct().collect(Collectors.toList());
        // 收集校验不通过的单据
        Map<String, String> invalidMap = new HashMap<>();
        Map<String, String> billNoMap = modelRcvBills.stream()
                .collect(Collectors.toMap(
                        ModelRcvBillDTO::getBillNo,
                        ModelRcvBillDTO::getTaskNo,
                        (existing, replacement) -> existing
                ));
        //1.1查询客户部件类型，构建参数用
        List<String> taskNos = modelRcvBills.stream().map(ModelRcvBillDTO::getTaskNo).distinct().collect(Collectors.toList());
        Map<String, String> customerPartTypeMap = this.getCustomerComponentType(taskNos);
        //3.查询字典配置的仓库号，查询发料记录，保留status为9的单据
        List<String> billNos = modelRcvBills.stream().map(ModelRcvBillDTO::getBillNo).distinct().collect(Collectors.toList());
        List<EdiSoSDTO> sendRecordList = this.getSendMaterialsList(billNos);
        List<EdiSoSDTO> validSendRecordList = this.filterSendRecordList(sendRecordList, invalidMap, lmb);
        //4.物料代码是阿里管控料才要传给阿里
        List<EdiSoSDTO> ediSoList = this.getSuitableCustomerItems(validSendRecordList, customerPartTypeMap, billNoMap, invalidMap, lmb);
        Map<String, EdiSoSDTO> fromIdMap = ediSoList.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getFromId() + Constant.UNDER_LINE + dto.getOrderNo() + Constant.UNDER_LINE + dto.getExternalorderkey2(),
                        dto -> dto,
                        (existing, replacement) -> existing
                ));
        Map<String, List<BarcodeExpandVO>> cartonNoMap = new HashMap<>();
        Set<String> errList = new HashSet<>();
        for (EdiSoSDTO ediSo : ediSoList) {
            String rcvNo = ediSo.getExternalorderkey2();
            if (!needPushRcvNos.contains(rcvNo)) {
                //只推送待推送的凭证单,防止重复推
                continue;
            }
            String cartonNo = ediSo.getFromId();
            String orderNo = ediSo.getOrderNo();
            List<BarcodeExpandVO> barCodeList = barcodeCenterRemoteService.getSnByBarCode(cartonNo, orderNo);
            if(CollectionUtils.isEmpty(barCodeList)){
                invalidMap.putIfAbsent(rcvNo, lmb.getMessage(MessageId.SN_CENTER_LESS_THAN_INFOR_ERR,
                        new String[]{Constant.STR_0, ediSo.getQty().toString()}));
                continue;
            }
            //若箱关联到是SN数量与inFor发料数量相等，则认为生产已完成接收扫描
            if (barCodeList.size() == ediSo.getQty().intValue()) {
                cartonNoMap.put(cartonNo + Constant.UNDER_LINE + orderNo + Constant.UNDER_LINE + rcvNo, barCodeList);
                continue;
            }
            //若箱关联到是SN数量大于inFor发料数量，则触发告警，记录校验不通过信息
            if (barCodeList.size() > ediSo.getQty().intValue()) {
                List<String> snList = barCodeList.stream().map(BarcodeExpandVO::getBarcode).collect(Collectors.toList());
                errList.add(lmb.getMessage(MessageId.SN_CARTON_RELATION_ERR,
                        new String[]{ediSo.getQty().toString(), String.valueOf(snList.size()), ediSo.getOrderNo(), cartonNo, snList.toString()}));
                invalidMap.putIfAbsent(rcvNo, lmb.getMessage(MessageId.SN_CENTER_GREATER_THAN_INFOR_ERR,
                        new String[]{String.valueOf(snList.size()), ediSo.getQty().toString()}));
                continue;
            }
            //若箱关联到是SN数量小于inFor发料数量，记录校验不通过信息
            if (barCodeList.size() < ediSo.getQty().intValue()) {
                invalidMap.putIfAbsent(rcvNo, lmb.getMessage(MessageId.SN_CENTER_LESS_THAN_INFOR_ERR,
                        new String[]{String.valueOf(barCodeList.size()), ediSo.getQty().toString()}));
            }
        }
        this.next(cartonNoMap, fromIdMap, errList, invalidMap);
        return NUM_ONE;
    }

    private void next(Map<String, List<BarcodeExpandVO>> cartonNoMap, Map<String, EdiSoSDTO> fromIdMap, Set<String> errList, Map<String, String> invalidMap) {
        if(!invalidMap.isEmpty()){
            List<ModelRcvBillDTO> updateRcvBills = new ArrayList<>();
            for (Map.Entry<String, String> entry : invalidMap.entrySet()) {
                ModelRcvBillDTO dto = new ModelRcvBillDTO();
                dto.setRcvNo(entry.getKey());
                dto.setErrorMsg(entry.getValue());
                updateRcvBills.add(dto);
            }
            modelRcvBillRepository.updateErrorMsg(updateRcvBills);
        }
        if (!cartonNoMap.isEmpty()) {
            modelRcvBillService.pushB2bAndHandleData(cartonNoMap, fromIdMap);
        }
        if (!errList.isEmpty()) {
            noticeCenterUtils.sendEmail(Constant.LOOK_UP_VALUE_7599004, String.join(Constant.EMAIL_PREFIX, errList));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushB2bAndHandleData(Map<String, List<BarcodeExpandVO>> cartonNoMap, Map<String, EdiSoSDTO> fromIdMap) {
        List<ModelRcvBillDetailDTO> modelRcvBillDetailList = new ArrayList<>();
        List<CustomerDataLogDTO> aliResultList = buildAliPublicCloudParams(cartonNoMap, fromIdMap);
        List<CustomerDataLogDTO> resultList = buildB2bParams(cartonNoMap, fromIdMap, modelRcvBillDetailList);
        this.addPushDataAndUpdateStatus(modelRcvBillDetailList);
        //5.4.1 厂商新增或取消箱包与SN绑定 ZTEiMES-Alibaba-SyncCartonSnRelation
        String uploadFlag = Constant.FLAG_N;
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_VALUE_8886003);
        if (null != sysLookupValues) {
            uploadFlag = sysLookupValues.getLookupMeaning();
        }
        if(Constant.FLAG_Y.equals(uploadFlag)) {
            tradeDataLogService.pushDataOfExceptionRollback(aliResultList);
            try {
                Thread.sleep(pushSleepTime);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        //4.3.4 绑定箱包与SN ZTEiMES-Alibaba-CreateCartonRelation
        tradeDataLogService.pushDataOfExceptionRollback(resultList);
    }

    @Override
    @RedisDistributedLockAnnotation(redisPrefix = "pushModelChangeCartonToKafka", factoryId = true, redisLockTime = 600,
            lockFailMsgZh = "正在进行箱包修改同步操作请稍后再试", lockFailMsgEn = "The synchronization operation is in progress. Please try again later", redisLockParam = {})
    public Integer pushModelChangeCartonToKafka() {
        //1.获取近x天待推送的变化箱号SN关系
        ModelRcvBillDTO modelRcvBillDTO = getQueryCondition(Constant.LOOKUP_VALUE_1004113005);
        List<ModelChangedCartonDTO> changedCartonList = modelRcvBillRepository.getChangeSNByCondition(modelRcvBillDTO);
        if (CollectionUtils.isEmpty(changedCartonList)) {
            return NUM_ZERO;
        }
        //2.查询历史推送记录表，若未上传过则忽略此箱号。
        Map<String, ModelChangedCartonDTO> newCartonNoMap = changedCartonList.stream()
                .collect(Collectors.toMap(
                        ModelChangedCartonDTO::getCartonNo,
                        dto -> dto,
                        (existing, replacement) -> existing
                ));
        List<String> cartonNoList = new ArrayList<>(newCartonNoMap.keySet());
        List<ModelRcvBillDetailDTO> modelRcvBillDetailList = modelRcvBillRepository.getModelRcvBillDetailByCartonNo(cartonNoList);
        //2.1获取部件类型，构建参数用
        List<String> taskNos = modelRcvBillDetailList.stream().map(ModelRcvBillDetailDTO::getTaskNo).distinct().collect(Collectors.toList());
        Map<String, String> taskNoMap = getCustomerComponentType(taskNos);
        //3.若上传过则根据箱号调用条码中心接口获取其关联条码
        Map<String, List<ModelRcvBillDetailDTO>> cartonNoMap = modelRcvBillDetailList.stream()
                .collect(Collectors.groupingBy(detail -> detail.getCartonNo() + Constant.UNDER_LINE + detail.getRcvNo()));
        List<CustomerDataLogDTO> resultList = new ArrayList<>();
        List<String> uploadedCartonNos = new ArrayList<>();
        List<String> judgeNeedAlarmCartonNos = new ArrayList<>();
        List<String> needAlarmCartonNos = new ArrayList<>();
        for (Map.Entry<String, List<ModelRcvBillDetailDTO>> entry : cartonNoMap.entrySet()) {
            String cartonNo = entry.getValue().get(Constant.INT_0).getCartonNo();
            String rcvNo = entry.getValue().get(Constant.INT_0).getRcvNo();
            List<ModelRcvBillDetailDTO> oldSnList = entry.getValue();
            List<BarcodeExpandVO> newBarCodeList = barcodeCenterRemoteService.getSnByBarCodeAndRcvNo(cartonNo, rcvNo);
            //4.判断箱在条码中心的条码数量是否等于该箱在上传记录表的条码数量，若相等将条码中心的SN与上传记录中的SN进行比对，获取有差异的条码调用阿里箱包与SN接口将新的箱包关系传递给阿里
            if (newBarCodeList.size() == oldSnList.size()) {
                Map<String, String> oldSnOrderNoMap = oldSnList.stream()
                        .collect(Collectors.toMap(
                                ModelRcvBillDetailDTO::getSnNo,
                                ModelRcvBillDetailDTO::getOrderNo
                        ));
                Set<String> oldSnSet = new HashSet<>(oldSnOrderNoMap.keySet());
                Set<String> newBarCodeSet = newBarCodeList.stream()
                        .map(BarcodeExpandVO::getBarcode)
                        .collect(Collectors.toSet());
                // 计算仅在 oldSnSet 但不在 newBarCodeSet 的 SN（差集）, newBarCodeSet 但不在 oldSnSet 的 SN（差集）
                Set<String> onlyInOldSn = new HashSet<>(oldSnSet);
                onlyInOldSn.removeAll(newBarCodeSet);
                Set<String> onlyInnNewBarCode = new HashSet<>(newBarCodeSet);
                onlyInnNewBarCode.removeAll(oldSnSet);
                List<String> oldSns = new ArrayList<>(onlyInOldSn);
                List<String> newBarCodes = new ArrayList<>(onlyInnNewBarCode);
                if (!oldSns.isEmpty()) {
                    ReplaceCartonRelationCategory cartonRelationCategory = new ReplaceCartonRelationCategory();
                    List<ReplaceCartonRelationDTO> replaceList = new ArrayList<>();
                    for (int i = Constant.INT_0; i < oldSns.size(); i++) {
                        ReplaceCartonRelationDTO cartonRelationDTO = new ReplaceCartonRelationDTO();
                        cartonRelationDTO.setOrderNo(oldSnOrderNoMap.get(oldSns.get(i)));
                        cartonRelationDTO.setOldSnNo(oldSns.get(i));
                        cartonRelationDTO.setNewSnNo(newBarCodes.get(i));
                        replaceList.add(cartonRelationDTO);
                    }
                    cartonRelationCategory.setCategory(taskNoMap.get(oldSnList.get(Constant.INT_0).getTaskNo()));
                    cartonRelationCategory.setReplaceCartonRelationList(replaceList);
                    CustomerDataLogDTO customerDataLogDTO = getCustomerDataLogDTO(cartonNo, cartonRelationCategory);
                    resultList.add(customerDataLogDTO);
                    uploadedCartonNos.add(cartonNo);
                }
            } else if (newBarCodeList.size() < oldSnList.size()) {
                //4.1若箱在条码中心的条码数量 < 该箱在上传记录表中的条码数量，则暂时不传，待下次定时任务重新比对，超过字典阈值如果还不满足触发告警“XX箱未完成绑定”。
                judgeSendEmail(newCartonNoMap, cartonNo, judgeNeedAlarmCartonNos);
            } else {
                //4.2若箱在条码中心关联的条码数量 > 该箱在上传记录表中的条码数量，触发告警"XX箱条码数量>infor数量，不能上传。”
                needAlarmCartonNos.add(cartonNo);
            }
        }
        modelRcvBillService.pushB2BAndUpdateChangeCartons(uploadedCartonNos, resultList);
        buildEmailParams(MessageId.BARCODES_IN_BOX_IS_GREATER_THAN_INFOR_CODES, needAlarmCartonNos);
        buildEmailParams(MessageId.BOX_HAS_NOT_BEEN_FULLY_BOUND, judgeNeedAlarmCartonNos);
        return NUM_ONE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushB2BAndUpdateChangeCartons(List<String> uploadedCartonNos, List<CustomerDataLogDTO> resultList) {
        if (resultList.isEmpty()) {
            return;
        }
        List<PushStdModelDataDTO> result = uploadedCartonNos.stream()
                .map(cartonNo -> {
                    PushStdModelDataDTO dto = new PushStdModelDataDTO();
                    dto.setCartonNo(cartonNo);
                    dto.setPushStatus(Constant.INT_1);
                    dto.setPushDate(new Date());
                    return dto;
                }).distinct().collect(Collectors.toList());
        modelRcvBillRepository.updateStatusByCartonNo(result);
        tradeDataLogService.pushDataOfExceptionRollback(resultList);
    }

    private List<EdiSoSDTO> filterSendRecordList(List<EdiSoSDTO> sendRecordList, Map<String, String> invalidMap, LocaleMessageSourceBean lmb) {
        List<EdiSoSDTO> validSendRecordList = new ArrayList<>();
        for (EdiSoSDTO record : sendRecordList) {
            if (Constant.STR_9.equals(record.getStatus())) {
                validSendRecordList.add(record);
            } else {
                invalidMap.putIfAbsent(record.getExternalorderkey2(), lmb.getMessage(MessageId.INFOR_STATUS_IS_NOT_EQUAL_TO_9,
                        new String[]{record.getStatus()}));
            }
        }
        return validSendRecordList;
    }

    private void judgeSendEmail(Map<String, ModelChangedCartonDTO> newCartonNoMap, String cartonNo, List<String> judgeNeedAlarmCartonNos) {
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_VALUE_1004113006);
        if (null == sysLookupValues || StringUtils.isEmpty(sysLookupValues.getLookupMeaning())) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_VALUE_1004113006});
        }
        ModelChangedCartonDTO modelChangedCartonDTO = newCartonNoMap.get(cartonNo);
        int daysThreshold = Integer.parseInt(sysLookupValues.getLookupMeaning());
        Date createDate = Opt.ofNullable(modelChangedCartonDTO).map(ModelChangedCartonDTO::getCreateDate).orElseThrow(() -> new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CAPACITY_DAY_NULL));
        Date now = new Date();
        long diffInMillis = now.getTime() - createDate.getTime();
        long daysBetween = TimeUnit.MILLISECONDS.toDays(diffInMillis);
        if (daysBetween > daysThreshold) {
            // 超过阈值，触发告警
            judgeNeedAlarmCartonNos.add(cartonNo);
        }
    }

    private void buildEmailParams(String message, List<String> cartonNoList) {
        if (cartonNoList.isEmpty()) {
            return;
        }
        LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES);
        Set<String> errList = new HashSet<>();
        for (String cartonNo : cartonNoList) {
            errList.add(lmb.getMessage(message,
                    new String[]{cartonNo}));
        }
        noticeCenterUtils.sendEmail(Constant.LOOK_UP_VALUE_7599004, String.join(Constant.EMAIL_PREFIX, errList));
    }

    private CustomerDataLogDTO getCustomerDataLogDTO(String cartonNo, ReplaceCartonRelationCategory cartonRelationCategory) {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setId(UUID.randomUUID().toString().trim().replaceAll(Constant.LINE, Constant.STRING_EMPTY));
        customerDataLogDTO.setOrigin(Constant.IMES);
        customerDataLogDTO.setCustomerName(Constant.ALIBABA);
        customerDataLogDTO.setProjectName(Constant.ALIBABA);
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_UPDATE_CARTO_RELATION);
        customerDataLogDTO.setFactoryId(Integer.valueOf(Constant.FACTORY_ID_CENTER));
        customerDataLogDTO.setCreateBy(Constant.IMES);
        customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
        customerDataLogDTO.setKeywords(cartonNo);//关键词:箱号
        customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(cartonRelationCategory));
        return customerDataLogDTO;
    }

    private Map<String, String> getCustomerComponentType(List<String> taskNos) {
        List<PsTaskExtendedDTO> psTaskExtendedDTOList = psTaskExtendedService.queryByTaskNos(taskNos);
        return psTaskExtendedDTOList.stream().collect(Collectors.toMap(
                PsTaskExtendedDTO::getTaskNo,
                PsTaskExtendedDTO::getCustomerPartType
        ));
    }

    private List<EdiSoSDTO> getSuitableCustomerItems(List<EdiSoSDTO> sendRecordList, Map<String, String> customerPartTypeMap, Map<String, String> billNoMap, Map<String, String> invalidMap, LocaleMessageSourceBean lmb) throws Exception {
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        List<String> zteCodes = sendRecordList.stream().map(EdiSoSDTO::getSku).collect(Collectors.toList());
        List<CivControlInfoDTO> infoDTOList = iscpRemoteService.queryAliControlInfoList(zteCodes);
        List<String> itemNos = infoDTOList.stream()
                .filter(dto -> null != dto.getBarcodeControlType() && NUM_FORTY == dto.getBarcodeControlType())
                .map(CivControlInfoDTO::getItemNo)
                .collect(Collectors.toList());
        customerItemsDTO.setItemNoList(itemNos);
        List<CustomerItemsDTO> customerItemList = customerItemsService.queryListByCustomerList(customerItemsDTO);
        Map<String, String> zteCodeMap = customerItemList.stream()
                .collect(Collectors.toMap(
                        CustomerItemsDTO::getZteCode,
                        CustomerItemsDTO::getCustomerCode,
                        (existing, replacement) -> existing
                ));
        List<EdiSoSDTO> ediSoSDTOList = new ArrayList<>();
        for (EdiSoSDTO item : sendRecordList) {
            String zteCode = item.getSku();
            if (zteCodeMap.containsKey(zteCode)) {
                String billNo = item.getOrderNo();
                String taskNo = billNoMap.get(billNo);
                //客户部件类型
                item.setCustomerComponentType(customerPartTypeMap.get(taskNo));
                //客户物料代码
                item.setCustomerCode(zteCodeMap.get(zteCode));
                ediSoSDTOList.add(item);
            }else {
                invalidMap.putIfAbsent(item.getExternalorderkey2(),lmb.getMessage(MessageId.SKU_IS_NOT_ALI_CONTROL_OR_ALI_CODE, new String[]{zteCode}));
            }
        }
        return ediSoSDTOList;
    }


    private void addPushDataAndUpdateStatus(List<ModelRcvBillDetailDTO> modelRcvBillDetailList) {
        if (modelRcvBillDetailList.isEmpty()) {
            return;
        }
        //先根据凭证单号删除，再进行插入(防止重推导致SN重复)
        for (List<ModelRcvBillDetailDTO> tempList : CommonUtils.splitList(modelRcvBillDetailList, Constant.INT_50)) {
            List<String> rcvNos = tempList.stream().map(ModelRcvBillDetailDTO::getOrderNo).distinct().collect(Collectors.toList());
            modelRcvBillRepository.deleteExistedSn(rcvNos);
            modelRcvBillRepository.batchInsert(tempList);
        }
        List<PushStdModelDataDTO> result = modelRcvBillDetailList.stream()
                .map(detail -> {
                    PushStdModelDataDTO dto = new PushStdModelDataDTO();
                    dto.setRcvNo(detail.getOrderNo());
                    dto.setErrorMsg(Constant.STRING_EMPTY);
                    dto.setPushStatus(Constant.INT_1);
                    dto.setPushDate(new Date());
                    return dto;
                }).distinct().collect(Collectors.toList());
        modelRcvBillRepository.updateStatus(result);
    }

    private List<EdiSoSDTO> getSendMaterialsList(List<String> completeBillNos){
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_VALUE_8886002);
        if (null == sysLookupValues || null == sysLookupValues.getLookupMeaning()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_VALUE_1004113003});
        }
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setExternalorderkey2List(completeBillNos);
        ediSoSDTO.setWarehouseIds(sysLookupValues.getLookupMeaning());
        return DatawbRemoteService.getSendMaterials(ediSoSDTO);
    }

    private List<CustomerDataLogDTO> buildAliPublicCloudParams(Map<String, List<BarcodeExpandVO>> cartonNoMap, Map<String, EdiSoSDTO> fromIdMap) {
        List<CustomerDataLogDTO> resultList = new ArrayList<>();
        for (String key : cartonNoMap.keySet()) {
            EdiSoSDTO ediSoSDTO = fromIdMap.get(key);
            List<BarcodeExpandVO> snList = cartonNoMap.get(key);
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(UUID.randomUUID().toString().trim().replaceAll(Constant.LINE, Constant.STRING_EMPTY));
            customerDataLogDTO.setOrigin(Constant.IMES);
            customerDataLogDTO.setCustomerName(Constant.ALIBABA);
            customerDataLogDTO.setProjectName(Constant.ALIBABA);
            customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_SYNC_CARTO_RELATION);
            customerDataLogDTO.setFactoryId(Integer.valueOf(Constant.FACTORY_ID_CENTER));
            customerDataLogDTO.setCreateBy(Constant.IMES);
            customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
            customerDataLogDTO.setKeywords(ediSoSDTO.getExternalorderkey2());//关键词:凭证单号

            CartonSnRelationCategory cartonSnRelationCategory = getCartonSnRelationCategory(ediSoSDTO, snList);

            customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(cartonSnRelationCategory));
            resultList.add(customerDataLogDTO);
        }
        return resultList;
    }

    private CartonSnRelationCategory getCartonSnRelationCategory(EdiSoSDTO ediSosDTO, List<BarcodeExpandVO> snList) {
        CartonSnRelationCategory cartonSnRelationCategory = new CartonSnRelationCategory();
        cartonSnRelationCategory.setOperateType(Constant.STR_0);
        cartonSnRelationCategory.setSource(Constant.ZTE101);

        List<SyncCartonSnRelationOperateDTO> operateDataList = new ArrayList<>();
        SyncCartonSnRelationOperateDTO snRelationOperateDTO = new SyncCartonSnRelationOperateDTO();
        snRelationOperateDTO.setCartonId(ediSosDTO.getFromId());//箱包号
        snRelationOperateDTO.setMpn(ediSosDTO.getCustomerCode());//客户物料代码

        List<SyncCartonSnRelationOperateDetailDTO> operateDetailList = new ArrayList<>();
        for (BarcodeExpandVO barcodeExpandVO : snList) {
            SyncCartonSnRelationOperateDetailDTO snRelationOperateDetailDTO = new SyncCartonSnRelationOperateDetailDTO();
            snRelationOperateDetailDTO.setSn(barcodeExpandVO.getBarcode());//发料条码
            operateDetailList.add(snRelationOperateDetailDTO);
        }
        snRelationOperateDTO.setOperateDetailList(operateDetailList);
        operateDataList.add(snRelationOperateDTO);
        cartonSnRelationCategory.setOperateData(operateDataList);
        return cartonSnRelationCategory;
    }

    private List<CustomerDataLogDTO> buildB2bParams(Map<String, List<BarcodeExpandVO>> cartonNoMap, Map<String, EdiSoSDTO> fromIdMap, List<ModelRcvBillDetailDTO> modelRcvBillDetailList) {
        List<CustomerDataLogDTO> resultList = new ArrayList<>();
        for (String key : cartonNoMap.keySet()) {
            EdiSoSDTO ediSoSDTO = fromIdMap.get(key);
            List<BarcodeExpandVO> snList = cartonNoMap.get(key);
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(UUID.randomUUID().toString().trim().replaceAll(Constant.LINE, Constant.STRING_EMPTY));
            customerDataLogDTO.setOrigin(Constant.IMES);
            customerDataLogDTO.setCustomerName(Constant.ALIBABA);
            customerDataLogDTO.setProjectName(Constant.ALIBABA);
            customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_CREATE_CARTO_RELATION);
            customerDataLogDTO.setFactoryId(Integer.valueOf(Constant.FACTORY_ID_CENTER));
            customerDataLogDTO.setCreateBy(Constant.IMES);
            customerDataLogDTO.setLastUpdatedBy(Constant.IMES);
            customerDataLogDTO.setCartonNo(ediSoSDTO.getFromId());
            customerDataLogDTO.setKeywords(ediSoSDTO.getExternalorderkey2());//关键词:凭证单号
            List<ModelRcvBillDetailDTO> modelRcvBillDetails = new ArrayList<>();
            CartonRelationCategory cartonRelationCategory = new CartonRelationCategory();
            cartonRelationCategory.setCategory(ediSoSDTO.getCustomerComponentType());
            for (BarcodeExpandVO barcodeExpandVO : snList) {
                ModelRcvBillDetailDTO modelRcvBillDetail = new ModelRcvBillDetailDTO();
                modelRcvBillDetail.setId(UUID.randomUUID().toString().trim().replaceAll(Constant.LINE, Constant.STRING_EMPTY));//主键id
                modelRcvBillDetail.setOrderNo(ediSoSDTO.getExternalorderkey2());//凭证单号
                modelRcvBillDetail.setBillNo(ediSoSDTO.getOrderNo());//领料单号
                modelRcvBillDetail.setMaterialMpn(ediSoSDTO.getCustomerCode());//客户物料代码
                modelRcvBillDetail.setStorageArea(Constant.INT_1);//库位(1原箱区2混箱区)，默认传1
                modelRcvBillDetail.setCartonNo(ediSoSDTO.getFromId());//箱包号
                modelRcvBillDetail.setSnNo(barcodeExpandVO.getBarcode());//发料条码
                modelRcvBillDetail.setCreateBy(Constant.IMES);
                modelRcvBillDetail.setLastUpdatedBy(Constant.IMES);
                modelRcvBillDetails.add(modelRcvBillDetail);
                modelRcvBillDetailList.add(modelRcvBillDetail);
            }
            cartonRelationCategory.setCreateCartonRelationList(modelRcvBillDetails);
            customerDataLogDTO.setJsonData(JacksonJsonConverUtil.beanToJson(cartonRelationCategory));
            resultList.add(customerDataLogDTO);
        }
        return resultList;
    }

    private ModelRcvBillDTO getQueryCondition(int lookupCode) {
        ModelRcvBillDTO modelRcvBillDTO = new ModelRcvBillDTO();
        //1.获取字典配置的查询时间范围
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(lookupCode);
        if (null == sysLookupValues || null == sysLookupValues.getLookupMeaning()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_VALUE_1004113003});
        }
        LocalDate currentDate = LocalDate.now();
        LocalDate oneDaysAgo = currentDate.minusDays(Long.parseLong(sysLookupValues.getLookupMeaning()));
        Date startDate = Date.from(oneDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(currentDate.atTime(Constant.INT_23, Constant.INT_59, Constant.INT_59).atZone(ZoneId.systemDefault()).toInstant());
        modelRcvBillDTO.setPushStatus(Constant.INT_0);
        modelRcvBillDTO.setStartCreateDate(startDate);
        modelRcvBillDTO.setEndCreateDate(endDate);
        return modelRcvBillDTO;
    }

    private PickListQueryDTO getTimeRange() {
        PickListQueryDTO pickListQueryDTO = new PickListQueryDTO();
        //1.获取字典配置的查询时间范围
        SysLookupValues sysLookupValues = sysLookupValuesService.findByLookupCode(Constant.LOOKUP_VALUE_1004113003);
        if (null == sysLookupValues || null == sysLookupValues.getLookupMeaning()) {
            throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SYS_LOOK_NOT_CONFIG, new Object[]{Constant.LOOKUP_VALUE_1004113003});
        }
        String preDays = sysLookupValues.getLookupMeaning();
        // 结束时间需往后+1天，否则查不到当天数据
        String startTime = DateUtil.convertDateToString(DateUtil.addDay(-Integer.parseInt(preDays)).getTime(), DateUtil.DATE_FORMATE_DAY);
        String endTime = DateUtil.convertDateToString(DateUtil.addDay(NUM_ONE).getTime(), DateUtil.DATE_FORMATE_DAY);
        pickListQueryDTO.setStartDate(startTime);
        pickListQueryDTO.setEndDate(endTime);
        return pickListQueryDTO;
    }

    private boolean checkSuccess(B2bCallBackNewDTO b2bCallBackNewDTO) {
        if (b2bCallBackNewDTO.isSuccess()) {
            WorkOrderWriteCallBackDTO orderWriteCallBackDTO = JSON.parseObject(b2bCallBackNewDTO.getData(), WorkOrderWriteCallBackDTO.class);
            WorkOrderWriteCallBackDataDTO orderWriteCallBackDataDTO = JSON.parseObject(orderWriteCallBackDTO.getData(), WorkOrderWriteCallBackDataDTO.class);
            if (orderWriteCallBackDataDTO.getSuccess()) {
                WorkOrderWriteCallBackResultDTO orderWriteCallBackResultDTO = JSON.parseObject(orderWriteCallBackDataDTO.getResult(), WorkOrderWriteCallBackResultDTO.class);
                return orderWriteCallBackResultDTO.getSuccess();
            }
        }
        return false;
    }
}
