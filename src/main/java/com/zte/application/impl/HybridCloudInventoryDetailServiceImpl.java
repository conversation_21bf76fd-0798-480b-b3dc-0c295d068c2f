package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.zte.application.HrmUserCenterService;
import com.zte.application.HybridcloudInventoryDetailService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.HybridcloudInventoryDetail;
import com.zte.domain.model.HybridcloudInventoryDetailRepository;
import com.zte.gei.processor.handler.exporter.AbstractExportTaskHandler;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 混合云库存明细表服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-11 16:01:46
 */
@Service("hybridCloudInventoryDetailService")
@AllArgsConstructor
public class HybridCloudInventoryDetailServiceImpl extends AbstractExportTaskHandler<HybridCloudInventoryDetailPageQueryDTO, HybridCloudInventoryDetailDTO> implements HybridcloudInventoryDetailService {

    private HybridcloudInventoryDetailRepository hybridcloudInventoryDetailRepository;
    private HrmUserCenterService hrmUserCenterService;
    private Validator globalValidator;

    @Override
    public PageRows<HybridCloudInventoryDetailDTO> queryPage(HybridCloudInventoryDetailPageQueryDTO query) throws Exception {
        Page<HybridCloudInventoryDetailPageQueryDTO> page = new Page<>(query.getPage(), query.getRows());
        page.setParams(query);

        PageRows<HybridCloudInventoryDetailDTO> pageRows = new PageRows<>();
        List<HybridCloudInventoryDetailDTO> rows = hybridcloudInventoryDetailRepository.selectPage(page);
        setEmpName(rows);
        pageRows.setRows(rows);
        pageRows.setCurrent(query.getPage());
        pageRows.setTotal(page.getTotal());
        return pageRows;
    }

    @Override
    public List<HybridCloudInventoryDetailDTO> queryByInventoryDate(HybridCloudInventoryDetailPageQueryDTO query) throws Exception {
        if (null == query.getInventoryDate()){
            throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAM_IS_NULL);
        }
        return hybridcloudInventoryDetailRepository.selectByInventoryDate(query);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importData(HybridCloudInventoryDetailImportDTO detailImportDTO) {
        checkDataList(detailImportDTO.getDetail());
        for (HybridCloudInventoryDetailExcelDTO item : detailImportDTO.getDetail()) {
            if (StringUtils.isNotBlank(item.getErrMsg())) {
                throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, item.getErrMsg());
            }
        }
        List<HybridCloudInventoryDetailExcelDTO> list = detailImportDTO.getDetail().stream().filter(item -> StringUtils.isBlank(item.getErrMsg())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return 0L;
        }
        List<HybridCloudInventoryDetailExcelDTO> list1 = list.stream().filter(item -> StringUtils.isBlank(item.getErrMsg())).collect(Collectors.toList());
        ArrayList<HybridCloudInventoryDetailDTO> hybridCloudInventoryDetailList = new ArrayList<>();
        list1.forEach(item -> {
            HybridCloudInventoryDetailDTO hybridCloudInventoryDetailDTO = new HybridCloudInventoryDetailDTO();
            hybridCloudInventoryDetailDTO.setId(UUID.randomUUID().toString());
            hybridCloudInventoryDetailDTO.setInventoryDate(detailImportDTO.getInventoryDate());
            hybridCloudInventoryDetailDTO.setConfigModel(item.getConfigModel());
            hybridCloudInventoryDetailDTO.setMpn(item.getMpn());
            hybridCloudInventoryDetailDTO.setInventoryQty(item.getInventoryQty());
            hybridCloudInventoryDetailDTO.setDeliveredQuantity(item.getDeliveredQuantity());
            hybridCloudInventoryDetailDTO.setCreateBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
            hybridCloudInventoryDetailDTO.setLastUpdatedBy(MESHttpHelper.getHttpRequestHeader().get(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase()));
            hybridCloudInventoryDetailList.add(hybridCloudInventoryDetailDTO);
        });
        hybridcloudInventoryDetailRepository.deleteByInventoryDate(detailImportDTO.getInventoryDate());
        return hybridcloudInventoryDetailRepository.batchInsert(hybridCloudInventoryDetailList);
    }

    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String fileName = Constant.HybridcloudInventoryExcel.FILE_NAME;
        ImesExcelUtil.setResponseHeaderOfXls(response, fileName);
        EasyExcelFactory.write(response.getOutputStream(), HybridCloudInventoryDetailExcelDTO.class).sheet(Constant.HybridcloudInventoryExcel.SHEETNAME).doWrite(new ArrayList<>());
    }

    @Override
    public List<HybridCloudInventoryDetailExcelDTO> excelAnalysis(MultipartFile file) throws IOException {
        List<HybridCloudInventoryDetailExcelDTO> list = new ArrayList<>();
        EasyExcelFactory.read(file.getInputStream(), HybridCloudInventoryDetailExcelDTO.class, new AnalysisEventListener<HybridCloudInventoryDetailExcelDTO>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                List<String> headers = new ArrayList<>(headMap.values());
                if (!headers.equals(Arrays.asList(Constant.HybridcloudInventoryExcel.TEMPLATE_HEADER_LIST))) {
                    throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.FILE_FORMAT);
                }
            }

            /**
             * When analysis one row trigger invoke function.
             *
             * @param data    one row value. It is same as {@link AnalysisContext#readRowHolder()}
             * @param context analysis context
             */
            @Override
            public void invoke(HybridCloudInventoryDetailExcelDTO data, AnalysisContext context) {
                list.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                if (CollectionUtils.isEmpty(list)) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DATA_IS_EMPTY);
                }
                if (list.size() > NumConstant.NUM_5000) {
                    throw new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.MAX_IMPORT_IS_ONCE);
                }
            }

            @Override
            public void onException(Exception exception, AnalysisContext context) throws Exception {
                if (exception instanceof ExcelDataConvertException) {
                    ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
                    throw new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.ROW_COL_READ_ERROR, new String[]{String.valueOf(excelDataConvertException.getRowIndex()), String.valueOf((excelDataConvertException.getColumnIndex() + 1))});
                } else {
                    throw exception;
                }
            }
        }).sheet().doRead();
        checkDataList(list);
        return list;
    }

    private void checkDataList(List<HybridCloudInventoryDetailExcelDTO> list) {
        HashMap<String, List<HybridCloudInventoryDetailExcelDTO>> listHashMap = new HashMap<>();
        for (HybridCloudInventoryDetailExcelDTO item : list) {
            checkData(item);
            if (StringUtils.isNotBlank(item.getErrMsg())) {
                continue;
            }
            // 料单代码+主工序唯一，key
            String key = item.getConfigModel() + item.getMpn();
            List<HybridCloudInventoryDetailExcelDTO> detailExcels = listHashMap.get(key);
            if (null == detailExcels) {
                detailExcels = new ArrayList<>();
            }
            detailExcels.add(item);
            listHashMap.put(key, detailExcels);
        }
        listHashMap.forEach((key, value) -> {
            if (value.size() > NumConstant.NUM_ONE) {
                value.forEach(item -> item.setErrMsg(MessageId.DATA_TABLE_DUPLICATED));
            }
        });
    }

    private void checkData(HybridCloudInventoryDetailExcelDTO item) {
        Set<ConstraintViolation<HybridCloudInventoryDetailExcelDTO>> validates = globalValidator.validate(item);
        if (!validates.isEmpty()) {
            item.setErrMsg(validates.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("，")));
        }
    }

    @Override
    public Integer countExportTotal(HybridCloudInventoryDetailPageQueryDTO query) {
        return hybridcloudInventoryDetailRepository.selectCount(query);
    }

    @Override
    public List<HybridCloudInventoryDetailDTO> queryExportData(HybridCloudInventoryDetailPageQueryDTO query, int pageNo, int pageSize) {
        Page<HybridCloudInventoryDetailPageQueryDTO> page = new Page<>(pageNo, pageSize);
        page.setParams(query);
        page.setSearchCount(false);
        return hybridcloudInventoryDetailRepository.selectPage(page);
    }

    private void setEmpName(List<HybridCloudInventoryDetailDTO> rows) throws Exception {
        List<String> empNoList = rows.stream().flatMap(dto -> Stream.of(dto.getCreateBy(), dto.getLastUpdatedBy())).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, HrmPersonInfoDTO> hrmPersonInfoMap = hrmUserCenterService.getHrmPersonInfo(empNoList);
        rows.forEach(item -> {
            HrmPersonInfoDTO hrmPersonInfoCreateBy = hrmPersonInfoMap.get(item.getCreateBy());
            item.setCreateBy(hrmPersonInfoCreateBy.getEmpName() + item.getCreateBy());
            HrmPersonInfoDTO hrmPersonInfoUpdateBy = hrmPersonInfoMap.get(item.getLastUpdatedBy());
            item.setLastUpdatedBy(hrmPersonInfoUpdateBy.getEmpName() + item.getLastUpdatedBy());
        });
    }
}
