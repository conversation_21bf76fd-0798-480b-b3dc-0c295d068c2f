package com.zte;

import com.zte.itp.msa.netflixclient.EnableHttpClient;
import com.zte.springbootframe.config.SpringBeanNameGenerator;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.session.SessionAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;

import java.net.InetAddress;

/**
 * 服务包启动入口类
 * 1:启动springboot
 * 2:注册服务
 *
 * <AUTHOR>
 */
@Controller
/**
 * @SpringBootApplication 启用 redis时应这样注解
 * @SpringBootApplication(exclude = { SessionAutoConfiguration.class, DataSourceAutoConfiguration.class})
 * 禁止spring session 启动调用redis，未使用redis时应这样注解
 */
@SpringBootApplication(exclude = {SessionAutoConfiguration.class, DataSourceAutoConfiguration.class}, nameGenerator = SpringBeanNameGenerator.class)
@EnableHttpClient
@MapperScan("com.zte.domain.model")
@EnableAspectJAutoProxy(exposeProxy = true)
public class Application {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);


    public static void main(String[] args) {
        try {
            // 启动springboot
            ConfigurableApplicationContext application = SpringApplication.run(Application.class, args);
            new Application().otherStart();
            Environment env = application.getEnvironment();
            String ip = InetAddress.getLocalHost().getHostAddress();
            String port = env.getProperty("server.port");
            String path = env.getProperty("server.servlet.context-path");
            logger.info("\n----------------------------------------------------------\n\t" +
                    "Application is running! Access URLs:\n\t" +
                    "－Local: \thttp://localhost:{}{}\n\t" +
                    "－Network: \thttp://{}:{}{}\n\t" +
                    "－Swagger: \thttp://{}:{}{}/swagger-ui/index.html\n" +
                    "----------------------------------------------------------", port, path, ip, port, path, ip, port, path);
        } catch (Throwable e) {
            //控制台和logback日志都记录一下。
            e.printStackTrace();
            logger.error("Application Start Failed", e);
            System.exit(-1);
        }

    }

    /**
     * 随框架的启动项
     */
    private void otherStart() throws Exception {
        initEnv();

        // 启动微服务自动注册
        // startMSB();

        // 启动kafka
        // startKafkaConsumer();

        // 启动消息业务处理线程
        // startProcessMessageThread();
    }

    /**
     * 初始化环境
     */
    private void initEnv() {
        //open_config_center:true 启动配置中心 false读取authority.properties
    }

}
