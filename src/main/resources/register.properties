#部分配置已移至配置中心，可通过如下链接查看
#http://devconfig.test.zte.com.cn/zte-itp-isoa/zte-itp-isoa/blob/zte-itp-isoa/v1.0.1/zte-itp-isoa-frame.properties


#***设置注册微服务总线类型,如MSB,Consul等,目前暂定只有MSB,不要更改这个值
register.type=MSB
#***设置向注册服务发现查询的缓存时间,单位s(秒),这样点对点调用时不必每次都向注册中心查询,而是查本地,提升性能
register.cacheTime=1800
#注册服务名
register.serviceName=${spring.application.name}
#注册版本号( 格式：v（小写）+数字)
register.version=v1
#服务上下文contextpath (格式：以/开头，不能以/结尾)
register.url=/${spring.application.name}
#服务对应协议，目前支持REST、UI
register.protocol=REST
#HTTP检测超时时间
register.node.checkTimeOut=22s
#HTTP检测时间间隔
register.node.checkInterval=10s
#http 检测url,由msb consul主动检测,生产环境填写示例:
register.node.checkurl=http://${register.node.ip}:${register.node.port}/${spring.application.name}/info