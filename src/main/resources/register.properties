#é¨åéç½®å·²ç§»è³éç½®ä¸­å¿ï¼å¯éè¿å¦ä¸é¾æ¥æ¥ç
#http://devconfig.test.zte.com.cn/zte-itp-isoa/zte-itp-isoa/blob/zte-itp-isoa/v1.0.1/zte-itp-isoa-frame.properties


#***è®¾ç½®æ³¨åå¾®æå¡æ»çº¿ç±»å,å¦MSB,Consulç­,ç®åæå®åªæMSB,ä¸è¦æ´æ¹è¿ä¸ªå¼
register.type=MSB
#***è®¾ç½®åæ³¨åæå¡åç°æ¥è¯¢çç¼å­æ¶é´,åä½s(ç§),è¿æ ·ç¹å¯¹ç¹è°ç¨æ¶ä¸å¿æ¯æ¬¡é½åæ³¨åä¸­å¿æ¥è¯¢,èæ¯æ¥æ¬å°,æåæ§è½
register.cacheTime=1800
#æ³¨åæå¡å
register.serviceName=${spring.application.name}
#æ³¨åçæ¬å·( æ ¼å¼ï¼vï¼å°åï¼+æ°å­)
register.version=v1
#æå¡ä¸ä¸æcontextpath (æ ¼å¼ï¼ä»¥/å¼å¤´ï¼ä¸è½ä»¥/ç»å°¾)
register.url=/${spring.application.name}
#æå¡å¯¹åºåè®®ï¼ç®åæ¯æRESTãUI
register.protocol=REST
#HTTPæ£æµè¶æ¶æ¶é´
register.node.checkTimeOut=22s
#HTTPæ£æµæ¶é´é´é
register.node.checkInterval=10s
#http æ£æµurl,ç±msb consulä¸»å¨æ£æµ,çäº§ç¯å¢å¡«åç¤ºä¾:
register.node.checkurl=http://${register.node.ip}:${register.node.port}/${spring.application.name}/info