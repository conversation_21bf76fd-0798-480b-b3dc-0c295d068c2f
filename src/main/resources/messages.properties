RetCode.Success=操作成功
RetCode.ServerError=服务器错误
RetCode.AuthFailed=认证失败
RetCode.PermissionDenied=没有权限
RetCode.ValidationError=验证失败
RetCode.BusinessError=业务异常

factory.id.is.null=factory id is null
factory.id.must.greater.than.zero=factory id max greater than zero (curr value is {0})
lfid.is.null=LFID is null
batch.lfid.all.null=batch and LFID is must has one value
user.is.not.exists=user is not exists
password.is.error=password is error

label.generate.rule.is.null=label generate rule is not null
match.multiple.label.generate.rule=match multiple label generate rule
generate.barcode.failure=generate barcode failure
generate.max.reel.id.count=generate reelId max count is 2000

rfid.sign.info.data.is.null=Signature data not transmitted
rfid.sign.info.not.setting=No electronic signature information set
rfid.sign.info.not.salt=Electronic signature encryption salt value is not set
rfid.sign.info.not.cert.id=Electronic signature certificate ID is not set
rfid.sign.info.not.cert.id.get.addr=E-signature certificate ID acquisition address is not set
rfid.sign.info.cert.id.get.fail=Failed to get certificate ID
rfid.sign.info.cert.id.get.error=Exception in obtaining certificate ID
rfid.sign.info.cert.id.oper.success=Certificate ID obtained and updated successfully
board.first.house.service.error= Board first house service error
date.error=
datatype.error=
datetype.error=
cycle.type.error=
result.type.error=
cycle.productclass.and.plangroup.error=
str.format.time.not.null=
no.data.to.export=
deliver.entity.service.error=
bom.server.error=


barcode.call.system=调用系统不能为空
barcode.is.null=条码不能为空
choreographer.url.type.error=编排器接口地址数据字典未配置
choreographer.call.error=编排器接口调用出错

# 客户物料查询相关错误信息
CUSTOMER_NAME_NULL=客户名称不能为空
COOPERATION_MODE_NULL=合作模式不能为空
not.lfid.meter=不是 {0} 的物料
reel.id.registered={0} Reel Id 已注册
update.barcode.fail=调用条码中心更新条码接口失败，{0}
no.location.data=无位号数据，请先进行BOM位号解析
unbinding.setting.exist=绑定异常关系已存在（包含子卡），无需重复设置
timer.synchronize.fail=
you.have.timer.synchronize.fail=
cad.point.lost.error=
get.url.null=未获取数据字典:{0}的URL
erpstock.null=未获取仓库代码:{0}的ERP子库存
level.zero.fix.bom.detail.error=0 层 FixBom 数据缺失请确认!
fix.bom.detail.lost=
zte.code.fixbomid.is.null=ZTE物料代码 {0} 不存在对应fixbomid
get.url.null=未获取数据字典:{0}的URL
erpstock.null=未获取仓库代码:{0}的ERP子库存
level.zero.fix.bom.detail.error=0 层 FixBom 数据缺失请确认!
fix.bom.detail.lost=
zte.code.fixbomid.is.null=ZTE物料代码 {0} 不存在对应fixbomid
get.url.null=未获取数据字典:{0}的URL
erpstock.null=未获取仓库代码:{0}的ERP子库存
level.zero.fix.bom.detail.error=0 层 FixBom 数据缺失请确认!
fix.bom.detail.lost=
zte.code.fixbomid.is.null=ZTE物料代码 {0} 不存在对应fixbomid
required.task.no.or.fix.bom.id=任务号和fixbomid至少输一个
row.col.read.error=第{0}行，第{1}列读取错误，请确认
