server:
  port: 8080
  servlet:
    context-path: /${spring.application.name}
  tomcat:
    uri-encoding: UTF-8
    basedir: /usr/local/tomcat
    max-threads: 2000
  compression:
    enabled: true
    #min-response-size: 2048 #最少2048 bytes
    mime-types: application/javascript,text/html,text/xml,text/javascript,text/css,text/plain,application/json,application/x-javascript,image/gif,image/jpeg,image/png,text/html,text/html,application/xhtml+xml,application/xml

logging:
  config: classpath:logback-spring.xml
#加密
msa:
  expose:
    info:
      enable: true
#指定swagger api访问方式        
springfox:
  documentation:
    enabled: false
    auto-startup: false
    swagger-ui:
      enabled: false
    swagger:
      v2:
        path: /swagger.json
spring:
  main:
    allow-circular-references: true
  application:
    name: zte-mes-manufactureshare-centerfactory
  http:
    encoding:
      force: true
      forceRequest: true
      forceResponse: true
    multipart:
      maxRequestSize: 250Mb
      max-file-size: 250Mb
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  session:
    #spring session开关
    #如未搭建redis服务器,
    #请设置为false关闭此开关，同时在com.zte.Application类中的
    #@SpringBootApplication 注解后面加上 (exclude = {SessionAutoConfiguration.class})。
    enabled: false
  kafka:
    #kafka总开关,为false时关闭。
    enabled: true
    #kafak服务器，该项配置移至 application-dev.properties 或 配置中心。
    bootstrapServers:
      - **********:9092
    #连接到kafka服务器的客户端标识，按微服务名规范，方便于运维日志识别
    clientId: ${spring.application.name}
    producer:
      #同步到follower的数量,all的话就是所有的follower都同步成功了才返回给客户端
      acks: all
      #发送失败后的重试次数
      retries: 3
      #每批次发送的字节大小
      batchSize: 6144
      #划分给生产者使用的内存空间,指定的内存空间塞满后，发送方法会阻塞等待
      bufferMemory: 33554432
      #消息压缩格式
      compressionType: gzip
    consumer:
      #消费组id,按微服务名规范
      groupId: ${spring.application.name}
      #当Kafka中没有初始offset或如果当前的offset不存在时,自动将偏移重置为最新偏移
      autoOffsetReset: latest
      #设置手动提交
      enableAutoCommit: false
      #如果enable.auto.commit设置为true，则消费者偏移量自动提交给Kafka的频率（以毫秒为单位）
      autoCommitInterval: 5000
      #如果没有足够的数据满足fetch.min.bytes，服务器将在接收到提取请求之前阻止的最大时间
      fetchMaxWait: 500
      #kafka服务器拉取请求返回的最小数据量，如果数据不足，请求将等待数据积累.
      fetchMinSize: 1
      #当使用Kafka的分组管理功能时，心跳到消费者协调器之间的预计时间。
      #心跳用于确保消费者的会话保持活动状态，并当有新消费者加入或离开组时方便重新平衡。
      #该值必须必比session.timeout.ms小，通常不高于1/3。
      #它可以调整的更低，以控制正常重新平衡的预期时间。
      heartbeatInterval: 3000
      #在单次调用poll()中返回的最大记录数
      maxPollRecords: 500
    template:
      #默认使用的Topic
      defaultTopic: ${spring.application.type}
  thymeleaf:
    #开发时设为false关闭缓存，便于调试,生产环境为true
    cache: true
  flyway:
    #关闭flyway自动执行，在FlywayRunner中执行
    enabled: false
    #禁用Flyway 的 Clean 命令
    clean-disabled: true
    # 迁移时是否进行校验，默认true
    validate-on-migrate: false
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    #是否允许在同一脚本中混合使用事务性和非事务性语句,开启后不开启事务执行
    mixed: true
info:
  status: ok
management:
  server:
    port: 7001
  endpoints:
    web:
      base-path: /${spring.application.name}/
      exposure:
        include: [ "info", "health" ,"prometheus","metrics" ]
  security:
    enabled: false
  info:
    git:
      enabled: false

# To disable Hystrix in Feign
feign:
  hystrix:
    enabled: true
  config:
    default:
      connect-timeout: 60000
      read-timeout: 60000
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000
#ribbon:
#  ReadTimeout: 8000
#  ConnectTimeout: 6000

mybatis:
  #    config-location: classpath:mybatis-config.xml
  mapper-locations: classpath*:com/zte/infrastructure/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
#DT网管配置
dtEms.agent:
  # 组件开关，非必填，true启用，false禁用，默认false。开启修改配置中心
  enable: false
  # 应用名，必填
  application: iMES
  # 应用模块，必填
  applicationModule: iMES告警模块
  # 服务名，必填
  microService: ${spring.application.name}
  # DT网管服务端地址，必填。此处配置测试环境地址。生产环境请修改配置中心
  serverUrl: 10.54.150.131:9091
  #将所有告警指标下key的数量相加，总和的限制。非必填
  alarmMaxTotalNum: 1000
  #单个告警指标下key数量限制。非必填
  alarmMaxNumPerMetric: 100
  #将所有性能指标下key的数量相加，总和的限制。非必填
  performanceMaxTotalNum: 1000
  #单个性能指标下key数量限制。非必填
  performanceMaxNumPerMetric: 100
  # 指标列表，非必填，支持配置多个指标，指标命名按使用文档中指标定义参考命名，如果上报的指标未配置，则下面的参数都按默认处理
  metrics:
    approval_consumer_error:
      #告警码，非必填，已对接原业务EMS告警系统配置后，兼容原业务EMS告警接口支持告警码上报，不配置可以使用新接口用指标上报
      #code: 10000
      #生效时长，非必填，不建议配置，原则上告警都要有告警清除，为支持特殊场景，配置该配置项后，到时间告警自动清除，时间单位支持m（分钟）和h（小时），不支持混用。
      keepAliveTime: 10m
      #帮助信息，必填，指标说明
      help: 单板报废异常监控
    call_aps_error:
      #生效时长，非必填，不建议配置，原则上告警都要有告警清除，为支持特殊场景，配置该配置项后，到时间告警自动清除，时间单位支持m（分钟）和h（小时），不支持混用。
      keepAliveTime: 10m
      #帮助信息，必填，指标说明
      help: 调用aps接口异常或超时
    call_pdm_error:
      keepAliveTime: 10m
      help: 调用pdm接口异常或超时
    call_barcode_center_error:
      keepAliveTime: 10m
      help: 调用条码中心接口异常或超时
    call_srm_error:
      keepAliveTime: 10m
      help: 调用srm接口异常或超时
    automatic_download_identification_number_error:
      keepAliveTime: 10m
      help: 入网自动申请或下载标识超期告警
    technical_orders_deal_error:
      keepAliveTime: 10m
      help: 技改单同步处理异常告警
    alarm_for_exceeding_one_mb_error:
      keepAliveTime: 10m
      help: 技改单同步处理消息超过1MB告警
    imes_server_resource_alarm:
      keepAliveTime: 10m
      help: iMES微服务资源使用率超阈值
    imes_req_monitor_alarm:
      keepAliveTime: 10m
      help: iMES请求监控告警
    aoi_data_push_error:
      keepAliveTime: 10m
      help: AOI数据推送异常
    insert_push_data_push_error:
      keepAliveTime: 10m
      help: 推送Test/ICT数据新增异常
    refresh_fix_bom_id_error:
      keepAliveTime: 10m
      help: 生成fixBom数据定时任务异常