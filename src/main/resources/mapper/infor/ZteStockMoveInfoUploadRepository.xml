<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.ZteStockMoveInfoUploadRepository">

    <select id="getStockMoveInfo" parameterType="com.zte.interfaces.step.dto.ZteStockInfoDTO" resultType="com.zte.interfaces.infor.dto.ZteStockMoveInfoUploadDTO">
        select t.*,slv.lookup_meaning bdLocCode from
            (select
                dataDate,
                bdPlantCode,
                odmPlantCode,
                odmLocCode,
                materialDocNo,
                materialDocLine,
                sourceOrder,
                odmMaterialCode,
                odmMaterialName,
                materialCode,
                movementType,
                odmMovementType,
                sum(receivingQuantity) receivingQuantity,
                unit,
                operateTime,
                targetOdmPlantCode,
                dataSource
            from (
                select
                    to_char(sysdate,'YYYYMMDD') dataDate,
                    '' bdPlantCode,
                    'ZTE1' odmPlantCode,
                    to_char(ess.whseid) odmLocCode,
                    'SO'|| ess.whseid || ess.orderkey materialDocNo,
                    ess.orderlinenumber materialDocLine,
                    ess.externalorderkey2 sourceOrder,
                    ess.sku odmMaterialCode,
                    sk.descr odmMaterialName,
                    ci.customer_code materialCode,
                    (case when ess.whseid = 'WMWHSE27' and ess.href11 in ('610','620','400','611','621') then 'BD54'
                    when ess.whseid = 'WMWHSE27' and ess.href11 in ('652','651','655','654') and (ess.href05 not like '待处理%' and ess.href05 not like '盈亏%' and ess.href05 not like '盘盈%') then 'BD54'
                    when pb.DB_TYPE = 4 and ess.href11 = '430' and ess.href15 = 'WMWHSE27' then 'BD66'
                    when ess.whseid = 'WMWHSE27' and ess.href11 = '430' and plb.DB_TYPE = 4 then 'BD15'
                    when pb.DB_TYPE = 4 and ess.href11 in ('351','352') then 'BD19'
                    when ess.whseid = 'WMWHSE27' and ess.href11 = '471' then 'BD19'
                    when ess.whseid = 'WMWHSE27' and ess.href11 in ('652','651') and (ess.href05 like '待处理%' or ess.href05 like '盘亏%') then 'BD28'
                    when ess.whseid = 'WMWHSE27' and ess.href11 in ('650','653') then 'BD22'
                    end) movementType,
                    'Issue' odmMovementType,
                    ess.shippedqty receivingQuantity,
                    'EA' unit,
                    ess.edi_adddate operateTime,
                    'ZTE1' targetOdmPlantCode,
                    'InforWMS' dataSource
                from plugin.edi_so_s ess
                left join WMSADMIN.PL_DB pb on ess.whseid = UPPER(pb.DB_LOGID) and pb.ISACTIVE = 1
                left join WMSADMIN.PL_DB plb on ess.href15 = UPPER(plb.DB_LOGID) and plb.ISACTIVE = 1
                left join enterprise.sku sk
                on sk.sku = ess.sku
                join plugin.customer_items ci
                on ci.zte_code = ess.sku
                where ess.invsymbol = '1'
                and ess.shippedqty > 0
                and sk.storerkey = 'ZTE'
                and ci.customer_name = 'ByteDance'
                and trim(ci.customer_code) is not null
                and trim(ci.customer_item_name) is not null
                <if test="itemNoList !=null and itemNoList.size()>0">
                    and ess.sku in
                    <foreach collection="itemNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="uploadBeginDate == null or uploadBeginDate == '' or uploadEndDate == null or uploadEndDate == '' ">
                    and ess.endtime > trunc(sysdate, 'HH') - 1/24 and ess.endtime <![CDATA[<=]]> trunc(sysdate, 'HH')
                </if>
                <if test="uploadBeginDate != null and uploadBeginDate != '' and uploadEndDate != null and uploadEndDate != '' ">
                    and ess.endtime <![CDATA[>=]]> to_date(#{uploadBeginDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
                    and ess.endtime <![CDATA[<=]]> to_date(#{uploadEndDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
                </if>
                union all
                select
                    to_char(sysdate,'YYYYMMDD') dataDate,
                    '' bdPlantCode,
                    'ZTE1' odmPlantCode,
                    to_char(eps.whseid) odmLocCode,
                    'SI' || eps.whseid || eps.receiptkey materialDocNo,
                    eps.receiptlinenumber materialDocLine,
                    eps.externreceiptkey sourceOrder,
                    eps.sku odmMaterialCode,
                    sk.descr odmMaterialName,
                    ci.customer_code materialCode,
                    (case when eps.whseid = 'WMWHSE27' and eps.href11 in ('100','120','140','461') then 'BD51'
                    when eps.whseid = 'WMWHSE27' and eps.href11 in ('210','520') then 'BD55'
                    when eps.whseid = 'WMWHSE27' and eps.href11 in ('330','340','351','352') and (eps.href05 not like '待处理%' and eps.href05 not like '盈亏%' and eps.href05 not like '盘盈%') then 'BD55'
                    when eps.whseid = 'WMWHSE27' and eps.href11 = '430' and plb.DB_TYPE = 4 then 'BD05'
                    when pb.DB_TYPE = 4 and eps.href11 = '430' and eps.href14 = 'WMWHSE27' then 'BD67'
                    when pb.DB_TYPE = 4 and eps.href11 in ('320','340','330','350','351','352') then 'BD67'
                    when eps.whseid = 'WMWHSE27' and eps.href11 in ('330','340','351','352') and eps.href05 like '盘盈%' then 'BD27'
                    when eps.whseid = 'WMWHSE27' and eps.href11 in ('320','350') then 'BD21'
                    end) movementType,
                    'Receipt' odmMovementType,
                    eps.qtystore receivingQuantity,
                    'EA' unit,
                    eps.edi_adddate operateTime,
                    'ZTE1' targetOdmPlantCode,
                    'InforWMS' dataSource
                from plugin.edi_po_s eps
                left join WMSADMIN.PL_DB pb on eps.whseid = UPPER(pb.DB_LOGID) and pb.ISACTIVE = 1
                left join WMSADMIN.PL_DB plb on eps.href14 = UPPER(plb.DB_LOGID) and plb.ISACTIVE = 1
                left join enterprise.sku sk
                on sk.sku = eps.sku
                join plugin.customer_items ci
                on ci.zte_code = eps.sku
                where eps.invsymbol = '1'
                and eps.qtystore > 0
                and sk.storerkey = 'ZTE'
                and ci.customer_name = 'ByteDance'
                and trim(ci.customer_code) is not null
                and trim(ci.customer_item_name) is not null
                <if test="itemNoList !=null and itemNoList.size()>0">
                    and eps.sku in
                    <foreach collection="itemNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="uploadBeginDate == null or uploadBeginDate == '' or uploadEndDate == null or uploadEndDate == '' ">
                    and eps.endtime > trunc(sysdate, 'HH') - 1/24 and eps.endtime <![CDATA[<=]]> trunc(sysdate, 'HH')
                </if>
                <if test="uploadBeginDate != null and uploadBeginDate != '' and uploadEndDate != null and uploadEndDate != '' ">
                    and eps.endtime <![CDATA[>=]]> to_date(#{uploadBeginDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
                    and eps.endtime <![CDATA[<=]]> to_date(#{uploadEndDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
                </if>
            ) S
            group by
            dataDate,
            bdPlantCode,
            odmPlantCode,
            odmLocCode,
            materialDocNo,
            materialDocLine,
            sourceOrder,
            odmMaterialCode,
            odmMaterialName,
            materialCode,
            movementType,
            odmMovementType,
            unit,
            operateTime,
            targetOdmPlantCode,
            dataSource
        ) t,plugin.sys_lookup_values slv
        where slv.lookup_type = '1000047'
        and t.odmLocCode = slv.attribute1
        and slv.enabled_flag = 'Y'
    </select>

    <select id="getPurchaseOrder" parameterType="com.zte.interfaces.step.dto.ZteStockInfoDTO" resultType="com.zte.interfaces.infor.dto.ZtePurchaseOrderDTO">
        select distinct
            null,
            ess.externalorderkey2 materialDocNo,
            to_char(ess.adddate, 'YYYY') materialDocYear,
            to_char(sysdate, 'YYYYMMDD') dataDate,
            to_char(ess.edi_adddate, 'YYYYMMDD') receivingDate,
            ess.externlineno materialDocLine,
            'ZTE1' odmPlantCode,
            null purchaseOrderNo,
            '' poLineNo,
            ci.customer_code materialCode,
            ci.customer_item_name materialDesc,
            null receivingQuantity,
            'EA' unit,
            'N001' odmStorageLoc,
            'BD19' movementType,
            'H' movementCategory,
            ess.sku itemNo,
            'Y' enableFlag,
            ess.ref14 purchaseNetPrice,
            ess.ref13 currencyCode,
            ess.href11
        from plugin.edi_so_s ess
        join plugin.customer_items ci
        on ci.zte_code = ess.sku
        where ess.invsymbol = '1'
        and ess.href11 in ('351', '352')
        and ess.whseid in (select UPPER(pb.DB_LOGID) from WMSADMIN.PL_DB pb where pb.DB_TYPE = 4 and pb.ISACTIVE = 1)
        and ess.shippedqty > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        <if test="billNoList !=null and billNoList.size()>0">
            and ess.externalorderkey2 in
            <foreach collection="billNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="uploadBeginDate == null or uploadBeginDate == '' or uploadEndDate == null or uploadEndDate == '' ">
            and ess.endtime > trunc(sysdate) - 1 + 23.5/24 and ess.endtime <![CDATA[<=]]> trunc(sysdate) + 23.5/24
        </if>
        <if test="uploadBeginDate != null and uploadBeginDate != '' and uploadEndDate != null and uploadEndDate != '' ">
            and ess.endtime <![CDATA[>=]]> to_date(#{uploadBeginDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
            and ess.endtime <![CDATA[<=]]> to_date(#{uploadEndDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        union all
        select distinct
            null,
            ess.externalorderkey2 materialDocNo,
            to_char(ess.adddate, 'YYYY') materialDocYear,
            to_char(sysdate, 'YYYYMMDD') dataDate,
            to_char(ess.edi_adddate, 'YYYYMMDD') receivingDate,
            ess.externlineno materialDocLine,
            'ZTE1' odmPlantCode,
            null purchaseOrderNo,
            '' poLineNo,
            ci.customer_code materialCode,
            ci.customer_item_name materialDesc,
            null receivingQuantity,
            'EA' unit,
            'N001' odmStorageLoc,
            'BD19' movementType,
            'H' movementCategory,
            ess.sku itemNo,
            'Y' enableFlag,
            ess.ref14 purchaseNetPrice,
            ess.ref13 currencyCode,
            ess.href11
        from plugin.edi_so_s ess
        join plugin.customer_items ci
        on ci.zte_code = ess.sku
        where ess.invsymbol = '1'
        and ess.href11 = '471'
        and ess.whseid = 'WMWHSE27'
        and ess.shippedqty > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        <if test="billNoList !=null and billNoList.size()>0">
            and ess.externalorderkey2 in
            <foreach collection="billNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="uploadBeginDate == null or uploadBeginDate == '' or uploadEndDate == null or uploadEndDate == '' ">
            and ess.endtime > trunc(sysdate) - 1 + 23.5/24 and ess.endtime <![CDATA[<=]]> trunc(sysdate) + 23.5/24
        </if>
        <if test="uploadBeginDate != null and uploadBeginDate != '' and uploadEndDate != null and uploadEndDate != '' ">
            and ess.endtime <![CDATA[>=]]> to_date(#{uploadBeginDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
            and ess.endtime <![CDATA[<=]]> to_date(#{uploadEndDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
        union all
        select
            eps.serialkey,
            eps.externreceiptkey materialDocNo,
            to_char(eps.adddate, 'YYYY') materialDocYear,
            to_char(sysdate, 'YYYYMMDD') dataDate,
            to_char(eps.edi_adddate, 'YYYYMMDD') receivingDate,
            eps.externlineno materialDocLine,
            'ZTE1' odmPlantCode,
            eps.ref02 purchaseOrderNo,
            '' poLineNo,
            ci.customer_code materialCode,
            ci.customer_item_name materialDesc,
            eps.qtystore receivingQuantity,
            'EA' unit,
            'N001' odmStorageLoc,
            'BD51' movementType,
            'S' movementCategory,
            eps.sku itemNo,
            'Y' enableFlag,
            eps.ref04 purchaseNetPrice,
            eps.ref08 currencyCode,
            eps.href11
        from plugin.edi_po_s eps
        join plugin.customer_items ci
        on ci.zte_code = eps.sku
        where eps.invsymbol = '1'
        and eps.href11 in ('100', '120', '140', '461')
        and eps.whseid = 'WMWHSE27'
        and eps.qtystore > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        <if test="billNoList !=null and billNoList.size()>0">
            and eps.externreceiptkey in
            <foreach collection="billNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="uploadBeginDate == null or uploadBeginDate == '' or uploadEndDate == null or uploadEndDate == '' ">
            and eps.endtime > trunc(sysdate) - 1 + 23.5/24 and eps.endtime <![CDATA[<=]]> trunc(sysdate) + 23.5/24
        </if>
        <if test="uploadBeginDate != null and uploadBeginDate != '' and uploadEndDate != null and uploadEndDate != '' ">
            and eps.endtime <![CDATA[>=]]> to_date(#{uploadBeginDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
            and eps.endtime <![CDATA[<=]]> to_date(#{uploadEndDate, jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>

    <update id="updateEdiPoSBySerialKey" parameterType="java.util.List">
        update plugin.edi_po_s
        set endtime = sysdate
        where serialkey in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateEdiSoSBySerialKey" parameterType="java.util.List">
        update plugin.edi_so_s
        set endtime = sysdate
        where externalorderkey2 in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getTransferNo" resultType="com.zte.action.iscpedi.model.IscpEdiLog">
        select t.externkey, t.sourcetable
        from plugin.iscp_edi_log t
        where t.issend = -1
        and t.sourcetable in ('EDI_ZMD_PO_S', 'EDI_ZMD_SO_S')
    </select>

    <select id="getEdiPoSList" parameterType="java.util.List" resultType="com.zte.interfaces.infor.dto.InforIsrmTransferDTO">
        select a.externreceiptkey stocktransferno,
        a.externlineno stocktransferrowno,
        'transferIn' transfertype,
        a.sku newitemno,
        a.ref41 originitemno,
        a.lottable02 new22code,
        a.ref42 origin22code,
        sum(a.qtystore) transferqty
        from plugin.edi_po_s a
        join plugin.customer_items ci
        on ci.zte_code = a.sku
        where a.href11 = '461'
        and a.whseid = 'WMWHSE27'
        and a.qtystore > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        and externreceiptkey in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
        group by
        a.externreceiptkey,
        a.externlineno,
        a.sku,
        a.ref41,
        a.lottable02,
        a.ref42
    </select>

    <select id="getEdiSoSList" parameterType="java.util.List" resultType="com.zte.interfaces.infor.dto.InforIsrmTransferDTO">
        select a.externalorderkey2 stocktransferno,
        a.externlineno stocktransferrowno,
        'transferOut' transfertype,
        a.ref41 newitemno,
        a.sku originitemno,
        a.ref42 new22code,
        a.lottable02 origin22code,
        sum(a.shippedqty) transferqty
        from plugin.edi_so_s a
        join plugin.customer_items ci
        on ci.zte_code = a.sku
        where a.href11 = '471'
        and a.whseid = 'WMWHSE27'
        and a.shippedqty > 0
        and ci.customer_name = 'ByteDance'
        and trim(ci.customer_code) is not null
        and trim(ci.customer_item_name) is not null
        and externalorderkey2 in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
        group by
        a.externalorderkey2,
        a.externlineno,
        a.ref41,
        a.sku,
        a.ref42,
        a.lottable02
    </select>

    <update id="updateIscpEdiLog" parameterType="java.util.List">
        update plugin.iscp_edi_log
        set issend = #{issend, jdbcType=INTEGER},
        updateDate = sysdate,
        responseParam = #{responseParam, jdbcType=VARCHAR}
        where externkey in
        <foreach collection="list" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
        and sourcetable = #{sourceTable, jdbcType=VARCHAR}
    </update>

    <select id="getDistributeMaterialInfo" parameterType="com.zte.action.iscpedi.model.IscpEdiLog" resultType="com.zte.interfaces.infor.dto.AliOrderDeductionBillDTO">
        SELECT
        o.externorderkey externOrderkey,
        o.externalorderkey2 externOrderNo,
        s.customer_item_no mpn,
        NULL toId,
        sn.sn,
        1 qtyReceived,
        'mixBox' boxType,
        1 operateType,
        p.pickdetailkey sourcekey
        FROM
        ${whseid}.pickdetail p
        JOIN ${whseid}.orders o
        ON p.orderkey = o.orderkey
        JOIN ${whseid}.itrn it
        ON p.pickdetailkey = it.sourcekey
        AND p.lot = it.lot
        JOIN ${whseid}.sku s
        ON p.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        join plugin.zms_sn_bound_detail sn
        on sn.externalkey = o.externalorderkey2
        and sn.whseid = o.whseid
        and sn.pkg_id = it.fromid
        WHERE
        p.qty > 0
        AND it.trantype = 'MV'
        AND it.sourcetype = 'PICKING'
        AND it.Fromloc <![CDATA[<>]]> it.Toloc
        AND o.externalorderkey2 = #{externkey, jdbcType = VARCHAR}
        AND p.pickdetailkey = #{sourcekey, jdbcType = VARCHAR}
        AND NOT EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = it.fromid
        )
        UNION ALL
        SELECT
        o.externorderkey externOrderkey,
        o.externalorderkey2 externOrderNo,
        s.customer_item_no mpn,
        it.fromid toId,
        NULL sn,
        p.qty qtyReceived,
        'originalBox' boxType,
        1 operateType,
        p.pickdetailkey sourcekey
        FROM
        ${whseid}.pickdetail p
        JOIN ${whseid}.orders o
        ON p.orderkey = o.orderkey
        JOIN ${whseid}.itrn it
        ON p.pickdetailkey = it.sourcekey
        AND p.lot = it.lot
        JOIN ${whseid}.sku s
        ON p.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        WHERE
        p.qty > 0
        AND it.trantype = 'MV'
        AND it.sourcetype = 'PICKING'
        AND it.Fromloc <![CDATA[<>]]> it.Toloc
        AND o.externalorderkey2 = #{externkey, jdbcType = VARCHAR}
        AND p.pickdetailkey = #{sourcekey, jdbcType = VARCHAR}
        AND EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = it.fromid
        )
        UNION ALL
        SELECT
        o.externorderkey externOrderkey,
        o.externalorderkey2 externOrderNo,
        s.customer_item_no mpn,
        NULL toId,
        NULL sn,
        p.qty qtyReceived,
        'nonControl' boxType,
        1 operateType,
        p.pickdetailkey sourcekey
        FROM
        ${whseid}.pickdetail p
        JOIN ${whseid}.orders o
        ON p.orderkey = o.orderkey
        JOIN ${whseid}.sku s
        ON p.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess != 3
        AND s.busr8 = '10'
        WHERE
        p.qty > 0
        AND o.externalorderkey2 = #{externkey, jdbcType = VARCHAR}
        AND p.pickdetailkey = #{sourcekey, jdbcType = VARCHAR}
        UNION ALL
        SELECT
        ep.externalreceiptkey2 externOrderkey,
        ep.externreceiptkey externOrderNo,
        s.customer_item_no mpn,
        NULL toId,
        sn.sn,
        1 qtyReceived,
        'mixBox' boxType,
        2 operateType,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN plugin.edi_po ep
        ON rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        JOIN plugin.zms_sn_bound_detail sn
        on sn.externalkey = ep.externreceiptkey
        and sn.whseid = ep.whseid
        and sn.pkg_id = rd.toid
        WHERE
        rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND NOT EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        SELECT
        ep.externalreceiptkey2 externOrderkey,
        ep.externreceiptkey externOrderNo,
        s.customer_item_no mpn,
        rd.toid toId,
        NULL sn,
        rd.qtyreceived qtyReceived,
        'originalBox' boxType,
        2 operateType,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN plugin.edi_po ep
        ON rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        WHERE
        rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        SELECT
        ep.externalreceiptkey2 externOrderkey,
        ep.externreceiptkey externOrderNo,
        s.customer_item_no mpn,
        NULL toId,
        NULL sn,
        rd.qtyreceived qtyReceived,
        'nonControl' boxType,
        2 operateType,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN plugin.edi_po ep
        ON rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess != 3
        AND s.busr8 = '10'
        WHERE
        rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
    </select>

    <select id="getCreateInfo" parameterType="com.zte.action.iscpedi.model.IscpEdiLog" resultType="com.zte.interfaces.infor.dto.AliCreateMoveBillDTO">
        SELECT
        rd.externreceiptkey externReceiptKey,
        'originalBox' boxType,
        s.customer_item_no mpn,
        rd.toid toId,
        '1' sn,
        rd.qtyreceived qtyReceived,
        'original' fromWarehouseLocation,
        'original' toWarehouseLocation,
        'original' warehouseLocation,
        'BOX' skuType,
        'ZTE101_CO01' sourceSubOrganization,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        WHERE
        rd.qtyreceived > 0
        AND rd.qcqtyrejected > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND exists (
        select 1
        from plugin.edi_po ep
        where rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        AND ep.href11 in ('100', '110', '120', '130', '140')
        AND ep.symbol = 1
        )
        AND EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        SELECT
        rd.externreceiptkey externReceiptKey,
        'mixBox' boxType,
        s.customer_item_no mpn,
        rd.toid toId,
        TO_CHAR(sn.sn) AS sn,
        rd.qtyreceived qtyReceived,
        'mix' fromWarehouseLocation,
        'mix' toWarehouseLocation,
        'mix' warehouseLocation,
        'SN' skuType,
        'ZTE101_CO01' sourceSubOrganization,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        join plugin.zms_sn_bound_detail sn
        on sn.externalkey = rd.externreceiptkey
        AND sn.whseid = rd.whseid
        AND sn.pkg_id = rd.toid
        WHERE
        rd.qtyreceived > 0
        AND rd.qcqtyrejected > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND exists (
        select 1
        from plugin.edi_po ep
        where rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        AND ep.href11 in ('100', '110', '120', '130', '140')
        AND ep.symbol = 1
        )
        AND NOT EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        select
        rd.externreceiptkey externReceiptKey,
        'nonControl' boxType,
        s.customer_item_no mpn,
        rd.toid toId,
        'sn' AS sn,
        rd.qtyreceived qtyReceived,
        'mix' fromWarehouseLocation,
        'mix' toWarehouseLocation,
        'mix' warehouseLocation,
        'SN' skuType,
        'ZTE101_CO01' sourceSubOrganization,
        rd.receiptkey sourcekey
        from ${whseid}.receiptdetail rd
        join ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess != 3
        AND s.busr8 = '10'
        where
        rd.qtyreceived > 0
        AND rd.qcqtyrejected > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND exists (
        select 1
        from plugin.edi_po ep
        where rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        AND ep.href11 in ('100', '110', '120', '130', '140')
        AND ep.symbol = 1
        )
        UNION ALL
        SELECT
        rd.externreceiptkey externReceiptKey,
        'originalBox' boxType,
        s.customer_item_no mpn,
        rd.toid toId,
        'null' sn,
        rd.qtyreceived qtyReceived,
        'original' fromWarehouseLocation,
        'original' toWarehouseLocation,
        'original' warehouseLocation,
        'BOX' skuType,
        CASE WHEN pb.DB_TYPE = 4 THEN 'ZTE101_CO01' ELSE 'ZTE101_NG01' END sourceSubOrganization,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        left join WMSADMIN.PL_DB pb on rd.whseid = UPPER(pb.DB_LOGID) and pb.ISACTIVE = 1
        WHERE
        rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND exists (
        select 1
        from plugin.edi_po ep
        where rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        AND ep.href11 = '430'
        )
        AND EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        SELECT
        rd.externreceiptkey externReceiptKey,
        'mixBox' boxType,
        s.customer_item_no mpn,
        rd.toid toId,
        TO_CHAR(sn.sn) AS sn ,
        rd.qtyreceived qtyReceived,
        'mix' fromWarehouseLocation,
        'mix' toWarehouseLocation,
        'mix' warehouseLocation,
        'SN' skuType,
        CASE WHEN pb.DB_TYPE = 4 THEN 'ZTE101_CO01' ELSE 'ZTE101_NG01' END sourceSubOrganization,
        rd.receiptkey sourcekey
        FROM
        ${whseid}.receiptdetail rd
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        join plugin.zms_sn_bound_detail sn
        on sn.externalkey = rd.externreceiptkey
        and sn.whseid = rd.whseid
        and sn.pkg_id = rd.toid
        left join WMSADMIN.PL_DB pb on rd.whseid = UPPER(pb.DB_LOGID) and pb.ISACTIVE = 1
        WHERE
        rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND exists (
        select 1
        from plugin.edi_po ep
        where rd.externreceiptkey = ep.externreceiptkey
        and rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        and ep.href11 = '430'
        )
        AND NOT EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        select
        rd.externreceiptkey externReceiptKey,
        'nonControl' boxType,
        s.customer_item_no mpn,
        rd.toid toId,
        'sn' AS sn ,
        rd.qtyreceived qtyReceived,
        'mix' fromWarehouseLocation,
        'mix' toWarehouseLocation,
        'mix' warehouseLocation,
        'SN' skuType,
        CASE WHEN pb.DB_TYPE = 4 THEN 'ZTE101_CO01' ELSE 'ZTE101_NG01' END sourceSubOrganization,
        rd.receiptkey sourcekey
        from ${whseid}.receiptdetail rd
        join ${whseid}.sku s
        on rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess != 3
        AND s.busr8 = '10'
        left join WMSADMIN.PL_DB pb on rd.whseid = UPPER(pb.DB_LOGID) and pb.ISACTIVE = 1
        where
        rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND exists (
        select 1
        from plugin.edi_po ep
        where rd.externreceiptkey = ep.externreceiptkey
        and rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        and ep.href11 = '430'
        )
    </select>

    <select id="getSubmitMaterialInfo" parameterType="com.zte.action.iscpedi.model.IscpEdiLog" resultType="com.zte.interfaces.infor.dto.AliOrderSubmitDetailDTO">
        SELECT
            o.externorderkey externOrderkey,
            o.externalorderkey2 srcBillNo,
            o.ref24 pdoNo,
            s.customer_item_no mpn,
            sn.sn AS barcode,
            p.SKU itemCode,
            T.LOTTABLE02 batchBarcode,
            1 qty,
            0 boxType,
            'barcode' stockTypeCode,
            3 stockType,
            null lpnType,
            null boxNo,
            1 operateType,
            p.pickdetailkey sourcekey
        FROM
        ${whseid}.pickdetail p
            JOIN ${whseid}.orders o
            ON p.orderkey = o.orderkey
        JOIN ${whseid}.itrn it
            ON p.pickdetailkey = it.sourcekey
            AND p.lot = it.lot
        JOIN ${whseid}.sku s
            ON p.sku = s.sku
            AND s.storerkey = 'ZTE'
            AND s.customer_control_type = 1
            AND s.serialprocess = 3
            AND s.busr8 = '10'
        JOIN ${whseid}.lotattribute t
            ON t.lot = p.lot and t.storerkey = 'ZTE'
        JOIN plugin.zms_sn_bound_detail sn
            on sn.externalkey = o.externalorderkey2
            and sn.whseid = o.whseid
            and sn.pkg_id = it.fromid
        WHERE
            p.qty > 0
            AND it.trantype = 'MV'
            AND it.sourcetype = 'PICKING'
            AND it.Fromloc <![CDATA[<>]]> it.Toloc
            AND o.externalorderkey2 = #{externkey, jdbcType = VARCHAR}
            AND NOT EXISTS (
                SELECT 1
                FROM plugin.zms_original_box_info zo
                WHERE zo.original_box_id = it.fromid
            )
        UNION ALL
        SELECT
            o.externorderkey externOrderkey,
            o.externalorderkey2 srcBillNo,
            o.ref24 pdoNo,
            s.customer_item_no mpn,
            null AS barcode,
            p.sku itemCode,
            t.lottable02 batchBarcode,
            1 qty,
            1 boxType,
            'item' stockTypeCode,
            2 stockType,
            10 lpnType,
            it.fromid boxNo,
            1 operateType,
            p.pickdetailkey sourcekey
        FROM
        ${whseid}.pickdetail p
        JOIN ${whseid}.orders o
            ON p.orderkey = o.orderkey
        JOIN ${whseid}.itrn it
            ON p.pickdetailkey = it.sourcekey
            AND p.lot = it.lot
        JOIN ${whseid}.lotattribute t
            ON t.lot = p.lot and t.storerkey = 'ZTE'
        JOIN ${whseid}.sku s
            ON p.sku = s.sku
            AND s.storerkey = 'ZTE'
            AND s.customer_control_type = 1
            AND s.serialprocess = 3
            AND s.busr8 = '10'
        WHERE
            p.qty > 0
            AND it.trantype = 'MV'
            AND it.sourcetype = 'PICKING'
            AND it.Fromloc <![CDATA[<>]]> it.Toloc
            AND o.externalorderkey2 = #{externkey, jdbcType = VARCHAR}
            AND EXISTS (
                SELECT 1
                FROM plugin.zms_original_box_info zo
                WHERE zo.original_box_id = it.fromid
            )
    </select>
</mapper>