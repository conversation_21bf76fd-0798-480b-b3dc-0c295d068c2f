<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.ZteSnBoundPkgIdRepository">

    <select id="getSnBoundPkgId" parameterType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO" resultType="com.zte.interfaces.infor.dto.ZteSnBoundPkgIdDTO">
        select h.externalkey billNo, h.pkg_type pkgType, h.pkg_id pkgId, h.qty, d.sn, h.externlineno, h.pickdetailkey
        from plugin.zms_sn_bound_head h
        join plugin.zms_sn_bound_detail d
        on h.externalkey = d.externalkey
        and h.whseid = d.whseid
        and h.pkg_id = d.pkg_id
        where h.externalkey = #{billNo,jdbcType=VARCHAR}
        <if test="fallbackType == '01' ">
            and h.tran_type = 'DP'
        </if>
        <if test="fallbackType == '02' ">
            and h.tran_type = 'WD'
        </if>
        and h.status = 'POSTED'
        and d.status = 'POSTED'
        and h.enabled_flag = 'Y'
        and d.enabled_flag = 'Y'
    </select>

    <select id="getAlibabaExternkey" parameterType="com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO" resultType="com.zte.action.iscpedi.model.IscpEdiLog">
        select t.externkey, t.operateType, t.sourceTable, t.whseid, t.sourcekey
        from plugin.iscp_edi_log t
        where t.issend = -1
        and t.sendtimes <![CDATA[<]]> 4
        and t.sourcetable = #{sourcetable,jdbcType=VARCHAR}
        <if test="externkey != null and externkey != '' ">
            and t.externkey = #{externkey, jdbcType=VARCHAR}
        </if>
        <if test="sourcetable != null and sourcetable != '' ">
            and t.sourcetable = #{sourcetable, jdbcType=VARCHAR}
        </if>
        <if test="operatetype != null and operatetype != '' ">
            and t.operatetype = #{operatetype, jdbcType=VARCHAR}
        </if>
        <if test="sourcekey != null and sourcekey != '' ">
            and t.sourcekey = #{sourcekey, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryZtePkgIdBoundSnForExternalkey" resultType="java.lang.String">
        SELECT DISTINCT ZSBH.EXTERNALKEY
        FROM PLUGIN.ZMS_SN_BOUND_HEAD ZSBH
        JOIN PLUGIN.ZMS_SN_BOUND_DETAIL ZSBD
        ON ZSBH.EXTERNALKEY = ZSBD.EXTERNALKEY
        AND ZSBH.WHSEID = ZSBD.WHSEID
        AND ZSBH.PKG_ID = ZSBD.PKG_ID
        JOIN PLUGIN.ISCP_EDI_LOG IEL
        ON IEL.EXTERNKEY = ZSBH.EXTERNALKEY
        AND IEL.WHSEID = ZSBH.WHSEID
        AND IEL.SOURCEKEY = ZSBH.PICKDETAILKEY
        AND IEL.SOURCETABLE = 'ALIBABA_PUBLIC_CLOUD'
        AND IEL.OPERATETYPE = '12'
        AND IEL.EXTERNLINENO = '02'
        AND IEL.ISSEND = 0
        WHERE ZSBH.PKG_TYPE = #{pkgType, jdbcType=VARCHAR}
        AND ZSBH.STATUS IN ('BOUNDED', 'POSTED')
        AND ZSBH.CUSTOMER_ITEM_NO IS NOT NULL
        AND ZSBH.ENABLED_FLAG = 'Y'
        AND ZSBD.ENABLED_FLAG = 'Y'
        AND ZSBD.SEND_TIMES <![CDATA[<]]> 4
        AND ZSBD.SEND_STATUS = '-1'
        AND ZSBD.SN IS NOT NULL
        <if test="pkgType='1'">
            AND (ZSBH.HREF11 IN ('610', '611') OR
            (ZSBH.HREF11 IN ('650', '651', '652', '653', '654', '655') AND
            EXISTS (SELECT 1
            FROM PLUGIN.EDI_SO ES
            WHERE ES.EXTERNALORDERKEY2 = ZSBH.EXTERNALKEY
            AND ES.HREF14 = 'WX')))
        </if>
    </select>

    <select id="queryZtePkgIdBoundSnInfoList" resultType="com.zte.interfaces.infor.dto.ZtePkgIdBoundSnInfoDTO">
        SELECT DISTINCT ZSBH.EXTERNALKEY || ZSBH.PICKDETAILKEY, ZSBH.CUSTOMER_ITEM_NO customerItemNo, ZSBD.SN, 2 storageArea,
            ZSBD.MESSAGE_ID MESSAGEID,ZSBD.SERIALKEY
        FROM PLUGIN.ZMS_SN_BOUND_HEAD ZSBH
        JOIN PLUGIN.ZMS_SN_BOUND_DETAIL ZSBD
        ON ZSBH.EXTERNALKEY = ZSBD.EXTERNALKEY
        AND ZSBH.WHSEID = ZSBD.WHSEID
        AND ZSBH.PKG_ID = ZSBD.PKG_ID
        JOIN PLUGIN.ISCP_EDI_LOG IEL
        ON IEL.EXTERNKEY = ZSBH.EXTERNALKEY
        AND IEL.WHSEID = ZSBH.WHSEID
        AND IEL.SOURCEKEY = ZSBH.PICKDETAILKEY
        AND IEL.SOURCETABLE = 'ALIBABA_PUBLIC_CLOUD'
        AND IEL.OPERATETYPE = '12'
        AND IEL.EXTERNLINENO = '02'
        AND IEL.ISSEND = 0
        WHERE ZSBH.PKG_TYPE = #{pkgType, jdbcType=VARCHAR}
        AND ZSBH.STATUS IN ('BOUNDED', 'POSTED')
        AND ZSBH.CUSTOMER_ITEM_NO IS NOT NULL
        AND ZSBH.ENABLED_FLAG = 'Y'
        AND ZSBD.ENABLED_FLAG = 'Y'
        AND ZSBD.SEND_TIMES <![CDATA[<]]> 4
        AND ZSBD.SEND_STATUS = '-1'
        AND ZSBD.SN IS NOT NULL
        AND ZSBH.EXTERNALKEY = #{orderNo, jdbcType=VARCHAR}
        <if test="pkgType='1'">
            AND (ZSBH.HREF11 IN ('610', '611') OR
            (ZSBH.HREF11 IN ('650', '651', '652', '653', '654', '655') AND
            EXISTS (SELECT 1
            FROM PLUGIN.EDI_SO ES
            WHERE ES.EXTERNALORDERKEY2 = ZSBH.EXTERNALKEY
            AND ES.HREF14 = 'WX')))
        </if>
    </select>
    <update id="updateZtePkgIdBoundSnInfo" parameterType="com.zte.interfaces.infor.dto.UpdateZtePkgIdBoundSnInfoDTO">
        UPDATE PLUGIN.ZMS_SN_BOUND_DETAIL ZSBD
            SET ZSBD.SEND_STATUS = '0',
                ZSBD.SEND_TIMES = ZSBD.SEND_TIMES + 1,
                MESSAGE_TYPE = #{messageType, jdbcType=VARCHAR},
                MESSAGE_ID=#{messageId, jdbcType=VARCHAR},
                LAST_UPDATE_DATE = sysdate,
                LAST_UPDATED_BY=#{empNo, jdbcType=VARCHAR}
            WHERE ZSBD.EXTERNALKEY =#{externalkey,jdbcType=VARCHAR}
        <if test="serialkeys !=null and serialkeys.size()>0">
            AND ZSBD.SERIALKEY IN
            <foreach collection="serialkeys" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="snCodes !=null and snCodes.size()>0">
            AND ZSBD.SN IN
            <foreach collection="snCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </update>
    <update id="updateZtePkgIdBoundSnInfoByResult" parameterType="com.zte.interfaces.infor.dto.UpdateZtePkgIdBoundSnInfoDTO">
        UPDATE PLUGIN.ZMS_SN_BOUND_DETAIL ZSBD
        SET ZSBD.SEND_STATUS = #{sendStatus, jdbcType=VARCHAR},
            LAST_UPDATE_DATE = sysdate
        WHERE ZSBD.MESSAGE_ID =#{messageId, jdbcType=VARCHAR}
    </update>
    <select id="getAllInventory"  parameterType="java.util.List" resultType="com.zte.interfaces.step.dto.ZteSnInventoryQueryDTO">
        <foreach collection="inventoryWarehouseList" item="inventoryDb" index="index" separator="union all">
        <if test ="inventoryDb.dbType != null and inventoryDb.dbType == 4">
            SELECT 5 AS inventoryType,
            s.customer_item_no AS MPN,
            0 AS locatorType,
            TO_CHAR(la.lottable05,'YYYYMMDD') AS inventoryBatch,
            lx.id AS cartonId,
            lx.qty AS vendorCartonQuantity,
            null as sn,
            0 AS itemType
            FROM ${inventoryDb.warehouseId}.lotxlocxid lx
            JOIN ${inventoryDb.warehouseId}.sku s ON lx.sku = s.sku
            JOIN ${inventoryDb.warehouseId}.lotattribute la ON lx.lot = la.lot
            WHERE lx.qty > 0
            AND s.storerkey = 'ZTE'
            AND s.customer_control_type = 1
            AND s.serialprocess = 3
            AND s.busr8 = '10'
            AND EXISTS (
            SELECT 1
            FROM plugin.zms_original_box_info zo
            WHERE zo.original_box_id = lx.id
            )
            UNION ALL
            SELECT 5 AS inventoryType,
            s.customer_item_no AS MPN,
            1 AS locatorType,
            TO_CHAR(la.lottable05,'YYYYMMDD') AS inventoryBatch,
            null AS cartonId,
            null AS vendorCartonQuantity,
            sn.sn,
            1 AS itemType
            FROM ${inventoryDb.warehouseId}.lotxlocxid lx
            JOIN ${inventoryDb.warehouseId}.sku s ON lx.sku = s.sku
            JOIN ${inventoryDb.warehouseId}.lotattribute la ON lx.lot = la.lot
            JOIN ${inventoryDb.warehouseId}.sn sn ON lx.id = sn.id
            WHERE lx.qty > 0
            AND s.storerkey = 'ZTE'
            AND s.customer_control_type = 1
            AND s.serialprocess = 3
            AND s.busr8 = '10'
            AND NOT EXISTS (
            SELECT 1
            FROM plugin.zms_original_box_info zo
            WHERE zo.original_box_id = lx.id
            )
        </if>
        <if test ="inventoryDb.dbType != null and inventoryDb.dbType != 4">
            SELECT CASE
                     WHEN EXISTS (SELECT 1
                             FROM ${inventoryDb.warehouseId}.lotxlocxid lx2
                             JOIN ${inventoryDb.warehouseId}.lotattribute lt
                               ON lt.lot = lx2.lot
                              AND lt.sku = lx2.sku
                              AND lt.storerkey = lx2.storerkey
                             JOIN ${inventoryDb.warehouseId}.inventoryhold ii
                               ON ii.id = lx2.id
                            WHERE ii.status = 'QCFAILED'
                              AND lx2.storerkey = 'ZTE'
                              AND lx2.id = lx.id) THEN
                      5
                     ELSE
                      0
                   END AS inventoryType,
                   s.customer_item_no AS MPN,
                   0 AS locatorType,
                   TO_CHAR(la.lottable05, 'YYYYMMDD') AS inventoryBatch,
                   lx.id AS cartonId,
                   lx.qty AS vendorCartonQuantity,
                   null as sn,
                   0 AS itemType
              FROM ${inventoryDb.warehouseId}.lotxlocxid lx
              JOIN ${inventoryDb.warehouseId}.sku s
                ON lx.sku = s.sku
              JOIN ${inventoryDb.warehouseId}.lotattribute la
                ON lx.lot = la.lot
              JOIN ${inventoryDb.warehouseId}.loc T
                ON T.LOC = lx.LOC
               AND T.locationtype != 'PENDINGQC'
             WHERE lx.qty > 0
               AND s.storerkey = 'ZTE'
               AND s.customer_control_type = 1
               AND s.serialprocess = 3
               AND s.busr8 = '10'
               AND EXISTS (SELECT 1
                      FROM plugin.zms_original_box_info zo
                     WHERE zo.original_box_id = lx.id)

            UNION ALL

            SELECT CASE
                     WHEN EXISTS (SELECT 1
                             FROM ${inventoryDb.warehouseId}.lotxlocxid lx2
                             JOIN ${inventoryDb.warehouseId}.lotattribute lt
                               ON lt.lot = lx2.lot
                              AND lt.sku = lx2.sku
                              AND lt.storerkey = lx2.storerkey
                             JOIN ${inventoryDb.warehouseId}.inventoryhold ii
                               ON ii.id = lx2.id
                            WHERE ii.status = 'QCFAILED'
                              AND lx2.storerkey = 'ZTE'
                              AND lx2.id = lx.id) THEN
                      5
                     ELSE
                      0
                   END AS inventoryType,
                   s.customer_item_no AS MPN,
                   1 AS locatorType,
                   TO_CHAR(la.lottable05, 'YYYYMMDD') AS inventoryBatch,
                   null AS cartonId,
                   null AS vendorCartonQuantity,
                   sn.sn,
                   1 AS itemType
              FROM ${inventoryDb.warehouseId}.lotxlocxid lx
              JOIN ${inventoryDb.warehouseId}.sku s
                ON lx.sku = s.sku
              JOIN ${inventoryDb.warehouseId}.sn sn
                ON lx.id = sn.id
              JOIN ${inventoryDb.warehouseId}.lotattribute la
                ON lx.lot = la.lot
              JOIN ${inventoryDb.warehouseId}.loc T
                ON T.LOC = lx.LOC
               AND T.locationtype != 'PENDINGQC'
             WHERE lx.qty > 0
               AND s.storerkey = 'ZTE'
               AND s.customer_control_type = 1
               AND s.serialprocess = 3
               AND s.busr8 = '10'
               AND NOT EXISTS (SELECT 1
                      FROM plugin.zms_original_box_info zo
                     WHERE zo.original_box_id = lx.id)
        </if>
        </foreach>
    </select>


    <!-- 查询指定外部单号的绑定关系详情 -->
    <select id="queryPkgSnBindingsByExternalNo" resultType="com.zte.interfaces.infor.dto.ZtePkgIdBoundSnInfoDTO">
        SELECT distinct
            zh.externalkey AS externalNo,
            zh.pkg_id AS pkgId,
            s.customer_item_no AS mpn,
            zd.sn AS snCode
        FROM plugin.zms_sn_bound_head zh
        JOIN plugin.zms_sn_bound_detail zd ON zh.externalkey = zd.externalkey AND zh.pkg_id = zd.pkg_id
        JOIN enterprise.sku s ON zh.sku = s.sku
        WHERE zh.pkg_type = '0'
           and s.storerkey = 'ZTE'
           and s.customer_control_type = 1
           and s.serialprocess = 3
           and s.busr8 = '10'
        AND zh.status IN ('BOUNDED', 'POSTED')
        AND zd.SN IS NOT NULL
        AND zd.CUSTOMER_ITEM_NO IS NOT NULL
        AND zd.send_status = '-1'
        AND zd.send_times <![CDATA[<]]> 4
    </select>

    <select id="getWhseIdByBillNo" parameterType="java.lang.String" resultType="java.lang.String">
        select t.whseid
        from plugin.edi_so t
        where t.externalorderkey2 = #{billNo, jdbcType=VARCHAR}
        and rownum = 1
    </select>

    <select id="getOutQtyByBillNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nvl(sum(pd.qty),-1)
        from ${whseId}.pickdetail pd
        join ${whseId}.orders o
        on pd.orderkey = o.orderkey
        join ${whseId}.sku s
        on s.sku = pd.sku
        and s.storerkey = 'ZTE'
        and s.customer_control_type = 1
        and s.serialprocess = 3
        and s.busr8 = '10'
        where o.externalorderkey2 = #{billNo, jdbcType=VARCHAR}
        <if test="pickdetailkey != null and pickdetailkey != '' ">
            and pd.pickdetailkey = #{pickdetailkey, jdbcType=VARCHAR}
        </if>
        and pd.status = 9
    </select>
</mapper>