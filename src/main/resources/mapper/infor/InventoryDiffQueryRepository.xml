<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InventoryDiffQueryRepository">

    <select id="getInventoryDiffData" parameterType="com.zte.interfaces.infor.dto.InventoryDiffDTO" resultType="com.zte.interfaces.infor.dto.InventoryDiffDTO">
        select t.* from (
        select s.*, rownum rn from (
        select serialkey, mpn, zte_stock_qty zteStockQty, zte_instock_qty zteInstockQty, zte_online_qty zteOnlineQty,
        alibaba_stock_qty alibabaStockQty, diff_stock_qty diffStockQty, snap_date snapDate, creation_date creationDate,
        available_quantity availableQuantity, freeze_quantity freezeQuantity
        from plugin.ZMS_INVENTORY_DIFF zsbh
        where enabled_flag = 'Y'
        and INVENTORY_TYPE = #{inventoryType}
        <if test="mpn != null and mpn !='' ">
            and mpn  = #{mpn}
        </if>
        <if test="snapDate != null and snapDate !=''  ">
            and snap_date  = to_date(#{snapDate,jdbcType=VARCHAR},'yyyy/mm/dd')
        </if>
        <if test="isDisplayDifferent != null and isDisplayDifferent == true ">
            and diff_stock_qty   <![CDATA[>]]> 0
        </if>
        order by serialkey
        ) s ) t where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  t.rn between #{startRow} and #{endRow}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  t.rn &lt;= 500000
        </if>
    </select>

    <select id="getInventoryDiffDataCount" parameterType="com.zte.interfaces.infor.dto.InventoryDiffDTO" resultType="java.lang.Integer">
        select count(1)
        from plugin.ZMS_INVENTORY_DIFF
        where enabled_flag = 'Y'
        and INVENTORY_TYPE = #{inventoryType}
        <if test="mpn != null and mpn !='' ">
            and mpn  = #{mpn}
        </if>
        <if test="snapDate != null and snapDate !=''  ">
            and snap_date  = to_date(#{snapDate,jdbcType=VARCHAR},'yyyy/mm/dd')
        </if>
        <if test="isDisplayDifferent != null and isDisplayDifferent == true ">
            and diff_stock_qty   <![CDATA[>]]> 0
        </if>
    </select>


    <select id="getInventoryDiffTempData" parameterType="com.zte.interfaces.infor.dto.InventoryDiffDTO" resultType="com.zte.interfaces.infor.dto.InventoryDiffDTO">
        select t.* from (
        select s.*, rownum rn from (
        select serialkey, mpn, zte_stock_qty zteStockQty, zte_instock_qty zteInstockQty, zte_online_qty zteOnlineQty,
        alibaba_stock_qty alibabaStockQty, diff_stock_qty diffStockQty, snap_date snapDate, creation_date creationDate,
        available_quantity availableQuantity, freeze_quantity freezeQuantity
        from plugin.ZMS_INVENTORY_DIFF_TEMP zsbh
        where enabled_flag = 'Y'
        and INVENTORY_TYPE = #{inventoryType}
        <if test="mpn != null and mpn !='' ">
            and mpn  = #{mpn}
        </if>
        <if test="isDisplayDifferent != null and isDisplayDifferent !='' ">
            and diff_stock_qty   <![CDATA[>]]> 0
        </if>
        order by serialkey
        ) s ) t where 1=1
        <if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
            and  t.rn between #{startRow} and #{endRow}
        </if>
        <if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
            and  t.rn &lt;= 500000
        </if>
    </select>

    <select id="getInventoryDiffDataTempCount" parameterType="com.zte.interfaces.infor.dto.InventoryDiffDTO" resultType="java.lang.Integer">
        select count(1)
        from plugin.ZMS_INVENTORY_DIFF_TEMP
        where enabled_flag = 'Y'
        and INVENTORY_TYPE = #{inventoryType}
        <if test="mpn != null and mpn !='' ">
            and mpn  = #{mpn}
        </if>
        <if test="isDisplayDifferent != null and isDisplayDifferent !='' ">
            and diff_stock_qty   <![CDATA[>]]> 0
        </if>
    </select>

    <delete id="deleteInventoryAlibabaTemp">
        TRUNCATE TABLE plugin.ZMS_INVENTORY_ALIBABA_TEMP
    </delete>
    <delete id="deleteInventoryTemp">
        TRUNCATE TABLE plugin.ZMS_INVENTORY_TEMP
    </delete>
    <delete id="deleteInventoryDiffTemp">
        TRUNCATE TABLE plugin.ZMS_INVENTORY_DIFF_TEMP
    </delete>
    <select id="getInforWarehouseList" resultType="com.zte.interfaces.step.dto.ZteWarehouseInfoDTO">
        SELECT UPPER(PB.DB_LOGID) as warehouseId,
        PB.DB_TYPE as dbType
        FROM WMSADMIN.PL_DB PB
        WHERE PB.ISACTIVE = 1
        AND PB.DB_ENTERPRISE = 0
        AND PB.DB_TYPE in (1,2)
    </select>

    <select id="getALiStockInfoList" parameterType="java.util.List"
            resultType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        <foreach collection="inventoryList" item="item" index="index" separator="union all">
        SELECT sk.whseid,
        sk.sku,
        sk.customer_item_no as mpn,
        lx.qty as vendorInventoryQuantity,
        TO_CHAR(lt.lottable11, 'YYYYMMDD') as inventoryBatch,
        sk.customer_control_type as customerControlType,
        '0' as inventoryType
        FROM ${item.warehouseId}.SKU sk
        JOIN ${item.warehouseId}.lotxlocxid lx
        on sk.sku = lx.sku
        and sk.storerkey = lx.storerkey
        JOIN ${item.warehouseId}.lotattribute lt
        on lt.lot = lx.lot
        and lt.sku = lx.sku
        and lt.storerkey = lx.storerkey
        WHERE sk.customer_control_type = 1
        and sk.storerkey = 'ZTE'
        and lx.id != ' '
        and not exists (
        SELECT 1
        FROM ${item.warehouseId}.inventoryhold ii
        WHERE ii.id = lx.id
        and ii.status = 'QCFAILED'
        )
        </foreach>
    </select>

    <insert id="addInventoryTempData" parameterType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO plugin.ZMS_INVENTORY_TEMP
            (SERIALKEY, SOURCE_SYSTEM, WHSEID, SKU, MPN,
            INVENTORY_BATCH, VENDOR_INVENTORY_QUANTITY, INVENTORY_TYPE, CUSTOMER_CONTROL_TYPE, ITEM_TYPE, CONFIG_MODEL,
            CREATED_BY, LAST_UPDATED_BY)
            VALUES
            (plugin.ZMS_INVENTORY_TEMP_S.NEXTVAL,
            #{item.sourceSystem},
            #{item.whseid},
            #{item.sku},
            #{item.mpn},
            #{item.inventoryBatch},
            #{item.vendorInventoryQuantity},
            #{item.inventoryType},
            #{item.customerControlType},
            #{item.itemType},
            #{item.configModel},
            #{item.createdBy},
            #{item.lastUpdatedBy}
            )
        </foreach>
    </insert>
    <insert id="insertAliInventoryTempData" parameterType="com.zte.interfaces.step.dto.B2BCallBackInventoryResultDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO PLUGIN.ZMS_INVENTORY_ALIBABA_TEMP
            (SERIALKEY,
            MESSAGE_ID,
            MESSAGE_TYPE,
            INVENTORY_CATEGORY_NAME,
            BRAND_NAME,
            CATEGORY_NAME,
            ITEM_NAME,
            MPN,
            BATCH,
            INVENTORY_QUANTITY,
            AVAILABLE_QUANTITY,
            FREEZE_QUANTITY,
            HOLD_DURATION,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            ENABLED_FLAG)
            VALUES
            (plugin.ZMS_INVENTORY_ALIBABA_TEMP_S.NEXTVAL,
            #{item.messageId},
            #{item.messageType},
            #{item.inventoryCategoryName},
            #{item.brandName},
            #{item.categoryName},
            #{item.itemName},
            #{item.mpn},
            #{item.batch},
            #{item.inventoryQuantity},
            #{item.availableQuantity},
            #{item.freezeQuantity},
            #{item.holdDuration},
            'system',
            sysdate,
            'system',
            sysdate,
            'Y'
            )
        </foreach>
    </insert>
    <select id="getInventoryTempData" resultType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        SELECT SOURCE_SYSTEM as sourceSystem,
        MPN,
        VENDOR_INVENTORY_QUANTITY AS vendorInventoryQuantity,
        INVENTORY_TYPE AS inventoryType
        FROM PLUGIN.ZMS_INVENTORY_TEMP
        WHERE ENABLED_FLAG = 'Y'
        AND CUSTOMER_CONTROL_TYPE = 1
        AND INVENTORY_TYPE in ('0','1')
    </select>
    <insert id="insertInventoryDiffTempData" parameterType="com.zte.interfaces.infor.dto.InventoryDiffTempDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO PLUGIN.ZMS_INVENTORY_DIFF_TEMP
            (SERIALKEY,
            INVENTORY_TYPE,
            MPN,
            ZTE_STOCK_QTY,
            ZTE_INSTOCK_QTY,
            ZTE_ONLINE_QTY,
            ALIBABA_STOCK_QTY,
            DIFF_STOCK_QTY,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            ENABLED_FLAG,
            AVAILABLE_QUANTITY,
            FREEZE_QUANTITY)
            VALUES
            (plugin.ZMS_INVENTORY_DIFF_TEMP_S.NEXTVAL,
            #{item.inventoryType},
            #{item.mpn},
            #{item.zteStockQty},
            #{item.zteInstockQty},
            #{item.zteOnlineQty},
            #{item.alibabaStockQty},
            #{item.diffStockQty},
            'system',
            sysdate,
            'system',
            sysdate,
            'Y',
            #{item.availableQuantity},
            #{item.freezeQuantity}
            )
        </foreach>
    </insert>

    <select id="getUnBindSnRelationData" resultType="com.zte.interfaces.infor.vo.UnBindPkgSnJobVo">
        select t.whseid AS "Whseid",
        t.externalkey as "Externalkey",
        t.sourcekey as "Sourcekey",
        CASE
        WHEN t.tran_type = 'DP' THEN '入库'
        WHEN t.tran_type = 'WD' THEN '出库'
        ELSE ''
        END as "TranType"
        FROM plugin.zms_sn_bound_head t
        WHERE 1 = 1
        AND t.status = 'BOUNDING'
        AND t.enabled_flag = 'Y'
        AND t.oper_date <![CDATA[<]]> sysdate - nvl(
        (
        SELECT slv.lookup_meaning
        FROM plugin.sys_lookup_values slv
        WHERE slv.lookup_code = '100007600005'
        AND slv.enabled_flag = 'Y'
        AND rownum = 1
        ),
        2
        ) / 24
        GROUP BY t.whseid,t.externalkey,t.sourcekey,t.tran_type
        order by t.whseid, t.tran_type,t.externalkey,t.sourcekey
    </select>



    <select id="getOriginAndMixedBoxInventory" parameterType="java.util.List" resultType="com.zte.interfaces.infor.dto.OriginMixedBoxInventoryDTO">
        <foreach collection="inventoryWarehouseList" item="inventoryDb" index="index" separator="union all">
            SELECT
            m.whseid,
            m.sku,
            m.id,
            m.pkgtype,
            m.loc,
            m.putawayzone,
            m.zonetype
            FROM
            (
            SELECT
            l.whseid,
            l.sku,
            l.id,
            (
            CASE
            WHEN NVL(zo.serialkey, 0)> 0 THEN '原箱'
            ELSE '混箱'
            END) pkgType,
            l.loc,
            c.putawayzone,
            (
            CASE
            WHEN paz.PUTAWAYZONE_TYPE = 1 THEN '原箱'
            WHEN paz.PUTAWAYZONE_TYPE = 2 THEN '混箱'
            ELSE '一般库区'
            END) zonetype
            FROM
            ${inventoryDb.warehouseId}.lotxlocxid l
            JOIN ${inventoryDb.warehouseId}.sku s ON
            l.sku = s.sku
            LEFT JOIN plugin.zms_original_box_info zo ON
            zo.original_box_id = l.id
            LEFT JOIN ${inventoryDb.warehouseId}.loc c ON
            c.loc = l.loc
            LEFT JOIN ${inventoryDb.warehouseId}.PUTAWAYZONE paz ON
            c.PUTAWAYZONE = paz.PUTAWAYZONE
            WHERE
            l.qty > 0
            AND s.storerkey = 'ZTE'
            AND s.customer_control_type = 1
            AND s.serialprocess = 3
            AND s.busr8 = '10') m
            WHERE
            m.pkgtype != m.zonetype
        </foreach>
    </select>
    <select id="getMixedBoxAndSnInventory" parameterType="java.util.List" resultType="com.zte.interfaces.infor.dto.MixedBoxInventoryDTO">
        <foreach collection="inventoryWarehouseList" item="inventoryDb" index="index" separator="union all">
            SELECT
            m.whseid,
            m.sku,
            m.id,
            m.loc,
            m.qty,
            m.snqty
            FROM
            (
            SELECT
            l.whseid,
            l.sku,
            l.id,
            l.loc,
            l.qty,
            count(n.sn) snQty
            FROM
            ${inventoryDb.warehouseId}.lotxlocxid l
            JOIN ${inventoryDb.warehouseId}.sku s ON
            l.sku = s.sku
            LEFT JOIN ${inventoryDb.warehouseId}.sn n ON
            n.id = l.id
            WHERE
            l.qty > 0
            AND s.storerkey = 'ZTE'
            AND s.customer_control_type = 1
            AND s.serialprocess = 3
            AND s.busr8 = '10'
            AND NOT EXISTS (
            SELECT
            *
            FROM
            plugin.zms_original_box_info zo
            WHERE
            zo.original_box_id = l.id )
            GROUP BY
            l.whseid,
            l.sku,
            l.id,
            l.loc,
            l.qty ) m
            WHERE
            m.qty != m.snqty
        </foreach>
    </select>
    <select id="getZteAndAliInventoryDiff" resultType="com.zte.interfaces.infor.dto.ZteAliInventoryDTO">
        SELECT
        t.mpn,
        t.zte_instock_qty,
        t.zte_online_qty,
        t.alibaba_stock_qty,
        t.diff_stock_qty
        FROM
        plugin.ZMS_INVENTORY_DIFF_TEMP t
        WHERE
        t.inventory_type = 0
        AND t.enabled_flag = 'Y'
        AND t.diff_stock_qty > 0
    </select>
    <insert id="saveProductInventoryStaticsData" parameterType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO plugin.ZMS_INVENTORY_STATISTICS
            (SERIALKEY, SOURCE_SYSTEM, WHSEID, SKU, MPN,
            INVENTORY_BATCH, VENDOR_INVENTORY_QUANTITY, INVENTORY_TYPE, CUSTOMER_CONTROL_TYPE,MERGED_FLAG,
            CREATED_BY, LAST_UPDATED_BY)
            VALUES
            (plugin.ZMS_INVENTORY_STATISTICS_S.NEXTVAL,
            #{item.sourceSystem},
            #{item.whseid},
            #{item.sku},
            #{item.mpn},
            #{item.inventoryBatch},
            #{item.vendorInventoryQuantity},
            #{item.inventoryType},
            #{item.customerControlType},'Y',
            #{item.createdBy},
            #{item.lastUpdatedBy}
            )
        </foreach>
    </insert>

    <!-- 根据仓库、物料代码、状态查询库存数量并返回仓库id -->
    <select id="getStockQtyWithWisId" parameterType="com.zte.interfaces.infor.dto.ECLotxlocxidDTO" resultType="com.zte.interfaces.infor.dto.ECLotxlocxid">
        <foreach collection="wisIdList" item="wisId" open="" separator="union all" close="">
            select sku, sum(qty) as qty, #{wisId} as wis_id from ${wisId}.lotxlocxid
            where sku in (
            <foreach collection="skuList" item="sku" open="" separator="," close="">
                #{sku}
            </foreach>
            )
            and qty > 0
            and storerkey = 'ZTE'
            group by sku
        </foreach>
    </select>

</mapper>