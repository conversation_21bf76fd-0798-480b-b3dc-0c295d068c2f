<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.EdiPcbserialRepository">
  <resultMap id="BaseResultMap" type="com.zte.domain.model.infor.EdiPcbserial">
    <result column="SERIALKEY" jdbcType="DECIMAL" property="serialkey" />
    <result column="WHSEID" jdbcType="OTHER" property="whseid" />
    <result column="RECEIPTKEY" jdbcType="OTHER" property="receiptkey" />
    <result column="STORERKEY" jdbcType="OTHER" property="storerkey" />
    <result column="EXTERNRECEIPTKEY" jdbcType="OTHER" property="externreceiptkey" />
    <result column="EXTERNLINENO" jdbcType="OTHER" property="externlineno" />
    <result column="EXTERNALRECEIPTKEY2" jdbcType="OTHER" property="externalreceiptkey2" />
    <result column="SKU" jdbcType="OTHER" property="sku" />
    <result column="LOTTABLE02" jdbcType="OTHER" property="lottable02" />
    <result column="QTY" jdbcType="DECIMAL" property="qty" />
    <result column="TOID" jdbcType="OTHER" property="toid" />
    <result column="SERIALNUMBER" jdbcType="OTHER" property="serialnumber" />
    <result column="REPAIR" jdbcType="OTHER" property="repair" />
    <result column="SMALLLOT" jdbcType="OTHER" property="smalllot" />
    <result column="RETURN" jdbcType="OTHER" property="returner" />
    <result column="B_SCANED" jdbcType="OTHER" property="bScaned" />
    <result column="SCANEDWHO" jdbcType="OTHER" property="scanedwho" />
    <result column="SCANEDDATE" jdbcType="TIMESTAMP" property="scaneddate" />
    <result column="B_RECEIVED" jdbcType="DECIMAL" property="bReceived" />
    <result column="B_ENABLED" jdbcType="DECIMAL" property="bEnabled" />
    <result column="ADDDATE" jdbcType="TIMESTAMP" property="adddate" />
    <result column="EDITDATE" jdbcType="TIMESTAMP" property="editdate" />
    <result column="HREF11" jdbcType="OTHER" property="href11" />
    <result column="SYMBOL" jdbcType="DECIMAL" property="symbol" />
    <result column="ASNTYPE" jdbcType="DECIMAL" property="asntype" />
    <result column="GOOD_DIE_QTY" jdbcType="DECIMAL" property="goodDieQty" />
  </resultMap>

  <select id="selectEdiPcbserialAll" resultMap="BaseResultMap">
    select SERIALKEY, WHSEID, RECEIPTKEY, STORERKEY, EXTERNRECEIPTKEY, EXTERNLINENO, 
    EXTERNALRECEIPTKEY2, SKU, LOTTABLE02, QTY, TOID, SERIALNUMBER, REPAIR, SMALLLOT, 
    RETURN, B_SCANED, SCANEDWHO, SCANEDDATE, B_RECEIVED, B_ENABLED, ADDDATE, EDITDATE, 
    HREF11, SYMBOL, ASNTYPE, GOOD_DIE_QTY
    from plugin.EDI_PCBSERIAL
  </select>
  <delete id="deleteEdiPcbserial" parameterType="com.zte.domain.model.infor.EdiPcbserial">
    delete from plugin.EDI_PCBSERIAL where EXTERNRECEIPTKEY= #{externreceiptkey,jdbcType=OTHER}
  </delete>
  <update id="updateSymbol" parameterType="com.zte.domain.model.infor.EdiPcbserial">
    update plugin.EDI_PCBSERIAL set SYMBOL=#{symbol,jdbcType=DECIMAL}
    where EXTERNRECEIPTKEY= #{externreceiptkey,jdbcType=OTHER}
  </update>

  <insert id="insertEdiPcbserialBatch" parameterType="com.zte.domain.model.infor.EdiPcbserial">
	  <foreach collection="list"  item="item" separator=";" open="begin" close=";end;">
	  insert into EDI_PCBSERIAL (WHSEID, RECEIPTKEY, 
      STORERKEY, EXTERNRECEIPTKEY, EXTERNLINENO, 
      EXTERNALRECEIPTKEY2, SKU, LOTTABLE02, 
      QTY, TOID, SERIALNUMBER, 
      REPAIR, SMALLLOT, RETURN, 
      B_SCANED, SCANEDWHO, SCANEDDATE, 
      B_RECEIVED, B_ENABLED, ADDDATE, 
      EDITDATE, HREF11, SYMBOL, 
      ASNTYPE, GOOD_DIE_QTY)
    values (#{item.whseid,jdbcType=VARCHAR}, #{item.receiptkey,jdbcType=VARCHAR}, 
      #{item.storerkey,jdbcType=VARCHAR}, #{item.externreceiptkey,jdbcType=VARCHAR}, #{item.externlineno,jdbcType=VARCHAR}, 
      #{item.externalreceiptkey2,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.lottable02,jdbcType=VARCHAR}, 
      #{item.qty,jdbcType=DECIMAL}, #{item.toid,jdbcType=VARCHAR}, #{item.serialnumber,jdbcType=VARCHAR}, 
      #{item.repair,jdbcType=VARCHAR}, #{item.smalllot,jdbcType=VARCHAR}, #{item.returner,jdbcType=VARCHAR}, 
      #{item.bScaned,jdbcType=VARCHAR}, #{item.scanedwho,jdbcType=VARCHAR}, #{item.scaneddate,jdbcType=TIMESTAMP}, 
      #{item.bReceived,jdbcType=DECIMAL}, #{item.bEnabled,jdbcType=DECIMAL}, sysdate, 
      sysdate, #{item.href11,jdbcType=VARCHAR}, #{item.symbol,jdbcType=DECIMAL}, 
      #{item.asntype,jdbcType=DECIMAL}, #{item.goodDieQty,jdbcType=DECIMAL})
	  </foreach>
  </insert>
  <insert id="insertEdiPcbserial" parameterType="com.zte.domain.model.infor.EdiPcbserial">
    insert into EDI_PCBSERIAL (SERIALKEY, WHSEID, RECEIPTKEY, 
      STORERKEY, EXTERNRECEIPTKEY, EXTERNLINENO, 
      EXTERNALRECEIPTKEY2, SKU, LOTTABLE02, 
      QTY, TOID, SERIALNUMBER, 
      REPAIR, SMALLLOT, RETURN, 
      B_SCANED, SCANEDWHO, SCANEDDATE, 
      B_RECEIVED, B_ENABLED, ADDDATE, 
      EDITDATE, HREF11, SYMBOL, 
      ASNTYPE, GOOD_DIE_QTY)
    values (#{serialkey,jdbcType=DECIMAL}, #{whseid,jdbcType=OTHER}, #{receiptkey,jdbcType=OTHER}, 
      #{storerkey,jdbcType=OTHER}, #{externreceiptkey,jdbcType=OTHER}, #{externlineno,jdbcType=OTHER}, 
      #{externalreceiptkey2,jdbcType=OTHER}, #{sku,jdbcType=OTHER}, #{lottable02,jdbcType=OTHER}, 
      #{qty,jdbcType=DECIMAL}, #{toid,jdbcType=OTHER}, #{serialnumber,jdbcType=OTHER}, 
      #{repair,jdbcType=OTHER}, #{smalllot,jdbcType=OTHER}, #{returner,jdbcType=OTHER}, 
      #{bScaned,jdbcType=OTHER}, #{scanedwho,jdbcType=OTHER}, #{scaneddate,jdbcType=TIMESTAMP}, 
      #{bReceived,jdbcType=DECIMAL}, #{bEnabled,jdbcType=DECIMAL}, #{adddate,jdbcType=TIMESTAMP}, 
      #{editdate,jdbcType=TIMESTAMP}, #{href11,jdbcType=OTHER}, #{symbol,jdbcType=DECIMAL}, 
      #{asntype,jdbcType=DECIMAL}, #{goodDieQty,jdbcType=DECIMAL})
  </insert>

  <insert id="insertEdiPcbserialSelective" parameterType="com.zte.domain.model.infor.EdiPcbserial">
    insert into EDI_PCBSERIAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialkey != null">
        SERIALKEY,
      </if>

      <if test="whseid != null">
        WHSEID,
      </if>

      <if test="receiptkey != null">
        RECEIPTKEY,
      </if>

      <if test="storerkey != null">
        STORERKEY,
      </if>

      <if test="externreceiptkey != null">
        EXTERNRECEIPTKEY,
      </if>

      <if test="externlineno != null">
        EXTERNLINENO,
      </if>

      <if test="externalreceiptkey2 != null">
        EXTERNALRECEIPTKEY2,
      </if>

      <if test="sku != null">
        SKU,
      </if>

      <if test="lottable02 != null">
        LOTTABLE02,
      </if>

      <if test="qty != null">
        QTY,
      </if>

      <if test="toid != null">
        TOID,
      </if>

      <if test="serialnumber != null">
        SERIALNUMBER,
      </if>

      <if test="repair != null">
        REPAIR,
      </if>

      <if test="smalllot != null">
        SMALLLOT,
      </if>

      <if test="return != null">
        RETURN,
      </if>

      <if test="bScaned != null">
        B_SCANED,
      </if>

      <if test="scanedwho != null">
        SCANEDWHO,
      </if>

      <if test="scaneddate != null">
        SCANEDDATE,
      </if>

      <if test="bReceived != null">
        B_RECEIVED,
      </if>

      <if test="bEnabled != null">
        B_ENABLED,
      </if>

      <if test="adddate != null">
        ADDDATE,
      </if>

      <if test="editdate != null">
        EDITDATE,
      </if>

      <if test="href11 != null">
        HREF11,
      </if>

      <if test="symbol != null">
        SYMBOL,
      </if>

      <if test="asntype != null">
        ASNTYPE,
      </if>

      <if test="goodDieQty != null">
        GOOD_DIE_QTY,
      </if>

    </trim>

    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialkey != null">
        #{serialkey,jdbcType=DECIMAL},
      </if>

      <if test="whseid != null">
        #{whseid,jdbcType=OTHER},
      </if>

      <if test="receiptkey != null">
        #{receiptkey,jdbcType=OTHER},
      </if>

      <if test="storerkey != null">
        #{storerkey,jdbcType=OTHER},
      </if>

      <if test="externreceiptkey != null">
        #{externreceiptkey,jdbcType=OTHER},
      </if>

      <if test="externlineno != null">
        #{externlineno,jdbcType=OTHER},
      </if>

      <if test="externalreceiptkey2 != null">
        #{externalreceiptkey2,jdbcType=OTHER},
      </if>

      <if test="sku != null">
        #{sku,jdbcType=OTHER},
      </if>

      <if test="lottable02 != null">
        #{lottable02,jdbcType=OTHER},
      </if>

      <if test="qty != null">
        #{qty,jdbcType=DECIMAL},
      </if>

      <if test="toid != null">
        #{toid,jdbcType=OTHER},
      </if>

      <if test="serialnumber != null">
        #{serialnumber,jdbcType=OTHER},
      </if>

      <if test="repair != null">
        #{repair,jdbcType=OTHER},
      </if>

      <if test="smalllot != null">
        #{smalllot,jdbcType=OTHER},
      </if>

      <if test="return != null">
        #{return,jdbcType=OTHER},
      </if>

      <if test="bScaned != null">
        #{bScaned,jdbcType=OTHER},
      </if>

      <if test="scanedwho != null">
        #{scanedwho,jdbcType=OTHER},
      </if>

      <if test="scaneddate != null">
        #{scaneddate,jdbcType=TIMESTAMP},
      </if>

      <if test="bReceived != null">
        #{bReceived,jdbcType=DECIMAL},
      </if>

      <if test="bEnabled != null">
        #{bEnabled,jdbcType=DECIMAL},
      </if>

      <if test="adddate != null">
        #{adddate,jdbcType=TIMESTAMP},
      </if>

      <if test="editdate != null">
        #{editdate,jdbcType=TIMESTAMP},
      </if>

      <if test="href11 != null">
        #{href11,jdbcType=OTHER},
      </if>

      <if test="symbol != null">
        #{symbol,jdbcType=DECIMAL},
      </if>

      <if test="asntype != null">
        #{asntype,jdbcType=DECIMAL},
      </if>

      <if test="goodDieQty != null">
        #{goodDieQty,jdbcType=DECIMAL},
      </if>

    </trim>

  </insert>
  <select id="selectPldb" parameterType="com.zte.domain.model.infor.EdiPcbserial" resultType="java.lang.Integer">
    SELECT COUNT(1) 
        FROM WMSADMIN.PL_DB P
       WHERE P.ISACTIVE = 1
         AND UPPER(P.DB_LOGID) = #{whseid,jdbcType=OTHER}
  </select>
  <select id="selectPcbSerialCount" parameterType="com.zte.domain.model.infor.EdiPcbserial" resultType="java.lang.Integer">
    SELECT COUNT(1) 
        FROM plugin.EDI_PCBSERIAL P
       WHERE EXTERNRECEIPTKEY= #{externreceiptkey,jdbcType=OTHER}
  </select>
  <select id="selectReceiptCount" parameterType="com.zte.domain.model.infor.EdiPcbserial" resultType="java.lang.Integer">
       SELECT COUNT(1) FROM 
          ${whseid}.RECEIPT R
      WHERE R.EXTERNRECEIPTKEY=#{externreceiptkey,jdbcType=OTHER}
  </select>
  <select id="selectZteInboundSerial" parameterType="com.zte.domain.model.infor.EdiPcbserial" resultType="java.lang.Integer">
     SELECT COUNT(1) FROM 
         ${whseid}.ZTEINBOUNDSERIAL
     WHERE EXTERNRECEIPTKEY = #{externreceiptkey,jdbcType=OTHER}
  </select>
  <select id="selectZteInboundSerialToId" parameterType="com.zte.domain.model.infor.EdiPcbserial" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM 
        ${whseid}.ZTEINBOUNDSERIAL
	 WHERE TOID IN (SELECT DISTINCT TOID
     FROM PLUGIN.EDI_PCBSERIAL P
     WHERE P.EXTERNRECEIPTKEY = #{externreceiptkey,jdbcType=OTHER})
  </select>
  <insert id="insertZteInboundSerial" parameterType="com.zte.domain.model.infor.EdiPcbserial">
	  INSERT INTO ${whseid}.ZTEINBOUNDSERIAL
	  (RECEIPTKEY,
	   STORERKEY,
	   EXTERNRECEIPTKEY,
	   EXTERNLINENO,
	   EXTERNALRECEIPTKEY2,
	   SKU,
	   LOTTABLE02,
	   QTY,
	   TOID,
	   SERIALNUMBER,
	   REPAIR,
	   SMALLLOT,
	   RETURN,
	   B_SCANED,
	   SCANEDWHO,
	   SCANEDDATE,
	   SERIALKEY)
	  SELECT RECEIPTKEY,
	         STORERKEY,
	         EXTERNRECEIPTKEY,
	         MIN(EXTERNLINENO) EXTERNLINENO,
	         EXTERNALRECEIPTKEY2,
	         SKU,
	         LOTTABLE02,
	         NVL(SUM(QTY),0) QTY,
	         TOID,
	         SERIALNUMBER,
	         MIN(REPAIR) REPAIR,
	         MIN(SMALLLOT) SMALLLOT,
	         MIN(RETURN) RETURN,
	         MIN(B_SCANED) B_SCANED,
	         MIN(SCANEDWHO) SCANEDWHO,
	         MIN(SCANEDDATE) SCANEDDATE,
	         MIN(SERIALKEY) SERIALKEY
	    FROM PLUGIN.EDI_PCBSERIAL P
	   WHERE P.EXTERNRECEIPTKEY =#{externreceiptkey,jdbcType=OTHER}
	     and upper(P.WHSEID) = #{whseid,jdbcType=OTHER} and P.SKU not like '1%' GROUP BY RECEIPTKEY,STORERKEY,EXTERNRECEIPTKEY,
	     EXTERNALRECEIPTKEY2,SKU,LOTTABLE02,TOID,SERIALNUMBER
	  
  </insert>
  <update id="updatePcbSerial" parameterType="com.zte.domain.model.infor.EdiPcbserial">
    UPDATE PLUGIN.EDI_PCBSERIAL
       SET TOID = ' '
     WHERE TOID IN (SELECT TOID
                  FROM ${whseid}.ZTEINBOUNDSERIAL
                 WHERE EXTERNRECEIPTKEY = #{externreceiptkey,jdbcType=OTHER}
                   AND TOID IN (SELECT DISTINCT TOID
                                  FROM PLUGIN.EDI_PCBSERIAL P
                                 WHERE P.EXTERNRECEIPTKEY = #{externreceiptkey,jdbcType=OTHER}))
  </update>
  <delete id="deleteZteInboundSerial" parameterType="com.zte.domain.model.infor.EdiPcbserial">
     DELETE ${whseid}.ZTEINBOUNDSERIAL
     WHERE EXTERNRECEIPTKEY =#{externreceiptkey,jdbcType=OTHER}
  </delete>
</mapper>
