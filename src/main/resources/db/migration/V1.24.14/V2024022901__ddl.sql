CREATE TABLE if not exists mes_centerfactory.ps_task_history (
	id varchar(50) NOT NULL, -- 主键ID
	opt_type int2 NOT NULL, -- 操作类型 1:在线任务数量变更
	task_id varchar(80) NOT NULL, -- 任务ID
	task_no varchar(240) NOT NULL, -- 任务编号
	item_no varchar(50), -- 物料编码
	item_name varchar(500), -- 物料名称
	is_lead varchar(50), -- 环保属性
	lead_flag varchar(1), -- 含铅标识
	task_qty numeric, -- 任务数量
	task_status varchar(50), -- 任务状态
	org_id numeric, -- 组织ID
	factory_id numeric NULL, -- 工厂ID
	prodplan_no varchar(50), -- 计划跟踪单号
	prodplan_id varchar(50), -- 批次
	remark varchar(2000), -- 备注
	create_by varchar(32), -- 创建人
    create_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 创建日期
    enabled_flag varchar(1) not NULL DEFAULT 'Y',-- 是否有效
	CONSTRAINT pk_ps_task_history PRIMARY KEY (id)
);

CREATE INDEX if not exists idx_pth_task_id ON mes_centerfactory.ps_task_history USING btree (task_id);
CREATE INDEX if not exists idx_pth_task_no ON mes_centerfactory.ps_task_history USING btree (task_no);
CREATE INDEX if not exists idx_pth_create_date ON mes_centerfactory.ps_task_history USING btree (create_date);
CREATE INDEX if not exists idx_pth_prodplan_id ON mes_centerfactory.ps_task_history USING btree (prodplan_id);

COMMENT ON TABLE mes_centerfactory.ps_task_history IS '任务历史信息(记录操作前任务数据)';
-- Column comments
COMMENT ON COLUMN mes_centerfactory.ps_task_history.id IS '主键ID';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.opt_type IS '操作类型 1:在线任务数量变更';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.task_id IS '任务ID';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.task_no IS '任务编号';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.item_no IS '物料编码';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.item_name IS '物料名称';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.is_lead IS '环保属性';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.lead_flag IS '含铅标识';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.task_qty IS '任务数量';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.task_status IS '任务状态';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.org_id IS '组织ID';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.factory_id IS '工厂ID';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.prodplan_no IS '计划跟踪单号';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.prodplan_id IS '批次';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.remark IS '备注';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.create_by IS '创建人';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.create_date IS '创建日期';
COMMENT ON COLUMN mes_centerfactory.ps_task_history.enabled_flag IS '是否有效';
