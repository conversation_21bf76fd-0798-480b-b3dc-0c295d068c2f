set lock_timeout='20000ms';
COMMENT ON COLUMN mes_centerfactory.push_std_model_rcv_bill.bill_no IS '领料单号';
COMMENT ON COLUMN mes_centerfactory.push_std_model_rcv_bill.rcv_no IS '凭证单号';

ALTER TABLE mes_centerfactory.push_std_model_rcv_bill DROP CONSTRAINT pk_push_std_model_rcv_bill;
ALTER TABLE mes_centerfactory.push_std_model_rcv_bill ADD CONSTRAINT pk_push_std_model_rcv_bill PRIMARY KEY (rcv_no);


-- 为ps_task_extended表添加material_control和cloud_type字段
ALTER TABLE mes_centerfactory.ps_task_extended ADD if not exists material_control varchar(32) NULL;
ALTER TABLE mes_centerfactory.ps_task_extended ADD if not exists cloud_type varchar(32) NULL;
-- 添加字段注释
COMMENT ON COLUMN mes_centerfactory.ps_task_extended.material_control IS '物料管控类型 1:扣料模式 2:整机模式 3:部分扣料模式';
COMMENT ON COLUMN mes_centerfactory.ps_task_extended.cloud_type IS '云类型 PC:公有云 HC:混合云';

-- 增加字段
ALTER TABLE mes_centerfactory.fix_bom_head ADD if not exists task_no varchar(240) NULL;
COMMENT ON COLUMN mes_centerfactory.fix_bom_head.task_no IS '任务号';

DROP INDEX if exists mes_centerfactory.idx_fbh_fixbom_id;

ALTER TABLE mes_centerfactory.fix_bom_detail ADD if not exists head_id varchar(64) NULL;
COMMENT ON COLUMN mes_centerfactory.fix_bom_detail.head_id IS 'fix_bom_head的主键id';

CREATE index if not exists idx_fbd_head_id ON mes_centerfactory.fix_bom_detail (head_id);


ALTER TABLE mes_centerfactory.ps_task_extended ADD if not exists fix_bom_head_id varchar(64) NULL;
COMMENT ON COLUMN mes_centerfactory.ps_task_extended.fix_bom_head_id IS 'fix_bom_head主键id';

CREATE INDEX if not exists idx_ps_task_extended_fix_bom_head_id ON mes_centerfactory.ps_task_extended (fix_bom_head_id);

update mes_centerfactory.fix_bom_head set id = fix_bom_id where task_no is null;

update mes_centerfactory.fix_bom_detail set head_id = fix_bom_id where head_id is null;

update mes_centerfactory.ps_task_extended set fix_bom_head_id = fix_bom_id where fix_bom_head_id is null;
