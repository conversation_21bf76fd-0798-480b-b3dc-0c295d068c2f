CREATE TABLE IF NOT EXISTS v_mes_box_no_his (
	serialkey varchar(50) NOT NULL,
	externalorderkey2 varchar(50) NULL,
	toid varchar(50) NULL,
	sku varchar(50) NULL,
	lottable02 varchar(50) NULL,
	formid varchar(50) NULL,
	editdate timestamp NULL,
	CONSTRAINT v_mes_box_no_his_pk PRIMARY KEY (serialkey)
);
CREATE UNIQUE INDEX IF NOT EXISTS v_mes_box_no_his_serialkey_idx ON v_mes_box_no_his (serialkey);
CREATE INDEX IF NOT EXISTS v_mes_box_no_his_externalorderkey2_idx ON v_mes_box_no_his (externalorderkey2);
COMMENT ON TABLE v_mes_box_no_his IS '装焊线边仓调拨-infor按箱发料信息同步仓储成功历史信息';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN v_mes_box_no_his.serialkey IS '发料信息唯一码';
COMMENT ON COLUMN v_mes_box_no_his.externalorderkey2 IS '调拨单号';
COMMENT ON COLUMN v_mes_box_no_his.toid IS 'LFID';
COMMENT ON COLUMN v_mes_box_no_his.sku IS '物料代码';
COMMENT ON COLUMN v_mes_box_no_his.lottable02 IS '220条码';
COMMENT ON COLUMN v_mes_box_no_his.formid IS '箱号';
COMMENT ON COLUMN v_mes_box_no_his.editdate IS '操作时间';


CREATE TABLE IF NOT EXISTS v_mes_box_no_copy (
	serialkey varchar(50) NOT NULL,
	whseid varchar(50) NULL,
	sku varchar(50) NULL,
	allowoverpick numeric NULL,
	lottable02 varchar(50) NULL,
	lottable07 varchar(50) NULL,
	lottable08 varchar(50) NULL,
	toid varchar(50) NULL,
	externalorderkey2 varchar(50) NULL,
	externorderkey varchar(50) NULL,
	href11 varchar(50) NULL,
	href55 varchar(50) NULL,
	susr1 varchar(50) NULL,
	ref02 varchar(50) NULL,
	c_company varchar(50) NULL,
	formid varchar(50) NULL,
	qty numeric NULL,
	externLineNo varchar(50) NULL,
	editdate timestamp NULL,
	CONSTRAINT v_mes_box_no_copy_pk PRIMARY KEY (serialkey)
);
CREATE UNIQUE INDEX IF NOT EXISTS v_mes_box_no_copy_serialkey_idx ON v_mes_box_no_copy (serialkey);
CREATE INDEX IF NOT EXISTS v_mes_box_no_copy_externalorderkey2_idx ON v_mes_box_no_copy (externalorderkey2);
CREATE INDEX IF NOT EXISTS v_mes_box_no_copy_formid_idx ON v_mes_box_no_copy (formid);
COMMENT ON TABLE v_mes_box_no_copy IS '装焊线边仓调拨-infor按箱发料信息同步仓储失败数据';

-- Column comments

COMMENT ON COLUMN v_mes_box_no_copy.serialkey IS '发料信息唯一码';
COMMENT ON COLUMN v_mes_box_no_copy.whseid IS '央仓仓库';
COMMENT ON COLUMN v_mes_box_no_copy.sku IS '物料代码';
COMMENT ON COLUMN v_mes_box_no_copy.allowoverpick IS '是否超发标志';
COMMENT ON COLUMN v_mes_box_no_copy.lottable02 IS '220条码';
COMMENT ON COLUMN v_mes_box_no_copy.lottable07 IS 'UUID';
COMMENT ON COLUMN v_mes_box_no_copy.lottable08 IS '环保属性leadProperties';
COMMENT ON COLUMN v_mes_box_no_copy.toid IS 'LFID';
COMMENT ON COLUMN v_mes_box_no_copy.externalorderkey2 IS '调拨单号';
COMMENT ON COLUMN v_mes_box_no_copy.href11 IS '单据类型 101表示线边仓';
COMMENT ON COLUMN v_mes_box_no_copy.href55 IS '线边仓仓库编码';
COMMENT ON COLUMN v_mes_box_no_copy.susr1 IS '计划跟踪单批次';
COMMENT ON COLUMN v_mes_box_no_copy.formid IS '箱号';
COMMENT ON COLUMN v_mes_box_no_copy.qty IS '发运数量';
COMMENT ON COLUMN v_mes_box_no_copy.externLineNo IS '关联需求单行号';
COMMENT ON COLUMN v_mes_box_no_copy.editdate IS '操作时间';