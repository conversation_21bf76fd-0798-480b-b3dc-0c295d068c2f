#
##*****************************************************
##redis!!!!!!!!!!\u5355\u8282\u70B9\u914D\u7F6E\uFF0C\u8BE5\u9879\u914D\u7F6E\u4EC5\u7528\u4F5C\u9ED8\u8BA4\u503C\u4F7F\u7528\uFF0C\u4E0D\u80FD\u4FEE\u6539\u3002
##*****************************************************
## Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#spring.redis.host=127.0.0.1
## Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#spring.redis.port=0
#
##*****************************************************
##RedisCluster\u96C6\u7FA4\u914D\u7F6E\uFF0C\u8BE5\u9879\u914D\u7F6E\u79FB\u81F3 application-dev.properties \u6216 \u914D\u7F6E\u4E2D\u5FC3
##*****************************************************
#
## Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#spring.redis.hosts=127.0.0.1:0
##Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#spring.redis.sotimeout=0
##Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#spring.redis.maxAttempts=0
##Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#spring.redis.maxRedirects=0
#
##Redis\u9ED8\u8BA4\u914D\u7F6E\uFF0C\u6B64\u5904\u914D\u7F6E\u4E0D\u80FD\u4FEE\u6539\uFF0C\u53EA\u80FD\u4ECE application-*.properties\u6216\u914D\u7F6E\u4E2D\u5FC3\u53BB\u914D\u7F6E \u3002
#
##*****************************************************
##redis\u901A\u7528\u914D\u7F6E
##*****************************************************
#
#
## \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
#spring.redis.pool.max-active=64
## \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09,\u5355\u4F4D\u6BEB\u79D2
#spring.redis.pool.max-wait=-1
## \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
#spring.redis.pool.max-idle=8
## \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
#spring.redis.pool.min-idle=1
## \u8FDE\u63A5\u6C60\u4E2D\u9010\u51FA\u8FDE\u63A5\u7684\u6700\u5C0F\u7A7A\u95F2\u65F6\u95F4 \u9ED8\u8BA41800000\u6BEB\u79D2(30\u5206\u949F)
#spring.redis.pool.min-evictableIdleTimeMillis=1800000
## \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
#spring.redis.timeout=30000
## redis cacheManager\u8FC7\u671F\u65F6\u95F4,\u5355\u4F4D\u79D2\uFF0C\u9ED8\u8BA4\u4E3A0,\u4EE3\u8868\u6C38\u4E0D\u8FC7\u671F
#spring.redis.cacheManager.expire = 86400
