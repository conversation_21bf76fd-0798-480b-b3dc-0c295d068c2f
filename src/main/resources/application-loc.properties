#======================
#======================
#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================
#======================
#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================
#*****************************************************
#======================
#MSB注册信息配置
#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#*****************************************************
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#======================
#MSB注册信息配置
#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名
#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名
#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#======================

#本示例用的是开发环境的情况，一般为你的电脑的IP。
#*****************************************************
#点对点调用服务ip
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#服务实例名

#======================
#MSB注册信息配置

#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
spring.druid.allow =
#*****************************************************
#本示例用的是开发环境的情况，一般为你的电脑的IP。

#点对点调用服务ip
#*****************************************************
#服务实例名

#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#======================

#MSB注册信息配置
#该配置文件仅在当未启用配置中心时，开发本地临时调试使用，maven打包时，会自动删除该文件。
#*****************************************************
servicecenter = msb
#本示例用的是开发环境的情况，一般为你的电脑的IP。
#点对点调用服务ip

#*****************************************************

#服务实例名
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
#======================

#MSB注册信息配置
eureka.client.enabled = false

#*****************************************************
register.address = ************:10081
#服务实例,生产环境为域名,如:api.prm.zte.com.cn.user,api.ichannel.zte.com.cn.order
#点对点调用服务ip
#本示例用的是开发环境的情况，一般为你的电脑的IP。


#*****************************************************
#服务实例名
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
register.node.ipport = imes.dev.zte.com.cn:80


#MSB注册信息配置
register.serviceName = ${spring.application.name}
#配置上传文件大小限制为100MB。注：必须带单位。设置为-1表示不限制大小
#*****************************************************
#服务实例,生产环境为域名,如:api.prm.zte.com.cn.user,api.ichannel.zte.com.cn.order
spring.servlet.multipart.max-file-size = 200MB



#点对点调用服务ip
#本示例用的是开发环境的情况，一般为你的电脑的IP。

spring.servlet.multipart.max-request-size = 200MB


#服务实例名
#注册msb地址，本示例放的是测试环境的MSB。 #msrtest.zte.com.cn:10081
center.interface.address = http://imes.dev.zte.com.cn

common.exception.log.enabled = false

#配置上传文件大小限制为100MB。注：必须带单位。设置为-1表示不限制大小
common.exception.log.always =
common.auth.enabled = true
common.auth.secretKey = KENC(bL1j0fKTGh0oAsBnOVWZRNnn5gZwyn2Xvu3x3QH7qWc5Uj7UwtaCajKxN2wA9bcsr9ZoQQ==$$pnW3HqB1FXljIf0R)
#服务实例,生产环境为域名,如:api.prm.zte.com.cn.user,api.ichannel.zte.com.cn.order


common.auth.ignore.emp = 0668000743,0668001145,0668001246,00286523,10313234,0668000830,00286569,10317937,10309565,0668000851,10350563,0668001147,10351947,0668000782,00339259,10337580,10349620,10351836
#点对点调用服务ip
#本示例用的是开发环境的情况，一般为你的电脑的IP。
common.auth.ignore.path = /info,/swagger-ui/**,/swagger-resources/**,/v3/api-docs,/webjars/**,/swagger.json,/swagger-ui.html,/user/ucs/verify,/resource/factoryList,/resource/menuList/*,/BS/getWebSwitch,/customerDataLog/handleResultOfPushDataToB2B,/ai/openapi/v1/chat,/BS/sysLookupValues,/ai/openapi/v1/generalExportAI


common.auth.ignore.ip = ************,*************
#服务实例名
common.tokenMap[tokenVerifyUrl] = http://mdmtest.zte.com.cn:8888/uactoken/auth/token/verify.serv
common.artifactory.url = https://arttest.zte.com.cn/artifactory

common.artifactory.user = KENC(M6XOSRpSBinPP/gvBNBEWD7GfZmjFhtJbDmkVm9RnDL218owhsBdnkUA9Q==$$mbtfUsvj3bpVUmJw)

#配置上传文件大小限制为100MB。注：必须带单位。设置为-1表示不限制大小
common.artifactory.pwd = KENC(M6XOSRpSBinPP/gvBNBEWD7GfZmjFhtJbDmkVm9RnDL218owhsBdnkUA9Q==$$mbtfUsvj3bpVUmJw)
common.otherMap[email_url] = http://*********:80/api
common.alarm.emp = 10328274,10266925


common.alarm.type = sql,memory
#点对点调用服务ip
resource.factoryDomain = {"51":"//mes.test.zte.com.cn","52":"//mes.test.zte.com.cn","53":"//mes.test.zte.com.cn","55":"//mes.test.zte.com.cn","56":"//mes.test.zte.com.cn","58":"//mes.test.zte.com.cn","60":"//mes.test.zte.com.cn","1":"//mes.test.zte.com.cn","2":"//mes.test.zte.com.cn","3":"//mes.test.zte.com.cn"}
#实体ID：1.手机，2.系统
jdbc1.type = com.alibaba.druid.pool.DruidDataSource


jdbc1.url = ******************************************
jdbc1.username = KENC(OPelecBPQULL0CkHTinEiPZfcP2xjQgXdVfHiKgBv/Ra$$S1UGePTn3voZNq8C)

jdbc1.password = KENC(kYuY/DE6khtpwKFVbVrXbOy4Pr7cng7cIiU=$$uFbjl/gQpNiA4P07)
#*****************************************************
jdbc1.driverClassName = org.postgresql.Driver


#=======================================archiveData=====================================
#实体ID：1.手机，2.系统
archiveData.type = com.alibaba.druid.pool.DruidDataSource
archiveData.url = ********************************************
archiveData.username = KENC(OPelecBPQULL0CkHTinEiPZfcP2xjQgXdVfHiKgBv/Ra$$S1UGePTn3voZNq8C)
archiveData.password = KENC(kYuY/DE6khtpwKFVbVrXbOy4Pr7cng7cIiU=$$uFbjl/gQpNiA4P07)
archiveData.driverClassName = org.postgresql.Driver

# 网关鉴权和错误日志打印
message.server = http://msptest.zte.com.cn:8888/zte-itp-msp-message/v1
#*****************************************************
msa.ccs.enable = true
msa.ccs.resourceCode = mesdev
cacheCloud.server = http://mspuat.zte.com.cn:8888/zte-itp-msp-cachecloud/
msa.redis.serializerType = genericJackson2Json
upp.auth.productId = 3192
# 2790002
upp.auth.moduleId = KENC(3xZm0k2vSWl4Ww6QwMx7uAyyGiBMZNJcs8P9QQ==$$VVBIQZB+cHjl9Ij2)

upp.auth.manageUrl = https://uactest.zte.com.cn/zte-sec-upp-bff
upp.auth.tenantId = 4304
#*****************************************************
upp.auth.productSecretKey = KENC(JOyF90f8wEhQT2STN9aVpjZUQOt1D2dHioyOF5IjnVp4TATNaqyAAYLFIH4axFQo$$HVNLx6vzhmdr4/dF)
#数据库配置
#实体ID：1.手机，2.系统
#通用配置
upp.auth.authUrl = https://uactest.zte.com.cn/zte-sec-upp-authbff

#数据对称加密密钥
upp.auth.encryptor = KENC(PrUvJ1rCqK06Tj/kSiZFmimTKHnVAKunEIRhCTNMtlR3Ou32SPAJOHyj731lkMsiRxOUyjPKW3jB8kpsfhp4EoobOWK6P+TlrSBXBdHIO6Y9YPwBlFUKXvzYPEY5AyFrj7IlPh+6tKxjfrBR08QJSeDzyaifVLXAP3gfcQyUBApqOjtM8hSoogJ1O0X5Gu2pODWDKc+D4YvJLDgGDhrI/wOM0G6itCYCWP4jSkyszJ/qCioPwe5O2Zh68N7G6LBRX6SmN29XUNto52YZj0aMAd7deXrgxCqifWhZViIg23lJqzTbFXeTrA==$$8bHXfItzi58kc1no)

#签名验证，sha256加密盐值
upp.sha256.salt = KENC(s2UP3DD+zsBa8kMdFvEL7SL/bCURcfjhYxzcbT11cHe5TXkWpowtttgUwjzPpzTQRT5+fAtynTpVeo+MtZIlUGfKFgNxDoB0AGe5PsqCvS6jfauZFNUV0omp9EbvpbEviM13tYf+4jMx6uvKeo/KPDWJpNQ9SFSqLzv0wsDmNm3ipZKYouSaT2SJzr2+zb/A$$kyyEJG2Y0vL5irLk)
#*****************************************************
msb.address = msrtest.zte.com.cn:10081

common.entity.id = 2
common.center.factory.id = 51

common.center.factory.db = DB1
#制品库地址配置
common.pdmdocws.url = http://**********/WebUINewProject/DocManage/DocInterface/PDM_M01_DocSrv.asmx
common.pdmuploadfile.url = http://**********/WebUINewProject/DocManage/DocInterface/PDM_M01_UploadFileSrv.asmx
common.pdmdownloadfile.url = http://***********/EAIWebService/EaiService/ProdName/PDM_M00_AttachFileStreamSrv.aspx

common.pdmdocsearchsrv.url = http://**********/WebUINewProject/DocManage/DocInterface/PDM_M01_DocSearchSrv.asmx
common.pdfConvertUrl.url = http://***********:8877/api/PdfConvert?zoom=95&cxfix=1
#实体ID：1.手机，2.系统
#邮件服务地址 test
sn.ca.socket.ip = icatest.zte.com.cn
sn.ca.socket.port = 9040
#邮件服务地址 正式
pdm.x.system.code = 100000108713

#common.otherMap[email_url]= http://msr.zte.com.cn/api
pdm.x.auth.value = KENC(++X3zpkSqlc3ENbtlONh4dN8hjt+hUhn9xEAIerqOJXSePsHWv1h7dN0s/UcRX2q$$iKmAWCBOcghfGp8E)

ucs.sourceId = 10014
# 告警通知配置
ucs.accessKey = KENC(QkKWLKoz4nMPI6qANsWfFqWbDkMAP7may2f5IZZh/7HGJMQeYzg5kUuBm0Mjtt8BqwPBdA==$$oNbubxcEC/GPTmsT)
ucs.secretKey = KENC(OhRkTNKR7FLqohMgbASm05mybhmKsJsdDmshol1JKMDSc+RD4+6dZFWEFRbGy3DKqgCeFA==$$6sfz29GfRxh+MIdP)
ucs.apiSvcName = zte-bmt-ucs-api

ucs.apiVersion = v1
#菜单url路径配置
ucs.apiVerifyOper = /srv/token/verify
ucs.apiDetailOper = /srv/account/detail
#*****************************************************
#实体ID：1.手机，2.系统
#数据库配置

ucs.apiDetailOper2 = /srv/v2/account/detail/
ucs.apiLogout = /srv/logout
#*****************************************************#=======================================jdbc1=====================================
ucs.apiSendType = POST
#CA绑定Socket接口测试环境port
### druid setting 本地工厂数据源配置###
ucs.outAccountLen = 20
ucs.apiUrl = http://test55.ucs.zte.com.cn:8888/
ucs.userInfoUrl = http://api.hr.zte.com.cn/zte-hrm-usercenter-pginfo
ucs.prefixUrl = http://test55.ucs.zte.com.cn:8888/
ucs.sdk.mode = ucstoken
ucs.sdk.issuer = https://test55.ucs.zte.com.cn/zte-bmt-ucs-portalbff/oidc/10001
### 不支持的补充配置 ###
ucs.sdk.clientId = 30014

ucs.sdk.secretKey = KENC(H/wsVteKyIhBmQx01jVZSX0K/86saABOfZqxuXTBWuDDfa5Sz7XsXtL5pk1GsjsSphIRZ/T+CT4cmqVGNfQPI/1+OXmdeyvI+XZzuw1AFBU=$$h7D006+y3+XS5JlL)

ucs.sdk.baseUrl = http://imes.dev.zte.com.cn/zte-mes-manufactureshare-centerfactory
#*****************************************************
ucs.sdk.scope = openid
#EXCEL文件上传(PDFConvert)生产地址


#实体ID：1.手机，2.系统
#KAFKA配置
ucs.sdk.store = redis
#CA绑定Socket接口测试环境port
#*****************************************************
ucs.sdk.checkState = true
#spring.kafka.bootstrapServers[0]=10.5.5.252:9092
ucs.sdk.ucstoken.authFrom = header
ucs.sdk.ucstoken.forceCheck = true

ucs.auth.whiteList = system,10260525,10307315,10296137,00286523,10313234,10328274,10270446,10274973,00236517,00286569,10309565,00236785,10275508,00260524,10266925,10321587

ucs.auth.exclusionsPath = /PS/pilotTestBarcodeTestRecordUpload,/info,/swagger-ui/**,/hrmUser/getHrmPersonInfo,/approvalProcessInfo/**,/swagger-resources/**,/v3/api-docs,/webjars/**,/swagger.json,/swagger-ui.html

springfox.documentation.swagger-ui.enabled = true
#*****************************************************
approval.sdk.app.appId = APP98801639808899935
# 2020-09-30 缓存云高可用改造，主从模式，redis配置
approval.sdk.app.secretKey = KENC(6fnuWOg/ca3zQmSVy5Bp2VTndZjNoeDGUGhcRki5yG/WII8+RNE0rmkHxbUbSOWcmLRP7R0nWPQeYTGRngtJTBVrIblQusuHhCcGYjNdBhk=$$azmohhaGzKHnbVUv)
#*****************************************************
approval.sdk.app.appCode = iMES
#common.pdfConvertUrl.url=http://*************/api/PdfConvert
#EXCEL文件上传(PDFConvert)生产地址
#开启缓存云访问模式
approval.sdk.header.xEmpNo.default = 10328274


approval.flowCode.scrap = zte-mes-manufactureshare-scrap
#CA绑定Socket接口测试环境ip
approval.flowCode.production.feed = zte-mes-manufactureshare-production-feed
#CA绑定Socket接口测试环境port
#缓存云资源编码，或主从模式的集群名称
cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true
#CA绑定Socket接口测试环境ip
#指定缓存云微服务url，默认为空，用于测试环境，3.2.10.RELEASE及之后的版本支持
cloudDiskSDK.xOrgId = MES_ACCESS_KEY
#目前我们只在UAT环境上提供缓存云的redis服务器，所以如果在测试环境中需要使用UAT环境下的redis服务器，就要配置UAT缓存云微服务地址。
cloudDiskSDK.xOriginServiceName = MES
#生产环境和UAT环境中，该配置项可以不配置
cloudDiskSDK.xSecretKey = KENC(7hVHdfiHVRXLAYTfUmeFf50Wno2YsB3t$$NojmC0QDMBc0ESiD)
pdm.status.not.released.classified = 修改中,审核中,标审中,会签中,待批准,批准中,待发放
#序列化器;必填
user.language = zh
user.region = CN

imes.interface.log.Path = /PS/taskInfoChangeableQuery,/PS/exportTaskQueryTable,/aoiFile/upload/**,/customerDataLog/handleResultOfPushDataToB2B

spring.mail.host = *************
############################################统一权限配置 start #############################################################
spring.mail.username = <EMAIL>
# 当前产品id, 向统一权限系统索要（区分环境）
approval.flowCode.scrap.frock = zte-mes-manufactureshare-scrap-frock
icenter.hostname = https://icenter-systest.test.zte.com.cn/
# 当前模块id, 由DT管理员创建（区分环境）
icenter.pushType = 972f0ad8374e44428e5a224d055d7d6d
icenter.sysCode = 207083
# 租户id,测试/仿真环#测试环境：http://uactest.zte.com.cn/zte-sec-upp-bff；生产环境：http://uac.zte.com.cn/zte-sec-upp-bff；仿真环境：http://uactest.zte.com.cn:8081/zte-sec-upp-bff
icenter.tenantId = 10151
icenter.secretKey = KENC(wFHeYSXXpTo9+MR/5WX99AwVdMnrESSmI/FYK3s+27PuefpUlNT21Xv+FkoE3nHE$$EO/OERV5X/MJTULP)
# 测试环境:4304  生产环境:2
mybatis.configuration.skipLimitKey = insert into,update ,delete from,count(,sum(,avg(,min(,max(,call ,.nextval
approval.flowCode.production.fixture = imes-zte-mes-manufactureshare-fixture
# 产品密钥key，向统一权限索要（区分环境）
b2b.header.appId = internal_iMES
b2b.authorization.appSecret = KENC(ZcUIk7oAHpipA6ItBgwjqXYC0PScyO0aAmSGNBKAPcnoS9znzgwDqGcyG/RioehKMaVEqg==$$Rw5Z3GiV1wPZA7WP)
# 鉴权地址（区分环境）
b2b.url = https://b2b.test.zte.com.cn/gateway/common/1.0/api/receive
#测试环境：http://uactest.zte.com.cn/zte-sec-upp-authbff；生产环境：http://uac.zte.com.cn/zte-sec-upp-authbff；仿真环境：http://uactest.zte.com.cn:8081/zte-sec-upp-authbff
numberSquare.sdk.xEmpNo.default = 0000000
dmt.dcp.dataservice.dbEnv = prod
# 授权地址（区分环境）
dmt.dcp.dataservice.rmaBoardAppId = 856236767346458624

dmt.dcp.dataservice.faultInformationAppId = 858320066306932736
#����msb ��ַ��������  Ĭ�� *********:10081
dmt.dcp.dataservice.boardRepairStationAppId = 858333209120768000
dmt.dcp.dataservice.appKey = KENC(xlPxc437H7bQf377FHZzKpzmCL5IdpGaVE8N2kT0cIa/KcGs+zKhDQtIigvSMARw$$Uiuc8y8wEabem3TX)

dmt.dcp.dataservice.appSecret = KENC(NrN0JB0E30MiMFPp0VXNtUc8C5FJFRKMsDZDpGAbt3vlUiM5rj+ygtNAbHmetSLarA2GhmLTe6ulsgcziuk/flcjDnpQDJCp+sp71Pq3nZ0=$$8CX4EWTEvHoCDxe0)
############################################统一权限配置 end #############################################################

##EXCEL文件上传(PDFConvert)地址
#common.pdfConvertUrl.url=http://*************/api/PdfConvert
asm.station.log = http://test.asm.zte.com.cn/asmstest/eq/StationLog.do?operation=queryStationLog
#EXCEL文件上传(PDFConvert)生产地址
aps.get.pan.and.model.url = http://testscpa.zte.com.cn:8443/zte-aps-scpa-sop/overallModel/getProdModelList
aps.get.pan.and.model.max = 20000

gei.taskServerClient = RedisTaskServerClient
#CA绑定Socket接口测试环境ip
gei.fileStoreType = CloudUdmFileStore
gei.mainTaskCallback = IMESMainTaskCallback
#CA绑定Socket接口测试环境port
gei.directExportEnable = true
get.directExportThreshold = 50000
#CA绑定Socket接口测试环境ip
approval.flowCode.scrapTerminal = zte-mes-manufactureshare-scrap-terminal
cf.user.menu.max.count = 20
#CA绑定Socket接口测试环境port


#=================================== ucs单点登录配置 ===================================
#从用户中心申请到的调用者ID（tenant_id）
#从用户中心申请到的AccessKey，必填
#从用户中心申请到的SecretKey，计算"X-Auth-Value"，必填
#用户中心的核心API微服务名称
#微服务版本号
#微服务验证接口地址
#微服务查询详细信息接口地址
#微服务用户注销接口地址
#微服务HTTP请求方式
#外部用户账户长度判断逻辑
#完整API URL地址
#获取个人信息API_URL
#UCS login uri

#*****************************************************
# 用户中心，鉴权配置
#*****************************************************
# redis或memory，默认memory，一般使用 redis，因为当前环境一般为多实例环境，需要使用 redis 作为分布式存储；只有单实例时，才使用 memory（内存形式存储）
#打开/关闭token校验
#跳过鉴权白名单

# 打开/关闭swgger  生产环境需配置为false屏蔽swagger


#*****************************************************
# 审批中心SDK配置
#*****************************************************

#文档云





#json 字段忽略注解
#spring.jackson.default-property-inclusion=non_null

#记录指定接口记录日志到数据库表

# 发送邮件的服务器，
#发送邮箱地址

# icenter配置

# B2B 接口请求头相关配置

#*******************返修中心大数据相关配置************************
#******** 大数据环境 *******
#******** 板件信息接口appid *******
#******** 故障信息接口appid *******
#******** RMA工站日志信息appid *******
#******** IMES调用系统相关鉴权配置 *******


#*****************************************************
# seata 配置
#*****************************************************
# 开发,测试，仿真，生产环境 分别是 seata-dev,seata-test,seata-uat,seata-prod
# 降级开关
# 全局事务开关   默认false。false为开启，true为关闭
#seata.config.zk.server-addr: 10.40.13.58:2181,10.54.31.62:2182,10.55.22.250:2183      #生产环境
#seata.registry.zk.server-addr: 10.40.13.58:2181,10.54.31.62:2182,10.55.22.250:2183      #生产环境
# aps获取计划组以及机型信息

# 导出配置
# 任务服务器，保存任务信息(必填)。LocalTaskServerClient保存在内存中，单机调试时使用。
# 文档存储类型(必填)。LocalFileStore 本地， CloudUdmFileStore 文档云
# 任务完成回调(选填). IMESMainTaskCallback异步导出上传文档云后发邮件
# 直接导出开关，默认开启(选填)


# 文档预览配置
filePreView.accessKey = KENC(k1rOAtjYUUssTUi5uzHfdeMy88UPWes2/wPFYzV0EUo=$$Xyxe4h94RhPwoI0+)
filePreView.accessSecret = KENC(ZXCDKTT+sAtTCQVBHp72L3OK4j5yto5A0hEZct8vPOLFxhtz89AIs5bK0niXqMWxYmtWRH9xxSiSyxVvx2lSu7czZYHw3+WEj1z4zsgC/sQ=$$R90Unpy1jwdx9WaA)
filePreView.baseUrl = http://test.idrive.zte.com.cn/zte-km-cloududm-docview/
filePreView.viewUri = /api/docview/getPreviewUrl/v2
filePreView.environment = dev
#人事接口
ihol.usercenter.getPersonGeneralInfo.url = https://icosg.test.zte.com.cn/ihol/usercenter/pginfo/usercenter/plain/getPersonGeneralInfo
token.secret = KENC(h9calvYOGxU1kvTK9+QTXqbSaGNyGfo0lcB3ZxquRUXJZy9I9CwJM4Dhjw5ZTBcgy5fHXlWvD0V1XcpK$$kruGAnI495jQHhZA)
msa.druid.web.enable = false
management.endpoints.web.exposure.include = info,health,metrics

springfox.documentation.auto-startup = true
springfox.documentation.enabled = true
ai.platform.chatbot.chatBotUrl = https://moaportal.test.zte.com.cn:15061/services/sendExtmsg/users/{senduri}
ai.platform.chatbot.sysCode = 60
ai.platform.chatbot.authKey = KENC(BptNHrWqwPYBKIMhEsnEkioaUdU=$$F/vG84oK8YnYtRd0)
ai.platform.chatbot.authCode = KENC(cxoei5Mq1yD3hR7+mUlLgBhuvhs912G8pn5oSNEyla3TqyYfsXDB5oL3p4dOUumF$$cGG9hD1d9br3crSS)
ai.platform.chatbot.appId = KENC(j5mjm/b7ZQHP49QMynvuSe8yGDjfZSnEONlOvUzBa0RyZ5EjLtf8bKsQTu3rL5GG$$P0xsN8T0ieqomBA8)
ai.platform.chatbot.apiKey = KENC(tUJiTPWYnFWebC4OOUTFruYLLPqXxYGrA/CrddGGcWqWpZIuk32vuAVH9ffd5dWI$$pLc4OGahfZbQAESh)

raas.emp.no = 00236785
raas.auth.value = KENC(Ir67s4glg5ve9qmF7rAM/3AkD1pWFa1lLjcQpdiuvXJN0PaIl11DjWgaerT/JzO+US2VNekTdEVDkh6dyXXRo3/jSwBpyIdHOOAtvh0JKYdVviSitUw9VZ67MGGKIJ1TYf5qUGgYCpQ2S1/cet3Iagb6jXDSxhuu1v4iJO8WgEdRtrux8y0jWf2zwDFRwgr7qe/9r1rzCIaZTPCvtbB6HNG7Ej08Es3CMrjAeymwZ9o98NsoIC2g0jeGtFN1SqkUw1bM8pmjFjfP8pc8Lcp8Vd2sGBCeFjEp4IRDrWN8qZvWo/RtC6o16KnXqxppR9DZ$$ZVHG6RE8GeSTX0px)
# 框架redis 加密串
b2b.auto.appSecret = KENC(ZcUIk7oAHpipA6ItBgwjqXYC0PScyO0aAmSGNBKAPcnoS9znzgwDqGcyG/RioehKMaVEqg==$$Rw5Z3GiV1wPZA7WP)
dmt.dcp.dataservice.appCodeNew = KENC(nj8tTIHcoLrqn0DzP2pu5wAQAAp7OL8ljyoCaPt4havIQssLFAV7YKuLBSoq+rKd$$p8QOOOBw6hxTtyEJ)
filePreView.accessSecretNew = KENC(ZXCDKTT+sAtTCQVBHp72L3OK4j5yto5A0hEZct8vPOLFxhtz89AIs5bK0niXqMWxYmtWRH9xxSiSyxVvx2lSu7czZYHw3+WEj1z4zsgC/sQ=$$R90Unpy1jwdx9WaA)
filePreView.accessKeyNew = KENC(k1rOAtjYUUssTUi5uzHfdeMy88UPWes2/wPFYzV0EUo=$$Xyxe4h94RhPwoI0+)
pdm.x.authentication.value = KENC(mcDpYwqkehztmDA/Dqw0FTew0DN1Fj4WAnqcf7cWJpB0g6HZ6PLXUbm5LNXtKSzb$$65rUdxCf+73yOE31)
ucs.system.secretKey = KENC(OhRkTNKR7FLqohMgbASm05mybhmKsJsdDmshol1JKMDSc+RD4+6dZFWEFRbGy3DKqgCeFA==$$6sfz29GfRxh+MIdP)
ucs.system.accessKey = KENC(QkKWLKoz4nMPI6qANsWfFqWbDkMAP7may2f5IZZh/7HGJMQeYzg5kUuBm0Mjtt8BqwPBdA==$$oNbubxcEC/GPTmsT)
#msa加解密
msa.rootkey.salt = fghijklmabcden
msa.rootkey.factor1 = imes_dev
msa.encrypt.datakey = A3csTD6qYMWbOl7YN0dGu9VhQ5Auh7cns7hmeTQj4f2HDSOPMdvNQLGYzXDHz43jO8+2Ddf4lj4QXTAV65aMVg==
msa.ccs.encrypt.factor = KENC(PMBkmXoq4xEAanRS2DX4bHuH7/nZoWjU8QXra/nIF28lAH0StIdV3Q==$$z4J9mqzRTT7UCFFR)
message.inoneAppCode = imes
aps.get.derivedCode.info.url = https://testscpa.zte.com.cn:8443/zte-aps-scpa-prodplan/prodPlanAppoint/getMainDeriveRelations
spring.kafka.enabled = true
bProdBom.erpWriteBack.alarmEmail = <EMAIL>,<EMAIL>
bProdBom.erpWriteBack.aheadData = 365
bProdBom.erpWriteBack.failedNumber = 10
inone.imes.app.code = KENC(U3BspWBL6V4ZcoryY8Sxdpd2ELugR7B09l70xY89gKju5crt7JY6d3OwRJK2Xu6f$$TBOTWz3TTYE85nwz)
ihol.usercenter.queryPersonGeneralInfo.url = https://icosg.test.zte.com.cn/ihol/usercenter/pginfo/usercenter/plain/queryPersonGeneralInfo
ihol.inone.usercenter.getPersonGeneralInfo.url = https://icosg.test.zte.com.cn/ihol/usercenter/pginfo/usercenter/plain/getPersonGeneralInfo
redis.mode = 4

###########KMS配置###########
msa.security.server.dt-kms.enable = true
msa.security.server.dt-kms.appName = ENC(tfi138lKo3hsNakbHWVtf40rrGB9r8gyj9CqrtkC7XDGx//hFr8bkqA=)
msa.security.server.dt-kms.appSecret = ENC(dSwm5npIP6hK0050sHnjcjTvNZelC+1oDXP9t/F+unfNo7pRmwU6gQuZ8A==)
msa.security.server.dt-kms.userId = ENC(PhzBXoTf1wtg6N2m7hMmHp6rXvJRZIBq6aIPmMlexywfbkZzFL2HWdgisG8=)
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
## = ZteSecurity的加密服务端类型，可选值：isoa-security、dt-kms，默认值isoa-security
msa.security.client.zteSecurity.serverType = dt-kms
### = 当加密服务端为dt-kms，该配置设置为true时，zteSecurity默认会使用kms服务端进行解密，若解密失败尝试使用isoa-security服务端进行解密
msa.security.client.zteSecurity.decryptCompatibleEnable = true
#### = 配置默认使用的主密钥标识，只有一个时可省略
msa.security.server.dt-kms.defaultMasterKey = imesKeys
msa.security.server.dt-kms.masterKeys.imesKeys.secretKeyType = zxiscp:imes300:imesweb:general
##### = 配置主密钥标识imesKeys默认使用的数据密钥标识，只有一个时可省略
msa.security.server.dt-kms.masterKeys.imesKeys.defaultDataKey = general
msa.security.server.dt-kms.masterKeys.imesKeys.dataKeys.general = M2ZkMDNkZDM1MGZjNGI0ZjIzMmVkYWRkYzczY2I0OGE4MzZlYWUyMGYwMThmMDVlZmU5YjI0MDM5YTU2ZjczNDUzZWYzZTVlNGNiNWZlMmU5NmQ4NTkxYzM1Mzg2NzAz$$OWRkNDU5OTkwMWVlZmQzMDQ5ZDY2YzJlMGM3ZmFmYTU=&&MGY2Zjc3ZmItNGQ1Yi00NzQ0LWFjZTctOTgzYzU0MjM5YjE5
server.port = 8081
flyway.specify.datasource = DB1
cf.api.user.permissions.list = 0668000851,10270446,0668000782,0668000743,0668001145,0668001246,0668000830,10308742,10309565,00260524,10338918,6005003044,00286569,10337580,10349620,00339259,0668000680,10317937,00286523,10305390,10351836,10275508,10351947,0668000743,10260525,10307315,10343014
#通知中心
notice.center.url = https://icenter-systest.test.zte.com.cn/zte-icenter-notice-message/notice/template/send
notice.center.appcode = KENC(iT4omABdEJJlu+k1ekQp1TSmv6G9L8xhez+XYSA7kPenyxYOszalK2jh7FHoQQOT$$yssVZdfsY0nIpQBu)
notice.center.accessSecret = KENC(FJ8c9Lt1BHVIQy0IMRXWqXnDTtHikBoaoNL5yrshtDHr3S6eADp38/gN/qTNCAP9ZHGFAHmGSLXbnUGppalvQgPf8S/ppXUNWRLqKK2B6Vs=$$Fk38bYzYaZCS2GSf)
notice.center.udsKey = KENC(5DRErRQcoKELVuQDizuAYPulMxCHlAhTQS/ROPXlzrI=$$PR0MPV1S+tvco4AM)
ucs.domain.url = http://test55.ucs.zte.com.cn:8888/zte-bmt-ucs-api
ucs.encryption.switch = N
inone.base.url = https://icosg.test.zte.com.cn
model.feedback.production.station.generateFile.url = https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/ali_queryStationLogInfo
model.feedback.production.station.test.data.url = https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/queryStationTestInfo
model.feedback.production.meituan.queryRepairInfo = https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/meituan_queryRepairInfo
push.std.model.sn.data.requestIdPrefixSwitch = true
imes.factory.id.name.map = {'58':'ZTE101','55':'ZTE101','52':'ZTE101','53':'ZTE101','56':'ZTE101'}
task.change.config.type = REWORK_2
model.feedback.production.ali.querySspTaskInfo = https://mdsdev.zte.com.cn:29001/api/mds-web-outservice/v1/api/rest/byte/querySspTaskInfo_ali
need.customer.component.type.list = server.configmodel
model.feedback.production.fm.uploadFmParamMesFileInfoUrl = https://mdsdev.zte.com.cn:29001/api/yxnjmds-web-fm-configcloud/v1/api/rest/fmConfig/uploadFmParamMesFile
tradedata.log.alarm = {'ZTEiMES-Alibaba-SendDeductionPlan':'0668000411'}
