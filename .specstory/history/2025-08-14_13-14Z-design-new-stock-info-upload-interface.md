<!-- Generated by SpecStory -->

# Design new stock info upload interface (2025-08-14 13:14Z)

_**User**_

设计一个类似ZteStockInfoUploadController.stockInfoUpload的接口，参照ZteStockInfoUploadController-stockInfoUpload方法调用链深度分析报告.md文档。
创建新的Controller类文件及Service和ServiceImpl文件。
方法入参：物料代码列表（可以为空）出参为一般的返回接口，正常时code为0000，参照编码规范（ZteStockInfoUploadController-stockInfoUpload方法调用链深度分析报告.md）
方法的主要步骤（在Service中实现）
1，调用第三方服务zte-mes-manufactureshare-centerfactory的getCustomerItemsByCustomerAndMode的接口（参照接口文档_getCustomerItemsByCustomerAndMode.md）
2，调用InforBarcodeCenterRepository.getInventoryHoldWmwhids方法获取可用库存对应的仓库ID列表
3，将1的物料代码列表和2的仓库列表作为参数调用zte-mes-manufactureshare-datawbsys服务的getStockQtyWithWisId的接口（参照InforController.getStockQtyWithWisId接口文档.md文档）
4，对3的结果进行处理（按物料代码维度汇总，主要为可用库存信息（物料代码，总数量））
5，调用InforBarcodeCenterRepository.getBadInventoryHoldWmwhids方法获取不良品库存对应的仓库ID列表
6，将1的物料代码列表和5的仓库列表作为参数调用zte-mes-manufactureshare-datawbsys服务的getStockQtyWithWisId的接口（参照InforController.getStockQtyWithWisId接口文档.md文档）
7，对6的结果进行处理（按物料代码维度汇总，主要为不良品库存信息（物料代码，总数量））
8，上传数据组装逻辑对象（date(YYYY-MM-dd),manufacturerPartNo(1中获取的返回对象中该物料代码对应的客户物料型号)，manufacturer（客户供应商）、quantity（正常数量），badQuantity（不良数量），description（客户物料名称）），一条物料代码对应一条，该物料可用或不良没有数据的用0赋值。
9，参照stockInfoUpload的接口中存快照和写日志
10，调用B2B接口上传8中组装的数据。   给出详细的代码设计，注意本次不修改任何代码及文件。

---

_**Assistant**_

我将设计一个类似ZteStockInfoUploadController.stockInfoUpload的接口。首先让我分析现有的代码结构和相关文档，然后提供详细的代码设计。

---

<details>
                <summary>Searched codebase "ZteStockInfoUploadController stockInfoUpload方法实现" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/interfaces/step/ZteStockInfoUploadController.java` | L15-63 |
| `src/main/java/com/zte/application/step/impl/ZteStockInfoUploadServiceImpl.java` | L41-70 |
| `src/main/java/com/zte/application/step/impl/ZteStockInfoUploadServiceImpl.java` | L151-176 |
| `src/main/java/com/zte/application/step/impl/ZteStockInfoUploadServiceImpl.java` | L66-94 |
| `src/main/java/com/zte/application/step/impl/ZteBaiduStockInfoUploadServiceImpl.java` | L119-146 |
| `src/main/java/com/zte/interfaces/step/ZteBaiduStockInfoUploadController.java` | Lundefined-31 |
| `src/main/resources/mapper/step/ZteStockInfoUploadRepository.xml` | Lundefined-39 |
| `src/main/java/com/zte/interfaces/step/ZteStockInfoUploadController.java` | Lundefined-22 |
| `src/main/java/com/zte/application/step/ZteStockInfoUploadService.java` | Lundefined-43 |
| `ZteStockInfoUploadController-stockInfoUpload方法调用链深度分析报告.md` | L97-157 |
| `src/main/java/com/zte/domain/model/step/ZteStockInfoUploadRepository.java` | Lundefined-57 |
| `src/main/java/com/zte/application/step/impl/ZteStockInfoUploadServiceImpl.java` | Lundefined-48 |
| `src/main/java/com/zte/application/step/impl/ZteStockInfoUploadServiceImpl.java` | L92-133 |
| `ZteStockInfoUploadController-stockInfoUpload方法调用链深度分析报告.md` | L52-99 |
| `ZteStockInfoUploadController-stockInfoUpload方法调用链深度分析报告.md` | L36-54 |

</details>

---



---



---



---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

<details>
                <summary>Searched codebase "InforBarcodeCenterRepository getInventoryHoldWmwhids getBadInventoryHoldWmwhids方法" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/domain/model/infor/InforBarcodeCenterRepository.java` | L13-81 |
| `src/main/resources/mapper/infor/InforBarcodeCenterRepository.xml` | L64-109 |
| `src/main/resources/mapper/infor/InforBarcodeCenterRepository.xml` | Lundefined-30 |
| `src/test/java/com/zte/application/infor/InventoryholdRecordServiceImplTest.java` | L79-118 |
| `src/test/java/com/zte/application/infor/InventoryholdRecordServiceImplTest.java` | L1073-1109 |
| `src/main/java/com/zte/application/infor/impl/InventoryholdRecordServiceImpl.java` | L296-317 |
| `src/main/java/com/zte/application/infor/impl/InventoryholdRecordServiceImpl.java` | L1022-1072 |
| `src/test/java/com/zte/application/infor/InventoryholdRecordServiceImplTest.java` | L559-598 |
| `src/main/java/com/zte/application/infor/impl/InventoryholdRecordServiceImpl.java` | L79-118 |
| `src/main/java/com/zte/application/infor/impl/InventoryholdRecordServiceImpl.java` | L583-616 |
| `src/main/java/com/zte/domain/model/infor/InventoryholdRecordRepository.java` | Lundefined-52 |
| `src/main/resources/mapper/infor/InventoryholdRecord.xml` | L560-590 |
| `src/main/resources/mapper/infor/InventoryholdRecord.xml` | L20-50 |
| `src/main/resources/mapper/infor/InventoryholdRecord.xml` | L533-562 |
| `src/test/java/com/zte/application/infor/InventoryholdRecordServiceImplTest.java` | L405-431 |

</details>

---

## Todo List

- [x] 分析现有的ZteStockInfoUploadController和相关代码结构
- [-] 设计新的CustomerStockInfoController类
- [ ] 设计CustomerStockInfoService接口
- [ ] 设计CustomerStockInfoServiceImpl实现类
- [ ] 设计相关的DTO数据传输对象
- [ ] 设计Service中的10个主要步骤逻辑
- [ ] 提供完整的代码设计文档

---

现在我将基于现有代码结构和相关文档，设计一个类似ZteStockInfoUploadController.stockInfoUpload的新接口。根据分析，我将创建详细的代码设计。

---

## Todo List

- [x] 分析现有的ZteStockInfoUploadController和相关代码结构
- [x] 设计新的CustomerStockInfoController类
- [-] 设计CustomerStockInfoService接口
- [ ] 设计CustomerStockInfoServiceImpl实现类
- [ ] 设计相关的DTO数据传输对象
- [ ] 设计Service中的10个主要步骤逻辑
- [ ] 提供完整的代码设计文档

---

## 详细代码设计方案

基于对现有代码的分析和相关接口文档，我将设计一个新的客户库存信息上传接口。以下是完整的设计方案：

### 1. Controller层设计

**文件：** `src/main/java/com/zte/interfaces/step/CustomerStockInfoController.java`

```java
package com.zte.interfaces.step;

import com.zte.application.step.CustomerStockInfoService;
import com.zte.common.constant.Constant;
import com.zte.common.exception.BusiAssertException;
import com.zte.common.utils.MessageId;
import com.zte.interfaces.step.dto.CustomerStockInfoRequestDTO;
import com.zte.interfaces.step.dto.CustomerStockInfoResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 客户库存信息上传控制器
 * <AUTHOR>
 */
@ZTERestController("/customerStock")
@Api(tags = "客户库存信息上传接口")
public class CustomerStockInfoController {

    @Autowired
    private CustomerStockInfoService customerStockInfoService;

    @ApiOperation("客户库存信息查询并上传")
    @PostMapping("/uploadCustomerStockInfo")
    public CustomerStockInfoResponseDTO uploadCustomerStockInfo(
            HttpServletRequest request, 
            @RequestBody CustomerStockInfoRequestDTO dto) {
        
        // 获取员工工号
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        
        // 设置员工工号
        dto.setEmpNo(xEmpNo);
        
        // 调用服务层处理
        return customerStockInfoService.uploadCustomerStockInfo(dto);
    }
}
```

### 2. DTO数据传输对象设计

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerStockInfoRequestDTO.java`

```java
package com.zte.interfaces.step.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户库存信息请求DTO
 */
@Data
@ApiModel("客户库存信息请求参数")
public class CustomerStockInfoRequestDTO {
    
    @ApiModelProperty("物料代码列表（可以为空）")
    private List<String> itemNoList;
    
    @ApiModelProperty(value = "员工工号", hidden = true)
    private String empNo;
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerStockInfoResponseDTO.java`

```java
package com.zte.interfaces.step.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户库存信息响应DTO
 */
@Data
@ApiModel("客户库存信息响应结果")
public class CustomerStockInfoResponseDTO {
    
    @ApiModelProperty("响应代码，成功时为0000")
    private String code;
    
    @ApiModelProperty("响应消息")
    private String message;
    
    @ApiModelProperty("成功标识")
    private boolean success;
    
    /**
     * 成功响应
     */
    public static CustomerStockInfoResponseDTO success() {
        CustomerStockInfoResponseDTO response = new CustomerStockInfoResponseDTO();
        response.setCode("0000");
        response.setMessage("操作成功");
        response.setSuccess(true);
        return response;
    }
    
    /**
     * 失败响应
     */
    public static CustomerStockInfoResponseDTO fail(String code, String message) {
        CustomerStockInfoResponseDTO response = new CustomerStockInfoResponseDTO();
        response.setCode(code);
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerStockInfoDataDTO.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户库存信息数据DTO
 */
@Data
public class CustomerStockInfoDataDTO {
    
    /** 日期(YYYY-MM-dd) */
    private String date;
    
    /** 客户物料型号 */
    private String manufacturerPartNo;
    
    /** 客户供应商 */
    private String manufacturer;
    
    /** 正常数量 */
    private BigDecimal quantity;
    
    /** 不良数量 */
    private BigDecimal badQuantity;
    
    /** 客户物料名称 */
    private String description;
    
    /** ZTE物料代码 */
    private String zteItemNo;
}
```

### 3. Service接口设计

**文件：** `src/main/java/com/zte/application/step/CustomerStockInfoService.java`

```java
package com.zte.application.step;

import com.zte.interfaces.step.dto.CustomerStockInfoRequestDTO;
import com.zte.interfaces.step.dto.CustomerStockInfoResponseDTO;

/**
 * 客户库存信息服务接口
 */
public interface CustomerStockInfoService {
    
    /**
     * 客户库存信息查询并上传
     * @param dto 请求参数
     * @return 响应结果
     */
    CustomerStockInfoResponseDTO uploadCustomerStockInfo(CustomerStockInfoRequestDTO dto);
}
```

---

## Todo List

- [x] 分析现有的ZteStockInfoUploadController和相关代码结构
- [x] 设计新的CustomerStockInfoController类
- [x] 设计CustomerStockInfoService接口
- [-] 设计CustomerStockInfoServiceImpl实现类
- [ ] 设计相关的DTO数据传输对象
- [ ] 设计Service中的10个主要步骤逻辑
- [ ] 提供完整的代码设计文档

---

### 4. ServiceImpl实现类设计

**文件：** `src/main/java/com/zte/application/step/impl/CustomerStockInfoServiceImpl.java`

```java
package com.zte.application.step.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.step.CustomerStockInfoService;
import com.zte.common.exception.BusiAssertException;
import com.zte.common.utils.*;
import com.zte.domain.model.infor.InforBarcodeCenterRepository;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.step.dto.*;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.zte.common.constant.DataTransferBatchNoEnum.ZTE1;
import static com.zte.common.utils.NumConstant.INT_500;

/**
 * 客户库存信息服务实现类
 */
@Service
public class CustomerStockInfoServiceImpl implements CustomerStockInfoService {

    private static final Logger log = LoggerFactory.getLogger(CustomerStockInfoServiceImpl.class);
    
    @Autowired
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;
    
    @Autowired
    private InforBarcodeCenterRepository inforBarcodeCenterRepository;
    
    @Autowired
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;

    @Override
    public CustomerStockInfoResponseDTO uploadCustomerStockInfo(CustomerStockInfoRequestDTO dto) {
        try {
            log.info("开始处理客户库存信息上传，物料代码数量：{}", 
                    dto.getItemNoList() != null ? dto.getItemNoList().size() : 0);
            
            // 步骤1：调用第三方服务获取客户物料信息
            List<CustomerItemInfo> customerItemInfoList = getCustomerItemsByCustomerAndMode(dto.getItemNoList());
            
            // 步骤2：获取可用库存仓库ID列表
            List<String> inventoryHoldWmwhids = inforBarcodeCenterRepository.getInventoryHoldWmwhids();
            
            // 步骤3：调用getStockQtyWithWisId获取可用库存
            List<StockQtyInfo> availableStockList = getStockQtyWithWisId(
                    getItemNoListFromCustomerItems(customerItemInfoList), inventoryHoldWmwhids);
            
            // 步骤4：处理可用库存数据（按物料代码汇总）
            Map<String, BigDecimal> availableStockMap = processStockData(availableStockList);
            
            // 步骤5：获取不良品库存仓库ID列表
            List<String> badInventoryHoldWmwhids = inforBarcodeCenterRepository.getBadInventoryHoldWmwhids();
            
            // 步骤6：调用getStockQtyWithWisId获取不良品库存
            List<StockQtyInfo> badStockList = getStockQtyWithWisId(
                    getItemNoListFromCustomerItems(customerItemInfoList), badInventoryHoldWmwhids);
            
            // 步骤7：处理不良品库存数据（按物料代码汇总）
            Map<String, BigDecimal> badStockMap = processStockData(badStockList);
            
            // 步骤8：组装上传数据
            List<CustomerStockInfoDataDTO> uploadDataList = assembleUploadData(
                    customerItemInfoList, availableStockMap, badStockMap);
            
            // 步骤9：存快照和写日志
            saveSnapshotAndLog(uploadDataList, dto.getEmpNo());
            
            // 步骤10：调用B2B接口上传数据
            uploadToB2B(uploadDataList, dto.getEmpNo());
            
            log.info("客户库存信息上传完成，上传数据条数：{}", uploadDataList.size());
            return CustomerStockInfoResponseDTO.success();
            
        } catch (Exception e) {
            log.error("客户库存信息上传失败", e);
            return CustomerStockInfoResponseDTO.fail("9999", "上传失败：" + e.getMessage());
        }
    }

    /**
     * 步骤1：调用第三方服务获取客户物料信息
     */
    private List<CustomerItemInfo> getCustomerItemsByCustomerAndMode(List<String> itemNoList) {
        log.info("调用getCustomerItemsByCustomerAndMode接口获取客户物料信息");
        
        // 构建请求参数
        CustomerItemsRequestDTO requestDTO = new CustomerItemsRequestDTO();
        requestDTO.setCustomerName("ByteDance"); // 根据业务需求设置客户名称
        requestDTO.setCooperationMode("ODM"); // 根据业务需求设置合作模式
        requestDTO.setItemNoList(itemNoList);
        
        // 调用远程服务
        List<CustomerItemInfo> result = imesCenterfactoryRemoteService.getCustomerItemsByCustomerAndMode(requestDTO);
        
        log.info("获取到客户物料信息数量：{}", result != null ? result.size() : 0);
        return result != null ? result : new ArrayList<>();
    }

    /**
     * 步骤3和6：调用getStockQtyWithWisId获取库存信息
     */
    private List<StockQtyInfo> getStockQtyWithWisId(List<String> itemNoList, List<String> wmwhidList) {
        if (CommonUtils.isEmpty(itemNoList) || CommonUtils.isEmpty(wmwhidList)) {
            return new ArrayList<>();
        }
        
        log.info("调用getStockQtyWithWisId接口获取库存信息，物料数量：{}，仓库数量：{}", 
                itemNoList.size(), wmwhidList.size());
        
        // 构建请求参数
        LotxlocxidDTO requestDTO = new LotxlocxidDTO();
        requestDTO.setWisIdList(wmwhidList);
        requestDTO.setSkuList(itemNoList);
        requestDTO.setStatus("OK"); // 查询可用库存
        
        // 调用远程服务
        List<StockQtyInfo> result = imesCenterfactoryRemoteService.getStockQtyWithWisId(requestDTO);
        
        log.info("获取到库存信息数量：{}", result != null ? result.size() : 0);
        return result != null ? result : new ArrayList<>();
    }

    /**
     * 步骤4和7：处理库存数据（按物料代码汇总）
     */
    private Map<String, BigDecimal> processStockData(List<StockQtyInfo> stockList) {
        Map<String, BigDecimal> stockMap = new HashMap<>();
        
        if (CommonUtils.isEmpty(stockList)) {
            return stockMap;
        }
        
        // 按物料代码分组汇总数量
        stockMap = stockList.stream()
                .collect(Collectors.groupingBy(
                        StockQtyInfo::getSku,
                        Collectors.reducing(BigDecimal.ZERO,
                                item -> BigDecimal.valueOf(item.getQty()),
                                BigDecimal::add)
                ));
        
        log.info("库存数据汇总完成，物料种类数：{}", stockMap.size());
        return stockMap;
    }

    /**
     * 步骤8：组装上传数据
     */
    private List<CustomerStockInfoDataDTO> assembleUploadData(
            List<CustomerItemInfo> customerItemInfoList,
            Map<String, BigDecimal> availableStockMap,
            Map<String, BigDecimal> badStockMap) {
        
        List<CustomerStockInfoDataDTO> uploadDataList = new ArrayList<>();
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        for (CustomerItemInfo customerItem : customerItemInfoList) {
            CustomerStockInfoDataDTO dataDTO = new CustomerStockInfoDataDTO();
            
            // 设置基本信息
            dataDTO.setDate(currentDate);
            dataDTO.setZteItemNo(customerItem.getZteCode());
            dataDTO.setManufacturerPartNo(customerItem.getCustomerCode()); // 客户物料型号
            dataDTO.setManufacturer(customerItem.getCustomerSupplier()); // 客户供应商
            dataDTO.setDescription(customerItem.getCustomerItemName()); // 客户物料名称
            
            // 设置库存数量
            String zteCode = customerItem.getZteCode();
            dataDTO.setQuantity(availableStockMap.getOrDefault(zteCode, BigDecimal.ZERO));
            dataDTO.setBadQuantity(badStockMap.getOrDefault(zteCode, BigDecimal.ZERO));
            
            uploadDataList.add(dataDTO);
        }
        
        log.info("组装上传数据完成，数据条数：{}", uploadDataList.size());
        return uploadDataList;
    }

    /**
     * 步骤9：存快照和写日志
     */
    private void saveSnapshotAndLog(List<CustomerStockInfoDataDTO> uploadDataList, String empNo) {
        log.info("开始保存库存快照和日志");
        
        String dataTransferBatchNo = getDataTransferBatchNo(ZTE1);
        
        // 保存库存快照
        List<CustomerStockSnapshotDTO> snapshotList = convertToSnapshotList(uploadDataList, dataTransferBatchNo);
        for (List<CustomerStockSnapshotDTO> tempList : CommonUtils.splitList(snapshotList, INT_500)) {
            zteStockInfoUploadRepository.insertCustomerStockSnapshot(tempList);
        }
        
        // 记录上传日志
        List<CustomerStockUploadLogDTO> logList = convertToLogList(uploadDataList, dataTransferBatchNo, empNo);
        for (List<CustomerStockUploadLogDTO> tempList : CommonUtils.splitList(logList, INT_500)) {
            zteStockInfoUploadRepository.insertCustomerStockUploadLog(tempList);
        }
        
        log.info("库存快照和日志保存完成");
    }

    /**
     * 步骤10：调用B2B接口上传数据
     */
    private void uploadToB2B(List<CustomerStockInfoDataDTO> uploadDataList, String empNo) {
        log.info("开始上传数据到B2B接口");
        
        String dataTransferBatchNo = getDataTransferBatchNo(ZTE1);
        
        // 分批处理上传数据
        List<List<CustomerStockInfoDataDTO>> batchList = CommonUtils.splitList(uploadDataList, INT_500);
        for (List<CustomerStockInfoDataDTO> tempList : batchList) {
            // 组装B2B推送对象
            CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
            customerDataLogDTO.setId(UUID.randomUUID().toString());
            customerDataLogDTO.setOrigin("InforWMS");
            customerDataLogDTO.setCustomerName("ByteDance");
            customerDataLogDTO.setProjectName("客户库存信息");
            customerDataLogDTO.setMessageType("ZTE_IMES_BYTEDANCE_CUSTOMER_INVENTORY");
            customerDataLogDTO.setTaskNo(dataTransferBatchNo);
            
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", tempList);
            customerDataLogDTO.setJsonData(JSON.toJSONString(dataMap));
            customerDataLogDTO.setFactoryId(51);
            customerDataLogDTO.setCreateBy(empNo);
            customerDataLogDTO.setLastUpdatedBy(empNo);
            
            // 推送到B2B
            List<CustomerDataLogDTO> pushList = Arrays.asList(customerDataLogDTO);
            imesCenterfactoryRemoteService.pushDataToB2B(pushList, empNo);
        }
        
        log.info("数据上传到B2B接口完成");
    }

    // 辅助方法
    private List<String> getItemNoListFromCustomerItems(List<CustomerItemInfo> customerItemInfoList) {
        return customerItemInfoList.stream()
                .map(CustomerItemInfo::getZteCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private String getDataTransferBatchNo(String batchNoType) {
        String dataTransferBatchNo = RedisSerialNoUtil.getDateIncreaseId(batchNoType, 6);
        return dataTransferBatchNo.substring(0, 12) + "2" + dataTransferBatchNo.substring(13);
    }

    private List<CustomerStockSnapshotDTO> convertToSnapshotList(
            List<CustomerStockInfoDataDTO> uploadDataList, String dataTransferBatchNo) {
        // 转换为快照对象列表的逻辑
        return uploadDataList.stream().map(data -> {
            CustomerStockSnapshotDTO snapshot = new CustomerStockSnapshotDTO();
            snapshot.setZteItemNo(data.getZteItemNo());
            snapshot.setQuantity(data.getQuantity());
            snapshot.setBadQuantity(data.getBadQuantity());
            snapshot.setDataTransferBatchNo(dataTransferBatchNo);
            snapshot.setCreateBy("system");
            return snapshot;
        }).collect(Collectors.toList());
    }

    private List<CustomerStockUploadLogDTO> convertToLogList(
            List<CustomerStockInfoDataDTO> uploadDataList, String dataTransferBatchNo, String empNo) {
        // 转换为日志对象列表的逻辑
        return uploadDataList.stream().map(data -> {
            CustomerStockUploadLogDTO log = new CustomerStockUploadLogDTO();
            log.setId(UUID.randomUUID().toString());
            log.setZteItemNo(data.getZteItemNo());
            log.setTaskNo(dataTransferBatchNo);
            log.setStatus("PENDING");
            log.setCreateBy(empNo);
            return log;
        }).collect(Collectors.toList());
    }
}
```

### 5. 相关数据传输对象设计

---

## Todo List

- [x] 分析现有的ZteStockInfoUploadController和相关代码结构
- [x] 设计新的CustomerStockInfoController类
- [x] 设计CustomerStockInfoService接口
- [x] 设计CustomerStockInfoServiceImpl实现类
- [-] 设计相关的DTO数据传输对象
- [ ] 设计Service中的10个主要步骤逻辑
- [ ] 提供完整的代码设计文档

---

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerItemsRequestDTO.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;
import java.util.List;

/**
 * 客户物料信息请求DTO
 */
@Data
public class CustomerItemsRequestDTO {
    
    /** 客户名称 */
    private String customerName;
    
    /** 合作模式 */
    private String cooperationMode;
    
    /** ZTE代码列表 */
    private List<String> itemNoList;
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerItemInfo.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;

/**
 * 客户物料信息
 */
@Data
public class CustomerItemInfo {
    
    /** 主键ID */
    private String id;
    
    /** 客户名称 */
    private String customerName;
    
    /** ZTE代码 */
    private String zteCode;
    
    /** 客户代码 */
    private String customerCode;
    
    /** 客户物料名称 */
    private String customerItemName;
    
    /** 客户供应商 */
    private String customerSupplier;
    
    /** 合作模式 */
    private String cooperationMode;
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/LotxlocxidDTO.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;
import java.util.List;

/**
 * 库存查询请求DTO
 */
@Data
public class LotxlocxidDTO {
    
    /** 仓库ID列表 */
    private List<String> wisIdList;
    
    /** 物料代码列表 */
    private List<String> skuList;
    
    /** 状态 */
    private String status;
    
    /** 品牌列表 */
    private List<String> brandList;
    
    /** UUID列表 */
    private List<String> uuidList;
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/StockQtyInfo.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;

/**
 * 库存数量信息
 */
@Data
public class StockQtyInfo {
    
    /** 仓库ID */
    private String wisId;
    
    /** 物料代码 */
    private String sku;
    
    /** 数量 */
    private Integer qty;
    
    /** 状态 */
    private String status;
    
    /** 物料UUID */
    private String itemUuid;
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerStockSnapshotDTO.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户库存快照DTO
 */
@Data
public class CustomerStockSnapshotDTO {
    
    /** 主键 */
    private Long serialkey;
    
    /** ZTE物料代码 */
    private String zteItemNo;
    
    /** 正常数量 */
    private BigDecimal quantity;
    
    /** 不良数量 */
    private BigDecimal badQuantity;
    
    /** 批次号 */
    private String dataTransferBatchNo;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createDate;
}
```

**文件：** `src/main/java/com/zte/interfaces/step/dto/CustomerStockUploadLogDTO.java`

```java
package com.zte.interfaces.step.dto;

import lombok.Data;
import java.util.Date;

/**
 * 客户库存上传日志DTO
 */
@Data
public class CustomerStockUploadLogDTO {
    
    /** 主键ID */
    private String id;
    
    /** ZTE物料代码 */
    private String zteItemNo;
    
    /** 任务号 */
    private String taskNo;
    
    /** 状态 */
    private String status;
    
    /** JSON数据 */
    private String jsonData;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createDate;
}
```

### 6. Repository层扩展

需要在现有Repository中添加新方法：

**文件：** `src/main/java/com/zte/domain/model/step/ZteStockInfoUploadRepository.java`

```java
// 在现有接口中添加以下方法

/**
 * 保存客户库存快照
 * @param list 快照列表
 * @return 影响行数
 */
int insertCustomerStockSnapshot(List<CustomerStockSnapshotDTO> list);

/**
 * 保存客户库存上传日志
 * @param list 日志列表
 * @return 影响行数
 */
int insertCustomerStockUploadLog(List<CustomerStockUploadLogDTO> list);
```

### 7. 远程服务接口扩展

**文件：** `src/main/java/com/zte/common/utils/ImesCenterfactoryRemoteService.java`

```java
// 在现有服务中添加以下方法

/**
 * 调用getCustomerItemsByCustomerAndMode接口
 * @param requestDTO 请求参数
 * @return 客户物料信息列表
 */
List<CustomerItemInfo> getCustomerItemsByCustomerAndMode(CustomerItemsRequestDTO requestDTO);

/**
 * 调用getStockQtyWithWisId接口
 * @param requestDTO 请求参数
 * @return 库存信息列表
 */
List<StockQtyInfo> getStockQtyWithWisId(LotxlocxidDTO requestDTO);
```

### 8. 数据库表设计

**客户库存快照表：** `kxstepiii.customer_stock_snapshot`

```sql
CREATE TABLE kxstepiii.customer_stock_snapshot (
    serialkey NUMBER(20) PRIMARY KEY,
    zte_item_no VARCHAR2(50) NOT NULL,
    quantity NUMBER(18,4) DEFAULT 0,
    bad_quantity NUMBER(18,4) DEFAULT 0,
    data_transfer_batch_no VARCHAR2(50),
    create_by VARCHAR2(50),
    create_date DATE DEFAULT SYSDATE
);

-- 创建序列
CREATE SEQUENCE kxstepiii.seq_customer_stock_snapshot START WITH 1 INCREMENT BY 1;
```

**客户库存上传日志表：** `kxstepiii.customer_stock_upload_log`

```sql
CREATE TABLE kxstepiii.customer_stock_upload_log (
    id VARCHAR2(50) PRIMARY KEY,
    zte_item_no VARCHAR2(50),
    task_no VARCHAR2(50),
    status VARCHAR2(20),
    json_data CLOB,
    create_by VARCHAR2(50),
    create_date DATE DEFAULT SYSDATE,
    last_updated_by VARCHAR2(50),
    last_updated_date DATE DEFAULT SYSDATE
);
```

---

## Todo List

- [x] 分析现有的ZteStockInfoUploadController和相关代码结构
- [x] 设计新的CustomerStockInfoController类
- [x] 设计CustomerStockInfoService接口
- [x] 设计CustomerStockInfoServiceImpl实现类
- [x] 设计相关的DTO数据传输对象
- [x] 设计Service中的10个主要步骤逻辑
- [-] 提供完整的代码设计文档

---

### 9. MyBatis映射文件

**文件：** `src/main/resources/mapper/step/CustomerStockInfoRepository.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.ZteStockInfoUploadRepository">

    <!-- 保存客户库存快照 -->
    <insert id="insertCustomerStockSnapshot" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator=" ">
            INTO kxstepiii.customer_stock_snapshot 
            (serialkey, zte_item_no, quantity, bad_quantity, data_transfer_batch_no, create_by, create_date)
            VALUES 
            (kxstepiii.seq_customer_stock_snapshot.nextval, 
             #{item.zteItemNo,jdbcType=VARCHAR}, 
             #{item.quantity,jdbcType=DECIMAL}, 
             #{item.badQuantity,jdbcType=DECIMAL}, 
             #{item.dataTransferBatchNo,jdbcType=VARCHAR}, 
             #{item.createBy,jdbcType=VARCHAR}, 
             SYSDATE)
        </foreach>
        SELECT 1 FROM dual
    </insert>

    <!-- 保存客户库存上传日志 -->
    <insert id="insertCustomerStockUploadLog" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO kxstepiii.customer_stock_upload_log 
            (id, zte_item_no, task_no, status, json_data, create_by, create_date, last_updated_by, last_updated_date)
            VALUES 
            (#{item.id,jdbcType=VARCHAR}, 
             #{item.zteItemNo,jdbcType=VARCHAR}, 
             #{item.taskNo,jdbcType=VARCHAR}, 
             #{item.status,jdbcType=VARCHAR}, 
             #{item.jsonData,jdbcType=CLOB}, 
             #{item.createBy,jdbcType=VARCHAR}, 
             SYSDATE, 
             #{item.createBy,jdbcType=VARCHAR}, 
             SYSDATE)
        </foreach>
    </insert>

</mapper>
```

### 10. 业务逻辑详细说明

#### 服务中的10个主要步骤详细实现：

1. **调用第三方服务getCustomerItemsByCustomerAndMode**
   - 构建请求参数（客户名称：ByteDance，合作模式：ODM）
   - 传入物料代码列表（可选）
   - 获取客户物料信息，包括客户物料型号、客户供应商、客户物料名称等

2. **调用InforBarcodeCenterRepository.getInventoryHoldWmwhids**
   - 获取可用库存对应的仓库ID列表
   - 这些仓库存储正常可用的库存

3. **调用getStockQtyWithWisId获取可用库存**
   - 将第1步的物料代码列表和第2步的仓库列表作为参数
   - 查询可用库存数量信息

4. **处理可用库存结果**
   - 按物料代码维度汇总数量
   - 形成物料代码->总数量的映射关系

5. **调用InforBarcodeCenterRepository.getBadInventoryHoldWmwhids**
   - 获取不良品库存对应的仓库ID列表
   - 这些仓库存储不良品库存

6. **调用getStockQtyWithWisId获取不良品库存**
   - 将第1步的物料代码列表和第5步的仓库列表作为参数
   - 查询不良品库存数量信息

7. **处理不良品库存结果**
   - 按物料代码维度汇总数量
   - 形成物料代码->不良品总数量的映射关系

8. **组装上传数据逻辑对象**
   - date：当前日期（YYYY-MM-dd格式）
   - manufacturerPartNo：客户物料型号（从第1步返回数据获取）
   - manufacturer：客户供应商（从第1步返回数据获取）
   - quantity：正常数量（从第4步汇总结果获取，没有数据用0）
   - badQuantity：不良数量（从第7步汇总结果获取，没有数据用0）
   - description：客户物料名称（从第1步返回数据获取）

9. **存快照和写日志**
   - 保存库存快照到customer_stock_snapshot表
   - 记录上传日志到customer_stock_upload_log表
   - 参照现有stockInfoUpload的实现方式

10. **调用B2B接口上传数据**
    - 组装CustomerDataLogDTO对象
    - 设置消息类型为"ZTE_IMES_BYTEDANCE_CUSTOMER_INVENTORY"
    - 调用imesCenterfactoryRemoteService.pushDataToB2B方法

### 11. 错误处理和编码规范

根据分析报告中的编码规范，响应码设计：

```java
public class CustomerStockResponseCode {
    
    /** 成功 */
    public static final String SUCCESS = "0000";
    
    /** 系统异常 */
    public static final String SYSTEM_ERROR = "9999";
    
    /** 参数校验失败 */
    public static final String PARAM_ERROR = "1001";
    
    /** 第三方服务调用失败 */
    public static final String REMOTE_SERVICE_ERROR = "2001";
    
    /** 数据库操作失败 */
    public static final String DATABASE_ERROR = "3001";
}
```

### 12. 配置文件

在application.yml中添加相关配置：

```yaml
# 第三方服务配置
remote:
  service:
    centerfactory:
      url: http://localhost:8080/zte-mes-manufactureshare-centerfactory
    datawbsys:
      url: http://localhost:8080/zte-mes-manufactureshare-datawbsys

# 客户库存上传配置
customer:
  stock:
    upload:
      batch-size: 500
      customer-name: ByteDance
      cooperation-mode: ODM
```

---

## Todo List

- [x] 分析现有的ZteStockInfoUploadController和相关代码结构
- [x] 设计新的CustomerStockInfoController类
- [x] 设计CustomerStockInfoService接口
- [x] 设计CustomerStockInfoServiceImpl实现类
- [x] 设计相关的DTO数据传输对象
- [x] 设计Service中的10个主要步骤逻辑
- [x] 提供完整的代码设计文档

---

## 总结

我已经完成了一个类似`ZteStockInfoUploadController.stockInfoUpload`的完整接口设计，主要特点如下：

### 接口设计特点：

1. **Controller层**：`CustomerStockInfoController.uploadCustomerStockInfo`
   - 入参：物料代码列表（可为空）
   - 出参：标准响应格式，成功时code为"0000"
   - 遵循现有编码规范

2. **Service层**：`CustomerStockInfoServiceImpl`
   - 实现了10个主要业务步骤
   - 完整的异常处理机制
   - 参照现有代码的分批处理逻辑

3. **10个主要步骤实现**：
   - ✅ 调用getCustomerItemsByCustomerAndMode接口
   - ✅ 获取可用库存仓库ID列表
   - ✅ 调用getStockQtyWithWisId获取可用库存
   - ✅ 汇总可用库存数据
   - ✅ 获取不良品库存仓库ID列表  
   - ✅ 调用getStockQtyWithWisId获取不良品库存
   - ✅ 汇总不良品库存数据
   - ✅ 组装上传数据逻辑对象
   - ✅ 存快照和写日志
   - ✅ 调用B2B接口上传数据

4. **数据结构设计**：
   - 完整的DTO对象设计
   - 数据库表设计
   - MyBatis映射文件

5. **技术特点**：
   - 遵循现有架构模式
   - 完整的错误处理
   - 分批处理大数据量
   - 标准的日志记录

该设计完全参照了现有`ZteStockInfoUploadController`的实现模式，确保了代码风格的一致性和业务逻辑的完整性。所有设计都基于现有代码结构，便于后续实现和维护。

---

