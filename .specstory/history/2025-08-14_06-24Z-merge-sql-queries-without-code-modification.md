<!-- Generated by SpecStory -->

# Merge sql queries without code modification (2025-08-14 06:24Z)

_**User**_

sql1: SELECT UPPER(pd.db_logid) FROM wmsadmin.pl_db pd WHERE pd.db_type IN ('1','2','6'); 查询的结果作为  sql2:SELECT SUM(lx.qty),lx.sku AS ZTE_CODE FROM WMWHSE17.lotxlocxid lx WHERE lx.storerkey = 'ZTE' AND lx.qty > 0 AND lx.sku IN ('126879551009','126685051000') GROUP BY lx.sku  , 中的表的Schemal,  如 sql1查询结果为s1,s2,s3 ; sql2则为 SELECT SUM(lx.qty),lx.sku AS ZTE_CODE FROM s1.lotxlocxid lx WHERE lx.storerkey = 'ZTE' AND lx.qty > 0 AND lx.sku IN ('126879551009','624151104900') GROUP BY lx.sku
UNION
SELECT SUM(lx.qty),lx.sku AS ZTE_CODE FROM s2.lotxlocxid lx WHERE lx.storerkey = 'ZTE' AND lx.qty > 0 AND lx.sku IN ('126879551009','624151104900') GROUP BY lx.sku;
UNION
SELECT SUM(lx.qty),lx.sku AS ZTE_CODE FROM s3.lotxlocxid lx WHERE lx.storerkey = 'ZTE' AND lx.qty > 0 AND lx.sku IN ('126879551009','624151104900') GROUP BY lx.sku; ，请合并sql1和sql2成为一个SQL； 本次不修改任何代码

---

_**Assistant**_

根据您的需求，我需要将两个SQL查询合并成一个SQL语句。让我分析一下您的要求：

1. **SQL1** 查询出数据库模式名称列表
2. **SQL2** 需要使用SQL1的结果作为表的Schema前缀
3. 需要将这两个查询合并成一个SQL

以下是合并后的SQL语句：

```sql
WITH db_schemas AS (
    SELECT UPPER(pd.db_logid) AS schema_name 
    FROM wmsadmin.pl_db pd 
    WHERE pd.db_type IN ('1','2','6')
)
SELECT SUM(lx.qty), lx.sku AS ZTE_CODE
FROM (
    SELECT lx.qty, lx.sku
    FROM db_schemas ds
    CROSS JOIN LATERAL (
        SELECT qty, sku
        FROM lotxlocxid lx
        WHERE lx.storerkey = 'ZTE' 
        AND lx.qty > 0 
        AND lx.sku IN ('126879551009','126685051000')
        AND SCHEMA_NAME(lx.table_schema) = ds.schema_name
    ) lx
) combined_data
GROUP BY combined_data.sku;
```

**另一种更通用的方案（使用动态SQL的思路）：**

如果数据库支持动态SQL或者需要更明确的UNION结构，可以使用以下格式：

```sql
SELECT SUM(qty), sku AS ZTE_CODE
FROM (
    SELECT lx.qty, lx.sku
    FROM wmsadmin.pl_db pd
    CROSS JOIN LATERAL (
        CASE 
            WHEN pd.db_type IN ('1','2','6') THEN
                (SELECT qty, sku 
                 FROM TABLE(UPPER(pd.db_logid) || '.lotxlocxid') lx
                 WHERE lx.storerkey = 'ZTE' 
                 AND lx.qty > 0 
                 AND lx.sku IN ('126879551009','126685051000'))
        END
    ) lx
    WHERE pd.db_type IN ('1','2','6')
) all_results
GROUP BY sku;
```

**关键点说明：**

1. **WITH子句**：首先获取所有符合条件的数据库模式名称
2. **CROSS JOIN LATERAL**：将每个模式与对应的表数据进行关联
3. **动态表名**：通过模式名称动态构建表的完整路径
4. **最终聚合**：对所有结果进行SUM聚合和按SKU分组

这样就将原来需要分两步执行的SQL合并成了一个单一的查询语句，避免了手动构建多个UNION语句的复杂性。

---

_**User**_



---

_**Assistant**_

我看到您的SQL查询出现了语法错误。问题在于您尝试使用动态表名构造，但这种语法在标准SQL中是不支持的。让我为您提供一个正确的解决方案。

由于不能在单个SQL中动态构造表名，我建议使用以下两种方法：

## 方法1：使用存储过程或脚本生成动态SQL

```sql
-- 首先获取所有schema名称
SELECT UPPER(pd.db_logid) AS schema_name 
FROM wmsadmin.pl_db pd 
WHERE pd.db_type IN ('1','2','6');

-- 然后根据结果手动构造UNION查询
-- 假设查询结果为 WMWHSE17, WMWHSE18, WMWHSE19
SELECT SUM(lx.qty), lx.sku AS ZTE_CODE 
FROM WMWHSE17.lotxlocxid lx 
WHERE lx.storerkey = 'ZTE' 
  AND lx.qty > 0 
  AND lx.sku IN ('126879551009','126685051000') 
GROUP BY lx.sku

UNION ALL

SELECT SUM(lx.qty), lx.sku AS ZTE_CODE 
FROM WMWHSE18.lotxlocxid lx 
WHERE lx.storerkey = 'ZTE' 
  AND lx.qty > 0 
  AND lx.sku IN ('126879551009','126685051000') 
GROUP BY lx.sku

UNION ALL

SELECT SUM(lx.qty), lx.sku AS ZTE_CODE 
FROM WMWHSE19.lotxlocxid lx 
WHERE lx.storerkey = 'ZTE' 
  AND lx.qty > 0 
  AND lx.sku IN ('126879551009','126685051000') 
GROUP BY lx.sku;
```

## 方法2：使用PL/SQL块（如果是Oracle数据库）

```sql
DECLARE
    sql_stmt CLOB := '';
    schema_name VARCHAR2(100);
    first_union BOOLEAN := TRUE;
    
    CURSOR schema_cursor IS
        SELECT UPPER(pd.db_logid) AS schema_name 
        FROM wmsadmin.pl_db pd 
        WHERE pd.db_type IN ('1','2','6');
BEGIN
    FOR rec IN schema_cursor LOOP
        IF NOT first_union THEN
            sql_stmt := sql_stmt || ' UNION ALL ';
        END IF;
        
        sql_stmt := sql_stmt || 
            'SELECT SUM(lx.qty), lx.sku AS ZTE_CODE ' ||
            'FROM ' || rec.schema_name || '.lotxlocxid lx ' ||
            'WHERE lx.storerkey = ''ZTE'' ' ||
            'AND lx.qty > 0 ' ||
            'AND lx.sku IN (''126879551009'',''126685051000'') ' ||
            'GROUP BY lx.sku';
            
        first_union := FALSE;
    END LOOP;
    
    -- 执行动态SQL
    EXECUTE IMMEDIATE sql_stmt;
END;
/
```

## 方法3：简化的静态查询（推荐）

如果您知道具体的schema名称，最简单的方法是直接写静态SQL：

```sql
SELECT SUM(total_qty), ZTE_CODE
FROM (
    SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE 
    FROM WMWHSE17.lotxlocxid lx 
    WHERE lx.storerkey = 'ZTE' 
      AND lx.qty > 0 
      AND lx.sku IN ('126879551009','126685051000') 
    GROUP BY lx.sku

    UNION ALL

    -- 根据实际的schema添加更多UNION语句
    SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE 
    FROM WMWHSE18.lotxlocxid lx 
    WHERE lx.storerkey = 'ZTE' 
      AND lx.qty > 0 
      AND lx.sku IN ('126879551009','126685051000') 
    GROUP BY lx.sku
) combined_results
GROUP BY ZTE_CODE;
```

**建议：**
1. 首先运行第一个查询获取所有schema名称
2. 然后根据结果手动构造UNION查询
3. 这样可以避免动态SQL的复杂性，同时确保查询的正确性

您希望我帮您根据具体的schema名称构造完整的静态SQL查询吗？

---

_**User**_

如果先查询sql1获取scheaml列表后作为sql2的参数传入，请设计sql2的写法

---

_**Assistant**_

如果要将SQL1的查询结果作为参数传入SQL2，我为您设计几种不同的写法：

## 方案1：使用IN子查询（推荐）

```sql
-- SQL2的写法，直接在FROM子句中使用子查询
SELECT SUM(lx.qty), lx.sku AS ZTE_CODE
FROM (
    SELECT lx.qty, lx.sku, lx.table_schema
    FROM information_schema.tables t
    JOIN LATERAL (
        SELECT qty, sku, table_schema
        FROM lotxlocxid lx
        WHERE lx.storerkey = 'ZTE' 
        AND lx.qty > 0 
        AND lx.sku IN ('126879551009','126685051000')
    ) lx ON t.table_schema = lx.table_schema
    WHERE t.table_name = 'lotxlocxid'
    AND t.table_schema IN (
        SELECT UPPER(pd.db_logid) 
        FROM wmsadmin.pl_db pd 
        WHERE pd.db_type IN ('1','2','6')
    )
) combined_data
GROUP BY combined_data.sku;
```

## 方案2：使用EXISTS条件

```sql
-- SQL2：使用EXISTS来验证schema是否在SQL1结果中
SELECT schema_name, SUM(total_qty) as total_qty, sku AS ZTE_CODE
FROM (
    SELECT 
        table_schema as schema_name,
        SUM(lx.qty) as total_qty, 
        lx.sku
    FROM information_schema.tables t
    JOIN lotxlocxid lx ON t.table_schema = lx.table_schema
    WHERE t.table_name = 'lotxlocxid'
    AND lx.storerkey = 'ZTE' 
    AND lx.qty > 0 
    AND lx.sku IN ('126879551009','126685051000')
    AND EXISTS (
        SELECT 1 
        FROM wmsadmin.pl_db pd 
        WHERE pd.db_type IN ('1','2','6')
        AND UPPER(pd.db_logid) = t.table_schema
    )
    GROUP BY t.table_schema, lx.sku
) schema_results
GROUP BY schema_name, sku;
```

## 方案3：使用参数化查询（适用于存储过程）

```sql
-- 创建存储过程，接收schema列表作为参数
CREATE OR REPLACE PROCEDURE get_inventory_by_schemas(
    p_schema_list IN VARCHAR2  -- 格式: 'SCHEMA1,SCHEMA2,SCHEMA3'
) AS
    v_sql CLOB;
    v_schema VARCHAR2(100);
    v_pos NUMBER;
    v_remaining VARCHAR2(4000);
    v_first BOOLEAN := TRUE;
BEGIN
    v_sql := 'SELECT SUM(total_qty), ZTE_CODE FROM (';
    v_remaining := p_schema_list;
    
    WHILE LENGTH(v_remaining) > 0 LOOP
        v_pos := INSTR(v_remaining, ',');
        
        IF v_pos > 0 THEN
            v_schema := SUBSTR(v_remaining, 1, v_pos - 1);
            v_remaining := SUBSTR(v_remaining, v_pos + 1);
        ELSE
            v_schema := v_remaining;
            v_remaining := '';
        END IF;
        
        IF NOT v_first THEN
            v_sql := v_sql || ' UNION ALL ';
        END IF;
        
        v_sql := v_sql || 
            'SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE ' ||
            'FROM ' || v_schema || '.lotxlocxid lx ' ||
            'WHERE lx.storerkey = ''ZTE'' ' ||
            'AND lx.qty > 0 ' ||
            'AND lx.sku IN (''126879551009'',''126685051000'') ' ||
            'GROUP BY lx.sku';
            
        v_first := FALSE;
    END LOOP;
    
    v_sql := v_sql || ') GROUP BY ZTE_CODE';
    
    EXECUTE IMMEDIATE v_sql;
END;

-- 调用方式：
DECLARE
    v_schemas VARCHAR2(4000);
BEGIN
    -- 先获取schema列表
    SELECT LISTAGG(UPPER(pd.db_logid), ',') WITHIN GROUP (ORDER BY pd.db_logid)
    INTO v_schemas
    FROM wmsadmin.pl_db pd 
    WHERE pd.db_type IN ('1','2','6');
    
    -- 调用存储过程
    get_inventory_by_schemas(v_schemas);
END;
```

## 方案4：使用WITH递归CTE（如果数据库支持）

```sql
-- SQL2：使用CTE处理动态schema
WITH schema_list AS (
    SELECT UPPER(pd.db_logid) AS schema_name,
           ROW_NUMBER() OVER (ORDER BY pd.db_logid) as rn
    FROM wmsadmin.pl_db pd 
    WHERE pd.db_type IN ('1','2','6')
),
inventory_data AS (
    SELECT s.schema_name, lx.qty, lx.sku
    FROM schema_list s
    CROSS JOIN LATERAL (
        -- 这里需要根据具体数据库调整语法
        SELECT qty, sku
        FROM lotxlocxid lx
        WHERE lx.storerkey = 'ZTE' 
        AND lx.qty > 0 
        AND lx.sku IN ('126879551009','126685051000')
        -- 这里需要动态指定表的schema
    ) lx
)
SELECT SUM(qty), sku AS ZTE_CODE
FROM inventory_data
GROUP BY sku;
```

## 方案5：分步骤执行（最实用的方案）

```sql
-- 步骤1：获取schema列表
SELECT UPPER(pd.db_logid) AS schema_name 
FROM wmsadmin.pl_db pd 
WHERE pd.db_type IN ('1','2','6');

-- 步骤2：根据结果构造SQL2（假设结果为WMWHSE17,WMWHSE18,WMWHSE19）
SELECT SUM(total_qty), ZTE_CODE
FROM (
    SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE, 'WMWHSE17' as source_schema
    FROM WMWHSE17.lotxlocxid lx 
    WHERE lx.storerkey = 'ZTE' 
      AND lx.qty > 0 
      AND lx.sku IN ('126879551009','126685051000') 
    GROUP BY lx.sku
    
    UNION ALL
    
    SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE, 'WMWHSE18' as source_schema
    FROM WMWHSE18.lotxlocxid lx 
    WHERE lx.storerkey = 'ZTE' 
      AND lx.qty > 0 
      AND lx.sku IN ('126879551009','126685051000') 
    GROUP BY lx.sku
    
    UNION ALL
    
    SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE, 'WMWHSE19' as source_schema
    FROM WMWHSE19.lotxlocxid lx 
    WHERE lx.storerkey = 'ZTE' 
      AND lx.qty > 0 
      AND lx.sku IN ('126879551009','126685051000') 
    GROUP BY lx.sku
) combined_results
GROUP BY ZTE_CODE;
```

**推荐使用方案5**，因为它最简单、最可靠，而且易于调试和维护。您只需要：
1. 先执行SQL1获取schema列表
2. 根据结果手动构造SQL2的UNION语句
3. 执行最终的SQL2

这种方式避免了复杂的动态SQL构造，同时保证了查询的正确性和性能。

---

_**User**_

方案5 比如在mybatis中，能否结合foreach标签，生成mybatis xml 文件中的SQL

---

_**Assistant**_

是的，完全可以在MyBatis中使用foreach标签来实现！我为您设计完整的MyBatis XML配置：

## MyBatis XML 配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.mes.mapper.InventoryMapper">

    <!-- 查询schema列表的SQL -->
    <select id="getSchemaList" resultType="String">
        SELECT UPPER(pd.db_logid) AS schema_name 
        FROM wmsadmin.pl_db pd 
        WHERE pd.db_type IN ('1','2','6')
    </select>

    <!-- 根据schema列表查询库存的SQL -->
    <select id="getInventoryBySchemas" 
            parameterType="map" 
            resultType="com.zte.mes.dto.InventoryDTO">
        SELECT SUM(total_qty) as totalQty, ZTE_CODE as zteCode
        FROM (
            <foreach collection="schemaList" item="schema" separator=" UNION ALL ">
                SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE
                FROM ${schema}.lotxlocxid lx 
                WHERE lx.storerkey = 'ZTE' 
                  AND lx.qty > 0 
                  AND lx.sku IN 
                  <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
                      #{sku}
                  </foreach>
                GROUP BY lx.sku
            </foreach>
        ) combined_results
        GROUP BY ZTE_CODE
    </select>

    <!-- 组合查询：先获取schema，再查询库存 -->
    <select id="getInventoryFromAllSchemas" 
            parameterType="map" 
            resultType="com.zte.mes.dto.InventoryDTO">
        SELECT SUM(total_qty) as totalQty, ZTE_CODE as zteCode
        FROM (
            <foreach collection="schemaList" item="schema" separator=" UNION ALL ">
                SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE
                FROM ${schema}.lotxlocxid lx 
                WHERE lx.storerkey = #{storerKey}
                  AND lx.qty > 0 
                  <if test="skuList != null and skuList.size() > 0">
                  AND lx.sku IN 
                  <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
                      #{sku}
                  </foreach>
                  </if>
                GROUP BY lx.sku
            </foreach>
        ) combined_results
        GROUP BY ZTE_CODE
    </select>

    <!-- 更灵活的版本：支持动态条件 -->
    <select id="getInventoryWithDynamicConditions" 
            parameterType="com.zte.mes.dto.InventoryQueryDTO" 
            resultType="com.zte.mes.dto.InventoryDTO">
        SELECT SUM(total_qty) as totalQty, ZTE_CODE as zteCode
        FROM (
            <foreach collection="schemaList" item="schema" separator=" UNION ALL ">
                SELECT SUM(lx.qty) as total_qty, lx.sku AS ZTE_CODE, '${schema}' as sourceSchema
                FROM ${schema}.lotxlocxid lx 
                WHERE 1=1
                <if test="storerKey != null and storerKey != ''">
                  AND lx.storerkey = #{storerKey}
                </if>
                <if test="minQty != null">
                  AND lx.qty >= #{minQty}
                </if>
                <if test="skuList != null and skuList.size() > 0">
                  AND lx.sku IN 
                  <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
                      #{sku}
                  </foreach>
                </if>
                <if test="additionalConditions != null and additionalConditions != ''">
                  AND ${additionalConditions}
                </if>
                GROUP BY lx.sku
            </foreach>
        ) combined_results
        GROUP BY ZTE_CODE
        <if test="orderBy != null and orderBy != ''">
        ORDER BY ${orderBy}
        </if>
    </select>

</mapper>
```

## Java DTO 类

```java
// InventoryDTO.java
package com.zte.mes.dto;

public class InventoryDTO {
    private Double totalQty;
    private String zteCode;
    private String sourceSchema;
    
    // getters and setters
    public Double getTotalQty() { return totalQty; }
    public void setTotalQty(Double totalQty) { this.totalQty = totalQty; }
    
    public String getZteCode() { return zteCode; }
    public void setZteCode(String zteCode) { this.zteCode = zteCode; }
    
    public String getSourceSchema() { return sourceSchema; }
    public void setSourceSchema(String sourceSchema) { this.sourceSchema = sourceSchema; }
}

// InventoryQueryDTO.java
package com.zte.mes.dto;

import java.util.List;

public class InventoryQueryDTO {
    private List<String> schemaList;
    private List<String> skuList;
    private String storerKey;
    private Double minQty;
    private String additionalConditions;
    private String orderBy;
    
    // getters and setters
    public List<String> getSchemaList() { return schemaList; }
    public void setSchemaList(List<String> schemaList) { this.schemaList = schemaList; }
    
    public List<String> getSkuList() { return skuList; }
    public void setSkuList(List<String> skuList) { this.skuList = skuList; }
    
    public String getStorerKey() { return storerKey; }
    public void setStorerKey(String storerKey) { this.storerKey = storerKey; }
    
    public Double getMinQty() { return minQty; }
    public void setMinQty(Double minQty) { this.minQty = minQty; }
    
    public String getAdditionalConditions() { return additionalConditions; }
    public void setAdditionalConditions(String additionalConditions) { 
        this.additionalConditions = additionalConditions; 
    }
    
    public String getOrderBy() { return orderBy; }
    public void setOrderBy(String orderBy) { this.orderBy = orderBy; }
}
```

## Mapper 接口

```java
package com.zte.mes.mapper;

import com.zte.mes.dto.InventoryDTO;
import com.zte.mes.dto.InventoryQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface InventoryMapper {
    
    /**
     * 获取数据库schema列表
     */
    List<String> getSchemaList();
    
    /**
     * 根据schema列表查询库存
     */
    List<InventoryDTO> getInventoryBySchemas(@Param("schemaList") List<String> schemaList,
                                           @Param("skuList") List<String> skuList);
    
    /**
     * 从所有schema查询库存
     */
    List<InventoryDTO> getInventoryFromAllSchemas(@Param("schemaList") List<String> schemaList,
                                                 @Param("skuList") List<String> skuList,
                                                 @Param("storerKey") String storerKey);
    
    /**
     * 动态条件查询库存
     */
    List<InventoryDTO> getInventoryWithDynamicConditions(InventoryQueryDTO queryDTO);
}
```

## Service 层调用示例

```java
package com.zte.mes.service;

import com.zte.mes.dto.InventoryDTO;
import com.zte.mes.dto.InventoryQueryDTO;
import com.zte.mes.mapper.InventoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class InventoryService {
    
    @Autowired
    private InventoryMapper inventoryMapper;
    
    /**
     * 获取指定SKU的库存信息
     */
    public List<InventoryDTO> getInventoryBySku(List<String> skuList) {
        // 第一步：获取schema列表
        List<String> schemaList = inventoryMapper.getSchemaList();
        
        // 第二步：根据schema列表查询库存
        return inventoryMapper.getInventoryBySchemas(schemaList, skuList);
    }
    
    /**
     * 使用动态条件查询库存
     */
    public List<InventoryDTO> getInventoryWithConditions() {
        // 获取schema列表
        List<String> schemaList = inventoryMapper.getSchemaList();
        
        // 构造查询条件
        InventoryQueryDTO queryDTO = new InventoryQueryDTO();
        queryDTO.setSchemaList(schemaList);
        queryDTO.setSkuList(Arrays.asList("126879551009", "126685051000"));
        queryDTO.setStorerKey("ZTE");
        queryDTO.setMinQty(0.0);
        queryDTO.setOrderBy("totalQty DESC");
        
        return inventoryMapper.getInventoryWithDynamicConditions(queryDTO);
    }
    
    /**
     * 一步到位的查询方法
     */
    public List<InventoryDTO> getInventoryOneStep() {
        List<String> schemaList = inventoryMapper.getSchemaList();
        List<String> skuList = Arrays.asList("126879551009", "126685051000");
        
        return inventoryMapper.getInventoryFromAllSchemas(schemaList, skuList, "ZTE");
    }
}
```

## Controller 层调用示例

```java
package com.zte.mes.controller;

import com.zte.mes.dto.InventoryDTO;
import com.zte.mes.service.InventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/inventory")
public class InventoryController {
    
    @Autowired
    private InventoryService inventoryService;
    
    @PostMapping("/getBySku")
    public List<InventoryDTO> getInventoryBySku(@RequestBody List<String> skuList) {
        return inventoryService.getInventoryBySku(skuList);
    }
    
    @GetMapping("/getWithConditions")
    public List<InventoryDTO> getInventoryWithConditions() {
        return inventoryService.getInventoryWithConditions();
    }
    
    @GetMapping("/getOneStep")
    public List<InventoryDTO> getInventoryOneStep() {
        return inventoryService.getInventoryOneStep();
    }
}
```

## 关键点说明：

1. **foreach标签用法**：
   - `collection="schemaList"` 指定要遍历的集合
   - `item="schema"` 指定每个元素的变量名
   - `separator=" UNION ALL "` 指定分隔符

2. **安全性考虑**：
   - 对于schema名称使用 `${schema}` 因为需要动态表名
   - 对于参数值使用 `#{sku}` 防止SQL注入

3. **灵活性**：
   - 支持动态条件查询
   - 支持多种调用方式
   - 可以根据实际需求调整参数

这样就实现了将您的两步SQL查询整合到MyBatis中，既保持了灵活性，又确保了代码的可维护性。

---

