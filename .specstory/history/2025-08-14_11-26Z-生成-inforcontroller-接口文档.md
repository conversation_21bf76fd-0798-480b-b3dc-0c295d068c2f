<!-- Generated by SpecStory -->

# 生成 InforController 接口文档 (2025-08-14 11:26Z)

_**User**_

在根目录下面生成InforController.getStockQtyWithWisId的接口文档，包含调用方式，完整URL，出入参，请求示例，响应结果等

---

_**Assistant**_

我需要先找到 `InforController.getStockQtyWithWisId` 这个接口的相关信息，然后生成完整的接口文档。

---

## Todo List

- [-] 搜索并分析 InforController.getStockQtyWithWisId 接口
- [ ] 查看接口的入参和出参定义
- [ ] 分析相关的 DTO 和实体类
- [ ] 生成完整的接口文档

---

<details>
                <summary>Searched codebase "InforController getStockQtyWithWisId 接口方法" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/interfaces/InforController.java` | L1029-1066 |
| `src/main/java/com/zte/interfaces/InforController.java` | L1063-1105 |
| `src/main/java/com/zte/infrastructure/persistence/mybatis/infor/LotxlocxidRepository.xml` | L22-59 |
| `src/main/java/com/zte/interfaces/InforController.java` | L76-113 |
| `src/main/java/com/zte/infrastructure/persistence/mybatis/infor/LotxlocxidRepository.xml` | Lundefined-26 |
| `src/main/java/com/zte/domain/model/infordt/LotxlocxidRepository.java` | Lundefined-28 |
| `src/main/java/com/zte/application/datawb/LotxlocxidService.java` | Lundefined-23 |
| `src/main/java/com/zte/interfaces/infordt/EdiAsnqcsController.java` | L229-262 |
| `src/main/java/com/zte/application/stepdt/impl/StItembarcodeStockServiceImpl.java` | L362-408 |
| `src/main/java/com/zte/interfaces/InfoInventoryQueryController.java` | L18-63 |
| `src/main/java/com/zte/interfaces/BaItemController.java` | L57-101 |
| `src/main/java/com/zte/application/datawb/impl/LotxlocxidServiceImpl.java` | L60-99 |
| `src/main/java/com/zte/application/datawb/impl/EdiSoSServiceImpl.java` | L817-881 |
| `src/main/java/com/zte/application/datawb/impl/LotxlocxidServiceImpl.java` | L24-66 |
| `src/main/java/com/zte/interfaces/InforController.java` | Lundefined-41 |

</details>

---



---



---

## Todo List

- [x] 搜索并分析 InforController.getStockQtyWithWisId 接口
- [ ] 查看接口的入参和出参定义
- [ ] 分析相关的 DTO 和实体类
- [ ] 生成完整的接口文档

---

现在我需要查看相关的 DTO 和实体类定义，了解入参和出参的详细结构。

---

## Todo List

- [x] 搜索并分析 InforController.getStockQtyWithWisId 接口
- [-] 查看接口的入参和出参定义
- [ ] 分析相关的 DTO 和实体类
- [ ] 生成完整的接口文档

---

Read file: undefined

---

Read file: undefined

---

Could not find file 'src/main/java/com/zte/common/model/ServiceData.java' in the workspace.

---

Read file: undefined

---



---

Read file: undefined

---

<details>
                <summary>Searched codebase "ServiceData 类定义 import com.zte.itp.msa.core.model" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/application/datawb/SnValidateService.java` | Lundefined-12 |
| `src/main/java/com/zte/application/datawb/NPDMaterialService.java` | Lundefined-33 |
| `src/main/java/com/zte/application/datawb/BaiduService.java` | Lundefined-13 |
| `src/main/java/com/zte/common/utils/IscpRemoteServiceDataUtil.java` | Lundefined-21 |
| `src/main/java/com/zte/application/datawb/MeiTuanService.java` | Lundefined-11 |
| `src/main/java/com/zte/application/datawb/impl/BaiduServiceImpl.java` | Lundefined-39 |
| `src/main/java/com/zte/application/datawb/impl/ZmsCommonServiceImpl.java` | Lundefined-42 |
| `src/main/java/com/zte/application/datawb/impl/OneSizeToEndServiceImpl.java` | Lundefined-27 |
| `src/main/java/com/zte/application/datawb/OneSizeToEndService.java` | Lundefined-8 |
| `src/main/java/com/zte/application/datawb/impl/MesUploadDataServiceImpl.java` | Lundefined-21 |
| `src/main/java/com/zte/application/datawb/impl/EdiSoSServiceImpl.java` | Lundefined-43 |
| `src/main/java/com/zte/application/datawb/impl/SnValidateServiceImpl.java` | Lundefined-27 |
| `src/main/java/com/zte/application/datawb/impl/EdiSoSServiceImplNew.java` | Lundefined-55 |
| `src/main/java/com/zte/application/datawb/BomConfigBindInfoService.java` | Lundefined-24 |
| `src/main/java/com/zte/application/datawb/impl/CodeDetailQueryServiceImpl.java` | Lundefined-27 |

</details>

---

## Todo List

- [x] 搜索并分析 InforController.getStockQtyWithWisId 接口
- [x] 查看接口的入参和出参定义
- [x] 分析相关的 DTO 和实体类
- [ ] 生成完整的接口文档

---

现在我需要查看应用的配置信息以了解端口和上下文路径，然后生成完整的接口文档。

---

## Todo List

- [x] 搜索并分析 InforController.getStockQtyWithWisId 接口
- [x] 查看接口的入参和出参定义
- [x] 分析相关的 DTO 和实体类
- [-] 生成完整的接口文档

---

Read file: undefined

---

Read file: undefined

---

现在我有足够的信息来生成完整的接口文档。让我创建一个详细的接口文档。

---



---

## Todo List

- [x] 搜索并分析 InforController.getStockQtyWithWisId 接口
- [x] 查看接口的入参和出参定义
- [x] 分析相关的 DTO 和实体类
- [x] 生成完整的接口文档

---

已成功在根目录下生成了 `InforController.getStockQtyWithWisId接口文档.md` 文件。

## 文档包含的主要内容：

### 1. **接口基本信息**
- 接口名称、描述、请求方式
- 完整的URL路径：`http://ip:8080/zte-mes-manufactureshare-datawbsys/inforInquery/getStockQtyWithWisId`
- Content-Type等基本配置

### 2. **详细的请求参数说明**
- **请求头参数**：factoryId、empno（必填用于权限验证）
- **请求体参数**：LotxlocxidDTO对象的完整字段说明
- **参数校验规则**：包括长度限制、格式要求等

### 3. **完整的响应结果**
- 响应数据结构（ServiceData封装格式）
- 响应参数详细说明
- 成功和错误响应示例

### 4. **多种调用示例**
- **cURL命令**：可直接复制使用
- **Java代码**：使用RestTemplate的完整示例
- **JavaScript代码**：使用fetch API的前端调用示例

### 5. **业务逻辑说明**
- 数据查询逻辑的详细描述
- SQL查询特点和返回结果特点
- 与其他相关接口的区别

### 6. **异常情况处理**
- 各种参数校验失败的错误码和描述
- 安全性注意事项

### 7. **技术细节**
- 数据源配置（INFOR数据源）
- 安全防护（SQL注入防护）
- 性能注意事项

这个接口文档包含了开发者调用该接口所需的所有信息，可以直接用于API文档或开发指导。

---

