# ZTE MES 制造执行系统编码规范

## 1. 总体原则

### 1.1 编码理念
- **可读性优先**: 代码应该像文档一样易于理解
- **一致性**: 整个项目保持统一的编码风格
- **简洁性**: 避免冗余和复杂的实现
- **可维护性**: 便于后续修改和扩展
- **安全性**: 遵循安全编码实践

### 1.2 适用范围
本规范适用于ZTE MES系统所有Java代码，包括但不限于：
- 业务逻辑代码
- 数据访问层代码
- 接口层代码
- 工具类代码
- 测试代码

## 2. 命名规范

### 2.1 包命名规范
```java
// 基础包结构
com.zte.{模块}.{子模块}.{功能}

// 示例
com.zte.interfaces.dto          // 接口层DTO
com.zte.application.datawb      // 应用服务层-数据回写模块
com.zte.domain.model.datawb     // 领域模型层-数据回写模块
com.zte.infrastructure.persistence  // 基础设施层-持久化
com.zte.common.utils            // 公共工具类
```

### 2.2 类命名规范
```java
// Controller类：功能名 + Controller
public class BasBarcodeInfoController {}
public class ProdPlanController {}

// Service类：功能名 + Service
public interface BasBarcodeInfoService {}
public class BasBarcodeInfoServiceImpl implements BasBarcodeInfoService {}

// Repository类：实体名 + Repository
public interface ProdPlanRepository {}

// DTO类：功能名 + DTO
public class BarcodeExpandQueryDTO {}
public class TechnicalChangeBarcodeDTO {}

// 枚举类：功能名 + Enum
public enum ArchiveBusinessTypeEnum {}

// 异常类：功能名 + Exception
public class MesBusinessException extends RuntimeException {}

// 工具类：功能名 + Util/Utils
public class CommonUtils {}
public class DateUtils {}
```

### 2.3 方法命名规范
```java
// 查询方法：get/find/query + 对象名
public List<ProdPlan> getProdPlanList(String taskNo) {}
public ProdPlan findProdPlanById(Long id) {}
public Page<ProdPlan> queryProdPlanByCondition(ProdPlanDTO dto) {}

// 保存方法：save/insert/add + 对象名
public void saveProdPlan(ProdPlan prodPlan) {}
public int insertBatchProdPlan(List<ProdPlan> list) {}

// 更新方法：update/modify + 对象名
public void updateProdPlanStatus(Long id, String status) {}
public int modifyProdPlanInfo(ProdPlan prodPlan) {}

// 删除方法：delete/remove + 对象名
public void deleteProdPlan(Long id) {}
public int removeProdPlanByTaskNo(String taskNo) {}

// 验证方法：validate/check/verify + 对象名
public boolean validateBarcode(String barcode) {}
public void checkProdPlanStatus(String taskNo) {}

// 业务处理方法：动词 + 对象名
public void processProdPlan(ProdPlanDTO dto) {}
public void handleBarcodeException(String barcode) {}
```

### 2.4 变量命名规范
```java
// 成员变量：驼峰命名法
private String taskNo;
private List<String> barcodeList;
private BigDecimal organizationId;

// 局部变量：驼峰命名法，语义明确
String contractNumber = dto.getContractNumber();
List<ProdPlan> prodPlanList = new ArrayList<>();
Map<String, Object> resultMap = new HashMap<>();

// 常量：全大写，下划线分隔
public static final String SUCCESS_CODE = "0000";
public static final int MAX_RETRY_COUNT = 3;
public static final long DEFAULT_TIMEOUT = 30000L;

// 布尔变量：is/has/can/should开头
private boolean isActive;
private boolean hasPermission;
private boolean canProcess;
```

## 3. 注解使用规范

### 3.1 Spring注解规范
```java
// Controller层
@RestController
@RequestMapping("/prodPlan")
@Api(tags = "生产计划管理")
public class ProdPlanController {
    
    @Autowired
    private ProdPlanService prodPlanService;
    
    @ApiOperation("查询生产计划")
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public ServiceData<List<ProdPlanDTO>> queryProdPlan(@RequestBody ProdPlanQueryDTO dto) {
        // 实现逻辑
    }
}

// Service层
@Service
@DataSource(DatabaseType.WMES)  // 指定数据源
@Transactional(rollbackFor = Exception.class)  // 事务管理
public class ProdPlanServiceImpl implements ProdPlanService {
    
    @Autowired
    private ProdPlanRepository prodPlanRepository;
    
    @Override
    @RecordLogAnnotation(operationType = "查询生产计划")  // 操作日志
    public List<ProdPlan> queryProdPlan(ProdPlanQueryDTO dto) {
        // 实现逻辑
    }
}

// Repository层
@Mapper
@Repository
public interface ProdPlanRepository {
    List<ProdPlan> selectByCondition(@Param("dto") ProdPlanQueryDTO dto);
}
```

### 3.2 验证注解规范
```java
public class ProdPlanDTO {
    @NotBlank(message = "任务号不能为空")
    @Size(max = 50, message = "任务号长度不能超过50个字符")
    private String taskNo;
    
    @NotNull(message = "组织ID不能为空")
    @Min(value = 1, message = "组织ID必须大于0")
    private Long organizationId;
    
    @Valid
    @NotEmpty(message = "生产计划明细不能为空")
    private List<ProdPlanDetailDTO> details;
}
```

## 4. 注释规范

### 4.1 类注释规范
```java
/**
 * 生产计划管理服务实现类
 * 
 * 主要功能：
 * 1. 生产计划的增删改查
 * 2. 生产计划状态管理
 * 3. 生产计划数据同步
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public class ProdPlanServiceImpl implements ProdPlanService {
    // 类实现
}
```

### 4.2 方法注释规范
```java
/**
 * 根据条件查询生产计划列表
 * 
 * @param dto 查询条件DTO，包含任务号、状态等筛选条件
 * @return 符合条件的生产计划列表，如果没有数据返回空列表
 * @throws MesBusinessException 当查询条件不合法时抛出业务异常
 * <AUTHOR>
 * @since 2024-01-01
 */
public List<ProdPlan> queryProdPlan(ProdPlanQueryDTO dto) {
    // 方法实现
}
```

### 4.3 字段注释规范
```java
public class ProdPlanDTO {
    /** 任务号，唯一标识一个生产任务 */
    private String taskNo;
    
    /** 生产计划状态：0-待开始，1-进行中，2-已完成，3-已取消 */
    private Integer status;
    
    /** 组织ID，关联组织表主键 */
    private Long organizationId;
    
    // 复杂业务逻辑的行内注释
    // 根据任务状态判断是否可以修改
    if (ProdPlanStatus.IN_PROGRESS.equals(status)) {
        // 进行中的任务不允许修改基本信息
        throw new MesBusinessException("进行中的任务不允许修改");
    }
}
```

## 5. 异常处理规范

### 5.1 异常分类
```java
// 业务异常：继承RuntimeException
public class MesBusinessException extends RuntimeException {
    private String errorCode;
    
    public MesBusinessException(String message) {
        super(message);
    }
    
    public MesBusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}

// 系统异常：继承Exception
public class MesSystemException extends Exception {
    // 系统级异常实现
}
```

### 5.2 异常处理方式
```java
// Controller层异常处理
@RestController
public class ProdPlanController {
    
    @RequestMapping("/save")
    public ServiceData<String> saveProdPlan(@RequestBody ProdPlanDTO dto) {
        try {
            prodPlanService.saveProdPlan(dto);
            return ServiceDataBuilderUtil.success("保存成功");
        } catch (MesBusinessException e) {
            logger.error("保存生产计划失败：{}", e.getMessage(), e);
            return ServiceDataBuilderUtil.failure(e.getMessage());
        } catch (Exception e) {
            logger.error("系统异常：", e);
            return ServiceDataBuilderUtil.failure("系统异常，请联系管理员");
        }
    }
}

// Service层异常处理
@Service
public class ProdPlanServiceImpl implements ProdPlanService {
    
    public void saveProdPlan(ProdPlanDTO dto) {
        // 参数验证
        if (StringUtils.isBlank(dto.getTaskNo())) {
            throw new MesBusinessException("PARAM_ERROR", "任务号不能为空");
        }
        
        // 业务逻辑验证
        ProdPlan existPlan = prodPlanRepository.findByTaskNo(dto.getTaskNo());
        if (existPlan != null) {
            throw new MesBusinessException("BUSINESS_ERROR", "任务号已存在");
        }
        
        try {
            // 保存逻辑
            prodPlanRepository.save(convertToProdPlan(dto));
        } catch (Exception e) {
            logger.error("保存生产计划到数据库失败：", e);
            throw new MesBusinessException("DB_ERROR", "数据保存失败");
        }
    }
}
```

## 6. 日志规范

### 6.1 日志级别使用
```java
@Service
public class ProdPlanServiceImpl implements ProdPlanService {
    private static final Logger logger = LoggerFactory.getLogger(ProdPlanServiceImpl.class);
    
    public void processProdPlan(String taskNo) {
        // DEBUG：调试信息，生产环境不输出
        logger.debug("开始处理生产计划，任务号：{}", taskNo);
        
        // INFO：关键业务流程信息
        logger.info("生产计划处理开始，任务号：{}，操作人：{}", taskNo, getCurrentUser());
        
        try {
            // 业务处理逻辑
            doProcess(taskNo);
            
            // INFO：成功信息
            logger.info("生产计划处理完成，任务号：{}", taskNo);
            
        } catch (MesBusinessException e) {
            // WARN：业务异常，可预期的错误
            logger.warn("生产计划处理失败，任务号：{}，错误信息：{}", taskNo, e.getMessage());
            throw e;
        } catch (Exception e) {
            // ERROR：系统异常，不可预期的错误
            logger.error("生产计划处理系统异常，任务号：{}", taskNo, e);
            throw new MesBusinessException("系统异常");
        }
    }
}
```

### 6.2 日志格式规范
```java
// 正确的日志格式
logger.info("用户登录成功，用户ID：{}，登录时间：{}", userId, loginTime);
logger.error("数据库连接失败，数据源：{}，错误信息：{}", dataSource, e.getMessage(), e);

// 错误的日志格式（避免使用）
logger.info("用户登录成功，用户ID：" + userId);  // 字符串拼接性能差
logger.error("数据库连接失败：" + e.getMessage());  // 没有异常堆栈
```

## 7. 数据库操作规范

### 7.1 Repository接口规范
```java
@Mapper
@Repository
public interface ProdPlanRepository {
    
    /**
     * 根据任务号查询生产计划
     * @param taskNo 任务号
     * @return 生产计划信息，不存在返回null
     */
    ProdPlan selectByTaskNo(@Param("taskNo") String taskNo);
    
    /**
     * 分页查询生产计划
     * @param page 分页参数
     * @return 分页结果
     */
    List<ProdPlan> selectByPage(Page<ProdPlan> page);
    
    /**
     * 批量插入生产计划
     * @param list 生产计划列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<ProdPlan> list);
    
    /**
     * 更新生产计划状态
     * @param taskNo 任务号
     * @param status 新状态
     * @param updateUser 更新人
     * @return 更新成功的记录数
     */
    int updateStatus(@Param("taskNo") String taskNo, 
                    @Param("status") Integer status, 
                    @Param("updateUser") String updateUser);
}
```

### 7.2 MyBatis XML规范
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ProdPlanRepository">

    <!-- 结果映射 -->
    <resultMap id="ProdPlanResultMap" type="com.zte.domain.model.datawb.ProdPlan">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_no" property="taskNo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="organization_id" property="organizationId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="Base_Column_List">
        id, task_no, status, organization_id, create_time, update_time
    </sql>

    <!-- 查询条件SQL片段 -->
    <sql id="Where_Condition">
        <where>
            <if test="taskNo != null and taskNo != ''">
                AND task_no = #{taskNo,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                AND status = #{status,jdbcType=INTEGER}
            </if>
            <if test="organizationId != null">
                AND organization_id = #{organizationId,jdbcType=BIGINT}
            </if>
        </where>
    </sql>

    <!-- 根据任务号查询 -->
    <select id="selectByTaskNo" resultMap="ProdPlanResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prod_plan
        WHERE task_no = #{taskNo,jdbcType=VARCHAR}
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO prod_plan (task_no, status, organization_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.taskNo,jdbcType=VARCHAR}, 
             #{item.status,jdbcType=INTEGER}, 
             #{item.organizationId,jdbcType=BIGINT}, 
             SYSDATE)
        </foreach>
    </insert>

</mapper>
```

## 8. 接口设计规范

### 8.1 REST接口规范
```java
@RestController
@RequestMapping("/api/v1/prodPlan")
@Api(tags = "生产计划管理API")
public class ProdPlanController {
    
    @ApiOperation("查询生产计划列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 400, message = "参数错误"),
        @ApiResponse(code = 500, message = "系统异常")
    })
    @PostMapping("/query")
    public ServiceData<PageRows<ProdPlanDTO>> queryProdPlan(
            @Valid @RequestBody ProdPlanQueryDTO queryDTO,
            @RequestHeader(SysGlobalConst.REQUEST_HEADER_USER_ID) String userId) {
        
        // 参数验证
        RequestHeadValidationUtil.validateRequestHead(userId);
        
        // 业务处理
        PageRows<ProdPlanDTO> result = prodPlanService.queryProdPlan(queryDTO);
        
        // 返回结果
        return ServiceDataBuilderUtil.success(result);
    }
    
    @ApiOperation("保存生产计划")
    @PostMapping("/save")
    public ServiceData<String> saveProdPlan(
            @Valid @RequestBody ProdPlanDTO prodPlanDTO,
            @RequestHeader(SysGlobalConst.REQUEST_HEADER_USER_ID) String userId) {
        
        prodPlanService.saveProdPlan(prodPlanDTO, userId);
        return ServiceDataBuilderUtil.success("保存成功");
    }
}
```

### 8.2 DTO设计规范
```java
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel("生产计划查询DTO")
public class ProdPlanQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "任务号", example = "TASK001")
    private String taskNo;
    
    @ApiModelProperty(value = "状态：0-待开始，1-进行中，2-已完成", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "组织ID", example = "1001")
    private Long organizationId;
    
    @ApiModelProperty(value = "当前页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer currentPage = 1;
    
    @ApiModelProperty(value = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 20;
}
```

## 9. 测试代码规范

### 9.1 单元测试规范
```java
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class ProdPlanServiceImplTest extends BaseTestCase {
    
    @InjectMocks
    private ProdPlanServiceImpl prodPlanService;
    
    @Mock
    private ProdPlanRepository prodPlanRepository;
    
    @Test
    public void testQueryProdPlan_Success() {
        // Given
        ProdPlanQueryDTO queryDTO = new ProdPlanQueryDTO();
        queryDTO.setTaskNo("TASK001");
        
        List<ProdPlan> mockResult = Arrays.asList(createMockProdPlan());
        PowerMockito.when(prodPlanRepository.selectByCondition(any())).thenReturn(mockResult);
        
        // When
        List<ProdPlan> result = prodPlanService.queryProdPlan(queryDTO);
        
        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("TASK001", result.get(0).getTaskNo());
    }
    
    @Test(expected = MesBusinessException.class)
    public void testSaveProdPlan_TaskNoExists() {
        // Given
        ProdPlanDTO dto = new ProdPlanDTO();
        dto.setTaskNo("TASK001");
        
        PowerMockito.when(prodPlanRepository.selectByTaskNo("TASK001"))
                   .thenReturn(createMockProdPlan());
        
        // When
        prodPlanService.saveProdPlan(dto, "user001");
        
        // Then - 期望抛出异常
    }
    
    private ProdPlan createMockProdPlan() {
        ProdPlan prodPlan = new ProdPlan();
        prodPlan.setTaskNo("TASK001");
        prodPlan.setStatus(1);
        prodPlan.setOrganizationId(1001L);
        return prodPlan;
    }
}
```

## 10. 代码格式化规范

### 10.1 缩进和空格
- 使用4个空格进行缩进，不使用Tab
- 操作符前后加空格：`a + b`，`if (condition)`
- 逗号后加空格：`method(param1, param2)`
- 大括号不换行：`if (condition) {`

### 10.2 行长度和换行
- 每行代码长度不超过120个字符
- 方法参数过多时换行对齐
- 链式调用适当换行

```java
// 参数换行对齐
public void methodWithManyParams(String param1, 
                               String param2, 
                               String param3, 
                               String param4) {
    // 方法实现
}

// 链式调用换行
List<String> result = list.stream()
    .filter(item -> StringUtils.isNotBlank(item))
    .map(String::trim)
    .collect(Collectors.toList());
```

## 11. 性能优化规范

### 11.1 数据库查询优化
```java
// 避免N+1查询问题
// 错误方式
for (ProdPlan plan : plans) {
    List<ProdPlanDetail> details = detailRepository.findByPlanId(plan.getId());
    plan.setDetails(details);
}

// 正确方式
List<Long> planIds = plans.stream().map(ProdPlan::getId).collect(Collectors.toList());
Map<Long, List<ProdPlanDetail>> detailMap = detailRepository.findByPlanIds(planIds)
    .stream().collect(Collectors.groupingBy(ProdPlanDetail::getPlanId));
plans.forEach(plan -> plan.setDetails(detailMap.get(plan.getId())));
```

### 11.2 集合操作优化
```java
// 指定集合初始容量
List<String> list = new ArrayList<>(expectedSize);
Map<String, Object> map = new HashMap<>(expectedSize);

// 使用Stream API进行集合操作
List<String> taskNos = prodPlans.stream()
    .filter(plan -> plan.getStatus() == 1)
    .map(ProdPlan::getTaskNo)
    .collect(Collectors.toList());
```

## 12. 安全编码规范

### 12.1 输入验证
```java
// 参数验证
public void processProdPlan(String taskNo) {
    if (StringUtils.isBlank(taskNo)) {
        throw new MesBusinessException("任务号不能为空");
    }
    
    if (taskNo.length() > 50) {
        throw new MesBusinessException("任务号长度不能超过50个字符");
    }
    
    // 防止SQL注入，使用参数化查询
    // 业务处理...
}
```

### 12.2 敏感信息处理
```java
// 敏感信息不记录到日志
logger.info("用户登录，用户ID：{}", userId);  // 正确
logger.info("用户登录，密码：{}", password);    // 错误，不能记录密码

// 敏感信息加密存储
String encryptedPassword = encryptionUtil.encrypt(password);
```

## 13. 配置管理规范

### 13.1 配置文件命名规范
```yaml
# 主配置文件
application.yml                    # 主配置
application-{env}.yml              # 环境特定配置
bootstrap.yml                      # 启动配置

# 专用配置文件
datasource.properties              # 数据源配置
redis.properties                   # Redis配置
kafka.properties                   # Kafka配置
```

### 13.2 配置属性规范
```yaml
# 使用kebab-case命名
spring:
  application:
    name: zte-mes-manufactureshare-datawbsys
  data-source:
    driver-class-name: oracle.jdbc.OracleDriver
    connection-timeout: 30000

# 敏感信息使用占位符
database:
  username: ${DB_USERNAME:defaultUser}
  password: ${DB_PASSWORD:defaultPassword}

# 配置分组和注释
server:
  # 服务器端口配置
  port: 8080
  servlet:
    # 上下文路径配置
    context-path: /${spring.application.name}/
```

### 13.3 环境配置管理
```yaml
# 开发环境 (application-dev.yml)
spring:
  profiles:
    active: dev
logging:
  level:
    com.zte: DEBUG

# 生产环境 (application-prod.yml)
spring:
  profiles:
    active: prod
logging:
  level:
    com.zte: INFO
    root: WARN
```

## 14. 多线程编程规范

### 14.1 线程池使用规范
```java
@Configuration
public class ThreadPoolConfig {

    @Bean("asyncTaskExecutor")
    public ThreadPoolTaskExecutor asyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("async-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

@Service
public class AsyncTaskService {

    @Async("asyncTaskExecutor")
    public CompletableFuture<String> processAsync(String taskId) {
        try {
            // 异步处理逻辑
            String result = doProcess(taskId);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            logger.error("异步任务处理失败，任务ID：{}", taskId, e);
            return CompletableFuture.failedFuture(e);
        }
    }
}
```

### 14.2 线程安全规范
```java
// 使用线程安全的集合
private final Map<String, Object> cache = new ConcurrentHashMap<>();
private final List<String> list = Collections.synchronizedList(new ArrayList<>());

// 使用volatile关键字
private volatile boolean isRunning = false;

// 使用synchronized同步
public synchronized void updateStatus(String status) {
    this.status = status;
    notifyStatusChange();
}

// 使用Lock接口
private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

public String getData() {
    lock.readLock().lock();
    try {
        return data;
    } finally {
        lock.readLock().unlock();
    }
}
```

## 15. 缓存使用规范

### 15.1 本地缓存规范
```java
@Service
public class DictService {

    // 使用Caffeine本地缓存
    private final Cache<String, List<DictItem>> dictCache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .build();

    public List<DictItem> getDictItems(String dictType) {
        return dictCache.get(dictType, this::loadDictFromDB);
    }

    private List<DictItem> loadDictFromDB(String dictType) {
        return dictRepository.findByType(dictType);
    }

    @CacheEvict(value = "dictCache", key = "#dictType")
    public void refreshDict(String dictType) {
        dictCache.invalidate(dictType);
    }
}
```

### 15.2 分布式缓存规范
```java
@Service
public class UserService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String USER_CACHE_PREFIX = "user:";
    private static final int CACHE_EXPIRE_SECONDS = 3600;

    public User getUserById(Long userId) {
        String cacheKey = USER_CACHE_PREFIX + userId;

        // 先从缓存获取
        User user = (User) redisTemplate.opsForValue().get(cacheKey);
        if (user != null) {
            return user;
        }

        // 缓存未命中，从数据库查询
        user = userRepository.findById(userId);
        if (user != null) {
            // 写入缓存
            redisTemplate.opsForValue().set(cacheKey, user, CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
        }

        return user;
    }
}
```

## 16. 消息队列使用规范

### 16.1 Kafka生产者规范
```java
@Service
public class MessageProducerService {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String TOPIC_PROD_PLAN = "prod-plan-topic";

    public void sendProdPlanMessage(ProdPlanMessage message) {
        try {
            // 设置消息头
            ProducerRecord<String, Object> record = new ProducerRecord<>(
                TOPIC_PROD_PLAN,
                message.getTaskNo(),
                message
            );
            record.headers().add("messageType", "PROD_PLAN_UPDATE".getBytes());
            record.headers().add("timestamp", String.valueOf(System.currentTimeMillis()).getBytes());

            // 发送消息
            ListenableFuture<SendResult<String, Object>> future = kafkaTemplate.send(record);
            future.addCallback(
                result -> logger.info("消息发送成功，topic：{}，key：{}", TOPIC_PROD_PLAN, message.getTaskNo()),
                failure -> logger.error("消息发送失败，topic：{}，key：{}", TOPIC_PROD_PLAN, message.getTaskNo(), failure)
            );

        } catch (Exception e) {
            logger.error("发送Kafka消息异常：", e);
            throw new MesBusinessException("消息发送失败");
        }
    }
}
```

### 16.2 Kafka消费者规范
```java
@Component
public class MessageConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(MessageConsumerService.class);

    @KafkaListener(topics = "prod-plan-topic", groupId = "${spring.application.name}")
    public void handleProdPlanMessage(
            @Payload ProdPlanMessage message,
            @Header Map<String, Object> headers,
            Acknowledgment acknowledgment) {

        String taskNo = message.getTaskNo();
        logger.info("接收到生产计划消息，任务号：{}", taskNo);

        try {
            // 消息处理逻辑
            processProdPlanMessage(message);

            // 手动确认消息
            acknowledgment.acknowledge();
            logger.info("生产计划消息处理完成，任务号：{}", taskNo);

        } catch (Exception e) {
            logger.error("处理生产计划消息失败，任务号：{}", taskNo, e);
            // 根据业务需要决定是否重试或发送到死信队列
        }
    }

    private void processProdPlanMessage(ProdPlanMessage message) {
        // 具体的业务处理逻辑
    }
}
```

## 17. 文件操作规范

### 17.1 文件上传规范
```java
@RestController
public class FileUploadController {

    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of("xlsx", "xls", "csv", "pdf");

    @PostMapping("/upload")
    public ServiceData<String> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 文件验证
            validateFile(file);

            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());
            String filePath = uploadPath + "/" + fileName;

            // 保存文件
            Files.copy(file.getInputStream(), Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);

            logger.info("文件上传成功，文件名：{}，大小：{}", fileName, file.getSize());
            return ServiceDataBuilderUtil.success(fileName);

        } catch (Exception e) {
            logger.error("文件上传失败：", e);
            return ServiceDataBuilderUtil.failure("文件上传失败");
        }
    }

    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new MesBusinessException("文件不能为空");
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            throw new MesBusinessException("文件大小不能超过10MB");
        }

        String extension = getFileExtension(file.getOriginalFilename());
        if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase())) {
            throw new MesBusinessException("不支持的文件类型");
        }
    }

    private String generateFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        return UUID.randomUUID().toString() + "." + extension;
    }
}
```

### 17.2 文件读写规范
```java
@Service
public class FileProcessService {

    public List<String> readFileLines(String filePath) {
        try {
            return Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("读取文件失败，文件路径：{}", filePath, e);
            throw new MesBusinessException("文件读取失败");
        }
    }

    public void writeToFile(String filePath, List<String> lines) {
        try {
            Files.write(Paths.get(filePath), lines, StandardCharsets.UTF_8,
                       StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        } catch (IOException e) {
            logger.error("写入文件失败，文件路径：{}", filePath, e);
            throw new MesBusinessException("文件写入失败");
        }
    }

    // 使用try-with-resources处理流
    public void processLargeFile(String filePath) {
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath), StandardCharsets.UTF_8)) {
            String line;
            while ((line = reader.readLine()) != null) {
                processLine(line);
            }
        } catch (IOException e) {
            logger.error("处理大文件失败，文件路径：{}", filePath, e);
            throw new MesBusinessException("文件处理失败");
        }
    }
}
```

## 18. 代码审查规范

### 18.1 代码审查检查点
1. **功能正确性**
   - 业务逻辑是否正确
   - 边界条件是否处理
   - 异常情况是否考虑

2. **代码质量**
   - 命名是否规范
   - 注释是否充分
   - 代码是否简洁

3. **性能考虑**
   - 是否存在性能瓶颈
   - 数据库查询是否优化
   - 内存使用是否合理

4. **安全性**
   - 输入验证是否充分
   - 敏感信息是否保护
   - 权限控制是否正确

### 18.2 代码审查流程
```
1. 开发人员提交代码 → 2. 自动化检查 → 3. 同行评审 → 4. 技术负责人审查 → 5. 合并代码
```

## 19. 版本控制规范

### 19.1 Git提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

<body>

<footer>

# 示例
feat(prodplan): 添加生产计划查询功能

- 实现按条件查询生产计划
- 支持分页查询
- 添加状态筛选

Closes #123
```

### 19.2 分支管理规范
```
master/main     # 主分支，生产环境代码
develop         # 开发分支，集成最新功能
feature/*       # 功能分支，开发新功能
hotfix/*        # 热修复分支，紧急修复
release/*       # 发布分支，准备发布版本
```

## 20. 监控和告警规范

### 20.1 业务监控规范
```java
@Service
public class ProdPlanServiceImpl implements ProdPlanService {

    @Autowired
    private MeterRegistry meterRegistry;

    public void saveProdPlan(ProdPlanDTO dto) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            // 业务逻辑
            doSaveProdPlan(dto);

            // 成功计数
            meterRegistry.counter("prodplan.save.success").increment();

        } catch (Exception e) {
            // 失败计数
            meterRegistry.counter("prodplan.save.failure").increment();
            throw e;
        } finally {
            // 记录耗时
            sample.stop(Timer.builder("prodplan.save.duration")
                       .description("生产计划保存耗时")
                       .register(meterRegistry));
        }
    }
}
```

### 20.2 告警配置规范
```java
@Component
public class AlarmService {

    @AlarmAnnotation(
        metricName = "prodplan_process_error",
        alarmLevel = AlarmSeverityEnum.CRITICAL,
        description = "生产计划处理异常"
    )
    public void processProdPlanWithAlarm(String taskNo) {
        try {
            processProdPlan(taskNo);
        } catch (Exception e) {
            // 触发告警
            AlarmHelper.alarm("prodplan_process_error",
                            "生产计划处理失败，任务号：" + taskNo,
                            AlarmSeverityEnum.CRITICAL);
            throw e;
        }
    }
}
```

---

**版本信息**
- 版本：v1.0
- 创建日期：2024-01-01
- 最后更新：2024-01-01
- 维护人：ZTE MES开发团队

**说明**
本规范为ZTE MES系统开发的强制性标准，所有开发人员必须严格遵守。如有疑问或建议，请联系架构团队。

**附录**
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)
- [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)
- [Google Java Style Guide](https://google.github.io/styleguide/javaguide.html)
